#!/usr/bin/env python3
"""
Script to fix compilation errors after logrus to zap conversion.
"""

import os
import re
import sys

def fix_compilation_errors(content):
    """Fix compilation errors in Go files."""
    
    # Add fmt import if using fmt.Sprintf but fmt is not imported
    if 'fmt.Sprintf' in content and '"fmt"' not in content:
        # Find import section and add fmt
        import_pattern = r'(import\s*\(\s*\n)(.*?)(\n\s*\))'
        match = re.search(import_pattern, content, re.DOTALL)
        if match:
            imports = match.group(2)
            if '"fmt"' not in imports:
                new_imports = imports + '\n\t"fmt"'
                content = content.replace(match.group(0), match.group(1) + new_imports + match.group(3))
        elif 'import ' in content and '"fmt"' not in content:
            # Single import case
            content = re.sub(r'(import\s+"[^"]+"\s*\n)', r'\1import "fmt"\n', content, count=1)
    
    # Fix zap field type errors - convert string to zap.String
    content = re.sub(
        r'zap\.Int64\("([^"]*)", ([^)]+)\)\s*,\s*([^,)]+)\s*,',
        lambda m: f'zap.Int64("{m.group(1)}", {m.group(2)}), zap.String("value", fmt.Sprintf("%v", {m.group(3)})),',
        content
    )
    
    # Fix cases where string is passed to zap.Int64
    content = re.sub(
        r'zap\.Int64\("([^"]*)", ([^)]+)\)([,)])',
        lambda m: f'zap.String("{m.group(1)}", {m.group(2)}){m.group(3)}' if not m.group(2).startswith('int64(') and not m.group(2).isdigit() else m.group(0),
        content
    )
    
    # Fix cases where non-zap.Field values are passed as arguments
    content = re.sub(
        r'zap\.L\(\)\.(\w+)\("([^"]*)", ([^,)]+), ([^,)]+), ([^,)]+)\)',
        lambda m: fix_zap_args(m.group(1), m.group(2), [m.group(3), m.group(4), m.group(5)]),
        content
    )
    
    # Fix err.Error() passed to zap.Error - should be just err
    content = re.sub(r'zap\.Error\(([^)]+)\.Error\(\)\)', r'zap.Error(\1)', content)
    
    # Fix syntax errors in parameter lists
    content = re.sub(r'zap\.Int64\("param1", int64\(([^)]+)\)\) % ([^,)]+)', r'zap.Int64("param1", int64(\1) % int64(\2))', content)
    
    # Fix cases where string values are not wrapped in zap.String
    content = re.sub(
        r'zap\.L\(\)\.(\w+)\("([^"]*)", ([^,)]+), ([^,)]+)\)',
        lambda m: fix_two_arg_zap_call(m.group(1), m.group(2), m.group(3), m.group(4)),
        content
    )
    
    return content

def fix_zap_args(method, message, args):
    """Fix zap arguments to ensure they are proper zap.Field types."""
    fixed_args = []
    for arg in args:
        arg = arg.strip()
        if arg.startswith('zap.'):
            fixed_args.append(arg)
        elif arg.startswith('"') and arg.endswith('"'):
            fixed_args.append(f'zap.String("value", {arg})')
        elif arg.isdigit():
            fixed_args.append(f'zap.Int("value", {arg})')
        else:
            fixed_args.append(f'zap.String("value", fmt.Sprintf("%v", {arg}))')
    
    return f'zap.L().{method}("{message}", {", ".join(fixed_args)})'

def fix_two_arg_zap_call(method, message, arg1, arg2):
    """Fix two-argument zap calls."""
    arg1 = arg1.strip()
    arg2 = arg2.strip()
    
    fixed_args = []
    
    # Fix first argument
    if arg1.startswith('zap.'):
        fixed_args.append(arg1)
    else:
        fixed_args.append(f'zap.String("value1", fmt.Sprintf("%v", {arg1}))')
    
    # Fix second argument
    if arg2.startswith('zap.'):
        fixed_args.append(arg2)
    else:
        fixed_args.append(f'zap.String("value2", fmt.Sprintf("%v", {arg2}))')
    
    return f'zap.L().{method}("{message}", {", ".join(fixed_args)})'

def process_file(filepath):
    """Process a single Go file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        content = fix_compilation_errors(content)
        
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed: {filepath}")
            return True
        
        return False
    except Exception as e:
        print(f"Error processing {filepath}: {e}")
        return False

def main():
    """Main function."""
    if len(sys.argv) != 2:
        print("Usage: python3 fix_compilation_errors.py <directory>")
        sys.exit(1)
    
    root_dir = sys.argv[1]
    
    if not os.path.exists(root_dir):
        print(f"Directory {root_dir} does not exist")
        sys.exit(1)
    
    # Change to the directory
    os.chdir(root_dir)
    
    fixed_files = 0
    total_files = 0
    
    for root, _, files in os.walk("."):
        for file in files:
            if file.endswith('.go'):
                filepath = os.path.join(root, file)
                total_files += 1
                if process_file(filepath):
                    fixed_files += 1
    
    print(f"\nProcessed {total_files} Go files")
    print(f"Fixed {fixed_files} files")

if __name__ == "__main__":
    main()
