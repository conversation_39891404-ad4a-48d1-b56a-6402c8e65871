#!/usr/bin/env python3
"""
Script to convert final remaining logrus logging calls to zap.L() calls in Go files.
"""

import os
import re
import sys

def convert_with_fields_to_zap(fields_content):
    """Convert logrus.Fields{} to zap fields."""
    # Parse key-value pairs from fields
    pairs = []
    # Simple parsing for key: value pairs
    field_pairs = re.findall(r'"([^"]+)":\s*([^,}]+)', fields_content)
    
    for key, value in field_pairs:
        value = value.strip()
        # Determine the appropriate zap field type
        if value == 'true' or value == 'false':
            pairs.append(f'zap.Bool("{key}", {value})')
        elif value.isdigit():
            pairs.append(f'zap.Int("{key}", {value})')
        elif value.startswith('"') and value.endswith('"'):
            pairs.append(f'zap.String("{key}", {value})')
        else:
            pairs.append(f'zap.String("{key}", fmt.Sprintf("%v", {value}))')
    
    return ', '.join(pairs)

def convert_logger_with_fields_to_zap(logger_name, fields_content):
    """Convert logger.WithFields() to zap fields."""
    fields = convert_with_fields_to_zap(fields_content)
    return f'zap.L().With({fields})'

def convert_final_logrus_calls(content):
    """Convert final remaining logrus calls to zap calls."""
    
    # Replace imports
    content = re.sub(r'import\s+"github\.com/sirupsen/logrus"', 'import (\n\t"go.uber.org/zap"\n\t"go.uber.org/zap/zapcore"\n)', content)
    content = re.sub(r'"github\.com/sirupsen/logrus"', '"go.uber.org/zap"', content)
    
    # Handle logrus.New() and logger creation
    content = re.sub(r'logrus\.New\(\)', 'zap.L()', content)
    content = re.sub(r'logger\s*:=\s*logrus\.New\(\)', 'logger := zap.L()', content)
    
    # Handle logrus.SetFormatter, SetLevel, SetOutput, SetReportCaller
    content = re.sub(r'logrus\.SetFormatter\([^)]+\)', '// logrus.SetFormatter converted - configure zap logger instead', content)
    content = re.sub(r'logrus\.SetLevel\([^)]+\)', '// logrus.SetLevel converted - configure zap logger instead', content)
    content = re.sub(r'logrus\.SetOutput\([^)]+\)', '// logrus.SetOutput converted - configure zap logger instead', content)
    content = re.sub(r'logrus\.SetReportCaller\([^)]+\)', '// logrus.SetReportCaller converted - configure zap logger instead', content)
    
    # Handle logger.SetFormatter, SetLevel, etc.
    content = re.sub(r'(\w+)\.SetFormatter\([^)]+\)', r'// \1.SetFormatter converted - configure zap logger instead', content)
    content = re.sub(r'(\w+)\.SetLevel\([^)]+\)', r'// \1.SetLevel converted - configure zap logger instead', content)
    content = re.sub(r'(\w+)\.SetOutput\([^)]+\)', r'// \1.SetOutput converted - configure zap logger instead', content)
    content = re.sub(r'(\w+)\.SetReportCaller\([^)]+\)', r'// \1.SetReportCaller converted - configure zap logger instead', content)
    
    # Handle logrus level constants
    content = re.sub(r'logrus\.DebugLevel', 'zapcore.DebugLevel', content)
    content = re.sub(r'logrus\.InfoLevel', 'zapcore.InfoLevel', content)
    content = re.sub(r'logrus\.WarnLevel', 'zapcore.WarnLevel', content)
    content = re.sub(r'logrus\.ErrorLevel', 'zapcore.ErrorLevel', content)
    content = re.sub(r'logrus\.FatalLevel', 'zapcore.FatalLevel', content)
    content = re.sub(r'logrus\.PanicLevel', 'zapcore.PanicLevel', content)
    
    # Handle logrus.Level type
    content = re.sub(r'logrus\.Level', 'zapcore.Level', content)
    
    # Handle simple logrus.Errorf calls
    content = re.sub(
        r'logrus\.Errorf\("([^"]*)" \+ ([^)]+)\)',
        lambda m: f'zap.L().Error("{m.group(1)}", zap.Error({m.group(2)}))',
        content
    )
    
    # Handle logrus.WithFields() calls with simple Debug/Info/Warn/Error
    content = re.sub(
        r'(\w+)\.WithFields\(logrus\.Fields\{([^}]+)\}\)\.Debug\("([^"]*)"\)',
        lambda m: f'zap.L().Debug("{m.group(3)}", {convert_with_fields_to_zap(m.group(2))})',
        content
    )
    
    content = re.sub(
        r'(\w+)\.WithFields\(logrus\.Fields\{([^}]+)\}\)\.Info\("([^"]*)"\)',
        lambda m: f'zap.L().Info("{m.group(3)}", {convert_with_fields_to_zap(m.group(2))})',
        content
    )
    
    content = re.sub(
        r'(\w+)\.WithFields\(logrus\.Fields\{([^}]+)\}\)\.Warn\("([^"]*)"\)',
        lambda m: f'zap.L().Warn("{m.group(3)}", {convert_with_fields_to_zap(m.group(2))})',
        content
    )
    
    content = re.sub(
        r'(\w+)\.WithFields\(logrus\.Fields\{([^}]+)\}\)\.Error\("([^"]*)"\)',
        lambda m: f'zap.L().Error("{m.group(3)}", {convert_with_fields_to_zap(m.group(2))})',
        content
    )
    
    # Handle logger variable assignments with WithFields
    content = re.sub(
        r'(\w+)\.logger = logger\.WithFields\(logrus\.Fields\{([^}]+)\}\)',
        lambda m: f'// {m.group(1)}.logger converted - use zap.L().With({convert_with_fields_to_zap(m.group(2))}) instead',
        content
    )
    
    # Handle Logger.SetLevel calls on logger instances
    content = re.sub(
        r'(\w+)\.logger\.Logger\.SetLevel\(logrus\.(\w+)Level\)',
        r'// \1.logger.Logger.SetLevel converted - configure zap logger instead',
        content
    )

    # Handle simple logrus.WithField calls in struct initialization
    content = re.sub(
        r'logrus\.WithField\("([^"]*)", "([^"]*)"\)',
        lambda m: f'zap.L().With(zap.String("{m.group(1)}", "{m.group(2)}"))',
        content
    )

    # Handle logrus.Infof calls in tests
    content = re.sub(
        r'logrus\.Infof\(([^)]+)\)',
        lambda m: f'zap.L().Info(fmt.Sprintf({m.group(1)}))',
        content
    )

    # Handle logrus.Debugf calls
    content = re.sub(
        r'logrus\.Debugf\(([^)]+)\)',
        lambda m: f'zap.L().Debug(fmt.Sprintf({m.group(1)}))',
        content
    )

    # Handle logrus.WithError().Errorf() calls
    content = re.sub(
        r'logrus\.WithError\(([^)]+)\)\.Errorf\("([^"]*)", ([^)]+)\)',
        lambda m: f'zap.L().Error("{m.group(2)}", zap.Error({m.group(1)}), zap.String("value", fmt.Sprintf("%v", {m.group(3)})))',
        content
    )

    # Handle logrus.WithField().Info() calls
    content = re.sub(
        r'logrus\.WithField\("([^"]*)", ([^)]+)\)\.Info\("([^"]*)"\)',
        lambda m: f'zap.L().Info("{m.group(3)}", zap.String("{m.group(1)}", fmt.Sprintf("%v", {m.group(2)})))',
        content
    )

    # Handle logrus.WithError().Debug() calls
    content = re.sub(
        r'logrus\.WithError\(([^)]+)\)\.Debug\("([^"]*)"\)',
        lambda m: f'zap.L().Debug("{m.group(2)}", zap.Error({m.group(1)}))',
        content
    )

    # Handle logrus.WithField().Debug() calls
    content = re.sub(
        r'logrus\.WithField\("([^"]*)", ([^)]+)\)\.Debug\("([^"]*)"\)',
        lambda m: f'zap.L().Debug("{m.group(3)}", zap.String("{m.group(1)}", fmt.Sprintf("%v", {m.group(2)})))',
        content
    )

    # Handle c.log.WithError().WithFields() calls (complex pattern)
    content = re.sub(
        r'(\w+)\.log\.WithError\(([^)]+)\)\.WithFields\(logrus\.Fields\{([^}]+)\}\)',
        lambda m: f'zap.L().Error("error with fields", zap.Error({m.group(2)}), {convert_with_fields_to_zap(m.group(3))})',
        content
    )

    return content

def process_file(filepath):
    """Process a single Go file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        content = convert_final_logrus_calls(content)
        
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Converted: {filepath}")
            return True
        
        return False
    except Exception as e:
        print(f"Error processing {filepath}: {e}")
        return False

def main():
    """Main function."""
    if len(sys.argv) != 2:
        print("Usage: python3 convert_final_logrus.py <directory>")
        sys.exit(1)
    
    root_dir = sys.argv[1]
    
    if not os.path.exists(root_dir):
        print(f"Directory {root_dir} does not exist")
        sys.exit(1)
    
    # Change to the directory
    os.chdir(root_dir)
    
    converted_files = 0
    total_files = 0
    
    for root, _, files in os.walk("."):
        for file in files:
            if file.endswith('.go'):
                filepath = os.path.join(root, file)
                total_files += 1
                if process_file(filepath):
                    converted_files += 1
    
    print(f"\nProcessed {total_files} Go files")
    print(f"Converted {converted_files} files")

if __name__ == "__main__":
    main()
