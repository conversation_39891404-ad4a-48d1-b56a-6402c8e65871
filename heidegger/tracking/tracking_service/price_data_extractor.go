package tracking_service

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/ad_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/media_loader"
	"gitlab.com/dev/heidegger/library/price/price_manager"
	"gitlab.com/dev/heidegger/library/utils"
	"strconv"
	"fmt"
)

type PriceDataExtractor struct {
	adLoader        ad_loader.AdLoader
	mediaSlotLoader media_loader.MediaSlotLoader
	mediaLoader     media_loader.MediaLoader
	priceManager    *price_manager.PriceManager
	dataType        string
}

func NewPriceDataExtractor(
	adLoader ad_loader.AdLoader,
	mediaLoader media_loader.MediaLoader,
	mediaSlotLoader media_loader.MediaSlotLoader,
	priceManager *price_manager.PriceManager,
	dataType string) *PriceDataExtractor {
	return &PriceDataExtractor{
		adLoader:        adLoader,
		mediaLoader:     mediaLoader,
		mediaSlotLoader: mediaSlotLoader,
		priceManager:    priceManager,
		dataType:        dataType,
	}
}

func (l *PriceDataExtractor) Start() error {
	return nil
}

func (l *PriceDataExtractor) Stop() {

}

func (l *PriceDataExtractor) GetName() string {
	return "PriceDataExtractor"
}

func (l *PriceDataExtractor) Do(request *TrackingRequest) error {
	ext, err := request.GetExtData()
	if err != nil {
		zap.L().Error("[PriceDataExtractor]get ext data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	mediaId := ext.GetMediaId()
	mediaInfo := l.mediaLoader.GetMediaById(utils.ID(mediaId))
	if mediaInfo == nil {
		return nil
	}

	mediaSlotId, err := strconv.ParseInt(ext.MediaSlotId, 10, 64)
	if err != nil {
		zap.L().Error("parse media slot id error, err", zap.Error(err))
	}

	mediaSlot := l.mediaSlotLoader.GetMediaSlotById(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		zap.L().Error("get media slot error, media slot id", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(mediaSlotId)))))
	}

	priceData := request.GetTrackingPriceData()
	priceData.WinPriceRaw = request.GetRawPrice()
	priceData.ChargePrice = ext.ChargePrice
	priceData.ChargeType = ext.ChargePriceType
	adType := entity.AdTypeDsp
	adInfo := l.adLoader.GetAdById(utils.ID(ext.AdId))
	if adInfo != nil {
		adType = adInfo.AdType
		if l.dataType == "clk" && adInfo.GetBidPrice().Type != entity.BidTypeCpc {
			return nil
		}

		if l.dataType == "imp" && adInfo.GetBidPrice().Type != entity.BidTypeCpm {
			return nil
		}

		priceData.WinPriceType = int32(adInfo.GetBidPrice().Type)
	}

	if len(priceData.WinPriceRaw) > 0 && priceData.WinPriceRaw != "__X_WP__" && adType != entity.AdTypeAttribution {
		winPrice, err := l.priceManager.DecodeMediaWinPrice(priceData.WinPriceRaw, mediaInfo.ProtocolType, mediaInfo.Ikey, mediaInfo.Ekey)
		if err != nil {
			zap.L().Error("decode winprice error,mediaId:,mediaName:,macro:%s,slot:%s err:%s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(mediaInfo.Id)))), zap.Int64("param2", int64(mediaInfo.Name)), zap.Int64("param3", int64(priceData.WinPriceRaw)), zap.Int64("id", int64(ext.MediaSlotId)), zap.Error(err))
		} else {
			priceData.WinPrice = int32(winPrice)
			priceData.IsCost = 1
		}
	} else {
		if adType == entity.AdTypeAttribution {
			priceData.WinPrice = 0
			priceData.IsCost = 1
		} else if mediaSlot != nil && mediaSlot.CostPrice != 0 {
			priceData.WinPrice = mediaSlot.CostPrice
			priceData.IsCost = 1
		} else {
			priceData.WinPrice = 0
			priceData.IsCost = 1
		}
	}

	return nil
}
