package tracking_service

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/go-redis/redis"
	"github.com/labstack/echo/v4"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/hash_utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"go.uber.org/zap"
)

var shortUrlKeyPrefix = "sUrl_"

type ShortUrlHandler struct {
	path        string
	redisClient *redis.Client
}

func NewShortUrlHandler(path string, redisClient *redis.Client) *ShortUrlHandler {
	return &ShortUrlHandler{
		path:        path,
		redisClient: redisClient,
	}
}

func (handler *ShortUrlHandler) RegisterEcho(server *echo.Echo) {
	server.Any(handler.path, handler.Do)
}

func (handler *ShortUrlHandler) Do(c echo.Context) error {
	sType := c.QueryParam("t")
	if sType == "s" {
		return handler.shortenURL(c)
	} else {
		return handler.redirectURL(c)
	}
}

func (handler *ShortUrlHandler) shortenURL(c echo.Context) error {
	// 从请求体中获取原始URL
	originUrl := c.QueryParam("url")

	if originUrl == "" {
		return c.String(http.StatusOK, "URL is required")
	}

	urlKey, err := handler.DoShorten(c.Request().Context(), originUrl, 7*24*time.Hour)
	if err != nil {
		zap.L().Error("shorten short url error", zap.Error(err))
		return err
	}
	// 返回短链接
	c.Response().Header().Set("Access-Control-Allow-Origin", "*")
	return c.String(http.StatusOK, "Shortened URL: "+urlKey)
}

func (handler *ShortUrlHandler) redirectURL(c echo.Context) error {
	// 从请求中获取短链接
	shortUrl := c.QueryParam("s")
	if shortUrl == "" {
		return c.String(http.StatusOK, "s param nil")
	}

	originUrl, err := handler.UndoShorten(c.Request().Context(), shortUrl)
	if err != nil {
		zap.L().Error("get short url error", zap.Error(err))
		return err
	}
	// 记录访问日志 logToFile(shortURL)
	// 重定向到原始URL
	return c.Redirect(http.StatusMovedPermanently, originUrl)
}

func (handler *ShortUrlHandler) DoShorten(ctx context.Context, url string, expires time.Duration) (string, error) {
	key := string_utils.FormatUintBase62(hash_utils.XXHash(url))
	// 将短链接和原始URL映射存储到Redis
	err := handler.redisClient.Set(shortUrlKeyPrefix+key, url, expires).Err()
	if err != nil {
		zap.L().Error("shorten short url error", zap.Error(err))
		return utils.EmptyString, err
	}
	return key, nil
}

func (handler *ShortUrlHandler) DoShortenAsync(ctx context.Context, url string, expires time.Duration) string {
	key := string_utils.FormatUintBase62(hash_utils.XXHash(url))
	go func() {
		// 将短链接和原始URL映射存储到Redis
		err := handler.redisClient.Set(shortUrlKeyPrefix+key, url, expires).Err()
		if err != nil {
			zap.L().Error("save short url error", zap.Error(err), zap.String("url", url), zap.String("key", key))
		}
	}()
	return key
}

func (handler *ShortUrlHandler) UndoShorten(ctx context.Context, key string) (string, error) {
	// 从Redis获取原始URL
	originUrl, err := handler.redisClient.Get(shortUrlKeyPrefix + key).Result()
	if err == redis.Nil {
		zap.L().Error("short url not found", zap.String("key", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", key)))))
		return utils.EmptyString, errors.New("url not found")
	} else if err != nil {
		zap.L().Error("get short url error", zap.Error(err))
		return utils.EmptyString, err
	}
	return originUrl, nil
}
