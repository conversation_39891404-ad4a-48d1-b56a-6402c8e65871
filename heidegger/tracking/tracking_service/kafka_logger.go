package tracking_service

import (
	"time"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/kafka_producer"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/tracking/tracking_service/custom_data_log"
	"gitlab.com/dev/heidegger/tracking/tracking_service/ext_data"
	"fmt"
)

type TrackingKafkaLogger struct {
	producer kafka_producer.GenericKafkaProducer
	topic    string

	counter      *prometheus_helper.LabelCounter
	successLabel []string
	failLabel    []string
}

func NewTrackingKafkaLogger(topic string, producer kafka_producer.GenericKafkaProducer) *TrackingKafkaLogger {
	return &TrackingKafkaLogger{
		topic:    topic,
		producer: producer,

		counter:      prometheus_helper.RegisterLabelCounter("tracking_kafka_logger", []string{"type", "result"}),
		successLabel: []string{topic, "success"},
		failLabel:    []string{topic, "fail"},
	}
}

func (l *TrackingKafkaLogger) Start() error {
	return nil
}

func (l *TrackingKafkaLogger) Stop() {

}

func (l *TrackingKafkaLogger) GetName() string {
	return "TrackingKafkaLogger"
}

func (l *TrackingKafkaLogger) Do(request *TrackingRequest) error {
	if err := l.do(request); err != nil {
		l.counter.Inc(l.failLabel)
		zap.L().Error("do tracking kafka error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil
	}
	l.counter.Inc(l.successLabel)
	return nil
}

func (l *TrackingKafkaLogger) do(request *TrackingRequest) error {
	if l.topic == "ad_custom_data_log" {
		return l.customDo(request)
	} else {
		return l.commonDo(request)
	}
}

func (l *TrackingKafkaLogger) commonDo(request *TrackingRequest) error {
	if l.producer == nil {
		zap.L().Debug("[TrackingKafkaLogger] producer is nil")
		return nil
	}

	ext, err := request.GetExtData()
	if err != nil {
		zap.L().Error("[TrackingKafkaLogger]get ext data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	if l.topic == "ad_tracking_click_log" && ext.AdId == 50008 {
		zap.L().Info("[TrackingKafkaLogger] adId:, shadow", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ext.AdId)))), zap.Int64("param2", int64(ext.IsShadowedClick)))
	}

	if l.topic == "ad_tracking_click_log" && ext.IsShadowedClick == 1 {
		zap.L().Info("[TrackingKafkaLogger] shadowed click, adId", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ext.AdId)))))
		return nil
	}

	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		zap.L().Error("get basic data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	trackingData := ext_data.TrackingData{}
	trackingData.BasicData = basicData
	trackingData.ExtData = ext
	trackingData.PriceData = request.GetTrackingPriceData()

	const actionTopic = "ad_tracking_action_log"
	if l.topic == actionTopic {
		trackingData.BasicData.RawUrl = request.GetRawUrl()
	}

	trackingDataBytes, err := trackingData.Marshal()
	if err != nil {
		zap.L().Error("marshal tracking data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}
	l.producer.Input(l.topic, trackingDataBytes)
	return nil
}

func (l *TrackingKafkaLogger) customDo(request *TrackingRequest) error {
	if l.producer == nil {
		zap.L().Debug("[TrackingKafkaLogger] producer is nil")
		return nil
	}

	trackingData := custom_data_log.CustomDataLog{}
	trackingData.Timestamp = time.Now().UnixMilli()
	trackingData.CustomType = request.GetCustomType()
	trackingData.CustomData = request.GetCustomData()
	trackingData.RawUrl = request.GetRawUrl()
	trackingData.ClientIp = request.GetClientIP().String()
	trackingData.Host = request.GetHost()
	trackingData.Ua = request.GetUserAgent()

	trackingDataBytes, err := trackingData.Marshal()
	if err != nil {
		zap.L().Error("marshal tracking data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}
	l.producer.Input(l.topic, trackingDataBytes)
	return nil
}
