package tracking_service

import (
	"strings"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/budget_platform_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/dsp_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/tracking/tracking_error"
	"fmt"
)

type CpaDataExtractor struct {
	budgetPlatformLoader budget_platform_loader.BudgetPlatformLoader
	dspLoader            dsp_loader.DspLoader
}

func NewCpaDataExtractor(budgetPlatformLoader budget_platform_loader.BudgetPlatformLoader, dspLoader dsp_loader.DspLoader) *CpaDataExtractor {
	return &CpaDataExtractor{
		budgetPlatformLoader: budgetPlatformLoader,
		dspLoader:            dspLoader,
	}
}

func (l *CpaDataExtractor) Start() error {
	return nil
}

func (l *CpaDataExtractor) Stop() {

}

func (l *CpaDataExtractor) GetName() string {
	return "CpaDataExtractor"
}

func (l *CpaDataExtractor) Do(request *TrackingRequest) error {
	ext, err := request.GetExtData()
	if err != nil {
		zap.L().Error("[CpaDataExtractor]get ext data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}
	budgetPlatformKey := ""
	budgetPlatformId := ext.GetBudgetPlatformId()
	actType := request.GetActType()
	if actType == entity.OldCpaEventOldDpSuccess {
		if ext.DspId != 0 {
			dspInfo := l.dspLoader.GetDspById(utils.ID(ext.DspId))
			if dspInfo != nil {
				switch dspInfo.Protocol {
				case entity.DspProtoTypeQihang, entity.DspProtoTypeQihangCPA:
					request.cpaEventType = entity.CpaEventTypeActivate
				default:
					request.cpaEventType = entity.CpaEventTypeDpSuccess
				}
			}
		} else {
			request.cpaEventType = entity.CpaEventTypeActivate
		}
		basicData, _ := request.GetTrackingBasicData()
		if basicData != nil {
			basicData.EventType = entity.CpaEventTypeDpSuccess
			//直接返回,系统吊起链接
			return nil
		}
	} else if actType == entity.OldCpaEventBidFailed {
		basicData, _ := request.GetTrackingBasicData()
		if basicData != nil {
			basicData.EventType = entity.CpaEventTypeBidFailed
			//直接返回,竞价失败
			return nil
		}
	} else if budgetPlatformId != 0 {
		budgetPlatform := l.budgetPlatformLoader.GetBudgetPlatformById(utils.ID(budgetPlatformId))
		if budgetPlatform == nil {
			zap.L().Error("[CpaDataExtractor]budgetPlatformId not found", zap.String("id", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", budgetPlatformId)))))
			return tracking_error.ErrNoBudgetPlatformId
		} else {
			budgetPlatformKey = budgetPlatform.Key
		}
	} else if ext.DspId != 0 {
		//默认启航,兼容老逻辑
		budgetPlatformKey = entity.BudgetPlatformSurge
		dspInfo := l.dspLoader.GetDspById(utils.ID(ext.DspId))
		if dspInfo != nil {
			switch dspInfo.Protocol {
			case entity.DspProtoTypeQihang, entity.DspProtoTypeQihangCPA:
				request.cpaEventType = entity.CpaEventTypeActivate
				budgetPlatformKey = entity.BudgetPlatformSurge
			case entity.DspProtoTypeXunYu:
				budgetPlatformKey = entity.BudgetPlatformXunYu
			default:
				request.cpaEventType = entity.CpaEventTypeActivate
			}
		}
	}

	switch budgetPlatformKey {
	case entity.BudgetPlatformKeyCommon, entity.BudgetPlatformKeyEmpty:
		return l.doCommonCpa(request)
	case entity.BudgetPlatformKeyIqiyi:
		return l.doIqiyiCpa(request)
	case entity.BudgetPlatformChuanQi:
		return l.doChuanQiCpa(request)
	case entity.BudgetPlatformWangMeng:
		return l.doWangMengCpa(request)
	case entity.BudgetPlatformVipShop:
		return l.doVipShopCpa(request)
	case entity.BudgetPlatformYouLiang:
		return l.doYouLiangCpa(request)
	case entity.BudgetPlatformSurge:
		return l.doSurgeCpa(request)
	case entity.BudgetPlatformMeiTuan:
		return l.doMeiTuanCpa(request)
	case entity.BudgetPlatformDefaultActive:
		return l.doDefaultEventCpa(request)
	case entity.BudgetPlatformXinyi:
		return l.doXinyiCpa(request)
	case entity.BudgetPlatform6BianShi:
		return l.do6BianShi(request)
	case entity.BudgetPlatformXunYu:
		return l.doXunYuEventCpa(request)
	case entity.BudgetPlatformAlipay:
		return l.doAlipayCpa(request)
	case entity.BudgetPlatformHYXT:
		return l.doHYXTCpa(request)
	case entity.BudgetPlatformDianSi:
		return l.doDianSiCpa(request)
	case entity.BudgetPlatformXima:
		return l.doXimaCpa(request)
	case entity.BudgetPlatformZhijie:
		return l.doZhijieCpa(request)
	case entity.BudgetPlatformOneWay:
		return l.doOneWayCpa(request)
	case entity.BudgetPlatformFeiDian:
		return l.doFeiDianCpa(request)
	case entity.BudgetPlatformBaidu:
		return l.doBaiduCpa(request)
	case entity.BudgetPlatformMoWen:
		return l.doMoWenCpa(request)
	case entity.BudgetPlatformSina:
		return l.doSinaCpa(request)
	case entity.BudgetPlatformFangge:
		return l.doFanggeCpa(request)
	case entity.BudgetPlatformDaHangHai:
		return l.doDaHangHaiCpa(request)
	case entity.BudgetPlatformKuaiShou:
		return l.doKuaiShouCpa(request)
	default:
		zap.L().Warn("unknown budgetPlatformKey", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", budgetPlatformKey)))))
		return nil
	}
}

func (l *CpaDataExtractor) doCommonCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	switch request.GetEventType() {
	case entity.OldCpaEventActived:
		basicData.EventType = entity.CpaEventTypeActivate
	case entity.OldCpaEventRegister:
		basicData.EventType = entity.CpaEventTypeRegister
	case entity.OldCpaEventCommitMsg:
		basicData.EventType = entity.CpaEventTypeCommitMsg
	case entity.OldCpaEventPay:
		basicData.EventType = entity.CpaEventTypePay
	case entity.OldCpaEventRetained:
		basicData.EventType = entity.CpaEventTypeRetained
	case entity.OldCpaEventOpenApp:
		basicData.EventType = entity.CpaEventTypeOpenApp
	case entity.OldCpaEventDownload:
		basicData.EventType = entity.CpaEventTypeDownload
	case entity.OldCpaEventAddCart:
		basicData.EventType = entity.CpaEventTypeAddCart
	case entity.OldCpaEventNewOrder:
		basicData.EventType = entity.CpaEventTypeNewOrder
	case entity.OldCpaEventOldOrder:
		basicData.EventType = entity.CpaEventTypeOldOrder
	case entity.OldCpaEventOldDpSuccess:
		basicData.EventType = entity.CpaEventTypeDpSuccess
	case entity.OldCpaEventTypeUnKnown:
		basicData.EventType = entity.CpaEventTypeUnknown
	default:
		basicData.EventType = strings.ToLower(request.GetEventType())
	}

	return nil
}

func (l *CpaDataExtractor) doIqiyiCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	eventType := request.GetEventType()
	switch eventType {
	case "activate":
		basicData.EventType = entity.CpaEventTypeActivate
	case "register":
		basicData.EventType = entity.CpaEventTypeRegister
	case "pay":
		basicData.EventType = entity.CpaEventTypePay
	case "leave":
		basicData.EventType = entity.CpaEventTypeRetained
	//上面是给的枚举。。实际上和网易有道一样。
	case "ios_activate", "android_activate":
		basicData.EventType = entity.CpaEventTypeActivate
	case "ios_register", "android_register":
		basicData.EventType = entity.CpaEventTypeRegister
	case "android_download":
		basicData.EventType = entity.CpaEventTypeDownload
	case "android_purchase", "ios_purchase":
		basicData.EventType = entity.CpaEventTypePay
	case "android_day1retention", "ios_day1retention":
		basicData.EventType = entity.CpaEventTypeRetained
	case "android_addtocart", "ios_addtocart":
		basicData.EventType = entity.CpaEventTypeAddCart
	case "android_credit", "ios_credit":
		basicData.EventType = entity.CpaEventTypeCredit
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}

	return nil
}

func (l *CpaDataExtractor) doWangMengCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	basicData.EventType = entity.CpaEventTypeActivate
	return nil
}

func (l *CpaDataExtractor) doChuanQiCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	eventType := request.GetEventType()
	switch eventType {
	case "actived":
		basicData.EventType = entity.CpaEventTypeActivate
	case "register":
		basicData.EventType = entity.CpaEventTypeRegister
	case "commit_msg":
		basicData.EventType = entity.CpaEventTypeCommitMsg
	case "pay":
		basicData.EventType = entity.CpaEventTypePay
	case "retained":
		basicData.EventType = entity.CpaEventTypeRetained
	case "open_app":
		basicData.EventType = entity.CpaEventTypeOpenApp
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doVipShopCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}

	event := request.ExtractFirstParamString("action_type")
	switch event {
	case "ACTIVE":
		basicData.EventType = entity.CpaEventTypeActivate
	case "LAUNCH":
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "ADD_CART":
		basicData.EventType = entity.CpaEventTypeAddCart
	case "NEW_ORDER":
		basicData.EventType = entity.CpaEventTypeNewOrder
	case "OLD_ORDER":
		basicData.EventType = entity.CpaEventTypeOldOrder
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doYouLiangCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	eventType := request.GetEventType()

	switch eventType {
	case "ios_activate", "android_activate", "wxMiniProgram_activate":
		basicData.EventType = entity.CpaEventTypeActivate
	case "ios_register", "android_register", "wxMiniProgram_register":
		basicData.EventType = entity.CpaEventTypeRegister
	case "android_download":
		basicData.EventType = entity.CpaEventTypeDownload
	case "android_purchase", "ios_purchase", "wxMiniProgram_purchase":
		basicData.EventType = entity.CpaEventTypePay
	case "android_day1retention", "ios_day1retention", "wxMiniProgram_day1retention", "ios_retention", "android_retention", "wxMiniProgram_retention":
		basicData.EventType = entity.CpaEventTypeRetained
	case "android_addtocart", "ios_addtocart":
		basicData.EventType = entity.CpaEventTypeAddCart
	case "android_credit", "ios_credit", "wxMiniProgram_credit":
		basicData.EventType = entity.CpaEventTypeCredit
	case "android_in_app_uv", "ios_in_app_uv":
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "android_in_app_order", "ios_in_app_order":
		basicData.EventType = entity.CpaEventTypeNewOrder
	case "android_custom", "ios_custom", "wxMiniProgram_custom":
		basicData.EventType = entity.CpaEventTypeCustom1
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doSurgeCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}

	event := request.ExtractFirstParamString("transformType")
	switch event {
	case "12":
		basicData.EventType = entity.CpaEventTypeActivate
	case "5":
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "6":
		basicData.EventType = entity.CpaEventTypeDpSuccess
	case "13":
		basicData.EventType = entity.CpaEventTypeCommitMsg
	case "49":
		basicData.EventType = entity.CpaEventTypeCustom1
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doMeiTuanCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}

	event := request.ExtractFirstParamString("event_type")
	switch event {
	case "1":
		//激活
		basicData.EventType = entity.CpaEventTypeActivate
	case "2":
		//用户下单
		basicData.EventType = entity.CpaEventTypeNewOrder
	case "3":
		//用户当日首次 DAU
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "4":
		//有效获客
		basicData.EventType = entity.CpaEventTypeCommitMsg
	case "5":
		//付费
		basicData.EventType = entity.CpaEventTypePay
	case "6":
		//意向 uv
		basicData.EventType = entity.CpaEventTypeRetained
	case "7":
		//关键行为
		basicData.EventType = entity.CpaEventTypeRegister
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doXinyiCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}

	event := request.ExtractFirstParamString("event_type")
	switch event {
	case "1":
		basicData.EventType = entity.CpaEventTypeActivate
	case "2":
		basicData.EventType = entity.CpaEventTypeRegister
	case "5":
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "7":
		basicData.EventType = entity.CpaEventTypeRetained
	case "8":
		basicData.EventType = entity.CpaEventTypeNewOrder
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) do6BianShi(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}

	event := request.ExtractFirstParamString("op2")
	switch event {
	case "0":
		basicData.EventType = entity.CpaEventTypeActivate
	case "1":
		basicData.EventType = entity.CpaEventTypeRegister
	case "2":
		basicData.EventType = entity.CpaEventTypePay
	case "8":
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "6":
		basicData.EventType = entity.CpaEventTypeRetained
	case "26":
		basicData.EventType = entity.CpaEventTypeCommitMsg
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doXunYuEventCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	event := request.ExtractFirstParamString("event_type")

	switch event {
	case "0":
		basicData.EventType = entity.CpaEventTypeActivate
	case "1":
		basicData.EventType = entity.CpaEventTypeRegister
	case "21": //拉活
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "20": //付费
		basicData.EventType = entity.CpaEventTypePay
	case "309":
		basicData.EventType = entity.CpaEventTypeRetained
	default:
		zap.L().Error("[CpaDataExtractor]doXunYuEventCpa unknown eventype", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", event)))))
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doAlipayCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	event := request.ExtractFirstParamString("transformtype")

	switch event {
	case "1": //新登
		basicData.EventType = entity.CpaEventTypeActivate
	case "2": //拉活⾸唤
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "3": //⽀付
		basicData.EventType = entity.CpaEventTypePay
	case "4": //自定义
		basicData.EventType = entity.CpaEventTypeCustom1
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doHYXTCpa(request *TrackingRequest) error {
	//虹宇信泰
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	event := request.ExtractFirstParamString("event_type")

	switch event {
	case "0": //激活
		basicData.EventType = entity.CpaEventTypeActivate
	case "1": //注册
		basicData.EventType = entity.CpaEventTypeRegister
	case "2": //付费
		basicData.EventType = entity.CpaEventTypePay
	case "3": //次留
		basicData.EventType = entity.CpaEventTypeRetained
	case "4": //授信
		basicData.EventType = entity.CpaEventTypeCredit
	case "5": //完件
		basicData.EventType = entity.CpaEventTypeNewOrder
	case "6": //关键行为
		basicData.EventType = entity.CpaEventTypeCustom1
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doDianSiCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	event := request.ExtractFirstParamString("eventType")

	switch event {
	case "FIRST_WAKE": //首唤
		basicData.EventType = entity.CpaEventTypeCustom1
	case "WAKE": //唤端
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "ACTIVE": //激活
		basicData.EventType = entity.CpaEventTypeActivate
	case "REGISTER": //注册
		basicData.EventType = entity.CpaEventTypeRegister
	case "RETENTION1": //1日留存(次留)
		basicData.EventType = entity.CpaEventTypeRetained
	case "RETENTION7": //7日留存
		basicData.EventType = entity.CpaEventTypeRetained
	case "PURCHASE": //付费
		basicData.EventType = entity.CpaEventTypePay
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doXimaCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	event := request.ExtractFirstParamString("event")

	switch event {
	case "activate": //激活
		basicData.EventType = entity.CpaEventTypeActivate
	case "register": //注册
		basicData.EventType = entity.CpaEventTypeRegister
	case "leave": //次日留存
		basicData.EventType = entity.CpaEventTypeRetained
	case "pay": //用户付费
		basicData.EventType = entity.CpaEventTypePay
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doZhijieCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	eventType := request.ExtractFirstParamString("conv_action")

	switch eventType {
	case "ios_activate", "android_activate":
		basicData.EventType = entity.CpaEventTypeActivate
	case "ios_register", "android_register":
		basicData.EventType = entity.CpaEventTypeRegister
	case "android_download_finish":
		basicData.EventType = entity.CpaEventTypeDownload
	case "android_buy", "ios_buy":
		basicData.EventType = entity.CpaEventTypePay
	case "android_next_day_retention", "ios_next_day_retention":
		basicData.EventType = entity.CpaEventTypeRetained
	case "android_add_to_cart", "ios_add_to_cart":
		basicData.EventType = entity.CpaEventTypeAddCart
	case "android_credit", "ios_credit":
		basicData.EventType = entity.CpaEventTypeCredit
	case "android_custom", "ios_custom":
		basicData.EventType = entity.CpaEventTypeCustom1
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}

	return nil
}

func (l *CpaDataExtractor) doOneWayCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	event := request.GetEventType()

	switch event {
	case "1": //激活拉新
		basicData.EventType = entity.CpaEventTypeActivate
	case "2": //注册
		basicData.EventType = entity.CpaEventTypeRegister
	case "3": //⾸唤拉活
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "4": //付费
		basicData.EventType = entity.CpaEventTypePay
	case "5": // 次日留存
		basicData.EventType = entity.CpaEventTypeRetained
	case "51": // 3日留存
		basicData.EventType = entity.CpaEventTypeRetained3d
	case "52": // 4日留存
		basicData.EventType = entity.CpaEventTypeRetained4d
	case "53": // 5日留存
		basicData.EventType = entity.CpaEventTypeRetained5d
	case "54": // 6日留存
		basicData.EventType = entity.CpaEventTypeRetained6d
	case "6": // 7日留存
		basicData.EventType = entity.CpaEventTypeRetained7d
	case "7": //下载
		basicData.EventType = entity.CpaEventTypeDownload
	case "8": //APP内访问
		basicData.EventType = entity.CpaEventTypeCommitMsg
	case "9": //召回
		basicData.EventType = entity.CpaEventTypeCustom2
	case "10": //关键行为
		basicData.EventType = entity.CpaEventTypeCustom1
	case "11": //首购
		basicData.EventType = entity.CpaEventTypeNewOrder
	case "12", "13": //12：唤端 13：有效唤端
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "14": //下单
		basicData.EventType = entity.CpaEventTypeOldOrder
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doFeiDianCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	event := request.ExtractFirstParamString("transformType")

	switch event {
	case "ACTIVATE":
		basicData.EventType = entity.CpaEventTypeActivate
	case "ACTIVATE_DAY_1_RETENTION": //次日留存
		basicData.EventType = entity.CpaEventTypeRetained
	case "ACTIVATE_DAY_2_RETENTION": //2日留存
		basicData.EventType = entity.CpaEventTypeRetained2d
	case "ACTIVATE_DAY_3_RETENTION": //3日留存
		basicData.EventType = entity.CpaEventTypeRetained3d
	case "ACTIVATE_DAY_4_RETENTION": //4日留存
		basicData.EventType = entity.CpaEventTypeRetained4d
	case "ACTIVATE_DAY_5_RETENTION": //5日留存
		basicData.EventType = entity.CpaEventTypeRetained5d
	case "ACTIVATE_DAY_6_RETENTION": //6日留存
		basicData.EventType = entity.CpaEventTypeRetained6d
	case "ACTIVATE_DAY_7_RETENTION": //7日留存
		basicData.EventType = entity.CpaEventTypeRetained7d
	case "PULL_UP": //拉活
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "KBU": //关键行为
		basicData.EventType = entity.CpaEventTypeCustom1
	case "PAY": //付费
		basicData.EventType = entity.CpaEventTypePay //付费
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doBaiduCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}

	event := request.ExtractFirstParamString("transformType")
	switch event {
	case "1":
		basicData.EventType = entity.CpaEventTypeActivate
	case "2":
		basicData.EventType = entity.CpaEventTypeRetained
	case "3":
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "4":
		basicData.EventType = entity.CpaEventTypeRetained2d
	case "5":
		basicData.EventType = entity.CpaEventTypeRetained3d
	case "6":
		basicData.EventType = entity.CpaEventTypeRetained4d
	case "7":
		basicData.EventType = entity.CpaEventTypeRetained5d
	case "8":
		basicData.EventType = entity.CpaEventTypeRetained6d
	case "9":
		basicData.EventType = entity.CpaEventTypeRetained7d
	case "10":
		basicData.EventType = entity.CpaEventTypePay
	case "11":
		basicData.EventType = entity.CpaEventTypeRegister
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doMoWenCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}

	event := request.ExtractFirstParamString("event_type")
	switch event {
	case "1":
		basicData.EventType = entity.CpaEventTypeActivate
	case "2":
		basicData.EventType = entity.CpaEventTypeRegister
	case "3":
		basicData.EventType = entity.CpaEventTypeCredit
	case "4":
		basicData.EventType = entity.CpaEventTypePay
	case "5":
		basicData.EventType = entity.CpaEventTypeRetained
	case "6":
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "7":
		basicData.EventType = entity.CpaEventTypeDownload
	case "8":
		basicData.EventType = entity.CpaEventTypeNewOrder
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doSinaCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	event := request.GetEventType()

	switch event {
	case "1": //激活（默认）
		basicData.EventType = entity.CpaEventTypeActivate
	case "2": //下单
		basicData.EventType = entity.CpaEventTypeOldOrder
	case "3": //注册
		basicData.EventType = entity.CpaEventTypeRegister
	case "4", "42": //付费, 首日非首次付费
		basicData.EventType = entity.CpaEventTypePay
	case "5": //APP内打开
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "21": //次日留存
		basicData.EventType = entity.CpaEventTypeRetained
	case "23": //3日留存
		basicData.EventType = entity.CpaEventTypeRetained3d
	case "27": //7日留存
		basicData.EventType = entity.CpaEventTypeRetained7d
	case "41": //首日首次付费
		basicData.EventType = entity.CpaEventTypeNewOrder
	default:
		basicData.EventType = entity.CpaEventTypeActivate
	}
	return nil
}

func (l *CpaDataExtractor) doFanggeCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}

	event := request.ExtractFirstParamString("event_type")
	switch event {
	case "1":
		basicData.EventType = entity.CpaEventTypeActivate
	case "2":
		basicData.EventType = entity.CpaEventTypeRegister
	case "3":
		basicData.EventType = entity.CpaEventTypeCredit
	case "4":
		basicData.EventType = entity.CpaEventTypePay
	case "5":
		basicData.EventType = entity.CpaEventTypeRetained
	case "6":
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "7":
		basicData.EventType = entity.CpaEventTypeDownload
	case "8":
		basicData.EventType = entity.CpaEventTypeNewOrder
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doDaHangHaiCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	event := request.ExtractFirstParamString("transformType")

	switch event {
	case "1": //激活
		basicData.EventType = entity.CpaEventTypeActivate
	case "2": //新登
		basicData.EventType = entity.CpaEventTypeRegister
	case "5": //唤端
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "7": //次日回访
		basicData.EventType = entity.CpaEventTypeRetained
	case "10": //下单
		basicData.EventType = entity.CpaEventTypeNewOrder
	case "11": //购买
		basicData.EventType = entity.CpaEventTypePay
	case "12": //首唤
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "47": //唤端1小时内购买
		basicData.EventType = entity.CpaEventTypePay
	case "48": //有效唤端
		basicData.EventType = entity.CpaEventTypeOpenApp
	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doKuaiShouCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	event := request.ExtractFirstParamString("actionType")

	switch event {
	case "1": // 新增
		basicData.EventType = entity.CpaEventTypeRegister
	case "2": // 次日留存
		basicData.EventType = entity.CpaEventTypeRetained
	case "3": // 活跃
		basicData.EventType = entity.CpaEventTypeActivate
	case "4": // 流失回流
		basicData.EventType = entity.CpaEventTypeOpenApp
	case "102": // 二留
		basicData.EventType = entity.CpaEventTypeRetained2d
	case "103": // 三留
		basicData.EventType = entity.CpaEventTypeRetained3d
	case "104": // 四留
		basicData.EventType = entity.CpaEventTypeRetained4d
	case "105": // 五留
		basicData.EventType = entity.CpaEventTypeRetained5d
	case "106": // 六留
		basicData.EventType = entity.CpaEventTypeRetained6d
	case "107": // 七留
		basicData.EventType = entity.CpaEventTypeRetained7d

	default:
		basicData.EventType = entity.CpaEventTypeUnknown
	}
	return nil
}

func (l *CpaDataExtractor) doDefaultEventCpa(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return err
	}
	basicData.EventType = entity.CpaEventTypeActivate
	return nil
}
