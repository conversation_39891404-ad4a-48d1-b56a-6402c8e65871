package tracking_service

import (
	"time"

	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity_loader/ad_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"fmt"
)

type CidClkHandler struct {
	adLoader ad_loader.AdLoader

	redisClient *redis.Client
}

func NewCidClkHandler(redisClient *redis.Client, adLoader ad_loader.AdLoader) *CidClkHandler {
	return &CidClkHandler{
		redisClient: redisClient,
		adLoader:    adLoader,
	}
}

func (l *CidClkHandler) Start() error {
	return nil
}

func (l *CidClkHandler) Stop() {

}

func (l *CidClkHandler) GetName() string {
	return "CidClkHandler"
}

func (l *CidClkHandler) Do(request *TrackingRequest) error {
	ext, err := request.GetExtData()
	if err != nil {
		zap.L().Error("[CidClkHandler]get ext data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil
	}

	if len(request.GetRequestExtString()) == 0 {
		return nil
	}

	adInfo := l.adLoader.GetAdById(utils.ID(ext.AdId))
	if adInfo == nil {
		return nil
	}

	if len(adInfo.AdMonitorInfo.PddGoodsId) > 0 && len(adInfo.AdMonitorInfo.PddPid) > 0 {
		redisKey := ext.RequestId + "_" + ext.MediaSlotId

		err := l.redisClient.Set(redisKey, request.GetRequestExtString(), 7*time.Hour*24).Err()
		if err != nil {
			zap.L().Error("CidClkHandler redisClient error: , adid:, ext:%s", zap.Error(err), zap.Int64("id", int64(ext.AdId)), zap.Int64("param3", int64(request.GetRequestExtString())))
			return nil
		}
	}

	return nil
}
