package tracking_service

import (
	"context"
	"encoding/base64"
	"errors"
	"net/url"
	"regexp"
	"strings"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/dsp_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"fmt"
)

var AndroidPkgRegex = regexp.MustCompile(`\b[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)+\b`)

type RedirectHandler struct {
	dspLoader       dsp_loader.DspLoader
	shortUrlHandler *ShortUrlHandler
}

func NewRedirectHandler() *RedirectHandler {
	return &RedirectHandler{}
}

func (h *RedirectHandler) Start() error {
	return nil
}

func (h *RedirectHandler) Stop() {}

func (h *RedirectHandler) GetName() string {
	return "RedirectHandler"
}

func (h *RedirectHandler) WithDspLoader(dspLoader dsp_loader.DspLoader) *RedirectHandler {
	h.dspLoader = dspLoader
	return h
}

func (h *RedirectHandler) WithShortUrlHandler(handler *ShortUrlHandler) *RedirectHandler {
	h.shortUrlHandler = handler
	return h
}

// Should always return nil
func (h *RedirectHandler) Do(request *TrackingRequest) error {
	urlEncoded := request.GetRedirectUrl()
	if len(urlEncoded) == 0 {
		return nil
	}

	var redirUrl string
	var err error
	var prefix = "surl_"
	if strings.HasPrefix(urlEncoded, prefix) {
		if h.shortUrlHandler == nil {
			return nil
		}
		redirUrl, err = h.shortUrlHandler.UndoShorten(context.TODO(), urlEncoded[len(prefix):])
		if err != nil {
			return nil
		}
	} else {
		urlBytes, err := base64.RawURLEncoding.DecodeString(urlEncoded)
		if err != nil {
			redirUrl, err = url.QueryUnescape(urlEncoded)
			if err != nil {
				zap.L().Warn("cannot decode redirect url", zap.String("url", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", urlEncoded)))))
				return nil
			}
		} else {
			redirUrl = string(urlBytes)
		}
	}

	if !net_utils.IsValidUrl(redirUrl) {
		zap.L().Warn("invalid redirect url", zap.String("url", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", urlEncoded)))))
		return nil
	}

	if err = h.doSinax(request); err != nil {
		return nil
	}

	if err = h.doBaidu(request); err != nil {
		return nil
	}

	request.Redirect(redirUrl)
	return nil
}

// Check if has Android package name in value
func (h *RedirectHandler) containsAndroidPkg(value string) bool {
	// minimum value contains pkg should be "x.x"
	if len(value) < 3 {
		return false
	}
	return AndroidPkgRegex.MatchString(value)
}

// Extract first Android package name in value
func (h *RedirectHandler) extractAndroidPkg(value string) string {
	// minimum value contains pkg should be "x.x"
	if len(value) < 3 {
		return utils.EmptyString
	}
	return AndroidPkgRegex.FindString(value)
}

// Check X-Requested-With and User-Agent and Cookie
func (h *RedirectHandler) doSinax(request *TrackingRequest) error {
	ext, err := request.GetExtData()
	if err != nil {
		return err
	}
	if h.dspLoader == nil {
		return nil
	}
	dsp := h.dspLoader.GetDspById(utils.ID(ext.GetDspId()))
	if dsp == nil || dsp.Protocol != entity.DspProtoTypeSinax {
		return nil
	}

	// landing page
	if request.GetEventType() != "c" && request.GetEventType() != "i" {
		if request.GetXRequestedWith() != "com.sina.news" {
			return errors.New("sinax invalid X-Requested-With")
		}
	}

	if strings.Contains(request.GetUserAgent(), "Browser") ||
		strings.Contains(request.GetUserAgent(), "Windows NT") {
		return errors.New("sinax invalid User-Agent")
	}

	for _, cookie := range request.GetCookies() {
		if h.containsAndroidPkg(cookie.Raw) ||
			strings.Contains(cookie.Raw, "http:") ||
			strings.Contains(cookie.Raw, "https:") {
			return errors.New("sinax invalid Cookie")
		}
	}

	return nil
}

// Check package name in request headers
func (h *RedirectHandler) doBaidu(request *TrackingRequest) error {
	ext, err := request.GetExtData()
	if err != nil {
		return err
	}
	// 新浪百度 & 微博百度
	if ext.GetDspId() != 200021 && ext.GetDspId() != 200001 {
		return nil
	}

	xrw := request.GetXRequestedWith()
	if h.containsAndroidPkg(xrw) &&
		xrw != "com.sina.news" &&
		xrw != "com.sina.sinanews" &&
		xrw != "com.sina.weibo" {
		return errors.New("baidu invalid X-Requested-With")
	}

	if h.containsAndroidPkg(request.GetUserAgent()) {
		return errors.New("baidu invalid User-Agent")
	}

	for _, cookie := range request.GetCookies() {
		if h.containsAndroidPkg(cookie.Raw) {
			return errors.New("baidu invalid Cookie")
		}
	}

	return nil
}
