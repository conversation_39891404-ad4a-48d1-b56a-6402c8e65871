package tracking_service

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/geo_parser"
	"fmt"
)

type TrackingGeoParser struct {
	geoQuery   *geo_parser.GeoParser
	geoQueryV6 *geo_parser.GeoParserV6
	geoFile    string
	geoFileV6  string
}

func NewTrackingGeoParser(
	geoFile string, geoFileV6 string) *TrackingGeoParser {
	return &TrackingGeoParser{
		geoQuery:   geo_parser.NewGeoParser(),
		geoQueryV6: geo_parser.NewGeoParserV6(),
		geoFile:    geoFile,
		geoFileV6:  geoFileV6,
	}
}

func (l *TrackingGeoParser) Start() error {
	err := l.geoQuery.StartLoadGeoData(l.geoFile)
	if err != nil {
		return err
	}

	err = l.geoQueryV6.StartLoadGeoData(l.geoFileV6)
	if err != nil {
		return err
	}

	return nil
}

func (l *TrackingGeoParser) Stop() {

}

func (l *TrackingGeoParser) GetName() string {
	return "TrackingGeoParser"
}

func (l *TrackingGeoParser) Do(request *TrackingRequest) error {
	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		zap.L().Error("[TrackingGeoParser]get basic data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil
	}

	if len(basicData.ClientIp) > 0 {
		basicData.ClientGeoCode = l.parseGeoCode(basicData.ClientIp)
	}

	if basicData.ClientIp == basicData.RealIp {
		basicData.RealGeoCode = basicData.ClientGeoCode
	} else if len(basicData.RealIp) > 0 {
		basicData.RealGeoCode = l.parseGeoCode(basicData.RealIp)
	}

	return nil

}

func (l *TrackingGeoParser) parseGeoCode(ipStr string) uint64 {
	var geoInfo geo_parser.GeoInfo
	var err error

	if geo_parser.IsIPv6(ipStr) {
		geoInfo, err = l.geoQueryV6.QueryGeoInfo(ipStr)
		if err != nil {
			return 0
		}
	} else {
		geoInfo, err = l.geoQuery.QueryGeoInfo(ipStr)
		if err != nil {
			return 0
		}
	}

	return geoInfo.GeoId
}
