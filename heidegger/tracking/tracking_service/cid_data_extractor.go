package tracking_service

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"fmt"
)

type CidDataExtractor struct {
}

func NewCidDataExtractor() *CidDataExtractor {
	return &CidDataExtractor{}
}

func (l *CidDataExtractor) Start() error {
	return nil
}

func (l *CidDataExtractor) Stop() {

}

func (l *CidDataExtractor) GetName() string {
	return "CidDataExtractor"
}

func (l *CidDataExtractor) Do(request *TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		zap.L().Error("[CidDataExtractor]get ext data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil
	}

	basicData, err := request.GetTrackingBasicData()
	if err != nil {
		return nil
	}

	// fill request id & device id if it's empty
	if len(extData.RequestId) == 0 || extData.RequestId == "00000000-0000-0000-0000-000000000000" {
		extData.RequestId = request.GetRequestId()
	}
	if len(extData.DeviceId) == 0 {
		extData.DeviceId = request.GetDeviceId()
		extData.DeviceIdType = int32(request.GetDeviceIdType())
	}
	if len(request.GetCreativeId()) > 0 {
		extData.CreativeKey = request.GetCreativeId()
	}

	eventType := request.GetEventType()

	if eventType != entity.CpaEventTypeGroupBuy && eventType != entity.CpaEventTypePendingBuy {
		return nil
	}

	basicData.EventType = eventType

	payPrice := request.GetPayPrice()
	//if len(payPrice) == 0 {
	//	return nil
	//}

	priceData := request.GetTrackingPriceData()
	priceData.ChargePrice = type_convert.GetAssertInt32(payPrice)
	priceData.ChargeType = 2
	if eventType == entity.CpaEventTypeGroupBuy {
		priceData.IsCharge = 1
	} else {
		priceData.IsCharge = 0
	}
	priceData.IsCost = 1

	return nil
}
