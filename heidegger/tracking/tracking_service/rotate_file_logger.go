package tracking_service

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
)

type RotateFileLogger struct {
	dataType        string
	directory       string
	filenamePattern string
	fileIntervalSec int

	fd     *os.File
	fdPath string
	fdTime int64
	fdLock sync.Mutex

	counter      *prometheus_helper.LabelCounter
	successLabel []string
	failLabel    []string

	term chan struct{}
}

func NewRotateFileLogger(dataType, directory string, filenamePattern string, fileIntervalSec int) *RotateFileLogger {
	if fileIntervalSec == 0 {
		panic("RotateFileLogger with zero interval")
	}
	return &RotateFileLogger{
		dataType:        dataType,
		directory:       directory,
		filenamePattern: filenamePattern,
		fileIntervalSec: fileIntervalSec,

		counter:      prometheus_helper.RegisterLabelCounter("tracking_file_logger", []string{"type", "result"}),
		successLabel: []string{dataType, "success"},
		failLabel:    []string{dataType, "fail"},

		term: make(chan struct{}),
	}
}

func (l *RotateFileLogger) Start() error {
	if err := l.rotateFile(); err != nil {
		return err
	}

	go func() {
		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if err := l.rotateFile(); err != nil {
					zap.L().Error("rotate file error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
				}
			case <-l.term:
				return
			}
		}
	}()

	return nil
}

func (l *RotateFileLogger) Stop() {
	close(l.term)

	if l.fd != nil {
		l.fd.Close()
		l.fd = nil
	}
}

func (l *RotateFileLogger) GetName() string {
	return "RotateFileLogger"
}

func (l *RotateFileLogger) rotateFile() error {
	timeToOpen := time.Now().Unix() / int64(l.fileIntervalSec) * int64(l.fileIntervalSec)
	filePath := l.getFilePathTmp(timeToOpen)

	l.fdLock.Lock()
	defer l.fdLock.Unlock()
	if l.fd != nil {
		if l.fdTime+int64(l.fileIntervalSec) > timeToOpen {
			return nil
		}
	}

	directory := filepath.Dir(filePath)
	if err := os.MkdirAll(directory, 0755); err != nil {
		return nil
	}

	fd, err := os.OpenFile(filePath, os.O_WRONLY|os.O_APPEND|os.O_CREATE, 0644)
	if err != nil {
		return err
	}

	oldFd := l.fd
	oldPath := l.fdPath

	zap.L().Info("rotateFile, newFile:, fileTime", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", filePath)))), zap.String("param2", fmt.Sprintf("%v", time.Unix(timeToOpen, 0))).Format("2006-01-02 15:04:05"))

	l.fd = fd
	l.fdTime = timeToOpen
	l.fdPath = filePath

	if oldFd != nil {
		time.Sleep(time.Second * 3)
		oldFd.Close()

		// check if the file is empty, if it is empty, remove the file
		if stat, err := os.Stat(oldPath); err == nil && stat.Size() == 0 {
			if err := os.Remove(oldPath); err != nil {
				zap.L().Error("remove file error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
			}
			return nil
		}

		if err := os.Rename(oldPath, strings.Replace(oldPath, ".tmp", "", 1)); err != nil {
			zap.L().Error("rename file error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		}
	}

	return nil
}

func (l *RotateFileLogger) getFilePath(timeToOpen int64) string {
	return filepath.Join(l.directory, time.Unix(timeToOpen, 0).Format(l.filenamePattern))
}

func (l *RotateFileLogger) getFilePathTmp(timeToOpen int64) string {
	return filepath.Join(l.directory, time.Unix(timeToOpen, 0).Format(l.filenamePattern)) + ".tmp"
}

func (l *RotateFileLogger) Write(p string) error {
	fd := l.fd
	if fd == nil {
		return fmt.Errorf("fd is nil")
	}

	_, err := fd.WriteString(p)
	if err != nil {
		return err
	}
	return nil
}

func (l *RotateFileLogger) Do(request *TrackingRequest) error {
	if err := l.do(request); err != nil {
		l.counter.Inc(l.failLabel)
		return nil
	}

	l.counter.Inc(l.successLabel)
	return nil
}

func (l *RotateFileLogger) do(request *TrackingRequest) error {
	if l.dataType == "custom" {
		return l.customDo(request)
	}

	return l.commonDo(request)
}

func (l *RotateFileLogger) commonDo(request *TrackingRequest) error {
	ext, err := request.GetExtData()
	if err != nil {
		zap.L().Error("[RotateFileLogger]get ext data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	lineData := make([]string, 0)
	lineData = append(lineData, request.GetRequestTime().Format("2006-01-02 15:04:05"))
	lineData = append(lineData, request.GetMethod())
	lineData = append(lineData, request.GetSchema())
	lineData = append(lineData, request.GetHost())
	lineData = append(lineData, request.GetClientIP().String())
	lineData = append(lineData, request.GetRealIP().String())
	lineData = append(lineData, strconv.Itoa(int(ext.MediaId)))
	lineData = append(lineData, strconv.Itoa(int(ext.AdgroupId)))
	lineData = append(lineData, strconv.Itoa(int(ext.AdId)))
	lineData = append(lineData, ext.MediaSlotId)
	lineData = append(lineData, strconv.Itoa(int(ext.GeoCode)))
	lineData = append(lineData, request.ExtractFirstParamString(ExtDataParamKey))
	lineData = append(lineData, strconv.Itoa(int(ext.DspId)))
	lineData = append(lineData, strconv.Itoa(int(ext.CreativeId)))
	lineData = append(lineData, ext.CreativeKey)
	lineData = append(lineData, strconv.Itoa(int(ext.TrafficRequestModifierId)))
	lineData = append(lineData, strconv.Itoa(int(ext.TrafficResponseModifierId)))
	lineData = append(lineData, strconv.Itoa(int(ext.AdIndexId)))
	lineData = append(lineData, strconv.Itoa(int(ext.AdMonitorInfoId)))
	lineData = append(lineData, strconv.Itoa(int(ext.BudgetPlatformId)))
	lineData = append(lineData, strconv.Itoa(int(ext.DeviceAllocatorSettingId)))
	lineData = append(lineData, strconv.Itoa(int(ext.DspSlotId)))
	lineData = append(lineData, request.GetRawUrl())
	lineData = append(lineData, ext.DspSlotKey)
	lineData = append(lineData, request.GetUserAgent())
	lineData = append(lineData, request.GetReferer())
	lineData = append(lineData, ext.DeviceId)
	lineData = append(lineData, strconv.Itoa(int(request.GetTrackingPriceData().IsCost)))
	lineData = append(lineData, ext.RequestId)
	lineData = append(lineData, strconv.Itoa(int(request.GetTrackingPriceData().IsCharge)))
	lineData = append(lineData, strconv.Itoa(int(request.GetTrackingPriceData().ChargePrice)))
	lineData = append(lineData, strconv.Itoa(int(request.GetTrackingPriceData().WinPrice)))
	lineData = append(lineData, strconv.Itoa(int(ext.BidPrice)))
	basicData, _ := request.GetTrackingBasicData()
	if basicData != nil {
		lineData = append(lineData, strconv.Itoa(int(basicData.GetClientGeoCode())))
		lineData = append(lineData, strconv.Itoa(int(basicData.GetRealGeoCode())))
	}

	lineData = append(lineData, strconv.Itoa(int(ext.MobileOsType)))
	lineData = append(lineData, request.GetXUserAgent())
	lineData = append(lineData, request.GetExtraHeaderString())

	line := strings.Join(lineData, "\t") + "\n"

	if err := l.Write(line); err != nil {
		zap.L().Error("write file error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	return nil
}

func (l *RotateFileLogger) customDo(request *TrackingRequest) error {
	lineData := make([]string, 0)
	lineData = append(lineData, request.GetRequestTime().Format("2006-01-02 15:04:05"))
	lineData = append(lineData, request.GetMethod())
	lineData = append(lineData, request.GetSchema())
	lineData = append(lineData, request.GetHost())
	lineData = append(lineData, request.GetClientIP().String())
	lineData = append(lineData, request.GetRealIP().String())
	lineData = append(lineData, request.GetCustomType())
	lineData = append(lineData, request.GetCustomData())
	lineData = append(lineData, request.GetRawUrl())
	lineData = append(lineData, request.GetUserAgent())

	line := strings.Join(lineData, "\t") + "\n"

	if err := l.Write(line); err != nil {
		zap.L().Error("write file error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	return nil
}
