package attribution_sender

import (
	"time"

	"github.com/panjf2000/ants/v2"
	"go.uber.org/zap"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"fmt"
)

type AttributionSender struct {
	storage    AttributionStorage
	worker     *ants.Pool
	httpClient *fasthttp.Client
}

func NewAttributionSender(storage AttributionStorage) *AttributionSender {
	worker, err := ants.NewPool(1024, ants.WithNonblocking(true))
	if err != nil {
		panic(err)
	}

	return &AttributionSender{
		storage:    storage,
		worker:     worker,
		httpClient: net_utils.CreateFastHttpClient(time.Second, time.Second*60),
	}
}

func (sender *AttributionSender) Start() error {
	return nil
}

func (sender *AttributionSender) Stop() {
}

func (sender *AttributionSender) QueryUrl(urlList []string) error {
	return sender.worker.Submit(func() {
		sender.doReport(urlList)
	})
}

func (sender *AttributionSender) PostUrl(url string, header map[string]string, payload []byte) error {
	return sender.worker.Submit(func() {
		sender.doPost(url, header, payload)
	})
}

func (sender *AttributionSender) StoreMediaAttributionCandidate(key string, candidate *AttributionCandidate) error {
	return sender.storage.StoreAttributionCandidate(key, candidate)
}

func (sender *AttributionSender) doReport(urlList []string) {
	for _, url := range urlList {
		tmpUrl := url
		req := fasthttp.AcquireRequest()
		req.SetRequestURI(tmpUrl)
		req.Header.SetMethod(fasthttp.MethodGet)
		resp := fasthttp.AcquireResponse()
		err := sender.httpClient.Do(req, resp)
		fasthttp.ReleaseRequest(req)
		if err != nil {
			zap.L().Error("AttributionSender.doReport failed", zap.Error(err), zap.String("url", fmt.Sprintf("%v", tmpUrl)))
			fasthttp.ReleaseResponse(resp)
			continue
		}

		if resp.StatusCode() > 399 {
			// TODO: Convert logrus.WithFields to zap structured logging,
				"response": string(resp.Body()),
			}).Error("AttributionSender.doReport failed")
		} else {
			// TODO: Convert logrus.WithFields to zap structured logging,
				"response": string(resp.Body()),
			}).Debug("AttributionSender.doReport success")
		}

		fasthttp.ReleaseResponse(resp)
	}
}

func (sender *AttributionSender) doPost(url string, header map[string]string, payload []byte) {
	req := fasthttp.AcquireRequest()
	req.SetRequestURI(url)
	req.SetBody(payload)
	if header != nil {
		for k, v := range header {
			req.Header.Set(k, v)
		}
	}
	req.Header.SetMethod(fasthttp.MethodPost)
	resp := fasthttp.AcquireResponse()
	err := sender.httpClient.Do(req, resp)
	fasthttp.ReleaseRequest(req)
	if err != nil {
		zap.L().Error("AttributionSender.doPost failed",
			zap.Error(err),
			zap.String("url", url),
			zap.String("header", fmt.Sprintf("%v", header)),
			zap.String("body", string(payload)))
		fasthttp.ReleaseResponse(resp)
		return
	}

	if resp.StatusCode() > 399 {
		// TODO: Convert logrus.WithFields to zap structured logging,
			"code":     resp.StatusCode(),
			"response": string(resp.Body()),
		}).Error("AttributionSender.doPost failed")
	} else {
		// TODO: Convert logrus.WithFields to zap structured logging,
			"body":     string(payload),
			"response": string(resp.Body()),
		}).Info("AttributionSender.doPost success")
	}

	fasthttp.ReleaseResponse(resp)
}

func (sender *AttributionSender) LoadAttributionCandidate(key string) (*AttributionCandidate, error) {
	return sender.storage.LoadAttributionCandidate(key)
}
