package media_callback

import (
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

// Doc: https://api.e.mi.com/doc.html#/1.0.0-mdtag9b26f-omd/document-2bd1c4c260259b072818205a8ae20139
type XiaomiCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewXiaomiCallBack() *XiaomiCallBack {
	return &XiaomiCallBack{
		log: zap.L().With(zap.String("callback", "XiaomiCallBack")),
	}
}

func (c *XiaomiCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		c.log.WithError(err).Error("get ext data error")
		return err
	}

	callbackParam := c.GetS2SPostback(request)
	if len(callbackParam) == 0 || callbackParam == "__CALLBACK__" {
		c.log.WithField("url", request.GetRawUrl()).Warn("no callbackParam in request url")
		return nil
	}

	adId := extData.AdId
	ad := c.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := c.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		c.log.WithError(err).Error("parse media slot id error")
	}

	mediaSlot := c.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		c.log.WithField("slotId", mediaSlotId).Error("get media slot error")
	}
	secretKey := mediaSlot.ExtData["secretKey"]
	if len(secretKey) == 0 {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("callbackParam", fmt.Sprintf("%v", callbackParam)), zap.String("slotId", fmt.Sprintf("%v", mediaSlotId)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] required `secretKey` not set on mediaSlot")
		return err
	}

	replaceEventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	mediaEventType := c.toXiaomiEventType(replaceEventType)

	var urlParam strings.Builder
	urlParam.WriteString("callback=")
	urlParam.WriteString(url.QueryEscape(callbackParam))
	switch entity.DeviceIdType(extData.GetDeviceIdType()) {
	case entity.DeviceIdTypeMd5Imei:
		urlParam.WriteString("&imei=")
		urlParam.WriteString(url.QueryEscape(strings.ToLower(extData.GetDeviceId())))
	case entity.DeviceIdTypeRawOaid:
		urlParam.WriteString("&oaid=")
		urlParam.WriteString(url.QueryEscape(extData.GetDeviceId()))
	}
	urlParam.WriteString("&conv_time=")
	urlParam.WriteString(url.QueryEscape(strconv.FormatInt(time.Now().UnixMilli(), 10)))
	urlParam.WriteString("&convType=")
	urlParam.WriteString(url.QueryEscape(mediaEventType))

	sign := md5_utils.GetMd5String(urlParam.String() + secretKey)
	urlParam.WriteString("&sign=")
	urlParam.WriteString(url.QueryEscape(sign))

	callbackUrl := "https://trail.e.mi.com/api/callback?" + urlParam.String()

	if err = c.sender.QueryUrl([]string{callbackUrl}); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		c.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil
}

func (c *XiaomiCallBack) toXiaomiEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return "APP_ACTIVE"
	case entity.CpaEventTypeRegister:
		return "APP_REGISTER"
	case entity.CpaEventTypePay:
		return "APP_PAY"
	case entity.CpaEventTypeCommitMsg:
		return "APP_SUBMIT"
	case entity.CpaEventTypeCredit:
		return "APP_CREDIT"
	case entity.CpaEventTypeNewOrder:
		return "APP_NEW_USER_PURCHASE"
	case entity.CpaEventTypeOldOrder:
		return "APP_PURCHASE"
	case entity.CpaEventTypeDpSuccess, entity.CpaEventTypeOpenApp:
		return "APP_RE_ACTIVE" // "APP_RE_ACTIVE_WAKE_UP"
	case entity.CpaEventTypeRetained, entity.CpaEventTypeRetained2d:
		return "APP_RETENTION"
	case entity.CpaEventTypeRetained3d:
		return "APP_RETENTION_3D"
	case entity.CpaEventTypeRetained4d:
		return "APP_RETENTION_4D"
	case entity.CpaEventTypeRetained5d:
		return "APP_RETENTION_5D"
	case entity.CpaEventTypeRetained6d:
		return "APP_RETENTION_6D"
	case entity.CpaEventTypeRetained7d:
		return "APP_RETENTION_7D"
	case entity.CpaEventTypeAddCart:
		return "ADD_CART"
	case entity.CpaEventTypeCustom1:
		return "APP_ADDICTION"
	default:
		return "APP_ACTIVE"
	}
}
