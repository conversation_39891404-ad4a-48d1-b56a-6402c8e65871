package media_callback

import (
	"fmt"
	"strconv"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

type OceanEngineCallBackPayload struct {
	EventType    string                                `json:"event_type"`
	EventWeight  float64                               `json:"event_weight,omitempty"`
	Context      *OceanEngineCallBackPayloadContext    `json:"context"`
	Properties   *OceanEngineCallBackPayloadProperties `json:"properties,omitempty"`
	Timestamp    int64                                 `json:"timestamp"`
	OuterEventId string                                `json:"outer_event_id,omitempty"`
}

type OceanEngineCallBackPayloadContext struct {
	Ad     *OceanEngineCallBackPayloadContextAd     `json:"ad"`
	Device *OceanEngineCallBackPayloadContextDevice `json:"device,omitempty"`
}

type OceanEngineCallBackPayloadContextAd struct {
	Callback  string `json:"callback"`
	MatchType int    `json:"match_type,omitempty"`
}

type OceanEngineCallBackPayloadContextDevice struct {
	Platform string `json:"platform,omitempty"`
	Imei     string `json:"imei,omitempty"`
	Idfa     string `json:"idfa,omitempty"`
	Oaid     string `json:"oaid,omitempty"`
}

type OceanEngineCallBackPayloadProperties struct {
	PayAmount float64 `json:"pay_amount,omitempty"`
}

// Doc: https://event-manager.oceanengine.com/docs/8650/app_api_docs
type OceanEngineCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewOceanEngineCallBack() *OceanEngineCallBack {
	return &OceanEngineCallBack{
		log: zap.L().With(zap.String("callback", "OceanEngineCallBack")),
	}
}

func (c *OceanEngineCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		c.log.WithError(err).Error("get ext data error")
		return err
	}

	callbackParam := c.GetS2SPostback(request)
	if len(callbackParam) == 0 || callbackParam == "__CALLBACK_PARAM__" {
		c.log.WithField("url", request.GetRawUrl()).Warn("no callbackParam in request url")
		return nil
	}

	adId := extData.AdId
	ad := c.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := c.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		c.log.WithError(err).Error("parse media slot id error")
	}

	mediaSlot := c.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		c.log.WithField("slotId", mediaSlotId).Error("get media slot error")
	}

	replaceEventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	mediaEventType := c.toOceanEngineEventType(replaceEventType)
	callbackUrl := "https://analytics.oceanengine.com/api/v2/conversion"
	headers := map[string]string{"Content-Type": "application/json"}
	callback := &OceanEngineCallBackPayload{
		EventType: mediaEventType,
		Context: &OceanEngineCallBackPayloadContext{
			Ad: &OceanEngineCallBackPayloadContextAd{
				Callback: callbackParam,
			},
		},
		Timestamp: time_utils.GetTimeUnixSecondUnsafe() * 1000,
	}
	payload, err := sonic.Marshal(callback)
	if err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("callbackParam", fmt.Sprintf("%v", callbackParam)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] marshal attribution callback error")
		return err
	}

	if err = c.sender.PostUrl(callbackUrl, headers, payload); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("callbackParam", fmt.Sprintf("%v", callbackParam)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		c.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("callbackParam", fmt.Sprintf("%v", callbackParam)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil
}

// https://event-manager.oceanengine.com/docs/8650/all_events
func (c *OceanEngineCallBack) toOceanEngineEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return "active"
	case entity.CpaEventTypeRegister:
		return "active_register"
	case entity.CpaEventTypeCommitMsg:
		return "form"
	case entity.CpaEventTypePay:
		return "active_pay"
	case entity.CpaEventTypeRetained:
		return "next_day_open"
	case entity.CpaEventTypeRetained2d:
		return "retention_2d"
	case entity.CpaEventTypeRetained3d:
		return "retention_3d"
	case entity.CpaEventTypeRetained4d:
		return "retention_4d"
	case entity.CpaEventTypeRetained5d:
		return "retention_5d"
	case entity.CpaEventTypeRetained6d:
		return "retention_6d"
	case entity.CpaEventTypeRetained7d:
		return "retention_7d"
	case entity.CpaEventTypeNewOrder:
		return "first_pay"
	case entity.CpaEventTypeCredit:
		return "loan_credit"
	case entity.CpaEventTypeCustom1:
		return "game_addiction"
	case entity.CpaEventTypeOpenApp:
		return "in_app_next_day_open"
	default:
		return "active"
	}
}
