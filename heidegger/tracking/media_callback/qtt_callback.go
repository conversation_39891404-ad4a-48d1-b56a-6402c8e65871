package media_callback

import (
	"fmt"
	"strconv"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

// Doc: https://cdn-qukan.1sapp.com/qukan/pdf/api%E5%AF%B9%E6%8E%A5-%E5%BA%94%E7%94%A8%E4%B8%8B%E8%BD%BDV4.3.pdf
type QttCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewQttCallBack() *QttCallBack {
	return &QttCallBack{
		log: zap.L().With(zap.String("callback", "QttCallBack")),
	}
}

func (c *QttCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		c.log.WithError(err).Error("get ext data error")
		return err
	}

	callbackUrl := c.GetS2SPostback(request)
	if len(callbackUrl) == 0 || !net_utils.IsValidUrl(callbackUrl) {
		c.log.WithField("url", request.GetRawUrl()).Warn("no callbackUrl in request url")
		return nil
	}

	adId := extData.AdId
	ad := c.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := c.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		c.log.WithError(err).Error("parse media slot id error")
	}

	mediaSlot := c.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		c.log.WithField("slotId", mediaSlotId).Error("get media slot error")
	}

	replaceEventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	mediaEventType := c.toQttEventType(replaceEventType)
	callbackUrl = net_utils.AppendParam(callbackUrl, "op2", mediaEventType)

	if err = c.sender.QueryUrl([]string{callbackUrl}); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		c.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil
}

func (c *QttCallBack) toQttEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return "0"
	case entity.CpaEventTypeRegister:
		return "1"
	case entity.CpaEventTypePay:
		return "2"
	case entity.CpaEventTypeCommitMsg:
		return "26"
	case entity.CpaEventTypeRetained:
		return "6"
	case entity.CpaEventTypeRetained2d:
		return "36"
	case entity.CpaEventTypeRetained3d:
		return "37"
	case entity.CpaEventTypeRetained4d:
		return "38"
	case entity.CpaEventTypeRetained5d:
		return "39"
	case entity.CpaEventTypeRetained6d:
		return "40"
	case entity.CpaEventTypeRetained7d:
		return "41"
	case entity.CpaEventTypeOpenApp:
		return "8"
	case entity.CpaEventTypeOldOrder:
		return "51"
	case entity.CpaEventTypeDpSuccess:
		return "44"
	case entity.CpaEventTypeCustom1:
		return "35"
	default:
		return "0"
	}
}
