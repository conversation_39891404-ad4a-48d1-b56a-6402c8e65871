package media_callback

import (
	"fmt"
	"strconv"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/rand_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

// VivoCallbackData 表示单个回调数据
type VivoCallbackData struct {
	UserIdType      string            `json:"userIdType"`                // 用户ID类型 枚举值IMEI/IMEI_MD5/OAID/OAID_MD5/OTHER/OPENID/INSTALL_REFERRER,当事件源类型为APP时,该字段必传。
	UserId          string            `json:"userId"`                    // 用户ID 如IMEI号等,
	CvType          string            `json:"cvType"`                    // 事件类型,
	CvTime          int64             `json:"cvTime"`                    // 事件发生的系统时间戳，精确到毫秒，13位
	CvParam         string            `json:"cvParam,omitempty"`         // 事件参数 事件参数，仅在cvType传OTHER时可填，如其他事件需要回传参数，请使用extParam字段;
	CvCustom        string            `json:"cvCustom,omitempty"`        // 自定义事件参数 cvType传OTHER必填，自定义事件参数由开发者自己定义，如转化类型等。
	RequestId       string            `json:"requestId"`                 // 请求ID
	CreativeId      string            `json:"creativeId"`                // 创意ID
	DlrSrc          string            `json:"dlrSrc,omitempty"`          // 下载来源，分包参数归因必传字段。
	InstallReferrer string            `json:"installReferrer,omitempty"` // 下载来源，智能分包归因所需字段。
	Score           string            `json:"score,omitempty"`           // 数据类型分型，纯数字不设定区间范围，支持10位整数+6位小数
	ExtParam        map[string]string `json:"extParam,omitempty"`        // 扩展参数
}

// VivoCallbackRequest 表示 Vivo 回调请求
type VivoCallbackRequest struct {
	SrcType  string             `json:"srcType"`            // 来源类型 枚举值：APP/Web/Quickapp/offline(不区分大小写)
	PkgName  string             `json:"pkgName,omitempty"`  // 包名 当事件源类型为APP/Quickapp时,该字段必传
	PageUrl  string             `json:"pageUrl,omitempty"`  // 页面URL 当事件源类型为Web时,该字段必传
	SrcId    string             `json:"srcId"`              // 来源ID 营销平台事件管理工具中新建，每个产品在每个账号下仅可新建一个
	DataFrom string             `json:"dataFrom,omitempty"` // 数据来源 0=测试事件，1=vivo事件，2=头条，3=快手，4=广点通，5=华为，6=OPPO，7=小米，8=百度，9=其他。不传值默认为1-vivo事件
	DataList []VivoCallbackData `json:"dataList"`           // 数据列表 明细数据列表,长度限制1-100
}

type VivoCallbackResponse struct {
	Code    int    `json:"code"`    // 响应码
	Message string `json:"message"` // 响应消息
	Data    string `json:"data"`    // 响应数据
}

type ViVoCallBack struct {
	CallBackBase
	log     *zap.Logger
	baseUrl string
}

func NewViVoCallBack() *ViVoCallBack {
	return &ViVoCallBack{
		log:     zap.L().With(zap.String("callback", "ViVoCallBack")),
		baseUrl: "https://marketing-api.vivo.com.cn/openapi",
	}
}

func (v *ViVoCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		v.log.WithError(err).Error("get ext data error")
		return err
	}

	adId := extData.AdId
	ad := v.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := v.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		v.log.WithError(err).Error("parse media slot id error")
	}

	mediaSlot := v.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		v.log.WithField("slotId", mediaSlotId).Error("get media slot error")
		return fmt.Errorf("media slot not found, slotId:%d", mediaSlotId)
	}

	accessToken := mediaSlot.ExtData["access_token"]
	if len(accessToken) == 0 {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("slotId", fmt.Sprintf("%v", mediaSlotId)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] Vivo required `access_token` not set on mediaSlot")
		return err
	}

	advertiserId := mediaSlot.ExtData["advertiser_id"]
	if len(advertiserId) == 0 {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("slotId", fmt.Sprintf("%v", mediaSlotId)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] Vivo required `advertiser_id` not set on mediaSlot")
		return err
	}
	now := time_utils.GetTimeUnixMilli()
	nonce := rand_utils.RandString("ABCDEF1234567890", 32)
	callbackUrl := fmt.Sprintf("%s/v1/advertiser/behavior/upload?access_token=%s&timestamp=%d&nonce=%s&advertiser_id=%s",
		v.baseUrl,
		accessToken,
		now,
		nonce,
		advertiserId)

	replaceEventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	mediaEventType := v.toVivoEventType(replaceEventType)

	// 构建回调数据
	callbackData := VivoCallbackData{
		UserIdType: v.getUserIdType(entity.DeviceIdType(extData.GetDeviceIdType())),
		UserId:     extData.GetDeviceId(),
		CvType:     mediaEventType,
		CvTime:     now,
		RequestId:  extData.RequestId,
		CreativeId: extData.CreativeKey,
	}

	var PkgName string // first get from ad.adMonitorInfo, then from mediaSlot
	monitor := ad.GetAdMonitorInfo()
	if monitor != nil {
		PkgName = monitor.AppInfo.PackageName
	}
	if len(PkgName) == 0 {
		PkgName = mediaSlot.ExtData["pkg"]
	}
	if len(PkgName) == 0 {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("slotId", fmt.Sprintf("%v", mediaSlotId)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] Vivo required `pkg` not set on mediaSlot")
		return err
	}

	srcId := mediaSlot.ExtData["src_id"]
	if len(srcId) == 0 {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("slotId", fmt.Sprintf("%v", mediaSlotId)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] Vivo required `src_id` not set on mediaSlot")
		return err
	}
	// 构建请求体
	payload := VivoCallbackRequest{
		SrcType:  "web",
		PageUrl:  "https://demo.nbweiou.cn", // pageurl 随便填
		PkgName:  PkgName,
		SrcId:    srcId,
		DataList: []VivoCallbackData{callbackData},
	}

	body, err := sonic.Marshal(payload)
	if err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] marshal attribution callback error")
		return err
	}

	headers := map[string]string{
		"Content-Type":  "application/json;charset=UTF-8",
		"Authorization": fmt.Sprintf("Bearer %s", accessToken),
	}

	if err = v.sender.PostUrl(callbackUrl, headers, body); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		v.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil
}

func (v *ViVoCallBack) toVivoEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return "ACTIVATION"
	case entity.CpaEventTypeRegister:
		return "REGISTER"
	case entity.CpaEventTypeCommitMsg:
		return "SUBMIT"
	case entity.CpaEventTypeOpenApp:
		return "REACTIVATION"
	case entity.CpaEventTypeDownload:
		return "APP_DOWNLOAD"
	case entity.CpaEventTypeAddCart:
		return "IN_APP_CART"
	case entity.CpaEventTypePay:
		return "PAY"
	case entity.CpaEventTypeRetained:
		return "RETENTION_1"
	case entity.CpaEventTypeRetained2d:
		return "RETENTION_2"
	case entity.CpaEventTypeRetained3d:
		return "RETENTION_3"
	case entity.CpaEventTypeRetained4d:
		return "RETENTION_4"
	case entity.CpaEventTypeRetained5d:
		return "RETENTION_5"
	case entity.CpaEventTypeRetained6d:
		return "RETENTION_6"
	case entity.CpaEventTypeRetained7d:
		return "RETENTION_7"
	case entity.CpaEventTypeCredit:
		return "CREDIT"
	case entity.CpaEventTypeNewOrder:
		return "IN_APP_ORDER"
	case entity.CpaEventTypeOldOrder:
		return "SHOPPING"
	default:
		return "ACTIVATION"
	}
}

func (v *ViVoCallBack) getUserIdType(deviceIdType entity.DeviceIdType) string {
	switch deviceIdType {
	case entity.DeviceIdTypeRawImei:
		return "IMEI"
	case entity.DeviceIdTypeMd5Imei:
		return "IMEI_MD5"
	case entity.DeviceIdTypeRawOaid:
		return "OAID"
	case entity.DeviceIdTypeMd5Oaid:
		return "OAID_MD5"
	default:
		return "OTHER"
	}
}
