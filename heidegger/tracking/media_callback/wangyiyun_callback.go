package media_callback

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

type WangYiYunCallbackRequest struct {
	PlatType   int    `json:"platType"`             // 平台类型，0: 自研监测，1: 第三方监测
	Source     int    `json:"source"`               // 接入平台标识
	Event      int    `json:"event"`                // 转化事件（激活:0，注册:7，其他等）
	Value      string `json:"value,omitempty"`      // 针对特定事件需要回传的值，已废弃
	ClickID    string `json:"clickid"`              // 云音乐上报给监测平台的clickid
	AppKey     string `json:"appkey"`               // 对接时需要联系云音乐获取
	Sign       string `json:"sign"`                 // 签名，监测平台生成
	Timestamp  int64  `json:"timestamp"`            // 转化发生的Unix时间戳，单位秒
	IP         string `json:"ip,omitempty"`         // 发起事件的用户IP
	AppVersion string `json:"appversion,omitempty"` // 客户端版本号
	OSVersion  string `json:"osversion,omitempty"`  // 设备版本号
	Money      int64  `json:"money,omitempty"`      // 付费金额，单位分
}

type WangYiYunCallbackResponse struct {
	Code    int    `json:"code"`    // 状态码，200表示成功，其他为错误
	Message string `json:"message"` // 状态信息，成功或失败的描述
	Data    struct {
		Success bool `json:"success"` // 是否成功，true表示成功
	} `json:"data"` // 响应数据，具体的结果信息
}

type WangYiYunCallback struct {
	CallBackBase
	log     *zap.Logger
	baseUrl string
}

func NewWangYiYunCallback() *WangYiYunCallback {
	return &WangYiYunCallback{
		log:     zap.L().With(zap.String("callback", "WangYiYunCallback")),
		baseUrl: "https://ad-effect.music.163.com",
	}
}

func (w *WangYiYunCallback) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		w.log.WithError(err).Error("get ext data error")
		return err
	}

	adId := extData.AdId
	ad := w.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := w.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		w.log.WithError(err).Error("parse media slot id error")
		return fmt.Errorf("parse media slot id error")
	}

	mediaSlot := w.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		w.log.WithField("slotId", mediaSlotId).Error("get media slot error")
		return fmt.Errorf("get media slot error")
	}

	// 拓展信息
	secretkey := mediaSlot.ExtData["secretkey"]
	appkey := mediaSlot.ExtData["appkey"]
	source := mediaSlot.ExtData["source"]

	if len(secretkey) == 0 || len(appkey) == 0 || len(source) == 0 {
		return fmt.Errorf("secretkey or appkey or source is empty")
	}

	clickId := extData.RequestId

	replaceEventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	mediaEventType := w.toWangYiYunEventType(replaceEventType)
	now := time.Now().Unix()

	params := map[string]string{
		"platType":   "0",
		"source":     source,
		"event":      strconv.Itoa(mediaEventType),
		"clickid":    clickId,
		"appkey":     appkey,
		"timestamp":  strconv.FormatInt(now, 10),
		"ip":         request.GetClientIP().String(),
		"appversion": "",
		"osversion":  extData.GetMobileOsVersion(),
		"money":      "0",
	}

	sign := w.generateSign(params, secretkey)

	u, err := url.Parse(w.baseUrl)
	if err != nil {
		w.log.WithError(err).Error("parse base url error")
		return err
	}

	u.Path = "/ad/action"

	query := u.Query()
	for key, value := range params {
		query.Set(key, value)
	}
	query.Set("sign", sign)

	u.RawQuery = query.Encode()
	callbackUrl := u.String()
	if err := w.sender.QueryUrl([]string{callbackUrl}); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		w.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil
}

/*
激活:0
添加购物⻋:2
下单:3
付费:4
注册:7
其他关键⾏为:8
APP内访问:9
APP内详情⻚到站UV:10
次⽇留存:11
7⽇留存:12
*/
func (w *WangYiYunCallback) toWangYiYunEventType(newType string) int {
	switch newType {
	case entity.CpaEventTypeRegister:
		return 7
	case entity.CpaEventTypeActivate:
		return 0
	case entity.CpaEventTypePay:
		return 4
	case entity.CpaEventTypeRetained:
		return 11
	case entity.CpaEventTypeAddCart:
		return 2
	case entity.CpaEventTypeNewOrder:
		return 3
	case entity.CpaEventTypeDpSuccess, entity.CpaEventTypeOpenApp:
		return 9
	case entity.CpaEventTypeRetained7d:
		return 12
	default:
		return 0
	}
}

func (w *WangYiYunCallback) generateSign(params map[string]string, secretKey string) string {
	// 创建一个参数列表，用于存储有效的key-value对
	var keys []string
	for key := range params {
		if params[key] != "" { // 如果参数有值才参与签名
			keys = append(keys, key)
		}
	}

	// 按照字典顺序排序参数
	sort.Strings(keys)

	// 拼接secretkey + key + value + secretkey
	var builder strings.Builder
	for _, key := range keys {
		builder.WriteString(secretKey)
		builder.WriteString(key)
		builder.WriteString(params[key])
	}
	builder.WriteString(secretKey)

	// 获取拼接后的字符串
	signString := builder.String()

	// 对拼接后的字符串进行MD5加密
	hash := md5.New()
	hash.Write([]byte(signString)) // 使用UTF-8编码
	md5Hash := hash.Sum(nil)

	// 将MD5结果转成大写并返回
	return strings.ToUpper(hex.EncodeToString(md5Hash))
}
