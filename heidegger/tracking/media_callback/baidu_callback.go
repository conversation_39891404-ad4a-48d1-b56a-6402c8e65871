package media_callback

import (
	"fmt"
	"strconv"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

// Doc: https://dev2.baidu.com/content?sceneType=0&pageId=101213&nodeId=663&subhead=
type BaiduCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewBaiduCallBack() *BaiduCallBack {
	return &BaiduCallBack{
		log: zap.L().With(zap.String("callback", "BaiduCallBack")),
	}
}

func (c *BaiduCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		c.log.WithError(err).Error("get ext data error")
		return err
	}

	s2sPostback := c.GetS2SPostback(request)
	if len(s2sPostback) == 0 || !net_utils.IsValidUrl(s2sPostback) {
		return nil
	}

	adId := extData.AdId
	ad := c.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := c.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		c.log.WithError(err).Error("parse media slot id error")
	}

	akey := ""
	mediaSlot := c.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		c.log.WithField("slotId", mediaSlotId).Error("get media slot error")
	} else {
		akey = mediaSlot.ExtData["akey"]
	}

	jonType := c.getBaiduJoinType(entity.DeviceIdType(extData.GetDeviceIdType()))

	callbackUrl := c.getCallBackUrl(request, ad, akey, s2sPostback, jonType)

	if err = c.sender.QueryUrl([]string{callbackUrl}); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		c.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil

}

func (c *BaiduCallBack) getCallBackUrl(request *tracking_service.TrackingRequest, ad *entity.Ad, akey string, url string, joinType string) string {
	eventTypeNew := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	return c.getBaiduCallBackUrl(eventTypeNew, akey, url, joinType)
}

func (c *BaiduCallBack) getBaiduCallBackUrl(sysEventType string, akey string, url string, joinType string) string {
	eventType := c.toBaiduEventType(sysEventType)
	url = net_utils.ReplaceMacroSimple(url, "{{ATYPE}}", eventType)
	url = net_utils.ReplaceMacroSimple(url, "{{AVALUE}}", "0")
	url = net_utils.AppendParam(url, "join_type", joinType)

	if len(akey) > 0 {
		sign := md5_utils.GetMd5String(url + akey)
		url = net_utils.AppendParam(url, "sign", sign)
	}

	return url
}

func (c *BaiduCallBack) getBaiduJoinType(idType entity.DeviceIdType) string {
	switch idType {
	case entity.DeviceIdTypeRawImei, entity.DeviceIdTypeMd5Imei:
		return "imei"
	case
		entity.DeviceIdTypeRawMac, entity.DeviceIdTypeMd5Mac:
		return "mac"
	case
		entity.DeviceIdTypeRawAndroidId, entity.DeviceIdTypeMd5AndroidId:
		return "android_id"
	case
		entity.DeviceIdTypeRawIdfa, entity.DeviceIdTypeMd5Idfa:
		return "idfa"
	case
		entity.DeviceIdTypeRawOaid, entity.DeviceIdTypeMd5Oaid:
		return "oaid"
	case
		entity.DeviceIdTypeRawCaid, entity.DeviceIdTypeMd5Caid:
		return "caid"
	default:
		return "ip"
	}
}

func (c *BaiduCallBack) toBaiduEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return "activate"
	case entity.CpaEventTypeRegister:
		return "register"
	case entity.CpaEventTypeCommitMsg:
		return "deep_page_access"
	case entity.CpaEventTypePay:
		return "orders"
	case entity.CpaEventTypeRetained:
		return "retain_1day"
	case entity.CpaEventTypeRetained2d:
		return "retain_2day"
	case entity.CpaEventTypeRetained3d:
		return "retain_3day"
	case entity.CpaEventTypeRetained4d:
		return "retain_4day"
	case entity.CpaEventTypeRetained5d:
		return "retain_5day"
	case entity.CpaEventTypeRetained6d:
		return "retain_6day"
	case entity.CpaEventTypeRetained7d:
		return "retain_7day"
	case entity.CpaEventTypeNewOrder:
		return "ec_buy"
	case entity.CpaEventTypeDpSuccess:
		return "deeplink"
	case entity.CpaEventTypeCredit:
		return "credit_granting"
	case entity.CpaEventTypeCustom1:
		return "key_action"
	default:
		return "user_defined"
	}
}
