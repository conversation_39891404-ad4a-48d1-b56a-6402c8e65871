package media_callback

import (
	"fmt"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

type CommonCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewCommonCallBack() *CommonCallBack {
	return &CommonCallBack{
		log: zap.L().With(zap.String("callback", "CommonCallBack")),
	}
}

func (c *CommonCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		c.log.WithError(err).Error("get ext data error")
		return err
	}

	s2sPostback := c.GetS2SPostback(request)
	if len(s2sPostback) == 0 {
		return nil
	}

	if !net_utils.IsValidUrl(s2sPostback) {
		return nil
	}

	adId := extData.AdId
	ad := c.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	callbackUrl := c.getCallBackUrl(request, ad, s2sPostback)

	if err = c.sender.QueryUrl([]string{callbackUrl}); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] query attribution callback error")
		return err
	} else {
		c.zap.L().Info("[DoCallBack] query attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil

}

func (c *CommonCallBack) getCallBackUrl(request *tracking_service.TrackingRequest, ad *entity.Ad, url string) string {
	eventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	// eventType = c.toOldEventType(eventType)
	url = net_utils.ReplaceMacroSimple(url, "__EVENT__", eventType)
	url = net_utils.AppendParam(url, "event_type", eventType)

	return url
}
