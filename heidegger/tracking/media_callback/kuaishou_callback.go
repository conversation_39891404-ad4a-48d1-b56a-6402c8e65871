package media_callback

import (
	"fmt"
	"strconv"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

type KuaishouCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewKuaishouCallBack() *KuaishouCallBack {
	return &KuaishouCallBack{
		log: zap.L().With(zap.String("callback", "KuaishouCallBack")),
	}
}

func (c *<PERSON><PERSON>houCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		c.log.WithError(err).Error("get ext data error")
		return err
	}

	callbackParam := c.GetS2SPostback(request)
	if len(callbackParam) == 0 || callbackParam == "__CALLBACK__" {
		c.log.WithField("url", request.GetRawUrl()).Warn("no callbackParam in request url")
		return nil
	}

	adId := extData.AdId
	ad := c.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := c.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		c.log.WithError(err).Error("parse media slot id error")
	}

	mediaSlot := c.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		c.log.WithField("slotId", mediaSlotId).Error("get media slot error")
	}

	replaceEventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	mediaEventType := c.toKuaishouEventType(replaceEventType)
	callbackUrl := "https://ad.partner.gifshow.com/track/activate"
	callbackUrl = net_utils.AppendParam(callbackUrl, "callback", callbackParam)
	callbackUrl = net_utils.AppendParam(callbackUrl, "event_type", mediaEventType)
	callbackUrl = net_utils.AppendParam(callbackUrl, "event_time", strconv.FormatInt(time_utils.GetTimeUnixSecondUnsafe()*1000, 10))

	if err = c.sender.QueryUrl([]string{callbackUrl}); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		c.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil
}

func (c *KuaishouCallBack) toKuaishouEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return "1"
	case entity.CpaEventTypeRegister:
		return "2"
	case entity.CpaEventTypePay:
		return "3"
	case entity.CpaEventTypeCommitMsg:
		return "9"
	case entity.CpaEventTypeCredit:
		return "11"
	case entity.CpaEventTypeAddCart:
		return "13"
	case entity.CpaEventTypeNewOrder, entity.CpaEventTypeOldOrder:
		return "14"
	case entity.CpaEventTypeDpSuccess, entity.CpaEventTypeOpenApp:
		return "84"
	case entity.CpaEventTypeRetained:
		return "7"
	case entity.CpaEventTypeRetained2d:
		return "102"
	case entity.CpaEventTypeRetained3d:
		return "103"
	case entity.CpaEventTypeRetained4d:
		return "104"
	case entity.CpaEventTypeRetained5d:
		return "105"
	case entity.CpaEventTypeRetained6d:
		return "106"
	case entity.CpaEventTypeRetained7d:
		return "8"
	default:
		return "1"
	}
}
