package media_callback

import (
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

type XiMaLaYaCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewXiMaLaYaCallBack() *XiMaLaYaCallBack {
	return &XiMaLaYaCallBack{
		log: zap.L().With(zap.String("callback", "XiMaLaYaCallBack")),
	}
}

func (x *XiMaLaYaCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		x.log.WithError(err).Error("get ext data error")
		return err
	}

	adId := extData.AdId
	ad := x.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := x.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		return fmt.Errorf("mediaSlotId not found, mediaSlotId:%s", extData.MediaSlotId)
	}

	mediaSlot := x.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		return fmt.Errorf("get media slot error, mediaSlotId:%d", mediaSlotId)
	}

	replaceEventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	mediaEventType := x.toXimalayaEventType(replaceEventType)

	s2sPostback := x.GetS2SPostback(request)
	callbackUrl := ""
	if len(s2sPostback) == 0 || !net_utils.IsValidUrl(s2sPostback) {
		callbackUrl = x.generateS2SPostback(request)
	} else {
		cb, err := x.genCallbackUrl(s2sPostback, mediaEventType)
		if err != nil {
			return errors.Wrap(err, "s2sPostback genCallbackUrl error")
		}
		callbackUrl = cb
	}

	if err = x.sender.QueryUrl([]string{callbackUrl}); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		x.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil
}

/*
https://ad.ximalaya.com/ad-action?uid=535430&timestamp=1730881957135&ip=**************&os=android&
imei_md5=_IMEI_MD5_&oaid=54797b1a24ca9a42&androidid=c2ab64a3c6ff6c6a&materialid=76279352&idfa=_IDFA_&type=act&invokeid=700837507&responseid=17284651019595
*/
func (x *XiMaLaYaCallBack) generateS2SPostback(request *tracking_service.TrackingRequest) string {
	extData, _ := request.GetExtData()
	cbUrl := "https://ad.ximalaya.com/ad-action?"

	cbUrl = net_utils.AppendParam(cbUrl, "uid", "123456")
	cbUrl = net_utils.AppendParam(cbUrl, "timestamp", type_convert.GetAssertString(time.Now().UnixMilli()))
	cbUrl = net_utils.AppendParam(cbUrl, "ip", extData.GetRequestIp())
	cbUrl = net_utils.AppendParam(cbUrl, "type", "act")
	cbUrl = net_utils.AppendParam(cbUrl, "materialid", request.GetCreativeId())
	cbUrl = net_utils.AppendParam(cbUrl, "responseid", request.GetRequestId())
	cbUrl = net_utils.AppendParam(cbUrl, "invokeid", request.GetDeviceId())

	os := "android"
	if extData.MobileOsType == 2 {
		os = "ios"
	}
	cbUrl = net_utils.AppendParam(cbUrl, "os", os)

	switch extData.GetDeviceIdType() {
	case 2:
		cbUrl = net_utils.AppendParam(cbUrl, "imei_md5", extData.GetDeviceId()) // md5
	case 4:
		cbUrl = net_utils.AppendParam(cbUrl, "mac", extData.GetDeviceId())
	case 8:
		cbUrl = net_utils.AppendParam(cbUrl, "idfa", extData.GetDeviceId())
	case 9:
		cbUrl = net_utils.AppendParam(cbUrl, "oaid", extData.GetDeviceId())
	case 11:
		cbUrl = net_utils.AppendParam(cbUrl, "caid", extData.GetDeviceId())
	default:

	}

	return cbUrl
}

func (x *XiMaLaYaCallBack) genCallbackUrl(s2sPostback, mediaEventType string) (string, error) {
	u, err := url.Parse(s2sPostback)
	if err != nil {
		return "", errors.Wrap(err, "s2sPostback parse error")
	}

	params := u.Query()

	params.Set("type", mediaEventType)

	u.RawQuery = params.Encode()

	return u.String(), nil
}

func (c *XiMaLaYaCallBack) toXimalayaEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return "act"
	case entity.CpaEventTypeRegister:
		return "register"
	case entity.CpaEventTypePay:
		return "pay"
	case entity.CpaEventTypeCommitMsg:
		return "announce" // 发布
	// case entity.CpaEventTypeCredit:
	// 	return "APP_CREDIT"
	case entity.CpaEventTypeNewOrder:
		return "submitorder" // 下单
	// case entity.CpaEventTypeOldOrder:
	// 	return "APP_PURCHASE"
	case entity.CpaEventTypeDpSuccess, entity.CpaEventTypeOpenApp:
		return "active" // 首活
	case entity.CpaEventTypeRetained, entity.CpaEventTypeRetained2d:
		return "leave"
	// case entity.CpaEventTypeRetained3d:
	// 	return "APP_RETENTION_3D"
	// case entity.CpaEventTypeRetained4d:
	// 	return "APP_RETENTION_4D"
	// case entity.CpaEventTypeRetained5d:
	// 	return "APP_RETENTION_5D"
	// case entity.CpaEventTypeRetained6d:
	// 	return "APP_RETENTION_6D"
	// case entity.CpaEventTypeRetained7d:
	// 	return "APP_RETENTION_7D"
	// case entity.CpaEventTypeAddCart:
	// 	return "submitorder" // 下单
	// case entity.CpaEventTypeCustom1:
	// 	return "APP_ADDICTION"
	default:
		return "act"
	}
}
