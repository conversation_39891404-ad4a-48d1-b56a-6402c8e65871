package media_callback

import (
	"fmt"
	"net/url"
	"strconv"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

type ProductItem struct {
	ProductSkuID string `json:"product_sku_id,omitempty"` // 商品SKU ID
	SkuCount     string `json:"sku_count,omitempty"`      // 商品数量
	SkuPrice     string `json:"sku_price,omitempty"`      // 商品单价
}

type ConversionExtend struct {
	Revenue      string         `json:"revenue,omitempty"`  // 付费金额/退款金额，在conversion_type传入paid和refund时使用
	Value        string         `json:"value,omitempty"`    // 转化价值
	Currency     string         `json:"currency,omitempty"` // 币种，请使用ISO 4217的字母代码(Alphabetic Code)，例如：USD,EUR,CNY
	Quantity     string         `json:"quantity,omitempty"` // 商品数量
	OrderID      string         `json:"order_id,omitempty"` // 订单号
	StoreID      string         `json:"store_id,omitempty"`
	SearchTerm   string         `json:"search_term,omitempty"`   // 搜索关键词
	ProductItems []*ProductItem `json:"product_items,omitempty"` // 商品列表，product_item定义参照表5.3 product_item定义
}

type HuaweiCallbackPayload struct {
	Oaid                string            `json:"oaid,omitempty"`                  // 设备标识符，明文；
	ConversionType      string            `json:"conversion_type"`                 // 转化事件的类型，详细枚举值见附录3
	ContentID           string            `json:"content_id,omitempty"`            // 创意id，与该条转化行为匹配的、广告主接收到创意id
	DeviceID            string            `json:"device_id,omitempty"`             // 设备id;在指定id_type时传入
	IDType              string            `json:"id_type,omitempty"`               // Id类型。包含0,6,8,9,10,11,12,13, 20：OAID_MD5
	GaidTrackingEnabled string            `json:"gaid_tracking_enabled,omitempty"` // gaid转化跟踪是否开启
	ConversionExtend    *ConversionExtend `json:"conversion_extend,omitempty"`
	Callback            string            `json:"callback"`                       // 广告转化回传标识
	CampaignID          string            `json:"campaign_id,omitempty"`          // 与该条转化行为匹配的、广告主接收到的事件中的计划id
	Timestamp           string            `json:"timestamp"`                      // 本请求发起的时间戳，Unix时间戳，单位毫秒
	AppVersion          string            `json:"app_version,omitempty"`          // 归属话单中的应用版本
	AppPackagename      string            `json:"app_packagename,omitempty"`      // 归属话单中的应用包名
	IsRetargetting      string            `json:"is_retargetting,omitempty"`      // 是否重定向
	AttributionLookback string            `json:"attribution_lookback,omitempty"` // 归因回溯
	AttributedTouchtype string            `json:"attributed_touchtype,omitempty"` // 属性触摸类型
	MatchType           string            `json:"match_type,omitempty"`           // 匹配类型
	UserAgent           string            `json:"user_agent,omitempty"`           // 用户代理,URLEncode UTF-8格式
	Referrer            string            `json:"referrer,omitempty"`             // referrer归因参数
	ConvertionTime      string            `json:"conversion_time"`                // 转化时间, Unix时间戳，单位秒
	AdvertiserID        string            `json:" advertiser_id,omitempty"`       // 广告主id，一方数据回传时callback不用填，advertiser_id字段必填
}

type HuaweiCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewHuaweiCallBack() *HuaweiCallBack {
	return &HuaweiCallBack{
		log: zap.L().With(zap.String("callback", "HuaweiCallBack")),
	}
}

func (c *HuaweiCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		c.log.WithError(err).Error("get ext data error")
		return err
	}

	callbackParam := c.GetS2SPostback(request)
	if len(callbackParam) == 0 || callbackParam == "{callback}" {
		c.log.WithField("url", request.GetRawUrl()).Warn("no callbackParam in request url")
		return nil
	}

	adId := extData.AdId
	ad := c.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := c.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		c.log.WithError(err).Error("parse media slot id error")
	}

	mediaSlot := c.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		c.log.WithField("slotId", mediaSlotId).Error("get media slot error")
	}

	secretKey := mediaSlot.ExtData["secretKey"]
	if len(secretKey) == 0 {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("slotId", fmt.Sprintf("%v", mediaSlotId)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] Huawei required `secretKey` not set on mediaSlot")
		return err
	}

	callbackUrl := "https://ppscrowd-drcn.op.hicloud.com/action-lib-track/hiad/v2/actionupload"

	replaceEventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	mediaEventType := c.toHuaweiEventType(replaceEventType)
	now := time_utils.GetTimeUnixSecondUnsafe()
	payload := &HuaweiCallbackPayload{
		ConversionType: mediaEventType,
		ContentID:      extData.CreativeKey,
		Callback:       url.QueryEscape(callbackParam),
		Timestamp:      strconv.FormatInt(now*1000, 10),
		ConvertionTime: strconv.FormatInt(now, 10),
	}

	switch entity.DeviceIdType(extData.GetDeviceIdType()) {
	case entity.DeviceIdTypeRawImei:
		payload.DeviceID = extData.GetDeviceId()
		payload.IDType = "0"
	case entity.DeviceIdTypeMd5Oaid:
		payload.DeviceID = extData.GetDeviceId()
		payload.IDType = "20"
	case entity.DeviceIdTypeRawOaid:
		payload.Oaid = extData.GetDeviceId()
	}

	body, err := sonic.Marshal(payload)
	if err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] marshal attribution callback error")
		return err
	}

	headers := map[string]string{
		"Content-Type":  "application/json;charset=UTF-8",
		"Authorization": c.getDigest(body, now*1000, secretKey),
	}

	if err = c.sender.PostUrl(callbackUrl, headers, body); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		c.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil
}

func (c *HuaweiCallBack) toHuaweiEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return "activate"
	case entity.CpaEventTypeRegister:
		return "register"
	case entity.CpaEventTypeRetained:
		return "retain"
	case entity.CpaEventTypeCredit:
		return "credit"
	case entity.CpaEventTypeNewOrder:
		return "firstPurchase"
	case entity.CpaEventTypeOldOrder:
		return "preOrder"
	case entity.CpaEventTypePay:
		return "paid"
	case entity.CpaEventTypeAddCart:
		return "addToCart"
	case entity.CpaEventTypeRetained3d:
		return "threeDayRetain"
	case entity.CpaEventTypeRetained7d:
		return "sevenDayRetain"
	case entity.CpaEventTypeDpSuccess, entity.CpaEventTypeOpenApp:
		return "reEngage"
	case entity.CpaEventTypeCommitMsg:
		return "form_submit"
	default:
		return "activate"
	}
}

func (c *HuaweiCallBack) getDigest(data []byte, ms int64, key string) string {
	digestFmt := "Digest validTime=\"%s\", response=\"%s\""
	msg := md5_utils.HmacSha256(data, []byte(key))
	return fmt.Sprintf(digestFmt, strconv.FormatInt(ms, 10), msg)
}
