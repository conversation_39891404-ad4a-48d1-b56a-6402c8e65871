package media_callback

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity_loader/ad_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/media_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/tracking/attribution_sender"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
	"time"
)

type MediaCallBackInterface interface {
	SetAdLoader(adLoader ad_loader.AdLoader)
	SetMediaLoader(mediaLoader media_loader.MediaLoader)
	SetMediaSlotLoader(mediaSlotLoader media_loader.MediaSlotLoader)
	SetSender(sender *attribution_sender.AttributionSender)
	DoCallBack(request *tracking_service.TrackingRequest) error
}

type MediaCallBackHandler struct {
	callbacks map[utils.ID]MediaCallBackInterface

	mediaLoader     media_loader.MediaLoader
	mediaSlotLoader media_loader.MediaSlotLoader

	adLoader ad_loader.AdLoader
	sender   *attribution_sender.AttributionSender

	term chan struct{}
}

func NewMediaCallBackHandler(mediaLoader media_loader.MediaLoader, mediaSlotLoader media_loader.MediaSlotLoader, adLoader ad_loader.AdLoader, sender *attribution_sender.AttributionSender) *MediaCallBackHandler {
	return &MediaCallBackHandler{
		callbacks:       make(map[utils.ID]MediaCallBackInterface, 0),
		mediaLoader:     mediaLoader,
		mediaSlotLoader: mediaSlotLoader,
		adLoader:        adLoader,
		sender:          sender,
		term:            make(chan struct{}),
	}
}

func (m *MediaCallBackHandler) Start() error {
	if err := m.refreshMediaCallBack(); err != nil {
		return err
	}

	go m.loop()

	zap.L().Info("[MediaCallBackHandler] start ok")

	return nil
}

func (m *MediaCallBackHandler) loop() {
	ticker := time.NewTicker(time.Second * 60)
	defer ticker.Stop()

	for {
		select {
		case <-m.term:
			return
		case <-ticker.C:
			if err := m.refreshMediaCallBack(); err != nil {
				zap.L().Error("[MediaCallBackHandler][loop] refreshMediaCallBack error", zap.Error(err))
			}
		}
	}
}

func (m *MediaCallBackHandler) DoMediaCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		zap.L().Error("[MediaCallBackHandler]get ext data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	mediaCallBack := m.callbacks[utils.ID(extData.MediaId)]
	if mediaCallBack == nil {
		zap.L().Error("[MediaCallBackHandler][DoMediaCallBack] unknown media id", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(extData.MediaId)))))
		return fmt.Errorf("media call back is nil")
	}

	return mediaCallBack.DoCallBack(request)
}

func (m *MediaCallBackHandler) refreshMediaCallBack() error {
	mediaList := m.mediaLoader.GetMediaList()
	mediaCalls := make(map[utils.ID]MediaCallBackInterface)

	for _, media := range mediaList {
		if _, ok := m.callbacks[media.Id]; !ok {
			mediaC, err := CreateMediaCallBack(media)
			if err != nil {
				zap.L().Error("[MediaCallBackHandler][refreshMediaCallBack] CreateMediaCallBack error", zap.Error(err))
				continue
			}

			mediaC.SetAdLoader(m.adLoader)
			mediaC.SetMediaLoader(m.mediaLoader)
			mediaC.SetMediaSlotLoader(m.mediaSlotLoader)
			mediaC.SetSender(m.sender)

			mediaCalls[media.Id] = mediaC
		} else {
			mediaCalls[media.Id] = m.callbacks[media.Id]
		}
	}

	m.callbacks = mediaCalls

	return nil
}
