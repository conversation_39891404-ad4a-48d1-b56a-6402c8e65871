package media_callback

import (
	"fmt"
	urllib "net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

type WifiCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewWifiCallBack() *WifiCallBack {
	return &WifiCallBack{
		log: zap.L().With(zap.String("callback", "WifiCallBack")),
	}
}

func (c *WifiCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		c.log.WithError(err).Error("get ext data error")
		return err
	}

	s2sPostback := c.GetS2SPostback(request)
	if len(s2sPostback) == 0 || !net_utils.IsValidUrl(s2sPostback) {
		s2sPostback = c.generateS2SPostback(request)
	}

	adId := extData.AdId
	ad := c.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := c.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		c.log.WithError(err).Error("parse media slot id error")
	}

	secret := ""
	clientid := ""
	mediaSlot := c.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		c.log.WithField("slotId", mediaSlotId).Error("get media slot error")
	} else {
		secret = mediaSlot.ExtData["secret"]
		clientid = mediaSlot.ExtData["clientid"]
	}

	callbackUrl := c.getCallBackUrl(request, ad, clientid, secret, s2sPostback)
	if len(callbackUrl) == 0 {
		c.zap.L().Error("[DoCallBack] callbackUrl is empty", zap.String("requestId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", s2sPostback)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
		return nil
	}

	if err = c.sender.QueryUrl([]string{callbackUrl}); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		c.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}
	return nil
}

// http://c2.wkanx.com/tracking?clientid=&sid=&stime=&cid=&os=&idfa=&mac=&imei=&event_type=&ts=&sign=&extra=
func (c *WifiCallBack) generateS2SPostback(request *tracking_service.TrackingRequest) string {
	extData, _ := request.GetExtData()
	cbUrl := "http://c2.wkanx.com/tracking?"

	cbUrl = net_utils.AppendParam(cbUrl, "stime", type_convert.GetAssertString(request.GetRequestTime().Unix()))
	cbUrl = net_utils.AppendParam(cbUrl, "ts", type_convert.GetAssertString(time.Now().Unix()))
	cbUrl = net_utils.AppendParam(cbUrl, "sid", extData.GetRequestId())
	cbUrl = net_utils.AppendParam(cbUrl, "cid", type_convert.GetAssertString(extData.GetAdId()))

	os := "1"
	if extData.MobileOsType == 2 {
		os = "0"
	}
	cbUrl = net_utils.AppendParam(cbUrl, "os", os)

	switch extData.GetDeviceIdType() {
	case 2:
		cbUrl = net_utils.AppendParam(cbUrl, "imei", extData.GetDeviceId()) // md5
	case 4:
		cbUrl = net_utils.AppendParam(cbUrl, "mac", extData.GetDeviceId()) // md5
	case 8:
		cbUrl = net_utils.AppendParam(cbUrl, "idfa", extData.GetDeviceId()) // md5
	case 9:
		cbUrl = net_utils.AppendParam(cbUrl, "oaid", extData.GetDeviceId()) // raw
	case 11:
		cbUrl = net_utils.AppendParam(cbUrl, "caid", extData.GetDeviceId()) // raw
	default:
		return ""
	}

	return cbUrl
}

func (c *WifiCallBack) getCallBackUrl(request *tracking_service.TrackingRequest, ad *entity.Ad, clientid string, secret string, url string) string {
	if len(url) == 0 {
		return ""
	}

	eventTypeNew := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	eventType := c.toWifiEventType(eventTypeNew)

	url = net_utils.AppendParam(url, "clientid", clientid)
	url = net_utils.AppendParam(url, "event_type", eventType)
	url = c.signSecret(url, secret)
	// extra参数不参与签名
	url = net_utils.AppendParam(url, "extra", c.getWifiExtra(eventType))

	return url
}

func (c *WifiCallBack) signSecret(url string, secret string) string {
	// Parse the URL
	parsedURL, err := urllib.Parse(url)
	if err != nil {
		c.log.WithError(err).WithField("url", url).Error("sign parse url error")
		return url
	}

	// Extract query parameters and sort them by key
	queryParams := parsedURL.Query()
	keys := make([]string, 0, len(queryParams))
	for key := range queryParams {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	// Concatenate the sorted key-value pairs
	var sb strings.Builder
	for i, key := range keys {
		sb.WriteString(key)
		sb.WriteString("=")
		sb.WriteString(urllib.QueryEscape(queryParams.Get(key)))
		if i != len(keys)-1 {
			sb.WriteString("&")
		}
	}

	// Append the secret
	sb.WriteString(secret)
	sig := md5_utils.GetMd5StringUpper(sb.String())
	url = net_utils.AppendParam(url, "sign", sig)
	return url
}

func (c *WifiCallBack) toWifiEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return "1"
	case entity.CpaEventTypeRegister:
		return "2"
	case entity.CpaEventTypePay:
		return "3"
	case entity.CpaEventTypeRetained:
		return "5"
	case entity.CpaEventTypeCustom1:
		return "6"
	case entity.CpaEventTypeCommitMsg:
		return "10"
	case entity.CpaEventTypeDpSuccess:
		return "16"
	case entity.CpaEventTypeOpenApp:
		return "15"
	case entity.CpaEventTypeRetained7d:
		return "17"
	default:
		return "1"
	}
}

func (c *WifiCallBack) getWifiExtra(eventType string) string {
	switch eventType {
	case "1":
		return "actived"
	case "3":
		return "payment"
	default:
		return "other"
	}
}
