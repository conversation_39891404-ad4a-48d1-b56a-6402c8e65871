package media_callback

import (
	"fmt"
	"maps"
	"slices"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

type YoukuRequestOrderInfo struct {
	OrderID         string `json:"order_id"`                   // 电商订单id（父订单）
	OrderCount      int64  `json:"order_count"`                // 购买件数
	PayAmount       int64  `json:"pay_amount"`                 // 成交金额，单位分，人民币
	ProductID       string `json:"product_id"`                 // 商品id
	ProductName     string `json:"product_name"`               // 商品名称
	ImgURL          string `json:"img_url,omitempty"`          // 商品大图
	ShopName        string `json:"shop_name,omitempty"`        // 店铺名称
	Platform        string `json:"platform"`                   // 来源，xx电商平台
	ProductPrice    int64  `json:"product_price"`              // 商品价格，单位分，人民币
	ProductCategory string `json:"product_category,omitempty"` // 商品类目
}

type YoukuRequestContent struct {
	EventType  string                 `json:"event_type"`            //
	DeepConv   int64                  `json:"deep_conv,omitempty"`   // 标识event_type事件是否深度转化。 0：非深度转化回传。1:深度转化事件回传。
	ClickID    string                 `json:"click_id,omitempty"`    // 点击id
	EventTime  int64                  `json:"event_time"`            // 请重视这个字段，该字段会用于去重操作
	ConvAmount string                 `json:"conv_amount,omitempty"` // 转化收益，单位分，人民币
	ConvCount  string                 `json:"conv_count,omitempty"`  // 转化数量，不传的话，默认1
	OrderInfo  *YoukuRequestOrderInfo `json:"order_info,omitempty"`  // 订单相关信息，见下表明细
	TrackID    string                 `json:"track_id"`              // 用于追踪关联广告投放维度的id (例如：广告曝光或点击监测上报中附带的广告唯一请求id),
	AdgroupID  string                 `json:"adgroup_id,omitempty"`  // 广告投放单元id (例如：投放计划id、排期id等),建议有就回传，提高后链路匹配率
	CreativeID string                 `json:"creative_id"`           // 广告投放创意id.
	ClientIP   string                 `json:"client_ip,omitempty"`   // 回传ip,
	Idfa       string                 `json:"idfa,omitempty"`        // 设备终端idfa原值，iOS上报
	Oaid       string                 `json:"oaid,omitempty"`        // 设备终端oaid原值
	Imei       string                 `json:"imei,omitempty"`        // 设备终端imei, md5摘要值，安卓上报
	Mac        string                 `json:"mac,omitempty"`         // mac地址，去除分隔符":"的大写mac地址取md5摘要值
	Ua         string                 `json:"ua,omitempty"`          // User Agent, 需要进行URL encode之后传值
	Caid       string                 `json:"caid,omitempty"`        // 互联网广告标识，版本号以及具体的CAID值，格式为：ver1_caid1, 多个之间以逗号分割，例：20210301_xxxxxxxx,20210101_xxxxxx
	Utdid      string                 `json:"utdid,omitempty"`       // 设备utdid
	Model      string                 `json:"model,omitempty"`       // 终端机型
	Brand      string                 `json:"brand,omitempty"`       // 终端品牌
	Os         string                 `json:"os,omitempty"`          // 系统类型，取0~4 0-表示Android，1-表示iOS，2-表示Windows Phone，4-表示HarmonyOS，3-表示其他
}

type YoukuCallbackPayload struct {
	AppKey    string              `json:"appKey"`    // 回传应用来源标识，线下沟通对接时颁发给出
	Signature string              `json:"signature"` // 签名
	Content   YoukuRequestContent `json:"content"`   // 回传数据内容
}

type YoukuCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewYoukuCallBack() *YoukuCallBack {
	return &YoukuCallBack{
		log: zap.L().With(zap.String("callback", "YoukuCallBack")),
	}
}

func (c *YoukuCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		c.log.WithError(err).Error("get ext data error")
		return err
	}

	var trackId = c.GetS2SPostback(request)
	if len(trackId) == 0 || trackId == "__TRACKID__" {
		trackId = extData.GetRequestId()
		if len(trackId) == 0 {
			c.log.WithField("url", request.GetRawUrl()).Warn("no callbackParam in request url")
			return nil
		}
	}

	adId := extData.AdId
	ad := c.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := c.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		c.log.WithError(err).Error("parse media slot id error")
	}

	mediaSlot := c.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		c.log.WithField("slotId", mediaSlotId).Error("get media slot error")
	}

	appKey := mediaSlot.ExtData["appKey"]
	if len(appKey) == 0 {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("slotId", fmt.Sprintf("%v", mediaSlotId)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] Youku required `appKey` not set on mediaSlot")
		return err
	}
	token := mediaSlot.ExtData["token"]
	if len(token) == 0 {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("slotId", fmt.Sprintf("%v", mediaSlotId)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] Youku required `token` not set on mediaSlot")
		return err
	}

	callbackUrl := "https://missile.youku.com/api/ad/conv/v2"

	replaceEventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	mediaEventType := c.toYoukuEventType(replaceEventType)
	now := time_utils.GetTimeUnixMilli()
	// payload := &YoukuCallbackPayload{
	// 	AppKey: appKey,
	// 	Content: YoukuRequestContent{
	// 		EventType:  mediaEventType,
	// 		EventTime:  now,
	// 		TrackID:    trackId,
	// 		CreativeID: extData.CreativeKey,
	// 	},
	// }
	payload := map[string]any{
		"appKey": appKey,
	}
	content := map[string]any{
		"event_type":  mediaEventType,
		"event_time":  now,
		"track_id":    trackId,
		"creative_id": extData.CreativeKey,
		"client_ip":   extData.RequestIp,
	}

	switch entity.DeviceIdType(extData.GetDeviceIdType()) {
	case entity.DeviceIdTypeRawImei:
		// payload.Content.Imei = md5_utils.GetMd5String(extData.GetDeviceId())
		content["imei"] = md5_utils.GetMd5String(extData.GetDeviceId())
	case entity.DeviceIdTypeMd5Imei:
		// payload.Content.Imei = extData.GetDeviceId()
		content["imei"] = extData.GetDeviceId()
	case entity.DeviceIdTypeRawIdfa:
		// payload.Content.Idfa = extData.GetDeviceId()
		content["idfa"] = extData.GetDeviceId()
	case entity.DeviceIdTypeRawOaid:
		// payload.Content.Oaid = extData.GetDeviceId()
		content["oaid"] = extData.GetDeviceId()
	case entity.DeviceIdTypeRawCaid:
		// payload.Content.Caid = extData.GetDeviceId()
		content["caid"] = extData.GetDeviceId()
	}

	payload["signature"], err = c.GetSignature(content, appKey, token)
	payload["content"] = content
	if err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] signature attribution callback error")
		return err
	}

	body, err := sonic.Marshal(payload)
	if err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] marshal attribution callback error")
		return err
	}

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	if err = c.sender.PostUrl(callbackUrl, headers, body); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		c.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil
}

func (c *YoukuCallBack) toYoukuEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeDownload:
		return "download"
	case entity.CpaEventTypeRegister:
		return "register"
	case entity.CpaEventTypeActivate:
		return "active"
	case entity.CpaEventTypePay:
		return "pay"
	case entity.CpaEventTypeCommitMsg:
		return "form_submit"
	case entity.CpaEventTypeRetained:
		return "retention"
	case entity.CpaEventTypeAddCart:
		return "add_cart"
	case entity.CpaEventTypeNewOrder:
		return "new_order"
	case entity.CpaEventTypeOldOrder:
		return "old_order"
	case entity.CpaEventTypeCredit:
		return "credit_granting"
	case entity.CpaEventTypeDpSuccess, entity.CpaEventTypeOpenApp:
		return "wake"
	case entity.CpaEventTypeRetained7d:
		return "7day_retention"
	default:
		return "activate"
	}
}

func (c *YoukuCallBack) GetSignature(strMap map[string]any, key string, token string) (string, error) {
	var sb strings.Builder
	sb.WriteString(key)
	var idx = 0
	for _, key := range slices.Sorted(maps.Keys(strMap)) {
		idx++
		if idx > 1 {
			sb.WriteRune('&')
		}
		sb.WriteString(key)
		sb.WriteRune('=')
		var value = strMap[key]
		if value != nil {
			// implement `String.valueOf(new TreeMap<>((Map) value))` in Java
			if v, ok := value.(map[string]any); ok {
				var sb2 strings.Builder
				sb2.WriteRune('{')
				for i, k := range slices.Sorted(maps.Keys(v)) {
					sb2.WriteString(k)
					sb2.WriteRune('=')
					if sv, ok := v[k].(string); ok {
						sb2.WriteString(sv)
					} else if iv, ok := v[k].(int64); ok {
						sb2.WriteString(strconv.FormatInt(iv, 10))
					} else if fv, ok := v[k].(float64); ok {
						sb2.WriteString(strconv.FormatInt(int64(fv), 10))
					} else {
						sb2.WriteString(fmt.Sprintf("%v", v[k]))
					}

					if i != len(v)-1 {
						sb2.WriteString(", ")
					}
				}
				sb2.WriteRune('}')
				sb.WriteString(sb2.String())
			} else if v, ok := value.(int64); ok {
				sb.WriteString(strconv.FormatInt(v, 10))
			} else if v, ok := value.(float64); ok {
				sb.WriteString(strconv.FormatInt(int64(v), 10))
			} else if v, ok := value.(string); ok {
				sb.WriteString(v)
			} else {
				sb.WriteString(fmt.Sprintf("%v", value))
			}
		}
	}

	msg := md5_utils.HmacSha256([]byte(sb.String()), []byte(token))
	return md5_utils.GetMd5StringUpper(msg), nil
}
