package media_callback

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/ad_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/media_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/tracking/attribution_sender"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

type CallBackBase struct {
	adLoader        ad_loader.AdLoader
	mediaLoader     media_loader.MediaLoader
	mediaSlotLoader media_loader.MediaSlotLoader
	sender          *attribution_sender.AttributionSender
}

func (c *CallBackBase) toOldEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return entity.OldCpaEventActived
	case entity.CpaEventTypeRegister:
		return entity.OldCpaEventRegister
	case entity.CpaEventTypeCommitMsg:
		return entity.OldCpaEventCommitMsg
	case entity.CpaEventTypePay:
		return entity.OldCpaEventPay
	case entity.CpaEventTypeRetained:
		return entity.OldCpaEventRetained
	case entity.CpaEventTypeOpenApp:
		return entity.OldCpaEventOpenApp
	case entity.CpaEventTypeDownload:
		return entity.OldCpaEventDownload
	case entity.CpaEventTypeAddCart:
		return entity.OldCpaEventAddCart
	case entity.CpaEventTypeNewOrder:
		return entity.OldCpaEventNewOrder
	case entity.CpaEventTypeOldOrder:
		return entity.OldCpaEventOldOrder
	case entity.CpaEventTypeDpSuccess:
		return entity.OldCpaEventOldDpSuccess
	case entity.CpaEventTypeUnknown:
		return entity.OldCpaEventTypeUnKnown
	default:
		return entity.OldCpaEventTypeUnKnown
	}
}

func (c *CallBackBase) SetSender(sender *attribution_sender.AttributionSender) {
	c.sender = sender
}

func (c *CallBackBase) SetAdLoader(adLoader ad_loader.AdLoader) {
	c.adLoader = adLoader
}

func (c *CallBackBase) SetMediaLoader(mediaLoader media_loader.MediaLoader) {
	c.mediaLoader = mediaLoader
}

func (c *CallBackBase) SetMediaSlotLoader(mediaSlotLoader media_loader.MediaSlotLoader) {
	c.mediaSlotLoader = mediaSlotLoader
}

func (c *CallBackBase) GetMediaSlotInfo(mediaSlotId utils.ID) *entity.MediaSlotInfo {
	return c.mediaSlotLoader.GetMediaSlotById(mediaSlotId)
}

func (c *CallBackBase) GetMediaInfo(mediaId utils.ID) *entity.Media {
	return c.mediaLoader.GetMediaById(mediaId)
}

func (c *CallBackBase) GetAdInfo(adId utils.ID) *entity.Ad {
	return c.adLoader.GetAdById(adId)
}

func (c *CallBackBase) GetS2SPostback(request *tracking_service.TrackingRequest) string {
	extData, _ := request.GetExtData()

	adId := extData.GetAdId()

	s2sPostback := request.GetRawS2SPostback()
	var err error
	if !net_utils.IsValidUrl(s2sPostback) {
		s2sPostback, err = net_utils.TryFixEncodedUrl(s2sPostback)
		if err != nil {
			zap.L().Error("GetS2SPostback invalid, adId:, s2sPostback:%s, err:%s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(adId)))), zap.Int64("param2", int64(s2sPostback)), zap.Int64("param3", int64(err.Error())))
			return ""
		}
	}

	return s2sPostback
}
