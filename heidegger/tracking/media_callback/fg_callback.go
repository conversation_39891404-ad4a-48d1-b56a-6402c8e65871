package media_callback

import (
	"fmt"
	"strconv"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

type FgCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewFgCallBack() *FgCallBack {
	return &FgCallBack{
		log: zap.L().With(zap.String("callback", "FgCallBack")),
	}
}

func (c *FgCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		c.log.WithError(err).Error("get ext data error")
		return err
	}

	callbackUrl := c.GetS2SPostback(request)
	if len(callbackUrl) == 0 || !net_utils.IsValidUrl(callbackUrl) {
		c.log.WithField("url", request.GetRawUrl()).Warn("no callbackUrl in request url")
		return nil
	}

	adId := extData.AdId
	ad := c.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := c.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		c.log.WithError(err).Error("parse media slot id error")
	}

	mediaSlot := c.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		c.log.WithField("slotId", mediaSlotId).Error("get media slot error")
	}

	replaceEventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	mediaEventType := c.toFgEventType(replaceEventType)
	// NOTE: 文档为: event, 线下通知更新为: event_type
	callbackUrl = net_utils.AppendParam(callbackUrl, "event_type", mediaEventType)
	callbackUrl = net_utils.AppendParam(callbackUrl, "opp_", "0")

	if err = c.sender.QueryUrl([]string{callbackUrl}); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		c.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil
}

func (c *FgCallBack) toFgEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return "1"
	case entity.CpaEventTypeRegister:
		return "2"
	case entity.CpaEventTypeCredit:
		return "3"
	case entity.CpaEventTypePay:
		return "4"
	case entity.CpaEventTypeRetained:
		return "5"
	case entity.CpaEventTypeDpSuccess, entity.CpaEventTypeOpenApp:
		return "6"
	case entity.CpaEventTypeDownload:
		return "7"
	case entity.CpaEventTypeOldOrder, entity.CpaEventTypeNewOrder:
		return "8"
	default:
		return "1"
	}
}
