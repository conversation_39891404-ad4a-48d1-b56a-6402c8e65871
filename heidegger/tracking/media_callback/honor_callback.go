package media_callback

import (
	"fmt"
	"strconv"
	"time"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

type HonorCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewHonorCallBack() *HonorCallBack {
	return &HonorCallBack{
		log: zap.L().With(zap.String("callback", "HonorCallBack")),
	}
}

func (c *HonorCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		c.log.WithError(err).Error("get ext data error")
		return err
	}

	adId := extData.AdId
	ad := c.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := c.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		c.log.WithError(err).Error("parse media slot id error")
	}

	mediaSlot := c.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		c.log.WithField("slotId", mediaSlotId).Error("get media slot error")
	}

	replaceEventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	mediaEventType := c.toHonorEventType(replaceEventType)
	callbackUrl := "https://ads-drcn.platform.hihonorcloud.com/api/ad-tracking/v1/conversion"
	callbackUrl = net_utils.AppendParam(callbackUrl, "trackId", extData.RequestId) // NOTE: __TRACK_ID__
	callbackUrl = net_utils.AppendParam(callbackUrl, "conversionId", mediaEventType)
	callbackUrl = net_utils.AppendParam(callbackUrl, "conversionTime", strconv.FormatInt(time.Now().UnixMilli(), 10))
	callbackUrl = net_utils.AppendParam(callbackUrl, "advertiserId", extData.CreativeKey) // NOTE: __ADVERTISER_ID__
	switch entity.DeviceIdType(extData.DeviceIdType) {
	case entity.DeviceIdTypeRawOaid, entity.DeviceIdTypeMd5Oaid:
		callbackUrl = net_utils.AppendParam(callbackUrl, "oaid", extData.DeviceId) // NOTE: __OAID__
	}

	if err = c.sender.QueryUrl([]string{callbackUrl}); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		c.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil
}

func (c *HonorCallBack) toHonorEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return "10001"
	case entity.CpaEventTypeRegister:
		return "10002"
	case entity.CpaEventTypeRetained:
		return "10003"
	case entity.CpaEventTypeRetained2d:
		return "100001"
	case entity.CpaEventTypeRetained3d:
		return "10013"
	case entity.CpaEventTypeRetained7d:
		return "10014"
	case entity.CpaEventTypeDpSuccess, entity.CpaEventTypeOpenApp:
		return "10022"
	case entity.CpaEventTypePay:
		return "10004"
	case entity.CpaEventTypeCommitMsg:
		return "20001"
	case entity.CpaEventTypeCredit:
		return "80003"
	case entity.CpaEventTypeAddCart:
		return "10012"
	case entity.CpaEventTypeNewOrder:
		return "10017"
	case entity.CpaEventTypeOldOrder:
		return "10007"
	default:
		return "10001"
	}
}
