package media_callback

import (
	"encoding/base64"
	"fmt"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/price_coder/aes_utils"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

type OppoCallbackPayload struct {
	Imei         string  `json:"imei"`
	Oaid         string  `json:"ouId"`
	Mac          string  `json:"mac,omitempty"`
	ClientIp     string  `json:"clientIp,omitempty"`
	Timestamp    int64   `json:"timestamp"`
	Pkg          string  `json:"pkg"`
	DataType     int32   `json:"dataType"`
	PayId        string  `json:"payId,omitempty"`
	CustomType   int32   `json:"customType,omitempty"`
	Channel      int32   `json:"channel"`
	Type         int32   `json:"type"`
	AppType      int32   `json:"appType,omitempty"`
	PayAmount    int64   `json:"payAmount,omitempty"`
	AscribeType  int32   `json:"ascribeType"`
	AdId         int64   `json:"adId"`
	RequestId    string  `json:"requestId,omitempty"`
	QualityScore float32 `json:"qualityScore,omitempty"`
}

type OppoCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewOppoCallBack() *OppoCallBack {
	return &OppoCallBack{
		log: zap.L().With(zap.String("callback", "OppoCallBack")),
	}
}

func (c *OppoCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		c.log.WithError(err).Error("get ext data error")
		return err
	}

	adId := extData.AdId
	ad := c.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := c.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		c.log.WithError(err).Error("parse media slot id error")
	}

	mediaSlot := c.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		c.log.WithField("slotId", mediaSlotId).Error("get media slot error")
	}

	var pkg string // first get from ad.adMonitorInfo, then from mediaSlot
	monitor := ad.GetAdMonitorInfo()
	if monitor != nil {
		pkg = monitor.AppInfo.PackageName
	}
	if len(pkg) == 0 {
		pkg = mediaSlot.ExtData["pkg"]
	}
	if len(pkg) == 0 {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("slotId", fmt.Sprintf("%v", mediaSlotId)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] Oppo required `pkg` not set on mediaSlot")
		return err
	}
	mediaAdId, err := strconv.ParseInt(extData.CreativeKey, 10, 64)
	if err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("mediaAdId", fmt.Sprintf("%v", extData.CreativeKey)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] Oppo required `adId` not found")
		return err
	}

	callbackUrl := "https://api.ads.heytapmobi.com/api/uploadActiveData"
	salt := "e0u6fnlag06lc3pl"           // 文档内固定值
	aesKey := "XGAXicVG5GMBsx5bueOe4w==" // 文档内固定值

	replaceEventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	mediaEventType := c.toOppoEventType(replaceEventType)
	ts := time_utils.GetTimeUnixSecondUnsafe() * 1000
	payload := &OppoCallbackPayload{
		ClientIp:    extData.GetRequestIp(),
		Timestamp:   ts,
		Pkg:         pkg, // 投放应用的包名
		DataType:    mediaEventType,
		Channel:     1,
		Type:        0,
		AppType:     1,
		AscribeType: 1,
		AdId:        mediaAdId, // 广告主回传转化数据时，附带已经归因好的广告id
		RequestId:   extData.GetRequestId(),
	}

	var isImeiMd5, isOaidMd5 bool
	switch entity.DeviceIdType(extData.GetDeviceIdType()) {
	case entity.DeviceIdTypeMd5Imei:
		isImeiMd5 = true
		payload.Imei, err = c.aesEncode(extData.GetDeviceId(), aesKey)
	case entity.DeviceIdTypeRawImei:
		payload.Imei, err = c.aesEncode(extData.GetDeviceId(), aesKey)
	case entity.DeviceIdTypeMd5Oaid:
		isOaidMd5 = true
		payload.Oaid, err = c.aesEncode(extData.GetDeviceId(), aesKey)
	case entity.DeviceIdTypeRawOaid:
		payload.Oaid, err = c.aesEncode(extData.GetDeviceId(), aesKey)
	case entity.DeviceIdTypeRawMac:
		payload.Mac, _ = c.aesEncode(extData.GetDeviceId(), aesKey)
	}
	if err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] Aes encrypt imei/oaid error")
		return err
	}
	if isImeiMd5 {
		payload.Type = 1
	} else if isOaidMd5 {
		payload.Type = 2
	}

	body, err := sonic.Marshal(payload)
	if err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] marshal attribution callback error")
		return err
	}

	headers := map[string]string{
		"Content-Type": "application/json;charset=UTF-8",
		"signature":    c.getSignature(body, ts, salt),
		"timestamp":    strconv.FormatInt(ts, 10),
	}

	if err = c.sender.PostUrl(callbackUrl, headers, body); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		c.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil
}

func (c *OppoCallBack) toOppoEventType(newType string) int32 {
	switch newType {
	case entity.CpaEventTypeActivate:
		return 1
	case entity.CpaEventTypeRegister:
		return 2
	case entity.CpaEventTypeRetained:
		return 4
	case entity.CpaEventTypeCredit:
		return 5
	case entity.CpaEventTypeNewOrder, entity.CpaEventTypeOldOrder:
		return 6
	case entity.CpaEventTypePay:
		return 7
	case entity.CpaEventTypeRetained3d:
		return 9
	case entity.CpaEventTypeRetained4d:
		return 10
	case entity.CpaEventTypeRetained5d:
		return 11
	case entity.CpaEventTypeRetained6d:
		return 12
	case entity.CpaEventTypeRetained7d:
		return 13
	case entity.CpaEventTypeDpSuccess, entity.CpaEventTypeOpenApp:
		return 15
	default:
		return 1
	}
}

func (c *OppoCallBack) getSignature(data []byte, ts int64, salt string) string {
	tss := strconv.FormatInt(ts, 10)
	var sb strings.Builder
	sb.Grow(len(data) + len(tss) + len(salt))
	sb.Write(data)
	sb.WriteString(tss)
	sb.WriteString(salt)
	return md5_utils.GetMd5String(sb.String())
}

func (c *OppoCallBack) aesEncode(data string, key string) (string, error) {
	if len(data) < 1 {
		return "", nil
	}

	ekey, err := base64.StdEncoding.DecodeString(key)
	if err != nil {
		return "", err
	}
	block, err := aes_utils.CreateCipher(ekey)
	if err != nil {
		return "", err
	}

	edata, err := aes_utils.EncryptAESECB([]byte(data), block)
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(edata), nil
}
