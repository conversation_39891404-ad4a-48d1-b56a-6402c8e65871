package media_callback

import (
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
)

/**
insert into `ad_enums` (`title`, `value`, `parent`, `create_time`, `update_time`) values ('万唯', 'oneway', 10182, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
*/

// Doc: https://doc.oneway.mobi/OCPX/#/
type OnewayCallBack struct {
	CallBackBase
	log *zap.Logger
}

func NewOnewayCallBack() *OnewayCallBack {
	return &OnewayCallBack{
		log: zap.L().With(zap.String("callback", "OnewayCallBack")),
	}
}

func (c *OnewayCallBack) DoCallBack(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		c.log.WithError(err).Error("get ext data error")
		return err
	}

	callbackUrl := c.GetS2SPostback(request)
	if len(callbackUrl) == 0 || !net_utils.IsValidUrl(callbackUrl) {
		c.log.WithField("url", request.GetRawUrl()).Warn("no callbackUrl in request url")
		return nil
	}

	adId := extData.AdId
	ad := c.GetAdInfo(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	media := c.GetMediaInfo(utils.ID(extData.MediaId))
	if media == nil {
		return fmt.Errorf("media not found, MediaId:%d", extData.MediaId)
	}

	mediaSlotId, err := strconv.ParseInt(extData.MediaSlotId, 10, 64)
	if err != nil {
		c.log.WithError(err).Error("parse media slot id error")
	}

	mediaSlot := c.GetMediaSlotInfo(utils.ID(mediaSlotId))
	if mediaSlot == nil {
		c.log.WithField("slotId", mediaSlotId).Error("get media slot error")
	}

	replaceEventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	mediaEventType := c.toOnewayEventType(replaceEventType)
	callbackUrl = strings.ReplaceAll(callbackUrl, "__CONV_TYPE__", mediaEventType)

	if err = c.sender.QueryUrl([]string{callbackUrl}); err != nil {
		zap.L().Error("error with fields", zap.Error(err), zap.String("requestId", fmt.Sprintf("%v", extData.RequestId)), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId))).Error("[DoCallBack] send attribution callback error")
		return err
	} else {
		c.zap.L().Info("[DoCallBack] send attribution callback success", zap.String("requesitId", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", extData.RequestId)))), zap.String("url", fmt.Sprintf("%v", callbackUrl)), zap.String("eventType", fmt.Sprintf("%v", request.GetEventType())), zap.String("isCharge", fmt.Sprintf("%v", request.GetTrackingPriceData().IsCharge)), zap.String("adId", fmt.Sprintf("%v", adId)))
	}

	return nil
}

func (c *OnewayCallBack) toOnewayEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return "1"
	case entity.CpaEventTypeRegister:
		return "2"
	case entity.CpaEventTypeOpenApp:
		return "3"
	case entity.CpaEventTypePay:
		return "4"
	case entity.CpaEventTypeRetained:
		return "5"
	case entity.CpaEventTypeRetained2d:
		return "5"
	case entity.CpaEventTypeRetained3d:
		return "51"
	case entity.CpaEventTypeRetained4d:
		return "52"
	case entity.CpaEventTypeRetained5d:
		return "53"
	case entity.CpaEventTypeRetained6d:
		return "54"
	case entity.CpaEventTypeRetained7d:
		return "6"
	case entity.CpaEventTypeDownload:
		return "7"
	case entity.CpaEventTypeAddiction:
		return "10"
	case entity.CpaEventTypeNewOrder:
		return "11"
	case entity.CpaEventTypeDpSuccess:
		return "13"
	case entity.CpaEventTypeOldOrder:
		return "14"
	case entity.CpaEventTypeCustom1:
		return "99"
	default:
		return "1"
	}
}
