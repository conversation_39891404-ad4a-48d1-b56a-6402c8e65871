package attribution_handler

import (
	"fmt"
	"math/rand"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/ad_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/media_loader"
	"gitlab.com/dev/heidegger/library/mclock"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"gitlab.com/dev/heidegger/tracking/attribution_sender"
	"gitlab.com/dev/heidegger/tracking/media_callback"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
	"gitlab.com/dev/heidegger/user_segment"
)

type AttributionTrackingRequestHandler struct {
	sender               *attribution_sender.AttributionSender
	histogram            *prometheus_helper.LabelHistogram
	adLoader             ad_loader.AdLoader
	mediaLoader          media_loader.MediaLoader
	mediaCallBackHandler *media_callback.MediaCallBackHandler

	UserSegmentClient user_segment.UserSegmentClient
}

func NewAttributionTrackingRequestHandler(sender *attribution_sender.AttributionSender, mediaLoader media_loader.MediaLoader, mediaSlotLoader media_loader.MediaSlotLoader, adLoader ad_loader.AdLoader, userSegmentClient user_segment.UserSegmentClient) *AttributionTrackingRequestHandler {
	return &AttributionTrackingRequestHandler{
		sender:               sender,
		adLoader:             adLoader,
		UserSegmentClient:    userSegmentClient,
		mediaCallBackHandler: media_callback.NewMediaCallBackHandler(mediaLoader, mediaSlotLoader, adLoader, sender),
		histogram:            prometheus_helper.RegisterLabelHistogram("AttributionTrackingHandler_Callback", []string{"result"}),
	}
}

func (h *AttributionTrackingRequestHandler) Start() error {
	err := h.mediaCallBackHandler.Start()
	if err != nil {
		return err
	}

	return nil
}

func (h *AttributionTrackingRequestHandler) Stop() {

}

func (h *AttributionTrackingRequestHandler) GetName() string {
	return "AttributionTrackingRequestHandler"
}

func (h *AttributionTrackingRequestHandler) Do(request *tracking_service.TrackingRequest) error {
	err := h.handleAttribution(request)
	if err != nil {
		zap.L().Error("handle attribution error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
	}

	return nil
}

func (h *AttributionTrackingRequestHandler) isDspTargetEventType(request *tracking_service.TrackingRequest, eventType string) bool {
	if len(request.GetCpaEventType()) == 0 {
		return true
	}

	if request.GetCpaEventType() == eventType {
		return true
	}

	return false
}

func (h *AttributionTrackingRequestHandler) discountAttribution(request *tracking_service.TrackingRequest, discountRate int32) bool {
	eventType := request.AssertGetTrackingBasicData().EventType

	ext, _ := request.GetExtData()
	deviceId := ext.GetDeviceId()
	adId := ext.GetAdId()
	deviceId += "_" + type_convert.GetAssertString(adId)

	userSegment, err := h.UserSegmentClient.GetUserSegment(deviceId)
	if err == nil {
		if userSegment.ContainsTag(entity.UserTagActive) {
			return true
		} else if userSegment.ContainsTag(entity.UserTagRegister) {
			if eventType != entity.CpaEventTypeActivate {
				return true
			}
		}
	}

	if request.GetTrackingPriceData().IsCost == 1 && rand.Int31n(10000) < discountRate {
		if userTag, ok := entity.CpaEventUserTagMap[eventType]; ok {
			h.UserSegmentClient.AddUserSegmentAsync(deviceId, userTag, 1, 86400, 0)
		}

		return true
	}

	return false
}

func (h *AttributionTrackingRequestHandler) handleAttribution(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		zap.L().Error("[AttributionTrackingRequestHandler]get ext data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	requestId := extData.GetRequestId()
	adId := extData.GetAdId()
	//s2sPostback := request.GetS2SPostback()
	//if !net_utils.IsValidUrl(s2sPostback) {
	//	s2sPostback, err = net_utils.TryFixEncodedUrl(s2sPostback)
	//	if err != nil {
	//		return fmt.Errorf("s2sPostback invalid, adId:%d, s2sPostback:%s", adId, s2sPostback)
	//	}
	//}

	ad := h.adLoader.GetAdById(utils.ID(adId))
	if ad == nil {
		return fmt.Errorf("ad not found, adId:%d", adId)
	}

	if ad.AdType != entity.AdTypeDsp {
		monitor := ad.GetAdMonitorInfo()
		if monitor == nil {
			return fmt.Errorf("monitor not found, adId:%d", adId)
		}

		if monitor.IsTargetEventType(request.AssertGetTrackingBasicData().EventType) {
			request.GetTrackingPriceData().IsCharge = 1
			request.GetTrackingPriceData().IsCost = 1
		}

		if !monitor.IsCallbackEventType(request.AssertGetTrackingBasicData().EventType) {
			return nil
			//return fmt.Errorf("callback event type not match, adId:%d, eventType:%s", adId, request.AssertGetTrackingBasicData().EventType)
		}

	} else {
		if h.isDspTargetEventType(request, request.AssertGetTrackingBasicData().EventType) {
			request.GetTrackingPriceData().IsCharge = 1
			request.GetTrackingPriceData().IsCost = 1
		} else {
			return nil
			//return fmt.Errorf("dsp event type not match, adId:%d, eventType:%s", adId, request.AssertGetTrackingBasicData().EventType)
		}
	}

	//if h.discountAttribution(request, int32(ad.GetAttributionCallbackRate())) {
	if rand.Int31n(10000) < int32(ad.GetAttributionCallbackRate()) {
		request.GetTrackingPriceData().IsCost = 0
		zap.L().Info("discount attribution adId:, requestId:%s, callback rate", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(adId)))), zap.Int64("id", int64(requestId)), zap.Int64("param3", int64(ad.GetAttributionCallbackRate())))
		return nil
	}

	start := mclock.Now()

	err = h.mediaCallBackHandler.DoMediaCallBack(request)

	//if len(s2sPostback) > 0 {
	//	zap.L().Info("handle attribution, param:%+v, url", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", //		request.GetParams())))), s2sPostback)
	//}

	//if net_utils.IsValidUrl(s2sPostback) {
	//	//eventType := h.toOldEventType(request.AssertGetTrackingBasicData().EventType)
	//	eventType := ad.GetMappedEvent(request.AssertGetTrackingBasicData().EventType)
	//
	//	err = h.handleS2sPostback(request, eventType, s2sPostback, adId, requestId)
	//} else {
	//	//废弃了
	//	//err = h.handleCallback(request, adId, requestId)
	//}
	elapse := mclock.Now() - start

	if err != nil {
		h.histogram.Observe(prometheus_helper.LabelFail, float64(elapse)/1e9)
	} else {
		h.histogram.Observe(prometheus_helper.LabelSuccess, float64(elapse)/1e9)
	}

	return err
}

func (h *AttributionTrackingRequestHandler) handleS2sPostback(request *tracking_service.TrackingRequest, eventTypeNew string, url string, adId int32, requestId string) error {
	eventType := h.toOldEventType(eventTypeNew)

	url = net_utils.ReplaceMacroSimple(url, "__EVENT__", eventType)

	url = net_utils.AppendParam(url, "event_type", eventType)

	if err := h.sender.QueryUrl([]string{url}); err != nil {
		zap.L().Error("query attribution callback error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	} else {
		zap.L().Info("[handleS2sPostback] query attribution callback success, url:%s, type:%s, requesit_id:%s, is_charge:, ad", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(url)))), zap.Int64("param2", int64(request.GetEventType())), requestId, request.GetTrackingPriceData().IsCharge, adId)
	}
	return nil
}

//func (h *AttributionTrackingRequestHandler) handleCallback(request *tracking_service.TrackingRequest, adId int32, requestId string) error {
//	key := fmt.Sprintf("%d_%s", adId, requestId)
//	candidate, err := h.sender.storage.LoadAttributionCandidate(key)
//	if err != nil {
//		zap.L().Error("query attribution candidate error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
//		return fmt.Errorf("query attribution candidate error:%s", err.Error())
//	}
//
//	if candidate == nil {
//		zap.L().Error("query attribution candidate nil, key", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", key)))))
//		return fmt.Errorf("query attribution candidate nil, key:%s", key)
//	}
//
//	eventType := h.toOldEventType(request.AssertGetTrackingBasicData().EventType)
//
//	candidate.Callback = net_utils.ReplaceMacroSimple(candidate.Callback, "__EVENT__", eventType)
//
//	candidate.Callback = net_utils.AppendParam(candidate.Callback, "event_type", eventType)
//
//	if len(candidate.Callback) > 0 {
//		if err := h.sender.QueryUrl([]string{candidate.Callback}); err != nil {
//			zap.L().Error("query attribution callback error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
//			return err
//		} else {
//			zap.L().Info("[handleCallback] query attribution callback success, url:%s, type:%s, request_id:%s, is_charge:, ad", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(candidate.Callback)))), zap.Int64("param2", int64(request.GetEventType())), requestId, request.GetTrackingPriceData().IsCharge, adId)
//		}
//	}
//
//	return nil
//}

func (h *AttributionTrackingRequestHandler) toOldEventType(newType string) string {
	switch newType {
	case entity.CpaEventTypeActivate:
		return entity.OldCpaEventActived
	case entity.CpaEventTypeRegister:
		return entity.OldCpaEventRegister
	case entity.CpaEventTypeCommitMsg:
		return entity.OldCpaEventCommitMsg
	case entity.CpaEventTypePay:
		return entity.OldCpaEventPay
	case entity.CpaEventTypeRetained:
		return entity.OldCpaEventRetained
	case entity.CpaEventTypeOpenApp:
		return entity.OldCpaEventOpenApp
	case entity.CpaEventTypeDownload:
		return entity.OldCpaEventDownload
	case entity.CpaEventTypeAddCart:
		return entity.OldCpaEventAddCart
	case entity.CpaEventTypeNewOrder:
		return entity.OldCpaEventNewOrder
	case entity.CpaEventTypeOldOrder:
		return entity.OldCpaEventOldOrder
	case entity.CpaEventTypeDpSuccess:
		return entity.OldCpaEventOldDpSuccess
	case entity.CpaEventTypeUnknown:
		return entity.OldCpaEventTypeUnKnown
	default:
		return entity.OldCpaEventTypeUnKnown
	}
}
