package internal

import (
	"fmt"
	"github.com/go-redis/redis"
	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/frequency_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/pacing_searcher"
	"gitlab.com/dev/heidegger/ad_server/ad_service/yielder"
	"gitlab.com/dev/heidegger/ad_server/pacing_service/pacing_controller"
	"gitlab.com/dev/heidegger/frequency/frequency_client"
	"gitlab.com/dev/heidegger/library/entity_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/ad_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/budget_platform_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/dsp_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/dsp_slot_info_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/frequency_control_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/media_loader"
	"gitlab.com/dev/heidegger/library/kafka_producer"
	"gitlab.com/dev/heidegger/library/price/price_manager"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/test_helper"
	"gitlab.com/dev/heidegger/master_server/master_server"
	"gitlab.com/dev/heidegger/tracking/attribution_handler"
	"gitlab.com/dev/heidegger/tracking/attribution_sender"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
	"os"
	"strings"
	"time"
	"xorm.io/xorm"
)

type TrackingServer struct {
	trackingClickUrl string
	trackingImpUrl   string

	env               Env
	dbEngine          *xorm.Engine
	pacingRedisClient *redis.Client
	echoServer        *echo.Echo
	kafkaProducer     *kafka_producer.KafkaProducer

	adLoader               ad_loader.AdLoader
	adGroupLoader          ad_loader.AdGroupLoader
	adIndexLoader          ad_loader.AdIndexLoader
	dspLoader              dsp_loader.DspLoader
	dspSlotInfoLoader      dsp_slot_info_loader.DspSlotInfoLoader
	frequencyControlLoader frequency_control_loader.FrequencyControlLoader
	mediaLoader            media_loader.MediaLoader
	mediaSlotLoader        media_loader.MediaSlotLoader
	budgetPlatformLoader   budget_platform_loader.BudgetPlatformLoader

	adPacingController      pacing_controller.PacingClient
	adGroupPacingController pacing_controller.PacingClient

	frequencyManager frequency_searcher.FrequencyManager

	attributionStorage attribution_sender.AttributionStorage
	attributionSender  *attribution_sender.AttributionSender

	kafkaDataCollector *yielder.KafkaDataCollector

	serviceRegister master_server.ServiceRegister
	priceManager    *price_manager.PriceManager
}

func NewTrackingServer() *TrackingServer {
	return &TrackingServer{
		env: GetEnv(),
	}
}

func (s *TrackingServer) Start() error {
	if err := s.loadEnv(); err != nil {
		return err
	}

	if err := s.startBasic(); err != nil {
		return err
	}

	if err := s.startDataLoader(); err != nil {
		return err
	}

	if err := s.startServiceRegister(); err != nil {
		return err
	}

	if err := s.startAttributionSender(); err != nil {
		return err
	}

	if err := s.startFrequencyManager(); err != nil {
		return err
	}

	if err := s.startPacingController(); err != nil {
		return err
	}

	if err := s.startImpressionTracking(); err != nil {
		return err
	}

	if err := s.startClickTracking(); err != nil {
		return err
	}

	if err := s.startActionTracking(); err != nil {
		return err
	}

	if err := s.startCustomTracking(); err != nil {
		return err
	}

	if err := s.startHttp(); err != nil {
		return err
	}

	return nil
}

func (s *TrackingServer) loadEnv() error {
	if s.env.IsDebug {
		return nil
	}

	zap.L().Info("[TrackingServer] load env")

	client := master_server.NewKeyValueHttpClient("http://local.master_server.com:28000")
	if err := client.GetInto("config", &s.env); err != nil {
		return err
	}

	s.env.DumpJson()
	return nil
}

func (s *TrackingServer) startServiceRegister() error {
	hostname, err := os.Hostname()
	if err != nil {
		return err
	}

	client := master_server.NewKeyValueHttpClient("http://local.master_server.com:28000")
	serviceRegister := master_server.NewMasterServiceRegister(client)
	if err := serviceRegister.RegisterService("tracking_server", hostname); err != nil {
		return err
	}

	if err := serviceRegister.Watch("tracking_server"); err != nil {
		return err
	}

	if err := serviceRegister.Start(); err != nil {
		return err
	}

	s.serviceRegister = serviceRegister
	return nil
}

func (s *TrackingServer) startBasic() error {
	// logrus.SetLevel converted - configure zap logger instead

	s.echoServer = echo.New()

	s.echoServer.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			if strings.Contains(c.Request().RequestURI, ";") {
				zap.L().Error("Url format error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", c.Request())))).RequestURI)
			}
			return next(c)
		}
	})

	zap.L().Info("[TrackingServer] database name", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", s.env.DatabaseName)))))
	var engine *xorm.Engine
	if len(s.env.DatabaseConn) != 0 {
		zap.L().Info("[TrackingServer] database conn", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", s.env.DatabaseConn)))))
		engine = test_helper.GetDatabaseWithEncryptedConn(s.env.DatabaseConn)
	} else if s.env.DatabaseName == "local" {
		engine = test_helper.GetTestXormEngine()
	} else {
		panic("invalid database name")
	}

	s.dbEngine = engine

	pacingRedisClient := redis.NewClient(&redis.Options{
		Addr: s.env.PacingRedis.Address,
		DB:   s.env.PacingRedis.Database,
	})
	s.pacingRedisClient = pacingRedisClient

	if len(s.env.KafkaBrokerList) != 0 {
		kafkaProducer := kafka_producer.NewKafkaProducer(s.env.KafkaBrokerList, s.env.KafkaVersion)
		if err := kafkaProducer.Start(); err != nil {
			return err
		}

		s.kafkaProducer = kafkaProducer
	}

	s.priceManager = price_manager.NewPriceManager()

	prometheus_helper.GetGlobalPrometheusManager().RegisterEcho(s.echoServer)

	return nil
}

func (s *TrackingServer) startDataLoader() error {
	reloadInterval := 60

	s.frequencyControlLoader = frequency_control_loader.NewMysqlFrequencyControlLoader(s.dbEngine, reloadInterval)
	if err := s.frequencyControlLoader.Start(); err != nil {
		return errors.Wrapf(err, "start frequency control loader failed")
	}

	s.adGroupLoader = ad_loader.NewMysqlAdGroupLoader(s.dbEngine, reloadInterval)
	if err := s.adGroupLoader.Start(); err != nil {
		return errors.Wrapf(err, "start ad group loader failed")
	}

	s.budgetPlatformLoader = budget_platform_loader.NewMysqlBudgetPlatformLoader(s.dbEngine, reloadInterval)
	if err := s.budgetPlatformLoader.Start(); err != nil {
		return errors.Wrapf(err, "start budget platform loader failed")
	}

	s.mediaLoader = media_loader.NewMysqlMediaLoader(s.dbEngine, reloadInterval)
	if err := s.mediaLoader.Start(); err != nil {
		return errors.Wrapf(err, "start media loader failed")
	}

	s.mediaSlotLoader = media_loader.NewMysqlMediaSlotLoader(s.dbEngine, reloadInterval)
	if err := s.mediaSlotLoader.Start(); err != nil {
		return errors.Wrapf(err, "start media slot loader failed")
	}

	s.adLoader = ad_loader.NewMysqlAdLoader(s.dbEngine, reloadInterval).
		WithAdGroupLoader(s.adGroupLoader).
		WithBudgetPlatformLoader(s.budgetPlatformLoader).
		WithFrequencyControlLoader(s.frequencyControlLoader).
		WithImpressionMonitor(s.env.ImpressionMonitor).
		WithClickMonitor(s.env.ClickMonitor).
		WithActionCallbackUrl(s.env.ActionCallbackUrl)

	if err := s.adLoader.Start(); err != nil {
		return errors.Wrapf(err, "start ad loader failed")
	}

	s.dspLoader = dsp_loader.NewMysqlDspLoader(s.dbEngine, reloadInterval)
	if err := s.dspLoader.Start(); err != nil {
		return errors.Wrapf(err, "start dsp loader failed")
	}

	s.dspSlotInfoLoader = dsp_slot_info_loader.NewMysqlDspSlotInfoLoader(s.dbEngine, reloadInterval)
	if err := s.dspSlotInfoLoader.Start(); err != nil {
		return errors.Wrapf(err, "start dsp slot info loader failed")
	}

	entityLoaderHttpHandler := entity_loader.NewEntityLoaderHttpHandler()
	entityLoaderHttpHandler.SetAdLoader(s.adLoader)
	entityLoaderHttpHandler.SetAdGroupLoader(s.adGroupLoader)
	entityLoaderHttpHandler.SetAdIndexLoader(s.adIndexLoader)
	entityLoaderHttpHandler.SetDspLoader(s.dspLoader)
	entityLoaderHttpHandler.SetFrequencyControlLoader(s.frequencyControlLoader)
	entityLoaderHttpHandler.SetDspSlotInfoLoader(s.dspSlotInfoLoader)
	entityLoaderHttpHandler.SetMediaLoader(s.mediaLoader)
	entityLoaderHttpHandler.SetMediaSlotLoader(s.mediaSlotLoader)

	entityLoaderHttpHandler.RegisterEcho(s.echoServer)

	return nil
}

func (s *TrackingServer) startPacingController() error {
	adPacingController := pacing_controller.NewPacingControllerClient("ad", 60)
	adPacingController.SetRedisClient(s.pacingRedisClient)
	adPacingController.SetPacingClient(pacing_controller.NewPacingControllerHttpClient(s.env.PacingServiceAddress, "ad"))
	if err := adPacingController.Start(false); err != nil {
		return err
	}

	adGroupPacingController := pacing_controller.NewPacingControllerClient("adgroup", 60)
	adGroupPacingController.SetRedisClient(s.pacingRedisClient)
	adGroupPacingController.SetPacingClient(pacing_controller.NewPacingControllerHttpClient(s.env.PacingServiceAddress, "adgroup"))
	if err := adGroupPacingController.Start(false); err != nil {
		return err
	}

	s.adPacingController = adPacingController
	s.adGroupPacingController = adGroupPacingController

	return nil
}

func (s *TrackingServer) startImpressionTracking() error {
	dataType := "imp"
	workflow := tracking_service.NewTrackingWorkflow(dataType)
	workflow.AddTask(tracking_service.NewPriceDataExtractor(s.adLoader, s.mediaLoader, s.mediaSlotLoader, s.priceManager))
	workflow.AddTask(tracking_service.NewRotateFileLogger(
		dataType,
		s.env.LoggingPath,
		"imp/20060102/150405.txt",
		s.env.LoggingRotateInterval,
	))
	workflow.AddTask(pacing_searcher.NewPacingImpressionReporter(s.adPacingController, s.adGroupPacingController))
	workflow.AddTask(frequency_searcher.NewFrequencyControlReporter(s.frequencyManager))
	const impTopic = "ad_tracking_imp_log"
	workflow.AddTask(tracking_service.NewTrackingKafkaLogger(impTopic, s.kafkaProducer))

	workflow.AddTask(tracking_service.NewStatisticHandler(dataType))

	if err := workflow.Start(); err != nil {
		return err
	}

	trackingHandler := tracking_service.NewTrackingRequestHandler("/tracking/i", workflow)
	trackingHandler.RegisterEcho(s.echoServer)

	return nil
}

func (s *TrackingServer) startClickTracking() error {
	dataType := "click"
	workflow := tracking_service.NewTrackingWorkflow(dataType)
	workflow.AddTask(tracking_service.NewRotateFileLogger(
		dataType,
		s.env.LoggingPath,
		"click/20060102/150405.txt",
		s.env.LoggingRotateInterval,
	))
	const clickTopic = "ad_tracking_click_log"
	workflow.AddTask(tracking_service.NewTrackingKafkaLogger(clickTopic, s.kafkaProducer))

	workflow.AddTask(tracking_service.NewStatisticHandler(dataType))

	if err := workflow.Start(); err != nil {
		return err
	}

	trackingHandler := tracking_service.NewTrackingRequestHandler("/tracking/c", workflow)
	trackingHandler.RegisterEcho(s.echoServer)

	return nil
}

func (s *TrackingServer) startActionTracking() error {
	dataType := "action"
	workflow := tracking_service.NewTrackingWorkflow(dataType)
	workflow.AddTask(tracking_service.NewCpaDataExtractor(s.budgetPlatformLoader, s.dspLoader))
	workflow.AddTask(attribution_handler.NewAttributionTrackingRequestHandler(s.attributionSender, s.mediaLoader, s.adLoader, nil))
	workflow.AddTask(tracking_service.NewRotateFileLogger(
		dataType,
		s.env.LoggingPath,
		"action/20060102/150405.txt",
		s.env.LoggingRotateInterval,
	))

	const actionTopic = "ad_tracking_action_log"
	workflow.AddTask(tracking_service.NewTrackingKafkaLogger(actionTopic, s.kafkaProducer))
	if err := workflow.Start(); err != nil {
		return err
	}

	workflow.AddTask(tracking_service.NewStatisticHandler(dataType))

	trackingHandler := tracking_service.NewTrackingRequestHandler("/tracking/ocpx", workflow)
	trackingHandler.RegisterEcho(s.echoServer)

	return nil
}

func (s *TrackingServer) startCustomTracking() error {
	dataType := "custom"
	workflow := tracking_service.NewTrackingWorkflow(dataType)
	workflow.AddTask(tracking_service.NewRotateFileLogger(
		dataType,
		s.env.LoggingPath,
		"custom/20060102/150405.txt",
		s.env.LoggingRotateInterval,
	))

	const customTopic = "ad_custom_data_log"
	workflow.AddTask(tracking_service.NewTrackingKafkaLogger(customTopic, s.kafkaProducer))
	if err := workflow.Start(); err != nil {
		return err
	}

	trackingHandler := tracking_service.NewTrackingRequestHandler("/tracking/cus", workflow)
	trackingHandler.RegisterEcho(s.echoServer)

	return nil
}

func (s *TrackingServer) startAttributionSender() error {
	attributionRedis := redis.NewClient(&redis.Options{
		Addr: s.env.AttributionRedis.Address,
		DB:   s.env.AttributionRedis.Database,
	})

	storage := attribution_sender.NewRedisAttributionStorage(attributionRedis, 12*time.Hour)
	attributionSender := attribution_sender.NewAttributionSender(storage)

	if err := attributionSender.Start(); err != nil {
		return err
	}

	s.attributionStorage = storage
	s.attributionSender = attributionSender

	return nil
}

func (s *TrackingServer) startFrequencyManager() error {
	client := frequency_client.NewFrequencyClient(
		s.env.FrequencyClient.Address,
		s.env.FrequencyClient.Timeout)

	if err := client.Start(); err != nil {
		return fmt.Errorf("start frequency client failed. err: %v", err)
	}

	s.frequencyManager = client
	return nil
}

func (s *TrackingServer) startHttp() error {
	return s.echoServer.Start(s.env.HttpAddress)
}
