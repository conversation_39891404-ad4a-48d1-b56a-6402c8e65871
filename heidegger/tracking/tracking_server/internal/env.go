package internal

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"os"
)

type RapidOption struct {
	Address []string
	Timeout int
}

type RedisConfig struct {
	Address  string
	Database int
}

type Env struct {
	IsDebug bool `json:"-"`

	FrequencyClient  RapidOption `json:"engine.frequency_client"`
	PacingRedis      RedisConfig `json:"engine.pacing_redis"`
	AttributionRedis RedisConfig `json:"engine.attribution_redis"`
	DatabaseName     string      `json:"engine.database_name"`
	DatabaseConn     string      `json:"engine.database_conn"`

	KafkaBrokerList       []string     `json:"ad_server.kafka_broker_list"`
	KafkaVersion          string       `json:"ad_server.kafka_version"`
	HttpAddress           string       `json:"ad_server.tracking_http_address"`
	ImpressionMonitor     []string     `json:"ad_server.impression_monitor"`
	ClickMonitor          []string     `json:"ad_server.click_monitor"`
	ActionCallbackUrl     string       `json:"ad_server.action_callback_url"`
	LoggingPath           string       `json:"ad_server.logging_path"`
	LoggingRotateInterval int          `json:"ad_server.logging_rotate_interval"`
	PacingServiceAddress  string       `json:"ad_server.pacing_service_address"`
	LogLevel              zapcore.Level `json:"ad_server.log_level"`
}

func (env *Env) DumpJson() {
	result, _ := json.MarshalIndent(env, "", "  ")
	fmt.Println(string(result))
}

func GetEnv() Env {
	host, _ := os.Hostname()

	zap.L().Info("[GetEnv] Hostname", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", host)))))

	return GetLocalEnv()
}

func GetLocalEnv() Env {
	return Env{
		LogLevel:     zapcore.DebugLevel,
		DatabaseName: "local",
		PacingRedis: RedisConfig{
			Address:  "localhost:6379",
			Database: 8,
		},
		AttributionRedis: RedisConfig{
			Address:  "localhost:6379",
			Database: 9,
		},
		FrequencyClient: RapidOption{
			Address: []string{"**************:58001"},
			Timeout: 100,
		},
		HttpAddress: "0.0.0.0:8081",
		ImpressionMonitor: []string{
			"http://127.0.0.1:8081/tracking/i?p=__SYSWPRICE__&d_=__SYSEXT__",
		},
		ClickMonitor: []string{
			"http://127.0.0.1:8081/tracking/c?d_=__SYSEXT__",
		},
		ActionCallbackUrl:     "http://127.0.0.1:8080/tracking/ocpx?d_=__SYSEXT__",
		LoggingPath:           "./tracking_server/app/tracking_server/logs",
		LoggingRotateInterval: 600,
		PacingServiceAddress:  "http://**************:58810",
	}
}
