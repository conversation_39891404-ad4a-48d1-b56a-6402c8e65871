package tracking_server

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/tracking/tracking_server/internal"
)

func main() {
	// logrus.SetLevel converted - configure zap logger instead

	//go func() {
	//	if err := http.ListenAndServe("0.0.0.0:38000", nil); err != nil {
	//		panic(err)
	//	}
	//}()

	adServer := internal.NewTrackingServer()

	if err := adServer.Start(); err != nil {
		panic(err)
	}
}
