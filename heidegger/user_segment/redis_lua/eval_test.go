package redis_lua

import (
	"encoding/hex"
	"fmt"
	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/user_segment/user_segment_entity"
	"io"
	"os"
	"testing"
	"time"
)

func TestEvalLua(t *testing.T) {
	client := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})

	luaFile, err := os.Open("./eval_test.lua")
	if err != nil {
		t.Fatalf("failed to open lua file: %v", err)
	}

	content, err := io.ReadAll(luaFile)
	if err != nil {
		t.Fatalf("failed to read lua file: %v", err)
	}

	//evalResult, err := client.Eval(string(content), []string{"testKey"}, []string{"123", "10"}).String()
	//if err != nil {
	//	t.Fatalf("failed to eval lua: %v", err)
	//}

	deviceId := fmt.Sprintf("%d", time.Now().Unix())

	scriptSha1, err := client.ScriptLoad(string(content)).Result()
	if err != nil {
		t.Fatalf("failed to load lua script: %v", err)
	}

	t.Logf("script sha1: %v", scriptSha1)

	evalResult, err := client.EvalSha(scriptSha1,
		[]string{deviceId},
		[]string{
			"123",
			fmt.Sprintf("%d", 0),
			"3600"}).String()
	if err != nil {
		t.Fatalf("failed to eval lua: %v", err)
	}

	evalResult, err = client.EvalSha(scriptSha1,
		[]string{deviceId},
		[]string{
			"123",
			fmt.Sprintf("%d", 0x0101F010),
			"3600",
			"8816"}).String()
	if err != nil {
		t.Fatalf("failed to eval lua: %v", err)
	}

	evalResult, err = client.EvalSha(scriptSha1,
		[]string{deviceId},
		[]string{
			"123",
			fmt.Sprintf("%d", 0x01010000),
			"3600",
			"8816"}).String()
	if err != nil {
		t.Fatalf("failed to eval lua: %v", err)
	}

	t.Logf("eval result: %v", evalResult)

	evalResult, err = client.EvalSha(scriptSha1,
		[]string{deviceId},
		[]string{
			"456",
			fmt.Sprintf("%d", 0x01010000),
			"3600",
			"8816"}).String()
	if err != nil {
		t.Fatalf("failed to eval lua: %v", err)
	}

	t.Logf("eval result: %v", evalResult)

	data, err := client.Get(deviceId).Bytes()
	if err != nil {
		t.Fatalf("failed to get key: %v", err)
	}

	redisValue, err := user_segment_entity.UserSegmentRedisValueFromBytes(data)
	if err != nil {
		t.Fatalf("failed to parse redis value: %v", err)
	}

	t.Logf("get key: %v, len: %d", hex.EncodeToString(data), len(data))
	t.Logf("redis value: %v", redisValue.DumpJson())
	for _, item := range redisValue.Tags {
		zap.L().Info("tag: , value:, valueHex:%X", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(item.TagId)))), zap.Int64("param2", int64(item.TagValue)), zap.Int64("param3", int64(item.TagValue)))
	}
}

func BenchmarkEvalLua(b *testing.B) {
	client := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})

	luaFile, err := os.Open("./eval_test.lua")
	if err != nil {
		b.Fatalf("failed to open lua file: %v", err)
	}

	content, err := io.ReadAll(luaFile)
	if err != nil {
		b.Fatalf("failed to read lua file: %v", err)
	}

	scriptSha1, err := client.ScriptLoad(string(content)).Result()
	if err != nil {
		b.Fatalf("failed to load lua script: %v", err)
	}

	b.Logf("script sha1: %v", scriptSha1)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := client.EvalSha(scriptSha1,
			[]string{"device_id"},
			[]string{
				"123",
				fmt.Sprintf("%d", 0x02010010),
				"3600",
				"8816"}).String()
		if err != nil {
			b.Fatalf("failed to eval lua: %v", err)
		}
	}
}
