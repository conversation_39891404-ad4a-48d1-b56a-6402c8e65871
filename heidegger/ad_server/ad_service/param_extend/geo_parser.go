package param_extend

import (
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/library/geo_parser"
	"fmt"
)

type GeoParser struct {
	geoQuery   *geo_parser.GeoParser
	geoQueryV6 *geo_parser.GeoParserV6
	geoFile    string
	geoFileV6  string
}

func NewGeoParser(geoFile string, geoFileV6 string) *GeoParser {
	return &GeoParser{
		geoQuery:   geo_parser.NewGeoParser(),
		geoQueryV6: geo_parser.NewGeoParserV6(),
		geoFile:    geoFile,
		geoFileV6:  geoFileV6,
	}
}

func (s *GeoParser) GetTaskName() string {
	return "GeoParser"
}

func (s *GeoParser) Start() error {
	err := s.geoQuery.StartLoadGeoData(s.geoFile)
	if err != nil {
		return err
	}

	err = s.geoQueryV6.StartLoadGeoData(s.geoFileV6)
	if err != nil {
		return err
	}

	return nil
}

func (s *GeoParser) Stop() {

}

func (s *GeoParser) Do(request *ad_service.AdRequest) error {
	requestIp := request.Device.GetRequestIp()
	if request.Device.RequestIp == "client" {
		requestIp = request.Device.GetTcpConnectionIp()
	}

	if !request.Device.IsIp6 && geo_parser.IsIPv6(requestIp) {
		request.Device.IsIp6 = true
	}

	var geoInfo geo_parser.GeoInfo
	var err error
	if request.Device.IsIp6 {
		geoInfo, err = s.geoQueryV6.QueryGeoInfo(requestIp)
		if err != nil {
			//zap.L().Error("parse geo error:, requsetIp:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))), requestIp)
			request.GeoCode = 0
			return nil
		}
	} else {
		geoInfo, err = s.geoQuery.QueryGeoInfo(requestIp)
		if err != nil {
			//zap.L().Error("parse geo error:, requsetIp:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))), requestIp)
			request.GeoCode = 0
			return nil
		}
	}

	request.GeoCode = geoInfo.GeoId

	return nil
}
