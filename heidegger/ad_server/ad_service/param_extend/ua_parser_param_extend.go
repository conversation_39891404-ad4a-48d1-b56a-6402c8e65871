package param_extend

import (
	"math/rand"
	"strings"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/ua_parser_manager"
)

type UaParserParamExtend struct {
	uaParserManager *ua_parser_manager.UaParserManager
}

func NewUaParserParamExtend(uaParserManager *ua_parser_manager.UaParserManager) *UaParserParamExtend {
	return &UaParserParamExtend{uaParserManager: uaParserManager}
}

func (s *UaParserParamExtend) GetTaskName() string {
	return "UaParserParamExtend"
}

func (s *UaParserParamExtend) Start() error {
	return nil
}

func (s *UaParserParamExtend) Stop() {

}

func (s *UaParserParamExtend) Do(request *ad_service.AdRequest) error {
	if len(request.Device.UserAgent) == 0 || s.uaParserManager == nil {
		return nil
	}

	// drop all request with keyword "spider" in UA
	// 宝宝树爬虫UA："kube-probe/1.30"
	if strings.Contains(request.Device.UserAgent, "Spider") ||
		strings.Contains(request.Device.UserAgent, "spider") ||
		strings.Contains(request.Device.UserAgent, "kube-probe/") {
		return err_code.ErrUAFilter
	}

	uaParseResult, err := s.uaParserManager.Parse(request.Device.UserAgent)
	if err != nil {
		return nil
	}

	trafficParseResult, err := s.uaParserManager.ParseTrafficUaInfo(ua_parser_manager.TrafficUaInfo{
		DeviceType: request.Device.GetDeviceType(),
		Os:         request.Device.GetOsType(),
		Brand:      request.Device.Brand,
		Model:      request.Device.Model,
		OsVersion:  request.Device.OsVersion,
	})
	if err != nil {
		return nil
	}

	request.Device.UserAgentParser = uaParseResult
	//result := uaParseResult
	if trafficParseResult.OsVersionId != 0 && trafficParseResult.BrandModelId != 0 { // use traffic data, if parsed
		request.Device.UserAgentParser = trafficParseResult
	}

	if request.Device.UserAgentParser.UaId == 0 {
		if rand.Int31n(10000)%5000 == 0 {
			zap.L().Info("UaParserParamExtend id: , media_id:, fail: %s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(request.Device.UserAgentParser.UaId)))), zap.Int64("id", int64(request.GetMediaId())), request.Device.UserAgent)
		}
	}

	if request.Device.UserAgentParser.UaId != 0 {
		if len(request.Device.Brand) == 0 && len(request.Device.UserAgentParser.Brand) != 0 {
			request.Device.Brand = request.Device.UserAgentParser.Brand
		}

		if len(request.Device.Model) == 0 && len(request.Device.UserAgentParser.Model) != 0 {
			request.Device.Model = request.Device.UserAgentParser.Model
		}

		if request.Device.OsType == entity.OsTypeUnknown && request.Device.UserAgentParser.OsType != entity.OsTypeUnknown {
			request.Device.OsType = request.Device.UserAgentParser.OsType
		}

		if request.Device.DeviceType == entity.DeviceTypeUnknown && request.Device.UserAgentParser.DeviceType != entity.DeviceTypeUnknown {
			request.Device.DeviceType = request.Device.UserAgentParser.DeviceType
		}

		if len(request.Device.OsVersion) == 0 && len(request.Device.UserAgentParser.OsVersionRaw) != 0 {
			request.Device.OsVersion = request.Device.UserAgentParser.OsVersionRaw
		}
	}

	// try get missing info from user-agent
	if uaParseResult.UaId != 0 && request.Device.OsType == uaParseResult.OsType {
		if len(request.Device.Brand) == 0 && len(uaParseResult.Brand) != 0 {
			request.Device.Brand = uaParseResult.Brand
		}

		if len(request.Device.Model) == 0 && len(uaParseResult.Model) != 0 {
			request.Device.Model = uaParseResult.Model
		}

		if len(request.Device.OsVersion) == 0 && len(uaParseResult.OsVersionRaw) != 0 {
			request.Device.OsVersion = uaParseResult.OsVersionRaw
		}
	}

	return nil
}
