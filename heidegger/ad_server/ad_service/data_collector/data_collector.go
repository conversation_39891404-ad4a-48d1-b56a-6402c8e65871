package data_collector

import (
	"github.com/bytedance/sonic"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/data_collector/data_collector_proto"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"time"
	"fmt"
)

type DataCollector struct {
}

func NewDataCollector() *DataCollector {
	return &DataCollector{}
}

func (c *DataCollector) GetSampleRate(request *ad_service.AdRequest) int32 {
	sampleRate := int32(1)
	if len(request.Response.GetTotalAdCandidateList()) == 0 {
		sampleRate = 100
	} else if len(request.Response.GetAdCandidateList()) == 0 {
		hasDspBid := false
		for _, ad := range request.Response.GetTotalAdCandidateList() {
			if ad.GetDspAdId() != "" {
				hasDspBid = true
				break
			}
		}
		if !hasDspBid {
			sampleRate = 100
		}
	}
	return sampleRate
}

func (c *DataCollector) SendRequestData(request *ad_service.AdRequest, sampleRate int32, sender func(data []byte) error) error {
	data := c.requestToRequestDataProto(request, sampleRate)
	defer data.Release()

	dataBytes, err := data.Marshal()
	if err != nil {
		return err
	}

	if err := sender(dataBytes); err != nil {
		return err
	}

	return nil
}

func (c *DataCollector) SendResponseBundle(request *ad_service.AdRequest, candidates []*ad_service.AdCandidate, sampleRate int32, sender func(data []byte) error) error {
	data := c.candidatesToResponseBundleProto(request, candidates, sampleRate)
	defer data.Release()

	dataBytes, err := data.Marshal()
	if err != nil {
		return err
	}

	//zap.L().Info("SendResponseBundle", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data.JsonString())))))

	if err := sender(dataBytes); err != nil {
		return err
	}

	return nil
}

func (c *DataCollector) SendSampleBundle(request *ad_service.AdRequest, candidates []*ad_service.AdCandidate, sender func(data []byte) error) error {
	sampled := false
	for _, candidate := range candidates {
		if !candidate.GetIsSampled() {
			continue
		}

		data := c.candidateToRawSampleLogProto(request, candidate)
		defer data.Release()

		//zap.L().Info("SendSampleBundle", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data.JsonString())))))

		dataBytes, err := data.Marshal()
		if err != nil {
			return err
		}

		if err := sender(dataBytes); err != nil {
			return err
		}

		sampled = true
	}

	if sampled == false && request.GetIsSampled() {
		data := c.candidateToRawSampleLogProto(request, nil)
		defer data.Release()

		dataBytes, err := data.Marshal()
		if err != nil {
			return err
		}

		//zap.L().Info("SendSampleBundle", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data.JsonString())))))

		if err := sender(dataBytes); err != nil {
			return err
		}
	}

	return nil
}

func (c *DataCollector) requestToRequestDataProto(request *ad_service.AdRequest, sampleRate int32) *data_collector_proto.RequestData {
	data := data_collector_proto.NewRequestData()
	data.SampleRate = sampleRate

	c.requestToBasicRequestDataProto(request, &data.BasicRequest)
	c.trafficDataToDeviceDataProto(&request.Device, &data.Device)
	c.trafficDataToAppDataProto(&request.App, &data.App)

	if len(request.Response.GetAdCandidateList()) > 0 {
		candidate := request.Response.GetAdCandidateList()[0]
		c.candidateToBasicResponseDataProto(candidate, &data.BasicResponse)
	}

	for _, candidate := range request.Response.GetTotalAdCandidates() {
		genericCreative := candidate.GetCreative()

		candidateDetail := data_collector_proto.CandidateDetail{
			AdId:         int32(candidate.GetAd().AdId),
			AdgroupId:    int32(candidate.GetAd().GetAdGroupId()),
			DspId:        int32(candidate.GetAd().GetDspId()),
			ErrorCode:    candidate.GetErrorCode().Code,
			DspRequested: candidate.GetIsDspRequested(),
			AdType:       int32(candidate.GetAd().GetAdType()),
			DspSlotId:    int32(candidate.GetAd().GetDspSlotId()),
			DspSlotKey:   candidate.GetDspSlotKey(),
			DspAppBundle: candidate.GetModifiedTrafficData().GetAppBundle(),
		}

		if len(candidateDetail.DspSlotKey) == 0 {
			candidateDetail.DspSlotKey = candidate.GetDspSlotKeyByOs(request.Device.GetOsType())
		}

		if candidate.GetIndexDeal() != nil {
			candidateDetail.DealId = candidate.GetIndexDeal().DealId
		}

		if genericCreative != nil {
			candidateDetail.CreativeId = int32(genericCreative.GetCreativeId())
			candidateDetail.CreativeKey = genericCreative.GetCreativeKey()
		}

		data.CandidateDetail = append(data.CandidateDetail, candidateDetail)
	}

	return data
}

func (c *DataCollector) requestToBasicRequestDataProto(request *ad_service.AdRequest, data *data_collector_proto.BasicRequestData) {
	data.RequestId = request.GetRequestId()
	data.Timestamp = uint64(request.RequestTime.UnixNano())
	data.MediaId = int32(request.GetMediaId())
	data.MediaSlotId = request.GetMediaSlotId().String()
	data.SourceSlotId = request.GetSourceSlotId()
	data.MediaSlotKey = request.GetMediaSlotKey()
	data.MediaSlotType = int32(request.SlotType)
	data.MediaSlotWidth = int32(request.SlotWidth)
	data.MediaSlotHeight = int32(request.SlotHeight)
	data.Ip = request.Device.RequestIp
	data.GeoCode = request.GeoCode
	data.UaId = request.Device.UserAgentParser.UaId
	data.RequestErrorCode = request.Response.GetAdErrorCode().Code
	for _, key := range request.GetCreativeTemplateKeyList() {
		data.CreativeTemplateKey = append(data.CreativeTemplateKey, uint64(key))
	}

	for _, deal := range request.SourceDeal {
		data.ReqDealId = append(data.ReqDealId, deal.DealId)
	}
}

func (c *DataCollector) trafficDataToDeviceDataProto(trafficData device_utils.DeviceDataProvider, data *data_collector_proto.DeviceData) {
	deviceId, deviceIdType := device_utils.GetDeviceIdWithType(trafficData)
	data.DeviceId = deviceId
	data.DeviceIdType = int32(deviceIdType)
	data.DeviceType = int32(trafficData.GetDeviceType())
	data.OsType = int32(trafficData.GetOsType())
	data.OsVersion = trafficData.GetOsVersion()
	data.DeviceBrand = trafficData.GetBrand()
	data.DeviceModel = trafficData.GetModel()
	data.DeviceLanguage = trafficData.GetLanguage()
	data.SdkVersion = trafficData.GetSdkVersion()

	data.ConnectionType = int32(trafficData.GetConnectionType())
	data.OperatorType = int32(trafficData.GetOperatorType())
	data.DeviceOrientation = int32(trafficData.GetScreenOrientation())
	data.ScreenWidth = int32(trafficData.GetScreenWidth())
	data.ScreenHeight = int32(trafficData.GetScreenHeight())
	data.ScreenDensity = int32(trafficData.GetScreenDensity())
	data.UserAgent = trafficData.GetUserAgent()
	data.Referer = trafficData.GetReferer()

	data.Imei = trafficData.GetImei()
	data.ImeiMd5 = trafficData.GetMd5Imei()
	data.Idfv = trafficData.GetIdfv()
	data.IdfvMd5 = trafficData.GetMd5Idfv()
	data.Mac = trafficData.GetMac()
	data.MacMd5 = trafficData.GetMd5Mac()
	data.Oaid = trafficData.GetOaid()
	data.OaidMd5 = trafficData.GetMd5Oaid()
	data.AndroidId = trafficData.GetAndroidId()
	data.AndroidIdMd5 = trafficData.GetMd5AndroidId()
	data.Idfa = trafficData.GetIdfa()
	data.IdfaMd5 = trafficData.GetMd5Idfa()
	data.Aaid = trafficData.GetAaid()
	data.AaidMd5 = trafficData.GetMd5Aaid()
	data.Caid = trafficData.GetCaid()
	data.CaidMd5 = trafficData.GetMd5Caid()
	data.BootMark = trafficData.GetBootMark()
	data.UpdateMark = trafficData.GetUpdateMark()
}

func (c *DataCollector) trafficDataToAppDataProto(trafficData device_utils.AppDataProvider, data *data_collector_proto.AppData) {
	data.AppBundle = trafficData.GetAppBundle()
	data.AppName = trafficData.GetAppName()
	data.AppVersion = trafficData.GetAppVersion()
}

func (c *DataCollector) candidatesToResponseBundleProto(request *ad_service.AdRequest, candidates []*ad_service.AdCandidate, sampleRate int32) *data_collector_proto.ResponseBundle {
	data := data_collector_proto.NewResponseBundle()
	data.SampleRate = sampleRate

	c.requestToBasicRequestDataProto(request, &data.BasicRequest)
	c.trafficDataToDeviceDataProto(&request.Device, &data.TrafficDevice)
	c.trafficDataToAppDataProto(&request.App, &data.TrafficApp)

	data.Items = make([]data_collector_proto.ResponseBundle_Item, len(candidates))
	for idx, candidate := range candidates {
		responseItem := &data.Items[idx]
		modifiedTraffic := candidate.GetModifiedTrafficData()

		c.trafficDataToDeviceDataProto(modifiedTraffic, &responseItem.Device)
		c.trafficDataToAppDataProto(modifiedTraffic, &responseItem.App)
		c.candidateToBasicResponseDataProto(candidate, &responseItem.BasicResponse)
		c.candidateToCreativeDataProto(candidate, &responseItem.Creative)
		c.candidateToMonitorUrlDataProto(candidate, &responseItem.MonitorUrls)
		c.candidateToRankingDataProto(candidate, &responseItem.Ranking)
	}

	return data
}

func (c *DataCollector) candidateToMonitorUrlDataProto(candidate *ad_service.AdCandidate, data *data_collector_proto.MonitorUrlData) {
	genericAd := candidate.GetGenericAd()

	landingType := genericAd.GetLandingAction()
	if landingType == entity.LandingTypeInWebView && len(genericAd.GetDeepLinkUrl()) > 0 {
		landingType = entity.LandingTypeDeepLink
	}
	data.LandingType = int32(landingType)
	data.LandingPage = net_utils.CutUrl(genericAd.GetLandingUrl(), "?")
	data.DeeplinkUrl = net_utils.CutUrl(genericAd.GetDeepLinkUrl(), "?")
	for _, url := range genericAd.GetImpressionMonitorList() {
		data.ImpressionMonitorUrls = append(data.ImpressionMonitorUrls, net_utils.CutUrl(url, "?"))
	}
	for _, url := range genericAd.GetClickMonitorList() {
		data.ClickMonitorUrls = append(data.ClickMonitorUrls, net_utils.CutUrl(url, "?"))
	}
}

func (c *DataCollector) candidateToBasicResponseDataProto(candidate *ad_service.AdCandidate, data *data_collector_proto.BasicResponseData) {
	data.ErrorCode = candidate.GetErrorCode().Code

	genericAd := candidate.GetGenericAd()
	if genericAd != nil {
		data.BidAdId = int32(genericAd.GetAdId())
		data.BidDspId = int32(genericAd.GetDspId())
		data.BidAdgroupId = int32(genericAd.GetAdGroupId())
		data.DspSlotId = int32(genericAd.GetDspSlotId())
		data.BidAdType = int32(genericAd.GetAdType())
		data.DspTaskId = genericAd.GetDspTaskId()
	}

	genericCreative := candidate.GetCreative()
	if genericCreative != nil {
		data.BidCreativeId = int32(genericCreative.GetCreativeId())
		data.BidCreativeKey = genericCreative.GetCreativeKey()
		data.ProductId = int32(genericCreative.GetProductId())
		data.AdvertiserId = int32(genericCreative.GetAdvertiserId())
	}

	dspAd := candidate.GetDspResponseAd()
	if dspAd != nil {
		data.DspAdId = candidate.GetDspAdId()
		data.DspSlotKey = candidate.GetDspSlotKey()
	}

	bidPrice := candidate.GetBidPrice()
	dsoBidOPrice := candidate.GetDspBidPrice()
	chargePrice := candidate.GetChargePrice()
	bidFloor := candidate.GetBidFloor()

	data.BidPrice = int32(bidPrice.Price)
	data.BidType = int32(bidPrice.Type)
	data.DspBidPrice = int32(dsoBidOPrice.Price)
	data.DspBidType = int32(dsoBidOPrice.Type)
	data.ChargePrice = int32(chargePrice.Price)
	data.ChargeType = int32(chargePrice.Type)
	data.BidFloor = int32(bidFloor.Price)
	data.BidFloorType = int32(bidFloor.Type)
	data.UserScore = int32(candidate.GetUserScore())
	data.TrafficLevel = candidate.GetTrafficLevel()

	priceStrategy := candidate.GetPricingStrategy()
	if priceStrategy != nil {
		data.BiddingStrategyType = int32(priceStrategy.GetBiddingStrategyType())
		data.ChargeStrategyType = int32(priceStrategy.GetChargeStrategyType())
	}

	trafficRequestModifier := candidate.GetTrafficRequestModifier()
	if trafficRequestModifier != nil {
		data.TrafficRequestModifierId = int32(trafficRequestModifier.Id)
	}

	trafficResponseModifier := candidate.GetTrafficResponseModifier()
	if trafficResponseModifier != nil {
		data.TrafficResponseModifierId = int32(trafficResponseModifier.Id)
	}

	trafficStrategyList := candidate.GetTrafficStrategyList()
	mediaSlotTS := trafficStrategyList.GetFirstByStrategyType(entity.TrafficStrategyTypeMediaSlot)
	adTS := trafficStrategyList.GetFirstByStrategyType(entity.TrafficStrategyTypeAd)
	customTS := trafficStrategyList.GetFirstByStrategyType(entity.TrafficStrategyTypeCustomize)

	if mediaSlotTS != nil {
		data.MediaSlotTrafficStrategyId = int32(mediaSlotTS.Id)
	}

	if adTS != nil {
		data.AdTrafficStrategyId = int32(adTS.Id)
	}

	if customTS != nil {
		data.CustomTrafficStrategyId = int32(customTS.Id)
	}

	if candidate.GetIndexDeal() != nil {
		data.BidDealId = candidate.GetIndexDeal().DealId
	}

	//if genericCreative != nil {
	//	zap.L().Info("[DataCollector][candidateToBasicResponseDataProto] creativeId:, key:%s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(//		data.BidCreativeId)))), zap.Int64("id", int64(data.BidCreativeKey)))
	//}
}

func (c *DataCollector) candidateToCreativeDataProto(candidate *ad_service.AdCandidate, data *data_collector_proto.CreativeData) {
	genericCreative := candidate.GetCreative()
	if genericCreative == nil {
		return
	}

	data.CreativeType = int32(genericCreative.GetCreativeType())
	data.Key = genericCreative.GetCreativeKey()
	data.Title = genericCreative.GetTitle()
	data.Desc = genericCreative.GetDesc()

	var images, videos, videoCoverImage []string
	for _, material := range genericCreative.GetMaterialList() {
		switch material.MaterialType {
		case entity.MaterialTypeImage:
			images = append(images, material.Url)
		case entity.MaterialTypeVideo:
			videos = append(videos, material.Url)
		case entity.MaterialTypeCoverImage:
			videoCoverImage = append(videoCoverImage, material.Url)
		}
	}

	data.Images, _ = sonic.MarshalString(images)
	data.Videos, _ = sonic.MarshalString(videos)
	data.VideoCoverImage, _ = sonic.MarshalString(videoCoverImage)
	data.MaterialList = genericCreative.GetMaterialList().MarshalJsonString()

	mainMaterial := candidate.GetMainMaterial()
	if mainMaterial != nil {
		data.MainMaterialIsLocal = mainMaterial.IsLocal()
		data.MainMaterialKey = mainMaterial.Key
		data.MainMaterialUrl = mainMaterial.Url
		for _, tag := range mainMaterial.MaterialTags {
			data.MainMaterialTags = append(data.MainMaterialTags, int32(tag))
		}
	}
}

func (c *DataCollector) candidateToRawSampleLogProto(request *ad_service.AdRequest, candidate *ad_service.AdCandidate) *data_collector_proto.RawSampleLog {
	data := data_collector_proto.NewRawSampleLog()
	data.RequestTime = request.RequestTime.UnixNano()
	data.ProcessTime = time.Now().UnixNano() - data.RequestTime
	data.RequestId = request.GetRequestId()
	data.MediaId = int32(request.GetMediaId())
	data.MediaSlotId = int32(request.GetMediaSlotId())
	data.MediaSlotKey = request.GetMediaSlotKey()
	data.SourceSlotId = request.GetSourceSlotId()
	for _, key := range request.GetCreativeTemplateKeyList() {
		data.CreativeTemplateKey = append(data.CreativeTemplateKey, uint64(key))
	}

	data.RequestErrorCode = request.Response.GetAdErrorCode().Code
	data.MediaRequest = request.GetSampleRequest()
	data.MediaResponse = request.GetSampleResponse()

	if candidate != nil {
		data.CandidateErrorCode = candidate.GetErrorCode().Code
		data.AdGroupId = int32(candidate.GetAd().AdGroupId)
		data.AdId = int32(candidate.GetAd().AdId)
		data.DspId = int32(candidate.GetAd().DspId)
		data.DspSlotId = int32(candidate.GetDspSlotId())
		data.DspRequest = candidate.GetDspRequestSample()
		data.DspResponse = candidate.GetDspResponseSample()
	}

	return data
}

func (c *DataCollector) candidateToRankingDataProto(candidate *ad_service.AdCandidate, data *data_collector_proto.RankingData) {
	rankerResult := candidate.GetRankerResult()

	data.ModelName = rankerResult.Model
	data.Ctr = uint32(rankerResult.ECtr * 1e6)
	data.Cvr = uint32(rankerResult.ECvr * 1e6)
	data.ECpm = uint32(rankerResult.ECpm)
	data.ECpc = uint32(rankerResult.ECpc)
	data.ECpa = uint32(rankerResult.ECpa)
}
