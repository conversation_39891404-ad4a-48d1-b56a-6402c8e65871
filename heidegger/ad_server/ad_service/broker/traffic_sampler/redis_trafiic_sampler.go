package traffic_sampler

import (
	"encoding/json"
	"fmt"
	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
)

type SampleData struct {
	Timestamp         time.Time `json:"timestamp"`
	RequestId         string    `json:"request_id"`
	MediaId           utils.ID  `json:"media_id"`
	MediaSlotKey      string    `json:"media_slot_key"`
	MediaSlotId       utils.ID  `json:"media_slot_id"`
	AdId              utils.ID  `json:"ad_id"`
	DspId             utils.ID  `json:"dsp_id"`
	DspSlotId         utils.ID  `json:"dsp_slot_id"`
	TrafficRequest    string    `json:"traffic_request"`
	TrafficResponse   string    `json:"traffic_response"`
	DspRequest        string    `json:"dsp_request"`
	DspResponse       string    `json:"dsp_response"`
	RequestErrCode    int       `json:"request_err_code"`
	RequestErrMsg     string    `json:"request_err_msg"`
	RequestErrHuman   string    `json:"request_err_human"`
	CandidateErrCode  int       `json:"candidate_err_code"`
	CandidateErrMsg   string    `json:"candidate_err_msg"`
	CandidateErrHuman string    `json:"candidate_err_human"`
}

type RedisTrafficSampler struct {
	redisClient *redis.Client

	sampleChan chan *SampleData
	term       chan struct{}
}

func NewRedisTrafficSampler(redisClient *redis.Client) *RedisTrafficSampler {
	return &RedisTrafficSampler{
		redisClient: redisClient,
		sampleChan:  make(chan *SampleData, 64),
		term:        make(chan struct{}),
	}
}

func (s *RedisTrafficSampler) Start() error {
	go s.consumeLoop()
	return nil
}

func (s *RedisTrafficSampler) Stop() {
	close(s.term)
}

func (s *RedisTrafficSampler) SampleTraffic(adRequest *ad_service.AdRequest) {
	if !adRequest.GetIsSampled() {
		return
	}

	//zap.L().Info("RedisTrafficSampler.SampleTraffic media", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(adRequest.GetMediaId())))))

	select {
	case s.sampleChan <- &SampleData{
		Timestamp:       adRequest.RequestTime,
		RequestId:       adRequest.GetRequestId(),
		MediaId:         adRequest.GetMediaId(),
		MediaSlotId:     adRequest.GetMediaSlotId(),
		MediaSlotKey:    adRequest.GetMediaSlotKey(),
		AdId:            0,
		DspId:           0,
		DspSlotId:       0,
		TrafficRequest:  string(adRequest.GetSampleRequest()),
		TrafficResponse: string(adRequest.GetSampleResponse()),
		RequestErrCode:  int(adRequest.Response.GetAdErrorCode().Code),
		RequestErrMsg:   adRequest.Response.GetAdErrorCode().Detail,
		RequestErrHuman: adRequest.Response.GetAdErrorCode().Human(),
	}:
	default:
		zap.L().Debug("RedisTrafficSampler.SampleTraffic sampleChan is full")
	}
	return
}

func (s *RedisTrafficSampler) SampleDsp(adRequest *ad_service.AdRequest, candidate *ad_service.AdCandidate) {
	if !candidate.GetIsSampled() {
		return
	}

	select {
	case s.sampleChan <- &SampleData{
		Timestamp:         adRequest.RequestTime,
		RequestId:         adRequest.GetRequestId(),
		MediaId:           adRequest.GetMediaId(),
		MediaSlotId:       adRequest.GetMediaSlotId(),
		MediaSlotKey:      adRequest.GetMediaSlotKey(),
		AdId:              candidate.GetAd().GetAdId(),
		DspId:             candidate.GetAd().GetDspId(),
		DspSlotId:         candidate.GetDspSlotId(),
		TrafficRequest:    string(adRequest.GetSampleRequest()),
		TrafficResponse:   string(adRequest.GetSampleResponse()),
		DspRequest:        string(candidate.GetDspRequestSample()),
		DspResponse:       string(candidate.GetDspResponseSample()),
		RequestErrCode:    int(adRequest.Response.GetAdErrorCode().Code),
		RequestErrMsg:     adRequest.Response.GetAdErrorCode().Detail,
		RequestErrHuman:   adRequest.Response.GetAdErrorCode().Human(),
		CandidateErrCode:  int(candidate.GetErrorCode().Code),
		CandidateErrMsg:   candidate.GetErrorCode().Detail,
		CandidateErrHuman: candidate.GetErrorCode().Error(),
	}:
	default:
		zap.L().Error("RedisTrafficSampler.SampleDsp sampleChan is full")
	}
	return
}

func (s *RedisTrafficSampler) consumeLoop() {
	zap.L().Info("[RedisTrafficSampler] consumeLoop")

	for {
		select {
		case sampleData := <-s.sampleChan:
			data, err := json.Marshal(sampleData)
			if err != nil {
				zap.L().Error("RedisTrafficSampler.consumeLoop marshal sampleData error", zap.Error(err))
				continue
			}

			if sampleData.DspId == 0 {
				s.WriteTrafficSample(sampleData, data)
			} else {
				s.WriteDspSample(sampleData, data)
			}
		case <-s.term:
			return
		}
	}
}

func (s *RedisTrafficSampler) WriteTrafficSample(sampleData *SampleData, data []byte) {
	s.WriteTrafficSampleRedis(sampleData, data)
	s.WriteTrafficSampleKafka(sampleData, data)
}

func (s *RedisTrafficSampler) WriteDspSample(sampleData *SampleData, data []byte) {
	s.WriteDspSampleRedis(sampleData, data)
	s.WriteDspSampleKafka(sampleData, data)
}

func (s *RedisTrafficSampler) WriteTrafficSampleRedis(sampleData *SampleData, data []byte) {
	mediaKey := s.getSamplerMediaKey(sampleData.MediaId)

	//s.redisClient.Do()
	if err := s.redisClient.Set(mediaKey, data, time.Hour*24).Err(); err != nil {
		zap.L().Error("RedisTrafficSampler.WriteTrafficSampleRedis redisClient error", zap.Error(err))
		return
	}

	mediaSlotKey := s.getSamplerMediaSlotKey(sampleData.MediaId, sampleData.MediaSlotKey)
	if err := s.redisClient.Set(mediaSlotKey, data, time.Hour*24).Err(); err != nil {
		zap.L().Error("RedisTrafficSampler.WriteTrafficSampleRedis redisClient error", zap.Error(err))
		return
	}
}

func (s *RedisTrafficSampler) WriteTrafficSampleKafka(sampleData *SampleData, data []byte) {
}

func (s *RedisTrafficSampler) WriteDspSampleRedis(sampleData *SampleData, data []byte) {
	dspKey := s.getSamplerDspKey(sampleData.DspId)
	if err := s.redisClient.Set(dspKey, data, time.Hour*24).Err(); err != nil {
		zap.L().Error("RedisTrafficSampler.WriteDspSampleRedis redisClient error", zap.Error(err))
		return
	}

	adKey := s.getSamplerAdKey(sampleData.AdId)
	if err := s.redisClient.Set(adKey, data, time.Hour*24).Err(); err != nil {
		zap.L().Error("RedisTrafficSampler.WriteTrafficSampleRedis redisClient error", zap.Error(err))
		return
	}

	dspSlotKey := s.getSamplerDspSlotKey(sampleData.DspId, sampleData.DspSlotId)
	if err := s.redisClient.Set(dspSlotKey, data, time.Hour*24).Err(); err != nil {
		zap.L().Error("RedisTrafficSampler.WriteTrafficSampleRedis redisClient error", zap.Error(err))
		return
	}
}

func (s *RedisTrafficSampler) WriteDspSampleKafka(sampleData *SampleData, data []byte) {

}

func (s *RedisTrafficSampler) getSamplerMediaKey(mediaId utils.ID) string {
	return fmt.Sprintf("traffic_sampler:media:%d", mediaId)
}

func (s *RedisTrafficSampler) getSamplerMediaSlotKey(mediaId utils.ID, mediaSlotKey string) string {
	return fmt.Sprintf("traffic_sampler:media_slot:%d_%s", mediaId, mediaSlotKey)
}

func (s *RedisTrafficSampler) getSamplerDspKey(dspId utils.ID) string {
	return fmt.Sprintf("traffic_sampler:dsp:%d", dspId)
}

func (s *RedisTrafficSampler) getSamplerAdKey(adId utils.ID) string {
	return fmt.Sprintf("traffic_sampler:ad:%d", adId)
}

func (s *RedisTrafficSampler) getSamplerDspSlotKey(dspId utils.ID, dspSlotId utils.ID) string {
	return fmt.Sprintf("traffic_sampler:dsp_slot:%d_%d", dspId, dspSlotId)
}
