package baidu_dsp_broker

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"strings"
)

type BaiduAsset struct {
	TemplateId int `json:"templateId"`
	Ratio      int `json:"ratio"`
	Duration   int `json:"duration,omitempty"`
}

type BaiduTemplate struct {
	AdType int          `json:"adType"`
	Assets []BaiduAsset `json:"assets"`
}

type BaiduSlotSlotInfo struct {
	*entity.DspSlotInfo
	AppId       string `json:"app_id"`
	MediaId     int64  `json:"media_id"`
	AdType      int    `json:"ad_type"`
	TemplateIds []int  `json:"template_id"`
	Ratio       []int  `json:"ratio"`
	AppVersion  string `json:"app_version"`
	PkgName     string `json:"pkg_name"`
	ActionType  []int  `json:"action_type"`
}

func (info *BaiduSlotSlotInfo) DumpPrettyJson() string {
	result, _ := json.MarshalIndent(info, "", "    ")
	return string(result)
}

func (info *BaiduSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppId, err = dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get app_id from extra_data failed, err: %v", err)
	}

	info.MediaId, err = dspSlotInfo.ExtraData.GetInt64("media_id")
	if err != nil {
		mediaId, err1 := dspSlotInfo.ExtraData.GetString("media_id")
		if err1 != nil {
			return fmt.Errorf("get media_id from extra_data failed, err: %v", err1)
		}
		info.MediaId = type_convert.GetAssertInt64(mediaId)
	}

	adType, err := dspSlotInfo.ExtraData.GetString("ad_type")
	if err != nil {
		return fmt.Errorf("get ad_type from extra_data failed, err: %v", err)
	}

	switch adType {
	case "native":
		info.AdType = 0
	case "splash":
		info.AdType = 3
	case "rvideo":
		info.AdType = 4
	case "insert":
		info.AdType = 6

	}

	templateIds, err := dspSlotInfo.ExtraData.GetString("template_id")
	if err != nil {
		return fmt.Errorf("get template_id from extra_data failed, err: %v", err)
	}

	templateIdsArr := strings.Split(templateIds, ",")
	if len(templateIdsArr) == 0 {
		return fmt.Errorf("get template_ids from extra_data failed, err: %v", err)
	}

	info.TemplateIds = make([]int, 0)
	for _, tId := range templateIdsArr {
		switch tId {
		case "img":
			info.TemplateIds = append(info.TemplateIds, 1)
		case "bimg":
			info.TemplateIds = append(info.TemplateIds, 2)
		case "imgs":
			info.TemplateIds = append(info.TemplateIds, 3)
		case "video":
			info.TemplateIds = append(info.TemplateIds, 4)
		case "rvideo":
			info.TemplateIds = append(info.TemplateIds, 5)
		}
	}

	info.Ratio = make([]int, 0)
	ratios, err := dspSlotInfo.ExtraData.GetString("ratio")
	if err == nil {
		ratiosArr := strings.Split(ratios, ",")
		if len(ratiosArr) != 0 {
			for _, rId := range ratiosArr {
				info.Ratio = append(info.Ratio, type_convert.GetAssertInt(rId))
			}
		}
	}

	info.ActionType = make([]int, 0)
	actions, err := dspSlotInfo.ExtraData.GetString("action_type")
	if err == nil {
		actionArr := strings.Split(actions, ",")
		if len(actionArr) != 0 {
			for _, rId := range actionArr {
				info.ActionType = append(info.ActionType, type_convert.GetAssertInt(rId))
			}
		}
	}

	return nil
}

type BaiduDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*BaiduSlotSlotInfo
}

func NewBaiduDspSlotRegister(dspId utils.ID) *BaiduDspSlotRegister {
	return &BaiduDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*BaiduSlotSlotInfo),
	}
}

func (r *BaiduDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *BaiduDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*BaiduSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &BaiduSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[BaiduDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *BaiduDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *BaiduDspSlotRegister) GetSlotInfo(slotId utils.ID) *BaiduSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
