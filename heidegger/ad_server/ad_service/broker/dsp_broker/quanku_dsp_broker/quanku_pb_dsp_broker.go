package quanku_dsp_broker

import (
	"io"
	"net/http"
	"strconv"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/quanku_dsp_broker/quanku_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	objectpool "gitlab.com/dev/heidegger/library/object_pool"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

var (
	bidRequestPool = objectpool.NewObjectPool(func() *quanku_proto.BiddingAdReq {
		return new(quanku_proto.BiddingAdReq)
	})
	bidRequestDevicePool = objectpool.NewObjectPool(func() *quanku_proto.BiddingAdReq_Device {
		return new(quanku_proto.BiddingAdReq_Device)
	})
	bidRequestGeoPool = objectpool.NewObjectPool(func() *quanku_proto.BiddingAdReq_Geo {
		return new(quanku_proto.BiddingAdReq_Geo)
	})
	bidRequestImpPool = objectpool.NewObjectPool(func() *quanku_proto.BiddingAdReq_Space {
		return new(quanku_proto.BiddingAdReq_Space)
	})
	bidRequestAppPool = objectpool.NewObjectPool(func() *quanku_proto.BiddingAdReq_App {
		return new(quanku_proto.BiddingAdReq_App)
	})
	bidRequestUserPool = objectpool.NewObjectPool(func() *quanku_proto.BiddingAdReq_User {
		return new(quanku_proto.BiddingAdReq_User)
	})
	bidResponsePool = objectpool.NewObjectPool(func() *quanku_proto.BiddingAdRes {
		return new(quanku_proto.BiddingAdRes)
	})
)

type QuanKuPbDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *QuanKuDspSlotRegister

	MacroWinPrice string
	log           *zap.Logger
}

func NewQuanKuPbDspBroker(dspId utils.ID) *QuanKuPbDspBroker {
	return &QuanKuPbDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{DspId: dspId},
		slotRegister:  NewQuanKuDspSlotRegister(dspId),
		MacroWinPrice: "${AUCTION_PRICE}",
		log:           zap.L().With(zap.String("broker", "QuanKuPbDspBroker")),
	}
}

func (impl *QuanKuPbDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *QuanKuPbDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		impl.log.Errorf("candidateList len: %d", len(candidateList))
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		impl.log.Errorf("dspSlot not found: %d", trafficData.GetDspSlotId())
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidFloor := candidate.GetBidFloor()

	bidReq := bidRequestPool.Get()
	defer bidRequestPool.Put(bidReq)

	bidReq.ReqId = request.GetRequestId()
	bidReq.MediaId = dspSlot.MediaId
	bidReq.Token = dspSlot.Token
	bidReq.Version = "1.2.1"
	bidReq.IsHttps = false

	bidReqApp := bidRequestAppPool.Get()
	defer bidRequestAppPool.Put(bidReqApp)
	bidReq.App = bidReqApp

	bidReqApp.Name = trafficData.GetAppName()
	bidReqApp.Version = trafficData.GetAppVersion()
	bidReqApp.Bundle = trafficData.GetAppBundle()

	bidReqDevice := impl.encodeDevice(request, trafficData)
	defer bidRequestDevicePool.Put(bidReqDevice)
	bidReq.Device = bidReqDevice

	bidReqImp := bidRequestImpPool.Get()
	defer bidRequestImpPool.Put(bidReqImp)
	bidReq.Space = bidReqImp

	slotIdInt, err := strconv.ParseInt(slotId, 10, 64)
	if err != nil {
		return nil, err_code.ErrDspSlotNotFound.Wrap(err)
	}
	bidReqImp.Id = slotIdInt
	bidReqImp.W = int32(dspSlot.Width)
	bidReqImp.H = int32(dspSlot.Height)
	bidReqImp.BidFloor = bidFloor.Price
	if len(dspSlot.Tid) > 0 {
		bidReqImp.Tid = dspSlot.Tid
	}

	bidReqGeo := bidRequestGeoPool.Get()
	defer bidRequestGeoPool.Put(bidReqGeo)
	bidReq.Geo = bidReqGeo

	bidReqGeo.Type = impl.mappingGeoType(request.Device.GeoStandard)
	bidReqGeo.Lng = trafficData.GetGeoLongitude()
	bidReqGeo.Lat = trafficData.GetGeoLatitude()
	bidReqGeo.T = time_utils.GetTimeUnixSecondUnsafe()

	bidReq.AppIdList = impl.getInstallAppIds(impl.GetDspId(), request.App.InstalledAppIds)

	if len(dspSlot.PkgName) > 0 {
		bidReqApp.Bundle = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidReqApp.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidReqApp.Version = dspSlot.AppVersion
	}

	if bidReqImp.W == 0 || bidReqImp.H == 0 {
		bidReqImp.W = int32(trafficData.GetSlotWidth())
		bidReqImp.H = int32(trafficData.GetSlotHeight())
	}
	if (bidReqImp.W == 0 || bidReqImp.H == 0) && len(request.SlotSize) > 0 {
		bidReqImp.W = int32(request.SlotSize[0].Width)
		bidReqImp.H = int32(request.SlotSize[0].Height)
	}

	httpReq, _, err := impl.BuildPbHttpHttpRequest(bidReq)
	if err != nil {
		impl.log.WithError(err).Error("BuildPbHttpHttpRequest error")
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}
	impl.SampleDspBroadcastProtobufRequest(impl.GetDspId(), trafficData.GetDspSlotId(), candidate, bidReq)

	if request.IsDebug {
		payload, _ := sonic.Marshal(bidReq)
		impl.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}

	return httpReq, nil
}

func (impl *QuanKuPbDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) *quanku_proto.BiddingAdReq_Device {
	bidReqDevice := bidRequestDevicePool.Get()
	carrier, carrierName := impl.mappingCarrier(trafficData.GetOperatorType())

	bidReqDevice.Ua = trafficData.GetUserAgent()
	bidReqDevice.Type = impl.mappingDeviceType(trafficData.GetDeviceType())
	bidReqDevice.Os = impl.mappingOsType(trafficData.GetOsType())
	bidReqDevice.Osv = trafficData.GetOsVersion()
	bidReqDevice.MacMd5 = trafficData.GetMd5Mac()
	bidReqDevice.MacStMd5 = ""
	bidReqDevice.Mac = trafficData.GetMac()
	bidReqDevice.Vendor = trafficData.GetBrand()
	bidReqDevice.Model = trafficData.GetModel()
	bidReqDevice.W = trafficData.GetScreenWidth()
	bidReqDevice.H = trafficData.GetScreenHeight()
	bidReqDevice.Ppi = request.Device.PPI
	bidReqDevice.Idfa = trafficData.GetIdfa()
	bidReqDevice.IdfaMd5 = trafficData.GetMd5Idfa()
	bidReqDevice.Openudid = trafficData.GetOpenUdid()
	bidReqDevice.Oaid = trafficData.GetOaid()
	bidReqDevice.Imei = trafficData.GetImei()
	bidReqDevice.ImeiMd5 = trafficData.GetMd5Imei()
	bidReqDevice.Anid = trafficData.GetAndroidId()
	bidReqDevice.AnidMd5 = trafficData.GetMd5AndroidId()
	bidReqDevice.Aaid = trafficData.GetAaid()
	bidReqDevice.AaidMd5 = ""
	bidReqDevice.Caid = device_utils.GetCaidRaw(trafficData.GetCaid())
	bidReqDevice.CaidVer = device_utils.GetCaidVersion(trafficData.GetCaid())
	bidReqDevice.Paid = request.Device.Paid
	bidReqDevice.Carrier = carrier
	bidReqDevice.ConnType = impl.mappingConnectionType(trafficData.GetConnectionType())
	bidReqDevice.DeviceName = request.Device.HardwareMachineCode
	bidReqDevice.DeviceModel = trafficData.GetModel()
	bidReqDevice.InitTime = trafficData.GetDeviceInitTime()
	bidReqDevice.StartupTime = trafficData.GetBootMark()
	bidReqDevice.MbTime = trafficData.GetUpdateMark()
	bidReqDevice.CountryCode = "CN"
	bidReqDevice.CarrierName = carrierName
	bidReqDevice.MemSize = request.Device.SystemTotalMem
	bidReqDevice.DiskSize = request.Device.SystemTotalDisk
	bidReqDevice.LocalTimeZone = "28800"
	bidReqDevice.Language = "zh-Hans-CN"
	bidReqDevice.PhoneNameMd5 = ""

	if request.Device.IsIp6 {
		bidReqDevice.Ipv6 = trafficData.GetRequestIp()
	} else {
		bidReqDevice.Ip = trafficData.GetRequestIp()
	}

	if len(bidReqDevice.DeviceName) == 0 {
		bidReqDevice.DeviceName = trafficData.GetRomName()
	}

	if len(bidReqDevice.DeviceName) == 0 {
		bidReqDevice.DeviceName = trafficData.GetModel()
	}

	if len(trafficData.GetDeviceStartupTime()) > 0 {
		bidReqDevice.StartupTime = trafficData.GetDeviceStartupTime()
	}

	if len(trafficData.GetDeviceUpgradeTime()) > 0 {
		bidReqDevice.MbTime = trafficData.GetDeviceUpgradeTime()
	}

	if len(request.Device.DeviceName) > 0 {
		bidReqDevice.PhoneNameMd5 = md5_utils.GetMd5String(request.Device.DeviceName)
	}

	if trafficData.GetOsType() == entity.OsTypeIOS &&
		len(bidReqDevice.Paid) == 0 &&
		len(trafficData.GetDeviceInitTime()) > 0 &&
		len(trafficData.GetDeviceUpgradeTime()) > 0 &&
		len(trafficData.GetDeviceStartupTime()) > 0 {
		bidReqDevice.Paid = string_utils.ConcatString(
			md5_utils.GetMd5String(trafficData.GetDeviceInitTime()),
			"-",
			md5_utils.GetMd5String(trafficData.GetDeviceUpgradeTime()),
			"-",
			md5_utils.GetMd5String(trafficData.GetDeviceStartupTime()))
	}

	return bidReqDevice
}

func (impl *QuanKuPbDspBroker) getInstallAppIds(dspId utils.ID, appIds []int) []string {
	result := make([]string, 0)
	if len(appIds) == 0 {
		return result
	}

	dspIdStr := dspId.String()
	for _, appid := range appIds {
		key := dspIdStr + "_" + type_convert.GetAssertString(appid)
		externalMapping := impl.ExternalMappingLoader.GetDspAppMappingMapByKey(key)
		if externalMapping != nil {
			result = append(result, externalMapping.SourceValue)
		}
	}

	return result
}

func (impl *QuanKuPbDspBroker) mappingGeoType(geoStandard int) quanku_proto.BiddingAdReq_GeoType {
	switch geoStandard {
	case 0, 3:
		return quanku_proto.BiddingAdReq_BD09
	case 1:
		return quanku_proto.BiddingAdReq_GCJ02
	case 2:
		return quanku_proto.BiddingAdReq_WGS84
	default:
		return quanku_proto.BiddingAdReq_GEO_UNKNOWN
	}
}

func (impl *QuanKuPbDspBroker) mappingCarrier(carrier entity.OperatorType) (string, string) {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return "46000", "中国移动"
	case entity.OperatorTypeChinaTelecom:
		return "46003", "中国电信"
	case entity.OperatorTypeTietong:
		return "46020", "中国铁通"
	case entity.OperatorTypeChinaUnicom:
		return "46001", "中国联通"
	default:
		return "", ""
	}
}

func (impl *QuanKuPbDspBroker) mappingDeviceType(s entity.DeviceType) quanku_proto.BiddingAdReq_DeviceType {
	switch s {
	case entity.DeviceTypeMobile:
		return quanku_proto.BiddingAdReq_PHONE
	case entity.DeviceTypePad:
		return quanku_proto.BiddingAdReq_TABLET
	case entity.DeviceTypeOtt:
		return quanku_proto.BiddingAdReq_TV
	default:
		return quanku_proto.BiddingAdReq_DEVICE_TYPE_UNKNOWN
	}
}

func (impl *QuanKuPbDspBroker) mappingOsType(os entity.OsType) quanku_proto.BiddingAdReq_DeviceOS {
	switch os {
	case entity.OsTypeIOS:
		return quanku_proto.BiddingAdReq_IOS
	case entity.OsTypeAndroid:
		return quanku_proto.BiddingAdReq_ANDROID
	default:
		return quanku_proto.BiddingAdReq_DEVICE_OS_UNKNOWN
	}
}

func (impl *QuanKuPbDspBroker) mappingConnectionType(connectionType entity.ConnectionType) quanku_proto.BiddingAdReq_ConnectionType {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return quanku_proto.BiddingAdReq_ETHERNET
	case entity.ConnectionTypeWifi:
		return quanku_proto.BiddingAdReq_WIFI
	case entity.ConnectionTypeCellular:
		return quanku_proto.BiddingAdReq_CELLULAR_UNKNOWN
	case entity.ConnectionType2G:
		return quanku_proto.BiddingAdReq_CELLULAR_2G
	case entity.ConnectionType3G:
		return quanku_proto.BiddingAdReq_CELLULAR_3G
	case entity.ConnectionType4G:
		return quanku_proto.BiddingAdReq_CELLULAR_4G
	case entity.ConnectionType5G:
		return quanku_proto.BiddingAdReq_CELLULAR_5G
	default:
		return quanku_proto.BiddingAdReq_CONNECTION_TYPE_UNKNOWN
	}
}

func (impl *QuanKuPbDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if resp.StatusCode != 200 {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			type_convert.GetAssertString(resp.StatusCode), type_convert.GetAssertString(resp.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			"1", "读取body失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	bidResp := bidResponsePool.Get()
	defer bidResponsePool.Put(bidResp)
	if err := impl.ParsePbHttpHttpResponse(resp, data, bidResp); err != nil {
		impl.log.WithError(err).Debug("ParseResponse error")
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			"2", "body反序列化失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	impl.SampleDspBroadcastProtobufResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, bidResp)
	if request.IsDebug {
		payload, _ := sonic.Marshal(bidResp)
		impl.log.WithField("response", string(payload)).Info("ParseResponse debug")
	}
	impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
		broadcastCandidate.GetDspSlotId().String(),
		type_convert.GetAssertString(bidResp.ErrCode), impl.GetCodeMsg(bidResp.ErrCode))

	if bidResp.ErrCode != 0 || len(bidResp.Ad) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, bid := range bidResp.Ad {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = &entity.AppInfo{}
		if bid.AppInfo != nil {
			app := bid.AppInfo
			candidateAd.AppInfo.AppName = app.AppName
			candidateAd.AppInfo.AppVersion = app.AppVersion
			candidateAd.AppInfo.PackageName = app.PackageName
			candidateAd.AppInfo.PackageSize = int(app.PackageSize)
			candidateAd.AppInfo.Develop = app.Publisher
			candidateAd.AppInfo.Privacy = app.Privacy
			candidateAd.AppInfo.Permission = app.Permission
			candidateAd.AppInfo.AppDescURL = app.AppIntroLink
			candidateAd.AppInfo.AppDesc = app.AppIntro
			if len(candidateAd.AppInfo.PackageName) == 0 {
				candidateAd.AppInfo.PackageName = app.BundleId
			}
		}

		if bid.WxProgramId != "" && bid.WxTargetPath != "" {
			candidateAd.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   bid.WxProgramId,
				ProgramPath: bid.WxTargetPath,
			}
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, bid)

		creative := impl.ParseCreativeData(bid)
		if creative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(bid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(creative)
		candidate.SetDspAdID(bid.Id)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break
	}

	return result, nil
}

func (impl *QuanKuPbDspBroker) ParseCreativeData(bid *quanku_proto.BiddingAdRes_Ad) *entity.Creative {
	creative := &entity.Creative{
		CreativeKey: bid.Id,
	}

	item := bid

	if len(item.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         item.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Desc) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         item.Desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	for _, image := range item.Image {
		if len(image.Url) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          image.Url,
				Width:        image.W,
				Height:       image.H,
			}

			switch image.Type {
			case quanku_proto.BiddingAdRes_ICON, quanku_proto.BiddingAdRes_CORNER:
				material.MaterialType = entity.MaterialTypeIcon
			case quanku_proto.BiddingAdRes_LOGO:
				material.MaterialType = entity.MaterialTypeLogo
			}

			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if item.Video != nil && len(item.Video.Url) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          item.Video.Url,
			Width:        item.Video.W,
			Height:       item.Video.H,
			Duration:     float64(item.Duration),
		}
		creative.MaterialList = append(creative.MaterialList, material)

		if len(item.Video.CoverUrl) > 0 {
			material1 := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          item.Video.CoverUrl,
				Width:        item.Video.W,
				Height:       item.Video.H,
			}
			creative.MaterialList = append(creative.MaterialList, material1)
		}
	}

	return creative
}

func (impl *QuanKuPbDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.PlaintextEncode(uint64(chargePrice))
	//result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *QuanKuPbDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *quanku_proto.BiddingAdRes_Ad) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      make([]string, 0),
		LandingAction:         entity.LandingTypeInWebView,
		LandingUrl:            bid.Landpage,
	}

	switch bid.Act {
	case quanku_proto.BiddingAdRes_DEEP_LINK:
		tracking.LandingAction = entity.LandingTypeDeepLink
		tracking.DeepLinkUrl = bid.Deeplink
		if len(bid.Iulk) > 0 {
			tracking.DeepLinkUrl = bid.Iulk
		}
		if len(tracking.DeepLinkUrl) == 0 {
			tracking.DeepLinkUrl = bid.MarketAppUrl
		}
	case quanku_proto.BiddingAdRes_DOWNLOAD:
		tracking.LandingAction = entity.LandingTypeDownload
		tracking.DownloadUrl = bid.DownloadUrl
	case quanku_proto.BiddingAdRes_WECHAT_MINI_PROGRAM:
		tracking.LandingAction = entity.LandingTypeWeChatProgram
	}

	for _, track := range bid.Tracking {
		switch track.Event {
		case "start":
			tracking.ImpressionMonitorList = track.Url
		case "click":
			tracking.ClickMonitorList = track.Url
		case "firstQuartile":
			tracking.VideoStartUrlList = track.Url
		case "complete", "close":
			tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, track.Url...)
		case "downloadStart":
			tracking.AppDownloadStartedMonitorList = track.Url
		case "downloadEnd":
			tracking.AppDownloadFinishedMonitorList = track.Url
		case "installStart":
			tracking.AppInstallStartMonitorList = track.Url
		case "installEnd":
			tracking.AppInstalledFinishMonitorList = track.Url
		case "active":
			tracking.AppOpenMonitorList = track.Url
		case "open":
			tracking.DeepLinkMonitorList = track.Url
		}
	}

	return tracking
}

func (impl *QuanKuPbDspBroker) GetCodeMsg(code int32) string {
	switch code {
	case 500:
		return "系统内部错误"
	case 501:
		return "JSON 解析错误"
	case 101:
		return "APP name,bundle,version 均不能为空"
	case 102:
		return "设备类型错误"
	case 103:
		return "操作系统类型错误"
	case 104:
		return "操作系统版本号不能为空"
	case 105:
		return "MAC 信息不能为空"
	case 106:
		return "设备生产商或型号不能为空"
	case 107:
		return "设备宽高错误"
	case 108:
		return "IMEI 不能为空"
	case 109:
		return "Android id 不能为空"
	case 110:
		return "IDFA 不能为空"
	case 111:
		return "OpenUDID 不能为空"
	case 201:
		return "Token验证失败"
	case 204:
		return "当前无广告填充"
	default:
		return utils.EmptyString
	}
}
