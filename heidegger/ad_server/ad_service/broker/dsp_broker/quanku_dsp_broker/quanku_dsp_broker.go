package quanku_dsp_broker

import (
	"encoding/json"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/quanku_dsp_broker/quanku_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"fmt"
)

type QuanKuDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *QuanKuDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string
}

func NewQuanKuDspBroker(dspId utils.ID) *QuanKuDspBroker {
	return &QuanKuDspBroker{
		slotRegister:  NewQuanKuDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "",
	}
}

func (impl *QuanKuDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *QuanKuDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("QuanKuDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("QuanKuDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))

		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("QuanKuDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidFloor := candidate.GetBidFloor()

	qkRequest := &quanku_broker_entity.BidRequest{
		ReqId:   request.GetRequestId(),
		MediaId: dspSlot.MediaId,
		Token:   dspSlot.Token,
		Version: "1.0.1",
		IsHttps: false,
		App: quanku_broker_entity.App{
			Bundle:  trafficData.GetAppBundle(),
			Name:    trafficData.GetAppName(),
			Version: trafficData.GetAppVersion(),
		},
		Device: impl.encodeDevice(request, trafficData),
		Space: quanku_broker_entity.Space{
			ID:           slotId,
			Width:        int(trafficData.GetSlotWidth()),
			Height:       int(trafficData.GetSlotHeight()),
			Tid:          dspSlot.Tid,
			BidFloor:     int(bidFloor.Price),
			CreativeType: dspSlot.CreativeType,
		},
		AppIdList: impl.getInstallAppIds(impl.dspId, request.App.InstalledAppIds),
	}

	if len(dspSlot.PkgName) > 0 {
		qkRequest.App.Bundle = dspSlot.PkgName
	}

	if len(dspSlot.AppName) > 0 {
		qkRequest.App.Name = dspSlot.AppName
	}

	if qkRequest.Space.Width == 0 || qkRequest.Space.Height == 0 {
		if len(request.SlotSize) > 0 {
			qkRequest.Space.Width = int(request.SlotSize[0].Width)
			qkRequest.Space.Height = int(request.SlotSize[0].Height)
		}
	}

	//requestBody, err := easyjson.Marshal(qkRequest)
	//if err != nil {
	//	zap.L().Error("QuanKuDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
	//	return nil, err_code.ErrBroadcastRequestBuildFail
	//}

	req, _, err := impl.BuildSonicJsonHttpRequest(qkRequest)
	if err != nil {
		zap.L().Error("QuanKuDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}

	if request.IsDebug {
		requestBody, _ := sonic.Marshal(qkRequest)
		zap.L().Info("QuanKuDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(requestBody))))))
	}

	impl.SampleDspBroadcastSonicJsonRequest(impl.dspId, dspSlot.Id, candidate, qkRequest)
	return req, nil

}

func (impl *QuanKuDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) quanku_broker_entity.Device {
	carrier, carrierName := impl.mappingCarrier(trafficData.GetOperatorType())

	deviceInfo := quanku_broker_entity.Device{
		Ua:            trafficData.GetUserAgent(),
		Type:          impl.mappingDeviceType(trafficData.GetDeviceType()),
		MacMd5:        trafficData.GetMd5Mac(),
		MacStMd5:      "",
		Mac:           trafficData.GetMac(),
		Model:         trafficData.GetModel(),
		Os:            impl.mappingOsType(trafficData.GetOsType()),
		Osv:           trafficData.GetOsVersion(),
		Vendor:        trafficData.GetBrand(),
		W:             int(trafficData.GetScreenWidth()),
		H:             int(trafficData.GetScreenHeight()),
		Ppi:           int(request.Device.PPI),
		Idfa:          trafficData.GetIdfa(),
		IdfaMd5:       trafficData.GetMd5Idfa(),
		Oaid:          trafficData.GetOaid(),
		Openudid:      trafficData.GetOpenUdid(),
		Imei:          trafficData.GetImei(),
		ImeiMd5:       trafficData.GetMd5Imei(),
		Anid:          trafficData.GetAndroidId(),
		AnidMd5:       trafficData.GetMd5AndroidId(),
		Aaid:          trafficData.GetAaid(),
		AaidMd5:       "",
		Caid:          device_utils.GetCaidRaw(trafficData.GetCaid()),
		CaidVer:       device_utils.GetCaidVersion(trafficData.GetCaid()),
		Paid:          request.Device.Paid,
		Carrier:       carrier,
		ConnType:      impl.mappingConnectionType(trafficData.GetConnectionType()),
		DeviceName:    request.Device.DeviceName,
		DeviceModel:   trafficData.GetModel(),
		StartupTime:   trafficData.GetBootMark(),
		MbTime:        trafficData.GetUpdateMark(),
		CountryCode:   "CN",
		CarrierName:   carrierName,
		MemSize:       request.Device.SystemTotalMem,
		DiskSize:      request.Device.SystemTotalDisk,
		LocalTimeZone: "28800",
		Language:      "zh-Hans-CN",
		PhoneNameMd5:  "",
	}

	if len(deviceInfo.DeviceName) == 0 {
		deviceInfo.DeviceName = trafficData.GetRomName()
	}

	if len(deviceInfo.DeviceName) == 0 {
		deviceInfo.DeviceName = trafficData.GetModel()
	}

	if len(trafficData.GetDeviceStartupTime()) > 0 {
		deviceInfo.StartupTime = trafficData.GetDeviceStartupTime()
	}

	if len(trafficData.GetDeviceUpgradeTime()) > 0 {
		deviceInfo.MbTime = trafficData.GetDeviceUpgradeTime()
	}

	if len(request.Device.DeviceName) > 0 {
		deviceInfo.PhoneNameMd5 = md5_utils.GetMd5String(request.Device.DeviceName)
	}

	if request.Device.IsIp6 {
		deviceInfo.Ipv6 = trafficData.GetRequestIp()
	} else {
		deviceInfo.Ip = trafficData.GetRequestIp()
	}

	return deviceInfo

}

func (impl *QuanKuDspBroker) getInstallAppIds(dspId utils.ID, appIds []int) []int {
	result := make([]int, 0)
	if len(appIds) == 0 {
		return result
	}

	dspIdStr := dspId.String()
	for _, appid := range appIds {
		key := dspIdStr + "_" + type_convert.GetAssertString(appid)
		externalMapping := impl.ExternalMappingLoader.GetDspAppMappingMapByKey(key)
		if externalMapping != nil {
			resultId := type_convert.GetAssertInt(externalMapping.SourceValue)
			if resultId != 0 {
				result = append(result, resultId)
			}
		}
	}

	return result
}

func (impl *QuanKuDspBroker) mappingCarrier(carrier entity.OperatorType) (string, string) {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return "46000", "中国移动"
	case entity.OperatorTypeChinaTelecom:
		return "46003", "中国电信"
	case entity.OperatorTypeTietong:
		return "46020", "中国铁通"
	case entity.OperatorTypeChinaUnicom:
		return "46001", "中国联通"
	default:
		return "", ""
	}
}

func (impl *QuanKuDspBroker) mappingDeviceType(s entity.DeviceType) int {
	switch s {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	default:
		return 1
	}
}

func (impl *QuanKuDspBroker) mappingOsType(os entity.OsType) int {
	switch os {
	case entity.OsTypeIOS:
		return 2
	case entity.OsTypeAndroid:
		return 1
	default:
		return 1
	}
}

func (impl *QuanKuDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionTypeCellular:
		return 3
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 0
	}
}

func (impl *QuanKuDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("QuanKuDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &quanku_broker_entity.BidResponse{}
	broadcastCandidate := broadcastCandidateList[0]

	resBody, err := impl.ParseSonicJsonHttpResponse(resp, data, response)
	//err = easyjson.Unmarshal(data, response)
	if err != nil {
		zap.L().Debug("QuanKuDspBroker.DecodeResponse json.Unmarshal,resp:, err:, dspslot", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))), zap.Error(err), zap.String("param3", fmt.Sprintf("%v", broadcastCandidate.GetDspSlotId())))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := json.Marshal(response)
		zap.L().Info("QuanKuDspBroker raw reponse1", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	if response.ErrCode != 0 || len(response.AdList) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	if response.AdList[0].LpType == 1 {
		return nil, err_code.ErrNotSupportGdt
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, resBid := range response.AdList {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = &entity.AppInfo{
			AppName: resBid.AppName,
		}

		if resBid.WxProgramId != "" && resBid.WxTargetPath != "" {
			candidateAd.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   resBid.WxProgramId,
				ProgramPath: resBid.WxTargetPath,
			}
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)

		candidateCreative := impl.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate.SetBidPrice(uint32(resBid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(resBid.ID)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break

	}

	return result, nil
}

func (impl *QuanKuDspBroker) ParseCreativeData(bid quanku_broker_entity.Ad) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.ID,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	item := bid

	if len(item.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         item.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Desc) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         item.Desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	for _, image := range item.Images {
		if len(image.URL) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          image.URL,
				Width:        int32(image.W),
				Height:       int32(image.H),
			}

			if image.Type == 1 || image.Type == 3 {
				material.MaterialType = entity.MaterialTypeIcon
			} else if image.Type == 2 {
				material.MaterialType = entity.MaterialTypeLogo
			}

			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if item.Video != nil && len(item.Video.URL) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          item.Video.URL,
			Width:        int32(item.Video.W),
			Height:       int32(item.Video.H),
			Duration:     float64(item.Duration),
		}
		creative.MaterialList = append(creative.MaterialList, material)

		if len(item.Video.CoverUrl) > 0 {
			material1 := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          item.Video.CoverUrl,
				Width:        int32(item.Video.W),
				Height:       int32(item.Video.H),
			}
			creative.MaterialList = append(creative.MaterialList, material1)
		}

	}

	return creative
}

func (impl *QuanKuDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.PlaintextEncode(uint64(chargePrice))
	//result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *QuanKuDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid quanku_broker_entity.Ad) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      make([]string, 0),
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkUrl:           bid.Deeplink,
		LandingUrl:            bid.Landpage,
	}
	//1 download 没有对应的下载六要素 不解析
	if bid.Act == 2 {
		tracking.LandingAction = entity.LandingTypeDeepLink
	}

	for _, track := range bid.Trackings {
		switch track.Event {
		case "start":
			tracking.ImpressionMonitorList = track.Urls
		case "click":
			tracking.ClickMonitorList = track.Urls
		case "download_start":
			tracking.AppDownloadStartedMonitorList = track.Urls
		case "download_end":
			tracking.AppDownloadFinishedMonitorList = track.Urls
		case "install_start":
			tracking.AppInstallStartMonitorList = track.Urls
		case "install_end":
			tracking.AppInstalledFinishMonitorList = track.Urls
		case "open", "active":
			tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, track.Urls...)
		}

	}

	return tracking

}
