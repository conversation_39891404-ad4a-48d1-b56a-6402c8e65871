package quanku_dsp_broker

import (
	"fmt"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type QuanKuSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height       int    `json:"height"`
	Width        int    `json:"width"`
	AppName      string `json:"app_name"`
	PkgName      string `json:"pkg_name"`
	AppVersion   string `json:"app_version"`
	MediaId      string `json:"media_id"`
	Token        string `json:"token"`
	Tid          string `json:"tid"`
	CreativeType int    `json:"creative_type"`
}

/*
JSON: {"dsp_slot_ext_attrs":[{"key":"token","label":"token","is_number":false,"multiple":false,"required":true,"comma_separated":false},{"key":"tid","label":"tid","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"app_name","label":"APP名","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"app_version","label":"APP版本","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"media_id","label":"媒体ID","is_number":false,"multiple":false,"required":true,"comma_separated":false},{"key":"pkg_name","label":"包名","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"width","label":"广告位宽","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"height","label":"广告位高","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"creative_type","label":"创意类型","is_number":false,"multiple":false,"required":false,"comma_separated":false}]}
PB: {"dsp_slot_ext_attrs":[{"key":"token","label":"token","is_number":false,"multiple":false,"required":true,"comma_separated":false},{"key":"tid","label":"tid","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"media_id","label":"媒体ID","is_number":false,"multiple":false,"required":true,"comma_separated":false},{"key":"app_name","label":"APP名","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"pkg_name","label":"包名","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"app_version","label":"APP版本","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"width","label":"广告位宽","is_number":true,"multiple":false,"required":false,"comma_separated":false},{"key":"height","label":"广告位高","is_number":true,"multiple":false,"required":false,"comma_separated":false}]}
*/
func (info *QuanKuSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.MediaId, err = dspSlotInfo.ExtraData.GetString("media_id")
	if err != nil {
		return fmt.Errorf("get media_id from extra_data failed, err: %v", err)
	}

	info.Token, err = dspSlotInfo.ExtraData.GetString("token")
	if err != nil {
		return fmt.Errorf("get token from extra_data failed, err: %v", err)
	}

	info.Tid, _ = dspSlotInfo.ExtraData.GetString("tid")

	creativeType, err := dspSlotInfo.ExtraData.GetString("creative_type")
	if err == nil {
		switch creativeType {
		case "img":
			info.CreativeType = 0
		case "video":
			info.CreativeType = 1
		}
	}

	return nil
}

type QuanKuDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*QuanKuSlotSlotInfo
}

func NewQuanKuDspSlotRegister(dspId utils.ID) *QuanKuDspSlotRegister {
	return &QuanKuDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*QuanKuSlotSlotInfo),
	}
}

func (r *QuanKuDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *QuanKuDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*QuanKuSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &QuanKuSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[QuanKuDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *QuanKuDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *QuanKuDspSlotRegister) GetSlotInfo(slotId utils.ID) *QuanKuSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
