package mango_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"fmt"
)

type MangoDspSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`

	AppId  string `json:"app_id"`
	AppKey string `json:"app_key"`
	AdType int    `json:"ad_type"` //如果DSP没有提供，默认填写8
}

func (m *MangoDspSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	m.DspSlotInfo = dspSlotInfo

	m.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	m.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	m.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	m.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	m.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")

	appId, err := dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return err
	}
	m.AppId = appId

	m.AppKey, err = dspSlotInfo.ExtraData.GetString("app_key")
	if err != nil {
		return err
	}
	m.AdType, err = dspSlotInfo.ExtraData.GetInt("ad_type")
	if err != nil {
		return err
	}
	return nil
}

type MangoDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*MangoDspSlotInfo
}

func NewMangoDspSlotRegister(dspId utils.ID) *MangoDspSlotRegister {
	return &MangoDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*MangoDspSlotInfo),
	}
}

func (m *MangoDspSlotRegister) GetDspId() utils.ID {
	return m.dspId
}

func (m *MangoDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	dspSlotMap := make(map[utils.ID]*MangoDspSlotInfo)
	for _, slotInfo := range list {
		slot := &MangoDspSlotInfo{}
		if err := slot.Init(slotInfo); err != nil {
			zap.L().Error("[MangoDspSlotRegister] init slot failed, slot:%d", zap.Error(err), zap.String("value", fmt.Sprintf("%v", slot.Id)))
			continue
		}

		dspSlotMap[slot.Id] = slot
	}

	m.dspSlotList = list
	m.dspSlotMap = dspSlotMap
	return nil
}

func (m *MangoDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return m.dspSlotList
}

func (m *MangoDspSlotRegister) GetSlotInfo(slotId utils.ID) *MangoDspSlotInfo {
	return m.dspSlotMap[slotId]
}
