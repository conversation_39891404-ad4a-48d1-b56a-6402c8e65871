package mango_dsp_broker

import (
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/mango_dsp_broker/mango_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"io"
	"net/http"
	netUrl "net/url"
	"strconv"
	"time"
)

type MangoDspBroker struct {
	dsp_broker.DspBrokerBase
	dspSlotRegister *MangoDspSlotRegister
	log             *zap.Logger
	macroInfo       macro_builder.MonitorMacroInfo
}

func NewMangoDspBroker(dspId utils.ID) *MangoDspBroker {
	return &MangoDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		dspSlotRegister: NewMangoDspSlotRegister(dspId),
		log:             zap.L().With(zap.String("broker", "MangoDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__PRICE__",
			MacroClickDownX: "__clickDownX__",
			MacroClickDownY: "__clickDownY__",
			MacroClickUpX:   "__clickUpX__",
			MacroClickUpY:   "__clickUpY__",
		},
	}
}

func (m *MangoDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := m.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(dspSlot.Id)
	candidate.SetDspSlotKey(slotId)

	bidRequest := mango_broker_entity.AdRequest{
		SDKVersion:      "1.0",
		ProtocolVersion: "4.0",
		AdType:          dspSlot.AdType,
		AppInfo: mango_broker_entity.AppInfo{
			AppID:       dspSlot.AppId,
			Name:        trafficData.GetAppName(),
			PackageName: trafficData.GetAppBundle(),
			Version:     trafficData.GetAppVersion(),
			VersionCode: trafficData.GetAppVersion(),
		},
		ImpInfo: nil,
		DeviceInfo: mango_broker_entity.DeviceInfo{
			IDFA:                 trafficData.GetIdfa(),
			IDFV:                 trafficData.GetIdfv(),
			IMEI:                 trafficData.GetImei(),
			IMEIMd5:              trafficData.GetMd5Imei(),
			OAID:                 trafficData.GetOaid(),
			OAIDMd5:              trafficData.GetMd5Oaid(),
			OSType:               mappingOsType(trafficData.GetOsType()),
			OSV:                  trafficData.GetOsVersion(),
			OSC:                  mappingAndroidApiVersion(trafficData),
			RomVersion:           trafficData.GetRomVersion(),
			ScreenType:           int(trafficData.GetScreenOrientation()),
			DPI:                  strconv.Itoa(int(request.Device.DPI)),
			DIP:                  strconv.Itoa(int(trafficData.GetScreenDensity())),
			SerialNo:             trafficData.GetImei(),
			Manufacture:          trafficData.GetBrand(),
			Model:                trafficData.GetModel(),
			Brand:                trafficData.GetBrand(),
			DeviceType:           mappingDeviceType(trafficData.GetDeviceType()),
			IMSI:                 request.Device.Imsi,
			Language:             trafficData.GetLanguage(),
			ScreenWidth:          int(trafficData.GetScreenWidth()),
			ScreenHeight:         int(trafficData.GetScreenHeight()),
			AndroidID:            trafficData.GetAndroidId(),
			AndroidIDMd5:         trafficData.GetMd5AndroidId(),
			AppStoreVersion:      request.Device.AppStoreVersion,
			BootMark:             trafficData.GetBootMark(),
			UpdateMark:           trafficData.GetUpdateMark(),
			Paid:                 request.Device.Paid,
			DeviceNameMd5:        md5_utils.GetMd5String(request.Device.DeviceName),
			PhysicalMemoryKBytes: request.Device.SystemTotalMem,
			HardDiskSizeKBytes:   request.Device.SystemTotalDisk,
			Country:              request.Device.CountryCode,
			TimeZone:             "GMT+0800",
			CAID:                 device_utils.GetCaidRaw(trafficData.GetCaid()),
			CAIDVer:              device_utils.GetCaidVersion(trafficData.GetCaid()),
			CAIDs:                make([]mango_broker_entity.CaidInfo, 0),
			AAID:                 trafficData.GetAaid(),
			OSUpdateTime:         trafficData.GetDeviceUpgradeTime(),
			OSBirthTime:          trafficData.GetDeviceInitTime(),
			OSBootTime:           trafficData.GetDeviceStartupTime(),
		},
		NetworkInfo: mango_broker_entity.NetworkInfo{
			MAC:            trafficData.GetMac(),
			MACMd5:         trafficData.GetMd5Mac(),
			ConnectionType: mappingConnectionType(trafficData.GetConnectionType()),
			UserAgent:      trafficData.GetUserAgent(),
			Reffer:         trafficData.GetReferer(),
		},
		GeoInfo: &mango_broker_entity.GeoInfo{
			CoordinateType: mappingCoordinateType(request.Device.GpsType),
			Latitude:       strconv.FormatFloat(trafficData.GetGeoLatitude(), 'f', -1, 64),
			Longitude:      strconv.FormatFloat(trafficData.GetGeoLongitude(), 'f', -1, 64),
		},
		UserInfo: &mango_broker_entity.UserInfo{
			Age:    int(request.UserAge),
			Gender: mappingGender(request.UserGender),
		},
	}
	if len(dspSlot.PkgName) > 0 {
		bidRequest.AppInfo.PackageName = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidRequest.AppInfo.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidRequest.AppInfo.Version = dspSlot.AppVersion
		bidRequest.AppInfo.VersionCode = dspSlot.AppVersion
	}
	if request.UseHttps {
		bidRequest.HTTPSRequired = 1
	}
	if request.Device.IsIp6 {
		bidRequest.NetworkInfo.IP6 = trafficData.GetRequestIp()
	} else {
		bidRequest.NetworkInfo.IP = trafficData.GetRequestIp()
	}
	bidRequest.NetworkInfo.OperatorType, bidRequest.NetworkInfo.MNO = mappingOperator(trafficData.GetOperatorType())
	for _, item := range trafficData.GetCaids() {
		if len(item) <= 0 {
			continue
		}
		caid := device_utils.GetCaidRaw(item)
		caidVersion := device_utils.GetCaidVersion(item)
		if len(bidRequest.DeviceInfo.CAID) <= 0 {
			bidRequest.DeviceInfo.CAID = caid
			bidRequest.DeviceInfo.CAIDVer = caidVersion
		}
		bidRequest.DeviceInfo.CAIDs = append(bidRequest.DeviceInfo.CAIDs, mango_broker_entity.CaidInfo{
			CAID:    caid,
			Version: caidVersion,
		})
	}
	for _, app := range request.App.InstalledApp {
		bidRequest.DeviceInfo.AppPackageName = append(bidRequest.DeviceInfo.AppPackageName, mango_broker_entity.AppPackage{
			PkgName: app,
		})
	}

	imp, err := m.makeImp(request, candidate, trafficData)
	if err != nil {
		return nil, err
	}
	bidRequest.ImpInfo = []mango_broker_entity.ImpInfo{*imp}

	httpReq, _, err := m.BuildSonicJsonHttpRequest(bidRequest)
	if err != nil {
		m.log.WithError(err).Error("BuildSonicJsonHttpRequest error")
		return nil, err
	}

	timestamp := time.Now().UnixMilli()
	ts := strconv.FormatInt(timestamp, 10)
	param := "?appId=%s&timeStamp=%s&apiToken=%s"
	param = fmt.Sprintf(param, dspSlot.AppId, ts, md5_utils.GetMd5String(dspSlot.AppId+dspSlot.AppKey+ts))
	parseUrl, err := netUrl.Parse(m.GetBidUrl() + param)
	if err != nil {
		m.log.WithError(err).WithField("param", param).Error("parseUrl error")
		return nil, err
	}
	httpReq.URL = parseUrl

	if request.IsDebug {
		marshal, _ := sonic.Marshal(bidRequest)
		m.log.WithField("request", string(marshal)).WithField("bidUrl", parseUrl.RequestURI()).Info("debug request")
	}

	m.SampleDspBroadcastSonicJsonRequest(m.GetDspId(), dspSlot.Id, candidate, bidRequest)
	return httpReq, nil
}

func mappingCoordinateType(gpsType entity.GpsType) int {
	switch gpsType {
	case entity.GpsTypeWSG84:
		return 1
	case entity.GpsTypeGCJ02:
		return 2
	case entity.GpsTypeBd09:
		return 3
	default:
		return 1
	}
}

func (m *MangoDspBroker) makeImp(request *ad_service.AdRequest,
	candidate *ad_service.AdCandidate, trafficData ad_service_entity.TrafficData) (*mango_broker_entity.ImpInfo, error) {
	dspSlot := m.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	posId, err := strconv.ParseInt(candidate.GetDspSlotKey(), 10, 64)
	if err != nil {
		return nil, errors.New("invalid dsp slot key")
	}

	w, h := uint32(dspSlot.Width), uint32(dspSlot.Height)
	if w == 0 || h == 0 {
		w, h = trafficData.GetSlotWidth(), trafficData.GetSlotHeight()
	}
	if (w == 0 || h == 0) && len(request.SlotSize) > 0 {
		w, h = uint32(request.SlotSize[0].Width), uint32(request.SlotSize[0].Height)
	}
	if w == 0 || h == 0 {
		w, h = 320, 1080
	}

	return &mango_broker_entity.ImpInfo{
		AdNum:    1,
		PosID:    int(posId),
		Width:    w,
		Height:   h,
		BidPrice: int(trafficData.GetBidPrice().Price),
	}, nil
}

func (m *MangoDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if response.StatusCode != 200 {
		m.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			strconv.Itoa(response.StatusCode), strconv.Itoa(response.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil || len(data) == 0 {
		m.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			err_code.ErrBrokerResponse.CodeStr(), "read response body err")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	bidResponse := &mango_broker_entity.AdResponse{}
	_, err = m.ParseSonicJsonHttpResponse(response, data, bidResponse)
	if err != nil {
		m.log.WithError(err).Error("ParseSonicJsonHttpResponse error")
		m.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			err_code.ErrBrokerResponse.CodeStr(), "ParseSonicJsonHttpResponse err")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	if request.IsDebug {
		resBody, _ := sonic.Marshal(bidResponse)
		m.log.Infof("ParseResponse, body:%s", resBody)
	}

	if bidResponse.Code != 0 {
		return nil, err_code.ErrBroadcastNoBidding.Wrap(errors.New(bidResponse.Msg))
	}

	m.SampleDspBroadcastResponse(m.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate,
		response.StatusCode, data)
	m.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
		strconv.Itoa(response.StatusCode), "")

	list := ad_service.DspAdCandidateList{}
	for _, seatBid := range bidResponse.Data {
		if seatBid == nil {
			continue
		}
		for _, bid := range seatBid.AdInfo {
			if bid == nil {
				continue
			}

			creative := m.buildCreative(bid)
			if creative == nil {
				return nil, err_code.ErrCreativeNotFound
			}

			ad := &entity.Ad{
				DspId:         m.GetDspId(),
				DspSlotId:     broadcastCandidate.GetDspSlotId(),
				DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
				AdMonitorInfo: m.buildMonitor(bid),
			}

			if len(bid.AdBaseInfo.AppPackageName) > 0 {
				ad.AppInfo = &entity.AppInfo{
					PackageName: bid.AdBaseInfo.AppPackageName,
					AppName:     bid.AdBaseInfo.AppName,
					Icon:        bid.AdBaseInfo.AppIconUrl,
					AppVersion:  bid.AdBaseInfo.AppVersion,
					PackageSize: int(bid.AdBaseInfo.AppSize / 1024),
					Privacy:     bid.AdBaseInfo.AppPrivacyUrl,
					Permission:  bid.AdBaseInfo.AppPermissionInfoUrl,
					AppDesc:     bid.AdBaseInfo.IntroductionInfo,
					Develop:     bid.AdBaseInfo.CorporationName,
				}
				if len(bid.AdBaseInfo.PermissionInfo) > 0 {
					ad.AppInfo.PermissionDesc = append(ad.AppInfo.PermissionDesc,
						entity.PermissionDesc{PermissionDesc: bid.AdBaseInfo.PermissionInfo})
				}
			}

			adCandidate := ad_service.NewDspAdCandidateWithPool(ad)
			adCandidate.SetCreative(creative)
			adCandidate.SetBidPrice(uint32(bid.AdBaseInfo.Price))
			adCandidate.SetBidType(entity.BidTypeCpm)
			adCandidate.SetDspProtocol(m.GetDspProtocol())
			adCandidate.SetDspAdID(strconv.Itoa(int(bid.AdBaseInfo.AdId)))

			list = append(list, adCandidate)
		}
	}
	return list, nil
}

func (m *MangoDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return m.dspSlotRegister
}

func (m *MangoDspBroker) buildCreative(bid *mango_broker_entity.AdInfoDetail) *entity.Creative {
	creative := &entity.Creative{
		CreativeKey: strconv.Itoa(bid.AdBaseInfo.CreativeID),
	}

	title := &entity.Material{
		MaterialType: entity.MaterialTypeTitle,
		Data:         bid.AdBaseInfo.AdTitle,
	}
	if len(title.Data) == 0 {
		title.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, title)

	desc := &entity.Material{
		MaterialType: entity.MaterialTypeDesc,
		Data:         bid.AdBaseInfo.AdDescription,
	}
	if len(desc.Data) == 0 {
		desc.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, desc)

	for _, feature := range bid.AdMaterialInfo.MaterialFeature {
		if feature.FeatureType == 1 {
			video := &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          feature.MaterialUrl,
				Duration:     float64(feature.VideoDuration),
			}
			if feature.MaterialSize != nil {
				video.Width = int32(feature.MaterialSize.Width)
				video.Height = int32(feature.MaterialSize.Height)
			}
			creative.MaterialList = append(creative.MaterialList, video)

			if len(feature.CoverUrl) > 0 {
				img := &entity.Material{
					MaterialType: entity.MaterialTypeCoverImage,
					Url:          feature.CoverUrl,
				}
				if feature.MaterialSize != nil {
					img.Width = int32(feature.MaterialSize.Width)
					img.Height = int32(feature.MaterialSize.Height)
				}
				creative.MaterialList = append(creative.MaterialList, img)
			}
		} else if feature.FeatureType == 2 {
			img := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          feature.MaterialUrl,
			}
			if len(img.Url) <= 0 {
				img.Url = feature.CoverUrl
				img.MaterialType = entity.MaterialTypeCoverImage
			}
			if feature.MaterialSize != nil {
				img.Width = int32(feature.MaterialSize.Width)
				img.Height = int32(feature.MaterialSize.Height)
			}
			creative.MaterialList = append(creative.MaterialList, img)
		}
	}

	return creative
}

func (m *MangoDspBroker) buildMonitor(bid *mango_broker_entity.AdInfoDetail) *entity.AdMonitorInfo {
	monitor := &entity.AdMonitorInfo{
		LandingUrl:  bid.AdConversionInfo.H5Url,
		DeepLinkUrl: bid.AdConversionInfo.DeeplinkUrl,
		DownloadUrl: bid.AdConversionInfo.AppDownloadUrl,
	}
	if len(bid.AdConversionInfo.DeeplinkUrl) > 0 || len(bid.AdConversionInfo.UniversalLink) > 0 {
		monitor.LandingAction = entity.LandingTypeDeepLink
	} else if bid.AdBaseInfo.AdOperationType == 1 {
		monitor.LandingAction = entity.LandingTypeDownload
	} else if bid.AdBaseInfo.AdOperationType == 2 {
		monitor.LandingAction = entity.LandingTypeInWebView
	}
	if len(monitor.DownloadUrl) > 0 {
		monitor.LandingUrl = monitor.DownloadUrl
	}
	if len(bid.AdConversionInfo.MarketUrl) > 0 {
		monitor.LandingUrl = bid.AdConversionInfo.MarketUrl
	}
	if len(bid.AdConversionInfo.UniversalLink) > 0 {
		monitor.DeepLinkUrl = bid.AdConversionInfo.UniversalLink
	}

	for _, url := range bid.AdBaseInfo.ConvUrl.WinNoticeArray {
		if len(url) > 0 {
			monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, url)
		}
	}
	for _, url := range bid.AdBaseInfo.ConvUrl.Show {
		if len(url) > 0 {
			monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, url)
		}
	}
	for _, url := range bid.AdBaseInfo.ConvUrl.Click {
		if len(url) > 0 {
			monitor.ClickMonitorList = append(monitor.ClickMonitorList, url)
		}
	}
	for _, url := range bid.AdBaseInfo.ConvUrl.StartDown {
		if len(url) > 0 {
			monitor.AppDownloadStartedMonitorList = append(monitor.AppDownloadStartedMonitorList, url)
		}
	}
	for _, url := range bid.AdBaseInfo.ConvUrl.EndDown {
		if len(url) > 0 {
			monitor.AppDownloadFinishedMonitorList = append(monitor.AppDownloadFinishedMonitorList, url)
		}
	}
	for _, url := range bid.AdBaseInfo.ConvUrl.StartInstall {
		if len(url) > 0 {
			monitor.AppInstallStartMonitorList = append(monitor.AppInstallStartMonitorList, url)
		}
	}
	for _, url := range bid.AdBaseInfo.ConvUrl.EndInstall {
		if len(url) > 0 {
			monitor.AppInstalledFinishMonitorList = append(monitor.AppInstalledFinishMonitorList, url)
		}
	}
	for _, url := range bid.AdBaseInfo.ConvUrl.StartPlay {
		if len(url) > 0 {
			monitor.VideoStartUrlList = append(monitor.VideoStartUrlList, url)
		}
	}
	for _, url := range bid.AdBaseInfo.ConvUrl.EndPlay {
		if len(url) > 0 {
			monitor.VideoCloseUrlList = append(monitor.VideoCloseUrlList, url)
		}
	}
	for _, url := range bid.AdBaseInfo.ConvUrl.Activation {
		if len(url) > 0 {
			monitor.AppOpenMonitorList = append(monitor.AppOpenMonitorList, url)
		}
	}
	for _, url := range bid.AdBaseInfo.ConvUrl.DeeplinkClick {
		if len(url) > 0 {
			monitor.DeepLinkMonitorList = append(monitor.DeepLinkMonitorList, url)
		}
	}

	if len(monitor.ImpressionMonitorList) > 0 {
		monitor.ImpressionMonitorList = m.macroInfo.MacroReplaceList(monitor.ImpressionMonitorList)
	}
	if len(monitor.ClickMonitorList) > 0 {
		monitor.ClickMonitorList = m.macroInfo.MacroReplaceList(monitor.ClickMonitorList)
	}
	if len(monitor.DeepLinkMonitorList) > 0 {
		monitor.DeepLinkMonitorList = m.macroInfo.MacroReplaceList(monitor.DeepLinkMonitorList)
	}

	return monitor
}

func mappingAndroidApiVersion(trafficData ad_service_entity.TrafficData) string {
	if trafficData.GetOsType() != entity.OsTypeAndroid {
		return trafficData.GetOsVersion()
	}

	switch trafficData.GetOsVersion() {
	case "16":
		return "36"
	case "15":
		return "35"
	case "14":
		return "34"
	case "13":
		return "33"
	case "12.1":
		return "32"
	case "12":
		return "31"
	case "11":
		return "30"
	case "10":
		return "29"
	case "9":
		return "28"
	case "8.1":
		return "27"
	case "8":
		return "26"
	case "7.1":
		return "25"
	case "7":
		return "24"
	case "6":
		return "23"
	case "5.1":
		return "22"
	case "5":
		return "21"
	case "4.4":
		return "19"
	case "4.3":
		return "18"
	case "4.2":
		return "17"
	case "4.1":
		return "16"
	case "4.0":
		return "14"
	case "3.2":
		return "13"
	case "3.1":
		return "12"
	case "3":
		return "11"
	case "2.3":
		return "9"
	case "2.2":
		return "8"
	case "2.1":
		return "7"
	case "2":
		return "5"
	case "1.6":
		return "4"
	case "1.5":
		return "3"
	case "1.1":
		return "2"
	case "1":
		return "1"
	default:
		return "0"
	}
}

func mappingDeviceType(deviceType entity.DeviceType) int {
	switch deviceType {
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypeOtt:
		return 4
	default:
		return 0
	}
}

func mappingOsType(osType entity.OsType) int {
	switch osType {
	case entity.OsTypeAndroid:
		return 1
	case entity.OsTypeIOS:
		return 2
	default:
		return 0
	}
}

func mappingOperator(operatorType entity.OperatorType) (int, string) {
	switch operatorType {
	case entity.OperatorTypeChinaMobile:
		return 1, "46000"
	case entity.OperatorTypeChinaTelecom:
		return 2, "46001"
	case entity.OperatorTypeChinaUnicom:
		return 3, "46003"
	case entity.OperatorTypeTietong:
		return 99, "46020"
	default:
		return 0, "00000"
	}
}

func mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeCellular:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	case entity.ConnectionTypeWifi:
		return 100
	default:
		return 0
	}
}

func mappingGender(gender entity.UserGenderType) string {
	switch gender {
	case entity.UserGenderMan:
		return "M"
	case entity.UserGenderWoman:
		return "F"
	default:
		return ""
	}
}
