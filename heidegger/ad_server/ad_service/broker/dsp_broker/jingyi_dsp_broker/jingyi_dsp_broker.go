package jingyi_dsp_broker

import (
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jingyi_dsp_broker/jingyi_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"math/rand"
	"net/http"
	"fmt"
)

type JingYiDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *JingYiDspSlotRegister
}

func NewJingYiDspBroker(dspId utils.ID) *JingYiDspBroker {
	return &JingYiDspBroker{
		slotRegister: NewJingYiDspSlotRegister(dspId),
	}
}

func (impl *JingYiDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *JingYiDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("JingYiDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("JingYiDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))

		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("JingYiDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}
	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	if len(trafficData.GetMac()) == 0 {
		return nil, err_code.ErrInvalidDeviceId
	}

	jyRequest := &jingyi_broker_entity.JingYiRequest{
		Device: jingyi_broker_entity.Device{
			IP:         trafficData.GetRequestIp(),
			UA:         trafficData.GetUserAgent(),
			Mac:        trafficData.GetMac(),
			IPdx:       "",
			RegionCode: "",
		},
		User: jingyi_broker_entity.User{
			Age:      impl.mappingAge(request.UserAge),
			FamStr:   []string{"0"},
			FamCsump: []string{"0"},
			FamInt:   []string{"0"},
		},
		AppKey:  dspSlot.AppKey,
		TraceId: request.GetRequestId(),
		Imp:     make([]jingyi_broker_entity.Imp, 0),
	}

	vtype := impl.getVtype(dspSlot.VType)
	if len(vtype) > 0 {
		jyRequest.App = &jingyi_broker_entity.App{VType: vtype}
	}

	if request.UserGender == entity.UserGenderMan {
		jyRequest.User.Gender = append(jyRequest.User.Gender, "1")
	} else if request.UserGender == entity.UserGenderWoman {
		jyRequest.User.Gender = append(jyRequest.User.Gender, "2")
	} else {
		jyRequest.User.Gender = append(jyRequest.User.Gender, "0")
	}

	jyImp := jingyi_broker_entity.Imp{
		AdSpaceId: type_convert.GetAssertInt(slotId),
		Bid:       request.ImpressionId,
	}

	jyRequest.Imp = append(jyRequest.Imp, jyImp)

	req, _, err := impl.BuildSonicJsonHttpRequest(jyRequest)
	if err != nil {
		zap.L().Error("JingYiDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}

	if request.IsDebug {
		requestBody, _ := sonic.Marshal(jyRequest)
		zap.L().Info("JingYiDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(requestBody))))))
	}

	impl.SampleDspBroadcastSonicJsonRequest(impl.GetDspId(), dspSlot.Id, candidate, jyRequest)
	return req, nil

}

func (impl *JingYiDspBroker) getVtype(vtypes []string) string {
	if len(vtypes) == 0 {
		return ""
	}

	randI := rand.Intn(len(vtypes)) % len(vtypes)

	return vtypes[randI]
}

func (impl *JingYiDspBroker) mappingAge(age int32) []string {
	result := make([]string, 0)

	if age > 0 && age <= 17 {
		result = append(result, "1")
	} else if age > 17 && age <= 24 {
		result = append(result, "2")
	} else if age > 24 && age <= 34 {
		result = append(result, "3")
	} else if age > 34 && age <= 44 {
		result = append(result, "4")
	} else if age > 44 && age <= 54 {
		result = append(result, "5")
	} else if age > 54 && age <= 59 {
		result = append(result, "6")
	} else if age > 59 {
		result = append(result, "7")
	} else {
		result = append(result, "0")
	}

	return result
}

func (impl *JingYiDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("JingYiDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &jingyi_broker_entity.JingYiResponse{}

	resBody, err := impl.ParseSonicJsonHttpResponse(resp, data, response)
	if err != nil {
		zap.L().Error("JingYiDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := sonic.Marshal(response)
		zap.L().Info("JingYiDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	if response.Code != 200 || len(response.Data.DataList) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, resBid := range response.Data.DataList {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		tracking := &entity.AdMonitorInfo{
			ImpressionMonitorList: make([]string, 0),
			ClickMonitorList:      make([]string, 0),
			LandingAction:         entity.LandingTypeInWebView,
		}

		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, resBid.StartImp)
		tracking.ClickMonitorList = append(tracking.ClickMonitorList, resBid.ClickItem)
		if len(resBid.OverImp) > 0 {
			tracking.VideoCloseUrlList = append(tracking.VideoStartUrlList, resBid.OverImp)
		}

		if len(resBid.MiddleImp) > 0 {
			delay := type_convert.GetAssertInt(resBid.Duration) / 2
			if delay == 0 {
				delay = 1
			}

			tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
				Url:   resBid.MiddleImp,
				Delay: delay,
			})
		}

		candidateAd.AdMonitorInfo = tracking

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)

		candidate.SetBidPrice(uint32(100))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break

	}
	return result, nil
}

func (impl *JingYiDspBroker) chargePriceEncoder(chargePrice uint32) string {
	return ""
}
