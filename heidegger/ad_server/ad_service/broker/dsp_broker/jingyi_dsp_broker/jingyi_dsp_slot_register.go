package jingyi_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"strings"
)

type JingYiSlotSlotInfo struct {
	*entity.DspSlotInfo
	AppKey string   `json:"app_key"`
	VType  []string `json:"v_type"`
}

func (info *JingYiSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.AppKey, err = dspSlotInfo.ExtraData.GetString("app_key")
	if err != nil {
		return fmt.Errorf("get app_key from extra_data failed, err: %v", err)
	}
	//1:电影 2:电视剧 3:⼉儿童 4:综艺 5:体育 6:教育 7:纪录⽚ 8:生活 9:动漫
	vType, err := dspSlotInfo.ExtraData.GetString("v_type")
	if err != nil {
	} else {
		vTypeArr := strings.Split(vType, ",")
		for _, item := range vTypeArr {
			if len(item) > 0 {
				info.VType = append(info.VType, item)
			}
		}

	}

	return nil
}

type JingYiDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*JingYiSlotSlotInfo
}

func NewJingYiDspSlotRegister(dspId utils.ID) *JingYiDspSlotRegister {
	return &JingYiDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*JingYiSlotSlotInfo),
	}
}

func (r *JingYiDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *JingYiDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*JingYiSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &JingYiSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[JingYiDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *JingYiDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *JingYiDspSlotRegister) GetSlotInfo(slotId utils.ID) *JingYiSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
