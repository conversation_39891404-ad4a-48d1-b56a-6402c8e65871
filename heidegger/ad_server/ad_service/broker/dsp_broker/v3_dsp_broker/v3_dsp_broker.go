package v3_dsp_broker

import (
	"bytes"
	"encoding/json"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/ad_server/ad_service/traffic_modifier"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"io"
	"net/http"
	"fmt"
)

type V3DspBroker struct {
	dsp_broker.DspBrokerBase

	dspSlotRegister *dsp_slot_register.SimpleDspSlotRegister

	bidUrl string
	dspId  utils.ID
}

func NewV3DspBroker(dspId utils.ID) *V3DspBroker {
	return &V3DspBroker{
		dspId:           dspId,
		dspSlotRegister: dsp_slot_register.NewSimpleDspSlotRegister(dspId),
	}
}

func (b *V3DspBroker) GetDspId() utils.ID {
	return b.dspId
}

func (b *V3DspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.dspSlotRegister
}

func (b *V3DspBroker) UpdateDspInfo(dsp *entity.Dsp) error {
	b.bidUrl = dsp.BidUrl
	return nil
}

func (b *V3DspBroker) BuildRequest(adRequest *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := b.dspSlotRegister.GetDspSlotById(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	deviceId, _ := trafficData.GetDeviceIdWithType()
	brokerRequest := V3BrokerRequest{
		RequestId:    trafficData.GetRequestId(),
		TagId:        dspSlot.GetDspSlotIdByTrafficContext(trafficData),
		IP:           trafficData.GetRequestIp(),
		UA:           trafficData.GetUserAgent(),
		Cid:          deviceId,
		Imei:         trafficData.GetImei(),
		Oaid:         trafficData.GetOaid(),
		Idfa:         trafficData.GetIdfa(),
		Caid:         trafficData.GetCaid(),
		AliAaid:      trafficData.GetAaid(),
		Mac:          trafficData.GetMac(),
		AndroidId:    trafficData.GetAndroidId(),
		Md5Imei:      trafficData.GetMd5Imei(),
		Md5Idfa:      trafficData.GetMd5Idfa(),
		Md5Oaid:      trafficData.GetMd5Oaid(),
		Md5AndroidId: trafficData.GetMd5AndroidId(),
		Brand:        trafficData.GetBrand(),
		Model:        trafficData.GetModel(),
		Os:           int(trafficData.GetOsType()),
		DeviceType:   int(trafficData.GetDeviceType()),
		Network:      int(trafficData.GetConnectionType()),
		Carrier:      int(trafficData.GetOperatorType()),
		BootMark:     trafficData.GetBootMark(),
		UpdateMark:   trafficData.GetUpdateMark(),
		AppBundle:    trafficData.GetAppBundle(),
		AppName:      trafficData.GetAppName(),
	}

	data, err := json.Marshal(brokerRequest)
	if err != nil {
		return nil, err
	}

	url := b.bidUrl

	request, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer(data))
	if err != nil {
		return nil, err
	}
	request.Header["Content-Type"] = []string{"application/json"}

	zap.L().Info("optBroker url:, send", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", b.bidUrl)))), zap.String("param2", fmt.Sprintf("%v", string(data))))

	return request, nil
}

func (b *V3DspBroker) ParseResponse(adRequest *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	zap.L().Info("optBroker response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(data))))))

	response := V3BrokerResponse{}
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	broadcastCandidate := broadcastCandidateList[0]
	b.SampleDspBroadcastResponse(b.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, data)

	candidateList := make(ad_service.DspAdCandidateList, 0, len(response.Ads))

	for _, ad := range response.Ads {
		adMonitorInfo := &entity.AdMonitorInfo{
			LandingUrl:            ad.LandingUrl,
			LandingStayTime:       ad.LandStayTime,
			DeepLinkUrl:           ad.DeepLink,
			ClickMonitorList:      ad.ClickMonitors,
			ClickDelay:            ad.ClickDelay,
			ImpressionMonitorList: ad.ImpMonitors,
			DeepLinkMonitorList:   ad.DeepLinkMonitor,
		}

		for _, delay := range ad.DelayMonitor {
			adMonitorInfo.AddDelayMonitorUrl(delay.Delay, delay.Url)
		}

		candidateAd := &entity.Ad{
			DspId:         b.GetDspId(),
			DspSlotId:     broadcastCandidate.GetDspSlotId(),
			DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
			AdMonitorInfo: adMonitorInfo,
		}

		modifier := traffic_modifier.NewTrafficModifier()
		modifier.SetUserAgent(ad.UserAgent)
		modifier.SetReferer(ad.Referer)
		modifier.SetShouldClick(ad.DoClick == 1)
		modifier.SetShouldLand(ad.DoLand == 1)

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetDspProtocol(b.GetDspProtocol())
		candidateList = append(candidateList, candidate)
	}

	return candidateList, nil
}
