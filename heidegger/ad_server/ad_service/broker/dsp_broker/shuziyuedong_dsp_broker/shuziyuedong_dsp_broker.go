package shuziyuedong_dsp_broker

import (
	"io"
	"net/http"
	"strconv"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	shuziyuedong_broker_entity_pb "gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/shuziyuedong_dsp_broker/shuziyuedong_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"fmt"
)

type ShuZiYueDongDspBroker struct {
	dsp_broker.DspBrokerBase
	dspSlotRegister *ShuZiYueDongDspSlotRegister
	log             *zap.Logger
	macroInfo       macro_builder.MonitorMacroInfo
}

func NewShuZiYueDongDspBroker(dspId utils.ID) *ShuZiYueDongDspBroker {
	return &ShuZiYueDongDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		dspSlotRegister: NewShuZiYueDongDspSlotRegister(dspId),
		log:             zap.L().With(zap.String("broker", "ShuZiYueDongBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "_PRICE_",
			MacroClickDownX: "_SBZMX_",
			MacroClickDownY: "_SBZMY_",
			MacroClickUpX:   "_SBZCX_",
			MacroClickUpY:   "_SBZCY_",
		},
	}
}

func (s *ShuZiYueDongDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := s.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(dspSlot.Id)
	candidate.SetDspSlotKey(slotId)

	bidRequest := &shuziyuedong_broker_entity_pb.Request{
		App: &shuziyuedong_broker_entity_pb.App{
			Bundle: trafficData.GetAppBundle(),
			Name:   trafficData.GetAppName(),
			Ver:    trafficData.GetAppVersion(),
		},
		Device: &shuziyuedong_broker_entity_pb.Device{
			Carrier:        mappingCarrierType(trafficData.GetOperatorType()),
			Connectiontype: mappingConnectionType(trafficData.GetConnectionType()),
			Devicetype:     mappingDeviceType(trafficData.GetDeviceType()),
			Did:            trafficData.GetImei(),
			Didmd5:         trafficData.GetMd5Imei(),
			Ifa:            trafficData.GetIdfa(),
			Ifamd5:         trafficData.GetMd5Idfa(),
			Dpid:           trafficData.GetAndroidId(),
			Dpidmd5:        trafficData.GetMd5AndroidId(),
			H:              trafficData.GetScreenHeight(),
			W:              trafficData.GetScreenWidth(),
			Ip:             trafficData.GetRequestIp(),
			Oid:            trafficData.GetOaid(),
			Oidmd5:         trafficData.GetMd5Oaid(),
			Make:           trafficData.GetBrand(),
			Model:          trafficData.GetModel(),
			Os:             mappingOsType(trafficData.GetOsType()),
			Osv:            trafficData.GetOsVersion(),
			Ppi:            trafficData.GetScreenDensity(),
			Ua:             trafficData.GetUserAgent(),
			Mac:            trafficData.GetMac(),
			Macmd5:         trafficData.GetMd5Mac(),
			RomVersion:     trafficData.GetRomVersion(),
			UpdateMark:     trafficData.GetUpdateMark(),
			UpdateTime:     trafficData.GetDeviceUpgradeTime(),
			BootMark:       trafficData.GetBootMark(),
			BootTime:       trafficData.GetDeviceStartupTime(),
			BirthTime:      trafficData.GetDeviceInitTime(),
			Paid_1_4:       request.Device.Paid,
			HmsVer:         request.Device.VercodeHms,
			HwagVer:        request.Device.VercodeAg,
			Caid:           device_utils.GetCaidRaw(trafficData.GetCaid()),
			CaidMd5:        trafficData.GetMd5CaidRaw(),
			CaidVersion:    device_utils.GetCaidVersion(trafficData.GetCaid()),
			DeviceNameMd5:  md5_utils.GetMd5String(request.Device.DeviceName),
			Language:       trafficData.GetLanguage(),
			Geo: &shuziyuedong_broker_entity_pb.Geo{
				Lat: float32(trafficData.GetGeoLatitude()),
				Lon: float32(trafficData.GetGeoLongitude()),
			},
		},
		Id: trafficData.GetRequestId(),
		User: &shuziyuedong_broker_entity_pb.User{
			Id:     request.UserId,
			Gender: mappingUserGender(request.UserGender),
		},
		Token: dspSlot.Token,
	}
	if len(dspSlot.PkgName) > 0 {
		bidRequest.App.Bundle = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidRequest.App.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidRequest.App.Ver = dspSlot.AppVersion
	}
	if request.Device.IsIp6 {
		bidRequest.Device.Ipv6 = trafficData.GetRequestIp()
	}

	imp, err := s.makeImp(request, candidate, trafficData)
	if err != nil {
		return nil, err
	}
	bidRequest.Imp = []*shuziyuedong_broker_entity_pb.Imp{imp}

	if request.IsDebug {
		marshal, _ := sonic.Marshal(bidRequest)
		s.log.WithField("request", string(marshal)).Info("debug request")
	}

	httpRequest, _, err := s.BuildPbHttpHttpRequest(bidRequest)
	if err != nil {
		s.log.WithError(err).Error("BuildPbHttpHttpRequest error")
		return nil, err
	}

	s.SampleDspBroadcastProtobufRequest(s.GetDspId(), dspSlot.Id, candidate, bidRequest)
	return httpRequest, nil
}

func (s *ShuZiYueDongDspBroker) makeImp(request *ad_service.AdRequest, candidate *ad_service.AdCandidate, trafficData ad_service_entity.TrafficData) (*shuziyuedong_broker_entity_pb.Imp, error) {
	dspSlot := s.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	imp := &shuziyuedong_broker_entity_pb.Imp{
		Deeplink: 1,
		Ul:       0,
		Id:       trafficData.GetRequestId(),
		Tagid:    candidate.GetDspSlotKey(),
		Subtagid: strconv.FormatUint(uint64(candidate.GetDspSlotId()), 10),
		Bidfloor: candidate.GetBidFloor().Price,
		Width:    int32(dspSlot.Width),
		Height:   int32(dspSlot.Height),
		Secure:   1,
	}
	if trafficData.GetOsType() == entity.OsTypeIOS {
		imp.Ul = 1
	}
	if imp.Height == 0 || imp.Width == 0 {
		imp.Height = int32(trafficData.GetSlotHeight())
		imp.Width = int32(trafficData.GetSlotWidth())
	}
	if (imp.Height == 0 || imp.Width == 0) && len(request.SlotSize) > 0 {
		imp.Height = int32(request.SlotSize[0].Height)
		imp.Width = int32(request.SlotSize[0].Width)
	}

	switch trafficData.GetSlotType() {
	case entity.SlotTypeOpening:
		imp.Style = 1
	case entity.SlotTypePopup:
		imp.Style = 2
	case entity.SlotTypeBanner:
		imp.Style = 3
	case entity.SlotTypeFeeds:
		imp.Style = 4
	case entity.SlotTypeRewardVideo:
		imp.Style = 5
	case entity.SlotTypeVideoOpening:
		imp.Style = 6
	case entity.SlotTypeVideo:
		imp.Style = 7
	}

	return imp, nil
}

func (s *ShuZiYueDongDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList,
	response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if response.StatusCode != 200 {
		s.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			type_convert.GetAssertString(response.StatusCode), type_convert.GetAssertString(response.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		s.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			"1", "读取body失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	bidResponse := &shuziyuedong_broker_entity_pb.Response{}
	err = s.ParsePbHttpHttpResponse(response, data, bidResponse)
	if err != nil {
		s.log.WithError(err).Debug("ParsePbHttpHttpResponse error")
		s.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			"2", "body反序列化失败")
		return nil, err
	}

	s.SampleDspBroadcastProtobufResponse(s.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate,
		response.StatusCode, bidResponse)

	if request.IsDebug {
		resBody, _ := sonic.Marshal(bidResponse)
		zap.L().Info("ShuZiYueDongDspBroker.ParseResponse, body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}
	s.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
		broadcastCandidate.GetDspSlotId().String(),
		strconv.FormatUint(uint64(bidResponse.GetCode()), 10), mappingErrorCode(bidResponse.GetCode()))

	if bidResponse.GetCode() != 0 || bidResponse.Seatbid == nil {
		return nil, err_code.ErrBroadcastNoBidding
	}

	list := ad_service.DspAdCandidateList{}
	for _, bid := range bidResponse.Seatbid.Bid {
		dspAdCandidate, err := s.buildDspAdCandidate(bid, broadcastCandidate)
		if err != nil {
			s.log.WithError(err).Error("buildDspAdCandidate error")
			return nil, err_code.ErrBroadcastResponseCandidateError.Wrap(err)
		}
		list = append(list, dspAdCandidate)
	}

	return list, nil
}

func (s *ShuZiYueDongDspBroker) buildDspAdCandidate(bid *shuziyuedong_broker_entity_pb.Bid,
	broadcastCandidate *ad_service.AdCandidate) (*ad_service.DspAdCandidate, error) {
	ad := &entity.Ad{
		DspId:         s.GetDspId(),
		DspSlotId:     broadcastCandidate.GetDspSlotId(),
		DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
		AdMonitorInfo: s.buildMonitor(bid),
	}

	ad.AppInfo = &entity.AppInfo{
		PackageName: bid.PackageName,
		AppName:     bid.AppName,
		AppVersion:  bid.AppVersion,
		PackageSize: int(bid.AppSize),
		Privacy:     bid.PrivacyUrl,
		Permission:  bid.PermissionUrl,
		AppDesc:     bid.AppDesc,
		AppDescURL:  bid.AppDescUrl,
		Develop:     bid.Developer,
	}
	if len(bid.MiniAppPath) > 0 {
		ad.AppInfo.WechatExt = &entity.WechatExt{
			ProgramId:   bid.MiniAppId,
			ProgramPath: bid.MiniAppPath,
		}
	}
	creative := s.buildCreative(bid)
	if creative == nil {
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	adCandidate := ad_service.NewDspAdCandidateWithPool(ad)
	adCandidate.SetCreative(creative)
	adCandidate.SetBidPrice(uint32(bid.Price))
	adCandidate.SetBidType(entity.BidTypeCpm)
	adCandidate.SetAdCandidateChargePriceEncoder(func(chargePrice uint32) string {
		result, err := s.PriceManager.GetDspCoder(s.DspProtocol).EncodeWithKey(uint64(chargePrice), s.GetIKey(), s.GetEKey())
		if err != nil {
			s.log.WithError(err).WithField("price", chargePrice).Error("EncodeWithKey error")
			return ""
		}
		return result
	})
	adCandidate.SetDspProtocol(s.GetDspProtocol())
	adCandidate.SetDspAdID(bid.Adid)

	return adCandidate, nil
}

func (s *ShuZiYueDongDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return s.dspSlotRegister
}

func (s *ShuZiYueDongDspBroker) buildMonitor(bid *shuziyuedong_broker_entity_pb.Bid) *entity.AdMonitorInfo {
	monitor := &entity.AdMonitorInfo{}

	monitor.LandingUrl = bid.LandingUrl
	switch bid.Action {
	case 2:
		monitor.LandingAction = entity.LandingTypeDeepLink
		monitor.DeepLinkUrl = bid.TargetUrl
	case 3:
		monitor.LandingAction = entity.LandingTypeDownload
		monitor.DownloadUrl = bid.TargetUrl
	case 4:
		monitor.LandingAction = entity.LandingTypeWeChatProgram
	default:
		monitor.LandingAction = entity.LandingTypeInWebView
		monitor.H5LandingUrl = bid.LandingUrl
	}

	if bid.Tracking != nil {
		monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, bid.Tracking.ImpTrackers...)
		monitor.ClickMonitorList = append(monitor.ClickMonitorList, bid.Tracking.ClickTrackers...)
		monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, bid.Tracking.NoticeUrls...)
		monitor.DeepLinkMonitorList = append(monitor.DeepLinkMonitorList, bid.Tracking.DplkTrackers...)
		monitor.DeepLinkFailedMonitorList = append(monitor.DeepLinkFailedMonitorList, bid.Tracking.DplkFailTrackers...)
		monitor.AppDownloadStartedMonitorList = append(monitor.AppDownloadStartedMonitorList, bid.Tracking.DownloadTrackers...)
		monitor.AppDownloadFinishedMonitorList = append(monitor.AppDownloadFinishedMonitorList, bid.Tracking.DownloadedTrackers...)
		monitor.AppInstallStartMonitorList = append(monitor.AppInstallStartMonitorList, bid.Tracking.InstallTrackers...)
		monitor.AppInstalledFinishMonitorList = append(monitor.AppInstalledFinishMonitorList, bid.Tracking.InstalledTrackers...)
		monitor.VideoStartUrlList = append(monitor.VideoStartUrlList, bid.Tracking.PlayerStartTrackers...)
		monitor.VideoCloseUrlList = append(monitor.VideoCloseUrlList, bid.Tracking.PlayerEndTrackers...)
	}

	//宏替换
	if len(monitor.ImpressionMonitorList) > 0 {
		monitor.ImpressionMonitorList = s.macroInfo.MacroReplaceList(monitor.ImpressionMonitorList)
	}
	if len(monitor.ClickMonitorList) > 0 {
		monitor.ClickMonitorList = s.macroInfo.MacroReplaceList(monitor.ClickMonitorList)
	}
	if len(monitor.DeepLinkMonitorList) > 0 {
		monitor.DeepLinkMonitorList = s.macroInfo.MacroReplaceList(monitor.DeepLinkMonitorList)
	}

	return monitor
}

func (s *ShuZiYueDongDspBroker) buildCreative(bid *shuziyuedong_broker_entity_pb.Bid) *entity.Creative {
	if bid.Adm == nil {
		return nil
	}
	creative := &entity.Creative{
		CreativeKey: bid.Adm.Id,
	}

	title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: bid.Adm.Title}
	if len(title.Data) == 0 {
		title.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, title)

	desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: bid.Adm.Desc}
	if len(desc.Data) == 0 {
		desc.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, desc)

	if len(bid.Adm.Icon) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.Adm.Icon,
			Height:       100,
			Width:        100,
		})
	}

	for _, image := range bid.Adm.Img {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          image.Url,
			Height:       image.H,
			Width:        image.W,
		})
	}
	if bid.Adm.Video != nil {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          bid.Adm.Video.Url,
			Height:       bid.Adm.Video.H,
			Width:        bid.Adm.Video.W,
			Duration:     float64(bid.Adm.Video.Duration),
		})
		if len(bid.Adm.Video.Cover) > 0 {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeCoverImage,
				Url:          bid.Adm.Video.Cover,
				Height:       bid.Adm.Video.H,
				Width:        bid.Adm.Video.W,
			})
		}
	}

	return creative
}

// 映射设备运营商类型
func mappingCarrierType(carrierType entity.OperatorType) int32 {
	switch carrierType {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 2
	case entity.OperatorTypeChinaTelecom:
		return 3
	default:
		return 0
	}
}

// 映射设备网络类型
func mappingConnectionType(connectionType entity.ConnectionType) int32 {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionTypeCellular:
		return 3
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 0
	}
}

// 映射设备类型
func mappingDeviceType(deviceType entity.DeviceType) int32 {
	switch deviceType {
	case entity.DeviceTypePc:
		return 3
	case entity.DeviceTypeOtt:
		return 4
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	default:
		return 0
	}
}

// 映射设备操作系统类型
func mappingOsType(a entity.OsType) int32 {
	switch a {
	case entity.OsTypeAndroid:
		return 1
	case entity.OsTypeIOS:
		return 2
	default:
		return 0
	}
}

// 映射用户性别
func mappingUserGender(a entity.UserGenderType) string {
	switch a {
	case entity.UserGenderMan:
		return "M"
	case entity.UserGenderWoman:
		return "F"
	default:
		return ""
	}
}

// 映射返回错误码
func mappingErrorCode(code int32) string {
	switch code {
	case 0:
		return "请求成功"
	case 117:
		return "无填充"
	case 118:
		return "请求体异常，解析失败"
	case 119:
		return "token校验失败"
	case 120:
		return "参数校验失败"
	default:
		return "异常错误"
	}
}
