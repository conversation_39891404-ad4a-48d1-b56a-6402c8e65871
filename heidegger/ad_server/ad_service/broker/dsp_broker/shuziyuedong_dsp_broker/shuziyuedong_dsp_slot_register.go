package shuziyuedong_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type ShuZiYueDongSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`

	Token string `json:"token"`
}

func (info *ShuZiYueDongSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	info.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	info.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")

	token, err := dspSlotInfo.ExtraData.GetString("token")
	if err != nil {
		return err
	}
	info.Token = token

	return nil
}

type ShuZiYueDongDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*ShuZiYueDongSlotSlotInfo
}

func NewShuZiYueDongDspSlotRegister(dspId utils.ID) *ShuZiYueDongDspSlotRegister {
	return &ShuZiYueDongDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*ShuZiYueDongSlotSlotInfo),
	}
}

func (s *ShuZiYueDongDspSlotRegister) GetDspId() utils.ID {
	return s.dspId
}

func (s *ShuZiYueDongDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	dspSlotMap := make(map[utils.ID]*ShuZiYueDongSlotSlotInfo)
	for _, slotInfo := range list {
		slot := &ShuZiYueDongSlotSlotInfo{}
		if err := slot.Init(slotInfo); err != nil {
			zap.L().Error("[YouTuiDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		dspSlotMap[slot.Id] = slot
	}

	s.dspSlotList = list
	s.dspSlotMap = dspSlotMap
	return nil
}

func (s *ShuZiYueDongDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return s.dspSlotList
}

func (s *ShuZiYueDongDspSlotRegister) GetSlotInfo(slotId utils.ID) *ShuZiYueDongSlotSlotInfo {
	return s.dspSlotMap[slotId]
}
