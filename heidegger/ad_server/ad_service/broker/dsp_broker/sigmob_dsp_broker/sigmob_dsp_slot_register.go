package sigmob_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"strings"
)

type SigmobSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height        int    `json:"height"`
	Width         int    `json:"width"`
	AppName       string `json:"app_name"`
	PkgName       string `json:"pkg_name"`
	AppId         string `json:"app_id"`
	Publisher     string `json:"publisher"`
	AdType        int    `json:"ad_type"`
	SupportAction []int  `json:"support_action"`
}

func (info *SigmobSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {

	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppId, err = dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get app_id from extra_data failed, err: %v", err)
	}

	info.Publisher, err = dspSlotInfo.ExtraData.GetString("publisher")
	if err != nil {
		return fmt.Errorf("get publisher from extra_data failed, err: %v", err)
	}

	//1=激励视频;2=开屏;3=全屏视频;5=banner;6=原生
	info.AdType, err = dspSlotInfo.ExtraData.GetInt("ad_type")
	if err != nil {
		return fmt.Errorf("get ad_type from extra_data failed, err: %v", err)
	}

	actions, err := dspSlotInfo.ExtraData.GetString("support_action")
	if err == nil && len(actions) > 0 {
		actionArr := strings.Split(actions, ",")
		for _, action := range actionArr {
			info.SupportAction = append(info.SupportAction, type_convert.GetAssertInt(action))
		}
	}

	return nil
}

type SigmobDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*SigmobSlotSlotInfo
}

func NewSigmobDspSlotRegister(dspId utils.ID) *SigmobDspSlotRegister {
	return &SigmobDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*SigmobSlotSlotInfo),
	}
}

func (r *SigmobDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *SigmobDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*SigmobSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &SigmobSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[SigmobDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *SigmobDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *SigmobDspSlotRegister) GetSlotInfo(slotId utils.ID) *SigmobSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
