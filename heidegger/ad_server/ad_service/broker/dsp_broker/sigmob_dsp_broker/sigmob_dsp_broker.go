package sigmob_dsp_broker

import (
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jd_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/sigmob_dsp_broker/sigmob_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"strings"
	"fmt"
)

const (
	uapattern = "Mozilla/5.0 (Linux; Android __OSV__; __MODEL__; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.114 Mobile Safari/537.36"
)

type SigmobDspBroker struct {
	dsp_broker.DspBrokerBase

	jdScore jd_broker.JdUserScore

	slotRegister *SigmobDspSlotRegister

	MacroWinPrice string
}

func NewSigmobDspBroker(dspId utils.ID) *SigmobDspBroker {
	return &SigmobDspBroker{
		slotRegister:  NewSigmobDspSlotRegister(dspId),
		MacroWinPrice: "_PRICE_",
	}
}

func (impl *SigmobDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *SigmobDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("SigmobDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("SigmobDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}
	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidFloor := candidate.GetBidFloor()

	width := int(trafficData.GetSlotWidth())
	height := int(trafficData.GetSlotHeight())

	if width == 0 {
		width = dspSlot.Width
	}

	if height == 0 {
		height = dspSlot.Height
	}

	sigRequest := &sigmob_dsp_entity.BidRequest{
		ID:         trafficData.GetRequestId(),
		ApiVersion: "1.8",
		Media: sigmob_dsp_entity.Media{
			Publisher:   dspSlot.Publisher,
			AppID:       dspSlot.AppId,
			Name:        trafficData.GetAppName(),
			Bundle:      trafficData.GetAppBundle(),
			Version:     trafficData.GetAppVersion(),
			Cat:         "",
			SupportHttp: 0,
		},
		Pos: sigmob_dsp_entity.Pos{
			AdType:        dspSlot.AdType,
			ID:            slotId,
			Floor:         int(bidFloor.Price),
			Width:         width,
			Height:        height,
			MaterialType:  impl.getMaterialType(dspSlot.AdType),
			MinDuration:   0,
			MaxDuration:   int(request.VideoMaxDuration),
			SupportAction: []int{1, 2, 98, 99},
			AdCount:       1,
			LastAds:       nil,
			Bseat:         nil,
			Bcat:          nil,
		},
		Device: impl.encodeDevice(request, trafficData),
		Network: sigmob_dsp_entity.Network{
			ConnectionType: impl.mappingConnectionType(trafficData.GetConnectionType()),
			Carrier:        impl.mappingOperatorType(trafficData.GetOperatorType()),
			PLMN:           "46000",
		},
		Geo: &sigmob_dsp_entity.Geo{
			Lat: trafficData.GetGeoLatitude(),
			Lng: trafficData.GetGeoLongitude(),
		},
		User: &sigmob_dsp_entity.User{},
		Test: 0,
		Tmax: 300,
	}

	if request.IsTest {
		sigRequest.Test = 1
	}

	if len(dspSlot.SupportAction) > 0 {
		sigRequest.Pos.SupportAction = dspSlot.SupportAction
	}

	for _, installApp := range request.App.InstalledApp {
		app := sigmob_dsp_entity.App{
			AppName:     "",
			Version:     "",
			PackageName: installApp,
			InfoTime:    0,
		}

		sigRequest.User.AppList = append(sigRequest.User.AppList, app)
	}

	if sigRequest.Pos.AdType == 6 {
		native := &sigmob_dsp_entity.Native{
			Assets: make([]sigmob_dsp_entity.Asset, 0),
			W:      width,
			H:      height,
		}

		image := sigmob_dsp_entity.Asset{
			ID:       0,
			Required: 1,
			Image: &sigmob_dsp_entity.Image{
				Type:  1,
				Mimes: []string{"jpg", "png", "jepg"},
				W:     width,
				H:     height,
			},
		}

		video := sigmob_dsp_entity.Asset{
			ID:       1,
			Required: 0,
			Video: &sigmob_dsp_entity.Video{
				Mimes:       []string{"mp4"},
				W:           width,
				H:           height,
				MinDuration: 0,
				MaxDuration: int(request.VideoMaxDuration),
			},
		}

		title := sigmob_dsp_entity.Asset{
			ID:       2,
			Required: 1,
			Text: &sigmob_dsp_entity.Text{
				Type:      1,
				MinLength: 1,
				MaxLength: 30,
			},
		}

		desc := sigmob_dsp_entity.Asset{
			ID:       3,
			Required: 0,
			Text: &sigmob_dsp_entity.Text{
				Type:      2,
				MinLength: 1,
				MaxLength: 30,
			},
		}

		native.Assets = append(native.Assets, image, video, title, desc)

		sigRequest.Pos.Native = native
	}

	if len(dspSlot.AppName) > 0 {
		sigRequest.Media.Name = dspSlot.AppName
	}

	if len(dspSlot.PkgName) > 0 {
		sigRequest.Media.Bundle = dspSlot.PkgName
	}

	req, _, err := impl.BuildSonicJsonHttpRequest(sigRequest)
	if err != nil {
		zap.L().Error("SigmobDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}

	req.Header.Set("X-Forwarded-For", trafficData.GetRequestIp())

	if request.IsDebug {
		requestBody, _ := sonic.Marshal(sigRequest)
		zap.L().Info("SigmobDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(requestBody))))))
	}

	impl.SampleDspBroadcastSonicJsonRequest(impl.GetDspId(), dspSlot.Id, candidate, sigRequest)
	return req, nil

}

func (impl *SigmobDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) sigmob_dsp_entity.Device {
	deviceInfo := sigmob_dsp_entity.Device{
		OS:              impl.mappingOsType(trafficData.GetOsType()),
		OSVersion:       trafficData.GetOsVersion(),
		Make:            trafficData.GetBrand(),
		Model:           trafficData.GetModel(),
		HardwareMachine: request.Device.HardwareMachineCode,
		UA:              trafficData.GetUserAgent(),
		Width:           int(trafficData.GetScreenWidth()),
		Height:          int(trafficData.GetScreenHeight()),
		DPI:             int(request.Device.PPI), //todo
		DeviceType:      impl.mappingDeviceType(trafficData.GetDeviceType()),
		Gaid:            "",
		GaidMd5:         "",
		IDFV:            trafficData.GetIdfv(),
		Mac:             trafficData.GetMac(),
		BootMark:        trafficData.GetBootMark(),
		UpdateMark:      trafficData.GetUpdateMark(),
		FBTime:          trafficData.GetDeviceInitTime(),
		DeviceStartSec:  trafficData.GetDeviceStartupTime(),
		Country:         "CN",
		DeviceLanguage:  "zh-Hans-CN",
		DeviceName:      request.Device.DeviceName,
		HardwareModel:   trafficData.GetModel(),
		SystemUpdateSec: trafficData.GetDeviceUpgradeTime(),
		TimeZone:        "28800",
		MntID:           "",
		Orientation:     impl.mappingOrientation(trafficData.GetScreenOrientation()),
		TotalDiskSize:   uint64(request.Device.SystemTotalDisk),
		FreeDiskSize:    uint64(request.Device.SystemFreeDisk),
	}

	if len(deviceInfo.UA) == 0 {
		ua := strings.ReplaceAll(uapattern, "__OSV__", trafficData.GetOsVersion())
		ua = strings.ReplaceAll(ua, "__MODEL__", trafficData.GetModel())
		deviceInfo.UA = ua
	}

	if len(deviceInfo.HardwareMachine) == 0 {
		deviceInfo.HardwareMachine = trafficData.GetModel()
	}

	if request.Device.SystemTotalMem > 0 {
		deviceInfo.PhysicalMemoryByte = type_convert.GetAssertString(request.Device.SystemTotalMem)
	}

	if request.Device.IsIp6 {
		deviceInfo.IPv6 = trafficData.GetRequestIp()
	} else {
		deviceInfo.IP = trafficData.GetRequestIp()
	}

	if len(trafficData.GetImei()) > 0 {
		deviceInfo.Imei = trafficData.GetImei()
	} else if len(trafficData.GetMd5Imei()) > 0 {
		deviceInfo.ImeiMd5 = strings.ToLower(trafficData.GetMd5Imei())
	}

	if len(trafficData.GetOaid()) > 0 {
		deviceInfo.OAID = trafficData.GetOaid()
	} else if len(trafficData.GetMd5Oaid()) > 0 {
		deviceInfo.OaidMd5 = strings.ToLower(trafficData.GetMd5Oaid())
	}

	if len(trafficData.GetIdfa()) > 0 {
		deviceInfo.IDFA = trafficData.GetIdfa()
	} else if len(trafficData.GetMd5Idfa()) > 0 {
		deviceInfo.IdfaMd5 = strings.ToLower(trafficData.GetMd5Idfa())
	}

	if len(trafficData.GetAndroidId()) > 0 {
		deviceInfo.AndroidID = trafficData.GetAndroidId()
	} else if len(trafficData.GetMd5AndroidId()) > 0 {
		deviceInfo.AndroidIdMd5 = strings.ToLower(trafficData.GetMd5AndroidId())
	}

	if len(trafficData.GetCaid()) > 0 {
		caid1 := sigmob_dsp_entity.Caid{
			ID:      device_utils.GetCaidRaw(trafficData.GetCaid()),
			Version: device_utils.GetCaidVersion(trafficData.GetCaid()),
		}
		deviceInfo.Caid = append(deviceInfo.Caid, caid1)
	}

	for _, caid := range request.Device.Caids {
		if caid == trafficData.GetCaid() {
			continue
		}

		caid1 := sigmob_dsp_entity.Caid{
			ID:      device_utils.GetCaidRaw(caid),
			Version: device_utils.GetCaidVersion(caid),
		}
		deviceInfo.Caid = append(deviceInfo.Caid, caid1)
	}

	return deviceInfo

}

func (impl *SigmobDspBroker) mappingOrientation(s entity.ScreenOrientationType) int {
	switch s {
	case entity.ScreenOrientationTypePortrait:
		return 1
	case entity.ScreenOrientationTypeLandscape:
		return 2
	default:
		return 0
	}
}

func (impl *SigmobDspBroker) mappingDeviceType(deviceType entity.DeviceType) int {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return 2
	case entity.DeviceTypePad:
		return 3
	default:
		return 1
	}
}

func (impl *SigmobDspBroker) mappingOsType(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "ios"
	case entity.OsTypeAndroid:
		return "android"
	case entity.OsTypeWindowsPhone:
		return "wp"
	default:
		return "android"
	}
}

func (impl *SigmobDspBroker) mappingSlotType(s entity.SlotType) int {
	switch s {
	case entity.SlotTypeRewardVideo:
		return 1
	case entity.SlotTypeOpening:
		return 2
	case entity.SlotTypeVideo:
		return 3
	case entity.SlotTypeBanner:
		return 5
	case entity.SlotTypeFeeds:
		return 6
	default:
		return 6
	}
}

func (impl *SigmobDspBroker) mappingOperatorType(carrier entity.OperatorType) int {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaTelecom:
		return 2
	case entity.OperatorTypeTietong:
		return 5
	case entity.OperatorTypeChinaUnicom:
		return 3
	default:
		return 0
	}
}

func (impl *SigmobDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 101
	case entity.ConnectionTypeWifi:
		return 100
	case entity.ConnectionTypeCellular:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (impl *SigmobDspBroker) getMaterialType(sigAdType int) []int {
	switch sigAdType {
	case 1:
		return []int{10}
	case 2:
		return []int{3, 8}
	case 3:
		return []int{10}
	case 5:
		return []int{3, 8, 11}
	case 6:
		return []int{15}
	default:
		return []int{11}
	}
}

func (impl *SigmobDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *SigmobDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("SigmobDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &sigmob_dsp_entity.BidResponse{}

	resBody, err := impl.ParseSonicJsonHttpResponse(resp, data, response)
	if err != nil {
		zap.L().Error("SigmobDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := sonic.Marshal(response)
		zap.L().Info("SigmobDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	if response.Result != 0 || len(response.Data) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, resBid := range response.Data {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = &entity.AppInfo{
			PackageName: resBid.AppBundle,
			AppName:     resBid.DownloadFileName,
			Icon:        resBid.IconUrl,
			WechatExt:   nil,
			AppID:       "",
			AppVersion:  resBid.AppVer,
			PackageSize: resBid.FileSize,
			Privacy:     resBid.Privacy,
			Permission:  resBid.PermissionsUrl,
			AppDescURL:  "",
			Develop:     resBid.AppDeveloper,
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

		if len(candidateAd.AdMonitorInfo.DeepLinkUrl) > 0 {
			appName, pkgName, icon := impl.ParseAppInfo(candidateAd.AdMonitorInfo.DeepLinkUrl, request.Device.OsType)
			if len(pkgName) > 0 && len(candidateAd.AppInfo.PackageName) == 0 {
				candidateAd.AppInfo.PackageName = pkgName
				candidateAd.AppInfo.AppName = appName
				candidateAd.AppInfo.Icon = icon
			}
		}

		candidateCreative := impl.ParseCreativeData(resBid)

		bidPrice := uint32(resBid.Price)
		if bidPrice == 0 {
			bidPrice = 1 //for test
		} else if bidPrice > 100*1000 {
			bidPrice = 100 * 500
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(bidPrice)
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(resBid.AdID)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break
	}

	return result, nil
}

func (impl *SigmobDspBroker) ParseCreativeData(bid sigmob_dsp_entity.Ad) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.AdID,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if len(bid.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         bid.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Description) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         bid.Description,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.ImageUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          bid.ImageUrl,
			Width:        bid.ImageW,
			Height:       bid.ImageH,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.VideoUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          bid.VideoUrl,
			Width:        bid.VideoW,
			Height:       bid.VideoH,
			Duration:     float64(bid.VideoDuration),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.IconUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.IconUrl,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if bid.Native != nil {
		for _, asset := range bid.Native.Assets {
			if asset.Image != nil && len(asset.Image.URL) > 0 {
				material := &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          asset.Image.URL,
					Width:        int32(asset.Image.W),
					Height:       int32(asset.Image.H),
				}
				creative.MaterialList = append(creative.MaterialList, material)
			}

			if asset.Video != nil && len(asset.Video.URL) > 0 {
				material := &entity.Material{
					MaterialType: entity.MaterialTypeVideo,
					Url:          asset.Video.URL,
					Width:        int32(asset.Video.W),
					Height:       int32(asset.Video.H),
					Duration:     float64(asset.Video.Duration),
				}
				creative.MaterialList = append(creative.MaterialList, material)
			}

			if asset.Text != nil && len(asset.Text.Context) > 0 {
				material := &entity.Material{
					MaterialType: entity.MaterialTypeTitle,
					Data:         bid.Title,
				}

				if asset.ID == 3 {
					material.MaterialType = entity.MaterialTypeDesc
				}
				creative.MaterialList = append(creative.MaterialList, material)
			}

		}
	}
	return creative
}

func (impl *SigmobDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid sigmob_dsp_entity.Ad) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      make([]string, 0),
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkUrl:           bid.Deeplink,
		LandingUrl:            bid.TargetUrl,
		H5LandingUrl:          bid.TargetUrl,
	}

	for _, clkTrack := range bid.Click {
		newClkTrack := strings.ReplaceAll(clkTrack, "_TIMESTAMP_", "__TS__")
		tracking.ClickMonitorList = append(tracking.ClickMonitorList, newClkTrack)
	}

	for _, impTrack := range bid.Imp {
		newImpTrack := strings.ReplaceAll(impTrack, "_TIMESTAMP_", "__TS__")

		if strings.Contains(newImpTrack, impl.MacroWinPrice) {
			newImpTrack = strings.ReplaceAll(newImpTrack, impl.MacroWinPrice, "__DSPWPRICE__")
			newImpTrack = strings.ReplaceAll(newImpTrack, "_ERRORCODE_", "1")
			tracking.AddImpressionMonitor(newImpTrack)
		} else {
			tracking.AddImpressionMonitor(newImpTrack)
		}
	}

	for _, impTrack := range bid.WinUrl {
		newImpTrack := strings.ReplaceAll(impTrack, "_AUCTION_PRICE_", "__DSPWPRICE__")
		newImpTrack = strings.ReplaceAll(newImpTrack, "_ERRORCODE_", "1")
		newImpTrack = strings.ReplaceAll(newImpTrack, "_TIMESTAMP_", "__TS__")

		if strings.Contains(newImpTrack, impl.MacroWinPrice) {
			newImpTrack = strings.ReplaceAll(newImpTrack, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.AddImpressionMonitor(newImpTrack)
		} else {
			tracking.AddImpressionMonitor(newImpTrack)
		}
	}

	if bid.Action == 2 || bid.Action == 5 {
		tracking.LandingAction = entity.LandingTypeDownload
		tracking.DownloadUrl = bid.TargetUrl
	} else if bid.Action == 99 {
		tracking.LandingAction = entity.LandingTypeDeepLink
	}

	if len(bid.DownloadStart) > 0 {
		tracking.AppDownloadStartedMonitorList = append(tracking.AppDownloadStartedMonitorList, bid.DownloadStart...)
	}

	if len(bid.DownloadComplete) > 0 {
		tracking.AppDownloadFinishedMonitorList = append(tracking.AppDownloadFinishedMonitorList, bid.DownloadComplete...)
	}

	if len(bid.InstallStart) > 0 {
		tracking.AppInstallStartMonitorList = append(tracking.AppInstallStartMonitorList, bid.InstallStart...)
	}

	if len(bid.InstallComplete) > 0 {
		tracking.AppInstalledFinishMonitorList = append(tracking.AppInstalledFinishMonitorList, bid.InstallComplete...)
	}

	if len(bid.DeeplinkTry) > 0 {
		tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, bid.DeeplinkTry...)
	}
	if len(bid.DeeplinkSuccess) > 0 {
		tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, bid.DeeplinkSuccess...)
	}

	if len(bid.DeeplinkFail) > 0 {
		tracking.DeepLinkFailedMonitorList = append(tracking.DeepLinkFailedMonitorList, bid.DeeplinkFail...)
	}

	if len(bid.VideoPlay4) > 0 {
		tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, bid.VideoPlay4...)
	}

	if len(bid.VideoView) > 0 {
		tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, bid.VideoView...)
	}

	if len(bid.VideoPlay0) > 0 {
		tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, bid.VideoPlay0...)
	}

	return tracking
}
