package tianmei_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type TianMeiSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height  int    `json:"height"`
	Width   int    `json:"width"`
	AppId   int    `json:"app_id"`
	PkgName string `json:"pkg_name"`
}

func (info *TianMeiSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error
	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	appId, err := dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get app_id from extra_data failed, err: %v", err)
	}
	info.AppId = type_convert.GetAssertInt(appId)

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	return nil
}

type TianMeiDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*TianMeiSlotSlotInfo
}

func NewTianMeiDspSlotRegister(dspId utils.ID) *TianMeiDspSlotRegister {
	return &TianMeiDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*TianMeiSlotSlotInfo),
	}
}

func (r *TianMeiDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *TianMeiDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*TianMeiSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &TianMeiSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[TianMeiDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *TianMeiDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *TianMeiDspSlotRegister) GetSlotInfo(slotId utils.ID) *TianMeiSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
