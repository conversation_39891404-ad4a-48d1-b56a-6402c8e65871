package tianmei_dsp_broker

import (
	"encoding/json"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/tianmei_dsp_broker/tianmei_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"fmt"
)

type TianMeiDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *TianMeiDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string
}

func NewTianMeiDspBroker(dspId utils.ID) *TianMeiDspBroker {
	return &TianMeiDspBroker{
		slotRegister:  NewTianMeiDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "",
	}
}

func (impl *TianMeiDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *TianMeiDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("TianMeiDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("TianMeiDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))

		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("TianMeiDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidFloor := candidate.GetBidFloor()

	deviceId, _ := trafficData.GetDeviceIdWithType()

	imp := tianmei_dsp_entity.Imp{
		TagID:      type_convert.GetAssertInt(slotId),
		Width:      int(trafficData.GetSlotWidth()),
		Height:     int(trafficData.GetSlotHeight()),
		BidType:    impl.mappingSlotType(trafficData.GetSlotType()),
		BidFloor:   int64(bidFloor.Price),
		ChargeType: "ecpm",
	}

	if dspSlot.Width != 0 {
		imp.Width = dspSlot.Width
	}

	if dspSlot.Height != 0 {
		imp.Height = dspSlot.Height
	}

	if imp.Width == 0 || imp.Height == 0 {
		if len(request.SlotSize) > 0 {
			imp.Width = int(request.SlotSize[0].Width)
			imp.Height = int(request.SlotSize[0].Height)
		}
	}

	tmRequest := tianmei_dsp_entity.BidRequest{
		ID:     trafficData.GetRequestId(),
		Imp:    []tianmei_dsp_entity.Imp{imp},
		Device: impl.encodeDevice(request, trafficData),
		App: tianmei_dsp_entity.App{
			ID:     dspSlot.AppId,
			Bundle: trafficData.GetAppBundle(),
			Name:   trafficData.GetAppName(),
			Ver:    trafficData.GetAppVersion(),
		},
		User: tianmei_dsp_entity.User{
			BuyerUID: deviceId,
			Age:      int(request.UserAge),
			Gender:   0,
		},
		Test:    0,
		Version: "V1.0.32",
		TMax:    300,
		HTTPS:   false,
	}

	req, _, err := impl.BuildEasyJsonHttpRequest(tmRequest)
	if err != nil {
		zap.L().Error("TianMeiDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}

	if request.IsDebug {
		reqbody, _ := json.Marshal(tmRequest)
		zap.L().Info("TianMeiDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqbody)))))
	}

	impl.SampleDspBroadcastEasyjsonRequest(impl.dspId, dspSlot.Id, candidate, tmRequest)

	return req, nil
}

func (impl *TianMeiDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) tianmei_dsp_entity.Device {
	deviceInfo := tianmei_dsp_entity.Device{
		UA:           trafficData.GetUserAgent(),
		DeviceType:   impl.mappingDeviceType(trafficData.GetDeviceType()),
		OS:           impl.mappingOsType(trafficData.GetOsType()),
		OSV:          trafficData.GetOsVersion(),
		AV:           0,
		MEID:         "",
		IMSI:         "",
		OSUpdateTime: trafficData.GetDeviceUpgradeTime(),
		FlashVer:     "",
		DPI:          "",
		MachineType:  "",
		Jailbreak:    0,
		Language:     "",
		Geo: tianmei_dsp_entity.Geo{
			Lat: trafficData.GetGeoLatitude(),
			Lon: trafficData.GetGeoLongitude(),
		},
		Width:          int(trafficData.GetScreenWidth()),
		Height:         int(trafficData.GetSlotHeight()),
		Make:           trafficData.GetBrand(),
		Model:          trafficData.GetModel(),
		ConnectionType: impl.mappingConnectionType(trafficData.GetConnectionType()),
		OperatorType:   impl.mappingOperatorType(trafficData.GetOperatorType()),
		OAID:           trafficData.GetOaid(),
		OAIDMd5:        trafficData.GetMd5Oaid(),
		MAC:            trafficData.GetMac(),
		MACMd5:         trafficData.GetMd5Mac(),
		BootMark:       trafficData.GetBootMark(),
		UpdateMark:     trafficData.GetUpdateMark(),
		GAID:           "",
		GAIDMd5:        "",
		OpenUDID:       trafficData.GetOpenUdid(),
		AAID:           trafficData.GetAaid(),
		PAID:           request.Device.Paid,
		CAID:           device_utils.GetCaidRaw(trafficData.GetCaid()),
		CAIDVersion:    device_utils.GetCaidVersion(trafficData.GetCaid()),
		StartupTime:    trafficData.GetDeviceStartupTime(),
	}

	if trafficData.GetOsType() == entity.OsTypeIOS {
		if len(trafficData.GetIdfa()) > 0 {
			deviceInfo.DID = trafficData.GetIdfa()
		} else if len(trafficData.GetMd5Idfa()) > 0 {
			deviceInfo.DIDMd5 = trafficData.GetMd5Idfa()
		}

		if len(trafficData.GetIdfv()) > 0 {
			deviceInfo.DPID = trafficData.GetIdfv()
		} else if len(trafficData.GetMd5Idfv()) > 0 {
			deviceInfo.DPID = trafficData.GetMd5Idfv()
		}

	} else {
		if len(trafficData.GetImei()) > 0 {
			deviceInfo.DID = trafficData.GetImei()
		} else if len(trafficData.GetMd5Imei()) > 0 {
			deviceInfo.DIDMd5 = trafficData.GetMd5Imei()
		}

		if len(trafficData.GetAndroidId()) > 0 {
			deviceInfo.DPID = trafficData.GetAndroidId()
		} else if len(trafficData.GetMd5AndroidId()) > 0 {
			deviceInfo.DPID = trafficData.GetMd5AndroidId()
		}
	}

	if request.Device.IsIp6 {
		deviceInfo.IPV6 = trafficData.GetRequestIp()
	} else {
		deviceInfo.IP = trafficData.GetRequestIp()
	}

	if request.Device.PPI > 0 {
		deviceInfo.PPI = type_convert.GetAssertString(request.Device.PPI)
	}

	if trafficData.GetScreenDensity() > 0 {
		deviceInfo.PxRatio = type_convert.GetAssertString(trafficData.GetScreenDensity())
	}

	if request.Device.SystemTotalMem > 0 {
		deviceInfo.SysMemory = type_convert.GetAssertString(request.Device.SystemTotalMem)
	}

	if request.Device.SystemTotalDisk > 0 {
		deviceInfo.SysDiskSize = type_convert.GetAssertString(request.Device.SystemTotalDisk)
	}

	if len(request.Device.DeviceName) > 0 {
		deviceInfo.DeviceNameMd5 = md5_utils.GetMd5String(request.Device.DeviceName)
	}

	return deviceInfo

}

func (impl *TianMeiDspBroker) mappingOperatorType(carrier entity.OperatorType) int {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaTelecom:
		return 2
	case entity.OperatorTypeTietong:
		return 2
	case entity.OperatorTypeChinaUnicom:
		return 3
	default:
		return 0
	}
}

func (impl *TianMeiDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionTypeCellular:
		return 3
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 0
	}
}

func (impl *TianMeiDspBroker) mappingDeviceType(deviceType entity.DeviceType) int {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypePc:
		return 3
	case entity.DeviceTypeOtt:
		return 4
	default:
		return 0
	}
}

func (impl *TianMeiDspBroker) mappingOsType(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "ios"
	case entity.OsTypeAndroid:
		return "android"
	default:
		return ""
	}
}

func (impl *TianMeiDspBroker) mappingSlotType(s entity.SlotType) int {
	switch s {
	case entity.SlotTypeBanner:
		return 2
	case entity.SlotTypeOpening:
		return 3
	case entity.SlotTypePopup:
		return 4
	case entity.SlotTypeFeeds:
		return 1
	case entity.SlotTypeRewardVideo:
		return 6
	case entity.SlotTypeVideo:
		return 5
	default:
		return 1
	}
}

func (impl *TianMeiDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("TianMeiDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &tianmei_dsp_entity.BidResponse{}

	resBody, err := impl.ParseJsonHttpResponse(resp, data, response)
	if err != nil {
		zap.L().Error("TianMeiDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := json.Marshal(response)
		zap.L().Info("TianMeiDspBroker raw reponse1", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	if response.Code != 200 || response.Data == nil {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	candidateAd := &entity.Ad{
		DspId: impl.GetDspId(),
	}

	resBid := response.Data

	candidateAd.AppInfo = &entity.AppInfo{
		PackageName:    resBid.PackageName,
		AppName:        resBid.AppName,
		Icon:           resBid.AppIcon,
		WechatExt:      nil,
		AppID:          "",
		AppVersion:     resBid.AppVersion,
		PackageSize:    int(resBid.AppSize),
		Privacy:        resBid.AppPrivacy,
		Permission:     resBid.AppPermissionURL,
		PermissionDesc: nil,
		AppDesc:        resBid.AppDescription,
		AppDescURL:     "",
		Develop:        resBid.AppDeveloper,
	}

	if resBid.AppletSourceID != "" && resBid.AppletPath != "" {
		candidateAd.AppInfo.WechatExt = &entity.WechatExt{
			ProgramId:   resBid.AppletSourceID,
			ProgramPath: resBid.AppletPath,
		}
	}

	candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

	//todo
	candidateCreative := impl.ParseCreativeData(resBid)
	if candidateCreative == nil {
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
	candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
	candidate.SetBidPrice(uint32(resBid.Money))
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetCreative(candidateCreative)
	candidate.SetDspProtocol(impl.GetDspProtocol())
	result = append(result, candidate)

	return result, nil
}

func (impl *TianMeiDspBroker) chargePriceEncoder(chargePrice uint32) string {
	//明文替换
	result, err := impl.PriceManager.PlaintextEncode(uint64(chargePrice))
	//result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *TianMeiDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *tianmei_dsp_entity.Data) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: bid.CMUrl,
		ClickMonitorList:      bid.MUrl,
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkUrl:           bid.DeeplinkURL,
		LandingUrl:            bid.CUrl,
		DownloadUrl:           bid.AppStoreLink,
		DeepLinkMonitorList:   bid.DeeplinkMUrl,
	}

	if bid.InteractionType == 2 {
		tracking.LandingAction = entity.LandingTypeDownload
	} else if bid.InteractionType == 3 {
		tracking.LandingAction = entity.LandingTypeDeepLink
	}

	if len(bid.ReportStartdown) > 0 {
		tracking.AppDownloadStartedMonitorList = append(tracking.AppDownloadStartedMonitorList, bid.ReportStartdown...)
	}

	if len(bid.ReportDownsucc) > 0 {
		tracking.AppDownloadFinishedMonitorList = append(tracking.AppDownloadFinishedMonitorList, bid.ReportDownsucc...)
	}

	if len(bid.ReportStartinstall) > 0 {
		tracking.AppInstallStartMonitorList = append(tracking.AppInstallStartMonitorList, bid.ReportStartinstall...)
	}

	if len(bid.ReportInstallsucc) > 0 {
		tracking.AppInstalledFinishMonitorList = append(tracking.AppInstalledFinishMonitorList, bid.ReportInstallsucc...)
	}

	if len(bid.ReportAppactive) > 0 {
		tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, bid.ReportAppactive...)
	}

	if len(bid.ReportVideoClose) > 0 {
		tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, bid.ReportVideoClose...)
	}

	if len(bid.ReportVideoPlay) > 0 {
		tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, bid.ReportVideoPlay...)
	}

	return tracking
}

func (impl *TianMeiDspBroker) ParseCreativeData(bid *tianmei_dsp_entity.Data) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        "",
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	return creative
}
