package topon_dsp_broker

import (
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/topon_broker/topon_broker_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"gitlab.com/dev/heidegger/library/utils/vast_utils"
	"io"
	"net/http"
	"strconv"
	"strings"
)

type TopOnDspBroker struct {
	dsp_broker.DspBrokerBase
	dspSlotRegister *TopOnSlotRegister
	log             *zap.Logger
	macroInfo       macro_builder.MonitorMacroInfo
}

func NewTopOnDspBroker(dspId utils.ID) *TopOnDspBroker {
	return &TopOnDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		dspSlotRegister: NewTopOnSlotRegister(dspId),
		log:             zap.L().With(zap.String("broker", "TopOnDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "${AUCTION_PRICE}",
			MacroClickDownX: "{__DOWN_X__}",
			MacroClickDownY: "{__DOWN_Y__}",
			MacroClickUpX:   "{__UP_X__}",
			MacroClickUpY:   "{__UP_Y__}",
			MacroHWSld:      "__SLD__",
		},
	}
}

func (t *TopOnDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := t.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(dspSlot.Id)
	candidate.SetDspSlotKey(slotId)

	bidRequest := &topon_broker_proto.BidRequest{
		Id: request.GetRequestId(),
		Site: &topon_broker_proto.BidRequest_Site{
			Page: request.Url,
			Ref:  request.Referer,
		},
		App: &topon_broker_proto.BidRequest_App{
			Id:     mappingAppId(trafficData.GetOsType()),
			Name:   request.App.AppName,
			Ver:    request.App.AppVersion,
			Bundle: request.App.AppBundle,
		},
		Device: &topon_broker_proto.BidRequest_Device{
			Geo: &topon_broker_proto.BidRequest_Geo{
				Lat:     trafficData.GetGeoLatitude(),
				Lon:     trafficData.GetGeoLongitude(),
				Country: "CN",
			},
			Ua:             trafficData.GetUserAgent(),
			Devicetype:     mappingDeviceType(trafficData.GetDeviceType()),
			Make:           trafficData.GetBrand(),
			Model:          trafficData.GetModel(),
			Os:             mappingOs(trafficData.GetOsType()),
			Osv:            trafficData.GetOsVersion(),
			Hwv:            request.Device.HardwareMachineCode,
			Ip:             trafficData.GetRequestIp(),
			W:              trafficData.GetScreenWidth(),
			H:              trafficData.GetScreenHeight(),
			Ppi:            request.Device.PPI,
			Language:       "zh",
			Carrier:        mappingCarrier(trafficData.GetOperatorType()),
			Connectiontype: mappingConnectionType(trafficData.GetConnectionType()),
			Ifa:            trafficData.GetIdfa(),
			Didmd5:         trafficData.GetMd5Imei(),
			Dpidmd5:        trafficData.GetMd5AndroidId(),
			Macmd5:         trafficData.GetMd5Mac(),
		},
		User: &topon_broker_proto.BidRequest_User{
			Id:     request.UserId,
			Gender: mappingGender(request.UserGender),
		},
		Tmax: 300,
	}

	if len(dspSlot.PkgName) > 0 {
		bidRequest.App.Bundle = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidRequest.App.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidRequest.App.Ver = dspSlot.AppVersion
	}
	if request.Device.IsIp6 {
		bidRequest.Device.Ipv6 = trafficData.GetRequestIp()
	}
	deviceExt := &topon_broker_proto.TopOnBidRequestDeviceExt{
		SysBootTime:   trafficData.GetDeviceStartupTime(),
		SysUpdateTime: trafficData.GetDeviceUpgradeTime(),
		BirthTime:     trafficData.GetDeviceInitTime(),
		Firm:          trafficData.GetRomName(),
		FirmVersion:   trafficData.GetRomVersion(),
		BootMark:      trafficData.GetBootMark(),
		UpdateMark:    trafficData.GetUpdateMark(),
		Oaid:          trafficData.GetOaid(),
		Did:           trafficData.GetImei(),
		HmsVersion:    request.Device.VercodeHms,
		AppList:       t.getInstallAppIds(t.GetDspId(), request.App.InstalledAppIds),
	}
	if trafficData.GetOsType() == entity.OsTypeAndroid {
		deviceExt.Dpid = trafficData.GetAndroidId()
	} else if trafficData.GetOsType() == entity.OsTypeIOS {
		deviceExt.Dpid = trafficData.GetIdfv()
	}
	marshal, _ := sonic.Marshal(deviceExt)
	bidRequest.Device.Ext = &topon_broker_proto.BidRequest_BidRequestDeviceExt{
		Common: string(marshal),
	}

	imp, err := t.makeImp(request, candidate, trafficData)
	if err != nil {
		return nil, err
	}
	bidRequest.Imp = []*topon_broker_proto.BidRequest_Imp{imp}

	if request.IsDebug {
		bidRequest.Test = 1
		marshal, _ := sonic.Marshal(bidRequest)
		t.log.WithField("request", string(marshal)).Info("debug request")
	}

	httpRequest, _, err := t.BuildSonicJsonHttpRequest(bidRequest)
	if err != nil {
		t.log.WithError(err).Error("BuildSonicJsonHttpRequest error")
		return nil, err
	}

	t.SampleDspBroadcastSonicJsonRequest(t.GetDspId(), dspSlot.Id, candidate, bidRequest)
	return httpRequest, nil
}

func (t *TopOnDspBroker) makeImp(request *ad_service.AdRequest, candidate *ad_service.AdCandidate,
	trafficData ad_service_entity.TrafficData) (*topon_broker_proto.BidRequest_Imp, error) {
	dspSlot := t.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	height, width := dspSlot.Height, dspSlot.Width
	if height <= 0 || width <= 0 {
		height, width = int(trafficData.GetSlotHeight()), int(trafficData.GetSlotWidth())
	}
	if (height <= 0 || width <= 0) && len(request.SlotSize) > 0 {
		height, width = int(request.SlotSize[0].Height), int(request.SlotSize[0].Width)
	}

	imp := &topon_broker_proto.BidRequest_Imp{
		Id:       request.ImpressionId,
		Tagid:    candidate.GetDspSlotKey(),
		Bidfloor: float64(candidate.GetBidFloor().Price / 100),
	}
	if len(imp.Id) == 0 {
		imp.Id = request.GetRequestId()
	}
	if request.UseHttps {
		imp.Secure = 1
	}

	switch trafficData.GetSlotType() {
	case entity.SlotTypeBanner:
		imp.Banner = &topon_broker_proto.BidRequest_Imp_Banner{
			W: int32(width),
			H: int32(height),
		}
	case entity.SlotTypeRewardVideo, entity.SlotTypeVideoOpening, entity.SlotTypeVideo:
		imp.Video = &topon_broker_proto.BidRequest_Imp_Video{
			Mimes:       []string{"video/mp4"},
			Minduration: request.VideoMinDuration,
			Maxduration: request.VideoMaxDuration,
			Protocols: []topon_broker_proto.Protocol{
				topon_broker_proto.Protocol_VAST_2_0,
				topon_broker_proto.Protocol_VAST_3_0},
			W:          int32(width),
			H:          int32(height),
			Minbitrate: request.VideoMinDuration,
			Maxbitrate: request.VideoMaxDuration,
		}
	default:
		requestNative := &topon_broker_proto.TopOnBidRequestNative{
			Native: topon_broker_proto.NativeRequest{},
		}
		id := int32(1)
		for _, key := range request.GetCreativeTemplateKeyList() {
			templateKey := creative_entity.CreativeTemplateKey(key)

			if templateKey.Title().GetRequiredCount() > 0 {
				required := int32(1)
				if templateKey.Title().IsOptional() {
					required = 0
				}
				requestNative.Native.Assets = append(requestNative.Native.Assets, &topon_broker_proto.NativeRequest_Asset{
					Id:       id,
					Required: required,
					Title: &topon_broker_proto.NativeRequest_Asset_Title{
						Len: 150,
					},
				})
				id++
			}

			if templateKey.Desc().GetRequiredCount() > 0 {
				required := int32(1)
				if templateKey.Desc().IsOptional() {
					required = 0
				}
				requestNative.Native.Assets = append(requestNative.Native.Assets, &topon_broker_proto.NativeRequest_Asset{
					Id:       id,
					Required: required,
					Data: &topon_broker_proto.NativeRequest_Asset_Data{
						Type: topon_broker_proto.DataAssetType_DESC,
						Len:  150,
					},
				})
				id++
			}

			if templateKey.Icon().GetRequiredCount() > 0 {
				required := int32(1)
				if templateKey.Image().IsOptional() {
					required = 0
				}
				requestNative.Native.Assets = append(requestNative.Native.Assets, &topon_broker_proto.NativeRequest_Asset{
					Id:       id,
					Required: required,
					Img: &topon_broker_proto.NativeRequest_Asset_Image{
						Type: topon_broker_proto.ImageAssetType_ICON,
						W:    100,
						H:    100,
					},
				})
				id++
			}

			if templateKey.Image().GetRequiredCount() > 0 {
				required := int32(1)
				if templateKey.Image().IsOptional() {
					required = 0
				}
				for range templateKey.Image().GetRequiredCount() {
					requestNative.Native.Assets = append(requestNative.Native.Assets, &topon_broker_proto.NativeRequest_Asset{
						Id:       id,
						Required: required,
						Img: &topon_broker_proto.NativeRequest_Asset_Image{
							Type: topon_broker_proto.ImageAssetType_MAIN,
							W:    int32(width),
							H:    int32(height),
						},
					})
					id++
				}
			}

			if templateKey.Video().GetRequiredCount() > 0 {
				required := int32(1)
				if templateKey.Video().IsOptional() {
					required = 0
				}
				for range templateKey.Video().GetRequiredCount() {
					requestNative.Native.Assets = append(requestNative.Native.Assets, &topon_broker_proto.NativeRequest_Asset{
						Id:       id,
						Required: required,
						Video: &topon_broker_proto.BidRequest_Imp_Video{
							Mimes:       []string{"video/mp4"},
							Minduration: request.VideoMinDuration,
							Maxduration: request.VideoMaxDuration,
							Protocols:   []topon_broker_proto.Protocol{topon_broker_proto.Protocol_VAST_3_0},
							W:           int32(width),
							H:           int32(height),
							Minbitrate:  request.VideoMinDuration,
							Maxbitrate:  request.VideoMaxDuration,
						},
					})
					id++
				}
			}

			break
		}

		marshal, _ := sonic.Marshal(requestNative)
		imp.Native = &topon_broker_proto.BidRequest_Imp_Native{
			Request: string(marshal),
			Ver:     "1.1",
		}
	}

	return imp, nil
}

func (t *TopOnDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList,
	response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if response.StatusCode != 200 {
		t.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			err_code.ErrBroadcastNoBidding.CodeStr(), err_code.ErrBroadcastNoBidding.Detail)
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil || len(data) == 0 {
		t.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			err_code.ErrBrokerResponse.CodeStr(), "read response body err")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	bidResponse := &topon_broker_proto.BidResponse{}
	_, err = t.ParseSonicJsonHttpResponse(response, data, bidResponse)
	if err != nil {
		t.log.WithError(err).Error("ParseSonicJsonHttpResponse error")
		t.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			err_code.ErrBrokerResponse.CodeStr(), "ParseSonicJsonHttpResponse err")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	if request.IsDebug {
		resBody, _ := sonic.Marshal(bidResponse)
		t.log.Infof("ParseResponse, body:%s", resBody)
	}

	t.SampleDspBroadcastResponse(t.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate,
		response.StatusCode, data)
	t.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
		strconv.Itoa(response.StatusCode), "")

	list := ad_service.DspAdCandidateList{}
	for _, seatBid := range bidResponse.Seatbid {
		if seatBid == nil {
			continue
		}
		for _, bid := range seatBid.Bid {
			if bid == nil {
				continue
			}

			creative := t.buildCreative(bid)
			if creative == nil {
				return nil, err_code.ErrBrokerResponseInternalFail
			}

			ad := &entity.Ad{
				DspId:         t.GetDspId(),
				DspSlotId:     broadcastCandidate.GetDspSlotId(),
				DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
				AdMonitorInfo: t.buildMonitor(request, bid),
			}
			if bid.Ext != nil && bid.Ext.CommonCn != nil {
				if len(bid.Ext.CommonCn.AppName) > 0 {
					ad.AppInfo = &entity.AppInfo{
						AppName:    bid.Ext.CommonCn.AppName,
						AppVersion: bid.Ext.CommonCn.AppVersion,
						Privacy:    bid.Ext.CommonCn.AppPrivacy,
						Permission: bid.Ext.CommonCn.AppPermissions,
						AppDesc:    bid.Ext.CommonCn.AppDesc,
						Develop:    bid.Ext.CommonCn.AppDeveloper,
					}
				}
				if len(bid.Ext.CommonCn.WxPath) > 0 {
					ad.AppInfo.WechatExt = &entity.WechatExt{
						ProgramPath: bid.Ext.CommonCn.WxPath,
					}
				}
			}

			adCandidate := ad_service.NewDspAdCandidateWithPool(ad)
			adCandidate.SetCreative(creative)
			adCandidate.SetBidPrice(uint32(bid.Price * 100))
			adCandidate.SetBidType(entity.BidTypeCpm)
			adCandidate.SetDspProtocol(t.GetDspProtocol())
			adCandidate.SetDspAdID(bid.Id)

			list = append(list, adCandidate)
		}
	}

	return list, nil
}

func (t *TopOnDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return t.dspSlotRegister
}

func (t *TopOnDspBroker) buildMonitor(request *ad_service.AdRequest, bid *topon_broker_proto.BidResponse_SeatBid_Bid) *entity.AdMonitorInfo {
	monitor := &entity.AdMonitorInfo{}

	monitor.AddImpressionMonitor(bid.Nurl)

	if bid.AdmNative.Link != nil {
		monitor.LandingUrl = bid.AdmNative.Link.Url
		monitor.DeepLinkUrl = bid.AdmNative.Link.Fallback
		monitor.ClickMonitorList = append(monitor.ClickMonitorList, bid.AdmNative.Link.Clicktrackers...)
	}
	for _, eventTracker := range bid.AdmNative.Eventtrackers {
		if eventTracker.Event != topon_broker_proto.EventType_EVENT_TYPE_UNKOWN && len(eventTracker.Url) > 0 {
			monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, eventTracker.Url)
		}
	}
	monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, bid.AdmNative.Imptrackers...)

	if bid.Ext != nil && bid.Ext.CommonCn != nil {
		if len(bid.Ext.CommonCn.Deeplink) > 0 {
			monitor.DeepLinkUrl = bid.Ext.CommonCn.Deeplink
			monitor.LandingAction = entity.LandingTypeDeepLink
		} else if len(bid.Ext.CommonCn.DownloadLink) > 0 {
			monitor.DownloadUrl = bid.Ext.CommonCn.DownloadLink
			monitor.LandingAction = entity.LandingTypeDownload
		} else if len(bid.Ext.CommonCn.WxPath) > 0 {
			monitor.LandingAction = entity.LandingTypeWeChatProgram
		} else {
			monitor.LandingAction = entity.LandingTypeInWebView
		}

		monitor.AppInstallStartMonitorList = append(monitor.AppInstallStartMonitorList, bid.Ext.CommonCn.ApkStartInstall...)
		monitor.AppInstalledFinishMonitorList = append(monitor.AppInstalledFinishMonitorList, bid.Ext.CommonCn.ApkInstall...)
		monitor.AppOpenMonitorList = append(monitor.AppOpenMonitorList, bid.Ext.CommonCn.ApkActive...)
		monitor.DeepLinkMonitorList = append(monitor.DeepLinkMonitorList, bid.Ext.CommonCn.DpSucc...)
		monitor.DeepLinkFailedMonitorList = append(monitor.DeepLinkFailedMonitorList, bid.Ext.CommonCn.DpInstFail...)
		monitor.DeepLinkFailedMonitorList = append(monitor.DeepLinkFailedMonitorList, bid.Ext.CommonCn.DpUninstFail...)

		if request.GetSlotType() == entity.SlotTypeVideo ||
			request.GetSlotType() == entity.SlotTypeVideoOpening ||
			request.GetSlotType() == entity.SlotTypeRewardVideo {
			monitor.VideoStartUrlList = append(monitor.VideoStartUrlList, bid.Ext.CommonCn.ApkDlStar...)
			monitor.VideoCloseUrlList = append(monitor.VideoCloseUrlList, bid.Ext.CommonCn.ApkDlEnd...)
		} else {
			monitor.AppDownloadStartedMonitorList = append(monitor.AppDownloadStartedMonitorList, bid.Ext.CommonCn.ApkDlStar...)
			monitor.AppDownloadFinishedMonitorList = append(monitor.AppDownloadFinishedMonitorList, bid.Ext.CommonCn.ApkDlEnd...)
		}
	}

	if len(monitor.ImpressionMonitorList) > 0 {
		monitor.ImpressionMonitorList = t.macroInfo.MacroReplaceList(monitor.ImpressionMonitorList)
	}
	if len(monitor.ClickMonitorList) > 0 {
		monitor.ClickMonitorList = t.macroInfo.MacroReplaceList(monitor.ClickMonitorList)
	}
	if len(monitor.DeepLinkMonitorList) > 0 {
		monitor.DeepLinkMonitorList = t.macroInfo.MacroReplaceList(monitor.DeepLinkMonitorList)
	}

	return monitor
}

func (t *TopOnDspBroker) buildCreative(bid *topon_broker_proto.BidResponse_SeatBid_Bid) *entity.Creative {
	creative := &entity.Creative{
		CreativeKey: bid.Crid,
	}
	if len(bid.Crid) == 0 {
		creative.CreativeKey = bid.Cid
	}

	if len(bid.Adm) > 0 && bid.AdmNative == nil {
		responseNative := &topon_broker_proto.TopOnBidResponseNative{Native: topon_broker_proto.NativeResponse{}}
		if strings.Contains(bid.Adm, "<VAST version=\"") {
			vast, err := vast_utils.XmlUnmarshal([]byte(bid.Adm))
			if err != nil || vast == nil {
				t.log.WithError(err).Error("buildCreative vast XmlUnmarshal err")
				return nil
			}

			for _, ad := range vast.Ads {
				if ad == nil || ad.InLine == nil {
					continue
				}

				title := &entity.Material{
					MaterialType: entity.MaterialTypeTitle,
					Data:         ad.InLine.AdTitle.Value,
				}
				if len(title.Data) == 0 {
					title.Data = "点击查看详情"
				}
				creative.MaterialList = append(creative.MaterialList, title)

				desc := &entity.Material{
					MaterialType: entity.MaterialTypeDesc,
					Data:         ad.InLine.Description.Value,
				}
				if len(desc.Data) == 0 {
					desc.Data = "点击查看详情"
				}
				creative.MaterialList = append(creative.MaterialList, desc)

				for _, impression := range ad.InLine.Impression {
					if impression == nil {
						continue
					}
					responseNative.Native.Imptrackers = append(responseNative.Native.Imptrackers, impression.URI)
				}

				link := &topon_broker_proto.NativeResponse_Link{}
				responseNative.Native.Link = link
				if bid.Ext == nil || bid.Ext.CommonCn == nil {
					bid.Ext = &topon_broker_proto.BidResponse_SeatBid_Bid_Ext{
						CommonCn: &topon_broker_proto.BidResponse_SeatBid_Bid_CommonCN{},
					}
				}
				for _, ct := range ad.InLine.Creatives.Creative {
					if ct == nil {
						continue
					}

					if ct.Linear != nil {
						if ct.Linear.Icons.Icon != nil && len(ct.Linear.Icons.Icon.StaticResource.Value) > 0 {
							creative.MaterialList = append(creative.MaterialList, &entity.Material{
								MaterialType: entity.MaterialTypeIcon,
								Height:       100,
								Width:        100,
								Url:          ct.Linear.Icons.Icon.StaticResource.Value,
							})
						}

						for _, file := range ct.Linear.MediaFiles.MediaFile {
							if file != nil && len(file.URI) > 0 {
								creative.MaterialList = append(creative.MaterialList, &entity.Material{
									MaterialType: entity.MaterialTypeVideo,
									Height:       int32(file.Height),
									Width:        int32(file.Width),
									Duration:     float64(ct.Linear.Duration),
									Url:          file.URI,
								})
							}
						}

						if ct.Linear.VideoClicks != nil {
							// 视频里面点击是deeplink
							if ct.Linear.VideoClicks.ClickThrough != nil {
								link.Fallback = ct.Linear.VideoClicks.ClickThrough.URI
							}
							for _, value := range ct.Linear.VideoClicks.ClickTracking {
								if len(value.Value) > 0 {
									link.Clicktrackers = append(link.Clicktrackers, value.Value)
								}
							}
						}

						for _, event := range ct.Linear.TrackingEvents.Tracking {
							if event == nil {
								continue
							}
							switch event.Event {
							case "start":
								// 临时替换，方便后续使用
								bid.Ext.CommonCn.ApkDlStar = append(bid.Ext.CommonCn.ApkDlStar, event.URI)
							case "close":
								// 临时替换，方便后续使用
								bid.Ext.CommonCn.ApkDlEnd = append(bid.Ext.CommonCn.ApkDlEnd, event.URI)
							}
						}
					}

					for _, companionAd := range ct.CompanionAds.CompanionAd {
						if companionAd.StaticResource != nil && len(companionAd.StaticResource.URI) > 0 {
							creative.MaterialList = append(creative.MaterialList, &entity.Material{
								MaterialType: entity.MaterialTypeImage,
								Height:       int32(companionAd.Height),
								Width:        int32(companionAd.Width),
								Url:          companionAd.StaticResource.URI,
							})
						}
						// 封面里面点击是落地页
						if companionAd.CompanionClickThrough != nil && len(companionAd.CompanionClickThrough.URI) > 0 {
							link.Url = companionAd.CompanionClickThrough.URI
						}
						for _, clickTracking := range companionAd.CompanionClickTracking {
							if len(clickTracking.Value) > 0 {
								link.Clicktrackers = append(link.Clicktrackers, clickTracking.Value)
							}
						}
					}
				}
				break
			}
		} else {
			err := sonic.Unmarshal([]byte(bid.Adm), responseNative)
			if err != nil {
				t.log.WithError(err).Error("buildCreative Unmarshal admNative err")
				return nil
			}
		}
		bid.AdmNative = &responseNative.Native
	}

	if bid.AdmNative == nil {
		return nil
	}

	for _, asset := range bid.AdmNative.Assets {
		if asset == nil {
			continue
		}

		title := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
		}
		if asset.Title != nil {
			title.Data = asset.Title.Text
		}
		if len(title.Data) == 0 {
			title.Data = "点击查看详情"
		}
		creative.MaterialList = append(creative.MaterialList, title)

		desc := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
		}
		if asset.Data != nil &&
			(asset.Data.Type == topon_broker_proto.DataAssetType_DESC ||
				asset.Data.Type == topon_broker_proto.DataAssetType_DESC2) {
			desc.Data = asset.Data.Value
		}
		if len(desc.Data) == 0 {
			desc.Data = "点击查看详情"
		}
		creative.MaterialList = append(creative.MaterialList, desc)

		if asset.Img != nil {
			switch asset.Img.Type {
			case topon_broker_proto.ImageAssetType_ICON:
				creative.MaterialList = append(creative.MaterialList, &entity.Material{
					MaterialType: entity.MaterialTypeIcon,
					Url:          asset.Img.Url,
					Width:        asset.Img.W,
					Height:       asset.Img.H,
				})
			case topon_broker_proto.ImageAssetType_LOGO:
				creative.MaterialList = append(creative.MaterialList, &entity.Material{
					MaterialType: entity.MaterialTypeLogo,
					Url:          asset.Img.Url,
					Width:        asset.Img.W,
					Height:       asset.Img.H,
				})
			case topon_broker_proto.ImageAssetType_MAIN:
				creative.MaterialList = append(creative.MaterialList, &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          asset.Img.Url,
					Width:        asset.Img.W,
					Height:       asset.Img.H,
				})
			}
		}
	}

	return creative
}

func mappingAppId(osType entity.OsType) string {
	switch osType {
	case entity.OsTypeIOS:
		return "a67c7e8148e8df"
	default:
		return "a67c7e82eaab0a"
	}
}

func mappingOs(osType entity.OsType) string {
	switch osType {
	case entity.OsTypeIOS:
		return "iOS"
	default:
		return "Android"
	}
}

func (t *TopOnDspBroker) getInstallAppIds(dspId utils.ID, appIds []int) []string {
	result := make([]string, 0)
	if len(appIds) == 0 {
		return result
	}

	dspIdStr := dspId.String()
	for _, appid := range appIds {
		key := dspIdStr + "_" + type_convert.GetAssertString(appid)
		externalMapping := t.ExternalMappingLoader.GetDspAppMappingMapByKey(key)
		if externalMapping != nil {
			result = append(result, externalMapping.SourceValue)
		}
	}

	return result
}

func mappingGender(gender entity.UserGenderType) string {
	switch gender {
	case entity.UserGenderMan:
		return "M"
	case entity.UserGenderWoman:
		return "F"
	default:
		return "O"
	}
}

func mappingDeviceType(deviceType entity.DeviceType) topon_broker_proto.DeviceType {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return topon_broker_proto.DeviceType_MOBILE
	case entity.DeviceTypePc:
		return topon_broker_proto.DeviceType_PERSONAL_COMPUTER
	case entity.DeviceTypeOtt:
		return topon_broker_proto.DeviceType_CONNECTED_TV
	default:
		return topon_broker_proto.DeviceType_DEVICE_TYPE_UNKOWN
	}
}

func mappingCarrier(operatorType entity.OperatorType) string {
	switch operatorType {
	case entity.OperatorTypeChinaMobile:
		return "46000"
	case entity.OperatorTypeChinaUnicom:
		return "46001"
	case entity.OperatorTypeChinaTelecom:
		return "46003"
	default:
		return ""
	}
}

func mappingConnectionType(connectionType entity.ConnectionType) topon_broker_proto.ConnectionType {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return topon_broker_proto.ConnectionType_ETHERNET
	case entity.ConnectionTypeWifi:
		return topon_broker_proto.ConnectionType_WIFI
	case entity.ConnectionType2G:
		return topon_broker_proto.ConnectionType_CELL_2G
	case entity.ConnectionType3G:
		return topon_broker_proto.ConnectionType_CELL_3G
	case entity.ConnectionType4G:
		return topon_broker_proto.ConnectionType_CELL_4G
	case entity.ConnectionType5G, entity.ConnectionTypeCellular:
		return topon_broker_proto.ConnectionType_CELL_UNKNOWN
	default:
		return topon_broker_proto.ConnectionType_CONNECTION_UNKNOWN
	}
}
