package yunjing_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type YunjingSlotSlotInfo struct {
	*entity.DspSlotInfo
	TaskId   int
	Version  string
	VendorId string
}

func (info *YunjingSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.TaskId, err = dspSlotInfo.ExtraData.GetInt("task_id")
	if err != nil {
		return fmt.Errorf("get task_id from extra_data failed, err: %v", err)
	}

	info.Version, err = dspSlotInfo.ExtraData.GetString("version")
	if err != nil {
		return fmt.Errorf("get version from extra_data failed, err: %v", err)
	}

	info.VendorId, err = dspSlotInfo.ExtraData.GetString("vendor_id")
	if err != nil {
		return fmt.Errorf("get vendor_id from extra_data failed, err: %v", err)
	}

	return nil
}

type YunjingDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*YunjingSlotSlotInfo
}

func NewYunjingDspSlotRegister(dspId utils.ID) *YunjingDspSlotRegister {
	return &YunjingDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*YunjingSlotSlotInfo),
	}
}

func (r *YunjingDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *YunjingDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*YunjingSlotSlotInfo)
	for _, slot := range list {
		qihangSlot := &YunjingSlotSlotInfo{}
		if err := qihangSlot.Init(slot); err != nil {
			zap.L().Error("[YunjingDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = qihangSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *YunjingDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *YunjingDspSlotRegister) GetSlotInfo(slotId utils.ID) *YunjingSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
