package yunjing_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/simple_kv_data"
	"net/http"
	"testing"
	"time"
)

func GetBroker() *YunjingDspBroker {
	dspId := utils.ID(1)
	dspSlotId := utils.ID(1)

	broker := NewYunjingDspBroker(dspId, "http://localhost:8080")
	err := broker.GetDspSlotRegister().UpdateDspSlotInfo([]*entity.DspSlotInfo{
		{
			Id:            dspSlotId,
			Name:          "test",
			DspIds:        []int32{int32(dspId)},
			Target:        entity.DspSlotTarget{},
			AndroidSlotId: "test_android",
			IosSlotId:     "test_ios",
			ExtraData: simple_kv_data.SimpleKeyValue{
				"task_id":   123,
				"version":   "1.0.0",
				"vendor_id": "bb9",
			},
		},
	})
	if err != nil {
		panic(err)
	}

	return broker
}

func GetCandidate() *ad_service.AdCandidate {

	adRequest := ad_service.CreateAdRequest(nil, nil)
	adRequest.SlotWidth = 640
	adRequest.SlotHeight = 960
	adRequest.Device.UserAgent = "Mozilla/5.0 (Linux; Android 10; YOK-AN10 Build/HONORYOR-AN10; wv) AppleWebKit/537.36 (KHTML, like Gecko) AppVersion/4.0 Chrome/88.0.4324.93 Mobile Safari/537.36"
	adRequest.Device.Oaid = "f49afa0a-1082-4c6d-abcb-3b382ea12b3a"
	adRequest.Device.Imei = "f49afa0a-1082-4c6d-abcb-3b382ea12b3a"
	adRequest.Device.Mac = "5DD75B979BD77553AF9901F1B90B46AB"
	adRequest.Device.Model = "YOK-AN10"
	adRequest.Device.DeviceType = entity.DeviceTypePad
	adRequest.Device.OsVersion = "3.1.2"
	adRequest.Device.OperatorType = entity.OperatorTypeChinaMobile
	adRequest.Device.RequestIp = "*********"
	adRequest.App.AppName = "快看漫画"
	adRequest.App.AppBundle = "com.kuaikan.comic"

	ad := &entity.Ad{}

	adCandidate := adRequest.Response.AddCandidates(ad)
	adCandidate.SetDspSlotId(1)

	return adCandidate
}

func GetHttpClient() *http.Client {
	httpClient := &http.Client{
		Transport: &http.Transport{
			MaxIdleConns:          50,
			MaxIdleConnsPerHost:   50,
			MaxConnsPerHost:       50,
			IdleConnTimeout:       1000 * time.Millisecond,
			ResponseHeaderTimeout: time.Millisecond * 1000,
		},
		Timeout: time.Millisecond * 1000,
	}
	return httpClient
}

func TestYunjingDspBroker_DoSend(t *testing.T) {
	broker := GetBroker()
	adCandidate := GetCandidate()
	httpClient := GetHttpClient()

	httpRequest, err := broker.BuildRequest([]*ad_service.AdCandidate{adCandidate})
	if err != nil {
		zap.L().Fatal("BuildRequest", zap.Error(err))
	}

	resp, err := httpClient.Do(httpRequest)
	if err != nil {
		zap.L().Fatal("httpClient Do", zap.Error(err))
	}
	defer resp.Body.Close()

	candidateList, err := broker.ParseResponse(resp)
	if err != nil {
		zap.L().Fatal("ParseResponse", zap.Error(err))
	}
	zap.L().Info("candidateList", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))
}

func BenchmarkYunjingDspBroker_BuildRequest(b *testing.B) {
	broker := GetBroker()
	adCandidate := GetCandidate()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		httpRequest, err := broker.BuildRequest([]*ad_service.AdCandidate{adCandidate})
		if err != nil {
			zap.L().Fatal("BuildRequest", zap.Error(err))
		}

		httpRequest.Body.Close()
	}
}
