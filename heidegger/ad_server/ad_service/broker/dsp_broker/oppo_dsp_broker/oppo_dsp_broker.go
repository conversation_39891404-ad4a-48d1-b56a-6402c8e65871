package oppo_dsp_broker

import (
	"fmt"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/oppo_dsp_broker/oppo_dsp_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"io"
	"net/http"
	"strings"
)

var (
	ErrRequestParam       = fmt.Errorf("code:1047, request param error")
	ErrRequestAppId       = fmt.Errorf("code:1014, app id not found")
	ErrRequestPkgName     = fmt.Errorf("code:1015, pkg name not found")
	ErrRequestPosId       = fmt.Errorf("code:1033, pos id not found")
	ErrRequestPosType     = fmt.Errorf("code:1029, pos type not found")
	ErrRequestOrientation = fmt.Errorf("code:1031, err orientation")
	ErrRequestDeviceId    = fmt.Errorf("code:1051, err device id")
	ErrRequestPosInfo     = fmt.Errorf("code:1110, err pos info")
)

type OppoDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *OppoDspSlotRegister

	MacroWinPrice string
}

func NewOppoDspBroker(dspId utils.ID) *OppoDspBroker {
	return &OppoDspBroker{
		slotRegister:  NewOppoDspSlotRegister(dspId),
		MacroWinPrice: "$swp$",
	}
}

func (impl *OppoDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *OppoDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	dspRequest := oppo_dsp_broker_entity.AcquireOPPORequest()
	defer oppo_dsp_broker_entity.ReleaseOPPORequest(dspRequest)

	dspRequest.ApiVersion = 1
	dspRequest.ApiVc = 108
	dspRequest.AppStoreVc = 5500

	if dspSlot.ApiVc != 0 {
		dspRequest.ApiVc = dspSlot.ApiVc
	}
	if dspSlot.AppStoreVc != 0 {
		dspRequest.AppStoreVc = dspSlot.AppStoreVc
	}

	if err := impl.buildRequestAppInfo(request, candidate, trafficData, dspSlot, dspRequest); err != nil {
		return nil, err_code.ErrBroadcastRequestBuildFail.Wrap(err)
	}

	if err := impl.buildRequestPosInfo(request, candidate, trafficData, dspSlot, dspRequest); err != nil {
		return nil, err_code.ErrBroadcastRequestBuildFail.Wrap(err)
	}

	if err := impl.buildRequestDevInfo(request, candidate, trafficData, dspSlot, dspRequest); err != nil {
		return nil, err_code.ErrBroadcastRequestBuildFail.Wrap(err)
	}

	if err := impl.buildRequestUserInfo(request, candidate, trafficData, dspSlot, dspRequest); err != nil {
		return nil, err_code.ErrBroadcastRequestBuildFail.Wrap(err)
	}

	req, _, err := impl.BuildSonicJsonHttpRequest(dspRequest)
	if err != nil {
		zap.L().Error("OppoDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}

	if request.IsDebug {
		requestBody, _ := sonic.Marshal(dspRequest)
		zap.L().Info("OppoDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(requestBody))))))
	}

	impl.SampleDspBroadcastSonicJsonRequest(impl.GetDspId(), dspSlot.Id, candidate, dspRequest)
	return req, nil
}

func (impl *OppoDspBroker) buildRequestAppInfo(
	request *ad_service.AdRequest,
	candidate *ad_service.AdCandidate,
	trafficData ad_service_entity.TrafficData,
	dspSlot *OppoSlotSlotInfo,
	dspRequest *oppo_dsp_broker_entity.OPPORequest) error {
	data := &dspRequest.AppInfo

	data.AppId = dspSlot.AppId
	data.Pkgname = trafficData.GetAppBundle()
	data.VerName = trafficData.GetAppVersion()

	if len(dspSlot.AppVersion) > 0 {
		data.VerName = dspSlot.AppVersion
	}

	if len(dspSlot.PkgName) > 0 {
		data.Pkgname = dspSlot.PkgName
	}

	if len(dspSlot.AppVersion) > 0 {
		data.VerName = dspSlot.AppVersion
	}

	return nil
}

func (impl *OppoDspBroker) buildRequestPosInfo(
	request *ad_service.AdRequest,
	candidate *ad_service.AdCandidate,
	trafficData ad_service_entity.TrafficData,
	dspSlot *OppoSlotSlotInfo,
	dspRequest *oppo_dsp_broker_entity.OPPORequest) error {
	data := &dspRequest.PosInfo

	data.Id = dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	data.PosType = oppo_dsp_broker_entity.ToOPPOPosType(trafficData.GetSlotType())

	data.W = int32(trafficData.GetSlotWidth())
	data.H = int32(trafficData.GetSlotHeight())
	if dspSlot.Width != 0 {
		data.W = int32(dspSlot.Width)
		data.H = int32(dspSlot.Height)
	}

	if dspSlot.SlotType != 0 {
		data.PosType = dspSlot.SlotType
	}

	return nil
}

func (impl *OppoDspBroker) buildRequestDevInfo(
	request *ad_service.AdRequest,
	candidate *ad_service.AdCandidate,
	trafficData ad_service_entity.TrafficData,
	dspSlot *OppoSlotSlotInfo,
	dspRequest *oppo_dsp_broker_entity.OPPORequest) error {

	data := &dspRequest.DevInfo
	data.Imei = trafficData.GetImei()
	data.ImeiMd5 = trafficData.GetMd5Imei()
	data.OaId = trafficData.GetOaid()
	data.VaId = ""
	data.Ua = trafficData.GetUserAgent()
	data.Mac = trafficData.GetMac()
	data.MacMd5 = trafficData.GetMd5Mac()
	data.AnId = trafficData.GetAndroidId()
	data.ColorOsv = trafficData.GetOsVersion()
	data.Romv = trafficData.GetRomVersion()
	data.AnVer = trafficData.GetOsVersion()
	data.W = trafficData.GetScreenWidth()
	data.H = trafficData.GetScreenHeight()
	data.Density = float64(trafficData.GetScreenDensity())
	data.ConnectionType = oppo_dsp_broker_entity.ToOPPOConnectionType(trafficData.GetConnectionType())
	data.Brand = trafficData.GetBrand()
	data.Model = trafficData.GetModel()
	data.BootMark = trafficData.GetBootMark()
	data.UpdateMark = trafficData.GetUpdateMark()

	if trafficData.GetGeoLatitude() != 0 {
		data.GpsInfo = &oppo_dsp_broker_entity.OPPORequestGPS{
			Lat: float32(trafficData.GetGeoLatitude()),
			Lon: float32(trafficData.GetGeoLongitude()),
		}
	}

	return nil
}

func (impl *OppoDspBroker) buildRequestUserInfo(
	request *ad_service.AdRequest,
	candidate *ad_service.AdCandidate,
	trafficData ad_service_entity.TrafficData,
	dspSlot *OppoSlotSlotInfo,
	dspRequest *oppo_dsp_broker_entity.OPPORequest) error {
	data := &dspRequest.User
	data.InstalledAppPkgList = request.App.InstalledApp
	return nil
}

func (impl *OppoDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastResponseStatusFail
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	dspResponse := oppo_dsp_broker_entity.AcquireOPPOResponse()
	defer oppo_dsp_broker_entity.ReleaseOPPOResponse(dspResponse)

	resBody, err := impl.ParseSonicJsonHttpResponse(resp, data, dspResponse)
	if err != nil {
		zap.L().Error("OppoDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := sonic.Marshal(dspResponse)
		zap.L().Info("OppoDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	if dspResponse.Code != 0 {
		switch dspResponse.Code {
		case 1003:
			return nil, err_code.ErrBroadcastNoBidding
		case 1047:
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrRequestParam)
		case 1014:
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrRequestAppId)
		case 1015:
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrRequestPkgName)
		case 1033:
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrRequestPosId)
		case 1029:
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrRequestPosType)
		case 1031:
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrRequestOrientation)
		case 1051:
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrRequestDeviceId)
		case 1110:
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(ErrRequestPosInfo)
		default:
			return nil, err_code.ErrBroadcastResponseStatusFail.Wrap(fmt.Errorf("code:%d, msg:%s", dspResponse.Code, dspResponse.Msg))
		}
	}

	if len(dspResponse.AdList) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	trafficData := broadcastCandidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	result := make(ad_service.DspAdCandidateList, 0)
	for idx := range dspResponse.AdList {
		candidate, err := impl.ParseResponseAd(request, broadcastCandidate, trafficData, dspSlot, &dspResponse.AdList[idx])
		if err != nil {
			return nil, err
		}
		result = append(result, candidate)
	}

	return result, nil
}

func (impl *OppoDspBroker) ParseResponseAd(request *ad_service.AdRequest, broadcastCandidate *ad_service.AdCandidate, trafficData ad_service_entity.TrafficData, slotInfo *OppoSlotSlotInfo, ad *oppo_dsp_broker_entity.OPPOResponseAdInfo) (*ad_service.DspAdCandidate, error) {
	candidateAd := &entity.Ad{
		DspId:      impl.GetDspId(),
		DspSlotId:  broadcastCandidate.GetDspSlotId(),
		DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		AdMonitorInfo: &entity.AdMonitorInfo{
			LandingUrl:    ad.TargetUrl,
			DeepLinkUrl:   ad.DeepLink,
			LandingAction: entity.LandingTypeInWebView,
		},
	}

	if ad.App != nil {
		candidateAd.AppInfo = &entity.AppInfo{
			PackageName: ad.App.AppPackage,
			AppName:     ad.App.AppName,
			Icon:        ad.App.AppIconUrl,
			AppVersion:  ad.App.VersionName,
			PackageSize: int(ad.App.ApkSize),
			Privacy:     ad.App.PrivacyUrl,
			Permission:  ad.App.PermissionUrl,
			AppDescURL:  ad.App.AppDescUrl,
			Develop:     ad.App.DeveloperName,
		}
	}

	video25 := 0
	video50 := 0
	video75 := 0
	videoDuration := ad.VideoDuration / 1000
	if videoDuration > 0 {
		video25 = int(videoDuration) / 4
		video50 = int(videoDuration) / 2
		video75 = int(videoDuration) * 75 / 100
	}

	for _, tracking := range ad.TrackingList {
		switch tracking.TrackingEvent {
		case 1:
			candidateAd.AdMonitorInfo.AddClickMonitorList(tracking.TrackUrls)
		case 2:
			for _, url := range tracking.TrackUrls {
				url = strings.ReplaceAll(url, "$br$", "0")
				url = strings.ReplaceAll(url, impl.MacroWinPrice, "__DSPWPRICE__")
				candidateAd.AdMonitorInfo.AddImpressionMonitor(url)
			}
		case 5:
			for _, url := range tracking.TrackUrls {
				url = strings.ReplaceAll(url, "$br$", "0")
				url = strings.ReplaceAll(url, impl.MacroWinPrice, "__DSPWPRICE__")
				candidateAd.AdMonitorInfo.AddWinNoticeUrl(url)
			}
		case 10010:
			candidateAd.AdMonitorInfo.VideoStartUrlList = tracking.TrackUrls
		case 10011:
			candidateAd.AdMonitorInfo.VideoCloseUrlList = tracking.TrackUrls
		case 10012:
			if video25 != 0 {
				for _, item := range tracking.TrackUrls {
					candidateAd.AdMonitorInfo.DelayMonitorUrlList = append(candidateAd.AdMonitorInfo.DelayMonitorUrlList, entity.AdDelayMonitor{
						Url:   item,
						Delay: video25,
					})
				}
			}
		case 10013:
			if video50 != 0 {
				for _, item := range tracking.TrackUrls {
					candidateAd.AdMonitorInfo.DelayMonitorUrlList = append(candidateAd.AdMonitorInfo.DelayMonitorUrlList, entity.AdDelayMonitor{
						Url:   item,
						Delay: video50,
					})
				}
			}
		case 10014:
			if video75 != 0 {
				for _, item := range tracking.TrackUrls {
					candidateAd.AdMonitorInfo.DelayMonitorUrlList = append(candidateAd.AdMonitorInfo.DelayMonitorUrlList, entity.AdDelayMonitor{
						Url:   item,
						Delay: video75,
					})
				}
			}
		}

	}

	if ad.ContentType == 1 {
		candidateAd.AdMonitorInfo.LandingAction = entity.LandingTypeDeepLink
	} else if ad.ContentType == 1 && len(candidateAd.AdMonitorInfo.DeepLinkUrl) != 0 {
		candidateAd.AdMonitorInfo.LandingAction = entity.LandingTypeDeepLink
	} else {
		candidateAd.AdMonitorInfo.LandingAction = entity.LandingTypeInWebView
	}

	candidateCreative := impl.ParseCreativeData(ad, trafficData, slotInfo)
	if candidateCreative == nil {
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
	candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoderAesEcb)
	candidate.SetBidPrice(uint32(ad.Price))
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetCreative(candidateCreative)
	candidate.SetDspAdID(ad.AdId)
	candidate.SetDspProtocol(impl.GetDspProtocol())

	return candidate, nil
}

func (impl *OppoDspBroker) chargePriceEncoderAesEcb(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *OppoDspBroker) ParseCreativeData(ad *oppo_dsp_broker_entity.OPPOResponseAdInfo, trafficData ad_service_entity.TrafficData, slotInfo *OppoSlotSlotInfo) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        ad.AdId,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	width := int32(trafficData.GetSlotWidth())
	height := int32(trafficData.GetSlotHeight())
	if slotInfo.Width != 0 {
		width = int32(slotInfo.Width)
		height = int32(slotInfo.Height)
	}

	switch ad.CreativeType {
	case 6:
		width = 640
		height = 320
	case 7:
		width = 320
		height = 210
	case 8:
		width = 320
		height = 210
	}

	for _, file := range ad.FileList {
		if file.FileType == 1 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          file.Url,
				Width:        width,
				Height:       height,
			}

			creative.MaterialList = append(creative.MaterialList, material)
		} else if file.FileType == 2 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          file.Url,
				Duration:     float64(ad.VideoDuration / 1000),
				Width:        width,
				Height:       height,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if len(ad.LogoFile.Url) != 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          ad.LogoFile.Url,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(ad.Title) != 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         ad.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(ad.Desc) != 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         ad.Desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative

}
