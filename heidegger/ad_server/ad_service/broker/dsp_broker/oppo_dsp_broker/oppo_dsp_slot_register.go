package oppo_dsp_broker

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type OppoSlotSlotInfo struct {
	*entity.DspSlotInfo
	ApiVc      int    `json:"api_vc"`
	AppStoreVc int    `json:"app_store_vc"`
	AppId      string `json:"app_id"`
	Width      int    `json:"width,omitempty"`  // 可以不填
	Height     int    `json:"height,omitempty"` // 可以不填
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`
	SlotType   int    `json:"slot_type"`
}

func (info *OppoSlotSlotInfo) DumpPrettyJson() string {
	result, _ := json.MarshalIndent(info, "", "    ")
	return string(result)
}

func (info *OppoSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {

	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.AppId, err = dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get app_id from extra_data failed, err: %v", err)
	}

	info.ApiVc, err = dspSlotInfo.ExtraData.GetInt("api_vc")
	if err != nil {
	}

	info.AppStoreVc, err = dspSlotInfo.ExtraData.GetInt("app_store_vc")
	if err != nil {
	}

	//1=banner;2=插屏;4=开屏;8=原生;64=激励视频
	info.SlotType, err = dspSlotInfo.ExtraData.GetInt("slot_type")
	if err != nil {

	}

	return nil
}

type OppoDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*OppoSlotSlotInfo
}

func NewOppoDspSlotRegister(dspId utils.ID) *OppoDspSlotRegister {
	return &OppoDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*OppoSlotSlotInfo),
	}
}

func (r *OppoDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *OppoDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*OppoSlotSlotInfo)
	for _, slot := range list {
		dspSlot := &OppoSlotSlotInfo{}
		if err := dspSlot.Init(slot); err != nil {
			zap.L().Error("[OppoDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = dspSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *OppoDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *OppoDspSlotRegister) GetSlotInfo(slotId utils.ID) *OppoSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
