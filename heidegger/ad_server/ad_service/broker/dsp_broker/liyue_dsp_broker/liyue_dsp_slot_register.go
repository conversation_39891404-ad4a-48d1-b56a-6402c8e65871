package liyue_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type LiYueSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height  int    `json:"height"`
	Width   int    `json:"width"`
	AppKey  string `json:"app_key"`
	PkgName string `json:"pkg_name"`
	AppName string `json:"app_name"`
}

func (info *LiYueSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {

	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	info.AppKey, err = dspSlotInfo.ExtraData.GetString("app_key")
	if err != nil {
		return fmt.Errorf("get app_key from extra_data failed, err: %v", err)
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	return nil
}

type LiYueDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*LiYueSlotSlotInfo
}

func NewLiYueDspSlotRegister(dspId utils.ID) *LiYueDspSlotRegister {
	return &LiYueDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*LiYueSlotSlotInfo),
	}
}

func (r *LiYueDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *LiYueDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*LiYueSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &LiYueSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[LiYueDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *LiYueDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *LiYueDspSlotRegister) GetSlotInfo(slotId utils.ID) *LiYueSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
