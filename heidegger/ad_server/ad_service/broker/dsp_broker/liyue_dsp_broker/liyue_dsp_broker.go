package liyue_dsp_broker

import (
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/liyue_dsp_broker/liyue_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/price_coder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"strings"
	"time"
)

type LiYueDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *LiYueDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string
	MacroAction   string
}

func NewLiYueDspBroker(dspId utils.ID) *LiYueDspBroker {
	return &LiYueDspBroker{
		slotRegister:  NewLiYueDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "$WL_PRICE$",
		MacroAction:   "__EVENT_TYPE__",
	}
}

func (impl *LiYueDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *LiYueDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("LiYueDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("LiYueDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))

		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("LiYueDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}
	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidFloor := candidate.GetBidFloor()

	deviceId, _ := trafficData.GetDeviceIdWithType()

	lyRequest := &liyue_broker_entity.BidRequest{
		PID:       slotId,
		Timestamp: time.Now().UnixMilli(),
		ClientIP:  trafficData.GetRequestIp(),
		UserAgent: trafficData.GetUserAgent(),
		Device:    impl.encodeDevice(request, trafficData),
		App: liyue_broker_entity.App{
			AppKey:     dspSlot.AppKey,
			Bundle:     trafficData.GetAppBundle(),
			AppName:    trafficData.GetAppName(),
			AppVersion: trafficData.GetAppVersion(),
			SupportDPL: 1,
		},
		Geo: liyue_broker_entity.Geo{
			Lat: trafficData.GetGeoLatitude(),
			Lon: trafficData.GetGeoLongitude(),
		},
		User: liyue_broker_entity.User{
			ID:  deviceId,
			Age: int(request.UserAge),
		},
		Price: int64(bidFloor.Price),
	}

	if request.UserGender == entity.UserGenderMan {
		lyRequest.User.Gender = "M"
	} else if request.UserGender == entity.UserGenderWoman {
		lyRequest.User.Gender = "F"
	}

	if len(dspSlot.PkgName) > 0 {
		lyRequest.App.Bundle = dspSlot.PkgName
	}

	if len(dspSlot.AppName) > 0 {
		lyRequest.App.AppName = dspSlot.AppName
	}

	reqbody, err := easyjson.Marshal(lyRequest)
	if err != nil {
		zap.L().Error("LiYueDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err_code.ErrBroadcastRequestBuildFail
	}

	iv := md5_utils.GetMd5String(request.GetRequestId())

	encData := impl.encryptRequest(reqbody, iv)
	if len(encData) == 0 {
		zap.L().Error("LiYueDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err_code.ErrBroadcastRequestBuildFail
	}

	encRequest := &liyue_broker_entity.EncryptRequest{
		Data:   encData,
		IV:     iv,
		AppKey: dspSlot.AppKey,
	}

	req, _, err := impl.BuildEasyJsonHttpRequest(encRequest)
	if err != nil {
		zap.L().Error("LiYueDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}
	req.Header.Set("user-agent", "Apache-HttpClient/4.5.12 (Java/1.8.0_101)")

	if request.IsDebug {
		zap.L().Info("LiYueDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(reqbody))))))
	}

	impl.SampleDspBroadcastRequest(impl.dspId, dspSlot.Id, candidate, reqbody)
	return req, nil
}

func (impl *LiYueDspBroker) encryptRequest(reqBody []byte, iv string) string {
	ikey, _ := hex.DecodeString(iv)
	ekey, _ := hex.DecodeString(impl.GetEKey())

	enCodeData, err := price_coder.AesCBCPkcs5PaddingEncrypt(reqBody, ekey, ikey)
	if err != nil {
		zap.L().Error("LiYueDspBroker encryptRequest AesCBCPkcs5PaddingEncrypt err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return ""
	}

	return hex.EncodeToString(enCodeData)
}

func (impl *LiYueDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) liyue_broker_entity.Device {
	deviceInfo := liyue_broker_entity.Device{
		OS:              impl.mappingOsType(trafficData.GetOsType()),
		OSVersion:       trafficData.GetOsVersion(),
		Carrier:         impl.mappingCarrier(trafficData.GetOperatorType()),
		Network:         impl.mappingConnectionType(trafficData.GetConnectionType()),
		Density:         type_convert.GetAssertString(trafficData.GetScreenDensity()),
		AID:             trafficData.GetAndroidId(),
		OAID:            trafficData.GetOaid(),
		IMEI:            trafficData.GetImei(),
		AAID:            trafficData.GetAaid(),
		Mac:             trafficData.GetMac(),
		Idfa:            trafficData.GetIdfa(),
		Idfv:            trafficData.GetIdfv(),
		OpenUdid:        trafficData.GetOpenUdid(),
		Orientation:     impl.mappingOrientation(trafficData.GetScreenOrientation()),
		Vendor:          trafficData.GetBrand(),
		Model:           trafficData.GetModel(),
		Language:        "zh-CN",
		BootMark:        trafficData.GetBootMark(),
		UpdateMark:      trafficData.GetUpdateMark(),
		AppstoreVersion: request.Device.VercodeAg,
		HMSVersion:      request.Device.VercodeHms,
		StartupTime:     trafficData.GetUpdateMark(),
		MDTime:          trafficData.GetDeviceUpgradeTime(),
		BirthTime:       trafficData.GetBootMark(),
		Paid:            request.Device.Paid,
	}

	if trafficData.GetScreenOrientation() == entity.ScreenOrientationTypeUnknown {
		if trafficData.GetScreenWidth() < trafficData.GetScreenHeight() {
			deviceInfo.Orientation = 0
		} else {
			deviceInfo.Orientation = 1
		}

	}

	if len(trafficData.GetCaid()) > 0 {
		deviceInfo.CAID = device_utils.GetCaidRaw(trafficData.GetCaid())
		deviceInfo.CAIDVersion = device_utils.GetCaidVersion(trafficData.GetCaid())
	}

	if trafficData.GetScreenWidth() > 0 && trafficData.GetScreenHeight() > 0 {
		deviceInfo.Resolution = fmt.Sprintf("%d*%d", trafficData.GetScreenWidth(), trafficData.GetScreenHeight())
	}

	if len(deviceInfo.MDTime) == 0 {
		deviceInfo.MDTime = trafficData.GetUpdateMark()
	}

	return deviceInfo
}

func (impl *LiYueDspBroker) mappingOrientation(s entity.ScreenOrientationType) int {
	switch s {
	case entity.ScreenOrientationTypeLandscape:
		return 1
	case entity.ScreenOrientationTypePortrait:
		return 0
	}

	return 0
}

func (impl *LiYueDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionTypeCellular:
		return 3
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 0
	}
}

func (impl *LiYueDspBroker) mappingCarrier(carrier entity.OperatorType) int {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaTelecom:
		return 3
	case entity.OperatorTypeTietong:
		return 3
	case entity.OperatorTypeChinaUnicom:
		return 2
	default:
		return 0
	}
}

func (impl *LiYueDspBroker) mappingOsType(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "iOS"
	case entity.OsTypeAndroid:
		return "Android"
	default:
		return ""
	}
}

func (impl *LiYueDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("LiYueDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &liyue_broker_entity.AdResponse{}

	resBody, err := impl.ParseJsonHttpResponse(resp, data, response)
	//err = easyjson.Unmarshal(data, response)
	if err != nil {
		zap.L().Error("LiYueDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}
	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := json.Marshal(response)
		zap.L().Info("LiYueDspBroker raw reponse1", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	if response.Status != 1000 || len(response.Data) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, resBid := range response.Data {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = &entity.AppInfo{
			PackageName: resBid.PackageName,
			AppName:     resBid.AppName,
			Icon:        resBid.AppInfo.AppIconURL,
			AppVersion:  resBid.AppInfo.AppVersion,
			PackageSize: resBid.AppInfo.Size,
			Privacy:     resBid.AppInfo.PrivacyURL,
			Permission:  resBid.AppInfo.PermissionURL,
			AppDescURL:  resBid.AppInfo.AppIntro,
			Develop:     resBid.AppInfo.Developer,
		}

		if len(candidateAd.AppInfo.AppName) == 0 {
			candidateAd.AppInfo.AppName = resBid.AppInfo.AppName
		}

		if len(candidateAd.AppInfo.PackageName) == 0 {
			candidateAd.AppInfo.PackageName = resBid.AppInfo.PackageName
		}

		if resBid.WxMpId != "" && resBid.WxMpPath != "" {
			candidateAd.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   resBid.WxMpId,
				ProgramPath: resBid.WxMpPath,
			}
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

		candidateCreative := impl.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)

		price := resBid.Price
		if price > 100000 {
			price = 100000
		}

		candidate.SetBidPrice(uint32(price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(resBid.ID)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break

	}

	return result, nil
}

func (impl *LiYueDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *LiYueDspBroker) ParseCreativeData(bid liyue_broker_entity.Ad) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.ID,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	item := bid

	if len(item.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         item.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Desc) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         item.Desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.IconURL) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          item.IconURL,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	for _, image := range item.ImageUrls {
		if len(image) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          image,
				Width:        int32(item.Width),
				Height:       int32(item.Height),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if len(item.VideoUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          item.VideoUrl,
			Width:        int32(item.Width),
			Height:       int32(item.Height),
			Duration:     item.VideoDuration,
		}
		creative.MaterialList = append(creative.MaterialList, material)

		if len(item.CoverUrl) > 0 {
			material1 := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          item.CoverUrl,
				Width:        int32(item.Width),
				Height:       int32(item.Height),
			}
			creative.MaterialList = append(creative.MaterialList, material1)
		}

	}

	return creative

}

func (impl *LiYueDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid liyue_broker_entity.Ad) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      bid.ClickTrackUrls,
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkUrl:           bid.DeepLinkUrl,
		LandingUrl:            bid.ClickUrl,
	}

	if bid.ActionType == 2 {
		tracking.LandingAction = entity.LandingTypeDownload
	} else if bid.ActionType == 3 {
		tracking.LandingAction = entity.LandingTypeWeChatProgram
	}

	for _, impUrl := range bid.ViewTrackUrls {
		if strings.Contains(impUrl, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impUrl, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impUrl)
		}
	}

	if len(bid.VideoGeneralEventTrackings) > 0 {
		for _, videoTrack := range bid.VideoGeneralEventTrackings {

			videoStart := strings.ReplaceAll(videoTrack, impl.MacroAction, "200")

			tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, videoStart)

			videoClose := strings.ReplaceAll(videoTrack, impl.MacroAction, "205")
			tracking.VideoCloseUrlList = append(tracking.VideoStartUrlList, videoClose)
		}
	}

	if len(bid.DownloadStartTrackUrls) > 0 {
		tracking.AppDownloadStartedMonitorList = append(tracking.AppDownloadStartedMonitorList, bid.DownloadStartTrackUrls...)
	}

	if len(bid.DownloadSuccessTrackUrls) > 0 {
		tracking.AppDownloadFinishedMonitorList = append(tracking.AppDownloadFinishedMonitorList, bid.DownloadSuccessTrackUrls...)
	}

	if len(bid.InstallStartTrackUrls) > 0 {
		tracking.AppInstallStartMonitorList = append(tracking.AppInstallStartMonitorList, bid.InstallStartTrackUrls...)
	}

	if len(bid.InstallSuccessTrackUrls) > 0 {
		tracking.AppInstalledFinishMonitorList = append(tracking.AppInstalledFinishMonitorList, bid.InstallSuccessTrackUrls...)
	}

	if len(bid.DeepLinkTrackEvents) > 0 {
		for _, dpTrack := range bid.DeepLinkTrackEvents {
			if dpTrack.Event == "open_url_app" || dpTrack.Event == "dpl_success" {
				tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, dpTrack.Url)
			}
		}
	}

	if len(bid.VideoEventTrackings) > 0 {
		for _, videoTrack := range bid.VideoEventTrackings {
			if videoTrack.Event == "start" {
				tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, videoTrack.Url)
			} else if videoTrack.Event == "complete" {
				tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, videoTrack.Url)
			}
		}
	}

	return tracking
}
