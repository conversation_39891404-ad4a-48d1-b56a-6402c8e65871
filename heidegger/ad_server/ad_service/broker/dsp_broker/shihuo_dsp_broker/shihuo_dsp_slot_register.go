package shihuo_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type ShiHuoSlotSlotInfo struct {
	*entity.DspSlotInfo
	DealType string `json:"deal_type"`
	Source   string `json:"source"`
}

func (info *ShiHuoSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.DealType, err = dspSlotInfo.ExtraData.GetString("deal_type")
	if err != nil {
	}

	info.Source, err = dspSlotInfo.ExtraData.GetString("source")
	if err != nil {
	}
	return nil
}

type ShiHuoDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*ShiHuoSlotSlotInfo
}

func NewShiHuoDspSlotRegister(dspId utils.ID) *ShiHuoDspSlotRegister {
	return &ShiHuoDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*ShiHuoSlotSlotInfo),
	}
}

func (r *ShiHuoDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *ShiHuoDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*ShiHuoSlotSlotInfo)
	for _, slot := range list {
		sSlot := &ShiHuoSlotSlotInfo{}
		if err := sSlot.Init(slot); err != nil {
			zap.L().Error("[ShiHuoDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = sSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *ShiHuoDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *ShiHuoDspSlotRegister) GetSlotInfo(slotId utils.ID) *ShiHuoSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
