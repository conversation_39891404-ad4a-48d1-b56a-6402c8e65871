package shihuo_dsp_broker

import (
	"fmt"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/shihuo_dsp_broker/shihuo_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"strconv"
)

type ShiHuoDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *ShiHuoDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string
}

func NewShiHuoDspBroker(dspId utils.ID) *ShiHuoDspBroker {
	return &ShiHuoDspBroker{
		dspId:        dspId,
		slotRegister: NewShiHuoDspSlotRegister(dspId),
	}
}

func (b *ShiHuoDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.slotRegister
}

func (b *ShiHuoDspBroker) GetDspId() utils.ID {
	return b.dspId
}

func (b *ShiHuoDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("ShiHuoDspBroker.EncodeRequest Enter")
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := b.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)

	if len(slotId) == 0 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidRequest := &shihuo_dsp_entity.ShiHuoRequest{
		Type:          slotId,
		IP:            trafficData.GetRequestIp(),
		UserAgent:     trafficData.GetUserAgent(),
		OS:            b.mappingOsType(trafficData.GetOsType()),
		OSV:           trafficData.GetOsVersion(),
		Network:       b.mappingNetworkType(trafficData.GetConnectionType()),
		IMEI:          trafficData.GetImei(),
		IDFA:          trafficData.GetIdfa(),
		OAID:          trafficData.GetOaid(),
		WebviewUA:     trafficData.GetUserAgent(),
		DeviceDensity: type_convert.GetAssertString(trafficData.GetScreenDensity()),
		Brand:         trafficData.GetBrand(),
		Model:         trafficData.GetModel(),
		ScreenWidth:   int(trafficData.GetScreenWidth()),
		ScreenHeight:  int(trafficData.GetScreenHeight()),
		DealType:      dspSlot.DealType,
		Caid:          trafficData.GetCaid(),
		SysMemory:     type_convert.GetAssertString(request.Device.SystemTotalMem),
		SysDiskSize:   type_convert.GetAssertString(request.Device.SystemTotalDisk),
		OsUpdateTime:  trafficData.GetDeviceUpgradeTime(),
		Carrier:       b.mappingCarrier(trafficData.GetOperatorType()),
		Source:        dspSlot.Source,
	}

	if trafficData.GetOsType() == entity.OsTypeAndroid {
		if len(bidRequest.IMEI) == 0 && len(bidRequest.OAID) == 0 {
			return nil, err_code.ErrInvalidDeviceId
		}
	}

	if trafficData.GetScreenWidth() > 0 {
		bidRequest.ScreenRatio = float64(trafficData.GetScreenHeight()) / float64(trafficData.GetScreenWidth())
		bidRequest.ScreenRatio, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", bidRequest.ScreenRatio), 64)
	}

	req, _, err := b.BuildSonicJsonHttpRequest(bidRequest)
	if err != nil {
		zap.L().Error("ShiHuoDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}

	if request.IsDebug {
		requestBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("ShiHuoDspBroker.EncodeRequest end, saxRequest", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(requestBody))))))
	}

	b.SampleDspBroadcastSonicJsonRequest(b.dspId, dspSlot.Id, candidate, bidRequest)

	return req, nil

}

func (b *ShiHuoDspBroker) mappingOsType(s entity.OsType) string {
	switch s {
	case entity.OsTypeIOS:
		return "ios"
	case entity.OsTypeAndroid:
		return "Android"
	default:
		return "unknown"
	}
}

func (b *ShiHuoDspBroker) mappingNetworkType(s entity.ConnectionType) int32 {
	switch s {
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (b *ShiHuoDspBroker) mappingCarrier(s entity.OperatorType) int {
	switch s {
	case entity.OperatorTypeChinaMobile:
		return 0
	case entity.OperatorTypeChinaUnicom:
		return 1
	case entity.OperatorTypeChinaTelecom, entity.OperatorTypeTietong:
		return 3
	}

	return 0
}

func (b *ShiHuoDspBroker) mappingOrientation(s entity.ScreenOrientationType) int32 {
	switch s {
	case entity.ScreenOrientationTypeLandscape:
		return 90
	case entity.ScreenOrientationTypePortrait:
		return 0
	default:
		return 0
	}
}

func (b *ShiHuoDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("ShiHuoDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	if request.IsDebug {
		zap.L().Info("ShiHuoDspBroker response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(data))))))
	}

	response := &shihuo_dsp_entity.ShiHuoResponse{}
	resBody, err := b.ParseSonicJsonHttpResponse(resp, data, response)
	if err != nil {
		response2 := &shihuo_dsp_entity.ShiHuoResponse2{}
		err = sonic.Unmarshal(data, response2)
		if err != nil {
			zap.L().Error("ShiHuoDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data)))), zap.Error(err))
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		return nil, err_code.ErrBroadcastNoBidding
	}

	broadcastCandidate := broadcastCandidateList[0]
	b.SampleDspBroadcastResponse(b.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)
	if request.IsDebug {
		resbody, _ := sonic.Marshal(response)
		zap.L().Info("ShiHuoDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	if response.Status != 0 || len(response.Data) < 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	candidateList := make(ad_service.DspAdCandidateList, 0)
	for _, ad := range response.Data {

		tracking := &entity.AdMonitorInfo{
			ImpressionMonitorList: make([]string, 0),
			ClickMonitorList:      make([]string, 0),
			LandingUrl:            ad.ClickThroughURL,
			DeepLinkUrl:           ad.DeeplinkURL,
			LandingAction:         entity.LandingTypeInWebView,
		}

		if len(ad.DeeplinkURL) > 0 {
			tracking.LandingAction = entity.LandingTypeDeepLink
		}

		if len(ad.ExposureURL) > 0 {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, ad.ExposureURL)
		}

		if len(ad.ExposureURLs) > 0 {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, ad.ExposureURLs...)

		}

		if len(ad.ClickTrackingURL) > 0 {
			tracking.ClickMonitorList = append(tracking.ClickMonitorList, ad.ClickTrackingURL)
		}

		if len(ad.ClickTrackingURLs) > 0 {
			tracking.ClickMonitorList = append(tracking.ClickMonitorList, ad.ClickTrackingURLs...)
		}

		if len(tracking.ImpressionMonitorList) == 0 && len(tracking.ClickMonitorList) == 0 {
			return nil, err_code.ErrBroadcastNoBidding
		}

		candidateAd := &entity.Ad{
			DspId:         b.GetDspId(),
			DspSlotId:     broadcastCandidate.GetDspSlotId(),
			DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
			AdMonitorInfo: tracking,
		}

		candidateCreative := &entity.Creative{
			MaterialList: make(entity.MaterialList, 0),
		}

		if ad.Img != "" {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          ad.Img,
			}
			candidateCreative.MaterialList = append(candidateCreative.MaterialList, material)
		}

		if ad.Video != "" {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          ad.Video,
			}
			candidateCreative.MaterialList = append(candidateCreative.MaterialList, material)
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(b.chargePriceEncoder)
		candidate.SetBidPrice(uint32(100))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspProtocol(b.GetDspProtocol())
		candidateList = append(candidateList, candidate)
		break
	}

	return candidateList, nil

}

func (b *ShiHuoDspBroker) chargePriceEncoder(chargePrice uint32) string {
	return ""
}
