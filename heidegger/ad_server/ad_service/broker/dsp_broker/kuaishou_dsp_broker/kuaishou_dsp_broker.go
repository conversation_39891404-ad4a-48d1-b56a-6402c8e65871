package kuasihou_broker

import (
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/kuaishou_dsp_broker/kuaishou_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"strings"
	"fmt"
)

type KuaiShouDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *KuaiShouDspSlotRegister
	dspId        utils.ID

	MacroWinPrice     string
	MacroWinPriceType string
	MacroAction       string
}

func NewKuaiShouDspBroker(dspId utils.ID) *KuaiShouDspBroker {
	return &KuaiShouDspBroker{
		slotRegister:      NewKuaiShouDspSlotRegister(dspId),
		dspId:             dspId,
		MacroWinPrice:     "__PR__",
		MacroWinPriceType: "__PRTYPE__",
		MacroAction:       "__ACTION__",
	}
}

func (impl *KuaiShouDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *KuaiShouDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("KuaiShouDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("KuaiShouDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))

		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("KuaiShouDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	ksRequest := &kuaishou_dsp_entity.KuaiShouDspAdRequest{
		ProtocolVersion: "1.1.22",
		AppInfo: kuaishou_dsp_entity.AppInfo{
			AppID:       dspSlot.AppId,
			Name:        trafficData.GetAppName(),
			PackageName: trafficData.GetAppBundle(),
			Version:     trafficData.GetAppVersion(),
		},
		DeviceInfo: impl.encodeDevice(request, trafficData),
		NetworkInfo: kuaishou_dsp_entity.NetworkInfo{
			Ip:             trafficData.GetRequestIp(),
			ConnectionType: impl.mappingConnectionType(trafficData.GetConnectionType()),
			OperatorType:   impl.mappingOperatorType(trafficData.GetOperatorType()),
			Mac:            trafficData.GetMac(),
			MacMd5:         trafficData.GetMd5Mac(),
		},
		ImpInfo: nil,
	}

	if len(dspSlot.AppName) > 0 {
		ksRequest.AppInfo.Name = dspSlot.AppName
	}

	if len(dspSlot.AppVersion) > 0 {
		ksRequest.AppInfo.Version = dspSlot.AppVersion
	}

	if len(dspSlot.PkgName) > 0 {
		ksRequest.AppInfo.PackageName = dspSlot.PkgName
	}

	if trafficData.GetGeoLongitude() != 0 || trafficData.GetGeoLatitude() != 0 {
		ksRequest.GeoInfo = &kuaishou_dsp_entity.GeoInfo{
			Latitude:  trafficData.GetGeoLatitude(),
			Longitude: trafficData.GetGeoLongitude(),
		}
	}

	if request.UserAge != 0 || request.UserGender != 0 {
		ksRequest.UserInfo = &kuaishou_dsp_entity.UserInfo{
			Age:    int(request.UserAge),
			Gender: "",
		}

		switch request.UserGender {
		case entity.UserGenderMan:
			ksRequest.UserInfo.Gender = "M"
		case entity.UserGenderWoman:
			ksRequest.UserInfo.Gender = "F"
		}
	}

	imp, err := impl.encodeImp(request, trafficData, candidate)
	if err != nil {
		return nil, err_code.ErrInvalidImpression

	}

	ksRequest.ImpInfo = imp
	req, _, err := impl.BuildSonicJsonHttpRequest(ksRequest)
	if err != nil {
		zap.L().Error("KuaiShouDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err_code.ErrBroadcastRequestBuildFail
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(ksRequest)
		zap.L().Info("KuaiShouDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(reqBody))))))
	}

	impl.SampleDspBroadcastSonicJsonRequest(impl.dspId, dspSlot.Id, candidate, ksRequest)

	return req, nil

}

func (impl *KuaiShouDspBroker) encodeImp(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData, candidate *ad_service.AdCandidate) ([]kuaishou_dsp_entity.ImpInfo, error) {
	imps := make([]kuaishou_dsp_entity.ImpInfo, 0)
	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return imps, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	bidFloor := candidate.GetBidFloor()

	imp := kuaishou_dsp_entity.ImpInfo{
		PosId:       type_convert.GetAssertInt64(slotId),
		AdNum:       1,
		Width:       dspSlot.Width,
		Height:      dspSlot.Height,
		Action:      0,
		Query:       "",
		AdStyle:     dspSlot.AdStyle,
		CpmBidFloor: int64(bidFloor.Price),
	}

	if imp.Width == 0 || imp.Height == 0 {
		if len(request.SlotSize) > 0 {
			imp.Width = int(request.SlotSize[0].Width)
			imp.Height = int(request.SlotSize[0].Height)
		}
	}

	imps = append(imps, imp)
	return imps, nil
}

func (impl *KuaiShouDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) kuaishou_dsp_entity.DeviceInfo {
	deviceInfo := kuaishou_dsp_entity.DeviceInfo{
		Idfa:      trafficData.GetIdfa(),
		Imei:      trafficData.GetImei(),
		ImeiMd5:   trafficData.GetMd5Imei(),
		Oaid:      trafficData.GetOaid(),
		OsType:    impl.mappingOsType(trafficData.GetOsType()),
		OsVersion: trafficData.GetOsVersion(),
		Language:  "zh",
		//AppPackageName:          nil,
		ScreenWidth:             int64(trafficData.GetScreenWidth()),
		ScreenHeight:            int64(trafficData.GetScreenHeight()),
		AndroidId:               trafficData.GetAndroidId(),
		AndroidIdMd5:            trafficData.GetMd5AndroidId(),
		DeviceModel:             trafficData.GetModel(),
		DeviceBrand:             trafficData.GetBrand(),
		DeviceVendor:            trafficData.GetModel(),
		Platform:                impl.mappingDeviceType(trafficData.GetDeviceType(), trafficData.GetOsType()),
		DeviceNameMd5:           md5_utils.GetMd5String(trafficData.GetModel()),
		PhysicalMemoryKBytes:    0,
		HardDiskSizeKBytes:      0,
		Country:                 "CHN",
		TimeZone:                "GMT+0800",
		SystemUpdateTimeNanoSec: request.Device.UpdateMark,
		SystemBootTimeMilliSec:  request.Device.BootMark,
		DeviceFileTime:          request.Device.BootMark,
	}

	if deviceInfo.DeviceVendor == "" {
		deviceInfo.DeviceVendor = "unknown"
	}

	return deviceInfo

}

func (impl *KuaiShouDspBroker) mappingOsType(os entity.OsType) int64 {
	switch os {
	case entity.OsTypeIOS:
		return 2
	case entity.OsTypeAndroid:
		return 1
	default:
		return 0
	}
}

func (impl *KuaiShouDspBroker) mappingDeviceType(deviceType entity.DeviceType, os entity.OsType) int {
	switch deviceType {
	case entity.DeviceTypeMobile:
		if os == entity.OsTypeAndroid {
			return 3
		} else {
			return 1
		}
	case entity.DeviceTypePad:
		if os == entity.OsTypeAndroid {
			return 4
		} else {
			return 2
		}
	default:
		return 1
	}
}

func (impl *KuaiShouDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 101
	case entity.ConnectionTypeWifi:
		return 100
	case entity.ConnectionTypeCellular:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (impl *KuaiShouDspBroker) mappingOperatorType(carrier entity.OperatorType) int {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaTelecom:
		return 2
	case entity.OperatorTypeTietong:
		return 2
	case entity.OperatorTypeChinaUnicom:
		return 3
	default:
		return 0
	}
}

func (impl *KuaiShouDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("KuaiShouDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &kuaishou_dsp_entity.KuaiShouDspAdResponse{}
	resBody, err := impl.ParseSonicJsonHttpResponse(resp, data, response)
	if err != nil {
		zap.L().Error("KuaiShouDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := sonic.Marshal(response)
		zap.L().Info("KuaiShouDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	if response.Result != 1 || len(response.ImpAdInfo) < 1 || len(response.ImpAdInfo[0].AdInfo) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, resBid := range response.ImpAdInfo[0].AdInfo {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = &entity.AppInfo{
			PackageName: resBid.AdBaseInfo.AppPackageName,
			AppName:     resBid.AdBaseInfo.AppName,
			Icon:        resBid.AdBaseInfo.AppIconUrl,
			AppVersion:  resBid.AdBaseInfo.AppVersion,
			PackageSize: int(resBid.AdBaseInfo.PackageSize),
			Develop:     resBid.AdBaseInfo.CorporationName,
		}

		if resBid.DownloadSafeInfo != nil {
			candidateAd.AppInfo.Privacy = resBid.DownloadSafeInfo.AppPrivacyUrl
			candidateAd.AppInfo.Permission = resBid.DownloadSafeInfo.AppPermissionInfoUrl
			candidateAd.AppInfo.AppDesc = resBid.DownloadSafeInfo.IntroductionInfo
			candidateAd.AppInfo.AppDescURL = resBid.DownloadSafeInfo.IntroductionInfoUrl
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

		candidateCreative := impl.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(resBid.AdBaseInfo.Ecpm))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(type_convert.GetAssertString(resBid.AdBaseInfo.AccountId))
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break

	}

	return result, nil

}

func (impl *KuaiShouDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *KuaiShouDspBroker) ParseCreativeData(bid kuaishou_dsp_entity.AdInfo) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        type_convert.GetAssertString(bid.AdBaseInfo.CreativeId),
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	item := bid.AdBaseInfo

	if len(item.AdDescription) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         item.AdDescription,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.AdActionDescription) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         item.AdActionDescription,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.AppIconUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          item.AppIconUrl,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)

	}

	for _, resM := range bid.AdMaterialInfo.MaterialFeature {
		if resM.FeatureType == 1 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          resM.MaterialUrl,
				Duration:     float64(resM.VideoDuration),
				Width:        int32(resM.VideoWidth),
				Height:       int32(resM.VideoHeight),
			}
			creative.MaterialList = append(creative.MaterialList, material)

			coverUrl := resM.CoverUrl
			if len(coverUrl) == 0 {
				coverUrl = resM.FirstFrame
			}

			if len(coverUrl) > 0 {
				material1 := &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          coverUrl,
					Width:        int32(resM.VideoWidth),
					Height:       int32(resM.VideoHeight),
				}
				creative.MaterialList = append(creative.MaterialList, material1)
			}

		} else if resM.FeatureType == 2 {

			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          resM.MaterialUrl,
				Width:        int32(resM.VideoWidth),
				Height:       int32(resM.VideoHeight),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	return creative

}

func (impl *KuaiShouDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid kuaishou_dsp_entity.AdInfo) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      make([]string, 0),
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkUrl:           bid.AdConversionInfo.DeeplinkUrl,
		LandingUrl:            bid.AdConversionInfo.H5Url,
		H5LandingUrl:          bid.AdConversionInfo.H5Url,
		DownloadUrl:           bid.AdConversionInfo.AppDownloadUrl,
	}

	if bid.AdBaseInfo.AdOperationType == 1 {
		tracking.LandingAction = entity.LandingTypeDownload
		if len(tracking.DownloadUrl) > 0 {
			tracking.LandingUrl = tracking.DownloadUrl
		}
	}

	if len(tracking.LandingUrl) == 0 {
		if len(bid.AdConversionInfo.MarketUrl) > 0 {
			tracking.LandingUrl = bid.AdConversionInfo.MarketUrl
		} else if len(bid.AdConversionInfo.KwaiLandingPageUrl) > 0 {
			tracking.LandingUrl = bid.AdConversionInfo.KwaiLandingPageUrl
		}
	}

	if len(bid.AdBaseInfo.ShowUrl) != 0 {
		if strings.Contains(bid.AdBaseInfo.ShowUrl, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(bid.AdBaseInfo.ShowUrl, impl.MacroWinPrice, "__DSPWPRICE__")
			newImpTrack = strings.ReplaceAll(newImpTrack, impl.MacroWinPriceType, "encrypt")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.AdBaseInfo.ShowUrl)
		}
	}

	if len(bid.AdBaseInfo.ClickUrl) != 0 {
		tracking.ClickMonitorList = append(tracking.ClickMonitorList, bid.AdBaseInfo.ClickUrl)
	}

	for _, track := range bid.AdTrackInfo {
		if track.Type == 1 {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, track.Url...)
		} else if track.Type == 2 {
			tracking.ClickMonitorList = append(tracking.ClickMonitorList, track.Url...)
		}
	}

	if len(bid.AdBaseInfo.ConvUrl) > 0 {
		if strings.Contains(bid.AdBaseInfo.ConvUrl, impl.MacroAction) {
			winActionTrack := strings.ReplaceAll(bid.AdBaseInfo.ConvUrl, impl.MacroAction, "600")
			winActionTrack = strings.ReplaceAll(winActionTrack, impl.MacroWinPrice, "__DSPWPRICE__")
			winActionTrack = strings.ReplaceAll(winActionTrack, impl.MacroWinPriceType, "encrypt")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, winActionTrack)

			if bid.AdBaseInfo.AdOperationType == 1 {
				startDownload := strings.ReplaceAll(bid.AdBaseInfo.ConvUrl, impl.MacroAction, "30")
				tracking.AppDownloadStartedMonitorList = append(tracking.AppDownloadFinishedMonitorList, startDownload)

				startFinish := strings.ReplaceAll(bid.AdBaseInfo.ConvUrl, impl.MacroAction, "31")
				tracking.AppDownloadFinishedMonitorList = append(tracking.AppDownloadFinishedMonitorList, startFinish)

				installFinish := strings.ReplaceAll(bid.AdBaseInfo.ConvUrl, impl.MacroAction, "32")
				tracking.AppInstalledFinishMonitorList = append(tracking.AppInstalledFinishMonitorList, installFinish)
			}

			if bid.AdMaterialInfo.MaterialType == 1 {
				videoStart := strings.ReplaceAll(bid.AdBaseInfo.ConvUrl, impl.MacroAction, "399")
				tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, videoStart)

				videoClose := strings.ReplaceAll(bid.AdBaseInfo.ConvUrl, impl.MacroAction, "400")
				tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, videoClose)

				delay3 := strings.ReplaceAll(bid.AdBaseInfo.ConvUrl, impl.MacroAction, "21")
				tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   delay3,
					Delay: 3,
				})

				delay5 := strings.ReplaceAll(bid.AdBaseInfo.ConvUrl, impl.MacroAction, "22")
				tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   delay5,
					Delay: 5,
				})

			}
		}
	}

	return tracking

}
