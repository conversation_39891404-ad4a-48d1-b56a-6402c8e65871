package kuasihou_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type KuaiShouSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	AppVersion string `json:"app_version"`
	PkgName    string `json:"pkg_name"`
	AppId      string `json:"app_id"`
	//1:信息流、2:激励视频、3:全屏 视频、4:开屏、6:draw 视 频、13:插屏视频
	AdStyle int `json:"ad_style"`
}

func (info *KuaiShouSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error
	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	info.AppId, err = dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get app_id from extra_data failed, err: %v", err)

	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	adType, err := dspSlotInfo.ExtraData.GetString("ad_type")
	if err != nil {
		return fmt.Errorf("get ad_type from extra_data failed, err: %v", err)
	}
	//1:信息流、2:激励视频、3:全屏 视频、4:开屏、6:draw 视 频、13:插屏视频
	switch adType {
	case "native":
		info.AdStyle = 1
	case "rvideo":
		info.AdStyle = 2
	case "video":
		info.AdStyle = 3
	case "splash":
		info.AdStyle = 4
	case "draw":
		info.AdStyle = 6
	case "insert":
		info.AdStyle = 13
	}

	return nil
}

type KuaiShouDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*KuaiShouSlotSlotInfo
}

func NewKuaiShouDspSlotRegister(dspId utils.ID) *KuaiShouDspSlotRegister {
	return &KuaiShouDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*KuaiShouSlotSlotInfo),
	}
}

func (r *KuaiShouDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *KuaiShouDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*KuaiShouSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &KuaiShouSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[BaiduDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *KuaiShouDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *KuaiShouDspSlotRegister) GetSlotInfo(slotId utils.ID) *KuaiShouSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
