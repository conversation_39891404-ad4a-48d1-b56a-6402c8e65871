package jdtg3_dsp_broker

import (
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	jdtg "gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jdtg3_dsp_broker/jdtg3_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
)

type JdTg3DspBroker struct {
	dsp_broker.DspBrokerBase
	dspSlotRegister *JdTg3DspSlotRegister
	log             *zap.Logger
	macroInfo       macro_builder.MonitorMacroInfo
}

func NewJdTg3DspBroker(dspId utils.ID) *JdTg3DspBroker {
	return &JdTg3DspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		dspSlotRegister: NewJdTg3DspSlotRegister(dspId),
		log:             zap.L().With(zap.String("broker", "JdTg3DspBroker")),
		macroInfo:       macro_builder.MonitorMacroInfo{},
	}
}

func (j *JdTg3DspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := j.dspSlotRegister.GetSlotInfo(candidate.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(dspSlot.Id)
	candidate.SetDspSlotKey(strconv.FormatInt(int64(dspSlot.Id), 10))

	bidRequest := &jdtg.Request{
		Source: &jdtg.Request_Source{
			SourceId:     dspSlot.DealId,
			ImpressionId: dspSlot.GetDspSlotIdByOs(trafficData.GetOsType()),
			Width:        int32(dspSlot.Width),
			Height:       int32(dspSlot.Height),
		},
		Device: &jdtg.Request_Device{
			Ip:             trafficData.GetRequestIp(),
			AndroidId:      trafficData.GetAndroidId(),
			DeviceType:     mappingDeviceType(trafficData.GetDeviceType()),
			Brand:          trafficData.GetBrand(),
			Model:          trafficData.GetModel(),
			Os:             trafficData.GetOsType().String(),
			Osv:            trafficData.GetOsVersion(),
			Network:        mappingNetwork(trafficData.GetConnectionType()),
			Operator:       mappingOperator(trafficData.GetOperatorType()),
			Width:          trafficData.GetScreenWidth(),
			Height:         trafficData.GetScreenHeight(),
			PixelRatio:     trafficData.GetScreenDensity(),
			Orientation:    int32(trafficData.GetScreenOrientation()),
			TimezoneOffset: 480,
		},
	}

	if bidRequest.Source.Width == 0 || bidRequest.Source.Height == 0 {
		bidRequest.Source.Width = int32(trafficData.GetSlotWidth())
		bidRequest.Source.Height = int32(trafficData.GetSlotHeight())
	}
	if (bidRequest.Source.Width == 0 || bidRequest.Source.Height == 0) && len(request.SlotSize) > 0 {
		bidRequest.Source.Width = int32(request.SlotSize[0].Width)
		bidRequest.Source.Height = int32(request.SlotSize[0].Height)
	}

	deviceId, deviceType := trafficData.GetDeviceIdWithType()
	bidRequest.Device.IdentifierType = mappingIdentifierType(deviceType)
	bidRequest.Device.Identifier = deviceId
	bidRequest.Device.DeiviceId = deviceId

	if trafficData.GetGeoLongitude() != 0 || trafficData.GetGeoLatitude() != 0 {
		bidRequest.Device.Geo = &jdtg.Request_Device_Geo{
			Lon: trafficData.GetGeoLongitude(),
			Lat: trafficData.GetGeoLatitude(),
		}
	}
	for _, caid := range trafficData.GetCaids() {
		bidRequest.Device.CaidInfo = append(bidRequest.Device.CaidInfo, &jdtg.Request_Device_CaidInfo{
			Caid:    device_utils.GetCaidRaw(caid),
			CaidMd5: md5_utils.GetMd5String(caid),
			Version: device_utils.GetCaidVersion(caid),
		})
	}

	if request.IsDebug {
		marshal, _ := sonic.Marshal(bidRequest)
		j.log.WithField("request", string(marshal)).Info("debug request")
	}

	httpRequest, _, err := j.BuildPbHttpHttpRequest(bidRequest)
	if err != nil {
		j.log.WithError(err).Error("BuildPbHttpHttpRequest error")
		return nil, err
	}

	j.SampleDspBroadcastProtobufRequest(j.GetDspId(), dspSlot.Id, candidate, bidRequest)
	return httpRequest, nil
}

func (j *JdTg3DspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList,
	response *http.Response) (ad_service.DspAdCandidateList, error) {
	if response.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		//j.log.WithError(err).Error("Read response error")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	bidResponse := &jdtg.Response{}
	err = j.ParsePbHttpHttpResponse(response, data, bidResponse)
	if err != nil {
		j.log.WithError(err).Error("ParsePbHttpHttpResponse error")
		return nil, err
	}

	broadcastCandidate := broadcastCandidateList[0]
	j.SampleDspBroadcastProtobufResponse(j.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate,
		response.StatusCode, bidResponse)

	if request.IsDebug {
		resBody, _ := sonic.Marshal(bidResponse)
		j.log.Infof("ParseResponse, body:%s", resBody)
	}
	j.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
		strconv.FormatUint(uint64(bidResponse.Status), 10), bidResponse.Msg)

	if bidResponse.Status != 0 || bidResponse.Ad == nil {
		return nil, err_code.ErrBroadcastNoBidding
	}

	dspAdCandidate, err := j.buildDspAdCandidate(bidResponse.Ad, broadcastCandidate)
	if err != nil {
		j.log.WithError(err).Error("buildDspAdCandidate error")
		return nil, err_code.ErrBroadcastResponseCandidateError.Wrap(err)
	}
	return ad_service.DspAdCandidateList{dspAdCandidate}, nil
}

func (j *JdTg3DspBroker) buildDspAdCandidate(bid *jdtg.Response_Ad,
	broadcastCandidate *ad_service.AdCandidate) (*ad_service.DspAdCandidate, error) {
	ad := &entity.Ad{
		DspId:         j.GetDspId(),
		DspSlotId:     broadcastCandidate.GetDspSlotId(),
		DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
		AdMonitorInfo: j.buildMonitor(bid),
	}

	creative := j.buildCreative(bid, broadcastCandidate)
	if creative == nil {
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	adCandidate := ad_service.NewDspAdCandidateWithPool(ad)
	adCandidate.SetCreative(creative)
	adCandidate.SetBidType(entity.BidTypeCpm)
	adCandidate.SetDspProtocol(j.GetDspProtocol())
	adCandidate.SetDspAdID(strconv.FormatInt(int64(bid.UniqId), 10))

	return adCandidate, nil
}

func (j *JdTg3DspBroker) buildMonitor(bid *jdtg.Response_Ad) *entity.AdMonitorInfo {
	monitor := &entity.AdMonitorInfo{}

	// 并行方案
	if len(bid.JdClickUrl) > 0 {
		if len(bid.JdImpUrl) > 0 {
			monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, bid.JdImpUrl)
		}
		if len(bid.ThirdImpUrl) > 0 {
			monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, bid.ThirdImpUrl)
		}

		if len(bid.JdOpenUrl) > 0 || len(bid.JdULinkUrl) > 0 {
			monitor.DeepLinkUrl = bid.JdOpenUrl
			monitor.LandingUrl = bid.JdClickUrl
			monitor.LandingAction = entity.LandingTypeDeepLink
			if len(bid.JdULinkUrl) > 0 {
				monitor.DeepLinkUrl = bid.JdULinkUrl
			}
		} else {
			monitor.LandingUrl = bid.JdClickUrl
			monitor.H5LandingUrl = bid.JdClickUrl
			monitor.LandingAction = entity.LandingTypeInWebView
		}
		monitor.ClickMonitorList = append(monitor.ClickMonitorList, bid.JdClickUrl)
		if len(bid.ThirdClickUrl) > 0 {
			monitor.ClickMonitorList = append(monitor.ClickMonitorList, bid.ThirdClickUrl)
		}
	} else { // 串行方案
		if len(bid.ImpressionUrl) > 0 {
			monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, bid.ImpressionUrl)
		}
		if len(bid.OpenUrl) > 0 || len(bid.ULinkUrl) > 0 {
			monitor.DeepLinkUrl = bid.OpenUrl
			monitor.LandingAction = entity.LandingTypeDeepLink
			if len(bid.ULinkUrl) > 0 {
				monitor.DeepLinkUrl = bid.ULinkUrl
			}
			if len(bid.ClickUrl) > 0 {
				monitor.LandingUrl = bid.ClickUrl
				monitor.ClickMonitorList = append(monitor.ClickMonitorList, bid.ClickUrl)
			}
		} else if len(bid.ClickUrl) > 0 {
			monitor.LandingUrl = bid.ClickUrl
			monitor.H5LandingUrl = bid.ClickUrl
			monitor.LandingAction = entity.LandingTypeInWebView
			monitor.ClickMonitorList = append(monitor.ClickMonitorList, bid.ClickUrl)
		}
	}
	monitor.ClickMonitorList = append(monitor.ClickMonitorList, bid.ExtendClickUrls...)

	return monitor
}

func (j *JdTg3DspBroker) buildCreative(bid *jdtg.Response_Ad, broadcastCandidate *ad_service.AdCandidate) *entity.Creative {
	trafficData := broadcastCandidate.GetModifiedTrafficData()
	dspSlot := j.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil
	}

	creative := &entity.Creative{
		CreativeKey: strconv.FormatInt(int64(bid.UniqId), 10),
	}
	height, width := dspSlot.Height, dspSlot.Width
	request := broadcastCandidate.GetAdRequest()
	if height == 0 || width == 0 {
		height, width = int(request.SlotHeight), int(request.SlotWidth)
	}
	if (height == 0 || width == 0) && len(broadcastCandidate.GetAdRequest().SlotSize) > 0 {
		height, width = int(request.SlotSize[0].Height), int(request.SlotSize[0].Width)
	}

	title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: bid.Title}
	if len(title.Data) == 0 {
		title.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, title)

	desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: bid.SubTitle}
	if len(desc.Data) == 0 {
		desc.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, desc)

	if len(bid.SourceAvatar) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.SourceAvatar,
			Height:       100,
			Width:        100,
		})
	}

	for _, ct := range bid.Creative {
		if strings.Contains(ct.CreativeUrl, ".mp4") {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          ct.CreativeUrl,
				Height:       int32(height),
				Width:        int32(width),
				//Duration:     60, // TODO
			})
		} else {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          ct.CreativeUrl,
				Height:       int32(height),
				Width:        int32(width),
			})
		}
	}

	return creative
}

func (j *JdTg3DspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return j.dspSlotRegister
}

func mappingDeviceType(deviceType entity.DeviceType) int32 {
	switch deviceType {
	case entity.DeviceTypePad:
		return 1
	case entity.DeviceTypePc:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	default:
		return 0
	}
}

func mappingIdentifierType(deviceIdType entity.DeviceIdType) jdtg.IdentifierType {
	switch deviceIdType {
	case entity.DeviceIdTypeRawImei:
		return jdtg.IdentifierType_IMEI
	case entity.DeviceIdTypeRawOaid:
		return jdtg.IdentifierType_OAID
	case entity.DeviceIdTypeRawIdfa:
		return jdtg.IdentifierType_IDFA
	case entity.DeviceIdTypeRawMac:
		return jdtg.IdentifierType_MAC
	case entity.DeviceIdTypeRawCaid:
		return jdtg.IdentifierType_CAID
	case entity.DeviceIdTypeMd5Oaid:
		return jdtg.IdentifierType_OAID_MD5
	case entity.DeviceIdTypeMd5Caid:
		return jdtg.IdentifierType_CAID_MD5
	default:
		return jdtg.IdentifierType_OTHER
	}
}

func mappingNetwork(connectionType entity.ConnectionType) int32 {
	switch connectionType {
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	case entity.ConnectionTypeCellular:
		return 6
	case entity.ConnectionTypeNetEthernet:
		return 7
	default:
		return 0
	}
}

func mappingOperator(operatorType entity.OperatorType) int32 {
	switch operatorType {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 2
	case entity.OperatorTypeChinaTelecom:
		return 3
	default:
		return 0
	}
}
