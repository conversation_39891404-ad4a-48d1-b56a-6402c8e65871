package jdtg3_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type JdTg3DspSlotRegisterInfo struct {
	*entity.DspSlotInfo
	Height int `json:"height"`
	Width  int `json:"width"`

	DealId string `json:"deal_id"`
}

func (info *JdTg3DspSlotRegisterInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")

	dealId, err := dspSlotInfo.ExtraData.GetString("deal_id")
	if err != nil {
		return err
	}
	info.DealId = dealId

	return nil
}

type JdTg3DspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*JdTg3DspSlotRegisterInfo
}

func NewJdTg3DspSlotRegister(dspId utils.ID) *JdTg3DspSlotRegister {
	return &JdTg3DspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*JdTg3DspSlotRegisterInfo),
	}
}

func (j *JdTg3DspSlotRegister) GetDspId() utils.ID {
	return j.dspId
}

func (j *JdTg3DspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	dspSlotMap := make(map[utils.ID]*JdTg3DspSlotRegisterInfo)
	for _, slotInfo := range list {
		slot := &JdTg3DspSlotRegisterInfo{}
		if err := slot.Init(slotInfo); err != nil {
			zap.L().Error("[JdTg3DspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		dspSlotMap[slot.Id] = slot
	}

	j.dspSlotList = list
	j.dspSlotMap = dspSlotMap
	return nil
}

func (j *JdTg3DspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return j.dspSlotList
}

func (j *JdTg3DspSlotRegister) GetSlotInfo(slotId utils.ID) *JdTg3DspSlotRegisterInfo {
	return j.dspSlotMap[slotId]
}
