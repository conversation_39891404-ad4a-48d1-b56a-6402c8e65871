package kaijie_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"strings"
)

type KaiJieSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height      int      `json:"height"`
	Width       int      `json:"width"`
	MediaId     int      `json:"media_id"`
	PkgName     string   `json:"pkg_name"`
	AppName     string   `json:"app_name"`
	TemplateIds []int    `json:"template_id"`
	AdType      []string `json:"ad_type"`
}

func (info *KaiJieSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error
	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	mediaId, err := dspSlotInfo.ExtraData.GetString("media_id")
	if err != nil {
		return fmt.Errorf("get media_id from extra_data failed, err: %v", err)
	}
	info.MediaId = type_convert.GetAssertInt(mediaId)

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}
	/*
		switch adType {
			case "native":
				info.AdType = 0
			case "splash":
				info.AdType = 3
			case "rvideo":
				info.AdType = 4
			case "insert":
				info.AdType = 6

			}*/
	adType, err := dspSlotInfo.ExtraData.GetString("ad_type")
	if err != nil {
		return fmt.Errorf("get ad_type from extra_data failed, err: %v", err)
	}

	info.AdType = strings.Split(adType, ",")
	if len(info.AdType) == 0 {
		return fmt.Errorf("get adTypes from extra_data failed, err: %v", err)
	}

	templateIds, err := dspSlotInfo.ExtraData.GetString("template_id")
	if err != nil {
		return fmt.Errorf("get template_id from extra_data failed, err: %v", err)
	}
	templateIdsArr := strings.Split(templateIds, ",")
	if len(templateIdsArr) == 0 {
		return fmt.Errorf("get template_ids from extra_data failed, err: %v", err)
	}
	info.TemplateIds = make([]int, 0)
	for _, tId := range templateIdsArr {
		switch tId {
		case "imgs":
			info.TemplateIds = append(info.TemplateIds, 1)
		case "img":
			info.TemplateIds = append(info.TemplateIds, 2)
		case "bimg":
			info.TemplateIds = append(info.TemplateIds, 3)
		case "video":
			info.TemplateIds = append(info.TemplateIds, 4)
		}
	}

	if len(info.TemplateIds) == 0 {
		return fmt.Errorf("get template_ids from extra_data failed, err: %v", err)
	}

	return nil
}

type KaiJieDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*KaiJieSlotSlotInfo
}

func NewKaiJieDspSlotRegister(dspId utils.ID) *KaiJieDspSlotRegister {
	return &KaiJieDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*KaiJieSlotSlotInfo),
	}
}

func (r *KaiJieDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *KaiJieDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*KaiJieSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &KaiJieSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[KaiJieDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *KaiJieDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *KaiJieDspSlotRegister) GetSlotInfo(slotId utils.ID) *KaiJieSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
