package kaijie_dsp_broker

import (
	"encoding/json"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/kaijie_dsp_broker/kaijie_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"strings"
	"time"
	"fmt"
)

type KaiJieDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *KaiJieDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string
}

func NewKaiJieDspBroker(dspId utils.ID) *KaiJieDspBroker {
	return &KaiJieDspBroker{
		slotRegister:  NewKaiJieDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "__WIN_PRICE__",
	}
}

func (impl *KaiJieDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *KaiJieDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("KaiJieDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("KaiJieDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))

		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("KaiJieDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidFloor := candidate.GetBidFloor()

	imp := &kaijie_dsp_entity.KaiJieImp{
		Id:          request.ImpressionId,
		PosId:       slotId,
		BidFloor:    float32(bidFloor.Price),
		BidFloorCur: "CNY",
		Secure:      0,
		OpenType:    []int{0, 1, 2, 3, 4, 5},
		Isul:        true,
		IsRtb:       1,
		MediaId:     dspSlot.MediaId,
	}

	width := dspSlot.Width
	height := dspSlot.Height

	if width == 0 || height == 0 {
		if len(request.SlotSize) > 0 {
			width = int(request.SlotSize[0].Width)
			height = int(request.SlotSize[0].Height)
		}
	}

	switch dspSlot.AdType[0] {
	case "splash", "insert", "banner":
		imp.Banner = &kaijie_dsp_entity.KaiJieImpBanner{
			W:      int32(width),
			H:      int32(height),
			AdType: 0,
			Mimes:  []string{"image/jpg", "image/gif"},
		}
		if dspSlot.AdType[0] == "splash" {
			imp.Banner.AdType = 2
		} else if dspSlot.AdType[0] == "insert" {
			imp.Banner.AdType = 1
		}
	case "video", "rvideo":
		imp.Video = &kaijie_dsp_entity.KaiJieImpVideo{
			W:           int32(width),
			H:           int32(height),
			AdType:      0,
			Mimes:       []string{"video/x-ms-wmv", "video/x-flv", "video/mp4"},
			Minduration: request.VideoMinDuration,
			Maxduration: request.VideoMaxDuration,
		}

		if dspSlot.AdType[0] == "rvideo" {
			imp.Video.AdType = 2
		}
	default:
		imp.Native = &kaijie_dsp_entity.KaiJieImpNative{
			Maxtitle: 30,
			Maxdesc:  []int32{30},
			MainImg: &kaijie_dsp_entity.KaiJieImpNativeAssert{
				W:     int32(width),
				H:     int32(height),
				Mimes: []string{"image/jpg", "image/gif"},
			},
			Video:  nil,
			AdType: 3,
		}

		if len(dspSlot.TemplateIds) > 0 {
			imp.Native.AdType = dspSlot.TemplateIds[0]
			if dspSlot.TemplateIds[0] == 4 {
				imp.Native.Video = &kaijie_dsp_entity.KaiJieImpNativeAssert{
					W:           int32(width),
					H:           int32(height),
					Mimes:       []string{"video/x-ms-wmv", "video/x-flv", "video/mp4"},
					Minduration: request.VideoMinDuration,
					Maxduration: request.VideoMaxDuration,
				}
			}
		}

	}

	kjRequest := &kaijie_dsp_entity.KaiJieBidRequest{
		Id:  trafficData.GetRequestId(),
		Imp: make([]*kaijie_dsp_entity.KaiJieImp, 0),
		App: &kaijie_dsp_entity.KaiJieApp{
			Bundle:   trafficData.GetAppBundle(),
			Id:       "",
			Name:     trafficData.GetAppName(),
			Version:  trafficData.GetAppVersion(),
			StoreUrl: "",
		},
		Device: impl.encodeDevice(request, trafficData),
	}

	kjRequest.Imp = append(kjRequest.Imp, imp)

	if len(dspSlot.AppName) > 0 {
		kjRequest.App.Name = dspSlot.AppName
	}

	if len(dspSlot.PkgName) > 0 {
		kjRequest.App.Bundle = dspSlot.PkgName
	}

	requestBody, err := easyjson.Marshal(kjRequest)
	if err != nil {
		zap.L().Error("KaiJieDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err_code.ErrBroadcastRequestBuildFail
	}

	req, _, err := impl.BuildJsonHttpRequest(requestBody)
	if err != nil {
		zap.L().Error("KaiJieDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}

	if request.IsDebug {
		//requestBody, _ := json.Marshal(kjRequest)
		zap.L().Info("KaiJieDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(requestBody))))))
	}

	impl.SampleDspBroadcastEasyjsonRequest(impl.dspId, dspSlot.Id, candidate, kjRequest)
	return req, nil
}

func (impl *KaiJieDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) *kaijie_dsp_entity.KaiJieDevice {
	deviceInfo := &kaijie_dsp_entity.KaiJieDevice{
		Ua:                  trafficData.GetUserAgent(),
		Ip:                  trafficData.GetRequestIp(),
		Devicetype:          impl.mappingDeviceType(trafficData.GetDeviceType()),
		Make:                trafficData.GetBrand(),
		Model:               trafficData.GetModel(),
		Os:                  impl.mappingOsType(trafficData.GetOsType()),
		Osv:                 trafficData.GetOsVersion(),
		Imei:                trafficData.GetImei(),
		ImeiMd5:             trafficData.GetMd5Imei(),
		Oaid:                trafficData.GetOaid(),
		Idfa:                trafficData.GetIdfa(),
		IdfaMd5:             trafficData.GetMd5Idfa(),
		Androidid:           trafficData.GetAndroidId(),
		Mac:                 trafficData.GetMac(),
		Connectiontype:      impl.mappingConnectionType(trafficData.GetConnectionType()),
		Carrier:             impl.mappingCarrier(trafficData.GetOperatorType()),
		Hww:                 trafficData.GetScreenWidth(),
		Hwh:                 trafficData.GetScreenHeight(),
		Orientation:         0,
		ApiLevel:            "",
		Serialno:            "",
		Dip:                 "",
		Dpi:                 "",
		Mno:                 "",
		Paid:                request.Device.Paid,
		InitTime:            trafficData.GetDeviceInitTime(),
		BootMark:            trafficData.GetBootMark(),
		UpdateMark:          trafficData.GetUpdateMark(),
		InstalledApp:        "",
		PowerOnTime:         "",
		RomVersion:          trafficData.GetRomVersion(),
		SysCompilingTime:    "",
		OsUpdateTimeSecond:  trafficData.GetUpdateMark(),
		HardwareModel:       request.Device.HardwareMachineCode,
		Country:             "CN",
		Language:            "",
		LocalTzTime:         "",
		DeviceNameMd5:       "",
		CpuNum:              "",
		DiskTotal:           type_convert.GetAssertString(request.Device.SystemTotalDisk),
		MemTotal:            type_convert.GetAssertString(request.Device.SystemTotalMem),
		AuthStatus:          "",
		SkadnetworkVersions: "",
		Geo: &kaijie_dsp_entity.KaiJieGeo{
			Lat: float32(trafficData.GetGeoLatitude()),
			Lon: float32(trafficData.GetGeoLongitude()),
		},
		Idfv:       trafficData.GetIdfv(),
		Mnc:        "",
		Mcc:        "",
		Openudid:   trafficData.GetOpenUdid(),
		Imsi:       "",
		HwHms:      request.Device.VercodeHms,
		HwAg:       request.Device.VercodeAg,
		ClientTime: time.Now().Format("2006-01-02 15:04:05.000Z0700"),
	}

	if len(trafficData.GetCaid()) != 0 {
		deviceInfo.Caid = device_utils.GetCaidRaw(trafficData.GetCaid())
		deviceInfo.CaidVersion = device_utils.GetCaidVersion(trafficData.GetCaid())
	}

	if len(request.Device.Caids) > 0 {
		for _, caid := range request.Device.Caids {
			deviceInfo.Caids = append(deviceInfo.Caids, &kaijie_dsp_entity.KaiJieDeviceCaid{
				Id:      device_utils.GetCaidRaw(caid),
				Version: device_utils.GetCaidVersion(caid),
			})
		}
	}

	if len(request.Device.DeviceName) > 0 {
		deviceInfo.DeviceNameMd5 = md5_utils.GetMd5String(request.Device.DeviceName)
	}

	if trafficData.GetScreenOrientation() == entity.ScreenOrientationTypePortrait {
		deviceInfo.Orientation = 1
	} else if trafficData.GetScreenOrientation() == entity.ScreenOrientationTypeLandscape {
		deviceInfo.Orientation = 3
	}

	return deviceInfo
}

func (impl *KaiJieDspBroker) mappingDeviceType(s entity.DeviceType) int {
	switch s {
	case entity.DeviceTypeMobile:
		return 4
	case entity.DeviceTypePc:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	case entity.DeviceTypePad:
		return 5
	default:
		return 1
	}
}

func (impl *KaiJieDspBroker) mappingOsType(s entity.OsType) string {
	switch s {
	case entity.OsTypeAndroid:
		return "Android"
	case entity.OsTypeIOS:
		return "iOS"
	default:
		return "Android"
	}
}

func (impl *KaiJieDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionTypeCellular:
		return 6
	case entity.ConnectionType2G:
		return 3
	case entity.ConnectionType3G:
		return 4
	case entity.ConnectionType4G:
		return 5
	case entity.ConnectionType5G:
		return 6
	default:
		return 0
	}
}

func (impl *KaiJieDspBroker) mappingCarrier(carrier entity.OperatorType) int {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 0
	case entity.OperatorTypeChinaTelecom:
		return 2
	case entity.OperatorTypeTietong:
		return 2
	case entity.OperatorTypeChinaUnicom:
		return 1
	default:
		return 0
	}
}

func (impl *KaiJieDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("KaiJieDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &kaijie_dsp_entity.KaiJieBidResponse{}

	resBody, err := impl.ParseJsonHttpResponse(resp, data, response)
	if err != nil {
		zap.L().Error("KaiJieDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := json.Marshal(response)
		zap.L().Info("KaiJieDspBroker raw reponse1:  , raw body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))), zap.String("param2", fmt.Sprintf("%v", data)))
	}

	if len(response.CreativeUrl) == 0 && response.Native == nil {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	candidateAd := &entity.Ad{
		DspId:      impl.GetDspId(),
		DspSlotId:  broadcastCandidate.GetDspSlotId(),
		DspSlotKey: broadcastCandidate.GetDspSlotKey(),
	}

	candidateAd.AppInfo = &entity.AppInfo{
		PackageName:    response.AppPackage,
		AppName:        response.AppName,
		Icon:           response.AppIcon,
		WechatExt:      nil,
		AppVersion:     response.AppVersion,
		PackageSize:    int(response.AppSize),
		Privacy:        response.AppPrivacyLink,
		Permission:     response.AppPermissionLink,
		PermissionDesc: nil,
		AppDesc:        response.AppInfo,
		AppDescURL:     "",
		Develop:        response.AppPublisher,
	}

	if len(response.WxAppId) > 0 && len(response.WxAppPath) > 0 {
		candidateAd.AppInfo.WechatExt = &entity.WechatExt{
			ProgramId:   response.WxAppId,
			ProgramPath: response.WxAppPath,
		}

	}

	candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, response)

	candidateCreative := impl.ParseCreativeData(response)
	if candidateCreative == nil {
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
	candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)

	candidate.SetBidPrice(uint32(response.Price))
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetCreative(candidateCreative)
	candidate.SetDspProtocol(impl.GetDspProtocol())
	result = append(result, candidate)

	return result, nil

}

func (impl *KaiJieDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *KaiJieDspBroker) ParseCreativeData(bid *kaijie_dsp_entity.KaiJieBidResponse) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.Posid,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if len(bid.CreativeUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          bid.CreativeUrl,
			Width:        bid.W,
			Height:       bid.H,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	hasTitle := false
	hasDesc := false

	if len(bid.VideoCover) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          bid.VideoCover,
			Width:        bid.W,
			Height:       bid.H,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.VideoEndcover) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          bid.VideoEndcover,
			Width:        bid.W,
			Height:       bid.H,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.VideoIcon) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.VideoIcon,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.VideoTitle) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         bid.VideoTitle,
		}
		creative.MaterialList = append(creative.MaterialList, material)
		hasTitle = true
	}

	if len(bid.VideoContent) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         bid.VideoContent,
		}
		creative.MaterialList = append(creative.MaterialList, material)
		hasDesc = true
	}

	if bid.Native != nil {
		if len(bid.Native.MainImgUrl) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          bid.Native.MainImgUrl,
				Width:        bid.W,
				Height:       bid.H,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

		for _, img := range bid.Native.ImgUrls {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img,
				Width:        bid.W,
				Height:       bid.H,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

		if len(bid.Native.VideoUrl) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          bid.Native.VideoUrl,
				Width:        bid.W,
				Height:       bid.H,
				Duration:     float64(bid.Duration),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

		if len(bid.Native.Title) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeTitle,
				Data:         bid.Native.Title,
			}
			creative.MaterialList = append(creative.MaterialList, material)
			hasTitle = true
		}

		if len(bid.Native.Descs) > 0 {
			for _, desc := range bid.Native.Descs {
				material := &entity.Material{
					MaterialType: entity.MaterialTypeDesc,
					Data:         desc,
				}
				creative.MaterialList = append(creative.MaterialList, material)
				hasDesc = true
			}
		}

		if len(bid.Native.IconUrl) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeIcon,
				Url:          bid.Native.IconUrl,
				Width:        100,
				Height:       100,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if !hasTitle {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if !hasDesc {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}

func (impl *KaiJieDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *kaijie_dsp_entity.KaiJieBidResponse) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      make([]string, 0),
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkUrl:           bid.Deeplink,
		LandingUrl:            bid.Curl,
		DownloadUrl:           bid.DownloadUrl,
		H5LandingUrl:          bid.Curl,
	}

	if len(bid.Wurl) > 0 {
		if strings.Contains(bid.Wurl, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(bid.Wurl, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.Wurl)
		}
	}

	for _, impUrl := range bid.ImpUrls {
		if strings.Contains(impUrl, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impUrl, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impUrl)
		}
	}

	for _, clkUrl := range bid.ClickUrls {
		if strings.Contains(clkUrl, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(clkUrl, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ClickMonitorList = append(tracking.ClickMonitorList, newImpTrack)
		} else {
			tracking.ClickMonitorList = append(tracking.ClickMonitorList, clkUrl)
		}
	}

	if bid.Action == 1 {
		tracking.LandingAction = entity.LandingTypeDownload
	} else if bid.Action == 2 {
		tracking.LandingAction = entity.LandingTypeDeepLink
	}

	if len(bid.DownloadStartUrls) > 0 {
		tracking.AppDownloadStartedMonitorList = append(tracking.AppDownloadStartedMonitorList, bid.DownloadStartUrls...)
	}

	if len(bid.DownloadCompleteUrls) > 0 {
		tracking.AppDownloadFinishedMonitorList = append(tracking.AppDownloadFinishedMonitorList, bid.DownloadCompleteUrls...)
	}

	if len(bid.InstallBeginUrls) > 0 {
		tracking.AppInstallStartMonitorList = append(tracking.AppInstallStartMonitorList, bid.InstallBeginUrls...)
	}

	if len(bid.InstallCompleteUrls) > 0 {
		tracking.AppInstalledFinishMonitorList = append(tracking.AppInstalledFinishMonitorList, bid.InstallCompleteUrls...)
	}

	if len(bid.ActiveAppUrls) > 0 {
		tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, bid.ActiveAppUrls...)
	}

	if len(bid.DeeplinkMonitorUrls) > 0 {
		for _, dpUrl := range bid.DeeplinkMonitorUrls {
			if strings.Contains(dpUrl, impl.MacroWinPrice) {
				newImpTrack := strings.ReplaceAll(dpUrl, impl.MacroWinPrice, "__DSPWPRICE__")
				tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, newImpTrack)
			} else {
				tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, dpUrl)
			}
		}
	}

	if len(bid.DeeplinkClickMonitorUrls) > 0 {
		for _, dpUrl := range bid.DeeplinkClickMonitorUrls {
			if strings.Contains(dpUrl, impl.MacroWinPrice) {
				newImpTrack := strings.ReplaceAll(dpUrl, impl.MacroWinPrice, "__DSPWPRICE__")
				tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, newImpTrack)
			} else {
				tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, dpUrl)
			}
		}
	}

	if len(bid.VideoCloseUrls) > 0 {
		tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, bid.VideoCloseUrls...)
	}

	if len(bid.VideoLoadSuccessUrls) > 0 {
		tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, bid.VideoLoadSuccessUrls...)
	}

	for _, videourl := range bid.VideoCheckpointSecondList {
		if len(videourl.Urls) > 0 {
			for _, vUrl := range videourl.Urls {
				if len(vUrl) == 0 {
					continue
				}
				delayMonitor := entity.AdDelayMonitor{
					Url:   vUrl,
					Delay: videourl.Second,
				}
				tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, delayMonitor)
			}
		}

	}

	return tracking

}
