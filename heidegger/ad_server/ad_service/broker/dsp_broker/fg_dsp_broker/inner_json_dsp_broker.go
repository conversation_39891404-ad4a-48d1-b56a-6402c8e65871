package fg_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/fg_traffic_broker/fg_broker_pb"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/utils"
	"io"
	"net/http"
	"fmt"
)

type InnerJsonDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *FGDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string

	inner *FGDspBroker
}

func NewInnerJsonDspBroker(dspId utils.ID) *InnerJsonDspBroker {
	return &InnerJsonDspBroker{
		dspId:         dspId,
		slotRegister:  NewFGDspSlotRegister(dspId),
		MacroWinPrice: "__PRICE__",
		inner:         NewFGDspBroker(dspId),
	}
}

func (b *InnerJsonDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.slotRegister
}

func (b *InnerJsonDspBroker) BuildRequest(adRequest *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := b.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())

	brokerRequest, err := b.inner.buildReq(adRequest, candidateList, b.slotRegister)
	if err != nil {
		zap.L().Error("InnerJsonDspBroker Error in  buildReq:,%v", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))), dspSlot)
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	req, _, err := b.BuildSonicJsonHttpRequest(brokerRequest)
	if err != nil {
		zap.L().Error("InnerJsonDspBroker Error in  BuildSonicJsonHttpRequest", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	b.SampleDspBroadcastSonicJsonRequest(b.GetDspId(), dspSlot.Id, candidate, brokerRequest)
	if adRequest.IsDebug {
		zap.L().Info("InnerJsonDspBroker.EncodeRequest end, url:,FgRequest:%s ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", req.URL.String())))), brokerRequest.String())
	}

	return req, nil

}

func (b *InnerJsonDspBroker) ParseResponse(adRequest *ad_service.AdRequest, candidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	if resp.StatusCode != 200 {
		if adRequest.IsDebug {
			zap.L().Error("[InnerJsonDspBroker] StatusCode", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(resp.StatusCode)))))
		}
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &fg_broker_pb.Response{}
	respBody, err := b.ParseSonicJsonHttpResponse(resp, data, response)
	if err != nil {
		zap.L().Debug("InnerJsonDspBroker.DecodeResponse json.Unmarshal, err", zap.Error(err))
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	broadcastCandidate := candidateList[0]
	b.SampleDspBroadcastResponse(b.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, respBody)

	if adRequest.IsDebug {
		zap.L().Info("InnerJsonDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data)))))
	}

	return b.inner.buildResponse(adRequest, response, broadcastCandidate)
}
