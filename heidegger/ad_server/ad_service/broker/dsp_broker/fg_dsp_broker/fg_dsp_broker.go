package fg_dsp_broker

import (
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"time"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/fg_traffic_broker/fg_broker_pb"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"fmt"
)

type FGDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *FGDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string
}

func NewFGDspBroker(dspId utils.ID) *FGDspBroker {
	return &FGDspBroker{
		dspId:         dspId,
		slotRegister:  NewFGDspSlotRegister(dspId),
		MacroWinPrice: "__PRICE__",
	}
}

func (b *FGDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.slotRegister
}

func (b *FGDspBroker) BuildRequest(adRequest *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := b.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	//buffer := buffer_pool.NewBuffer()
	//defer buffer.Release()
	//buffer.EnsureSize(brokerRequest.Size())
	//
	//_, err := brokerRequest.MarshalToSizedBuffer(buffer.Get())
	//if err != nil {
	//	zap.L().Error("MeiTuanDspBroker MarshalToSizedBuffer err", zap.Error(err))
	//	return nil, err
	//}
	//
	//req, err := http.NewRequest("POST", b.bidUrl, buffer.GetReadCloser())
	//if err != nil {
	//	zap.L().Error("QihangDspBroker http NewRequest err", zap.Error(err))
	//	return nil, err
	//}
	brokerRequest, err := b.buildReq(adRequest, candidateList, b.slotRegister)
	if err != nil {
		zap.L().Error("FGDspBroker Error in buildReq", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err
	}

	req, _, err := b.BuildPbHttpHttpRequest(brokerRequest)
	if err != nil {
		zap.L().Error("FGDspBroker Error in buildHttpRequest", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	req.Header.Set("Content-Type", "application/x-protobuf")

	b.SampleDspBroadcastProtobufRequest(b.GetDspId(), dspSlot.Id, candidate, brokerRequest)

	if adRequest.IsDebug {
		zap.L().Info("FGDspBroker.EncodeRequest end, url:,FgRequest:%s ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", req.URL.String())))), brokerRequest.String())
	}

	return req, nil

}

func (b *FGDspBroker) buildReq(adRequest *ad_service.AdRequest, candidateList ad_service.AdCandidateList, slotRegister *FGDspSlotRegister) (*fg_broker_pb.Request, error) {
	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	brokerRequest := &fg_broker_pb.Request{
		Id:  adRequest.GetRequestId(),
		Imp: make([]*fg_broker_pb.RequestImp, 0),
		Device: &fg_broker_pb.RequestDevice{
			Ip:          trafficData.GetRequestIp(),
			Ua:          trafficData.GetUserAgent(),
			Devicetype:  b.mappingDeviceType(trafficData.GetDeviceType()),
			H:           trafficData.GetScreenHeight(),
			W:           trafficData.GetScreenWidth(),
			Orientation: b.mappingOrientation(trafficData.GetScreenOrientation()),
			Geo: &fg_broker_pb.RequestDeviceGeo{
				Lat: float32(trafficData.GetGeoLatitude()),
				Lon: float32(trafficData.GetGeoLongitude()),
			},
			Make:            trafficData.GetBrand(),
			Vendor:          trafficData.GetBrand(),
			Model:           trafficData.GetModel(),
			Carrier:         b.mappingOperator(trafficData.GetOperatorType()),
			ConnectionType:  b.mappingConnectionType(trafficData.GetConnectionType()),
			Os:              b.mappingOsType(trafficData.GetOsType()),
			Osv:             trafficData.GetOsVersion(),
			Mac:             trafficData.GetMac(),
			MacMd5:          trafficData.GetMd5Mac(),
			Imei:            trafficData.GetImei(),
			ImeiMd5:         trafficData.GetMd5Imei(),
			Oaid:            trafficData.GetOaid(),
			OaidMd5:         trafficData.GetMd5Oaid(),
			Idfa:            trafficData.GetIdfa(),
			IdfaMd5:         trafficData.GetMd5Idfa(),
			AndroidId:       trafficData.GetAndroidId(),
			AndroidIdMd5:    trafficData.GetMd5AndroidId(),
			Paid:            adRequest.Device.Paid,
			Caid:            device_utils.GetCaidRaw(trafficData.GetCaid()),
			CaidVersion:     device_utils.GetCaidVersion(trafficData.GetCaid()),
			CaidMd5:         device_utils.GetCaidRaw(trafficData.GetMd5Caid()),
			BootMark:        trafficData.GetBootMark(),
			UpdateMark:      trafficData.GetUpdateMark(),
			BirthTime:       trafficData.GetDeviceInitTime(),
			BootTime:        trafficData.GetDeviceStartupTime(),
			UpdateTime:      trafficData.GetDeviceUpgradeTime(),
			VercodeAg:       adRequest.Device.VercodeAg,
			VercodeHms:      adRequest.Device.VercodeHms,
			CountryCode:     adRequest.Device.CountryCode,
			DiskTotal:       adRequest.Device.SystemTotalDisk,
			MemTotal:        adRequest.Device.SystemTotalMem,
			DeviceName:      adRequest.Device.DeviceName,
			HardwareMachine: adRequest.Device.HardwareMachineCode,
			Density:         float64(trafficData.GetScreenDensity()),
			Ppi:             adRequest.Device.PPI,
			Caids:           make([]*fg_broker_pb.RequestDevice_CaidInfo, 0),
		},
		App: &fg_broker_pb.RequestApp{
			//AppId:   trafficData.GetAppBundle(),
			Name:    trafficData.GetAppName(),
			Bundle:  trafficData.GetAppBundle(),
			Version: trafficData.GetAppVersion(),
		},
		User: &fg_broker_pb.RequestUser{
			//Gender:     "",
			//Age:        "",
			InstallApp: adRequest.App.InstalledApp,
		},
		//Site:    nil,
		Secure: 0,
	}

	if len(trafficData.GetCaid()) != 0 {
		brokerRequest.Device.Caids = append(brokerRequest.Device.Caids, &fg_broker_pb.RequestDevice_CaidInfo{
			Caid:    device_utils.GetCaidRaw(trafficData.GetCaid()),
			Version: device_utils.GetCaidVersion(trafficData.GetCaid()),
		})
	}
	if len(adRequest.Device.Caids) > 0 {
		for _, caidItem := range adRequest.Device.Caids {
			if caidItem == trafficData.GetCaid() {
				continue
			}
			if len(caidItem) > 0 {
				brokerRequest.Device.Caids = append(brokerRequest.Device.Caids, &fg_broker_pb.RequestDevice_CaidInfo{
					Version: device_utils.GetCaidVersion(caidItem),
					Caid:    device_utils.GetCaidRaw(caidItem),
				})
			}
		}
	}

	bidFloor := candidate.GetBidFloor()
	imp := &fg_broker_pb.RequestImp{
		Id:          adRequest.ImpressionId,
		BidFloor:    float32(bidFloor.Price),
		SlotId:      slotId,
		SlotType:    b.mappingSlotType(trafficData.GetSlotType()),
		Minduration: adRequest.VideoMinDuration,
		Maxduration: adRequest.VideoMaxDuration,
		//1 单图二文(标题描述)
		//5 三图二文(标题描述)
		//6 一图一视频
		//7 一图一Icon
		//9 三图一icon
		//11 一图一文(标题)
		TemplateId:    []int32{1, 6, 11},
		SupportAction: []int32{0, 1, 2, 3},
	}

	if len(dspSlot.TemplateIds) > 0 {
		imp.TemplateId = dspSlot.TemplateIds
	}

	if len(dspSlot.AppName) > 0 {
		brokerRequest.App.Name = dspSlot.AppName
	}

	if len(dspSlot.PkgName) > 0 {
		brokerRequest.App.Bundle = dspSlot.PkgName
	}

	if len(dspSlot.AppVersion) > 0 {
		brokerRequest.App.Version = dspSlot.AppVersion
	}

	brokerRequest.Imp = append(brokerRequest.Imp, imp)

	if len(adRequest.SlotSize) > 0 {
		for _, size := range adRequest.SlotSize {
			imp.Size_ = append(imp.Size_, &fg_broker_pb.RequestImpSize{
				W: int32(size.Width),
				H: int32(size.Height),
			})

		}
	}
	return brokerRequest, nil
}

func (b *FGDspBroker) mappingSlotType(t entity.SlotType) int32 {
	switch t {
	case entity.SlotTypeOpening:
		return 6
	case entity.SlotTypeVideo:
		return 1
	case entity.SlotTypeBanner:
		return 5
	case entity.SlotTypePopup:
		return 7
	case entity.SlotTypeFeeds:
		return 8
	case entity.SlotTypeRewardVideo:
		return 12
	default:
		return 8
	}
}

func (b *FGDspBroker) mappingDeviceType(deviceType entity.DeviceType) int32 {
	switch deviceType {
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypeOtt:
		return 4
	default:
		return 0
	}
}

func (b *FGDspBroker) mappingOrientation(t entity.ScreenOrientationType) int32 {
	switch t {
	case entity.ScreenOrientationTypePortrait:
		return 1
	case entity.ScreenOrientationTypeLandscape:
		return 2
	default:
		return 0
	}
}

func (b *FGDspBroker) mappingOperator(v entity.OperatorType) int32 {
	switch v {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 2
	case entity.OperatorTypeChinaTelecom:
		return 3
	case entity.OperatorTypeTietong:
		return 4
	default:
		return 0
	}
}

func (b *FGDspBroker) mappingConnectionType(v entity.ConnectionType) uint32 {
	switch v {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionTypeCellular:
		return 3
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 0
	}
}

func (b *FGDspBroker) mappingOsType(os entity.OsType) int32 {
	switch os {
	case entity.OsTypeAndroid:
		return 2
	case entity.OsTypeIOS:
		return 1
	default:
		return 0
	}
}

func (b *FGDspBroker) ParseResponse(adRequest *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	if resp.StatusCode != 200 {
		if adRequest.IsDebug {
			zap.L().Error("[FGDspBroker] StatusCode", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(resp.StatusCode)))))
		}
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &fg_broker_pb.Response{}
	if err := b.ParsePbHttpHttpResponse(resp, data, response); err != nil {
		zap.L().Debug("FGDspBroker.DecodeResponse json.Unmarshal, err", zap.Error(err))
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	broadcastCandidate := broadcastCandidateList[0]
	b.SampleDspBroadcastProtobufResponse(b.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, response)

	if adRequest.IsDebug {
		resbody, _ := json.Marshal(response)
		zap.L().Info("FGDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	return b.buildResponse(adRequest, response, broadcastCandidate)

}

func (b *FGDspBroker) markInnerCode(candidate *ad_service.AdCandidate, endTime int64) {
	if b.GetUserSegmentClient() == nil {
		return
	}

	trafficData := candidate.GetModifiedTrafficData()
	deviceId, _ := trafficData.GetDeviceIdWithType()

	// seconds to the end of the day
	now := time.Now()
	expireTime := endTime - now.Unix()
	if expireTime <= 0 {
		return
	}

	if err := b.GetUserSegmentClient().AddUserSegmentAsync(deviceId, entity.UserTagInnerColdTag, 1, uint32(expireTime), 0); err != nil {
		zap.L().Error("markInnerCode AddUserSegmentAsync err", zap.Error(err))
		return
	}
}

func (b *FGDspBroker) CheckBroadcastContext(ctx *ad_service.AdRequest, candidateList ad_service.AdCandidateList) error {
	if ctx.UserSegment.ContainsTag(entity.UserTagInnerColdTag) {
		return err_code.ErrBroadcastDspContextFilter
	}
	return nil
}

func (b *FGDspBroker) buildResponse(adRequest *ad_service.AdRequest, response *fg_broker_pb.Response, broadcastCandidate *ad_service.AdCandidate) (ad_service.DspAdCandidateList, error) {
	if response.Code != 0 || len(response.SeatBid) == 0 || len(response.SeatBid[0].Bid) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	if response.GetCode() == 9 && response.GetColdEndTime() > 0 {
		b.markInnerCode(broadcastCandidate, response.GetColdEndTime())
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)
	for _, resBid := range response.SeatBid[0].Bid {
		if resBid == nil || resBid.Tracking == nil {
			continue
		}
		candidateAd := &entity.Ad{
			DspId:      b.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		if resBid.App != nil {
			candidateAd.AppInfo = &entity.AppInfo{
				PackageName:    resBid.App.PackageName,
				AppName:        resBid.App.Name,
				Icon:           resBid.App.AppIcon,
				AppID:          resBid.App.AppId,
				AppVersion:     resBid.App.AppVersion,
				PackageSize:    int(resBid.App.AppSize),
				Privacy:        resBid.App.AppPrivacy,
				Permission:     resBid.App.AppPermission,
				PermissionDesc: nil,
				AppDesc:        resBid.App.AppDesc,
				AppDescURL:     resBid.App.AppDescUrl,
				Develop:        resBid.App.Developer,
			}
			if resBid.Tracking.WxProgramId != "" {
				candidateAd.AppInfo.WechatExt = &entity.WechatExt{
					ProgramId:   resBid.Tracking.WxProgramId,
					ProgramPath: resBid.Tracking.WxProgramPath,
				}
			}
		}

		candidateAd.AdMonitorInfo = b.ParseTrackingData(adRequest, resBid)

		candidateCreative := b.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(b.chargePriceEncoder)
		candidate.SetBidPrice(uint32(resBid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(resBid.CreativeId)
		candidate.SetDspProtocol(b.GetDspProtocol())
		result = append(result, candidate)
		break
	}

	return result, nil
}
func (b *FGDspBroker) chargePriceEncoder(chargePrice uint32) string {
	//明文替换
	result, err := b.PriceManager.PlaintextEncode(uint64(chargePrice))
	//result, err := impl.PriceManager.GetDspCoder(impl.dspProtocol).EncodeWithKey(uint64(chargePrice), impl.iKey, impl.eKey)
	if err != nil {
		return ""
	}

	return result
}

func (b *FGDspBroker) ParseCreativeData(bid *fg_broker_pb.ResponseBid) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.CreativeId,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	for _, assets := range bid.Native.Assets {
		if assets.Image != nil {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          assets.Image.Url,
				Width:        assets.Image.W,
				Height:       assets.Image.H,
			}

			if assets.Image.Type == 2 {
				material.MaterialType = entity.MaterialTypeLogo
			} else if assets.Image.Type == 3 {
				material.MaterialType = entity.MaterialTypeIcon
			}
			creative.AddMaterial(material)
		}

		if assets.Video != nil {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          assets.Video.Url,
				Width:        assets.Video.W,
				Height:       assets.Video.H,
				Duration:     float64(assets.Video.Duration),
			}
			creative.AddMaterial(material)

			if len(assets.Video.CoverUrl) > 0 {
				coverImg := &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          assets.Video.CoverUrl,
					Width:        assets.Video.W,
					Height:       assets.Video.H,
				}
				creative.AddMaterial(coverImg)
			}

		}

		if assets.Data != nil {
			switch assets.Data.Type {
			case 1:
				material := &entity.Material{
					MaterialType: entity.MaterialTypeTitle,
					Data:         assets.Data.Value,
				}
				creative.AddMaterial(material)
			case 2:
				material := &entity.Material{
					MaterialType: entity.MaterialTypeDesc,
					Data:         assets.Data.Value,
				}
				creative.AddMaterial(material)
			}
		}
	}

	return creative

}

func (b *FGDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *fg_broker_pb.ResponseBid) *entity.AdMonitorInfo {

	tracking := &entity.AdMonitorInfo{
		LandingUrl:            bid.Tracking.LandingPage,
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      make([]string, 0),
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkMonitorList:   make([]string, 0),
		VideoStartUrlList:     make([]string, 0),
		DeepLinkUrl:           bid.Tracking.DeepLink,
		DelayMonitorUrlList:   make([]entity.AdDelayMonitor, 0),
	}

	if bid.SupportAction == 1 {
		tracking.LandingAction = entity.LandingTypeDownload
	} else if bid.SupportAction == 2 {
		tracking.LandingAction = entity.LandingTypeDeepLink
	} else if bid.SupportAction == 3 {
		tracking.LandingAction = entity.LandingTypeWeChatProgram
	}

	if len(bid.Nurl) > 0 {
		if strings.Contains(bid.Nurl, b.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(bid.Nurl, b.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.Nurl)
		}
	}

	if len(bid.Tracking.ImpTrackers) > 0 {
		for _, impTrack := range bid.Tracking.ImpTrackers {
			if strings.Contains(impTrack, b.MacroWinPrice) {
				newImpTrack := strings.ReplaceAll(impTrack, b.MacroWinPrice, "__DSPWPRICE__")
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
			} else {
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impTrack)
			}
		}
	}

	if len(bid.Tracking.ClkTrackers) > 0 {
		tracking.ClickMonitorList = bid.Tracking.ClkTrackers
	}

	if len(bid.Tracking.DeeplinkTrackers) > 0 {
		tracking.DeepLinkMonitorList = bid.Tracking.DeeplinkTrackers
	}

	if len(bid.Tracking.DownloadStart) > 0 {
		tracking.AppDownloadStartedMonitorList = bid.Tracking.DownloadStart
	}

	if len(bid.Tracking.DownloadEnd) > 0 {
		tracking.AppDownloadFinishedMonitorList = bid.Tracking.DownloadEnd
	}

	if len(bid.Tracking.InstallStart) > 0 {
		tracking.AppInstallStartMonitorList = bid.Tracking.InstallStart
	}

	if len(bid.Tracking.InstallEnd) > 0 {
		tracking.AppInstalledFinishMonitorList = bid.Tracking.InstallEnd
	}

	video25 := 0
	video50 := 0
	video75 := 0
	video100 := 0

	for _, assets := range bid.Native.Assets {
		if assets.Video != nil && assets.Video.Duration > 0 {
			video25 = int(assets.Video.Duration / 4)
			video50 = int(assets.Video.Duration / 2)
			video75 = int(assets.Video.Duration * 75 / 100)
			video100 = int(assets.Video.Duration)
		}
	}

	if len(bid.Tracking.VideoStartPlay) > 0 {
		tracking.VideoStartUrlList = bid.Tracking.VideoStartPlay
	}

	if len(bid.Tracking.Video_25Play) > 0 {
		for _, item := range bid.Tracking.Video_25Play {
			tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
				Url:   item,
				Delay: video25,
			})
		}
	}

	if len(bid.Tracking.Video_50Play) > 0 {
		for _, item := range bid.Tracking.Video_50Play {
			tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
				Url:   item,
				Delay: video50,
			})
		}
	}
	if len(bid.Tracking.Video_75Play) > 0 {
		for _, item := range bid.Tracking.Video_75Play {
			tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
				Url:   item,
				Delay: video75,
			})
		}
	}

	if len(bid.Tracking.VideoEndPlay) > 0 {
		for _, item := range bid.Tracking.VideoEndPlay {
			tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
				Url:   item,
				Delay: video100,
			})
		}
	}
	if len(bid.Tracking.VideoClose) > 0 {
		for _, item := range bid.Tracking.VideoClose {
			tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
				Url:   item,
				Delay: video100,
			})
		}
	}

	return tracking

}
