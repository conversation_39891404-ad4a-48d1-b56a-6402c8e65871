package pdd_broker

import (
	"fmt"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

/*
{"dsp_slot_ext_attrs":[{"key":"media_id","label":"媒体ID","type":"input","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"template_id","label":"模板","type":"input","required":true,"is_number":false,"multiple":true,"comma_separated":true},{"key":"width","label":"广告位宽","type":"input","required":false,"is_number":true,"multiple":false,"comma_separated":false},{"key":"height","label":"广告位高","type":"input","required":false,"is_number":true,"multiple":false,"comma_separated":false}]}
*/
type PddSlotSlotInfo struct {
	*entity.DspSlotInfo
	MediaId    string `json:"media_id"`
	TemplateId string `json:"template_id"`
	Height     int    `json:"height"`
	Width      int    `json:"width"`
}

func (info *PddSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.MediaId, _ = dspSlotInfo.ExtraData.GetString("media_id")

	info.TemplateId, err = dspSlotInfo.ExtraData.GetString("template_id")
	if err != nil {
		return fmt.Errorf("get template_id from extra_data failed, err: %v", err)
	}

	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")

	return nil
}

type PddDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*PddSlotSlotInfo
}

func NewPddDspSlotRegister(dspId utils.ID) *PddDspSlotRegister {
	return &PddDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*PddSlotSlotInfo),
	}
}

func (r *PddDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *PddDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*PddSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &PddSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[PddDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *PddDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *PddDspSlotRegister) GetSlotInfo(slotId utils.ID) *PddSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
