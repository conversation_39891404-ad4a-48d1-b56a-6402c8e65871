package pdd_broker

import (
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/pdd_broker/pdd_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type PddDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister  *PddDspSlotRegister
	MacroWinPrice string
}

func NewPddDspBroker(dspId utils.ID) *PddDspBroker {
	return &PddDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{DspId: dspId},
		slotRegister:  NewPddDspSlotRegister(dspId),
		MacroWinPrice: "%%PRICE%%",
	}
}

func (impl *PddDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *PddDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("PddDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	adxId := "mrk_union_ttj"
	if dspSlot.MediaId != "" {
		adxId = dspSlot.MediaId
	}
	// adxId := "mrk_union_ddcx"
	//adxId = "mrk_union_wnl"
	//adxId = "mrk_union_xd"

	pddJsonRequest := &pdd_broker_entity.PddBidRequest{
		AdxId:     adxId,
		RequestId: trafficData.GetRequestId(),
		At:        2,
		Site:      impl.encodeSite(request),
		App:       impl.encodeApp(trafficData),
		Device:    impl.encodeDevice(request, trafficData),
		User:      impl.encodeUser(request),
		//Ext:       "",
	}
	imp, err := impl.encodeImp(request, trafficData, candidate)
	if err != nil {
		return nil, err_code.ErrInvalidImpression

	}
	pddJsonRequest.Imp = imp

	req, _, err := impl.BuildSonicJsonHttpRequest(pddJsonRequest)
	if err != nil {
		zap.L().Error("PddDspBroker buildJsonHttpRequest err", zap.Error(err))
		return nil, err_code.ErrBroadcastRequestBuildFail.Wrap(err)
	}

	impl.SampleDspBroadcastSonicJsonRequest(impl.GetDspId(), dspSlot.Id, candidate, pddJsonRequest)

	if request.IsDebug {
		body, _ := sonic.Marshal(pddJsonRequest)
		zap.L().Debug("PddDspBroker.EncodeRequest end, pddRequest", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(body))))))
	}

	return req, nil
}

func (impl *PddDspBroker) encodeImp(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData, candidate *ad_service.AdCandidate) ([]pdd_broker_entity.PddImp, error) {
	imps := make([]pdd_broker_entity.PddImp, 0)

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, fmt.Errorf("empty slot")
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, fmt.Errorf("empty slot")
	}

	templateId := dspSlot.TemplateId
	templateIds := strings.Split(templateId, ",")

	bidFloor := candidate.GetBidFloor()

	imp := pdd_broker_entity.PddImp{
		Id:            request.ImpressionId,
		Tagid:         slotId,
		Bidfloor:      int(bidFloor.Price),
		ContentType:   []int{1, 2}, //1.普通网址类 2.下载类
		ClickStrategy: 0,           // 此次曝光的点击策略 0 全局可点 1 局部可点
		//Keyword:         "",
		//Dealid:          "",
		//Dealids:         nil,
		SupportDeeplink: true,
		W:               dspSlot.Width,
		H:               dspSlot.Height,
		//Bcat:            nil,
		//Wcat:            nil,
		//Ext:             "",
	}

	if imp.W == 0 || imp.H == 0 {
		if len(request.SlotSize) > 0 {
			imp.W = int(request.SlotSize[0].Width)
			imp.H = int(request.SlotSize[0].Height)
		}
	}
	if imp.W == 0 || imp.H == 0 {
		imp.W = int(trafficData.GetSlotWidth())
		imp.H = int(trafficData.GetSlotHeight())
	}

	for _, v := range templateIds {
		imp.Templates = append(imp.Templates, pdd_broker_entity.PddTemplate{
			TemplateId: v,
		})
	}

	imps = append(imps, imp)

	return imps, nil
}

func (impl *PddDspBroker) encodeSite(request *ad_service.AdRequest) pdd_broker_entity.PddSite {
	site := pdd_broker_entity.PddSite{}
	site.Ref = request.Referer
	return site
}

func (impl *PddDspBroker) encodeApp(trafficData ad_service_entity.TrafficData) pdd_broker_entity.PddApp {
	app := pdd_broker_entity.PddApp{}
	//app.Id = bidRequest.Mobile.
	app.Name = trafficData.GetAppName()
	app.Bundle = trafficData.GetAppBundle()
	app.Version = trafficData.GetAppVersion()
	return app
}

func (impl *PddDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) pdd_broker_entity.PddDevice {
	device := pdd_broker_entity.PddDevice{}
	device.Ua = trafficData.GetUserAgent()
	if device.Ua == "" {
		device.Ua = trafficData.GetWebviewUA()
	}
	device.Ip = trafficData.GetRequestIp()
	device.Devicetype = impl.mappingDeviceType(trafficData.GetDeviceType())

	device.Make = trafficData.GetBrand()
	device.Model = trafficData.GetModel()
	device.Os = impl.mappingOs(trafficData.GetOsType())
	device.Osv = trafficData.GetOsVersion()
	device.W = int(trafficData.GetScreenWidth())
	device.H = int(trafficData.GetScreenHeight())
	device.Connectiontype = impl.mappingConnectionType(trafficData.GetConnectionType())
	device.Carrier = impl.mappingCarrier(trafficData.GetOperatorType())
	device.Imei = trafficData.GetImei()
	if len(trafficData.GetMd5Imei()) > 0 {
		device.ImeiMd5 = strings.ToLower(trafficData.GetMd5Imei())
	}
	device.Androidid = trafficData.GetAndroidId()
	device.AndroididMd5 = trafficData.GetMd5AndroidId()
	device.Idfa = trafficData.GetIdfa()
	device.IdfaMd5 = trafficData.GetMd5Idfa()
	//if mobile.Os == dict.OsTypeIOS {
	//	if device.IdfaMd5 == "" && device.Idfa != "" {
	//		device.IdfaMd5 = common2.Md5(device.Idfa)
	//	}
	//
	//	if device.IdfaMd5 != "" {
	//		// 设置随机数种子
	//		rand.Seed(time.Now().UnixNano())
	//		// 要求5%的idfa进行md5加密
	//		if rand.Intn(100) < 5 {
	//			device.IdfaMd5 = common2.Md5(device.IdfaMd5)
	//		}
	//	}
	//}
	device.Oaid = trafficData.GetOaid()
	device.OaidMd5 = trafficData.GetMd5Oaid()
	device.Caid = trafficData.GetCaid()
	device.Geo.Latitude = request.Device.Lat
	device.Geo.Longitude = request.Device.Lon
	device.BirthTime = trafficData.GetDeviceInitTime()
	device.BootTime = trafficData.GetDeviceStartupTime()
	if device.BootTime == "" {
		device.BootTime = trafficData.GetBootMark()
	}
	device.UpdateTime = trafficData.GetDeviceUpgradeTime()
	if device.UpdateTime == "" {
		device.UpdateTime = trafficData.GetUpdateMark()
	}
	device.Paid = request.Device.Paid
	return device
}

func (impl *PddDspBroker) encodeUser(request *ad_service.AdRequest) pdd_broker_entity.PddUser {
	user := pdd_broker_entity.PddUser{}

	user.Id = request.UserId
	switch request.UserGender {
	case entity.UserGenderMan:
		user.Gender = "M"
	case entity.UserGenderWoman:
		user.Gender = "F"
	}
	if request.UserAge > 0 {
		user.Age = strconv.Itoa(int(request.UserAge))
	}
	return user
}

func (impl *PddDspBroker) mappingDeviceType(deviceType entity.DeviceType) int {
	switch deviceType {
	case entity.DeviceTypePad, entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePc:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	default:
		return 0
	}
}

func (impl *PddDspBroker) mappingOs(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "ios"
	case entity.OsTypeAndroid:
		return "android"
	case entity.OsTypeWindowsPhone:
		return "windowsphone"
	case entity.OsTypeWindows:
		return "windows"
	case entity.OsTypeMacOs:
		return "macos"
	default:
		return ""
	}
}

func (impl *PddDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionTypeUnknown:
		return 3
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 3
	}
}

func (impl *PddDspBroker) mappingCarrier(carrier entity.OperatorType) string {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return "mobile"
	case entity.OperatorTypeChinaTelecom, entity.OperatorTypeTietong:
		return "telecom"
	case entity.OperatorTypeChinaUnicom:
		return "unicom"
	default:
		return "unknown"
	}
}

func (impl *PddDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *PddDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("PddDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBroadcastResponseReadBodyFail.Wrap(err)
	}

	response := &pdd_broker_entity.PddBidResponse{}
	payload, err := impl.ParseSonicJsonHttpResponse(resp, data, response)
	if err != nil {
		zap.L().Error("PddDspBroker.DecodeResponse unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data)))), zap.Error(err))
		return nil, err_code.ErrBroadcastResponseParseFail.Wrap(err)
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, payload)

	if request.IsDebug {
		zap.L().Info("PddDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data)))))
	}

	if response.Nbr != 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	if len(response.Seatbid) < 1 || len(response.Seatbid[0].Bid) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)
	for _, v := range response.Seatbid {
		for _, resBid := range v.Bid {
			candidateAd := &entity.Ad{
				DspId:         impl.GetDspId(),
				DspSlotId:     broadcastCandidate.GetDspSlotId(),
				DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
				AdMonitorInfo: impl.ParseTrackingData(request, resBid),
			}

			candidateAd.AppInfo = &entity.AppInfo{}
			candidateAd.AppInfo.AppName = resBid.AppName
			candidateAd.AppInfo.PackageName = resBid.AppBundle
			if len(resBid.Ext) > 2 {
				info := &pdd_broker_entity.PddBidExt{}
				err := sonic.UnmarshalString(resBid.Ext, info)
				if err == nil {
					candidateAd.AppInfo.Privacy = info.PrivacyPolicyLink
					candidateAd.AppInfo.Permission = info.PermissionLink
					candidateAd.AppInfo.AppVersion = info.AppVersion
					candidateAd.AppInfo.Develop = info.Developer
					candidateAd.AppInfo.AppDescURL = info.DownloadUrl // XXX:
				}
			}

			candidateCreative := impl.ParseCreativeData(resBid)
			if candidateCreative == nil {
				return nil, err_code.ErrBrokerResponseInternalFail
			}

			candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
			candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
			candidate.SetBidPrice(uint32(resBid.Price))
			candidate.SetBidType(entity.BidTypeCpm)
			candidate.SetCreative(candidateCreative)
			candidate.SetDspProtocol(impl.GetDspProtocol())
			result = append(result, candidate)
			break

			//if resBid.DealId != "" {
			//	bidInfo.CreativeData.SetDealId(resBid.DealId)
			//}
		}

	}

	return result, nil
}

func (impl *PddDspBroker) ParseCreativeData(bid pdd_broker_entity.PddBid) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.Adid,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if len(creative.CreativeKey) == 0 {
		creative.CreativeKey = bid.PddAdid
	}

	if bid.Adm != nil {
		hasTitle := false
		if bid.Adm.Text != "" {
			hasTitle = true
			material := &entity.Material{
				MaterialType: entity.MaterialTypeTitle,
				Data:         bid.Adm.Text,
			}
			creative.MaterialList = append(creative.MaterialList, material)

		}
		if bid.Adm.Title != "" {
			hasTitle = true
			material := &entity.Material{
				MaterialType: entity.MaterialTypeTitle,
				Data:         bid.Adm.Title,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
		if bid.Adm.Desc != "" {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeDesc,
				Data:         bid.Adm.Desc,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		} else {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeDesc,
				Data:         "点击查看详情",
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
		if !hasTitle {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeTitle,
				Data:         "点击查看详情",
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

		//if bid.Adm.Button != "" {
		//	hasTxt = true
		//	material.Resource = append(material.Resource, ad_entity.Resource{
		//		Type:     dict.MaterialResourceTypeText,
		//		AttrName: dict.MaterialResourceLayoutButtonText,
		//		Text: &ad_entity.Text{
		//			Text: bid.Adm.Button,
		//		},
		//	})
		//}

		if bid.Adm.VideoUrl != "" {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          bid.Adm.VideoUrl,
				Duration:     float64(bid.Adm.Duration),
				Width:        int32(bid.Adm.Width),
				Height:       int32(bid.Adm.Height),
			}
			creative.MaterialList = append(creative.MaterialList, material)

			if bid.Adm.VideoBackgroundUrl != "" {
				materialImg := &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          bid.Adm.VideoBackgroundUrl,
					Width:        int32(bid.Adm.Width),
					Height:       int32(bid.Adm.Height),
				}
				creative.MaterialList = append(creative.MaterialList, materialImg)
			}
		}

		if bid.Adm.IconUrl != "" {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeIcon,
				Url:          bid.Adm.IconUrl,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

		//material.Resource = append(material.Resource, ad_entity.Resource{
		//	Type:     dict.MaterialResourceTypeText,
		//	AttrName: dict.MaterialResourceLayoutAdvertiser,
		//	Text: &ad_entity.Text{
		//		Text: "拼多多",
		//	},
		//})

		for _, img := range bid.Adm.ImageUrl {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img,
				Width:        int32(bid.Adm.Width),
				Height:       int32(bid.Adm.Height),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}
	return creative
}

func (impl *PddDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid pdd_broker_entity.PddBid) *entity.AdMonitorInfo {
	trackings := &entity.AdMonitorInfo{
		LandingUrl:            bid.ClickUrl,
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      bid.Curl,
		LandingAction:         entity.LandingTypeInWebView,
	}

	if len(bid.Nurl) > 0 {
		newImpTrack := strings.ReplaceAll(bid.Nurl, impl.MacroWinPrice, "__DSPWPRICE__")
		trackings.ImpressionMonitorList = append(trackings.ImpressionMonitorList, newImpTrack)
	}

	for _, impTrack := range bid.Iurl {
		if strings.Contains(impTrack, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impTrack, impl.MacroWinPrice, "__DSPWPRICE__")
			trackings.ImpressionMonitorList = append(trackings.ImpressionMonitorList, newImpTrack)
		} else {
			trackings.ImpressionMonitorList = append(trackings.ImpressionMonitorList, impTrack)
		}
	}

	trackings.DeepLinkUrl = bid.DeeplinkUrl

	// 1 普通网址类 2 应用下载类
	switch bid.ContentType {
	case 2:
		trackings.LandingAction = entity.LandingTypeDownload
		trackings.DownloadUrl = bid.ClickUrl
	case 1:
		trackings.LandingAction = entity.LandingTypeInWebView
	}

	if len(bid.VideoPlayCompleteUrl) > 0 && bid.Adm != nil {
		trackings.DelayMonitorUrlList = make([]entity.AdDelayMonitor, 0)
		for _, v := range bid.VideoPlayCompleteUrl {
			trackings.DelayMonitorUrlList = append(trackings.DelayMonitorUrlList, entity.AdDelayMonitor{
				Delay: bid.Adm.Duration,
				Url:   v,
			})
		}
	}
	return trackings
}
