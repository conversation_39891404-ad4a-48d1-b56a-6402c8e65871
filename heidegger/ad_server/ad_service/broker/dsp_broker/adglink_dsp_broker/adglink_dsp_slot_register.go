package adglink_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"fmt"
)

type AdGlinkSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`
	ShowId     string `json:"show_id"`
}

func (info *AdGlinkSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	info.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	info.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")
	info.ShowId, _ = dspSlotInfo.ExtraData.GetString("show_id")

	return nil
}

type YuQueSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*AdGlinkSlotInfo
}

func NewAdGlinkSlotRegister(dspId utils.ID) *YuQueSlotRegister {
	return &YuQueSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*AdGlinkSlotInfo),
	}
}

func (r *YuQueSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *YuQueSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*AdGlinkSlotInfo)
	for _, slot := range list {
		dspSlot := &AdGlinkSlotInfo{}
		if err := dspSlot.Init(slot); err != nil {
			zap.L().Error("[YuQueSlotRegister] init slot failed", zap.Error(err), zap.String("slot", fmt.Sprintf("%v", slot.Id)))
			continue
		}

		slotMap[slot.Id] = dspSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *YuQueSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *YuQueSlotRegister) GetSlotInfo(slotId utils.ID) *AdGlinkSlotInfo {
	return r.dspSlotMap[slotId]
}
