package adglink_dsp_broker

import (
	"crypto/md5"
	"encoding/hex"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/adglink_dsp_broker/adglink_dsp_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"time"
)

type AdGlinkDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *YuQueSlotRegister
	log          *zap.Logger
	macroInfo    macro_builder.MonitorMacroInfo
}

func NewAdGlinkDspBroker(dspId utils.ID) *AdGlinkDspBroker {
	return &AdGlinkDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		slotRegister: NewAdGlinkSlotRegister(dspId),
		log:          zap.L().With(zap.String("broker", "AdGlinkDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__AUCTION_PRICE__",
			MacroClickDownX: "__DOWN_X__",
			MacroClickDownY: "__DOWN_Y__",
			MacroClickUpX:   "__UP_X__",
			MacroClickUpY:   "__UP_Y__",
		},
	}
}

func (a *AdGlinkDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		zap.L().Error("[AdGlinkDspBroker] candidateList too many", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := a.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	//dspSlotId
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	// 广告位宽高
	slotWidth, slotHeight := uint32(dspSlot.Width), uint32(dspSlot.Height)
	if slotWidth == 0 || slotHeight == 0 {
		if len(request.SlotSize) > 0 {
			slotWidth, slotHeight = uint32(request.SlotSize[0].Width), uint32(request.SlotSize[0].Height)
		} else {
			slotWidth, slotHeight = trafficData.GetSlotWidth(), trafficData.GetSlotHeight()
		}
	}

	imp := &adglink_dsp_broker_entity.Imp{
		SlotShowID:      slotId,
		SlotW:           int(trafficData.GetSlotWidth()),
		SlotH:           int(trafficData.GetSlotHeight()),
		NativeAdNum:     1,
		MinDuration:     int(request.VideoMinDuration),
		MaxDuration:     int(request.VideoMaxDuration),
		DeepLinkVersion: 1,
		SupportQuickApp: true,
		BidPrice:        uint64(candidate.GetBidFloor().Price),
	}
	//if imp.BidPrice > 0 {
	//	imp.BidPrice = imp.BidPrice*10000
	//}

	if dspSlot.Width != 0 {
		imp.SlotW = dspSlot.Width
	}

	if dspSlot.Height != 0 {
		imp.SlotH = dspSlot.Height
	}

	adGlinkRequest := &adglink_dsp_broker_entity.AdGlinkBidRequest{
		//Id:   request.GetRequestId(),
		Imps: []*adglink_dsp_broker_entity.Imp{imp},
		Media: &adglink_dsp_broker_entity.Media{
			ShowId: dspSlot.ShowId,
			Name:   trafficData.GetAppName(),
			Bundle: trafficData.GetAppBundle(),
			Ver:    trafficData.GetAppVersion(),
		},
		Device: &adglink_dsp_broker_entity.Device{
			Dvh: int(trafficData.GetScreenHeight()),
			Dvw: int(trafficData.GetScreenWidth()),
			Ua:  trafficData.GetUserAgent(),
			Gen: &adglink_dsp_broker_entity.Gen{
				Lat: trafficData.GetGeoLatitude(),
				Lon: trafficData.GetGeoLongitude(),
			},
			IP:              trafficData.GetRequestIp(),
			Os:              mappingDeviceOs(trafficData.GetOsType()),
			Osv:             trafficData.GetOsVersion(),
			Dpi:             int(request.Device.DPI),
			Ppi:             int(request.Device.PPI),
			Aid:             trafficData.GetAndroidId(),
			AndroidIDMd5:    trafficData.GetMd5AndroidId(),
			Imei:            trafficData.GetImei(),
			Didmd5:          trafficData.GetMd5Imei(),
			Aaid:            trafficData.GetOaid(),
			OaIDMd5:         trafficData.GetMd5Oaid(),
			Idfa:            trafficData.GetIdfa(),
			Caid:            trafficData.GetCaid(),
			Openudid:        trafficData.GetOpenUdid(),
			Imsi:            request.Device.Imsi,
			Mac:             trafficData.GetMac(),
			Make:            request.Device.Brand,
			Model:           trafficData.GetModel(),
			Orientation:     mappingOrientation(trafficData.GetScreenOrientation()),
			Carrier:         mappingCarrier(trafficData.GetOperatorType()),
			ConnType:        mappingConnectionType(trafficData.GetConnectionType()),
			DvcType:         mappingDeviceType(trafficData.GetDeviceType()),
			Region:          "CN",
			Lan:             1,
			Country:         "CN",
			DeviceNameMd5:   mappingDeviceNameMd5(request.Device.DeviceName),
			HardwareModel:   trafficData.GetModel(),
			HardwareMachine: request.Device.HardwareMachineCode,
		},
		CltReqTs:     uint64(time.Now().UnixNano() / 1000000),
		IsFullScreen: 1,
	}

	deviceInitTime := trafficData.GetDeviceInitTime()
	if len(deviceInitTime) == 0 {
		adGlinkRequest.Device.DeviceStartSec = trafficData.GetDeviceStartupTime()
	}

	deviceUpgradeTime := trafficData.GetDeviceUpgradeTime()
	if len(deviceUpgradeTime) == 0 {
		adGlinkRequest.Device.SystemUpdateSec = trafficData.GetUpdateMark()
	}

	if request.Device.IsIp6 {
		adGlinkRequest.Device.IP = trafficData.GetRequestIp()
	}

	if len(dspSlot.PkgName) > 0 {
		adGlinkRequest.Media.Bundle = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		adGlinkRequest.Media.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		adGlinkRequest.Media.Ver = dspSlot.AppVersion
	}

	httpReq, _, err := a.BuildSonicJsonHttpRequest(adGlinkRequest)
	if err != nil {
		a.log.WithError(err).Error("BuildSonicJsonHttpRequest error")
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}
	httpReq.Header.Set("x-protocol-ver", "1.0")
	httpReq.Header.Set("x-encrypt-type", "none")
	a.SampleDspBroadcastSonicJsonRequest(a.DspId, trafficData.GetDspSlotId(), candidate, adGlinkRequest)
	if request.IsDebug {
		payload, _ := sonic.Marshal(adGlinkRequest)
		a.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}
	return httpReq, nil
}

func mappingOrientation(orientation entity.ScreenOrientationType) int {
	switch orientation {
	case entity.ScreenOrientationTypePortrait:
		return 0
	case entity.ScreenOrientationTypeLandscape:
		return 1
	default:
		return 0
	}
}
func mappingDeviceOs(osType entity.OsType) int {
	switch osType {
	case entity.OsTypeIOS:
		return 2
	case entity.OsTypeAndroid:
		return 1
	case entity.OsTypeWindowsPhone:
		return 3
	default:
		return 0
	}
}
func mappingCarrier(carrier entity.OperatorType) int {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 2
	case entity.OperatorTypeChinaTelecom:
		return 3
	default:
		return 0
	}
}
func mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 0
	}
}
func mappingDeviceNameMd5(str string) string {
	if str == "" {
		return ""
	}

	hash := md5.Sum([]byte(str))
	return hex.EncodeToString(hash[:])
}
func mappingDeviceType(dy entity.DeviceType) int {
	switch dy {
	case entity.DeviceTypeMobile:
		return 3
	case entity.DeviceTypePad:
		return 4
	case entity.DeviceTypeOtt:
		return 5
	case entity.DeviceTypePc:
		return 1
	default:
		return 0
	}
}

func (a *AdGlinkDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if response.StatusCode != 200 {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(response.StatusCode), type_convert.GetAssertString(response.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse
	}

	resp := new(adglink_dsp_broker_entity.BidResponse)
	payload, err := a.ParseSonicJsonHttpResponse(response, data, resp)
	if err != nil {
		a.log.WithError(err).WithField("response", response).Error("ParseSonicJsonHttpResponse error")
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		return nil, err_code.ErrBrokerParseError.Wrap(err)
	}

	a.SampleDspBroadcastResponse(a.DspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, payload)
	if request.IsDebug {
		a.log.WithField("resp", string(payload)).Info("ParseResponse debug")
	}
	a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.Rc), getCodeMsg(resp.Rc))

	if resp.Rc != 1000 || len(resp.Adimps) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	bid := resp.Adimps[0].Adm[0]
	result := make(ad_service.DspAdCandidateList, 0, 1)
	ad := &entity.Ad{
		DspId:      a.DspId,
		DspSlotId:  broadcastCandidate.GetDspSlotId(),
		DspSlotKey: broadcastCandidate.GetDspSlotKey(),
	}

	adMonitorInfo, creative := a.parseCallbacksAndCreative(bid)
	ad.AdMonitorInfo = adMonitorInfo
	if creative == nil {
		a.log.WithError(err_code.ErrBrokerResponseInternalFail).WithField("bidResponse", bid).Error("creative is nil")
		return nil, err_code.ErrBrokerResponseInternalFail
	}
	candidate := ad_service.NewDspAdCandidateWithPool(ad)
	candidate.SetAdCandidateChargePriceEncoder(a.chargePriceEncoder)
	candidate.SetBidPrice(uint32(bid.RtbPrice))
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetCreative(creative)
	candidate.SetDspProtocol(a.DspProtocol)
	result = append(result, candidate)

	return result, nil
}

func (a *AdGlinkDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := a.PriceManager.GetDspCoder(a.DspProtocol).EncodeWithKey(uint64(chargePrice), a.GetIKey(), a.GetEKey())
	if err != nil {
		a.log.WithError(err).WithField("price", chargePrice).Errorf("chargePriceEncoder error")
		return ""
	}

	return result
}

func (a *AdGlinkDspBroker) parseCallbacksAndCreative(data *adglink_dsp_broker_entity.Adm) (*entity.AdMonitorInfo, *entity.Creative) {
	info := &entity.AdMonitorInfo{
		LandingUrl:  data.LandingURL,
		DeepLinkUrl: data.DeepLink,
		//ImpressionMonitorList: data.Iurls,
		ClickMonitorList: data.Curls,
		DownloadUrl:      data.PackageURL,
		LandingAction:    mappingLandingType(data),
	}

	if len(data.Nurl) > 0 {
		info.ImpressionMonitorList = append(info.ImpressionMonitorList, data.Nurl)
	}

	info.ImpressionMonitorList = append(info.ImpressionMonitorList, data.Iurls...)

	if len(data.Downloadstart) > 0 {
		info.AppDownloadStartedMonitorList = []string{data.Downloadstart}
	}
	if len(data.Download) > 0 {
		info.AppDownloadFinishedMonitorList = []string{data.Download}
	}
	if len(data.Install) > 0 {
		info.AppInstalledFinishMonitorList = []string{data.Install}
	}
	if len(data.AppOpen) > 0 {
		info.AppOpenMonitorList = []string{data.AppOpen}
	}
	if len(data.IntentSuccess) > 0 {
		info.DeepLinkMonitorList = []string{data.IntentSuccess}
	}

	info.AppInfo = entity.AppInfo{
		PackageName: data.PackageName,
		AppName:     data.AppName,
		AppVersion:  data.AppVersion,
		Develop:     data.AdvertiserName,
		AppDescURL:  data.AppInfoURL,
		AppID:       data.AppID,
	}

	if len(data.WechatAppUsername) > 0 {
		info.AppInfo.WechatExt = &entity.WechatExt{
			ProgramId:   data.WechatAppUsername,
			ProgramPath: data.WechatAppPath,
		}
	}

	//assets
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        data.AdID,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}
	assets := data.Assets[0]
	img := assets.Image
	vid := assets.Video

	if len(data.LogoURL) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeLogo,
			Url:          data.LogoURL,
		})
	}

	if img != nil {
		if len(img.ImgList) > 0 {
			for _, imgUrl := range img.ImgList {
				creative.MaterialList = append(creative.MaterialList, &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          imgUrl,
				})
			}
		}

		if len(img.URL) > 0 {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img.URL,
				Height:       int32(img.H),
				Width:        int32(img.W),
			})
		}

		title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: img.ShowName}
		if len(img.ShowName) == 0 {
			title.Data = "点击查看详情"
		}
		creative.MaterialList = append(creative.MaterialList, title)

		desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: img.Description}
		if len(img.Description) == 0 {
			desc.Data = "点击查看详情"
		}
		creative.MaterialList = append(creative.MaterialList, desc)

		if len(img.Img2URL) > 0 {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeIcon,
				Url:          img.Img2URL,
			})
		}
	}

	if vid != nil {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          vid.URL,
			Height:       int32(vid.H),
			Width:        int32(vid.W),
			Duration:     float64(vid.Duration),
			FileSize:     int32(vid.VideoFileSize),
		})

		title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: vid.ShowName}
		if len(vid.ShowName) == 0 {
			title.Data = "点击查看详情"
		}
		creative.MaterialList = append(creative.MaterialList, title)

		desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: vid.Description}
		if len(vid.Description) == 0 {
			desc.Data = "点击查看详情"
		}
		creative.MaterialList = append(creative.MaterialList, desc)

		if len(vid.Img2URL) > 0 {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeIcon,
				Url:          vid.Img2URL,
			})
		}
	}

	//宏替换
	info.ImpressionMonitorList = macro_builder.MacroReplaceList(info.ImpressionMonitorList, a.macroInfo)
	info.ClickMonitorList = macro_builder.MacroReplaceList(info.ClickMonitorList, a.macroInfo)

	return info, creative
}

// 映射落地页类型
func mappingLandingType(bid *adglink_dsp_broker_entity.Adm) entity.LandingType {
	if len(bid.DeepLink) > 0 {
		return entity.LandingTypeDeepLink
	}
	if len(bid.WechatAppUsername) > 0 {
		return entity.LandingTypeWeChatProgram
	}
	if bid.Action == 2 && len(bid.PackageURL) > 0 {
		return entity.LandingTypeDownload
	}
	return entity.LandingTypeInWebView
}

func getCodeMsg(code int) string {
	switch code {
	case 1000:
		return "广告请求成功"
	case 1001:
		return "请求成功，但是无广告内容"
	case 1002:
		return "协议错误"
	case 1003:
		return "关键信息无效"
	default:
		return "其他错误"
	}
}

func (a *AdGlinkDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return a.slotRegister
}
