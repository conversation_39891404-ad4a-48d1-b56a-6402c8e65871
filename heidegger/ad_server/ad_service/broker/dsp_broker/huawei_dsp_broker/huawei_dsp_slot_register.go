package huawei_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type HuaWeiSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height  int    `json:"height"`
	Width   int    `json:"width"`
	AppId   string `json:"app_id"`
	AdType  int    `json:"ad_type"`
	HWPrice int    `json:"hw_price"` // 单位分
}

func (info *HuaWeiSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.AppId, err = dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get app_id from extra_data failed, err: %v", err)
	}

	adType, err := dspSlotInfo.ExtraData.GetString("ad_type")
	if err != nil {
		return fmt.Errorf("get ad_type from extra_data failed, err: %v", err)
	}

	switch adType {
	case "banner":
		info.AdType = 8
	case "native":
		info.AdType = 3
	case "splash":
		info.AdType = 1
	case "rvideo":
		info.AdType = 7
	case "insert":
		info.AdType = 12
	case "video":
		info.AdType = 60
	}

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	info.HWPrice, err = dspSlotInfo.ExtraData.GetInt("hw_price")
	if err != nil {
	}

	return nil
}

type HuaWeiDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*HuaWeiSlotSlotInfo
}

func NewHuaWeiDspSlotRegister(dspId utils.ID) *HuaWeiDspSlotRegister {
	return &HuaWeiDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*HuaWeiSlotSlotInfo),
	}
}

func (r *HuaWeiDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *HuaWeiDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*HuaWeiSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &HuaWeiSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[HuaWeiDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *HuaWeiDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *HuaWeiDspSlotRegister) GetSlotInfo(slotId utils.ID) *HuaWeiSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
