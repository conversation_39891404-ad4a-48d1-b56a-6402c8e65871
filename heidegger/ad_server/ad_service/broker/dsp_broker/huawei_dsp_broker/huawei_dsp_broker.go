package huawei_dsp_broker

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/huawei_dsp_broker/huawei_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type HuaWeiDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *HuaWeiDspSlotRegister
	//MacroWinPrice string

	monitorMacro macro_builder.MonitorMacroInfo
}

func NewHuaWeiDspBroker(dspId utils.ID) *HuaWeiDspBroker {
	return &HuaWeiDspBroker{
		slotRegister: NewHuaWeiDspSlotRegister(dspId),
		//MacroWinPrice: "SECOND_PRICE",
		monitorMacro: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "SECOND_PRICE",
			MacroClickDownX: "__HW_DOWN_X__",
			MacroClickDownY: "__HW_DOWN_Y__",
			MacroClickUpX:   "__HW_UP_X__",
			MacroClickUpY:   "__HW_UP_Y__",
			MacroHWSld:      "__HW_SLD__",
		},
	}
}

func getPostReqDigestHeader(uri, workKey, appId, keyId string) string {
	nonce := time.Now().UnixNano() / int64(time.Millisecond)
	digestSb := strings.Builder{}
	digestSb.WriteString(fmt.Sprintf("%d:POST:%s", nonce, uri))

	key := fmt.Sprintf("%s:%s:%s", appId, uri[1:], workKey)
	digest, err := encryptHMACStr(digestSb.String(), []byte(key), "HmacSHA256")
	if err != nil {
		return ""
	}

	authDigest := getAuthorizationRegexORData(appId, uri[1:], fmt.Sprintf("%d", nonce), digest, "HmacSHA256", keyId)
	return authDigest
}

func encryptHMACStr(data string, key []byte, algorithm string) (string, error) {
	bytes, err := encryptHMAC([]byte(data), key, algorithm)
	if err != nil {
		return "", err
	}
	return byteArrayToHexString(bytes), nil
}

func encryptHMAC(data, key []byte, algorithm string) ([]byte, error) {
	mac := hmac.New(sha256.New, key)
	_, err := mac.Write(data)
	if err != nil {
		return nil, err
	}
	return mac.Sum(nil), nil
}

func byteArrayToHexString(bytes []byte) string {
	return hex.EncodeToString(bytes)
}

func getAuthorizationRegexORData(appId, realm, nonce, response, algorithm, keyId string) string {
	return fmt.Sprintf("Digest username=%s,realm=%s,nonce=%s,response=%s,algorithm=%s,usertype=%s,keyid=%s",
		appId, realm, nonce, response, algorithm, "1", keyId)
}

func (impl *HuaWeiDspBroker) Authorization(appId string) string {
	keyId := impl.GetIKey()
	secretKey := impl.GetEKey()
	requestUri := "/ppsadx/getResult"

	return getPostReqDigestHeader(requestUri, secretKey, appId, keyId)
}

func (impl *HuaWeiDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *HuaWeiDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("HuaWeiDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	if request.IsDebug {
		reqBody, _ := json.Marshal(request)
		zap.L().Info("HuaWeiDspBroker.EncodeRequest Ctx req", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))

	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	hwRequest := &huawei_broker_entity.HwBidRequest{
		Version: "3.4",
		App: &huawei_broker_entity.App{
			Version: trafficData.GetAppVersion(),
			Name:    trafficData.GetAppName(),
			PkgName: trafficData.GetAppBundle(),
			Lang:    "zh",
			Country: "CN",
		},
		Device: impl.encodeDevice(request, trafficData),
		Network: &huawei_broker_entity.Network{
			Type:    impl.mappingConnectionType(trafficData.GetConnectionType()),
			Carrier: impl.mappingCarrier(trafficData.GetOperatorType()),
		},
		// ClientAdRequestId: trafficData.GetRequestId(),
		ParentCtrlUser:    0,
		NonPersonalizedAd: 0,
		Tmax:              500,
	}

	if request.Device.Lon != 0 {
		hwRequest.Geo = &huawei_broker_entity.Geo{
			Lon: request.Device.Lon,
			Lat: request.Device.Lat,
		}
	}

	var err error
	hwRequest.Multislot, err = impl.encodeSlot(request, trafficData, candidate)
	if err != nil {
		return nil, err_code.ErrInvalidImpression
	}

	//buffer := buffer_pool.NewBufferWriter()
	//defer buffer.Release()
	//
	//_, err = easyjson.MarshalToWriter(hwRequest, buffer)
	//if err != nil {
	//	zap.L().Error("HuaWeiDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
	//	return nil, err_code.ErrBroadcastRequestBuildFail
	//}
	//
	//body := buffer.Get()

	//body, _ := easyjson.Marshal(hwRequest)
	//
	//req, err := http.NewRequest(http.MethodPost, impl.GetBidUrl(), bytes.NewBuffer(body))
	//if err != nil {
	//	zap.L().Error("HuaWeiDspBroker http NewRequest err", zap.Error(err))
	//	return nil, err
	//}

	req, _, err := impl.BuildSonicJsonHttpRequest(hwRequest)
	if err != nil {
		zap.L().Error("HuaWeiDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}

	authorization := impl.Authorization(dspSlot.AppId)
	req.Header.Set("Authorization", authorization)

	if request.IsDebug {
		reqbody, _ := sonic.Marshal(hwRequest)
		zap.L().Info("HuaWeiDspBroker.EncodeRequest end, huaweiRequest: ,Authorization", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqbody)))), zap.String("param2", fmt.Sprintf("%v", authorization)))
	}

	impl.SampleDspBroadcastSonicJsonRequest(impl.GetDspId(), dspSlot.Id, candidate, hwRequest)

	return req, nil
}

func (impl *HuaWeiDspBroker) encodeSlot(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData, candidate *ad_service.AdCandidate) ([]*huawei_broker_entity.Adslot30, error) {
	slots := make([]*huawei_broker_entity.Adslot30, 0)
	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return slots, fmt.Errorf("empty slot")
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, fmt.Errorf("empty slot")
	}

	bidFloor := candidate.GetBidFloor()

	floatBidFloor := float64(bidFloor.Price) / float64(100)

	slot := &huawei_broker_entity.Adslot30{
		SlotID:        slotId,
		AdType:        dspSlot.AdType,
		Test:          0,
		TotalDuration: int(request.VideoMaxDuration),
		Orientation:   1,
		BidFloor:      floatBidFloor,
	}

	slots = append(slots, slot)

	return slots, nil

}

func (impl *HuaWeiDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) *huawei_broker_entity.Device {
	device := &huawei_broker_entity.Device{
		Type:                impl.mappingDeviceType(trafficData.GetDeviceType()),
		UserAgent:           trafficData.GetUserAgent(),
		OS:                  impl.mappingOsType(trafficData.GetOsType()),
		Version:             trafficData.GetOsVersion(),
		Maker:               strings.ToUpper(trafficData.GetBrand()),
		Model:               trafficData.GetModel(),
		Width:               int(trafficData.GetScreenWidth()),
		Height:              int(trafficData.GetScreenHeight()),
		Language:            "zh",
		BuildVersion:        "",
		Dpi:                 int(request.Device.PPI),
		PxRatio:             0,
		Imei:                trafficData.GetImei(),
		Oaid:                trafficData.GetOaid(),
		AndroidID:           trafficData.GetAndroidId(),
		IsTrackingEnabled:   "1",
		EmuiVer:             "",
		LocaleCountry:       "CN",
		SimCountryIso:       "CN",
		BelongCountry:       "CN",
		GaidTrackingEnabled: "1",
		Gaid:                "",
		VerCodeOfHms:        request.Device.VercodeHms,
		ClientTime:          time.Now().Format("2006-01-02 15:04:05.000Z0700"),
		VerCodeOfAG:         request.Device.VercodeAg,
		VendorCountry:       "CN",
		RoLocaleCountry:     "CN",
		AgCountryCode:       request.Device.CountryCode,
		RouterCountry:       "CN",
		RoLocale:            "",
		IP:                  trafficData.GetRequestIp(),
	}

	if device.AgCountryCode == "" {
		device.AgCountryCode = "CN"
	}

	return device
}

func (impl *HuaWeiDspBroker) mappingOsType(s entity.OsType) string {
	switch s {
	case entity.OsTypeAndroid:
		return "android"
	case entity.OsTypeIOS:
		return "ios"
	default:
		return "android"
	}
}

func (impl *HuaWeiDspBroker) mappingDeviceType(s entity.DeviceType) int {
	switch s {
	case entity.DeviceTypeMobile:
		return 4
	case entity.DeviceTypePad:
		return 5
	default:
		return 4
	}
}

func (impl *HuaWeiDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionTypeCellular:
		return 3
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 0
	}
}

func (impl *HuaWeiDspBroker) mappingCarrier(carrier entity.OperatorType) int {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 2
	case entity.OperatorTypeChinaTelecom:
		return 3
	case entity.OperatorTypeTietong:
		return 3
	case entity.OperatorTypeChinaUnicom:
		return 1
	default:
		return 0
	}
}

func (impl *HuaWeiDspBroker) chargePriceEncoder(chargePrice uint32) string {
	//明文替换
	return strconv.FormatFloat(float64(chargePrice)/100, 'f', 2, 64)
	//result, err := impl.PriceManager.PlaintextEncode(uint64(chargePrice))
	////result, err := impl.PriceManager.GetDspCoder(impl.dspProtocol).EncodeWithKey(uint64(chargePrice), impl.iKey, impl.eKey)
	//if err != nil {
	//	return ""
	//}
	//
	//return result
}

func (impl *HuaWeiDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("HuaWeiDspBroker.ParseResponse Enter")
	broadcastCandidate := broadcastCandidateList[0]

	if resp.StatusCode != 200 {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.StatusCode), type_convert.GetAssertString(resp.StatusCode))
		if resp.StatusCode == 204 {
			return nil, err_code.ErrBroadcastNoBidding
		} else {
			data, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err_code.ErrBrokerResponse.Wrap(err)
			}

			if request.IsDebug {
				zap.L().Error("[HuaWeiDspBroker] StatusCode:,body:%s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(resp.StatusCode)))), zap.Int64("param2", int64(data)))
			}
			return nil, err_code.ErrBrokerRequest
		}

	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &huawei_broker_entity.HwBidResponse{}
	//err = easyjson.Unmarshal(data, response)
	resBody, err := impl.ParseSonicJsonHttpResponse(resp, data, response)
	if err != nil {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		zap.L().Error("HuaWeiDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	impl.SampleDspBroadcastResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := sonic.Marshal(response)
		zap.L().Info("HuaWeiDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}
	impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(response.RetCode), impl.GetCodeMsg(response.RetCode))

	if response.RetCode != 200 || len(response.MultiAd) == 0 || len(response.MultiAd[0].Content) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, resBid := range response.MultiAd[0].Content {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		if resBid.MetaData == nil {
			continue
		}

		if resBid.MetaData.ApkInfo != nil {
			candidateAd.AppInfo = &entity.AppInfo{
				PackageName: resBid.MetaData.ApkInfo.PackageName,
				Icon:        resBid.MetaData.ApkInfo.AppIcon,
				AppVersion:  resBid.MetaData.ApkInfo.VersionName,
				PackageSize: int(resBid.MetaData.ApkInfo.FileSize),
				Privacy:     resBid.MetaData.ApkInfo.PrivacyPolicy,
				Permission:  resBid.MetaData.ApkInfo.PermissionURL,
				Develop:     resBid.MetaData.ApkInfo.DeveloperName,
				AppDescURL:  resBid.MetaData.ApkInfo.DetailURL,
			}

			if len(resBid.MetaData.PrivacyURL) > 0 {
				candidateAd.AppInfo.Privacy = resBid.MetaData.PrivacyURL
			}

			candidateAd.AppInfo.AppName, _ = url.QueryUnescape(resBid.MetaData.ApkInfo.AppName)
			candidateAd.AppInfo.AppDesc, _ = url.QueryUnescape(resBid.MetaData.ApkInfo.AppDesc)

			for _, perm := range resBid.MetaData.ApkInfo.Permissions {
				permissionLabel, _ := url.QueryUnescape(perm.PermissionLabel)
				groupDesc, _ := url.QueryUnescape(perm.GroupDesc)
				candidateAd.AppInfo.PermissionDesc = append(candidateAd.AppInfo.PermissionDesc, entity.PermissionDesc{
					PermissionLab:  permissionLabel,
					PermissionDesc: groupDesc,
				})
			}
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

		// wechat miniprogram(after parse tracking data)
		if resBid.MetaData.PromoteInfo != nil &&
			resBid.MetaData.PromoteInfo.Type == 2 &&
			len(resBid.MetaData.MiniProgramID) > 0 &&
			len(resBid.MetaData.MiniProgramLink) > 0 {
			if candidateAd.AppInfo == nil {
				candidateAd.AppInfo = new(entity.AppInfo)
			}
			candidateAd.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   resBid.MetaData.MiniProgramID,
				ProgramPath: resBid.MetaData.MiniProgramLink,
			}
			candidateAd.AdMonitorInfo.LandingAction = entity.LandingTypeWeChatProgram
		}

		candidateCreative := impl.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(resBid.Price * 100))
		candidate.SetBidType(entity.BidTypeCpm)
		if resBid.Price == 0 {
			dspSlot := impl.slotRegister.GetSlotInfo(broadcastCandidate.GetModifiedTrafficData().GetDspSlotId())
			if dspSlot.HWPrice > 0 {
				candidate.SetBidPrice(uint32(dspSlot.HWPrice))
			} else {
				pricingStrategy := broadcastCandidate.GetPricingStrategy()
				//不出价，使用固定出价代替
				if pricingStrategy.GetBiddingStrategyType() == entity.BiddingStrategyTypeStaticPrice {
					bidPrice := pricingStrategy.GetBidPrice()
					candidate.SetBidPrice(uint32(bidPrice.Price))
					candidate.SetBidType(bidPrice.Type)
				}
			}
		}
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(resBid.Taskid)
		result = append(result, candidate)
		break
	}
	return result, nil
}

func (impl *HuaWeiDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid huawei_broker_entity.Content) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		LandingUrl:            bid.MetaData.ClickURL,
		H5LandingUrl:          bid.MetaData.ClickURL,
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      make([]string, 0),
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkMonitorList:   make([]string, 0),
		VideoStartUrlList:     make([]string, 0),
	}

	switch bid.InteractionType {
	case 2:
		tracking.LandingAction = entity.LandingTypeDownload
	case 3, 5, 7:
		tracking.LandingAction = entity.LandingTypeDeepLink
	}

	if strings.HasPrefix(bid.MetaData.Intent, "hwpps://landingpage") { // 支持静默安装
		tracking.DeepLinkUrl = bid.MetaData.Intent
		tracking.LandingAction = entity.LandingTypeDeepLink
	} else {
		if bid.InteractionType == 2 {
			if bid.MetaData.ApkInfo != nil && (len(bid.MetaData.ApkInfo.URL) > 0 || len(bid.MetaData.ApkInfo.SecondURL) > 0) {
				tracking.DownloadUrl = bid.MetaData.ApkInfo.URL
				if len(tracking.DownloadUrl) < 1 {
					tracking.DownloadUrl = bid.MetaData.ApkInfo.SecondURL
				}
			}
			tracking.DeepLinkUrl = "" //清空dp
		} else {
			tracking.DeepLinkUrl, _ = url.QueryUnescape(bid.MetaData.Intent)
		}
	}

	if bid.InteractionType == 0 || bid.InteractionType == 1 {
		tracking.DeepLinkUrl = "" //清空dp
	}

	if len(tracking.LandingUrl) == 0 {
		if bid.MetaData.ApkInfo != nil {
			if len(bid.MetaData.ApkInfo.URL) > 0 {
				tracking.LandingUrl = bid.MetaData.ApkInfo.URL
			} else if len(bid.MetaData.ApkInfo.SecondURL) > 0 {
				tracking.LandingUrl = bid.MetaData.ApkInfo.SecondURL
			} else {
				tracking.LandingUrl = bid.MetaData.ApkInfo.DetailURL
			}
		}
	}

	if len(bid.Nurl) > 0 {
		//if strings.Contains(bid.Nurl, impl.MacroWinPrice) {
		//	newImpTrack := strings.ReplaceAll(bid.Nurl, impl.MacroWinPrice, "__DSPWPRICE__")
		//	tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		//} else {
		//	tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.Nurl)
		//}
		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impl.monitorMacro.MacroReplace(bid.Nurl))
	}

	for _, monitor := range bid.Monitor {
		switch monitor.EventType {
		case "imp":
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impl.monitorMacro.MacroReplaceList(monitor.URL)...)
			//for _, impTrack := range monitor.URL {
			//	if strings.Contains(impTrack, impl.MacroWinPrice) {
			//		newImpTrack := strings.ReplaceAll(impTrack, impl.MacroWinPrice, "__DSPWPRICE__")
			//		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
			//	} else {
			//		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impTrack)
			//	}
			//}
		case "click":
			tracking.ClickMonitorList = impl.monitorMacro.MacroReplaceList(monitor.URL)
		case "playStart":
			tracking.VideoStartUrlList = monitor.URL
		//case "playPause":
		//case "playResume":
		case "playEnd":
			tracking.VideoCloseUrlList = monitor.URL
		case "download":
			tracking.AppDownloadFinishedMonitorList = monitor.URL
		case "downloadstart":
			tracking.AppDownloadStartedMonitorList = monitor.URL
		case "intentSuccess":
			tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, monitor.URL...)
		case "appOpen":
			tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, monitor.URL...)
			//tracking.AppOpenMonitorList = monitor.URL
		case "install":
			tracking.AppInstalledFinishMonitorList = monitor.URL
		}

	}

	return tracking

}

func (impl *HuaWeiDspBroker) ParseCreativeData(bid huawei_broker_entity.Content) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.ContentID,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}
	//bid.CreativeType

	item := bid.MetaData

	if len(item.Title) > 0 {
		title, _ := url.QueryUnescape(item.Title)
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Description) > 0 {
		desc, _ := url.QueryUnescape(item.Description)
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.ImageInfo) > 0 {
		for _, img := range item.ImageInfo {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img.URL,
				Width:        int32(img.Width),
				Height:       int32(img.Height),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if item.VideoInfo != nil {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          item.VideoInfo.VideoDownloadURL,
			Duration:     float64(item.VideoInfo.VideoDuration / 1000),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if item.MediaFile != nil {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          item.MediaFile.URL,
			Duration:     float64(item.Duration / 1000),
			Width:        int32(item.MediaFile.Width),
			Height:       int32(item.MediaFile.Height),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if item.ApkInfo != nil && len(item.ApkInfo.AppIcon) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          item.ApkInfo.AppIcon,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}

func (impl *HuaWeiDspBroker) GetCodeMsg(code int) string {
	switch code {
	case 204:
		return "没有匹配到广告"
	case 206:
		return "部分广告返回素材的情况下返回此码"
	case 400:
		return "请求数据格式错"
	case 401:
		return "请求方法错误"
	case 403:
		return "广告请求的接口版本号错误"
	case 430:
		return "App 信息缺失"
	case 431:
		return "应用包名缺失"
	case 432:
		return "应用包名在服务端不存在"
	case 439:
		return "应用其它信息错误"
	case 440:
		return "Device 信息缺失"
	case 441:
		return "设备尺寸缺失"
	case 442:
		return "设备至少应上报 IMEI、OAID 或 GAID 中的一个"
	case 449:
		return "设备其它信息错误"
	case 450:
		return "network 信息缺失"
	case 451:
		return "网络连接类型缺失"
	case 452:
		return "网络连接类型错误"
	case 459:
		return "网络其它信息错误"
	case 460:
		return "multislot 信息缺失"
	case 462:
		return "几个广告位不是来自同一个应用"
	case 471:
		return "该机型用户暂不投放"
	case 472:
		return "该用户的机型不在机型定向"
	case 473:
		return "用户归属国和用户当前所在国非同一国家"
	case 474:
		return "该站点不能处理该用户归属国/用户当前国过来的请求"
	case 475:
		return "没有是否允许使用 GAID 跟踪的标识"
	case 498:
		return "其它参数错误"
	case 499:
		return "其它未知请求错误"
	case 500:
		return "系统内部错误"
	default:
		return utils.EmptyString
	}
}
