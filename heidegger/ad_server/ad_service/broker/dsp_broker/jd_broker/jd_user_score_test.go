package jd_broker

import (
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/user_segment"
	"testing"
	"time"
)

func TestJdUserScore_ObserveScoreChange(t *testing.T) {
	jdUserScore := &JdUserScore{}
	for i := 0; i < 10; i++ {
		for j := 0; j < i; j++ {
			zap.L().Info("")
			zap.L().Info("=== req: , resp:  ===", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(i)))), zap.Int64("param2", int64(j)))
			zap.L().Info("")
			for _, price := range []float64{50, 100, 500, 1000, 2000, 5000, 10000} {
				score := jdUserScore.calcScore(float64(i), float64(j), float64(j)*price)
				zap.L().Info("req: , resp: , price:%f, score: %f", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(i)))), zap.Int64("param2", int64(j)), zap.Int64("param3", int64(price)), zap.Int64("param4", int64(score)))
			}
		}
	}
}

func TestJdUserScore_Update(t *testing.T) {
	clients := []*redis.Client{
		redis.NewClient(&redis.Options{
			Addr: "localhost:6379",
		}),
	}
	c := user_segment.NewRedisHashUserSegmentClient(clients)
	if err := c.Start(); err != nil {
		t.Fatal(err)
	}

	device := "2"

	jdUserScore := &JdUserScore{}
	{
		err := jdUserScore.UpdateUserScore(c, device, 20004, 1, 0, 0)
		if err != nil {
			t.Fatal(err)
		}

		time.Sleep(time.Second)

		userSegment, err := c.GetUserSegment(device)
		if err != nil {
			t.Fatal(err)
		}

		score, err := jdUserScore.ReadUserScore(userSegment, 20004)
		if err != nil {
			t.Fatal(err)
		}

		zap.L().Info("score", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(score)))))
	}

	{
		err := jdUserScore.UpdateUserScore(c, device, 20005, 1, 1, 50000)
		if err != nil {
			t.Fatal(err)
		}

		time.Sleep(time.Second)

		userSegment, err := c.GetUserSegment(device)
		if err != nil {
			t.Fatal(err)
		}

		score, err := jdUserScore.ReadUserScore(userSegment, 20004)
		if err != nil {
			t.Fatal(err)
		}

		zap.L().Info("score", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(score)))))
	}

	{
		err := jdUserScore.UpdateUserScore(c, device, 20004, 0, 0, 0)
		if err != nil {
			t.Fatal(err)
		}

		time.Sleep(time.Second)

		userSegment, err := c.GetUserSegment(device)
		if err != nil {
			t.Fatal(err)
		}

		score, err := jdUserScore.ReadUserScore(userSegment, 20004)
		if err != nil {
			t.Fatal(err)
		}

		zap.L().Info("score", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(score)))))
	}

}

func TestShift(t *testing.T) {
	score := JdUserScore{}
	key, day := score.parseKeyMask(3459973128)
	t.Log(key, day)
}
