package jd_broker

import (
	"encoding/base64"
	"encoding/json"
	"io"
	"math"
	"net"
	"net/http"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jd_broker/jd_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"fmt"
)

const (
	jdAPPPackage    = "com.jingdong.app.mall"
	jdIosAPPPackage = "com.360buy.jdmobile"
)

var (
	// not responsing status code due to device
	jdNoBidDeviceStatusCode = map[int32]struct{}{
		2002: {},
		4023: {},
		4044: {},
		4082: {},
		4083: {},
		5000: {},
		5003: {},
	}
)

type JdPBDspBroker struct {
	dsp_broker.DspBrokerBase
	jdScore JdUserScore

	slotRegister  *JdDspSlotRegister
	MacroWinPrice string
}

func NewJdPBDspBroker(dspId utils.ID) *JdPBDspBroker {
	return &JdPBDspBroker{
		slotRegister:  NewJdDspSlotRegister(dspId),
		MacroWinPrice: "%%WIN_PRICE%%",
	}
}

func (impl *JdPBDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *JdPBDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("JdPBDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	if request.IsDebug {
		reqBody, _ := json.Marshal(request)
		zap.L().Info("JdPBDspBroker.EncodeRequest Ctx req", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	jdRequest := &jd_proto.BidRequest{
		Id:      trafficData.GetRequestId(),
		Version: "4.0",
		App: &jd_proto.App{
			Bundle: dspSlot.PkgName,
			AppId:  dspSlot.AppId,
		},

		Device: impl.encodeJdDeviceRequest(request, trafficData),
		User: &jd_proto.User{
			Geo: &jd_proto.Geo{
				LongitudeEnc: impl.encodeBase64Geo(trafficData.GetGeoLongitude()),
				LatitudeEnc:  impl.encodeBase64Geo(trafficData.GetGeoLatitude()),
			},
			JdAppInstalled: false,
		},
	}

	if len(jdRequest.App.Bundle) == 0 {
		jdRequest.App.Bundle = trafficData.GetAppBundle()
	}

	if impl.isDeviceEmpty(jdRequest.Device) {
		return nil, err_code.ErrInvalidDeviceId
	}

	for _, pkg := range request.App.InstalledApp {
		if pkg == jdIosAPPPackage && request.Device.OsType == entity.OsTypeIOS || pkg == jdAPPPackage {
			jdRequest.User.JdAppInstalled = true
			break
		}
	}

	imp, err := impl.encodeJdImpRequest(request, trafficData, candidate)
	if err != nil {
		return nil, err_code.ErrInvalidImpression

	}

	jdRequest.Impressions = imp

	httpRequest, _, err := impl.BuildPbHttpHttpRequest(jdRequest)
	if err != nil {
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	impl.SampleDspBroadcastProtobufRequest(impl.GetDspId(), dspSlot.Id, candidate, jdRequest)

	if request.IsDebug {
		zap.L().Info("JdPBDspBroker url:, send:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", impl.GetBidUrl())))), jdRequest.String())
	}
	return httpRequest, err
}

func (impl *JdPBDspBroker) encodeJdImpRequest(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData, candidate *ad_service.AdCandidate) ([]*jd_proto.Impression, error) {
	imps := make([]*jd_proto.Impression, 0)

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return imps, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	imp := &jd_proto.Impression{
		Id:    request.ImpressionId,
		TagId: slotId,
		//Pmp:   nil,
	}

	if len(imp.Id) == 0 {
		imp.Id = request.GetRequestId()
	}

	imps = append(imps, imp)
	return imps, nil
}

func (impl *JdPBDspBroker) encodeBase64Geo(lon float64) string {
	if lon == 0 {
		return ""
	}

	// Convert the float64 to its binary representation
	bits := math.Float64bits(lon)

	// Convert the uint64 bits to a byte slice
	byteSlice := make([]byte, 8)
	for i := uint(0); i < 8; i++ {
		byteSlice[i] = byte(bits >> (56 - 8*i))
	}

	// Base64 encode the byte slice
	encoded := base64.StdEncoding.EncodeToString(byteSlice)
	return encoded
}

func (impl *JdPBDspBroker) encodeBase64IP(ipStr string, isIPV6 bool) string {
	// Parse the IP address
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return ""
	}

	var ipBytes []byte

	if isIPV6 {
		ipBytes = ip.To16()
	} else {
		ipBytes = ip.To4()
	}

	if ipBytes == nil {
		return ""
	}

	// Base64 encode the byte slice
	encoded := base64.StdEncoding.EncodeToString(ipBytes)
	return encoded
}

func (impl *JdPBDspBroker) isDeviceEmpty(device *jd_proto.Device) bool {
	switch device.Os {
	case jd_proto.OperatingSystem_OS_ANDROID:
		if len(device.Imei) == 0 && len(device.ImeiMd5) == 0 && len(device.Oaid) == 0 && len(device.OaidMd5) == 0 {
			return true
		}
	case jd_proto.OperatingSystem_OS_IOS:
		if len(device.Imei) == 0 && len(device.ImeiMd5) == 0 && len(device.Idfa) == 0 && len(device.IdfaMd5) == 0 && len(device.Caids) == 0 {
			return true
		}

	}

	return false
}

func (impl *JdPBDspBroker) encodeJdDeviceRequest(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) *jd_proto.Device {
	device := &jd_proto.Device{
		Os:           impl.mappingOs(trafficData.GetOsType()),
		OsVersion:    trafficData.GetOsVersion(),
		OsUpdateTime: trafficData.GetUpdateMark(),
		Imei:         trafficData.GetImei(),
		ImeiMd5:      trafficData.GetMd5Imei(),
		Oaid:         trafficData.GetOaid(),
		OaidMd5:      trafficData.GetMd5Oaid(),
		Idfa:         trafficData.GetIdfa(),
		IdfaMd5:      trafficData.GetMd5Idfa(),
		Ip:           trafficData.GetRequestIp(),
		//IpEnc:          impl.encodeBase64IP(trafficData.GetRequestIp(), request.Device.IsIp6),
		Ua:             trafficData.GetUserAgent(),
		ConnectionType: impl.mappingConnectionType(trafficData.GetConnectionType()),
		Make:           trafficData.GetBrand(),
		Model:          trafficData.GetModel(),
		HwName:         request.Device.DeviceName,
		HwMachine:      request.Device.HardwareMachineCode,
		Carrier:        impl.mappingCarrier(trafficData.GetOperatorType()),
		ScreenHeight:   trafficData.GetScreenHeight(),
		ScreenWidth:    trafficData.GetScreenWidth(),
		Ppi:            request.Device.PPI,
	}

	if len(device.HwName) == 0 {
		device.HwName = trafficData.GetBrand() + " " + trafficData.GetModel()
	}
	device.HwName = md5_utils.GetMd5String(device.HwName)

	if len(device.HwMachine) == 0 {
		device.HwMachine = trafficData.GetModel()
	}

	device.Caids = make([]*jd_proto.Device_Caid, 0)
	if len(trafficData.GetCaid()) > 0 {
		deviceCaid := &jd_proto.Device_Caid{
			Id:      device_utils.GetCaidRaw(trafficData.GetCaid()),
			Version: device_utils.GetCaidVersion(trafficData.GetCaid()),
		}
		device.Caids = append(device.Caids, deviceCaid)
	}

	if len(request.Device.Caids) > 0 {
		for _, caidItem := range request.Device.Caids {
			if caidItem == trafficData.GetCaid() {
				continue
			}
			deviceCaid1 := &jd_proto.Device_Caid{
				Id:      device_utils.GetCaidRaw(caidItem),
				Version: device_utils.GetCaidVersion(caidItem),
			}
			device.Caids = append(device.Caids, deviceCaid1)
		}
	}

	device.Language = "zh"
	device.CountryCode = "CN"

	if request.Device.SystemTotalMem > 0 {
		device.SysMemory = type_convert.GetAssertString(request.Device.SystemTotalMem)
	}

	if request.Device.SystemTotalDisk > 0 {
		device.SysDiskSize = type_convert.GetAssertString(request.Device.SystemTotalDisk)
	}

	return device
}

func (impl *JdPBDspBroker) mappingOs(os entity.OsType) jd_proto.OperatingSystem {
	switch os {
	case entity.OsTypeIOS:
		return jd_proto.OperatingSystem_OS_IOS
	case entity.OsTypeAndroid:
		return jd_proto.OperatingSystem_OS_ANDROID
	default:
		return jd_proto.OperatingSystem_OS_UNKNOWN
	}
}

func (impl *JdPBDspBroker) mappingConnectionType(connectionType entity.ConnectionType) jd_proto.ConnectionType {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return jd_proto.ConnectionType_ETHERNET
	case entity.ConnectionTypeWifi:
		return jd_proto.ConnectionType_WIFI
	case entity.ConnectionTypeUnknown:
		return jd_proto.ConnectionType_CELLULAR_UNKNOWN
	case entity.ConnectionType2G:
		return jd_proto.ConnectionType_CELLULAR_2G
	case entity.ConnectionType3G:
		return jd_proto.ConnectionType_CELLULAR_3G
	case entity.ConnectionType4G:
		return jd_proto.ConnectionType_CELLULAR_4G
	case entity.ConnectionType5G:
		return jd_proto.ConnectionType_CELLULAR_5G
	default:
		return jd_proto.ConnectionType_UNKNOWN
	}
}

func (impl *JdPBDspBroker) mappingCarrier(carrier entity.OperatorType) jd_proto.CarrierType {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return jd_proto.CarrierType_CHINA_MOBILE
	case entity.OperatorTypeChinaTelecom, entity.OperatorTypeTietong:
		return jd_proto.CarrierType_CHINA_TELECOM
	case entity.OperatorTypeChinaUnicom:
		return jd_proto.CarrierType_CHINA_UNICOM
	default:
		return jd_proto.CarrierType_UNKNOWN_CARRIER
	}
}

func (impl *JdPBDspBroker) chargePriceEncoder(chargePrice uint32) string {
	//不需要加密
	return ""
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *JdPBDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("JdPBDspBroker.ParseResponse Enter")
	broadcastCandidate := broadcastCandidateList[0]
	if resp.StatusCode != 200 {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.StatusCode), type_convert.GetAssertString(resp.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBroadcastResponseReadBodyFail.Wrap(err)
	}

	jdResp := &jd_proto.BidResponse{}
	if err := impl.ParsePbHttpHttpResponse(resp, body, jdResp); err != nil {
		zap.L().Debug("JdPBDspBroker.DecodeResponse json.Unmarshal, err", zap.Error(err))
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	impl.SampleDspBroadcastProtobufResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, jdResp)

	if request.IsDebug {
		resBody, _ := json.Marshal(jdResp)
		zap.L().Info("JdDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}

	impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(jdResp.StatusCode), jdResp.StatusMessage)

	if jdResp.StatusCode != 0 || len(jdResp.SeatBids) < 1 {
		if _, ok := jdNoBidDeviceStatusCode[jdResp.StatusCode]; ok {
			impl.tempBlockDevice(request, broadcastCandidate)
		}
		return nil, err_code.ErrBroadcastNoBidding
	}

	trafficData := broadcastCandidate.GetModifiedTrafficData()
	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())

	result := make(ad_service.DspAdCandidateList, 0)
	for _, resBid := range jdResp.SeatBids[0].Bids {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = &entity.AppInfo{
			PackageName: jdAPPPackage,
			AppName:     "京东",
		}

		if request.Device.OsType == entity.OsTypeIOS {
			candidateAd.AppInfo.PackageName = jdIosAPPPackage
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

		candidateCreative, hasVideo := impl.ParseCreativeData(resBid, dspSlot, broadcastCandidate.GetAd().AdId)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		if hasVideo && dspSlot != nil && len(dspSlot.TemplateIdMaps) > 0 {
			if _, ok := dspSlot.TemplateIdMaps["video"]; !ok {
				return nil, err_code.ErrBrokerResponseVideoNotSupport
			}
		}

		candidateAd.AdExtInfo = &entity.AdExtInfo{
			AdSource: "jd",
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(resBid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(resBid.AdId)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		candidate.SetDspUserScore(broadcastCandidate.GetUserScore())

		result = append(result, candidate)
		break

	}

	return result, nil
}

func (impl *JdPBDspBroker) ParseCreativeData(bid *jd_proto.Bid, dspSlot *JdSlotSlotInfo, adId utils.ID) (*entity.Creative, bool) {
	if bid.Adm == nil || len(bid.Adm.Items) == 0 {
		zap.L().Warn("JdPBDspBroker.parseMaterialData Adm.Items is empty")
		return nil, false
	}

	hasVideo := false

	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.AdId,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	item := bid.Adm.Items[0]

	if len(item.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         item.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "惊喜直降 价同大促",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	// text, image, video, icon, logo, title, digest, advertiser,subtitle
	if len(item.Desc) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         item.Desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if item.Video != nil && len(item.Video.Url) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          item.Video.Url,
			Width:        int32(item.Video.Width),
			Height:       int32(item.Video.Height),
			Duration:     float64(item.Video.Duration) / 1000,
		}
		creative.MaterialList = append(creative.MaterialList, material)
		hasVideo = true
	}

	if len(item.IconUrl) > 0 {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          item.IconUrl,
		}
		creative.MaterialList = append(creative.MaterialList, materialImg)
	} else {
		if dspSlot != nil && len(dspSlot.Icon) > 0 {
			materialImg := &entity.Material{
				MaterialType: entity.MaterialTypeIcon,
				Url:          dspSlot.Icon,
			}
			creative.MaterialList = append(creative.MaterialList, materialImg)
		}
	}

	if len(item.Imgs) > 0 {
		for _, img := range item.Imgs {
			if len(img.Url) == 0 {
				continue
			}

			materialImg := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img.Url,
				Width:        int32(img.Width),
				Height:       int32(img.Height),
			}
			creative.MaterialList = append(creative.MaterialList, materialImg)
		}
	}

	return creative, hasVideo
}

func (impl *JdPBDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *jd_proto.Bid) *entity.AdMonitorInfo {
	if bid.Adm == nil || len(bid.Adm.Items) == 0 {
		zap.L().Warn("JdPBDspBroker.parseMaterialData Adm.Items is empty")
		return nil
	}

	item := bid.Adm.Items[0]

	tracking := &entity.AdMonitorInfo{
		LandingUrl:            item.ClickUrl,
		H5LandingUrl:          item.ClickUrl,
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      item.ClickMonitorUrls,
		LandingAction:         entity.LandingTypeInWebView,
	}

	//for _, impTrack := range item.ExposalUrls {
	//	if strings.Contains(impTrack, impl.MacroWinPrice) {
	//		newImpTrack := strings.ReplaceAll(impTrack, impl.MacroWinPrice, "__DSPWPRICE__")
	//		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
	//	} else {
	//		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impTrack)
	//	}
	//}

	tracking.ImpressionMonitorList = item.ExposalUrls

	if len(item.NoticeUrl) > 0 {
		//if strings.Contains(item.NoticeUrl, impl.MacroWinPrice) {
		//	newImpTrack := strings.ReplaceAll(item.NoticeUrl, impl.MacroWinPrice, "__DSPWPRICE__")
		//	tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		//} else {
		//	tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, item.NoticeUrl)
		//}
		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, item.NoticeUrl)
	}

	tracking.DeepLinkUrl = item.DeeplinkUrl

	if request.Device.GetOsType() == entity.OsTypeIOS && len(item.UniversalLinkUrl) > 0 {
		tracking.DeepLinkUrl = item.UniversalLinkUrl
	}

	if len(tracking.DeepLinkUrl) > 0 {
		tracking.LandingAction = entity.LandingTypeDeepLink
	}

	return tracking
}

func (impl *JdPBDspBroker) tempBlockDevice(request *ad_service.AdRequest, candidate *ad_service.AdCandidate) {
	if impl.GetUserSegmentClient() == nil {
		return
	}

	trafficData := candidate.GetModifiedTrafficData()
	deviceId, _ := trafficData.GetDeviceIdWithType()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil || dspSlot.BlockNoBidDevice <= 0 {
		return
	}

	expireTime := dspSlot.BlockNoBidDevice * 3600

	if err := impl.GetUserSegmentClient().AddUserSegmentAsync(deviceId, entity.UserTagJdBlackDeviceTag, 1, uint32(expireTime), 0); err != nil {
		zap.L().Error("JdPBDspBroker.tempBlockDevice AddUserSegmentAsync error", zap.Error(err))
		return
	}
}

func (impl *JdPBDspBroker) CheckBroadcastContext(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) error {
	for _, adCandidate := range candidateList {
		trafficData := adCandidate.GetModifiedTrafficData()
		dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
		if dspSlot == nil || dspSlot.BlockNoBidDevice <= 0 {
			continue
		}

		if request.UserSegment.ContainsTag(entity.UserTagJdBlackDeviceTag) {
			return err_code.ErrBlackDeviceId
		}
	}

	return nil
}
