package jd_broker

import (
	"fmt"
	"strings"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

/*
{"dsp_slot_ext_attrs":[{"key":"app_id","label":"APP ID","is_number":false,"multiple":false,"required":true,"comma_separated":false},{"key":"pkg_name","label":"包名","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"width","label":"广告位宽","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"height","label":"广告位高","is_number":false,"multiple":false,"required":false,"comma_separated":false},{"key":"template_id","label":"模板ID","is_number":false,"multiple":true,"required":false,"comma_separated":true,"options":[{"label":"图片","value":"img"},{"label":"视频","value":"video"}]},{"key":"block_no_bid_device","label":"无竞价设备屏蔽(小时)","is_number":true,"multiple":false,"required":false,"comma_separated":false}]}
*/
type JdSlotSlotInfo struct {
	*entity.DspSlotInfo
	AppId            string         `json:"app_id"`
	PkgName          string         `json:"pkg_name"`
	Height           int            `json:"height"`
	Width            int            `json:"width"`
	TemplateIds      []string       `json:"template_id"`
	TemplateIdMaps   map[string]int `json:"template_id_map"`
	Icon             string         `json:"icon"`
	BlockNoBidDevice int            `json:"block_no_bid_device"` // blocking time in hour
}

func (info *JdSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.AppId, err = dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get app_id from extra_data failed, err: %v", err)
	}

	info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	info.Icon, _ = dspSlotInfo.ExtraData.GetString("icon")
	info.BlockNoBidDevice, _ = dspSlotInfo.ExtraData.GetInt("block_no_bid_device")

	templateIds, _ := dspSlotInfo.ExtraData.GetString("template_id")

	//img bimg imgs video rvideo
	info.TemplateIdMaps = make(map[string]int)
	info.TemplateIds = make([]string, 0)
	if len(templateIds) > 0 {
		info.TemplateIds = strings.Split(templateIds, ",")
		for _, templateId := range info.TemplateIds {
			switch templateId {
			case "img", "video", "bimg", "imgs", "rvideo":
				info.TemplateIdMaps[templateId] = 1
			}
		}
	}

	return nil
}

type JdDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*JdSlotSlotInfo
}

func NewJdDspSlotRegister(dspId utils.ID) *JdDspSlotRegister {
	return &JdDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*JdSlotSlotInfo),
	}
}

func (r *JdDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *JdDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*JdSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &JdSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[JdDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *JdDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *JdDspSlotRegister) GetSlotInfo(slotId utils.ID) *JdSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
