package jlhz_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

/*
配置信息初始化
insert into `ad_enums`(`title`, `value`, `parent`, `ext_data`, `create_time`, `update_time`) values
('巨量火种', 'jlhz', 594, '{"dsp_slot_ext_attrs":[{"key":"width","label":"广告位宽","type":"input","required":false,"is_number":true,"multiple":false,"comma_separated":false},
{"key":"height","label":"广告位高","type":"input","required":false,"is_number":true,"multiple":false,"comma_separated":false},{"key":"app_name","label":"APP名","type":"input",
"required":false,"is_number":false,"multiple":false,"comma_separated":false},{"key":"pkg_name","label":"包名","type":"input","required":false,"is_number":false,"multiple":false,
"comma_separated":false},{"key":"app_version","label":"APP版本","type":"input","required":false,"is_number":false,"multiple":false,"comma_separated":false}]}',
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
*/

type JlhzSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`
}

func (info *JlhzSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	info.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	info.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")

	return nil
}

type JlhzDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*JlhzSlotSlotInfo
}

func NewJlhzDspSlotRegister(dspId utils.ID) *JlhzDspSlotRegister {
	return &JlhzDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*JlhzSlotSlotInfo),
	}
}

func (y *JlhzDspSlotRegister) GetDspId() utils.ID {
	return y.dspId
}

func (y *JlhzDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	dspSlotMap := make(map[utils.ID]*JlhzSlotSlotInfo)
	for _, slotInfo := range list {
		slot := &JlhzSlotSlotInfo{}
		if err := slot.Init(slotInfo); err != nil {
			zap.L().Error("[JlhzDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		dspSlotMap[slot.Id] = slot
	}

	y.dspSlotList = list
	y.dspSlotMap = dspSlotMap
	return nil
}

func (y *JlhzDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return y.dspSlotList
}

func (y *JlhzDspSlotRegister) GetSlotInfo(slotId utils.ID) *JlhzSlotSlotInfo {
	return y.dspSlotMap[slotId]
}
