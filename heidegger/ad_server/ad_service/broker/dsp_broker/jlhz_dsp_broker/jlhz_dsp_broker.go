package jlhz_dsp_broker

import (
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jlhz_dsp_broker/jlhz_dsp_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"io"
	"net/http"
	"strconv"
	"fmt"
)

// 文档地址：https://doc.weixin.qq.com/doc/w3_AQAAwAZJAPk4SVIYySgQgeY3ZOoCm?scode=ABsACwdPAF8soHiyGmAQAAwAZJAPk
type JlhzDspBroker struct {
	dsp_broker.DspBrokerBase
	dspSlotRegister *JlhzDspSlotRegister
	log             *zap.Logger
	macroInfo       macro_builder.MonitorMacroInfo
}

func NewJlhzDspBroker(dspId utils.ID) *JlhzDspBroker {
	return &JlhzDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		dspSlotRegister: NewJlhzDspSlotRegister(dspId),
		log:             zap.L().With(zap.String("broker", "JlhzDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			//MacroWinPrice:   "__PRICE__",
			MacroClickDownX: "__X__",
			MacroClickDownY: "__Y__",
			MacroClickUpX:   "__LEVAE_X__",
			MacroClickUpY:   "__LEVAE_Y__",
		},
	}
}

func (y *JlhzDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := y.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(dspSlot.Id)
	candidate.SetDspSlotKey(slotId)

	bidRequest := &jihz_proto.AdRequest{
		SessionId: trafficData.GetRequestId(),
		App: &jihz_proto.App{
			Name:        trafficData.GetAppName(),
			Version:     trafficData.GetAppVersion(),
			PackageName: trafficData.GetAppBundle(),
		},
		Device: &jihz_proto.Device{
			UserAgent:         trafficData.GetUserAgent(),
			DeviceType:        toDeviceType(trafficData.GetDeviceType()),
			Os:                toOsType(trafficData.GetOsType()),
			Osv:               trafficData.GetOsVersion(),
			Dpi:               request.Device.DPI,
			Ppi:               request.Device.PPI,
			ScreenOrientation: toScreenOrientation(trafficData.GetScreenOrientation()),
			ScreenHeight:      trafficData.GetScreenHeight(),
			ScreenWidth:       trafficData.GetScreenWidth(),
			Idfa:              trafficData.GetIdfa(),
			Idfv:              trafficData.GetIdfv(),
			Imei:              trafficData.GetImei(),
			ImeiMd5:           trafficData.GetMd5Imei(),
			AndroidId:         trafficData.GetAndroidId(),
			AndroidIdMd5:      trafficData.GetMd5AndroidId(),
			Mac:               trafficData.GetMac(),
			MacMd5:            trafficData.GetMd5Mac(),
			Brand:             trafficData.GetBrand(),
			Model:             trafficData.GetModel(),
			Oaid:              trafficData.GetOaid(),
			OaidMd5:           trafficData.GetMd5Oaid(),
			BootMark:          trafficData.GetBootMark(),
			UpdateMark:        trafficData.GetUpdateMark(),
			HmsCore:           request.Device.VercodeHms,
			HwAgVersion:       request.Device.VercodeAg,
			AppstoreVersion:   request.Device.AppStoreVersion,
			InstallApps:       request.App.InstalledApp,
		},
		Net: &jihz_proto.Net{
			Ipv4:      trafficData.GetRequestIp(),
			Latitude:  trafficData.GetGeoLatitude(),
			Longitude: trafficData.GetGeoLongitude(),
			NetType:   toNetworkType(trafficData.GetConnectionType()),
			Operator:  toCarrierType(trafficData.GetOperatorType()),
		},
		Slot: &jihz_proto.Slot{
			SlotId:   slotId,
			Width:    int32(trafficData.GetSlotWidth()),
			Height:   int32(trafficData.GetSlotHeight()),
			SlotType: toSlotType(trafficData.GetSlotType()),
			BidFloor: candidate.GetBidFloor().Price,
		},
		User: &jihz_proto.User{
			Gender: toGender(request.UserGender),
			Age:    request.UserAge,
		},
	}
	if len(dspSlot.PkgName) > 0 {
		request.App.AppBundle = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		request.App.AppName = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		request.App.AppVersion = dspSlot.AppVersion
	}
	if request.Device.IsIp6 {
		bidRequest.Net.Ipv6 = trafficData.GetRequestIp()
	}
	if dspSlot.Width != 0 {
		bidRequest.Slot.Width = int32(dspSlot.Width)
	}

	if dspSlot.Height != 0 {
		bidRequest.Slot.Height = int32(dspSlot.Height)
	}
	if len(request.Device.Caids) > 0 {
		for _, caid := range request.Device.Caids {
			if trafficData.GetCaid() != caid {
				bidRequest.Device.Caids = append(bidRequest.Device.Caids, &jihz_proto.Caid{
					Id:      device_utils.GetCaidRaw(caid),
					Version: device_utils.GetCaidVersion(caid),
				})
			}
		}
	}

	if len(trafficData.GetCaid()) != 0 {
		bidRequest.Device.Caids = append(bidRequest.Device.Caids, &jihz_proto.Caid{
			Id:      device_utils.GetCaidRaw(trafficData.GetCaid()),
			Version: device_utils.GetCaidVersion(trafficData.GetCaid()),
		})
	}

	httpRequest, _, err := y.BuildPbHttpHttpRequest(bidRequest)
	if err != nil {
		y.log.WithError(err).Error("BuildPbHttpHttpRequest error")
		return nil, err
	}
	httpRequest.Header.Set("Content-Type", "application/octet-stream")

	y.SampleDspBroadcastProtobufRequest(y.GetDspId(), dspSlot.Id, candidate, bidRequest)

	return httpRequest, nil
}

func (y *JlhzDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	if response.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		y.log.WithError(err).Error("Read response error")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	bidResponse := &jihz_proto.AdResponse{}
	err = y.ParsePbHttpHttpResponse(response, data, bidResponse)
	if err != nil {
		y.log.WithError(err).Error("ParsePbHttpHttpResponse error")
		return nil, err
	}

	broadcastCandidate := broadcastCandidateList[0]
	y.SampleDspBroadcastProtobufResponse(y.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, bidResponse)

	if request.IsDebug {
		resBody, _ := sonic.Marshal(bidResponse)
		zap.L().Info("JlhzDspBroker.ParseResponse response,  body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}
	y.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), strconv.FormatUint(uint64(bidResponse.GetCode()), 10), bidResponse.GetErrorMessage())

	if bidResponse.GetCode() != 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	list := ad_service.DspAdCandidateList{}
	for _, responseData := range bidResponse.GetMaterial() {
		dspAdCandidate, err := y.buildDspAdCandidate(responseData, broadcastCandidate)
		if err != nil {
			y.log.WithError(err).Error("buildDspAdCandidate error")
			return nil, err_code.ErrBroadcastResponseCandidateError.Wrap(err)
		}
		list = append(list, dspAdCandidate)
	}

	return list, nil
}

func (y *JlhzDspBroker) buildDspAdCandidate(bidData *jihz_proto.Material, broadcastCandidate *ad_service.AdCandidate) (*ad_service.DspAdCandidate, error) {

	ad := &entity.Ad{
		DspId:         y.GetDspId(),
		DspSlotId:     broadcastCandidate.GetDspSlotId(),
		DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
		AdMonitorInfo: y.buildMonitor(bidData),
	}
	if bidData.AppInfo != nil {
		ad.AppInfo = &entity.AppInfo{
			PackageName: bidData.AppInfo.PackageName,
			AppName:     bidData.AppInfo.Name,
			AppVersion:  bidData.AppInfo.Version,
			Privacy:     bidData.AppInfo.PrivacyPolicyUrl,
			Permission:  bidData.AppInfo.PermissionsUrl,
			AppDescURL:  bidData.AppInfo.DescUrl,
			Icon:        bidData.AppInfo.LogoUrl,
			Develop:     bidData.AppInfo.DeveloperName,
			PackageSize: int(bidData.AppInfo.Size_),
		}
	}

	creative := y.buildCreative(bidData)
	if creative == nil {
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	adCandidate := ad_service.NewDspAdCandidateWithPool(ad)
	adCandidate.SetCreative(creative)
	adCandidate.SetBidType(entity.BidTypeCpm)
	adCandidate.SetBidPrice(uint32(bidData.Price))
	adCandidate.SetAdCandidateChargePriceEncoder(y.chargePriceEncoder)
	adCandidate.SetDspProtocol(y.GetDspProtocol())
	//adCandidate.SetDspAdID(bidData.Sposid)

	return adCandidate, nil
}

func (y *JlhzDspBroker) buildMonitor(bidData *jihz_proto.Material) *entity.AdMonitorInfo {
	monitor := &entity.AdMonitorInfo{}
	if bidData != nil {
		monitor.LandingUrl = bidData.GetClickUrl()
		//monitor.H5LandingUrl = bidData.
		monitor.DeepLinkUrl = bidData.GetDeepLinkUrl()
		monitor.LandingAction = mappingLandingAction(bidData.GetDownType(), bidData.GetPutType(), bidData.MiniAppId)
		monitor.DownloadUrl = bidData.DownloadUrl
	}

	if bidData.EventTracks != nil {

		for _, eventTracks := range bidData.EventTracks {
			switch eventTracks.EventId {
			case 1:
				for _, strUrl := range eventTracks.Urls {
					monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, strUrl)
				}
			case 2:
				for _, strUrl := range eventTracks.Urls {
					monitor.ClickMonitorList = append(monitor.ClickMonitorList, strUrl)
				}
			// 竞胜通知监测也放到曝光里
			case 6:
				for _, strUrl := range eventTracks.Urls {
					monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, strUrl)
				}
			case 302:
				for _, strUrl := range eventTracks.Urls {
					monitor.DeepLinkMonitorList = append(monitor.DeepLinkMonitorList, strUrl)
				}
			case 303:
				for _, strUrl := range eventTracks.Urls {
					monitor.DeepLinkFailedMonitorList = append(monitor.DeepLinkFailedMonitorList, strUrl)
				}
			case 209, 9:
				for _, strUrl := range eventTracks.Urls {
					monitor.AppDownloadStartedMonitorList = append(monitor.AppDownloadStartedMonitorList, strUrl)
				}
			case 202, 10:
				for _, strUrl := range eventTracks.Urls {
					monitor.AppDownloadFinishedMonitorList = append(monitor.AppDownloadFinishedMonitorList, strUrl)
				}
			case 210:
				for _, strUrl := range eventTracks.Urls {
					monitor.AppInstallStartMonitorList = append(monitor.AppInstallStartMonitorList, strUrl)
				}
			case 204:
				for _, strUrl := range eventTracks.Urls {
					monitor.AppInstalledFinishMonitorList = append(monitor.AppInstalledFinishMonitorList, strUrl)
				}
			case 101:
				for _, strUrl := range eventTracks.Urls {
					monitor.VideoStartUrlList = append(monitor.VideoStartUrlList, strUrl)
				}
			case 109, 105:
				for _, strUrl := range eventTracks.Urls {
					monitor.VideoCloseUrlList = append(monitor.VideoCloseUrlList, strUrl)
				}
			}
		}
	}

	// 宏替换
	if len(monitor.ImpressionMonitorList) > 0 {
		monitor.ImpressionMonitorList = y.macroInfo.MacroReplaceList(monitor.ImpressionMonitorList)
	}
	if len(monitor.ClickMonitorList) > 0 {
		monitor.ClickMonitorList = y.macroInfo.MacroReplaceList(monitor.ClickMonitorList)
	}
	if len(monitor.DeepLinkMonitorList) > 0 {
		monitor.DeepLinkMonitorList = y.macroInfo.MacroReplaceList(monitor.DeepLinkMonitorList)
	}

	return monitor
}

func (y *JlhzDspBroker) buildCreative(bid *jihz_proto.Material) *entity.Creative {
	if bid == nil {
		return nil
	}
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.GetAdId(),
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: bid.Title}
	if len(bid.Title) == 0 {
		title.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, title)
	desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: bid.Description}
	if len(bid.Description) == 0 {
		desc.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, desc)
	if len(bid.Icons) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.Icons[0],
			Height:       100,
			Width:        100,
		})
	}
	width := bid.Width
	height := bid.Height
	if bid.Video != nil {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          bid.Video.Url,
			Duration:     float64(bid.Video.Duration),
			Height:       height,
			Width:        width,
		})
	}

	for _, imgUrl := range bid.Images {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          imgUrl.Url,
			Height:       height,
			Width:        width,
		})
	}

	return creative
}

func (y *JlhzDspBroker) chargePriceEncoder(chargePrice uint32) string {
	return ""
	/*result, err := y.PriceManager.GetDspCoder(y.DspProtocol).EncodeWithKey(uint64(chargePrice), y.GetIKey(), y.GetEKey())
	if err != nil {
		y.log.WithError(err).WithField("price", chargePrice).Errorf("chargePriceEncoder error")
		return ""
	}

	return result*/
}

func (y *JlhzDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return y.dspSlotRegister
}

// 映射设备类型
func toDeviceType(deviceTyoe entity.DeviceType) jihz_proto.DeviceType {
	switch deviceTyoe {
	case entity.DeviceTypeMobile:
		return jihz_proto.DeviceType_MOBILE
	case entity.DeviceTypePad:
		return jihz_proto.DeviceType_TABLET
	case entity.DeviceTypeOtt:
		return jihz_proto.DeviceType_OTT
	default:
		return jihz_proto.DeviceType_OTHER
	}
}

// 映射设备操作系统类型
func toOsType(osType entity.OsType) jihz_proto.OsType {
	switch osType {
	case entity.OsTypeAndroid:
		return jihz_proto.OsType_ANDROID
	case entity.OsTypeIOS:
		return jihz_proto.OsType_IOS
	default:
		return jihz_proto.OsType_UNKNOWN_OS
	}
}

// 映射屏幕方向
func toScreenOrientation(soType entity.ScreenOrientationType) int32 {
	switch soType {
	case entity.ScreenOrientationTypePortrait:
		return 1
	case entity.ScreenOrientationTypeLandscape:
		return 2
	default:
		return 0
	}
}

// 映射设备运营商类型
func toCarrierType(carrierType entity.OperatorType) jihz_proto.CarrierType {
	switch carrierType {
	case entity.OperatorTypeChinaMobile:
		return jihz_proto.CarrierType_CHINA_MOBILE
	case entity.OperatorTypeChinaUnicom:
		return jihz_proto.CarrierType_CHINA_UNICOM
	case entity.OperatorTypeChinaTelecom:
		return jihz_proto.CarrierType_CHINA_TELECOM
	default:
		return jihz_proto.CarrierType_UNKNOWN_CARRIER
	}
}

// 映射设备网络类型
func toNetworkType(networkType entity.ConnectionType) jihz_proto.NetType {
	switch networkType {
	case entity.ConnectionTypeWifi:
		return jihz_proto.NetType_WIFI
	case entity.ConnectionType2G:
		return jihz_proto.NetType_CELLULAR_2G
	case entity.ConnectionType3G:
		return jihz_proto.NetType_CELLULAR_3G
	case entity.ConnectionType4G:
		return jihz_proto.NetType_CELLULAR_4G
	case entity.ConnectionType5G:
		return jihz_proto.NetType_CELLULAR_5G
	default:
		return jihz_proto.NetType_UNKNOWN
	}
}

// 广告位类型
func toSlotType(slotType entity.SlotType) jihz_proto.AdSlotType {
	switch slotType {
	case entity.SlotTypeFeeds:
		return jihz_proto.AdSlotType_INFO_FLOW
	case entity.SlotTypeOpening:
		return jihz_proto.AdSlotType_SPLASH
	case entity.SlotTypePopup:
		return jihz_proto.AdSlotType_INTERSTITIAL
	case entity.SlotTypeRewardVideo:
		return jihz_proto.AdSlotType_VIDEO
	default:
		return jihz_proto.AdSlotType_INFO_FLOW
	}
}

func toGender(genderType entity.UserGenderType) int32 {
	switch genderType {
	case entity.UserGenderMan:
		return 1
	case entity.UserGenderWoman:
		return 2
	default:
		return 0
	}
}

func mappingLandingAction(downType int32, putType int32, wxid string) entity.LandingType {
	if downType == 1 || downType == 2 {
		return entity.LandingTypeDownload
	} else if putType != 0 {
		return entity.LandingTypeDeepLink
	} else if len(wxid) != 0 {
		return entity.LandingTypeWeChatProgram
	} else {
		return entity.LandingTypeInWebView
	}
}
