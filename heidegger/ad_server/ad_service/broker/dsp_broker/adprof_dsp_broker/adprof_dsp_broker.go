package adprof_dsp_broker

import (
	"io"
	"net/http"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/adprof_dsp_broker/adprof_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	objectpool "gitlab.com/dev/heidegger/library/object_pool"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"gitlab.com/dev/heidegger/library/utils/video_utils"
)

var (
	bidRequestPool = objectpool.NewObjectPool(func() *adprof_proto.Request {
		return new(adprof_proto.Request)
	})
	bidRequestDevicePool = objectpool.NewObjectPool(func() *adprof_proto.Request_Device {
		return new(adprof_proto.Request_Device)
	})
	bidRequestGeoPool = objectpool.NewObjectPool(func() *adprof_proto.Request_Device_Geo {
		return new(adprof_proto.Request_Device_Geo)
	})
	bidRequestImpPool = objectpool.NewObjectPool(func() *adprof_proto.Request_Imp {
		return new(adprof_proto.Request_Imp)
	})
	bidRequestAppPool = objectpool.NewObjectPool(func() *adprof_proto.Request_App {
		return new(adprof_proto.Request_App)
	})
	bidRequestUserPool = objectpool.NewObjectPool(func() *adprof_proto.Request_User {
		return new(adprof_proto.Request_User)
	})
	bidResponsePool = objectpool.NewObjectPool(func() *adprof_proto.Response {
		return new(adprof_proto.Response)
	})
)

type AdprofDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *AdprofDspSlotRegister
	log          *zap.Logger
	macroInfo    macro_builder.MonitorMacroInfo
}

func NewAdprofDspBroker(dspId utils.ID) *AdprofDspBroker {
	return &AdprofDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{DspId: dspId},
		slotRegister:  NewAdprofDspSlotRegister(dspId),
		log:           zap.L().With(zap.String("broker", "AdprofDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__WINPRICE__",
			MacroClickDownX: "__DOWN_PX__",
			MacroClickDownY: "__DOWN_PY__",
			MacroClickUpX:   "__UP_PX__",
			MacroClickUpY:   "__UP_PY__",
			MacroHWSld:      "__SLD__",
		},
	}
}

func (impl *AdprofDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *AdprofDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		impl.log.Errorf("candidateList len: %d", len(candidateList))
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		impl.log.Errorf("dspSlot not found: %d", trafficData.GetDspSlotId())
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidFloor := candidate.GetBidFloor()

	bidReq := bidRequestPool.Get()
	defer bidRequestPool.Put(bidReq)

	bidReq.Id = request.GetRequestId()
	bidReq.Version = "2.4"
	bidReq.Tmax = 250

	bidReqApp := bidRequestAppPool.Get()
	defer bidRequestAppPool.Put(bidReqApp)
	bidReq.AppSite = &adprof_proto.Request_App_{App: bidReqApp}

	bidReqApp.Bundle = trafficData.GetAppBundle()
	bidReqApp.Name = trafficData.GetAppName()
	bidReqApp.Version = trafficData.GetAppVersion()
	bidReqApp.Appstoreversion = request.Device.AppStoreVersion

	bidReqDevice := impl.buildDevice(request, trafficData)
	defer bidRequestDevicePool.Put(bidReqDevice)
	bidReq.Device = bidReqDevice

	bidReqGeo := bidRequestGeoPool.Get()
	defer bidRequestGeoPool.Put(bidReqGeo)
	bidReq.Device.Geo = bidReqGeo

	bidReqGeo.Type = impl.mappingGeoType(request.Device.GeoStandard)
	bidReqGeo.Lat = trafficData.GetGeoLatitude()
	bidReqGeo.Lon = trafficData.GetGeoLongitude()

	bidReqImp := bidRequestImpPool.Get()
	defer bidRequestImpPool.Put(bidReqImp)
	bidReq.Imp = []*adprof_proto.Request_Imp{bidReqImp}

	bidReqImp.Id = request.ImpressionId
	if len(bidReqImp.Id) == 0 {
		bidReqImp.Id = request.GetRequestId()
	}
	bidReqImp.Tagid = slotId
	bidReqImp.Bidfloor = float64(bidFloor.Price)
	bidReqImp.Isdownload = true
	bidReqImp.Isdeeplink = true
	if trafficData.GetOsType() == entity.OsTypeIOS {
		bidReqImp.Isul = true
	}
	if request.UseHttps {
		bidReqImp.Secure = 1
	}
	impl.buildImp(request, trafficData, bidReq, bidReqImp, dspSlot)

	if len(dspSlot.PkgName) > 0 {
		bidReqApp.Bundle = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidReqApp.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidReqApp.Version = dspSlot.AppVersion
	}

	httpReq, _, err := impl.BuildPbHttpHttpRequest(bidReq)
	if err != nil {
		impl.log.WithError(err).Error("BuildPbHttpHttpRequest error")
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}
	httpReq.Header.Set("Content-Type", "application/octet-stream;charset=utf-8")
	impl.SampleDspBroadcastProtobufRequest(impl.GetDspId(), trafficData.GetDspSlotId(), candidate, bidReq)

	if request.IsDebug {
		payload, _ := sonic.Marshal(bidReq)
		impl.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}

	return httpReq, nil
}

func (impl *AdprofDspBroker) buildDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) *adprof_proto.Request_Device {
	bidReqDevice := bidRequestDevicePool.Get()

	bidReqDevice.Os = impl.mappingOsType(trafficData.GetOsType())
	bidReqDevice.Osv = trafficData.GetOsVersion()
	bidReqDevice.Did = trafficData.GetImei()
	bidReqDevice.Didmd5 = trafficData.GetMd5Imei()
	bidReqDevice.Oid = trafficData.GetOaid()
	bidReqDevice.Oidmd5 = trafficData.GetMd5Oaid()
	bidReqDevice.Ifa = trafficData.GetIdfa()
	bidReqDevice.Ifamd5 = trafficData.GetMd5Idfa()
	bidReqDevice.Caid = device_utils.GetCaidRaw(trafficData.GetCaid())
	bidReqDevice.CaidVersion = device_utils.GetCaidVersion(trafficData.GetCaid())
	bidReqDevice.Hwoaid = ""
	bidReqDevice.HwisTrackingEnabled = ""
	bidReqDevice.HwverCodeOfHms = request.Device.VercodeHms
	bidReqDevice.HwverCodeOfAG = request.Device.VercodeAg
	bidReqDevice.HwclientTime = ""
	bidReqDevice.PreCaid = ""
	bidReqDevice.PreCaidVersion = ""
	bidReqDevice.Aaid = trafficData.GetAaid()
	bidReqDevice.Androidid = trafficData.GetAndroidId()
	bidReqDevice.Androididmd5 = trafficData.GetMd5AndroidId()
	bidReqDevice.Ua = trafficData.GetUserAgent()
	bidReqDevice.Connectiontype = impl.mappingConnectionType(trafficData.GetConnectionType())
	bidReqDevice.Devicetype = impl.mappingDeviceType(trafficData.GetDeviceType())
	bidReqDevice.Make = trafficData.GetBrand()
	bidReqDevice.Model = trafficData.GetModel()
	bidReqDevice.Hwv = request.Device.HardwareMachineCode
	bidReqDevice.Carrier = impl.mappingCarrier(trafficData.GetOperatorType())
	bidReqDevice.Language = "zh-Hans-CN"
	bidReqDevice.Screenwidth = uint32(trafficData.GetScreenWidth())
	bidReqDevice.Screenheight = uint32(trafficData.GetScreenHeight())
	bidReqDevice.Orientation = impl.mappingOrientation(trafficData.GetScreenOrientation())
	bidReqDevice.Ppi = uint32(request.Device.PPI)
	bidReqDevice.Mac = trafficData.GetMac()
	bidReqDevice.Macidmd5 = trafficData.GetMd5Mac()
	bidReqDevice.Romversion = trafficData.GetRomVersion()
	bidReqDevice.Brand = trafficData.GetBrand()
	bidReqDevice.Openudid = trafficData.GetOpenUdid()
	bidReqDevice.BootMark = trafficData.GetBootMark()
	bidReqDevice.UpdateMark = trafficData.GetUpdateMark()
	// NOTE: must be app ids
	bidReqDevice.InstallApps = impl.getInstallAppIds(impl.GetDspId(), request.App.InstalledAppIds)
	bidReqDevice.StartupTime = trafficData.GetBootMark()
	bidReqDevice.UpdateTime = trafficData.GetUpdateMark()
	bidReqDevice.BirthTime = trafficData.GetDeviceInitTime()
	bidReqDevice.Paid = request.Device.Paid
	bidReqDevice.Country = "CN"
	bidReqDevice.LocalTzTime = "Asia/Shanghai"
	bidReqDevice.DeviceNameMd5 = request.Device.DeviceName
	bidReqDevice.DiskTotal = uint64(request.Device.SystemTotalDisk)
	bidReqDevice.MemTotal = uint64(request.Device.SystemTotalMem)

	if request.Device.IsIp6 {
		bidReqDevice.Ipv6 = trafficData.GetRequestIp()
	} else {
		bidReqDevice.Ip = trafficData.GetRequestIp()
	}

	if len(trafficData.GetWebviewUA()) > 0 {
		bidReqDevice.Ua = trafficData.GetWebviewUA()
	}

	if trafficData.GetOsType() == entity.OsTypeIOS {
		bidReqDevice.HardwareModel = trafficData.GetModel()
	}

	if len(bidReqDevice.DeviceNameMd5) == 0 {
		bidReqDevice.DeviceNameMd5 = trafficData.GetRomName()
		if len(bidReqDevice.DeviceNameMd5) == 0 {
			bidReqDevice.DeviceNameMd5 = trafficData.GetModel()
		}
	}

	if len(trafficData.GetDeviceStartupTime()) > 0 {
		bidReqDevice.StartupTime = trafficData.GetDeviceStartupTime()
	}

	if len(trafficData.GetDeviceUpgradeTime()) > 0 {
		bidReqDevice.UpdateTime = trafficData.GetDeviceUpgradeTime()
	}

	if len(bidReqDevice.DeviceNameMd5) > 0 {
		bidReqDevice.DeviceNameMd5 = md5_utils.GetMd5String(bidReqDevice.DeviceNameMd5)
	}

	if trafficData.GetOsType() == entity.OsTypeIOS &&
		len(bidReqDevice.Paid) == 0 &&
		len(trafficData.GetDeviceInitTime()) > 0 &&
		len(trafficData.GetDeviceUpgradeTime()) > 0 &&
		len(trafficData.GetDeviceStartupTime()) > 0 {
		bidReqDevice.Paid = string_utils.ConcatString(
			md5_utils.GetMd5String(trafficData.GetDeviceInitTime()),
			"-",
			md5_utils.GetMd5String(trafficData.GetDeviceUpgradeTime()),
			"-",
			md5_utils.GetMd5String(trafficData.GetDeviceStartupTime()))
	}

	return bidReqDevice
}

func (impl *AdprofDspBroker) buildImp(request *ad_service.AdRequest,
	trafficData ad_service_entity.TrafficData,
	bidReq *adprof_proto.Request,
	bidReqImp *adprof_proto.Request_Imp,
	dspSlot *AdprofSlotSlotInfo) {
	var w, h = uint32(dspSlot.Width), uint32(dspSlot.Height)
	if w == 0 || h == 0 {
		w = trafficData.GetSlotWidth()
		h = trafficData.GetSlotHeight()
	}
	if (w == 0 || h == 0) && len(request.SlotSize) > 0 {
		w = uint32(request.SlotSize[0].Width)
		h = uint32(request.SlotSize[0].Height)
	}

	switch trafficData.GetSlotType() {
	case entity.SlotTypeRewardVideo, entity.SlotTypeVideo, entity.SlotTypeVideoOpening:
		{
			videoObj := &adprof_proto.Request_Imp_Video{
				W:           w,
				H:           h,
				Type:        1,
				Minduration: uint32(request.VideoMinDuration),
				Maxduration: uint32(request.VideoMaxDuration),
				Mimes:       []string{"video/mp4"},
				Orientation: bidReq.Device.Orientation,
			}
			if trafficData.GetSlotType() == entity.SlotTypeRewardVideo {
				videoObj.Type = 2
			}
			bidReqImp.ImpObject = &adprof_proto.Request_Imp_Video_{Video: videoObj}
		}
	default:
		{
			imgObj := &adprof_proto.Request_Imp_Native{}
			var idx uint32 = 1
			for _, key := range request.GetCreativeTemplateKeyList() {
				creativeTemplateKey := creative_entity.CreativeTemplateKey(key)
				if creativeTemplateKey.Title().GetRequiredCount() > 0 {
					var isRequired uint32 = 1
					if creativeTemplateKey.Title().IsOptional() {
						isRequired = 0
					}
					imgObj.Assets = append(imgObj.Assets, &adprof_proto.Request_Imp_NativeAsset{
						Id:         idx,
						Isrequired: isRequired,
						NativeAss: &adprof_proto.Request_Imp_NativeAsset_Title{
							Title: &adprof_proto.Request_Imp_NativeAsset_NaTitle{
								Len: 90,
							},
						},
					})
					idx += 1
				}
				if creativeTemplateKey.Desc().GetRequiredCount() > 0 {
					var isRequired uint32 = 1
					if creativeTemplateKey.Desc().IsOptional() {
						isRequired = 0
					}
					imgObj.Assets = append(imgObj.Assets, &adprof_proto.Request_Imp_NativeAsset{
						Id:         idx,
						Isrequired: isRequired,
						NativeAss: &adprof_proto.Request_Imp_NativeAsset_Data{
							Data: &adprof_proto.Request_Imp_NativeAsset_NaData{
								Type: 1,
								Len:  90,
							},
						},
					})
					idx += 1
				}
				if creativeTemplateKey.Icon().GetRequiredCount() > 0 {
					var isRequired uint32 = 1
					if creativeTemplateKey.Icon().IsOptional() {
						isRequired = 0
					}
					imgObj.Assets = append(imgObj.Assets, &adprof_proto.Request_Imp_NativeAsset{
						Id:         idx,
						Isrequired: isRequired,
						NativeAss: &adprof_proto.Request_Imp_NativeAsset_Img{
							Img: &adprof_proto.Request_Imp_NativeAsset_NaImg{
								Type:  1,
								Wmin:  100,
								Hmin:  100,
								Mimes: []string{"image/jpeg", "image/png", "image/gif"},
							},
						},
					})
					idx += 1
				}
				if creativeTemplateKey.Image().GetRequiredCount() > 0 {
					var isRequired uint32 = 1
					if creativeTemplateKey.Image().IsOptional() {
						isRequired = 0
					}
					for range creativeTemplateKey.Image().GetRequiredCount() {
						imgObj.Assets = append(imgObj.Assets, &adprof_proto.Request_Imp_NativeAsset{
							Id:         idx,
							Isrequired: isRequired,
							NativeAss: &adprof_proto.Request_Imp_NativeAsset_Img{
								Img: &adprof_proto.Request_Imp_NativeAsset_NaImg{
									Type:  3,
									Wmin:  w,
									Hmin:  h,
									Mimes: []string{"image/jpeg", "image/png"},
								},
							},
						})
						idx += 1
					}
				}
				if creativeTemplateKey.Video().GetRequiredCount() > 0 {
					var isRequired uint32 = 1
					if creativeTemplateKey.Video().IsOptional() {
						isRequired = 0
					}
					imgObj.Assets = append(imgObj.Assets, &adprof_proto.Request_Imp_NativeAsset{
						Id:         idx,
						Isrequired: isRequired,
						NativeAss: &adprof_proto.Request_Imp_NativeAsset_Video{
							Video: &adprof_proto.Request_Imp_NativeAsset_NaVideo{
								Wmin:  w,
								Hmin:  h,
								Mimes: []string{"video/mp4"},
							},
						},
					})
					idx += 1
					if trafficData.GetSlotType() == entity.SlotTypeFeeds {
						imgObj.Layout = 503
					}
				}
				break // only choose first creative template
			}
			switch trafficData.GetSlotType() {
			case entity.SlotTypeOpening, entity.SlotTypePopup:
				imgObj.Layout = 501
			case entity.SlotTypeBanner:
				imgObj.Layout = 502
			default:
				if imgObj.Layout == 0 {
					imgObj.Layout = 6
				}
			}
			bidReqImp.ImpObject = &adprof_proto.Request_Imp_Native_{Native: imgObj}
		}
	}
}

func (impl *AdprofDspBroker) getInstallAppIds(dspId utils.ID, appIds []int) []string {
	result := make([]string, 0)
	if len(appIds) == 0 {
		return result
	}

	dspIdStr := dspId.String()
	for _, appid := range appIds {
		key := dspIdStr + "_" + type_convert.GetAssertString(appid)
		externalMapping := impl.ExternalMappingLoader.GetDspAppMappingMapByKey(key)
		if externalMapping != nil {
			result = append(result, externalMapping.SourceValue)
		}
	}

	return result
}

func (impl *AdprofDspBroker) mappingGeoType(geoStandard int) uint32 {
	switch geoStandard {
	case 0, 3:
		return 3
	case 1:
		return 2
	case 2:
		return 1
	default:
		return 0
	}
}

func (impl *AdprofDspBroker) mappingCarrier(carrier entity.OperatorType) string {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return "中国移动"
	case entity.OperatorTypeChinaTelecom:
		return "电信"
	case entity.OperatorTypeChinaUnicom, entity.OperatorTypeTietong:
		return "联通"
	default:
		return ""
	}
}

func (impl *AdprofDspBroker) mappingDeviceType(s entity.DeviceType) adprof_proto.Request_Device_DeviceType {
	switch s {
	case entity.DeviceTypeMobile:
		return adprof_proto.Request_Device_Mobile
	case entity.DeviceTypePad:
		return adprof_proto.Request_Device_PC
	case entity.DeviceTypeOtt:
		return adprof_proto.Request_Device_TV
	default:
		return adprof_proto.Request_Device_Other
	}
}

func (impl *AdprofDspBroker) mappingOsType(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "ios"
	case entity.OsTypeAndroid:
		return "android"
	default:
		return ""
	}
}

func (impl *AdprofDspBroker) mappingConnectionType(connectionType entity.ConnectionType) adprof_proto.Request_Device_ConnectionType {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return adprof_proto.Request_Device_Ethernet
	case entity.ConnectionTypeWifi:
		return adprof_proto.Request_Device_WiFi
	case entity.ConnectionTypeCellular:
		return adprof_proto.Request_Device_CellularNetworkUnknown
	case entity.ConnectionType2G:
		return adprof_proto.Request_Device_G2
	case entity.ConnectionType3G:
		return adprof_proto.Request_Device_G3
	case entity.ConnectionType4G:
		return adprof_proto.Request_Device_G4
	case entity.ConnectionType5G:
		return adprof_proto.Request_Device_G5
	default:
		return adprof_proto.Request_Device_Unknown
	}
}

func (impl *AdprofDspBroker) mappingOrientation(ori entity.ScreenOrientationType) uint32 {
	switch ori {
	case entity.ScreenOrientationTypeLandscape:
		return 1
	case entity.ScreenOrientationTypePortrait:
		return 2
	default:
		return 0
	}
}

func (impl *AdprofDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if resp.StatusCode != 200 {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			type_convert.GetAssertString(resp.StatusCode),
			type_convert.GetAssertString(resp.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			"1", "读取body失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	bidResp := bidResponsePool.Get()
	defer bidResponsePool.Put(bidResp)
	if err := impl.ParsePbHttpHttpResponse(resp, data, bidResp); err != nil {
		impl.log.WithError(err).Debug("ParseResponse error")
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			"2", "body反序列化失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	impl.SampleDspBroadcastProtobufResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, bidResp)
	if request.IsDebug {
		payload, _ := sonic.Marshal(bidResp)
		impl.log.WithField("response", string(payload)).Info("ParseResponse debug")
	}
	impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
		broadcastCandidate.GetDspSlotId().String(),
		type_convert.GetAssertString(resp.Status),
		bidResp.Nbr)

	if bidResp.Seatbid == nil || len(bidResp.Seatbid.GetBids()) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, bid := range bidResp.Seatbid.GetBids() {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = &entity.AppInfo{}
		if bid.App != nil {
			app := bid.App
			candidateAd.AppInfo.AppName = app.Name
			candidateAd.AppInfo.AppVersion = app.Version
			candidateAd.AppInfo.PackageName = app.Package
			candidateAd.AppInfo.PackageSize = int(app.Size_)
			candidateAd.AppInfo.Develop = app.Publisher
			candidateAd.AppInfo.Privacy = app.PrivacyLink
			candidateAd.AppInfo.Permission = app.PermissionLink
			candidateAd.AppInfo.AppDesc = app.AppDesc
		}

		if bid.Wxappid != "" && bid.Wxpath != "" {
			candidateAd.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   bid.Wxappid,
				ProgramPath: bid.Wxpath,
			}
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, bid)

		creative := impl.ParseCreativeData(bid)
		if creative == nil {
			return nil, err_code.ErrCreativeNotFound
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(bid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(creative)
		candidate.SetDspAdID(bid.Adid)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break
	}

	return result, nil
}

func (impl *AdprofDspBroker) ParseCreativeData(bid *adprof_proto.Response_SeatBid_Bid) *entity.Creative {
	creative := &entity.Creative{
		CreativeKey: bid.Crid,
	}

	if bid.Admobject == nil || (bid.Admobject.Video == nil && bid.Admobject.Native == nil) {
		return nil
	}

	hasTitle, hasDesc := false, false
	if bid.Admobject.Video != nil {
		video := bid.Admobject.Video
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          video.Url,
			Width:        int32(video.W),
			Height:       int32(video.H),
			FileSize:     int32(video.Size_),
			Duration:     float64(video.Duration),
		}
		creative.MaterialList = append(creative.MaterialList, material)
		if len(video.Cover) > 0 {
			materialImg := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          video.Cover,
				Width:        int32(video.W),
				Height:       int32(video.H),
			}
			creative.MaterialList = append(creative.MaterialList, materialImg)
		} else {
			cover := video_utils.GenerateVideoCoverFromCDN(video.Url)
			if len(cover) > 0 {
				materialImg := &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          cover,
					Width:        int32(video.W),
					Height:       int32(video.H),
				}
				creative.MaterialList = append(creative.MaterialList, materialImg)
			}
		}
		// TODO: video.Card
	}

	if bid.Admobject.Native != nil {
		for _, asset := range bid.Admobject.Native.Assets {
			if asset.GetTitle() != nil {
				title := asset.GetTitle().Text
				if len(title) > 0 {
					material := &entity.Material{
						MaterialType: entity.MaterialTypeTitle,
						Data:         title,
					}
					creative.MaterialList = append(creative.MaterialList, material)
					hasTitle = true
				}
			} else if asset.GetData() != nil {
				desc := asset.GetData().Value
				if len(desc) > 0 {
					material := &entity.Material{
						MaterialType: entity.MaterialTypeDesc,
						Data:         desc,
					}
					creative.MaterialList = append(creative.MaterialList, material)
					hasDesc = true
				}
			} else if asset.GetImg() != nil {
				_ = impl.parseAdmImage(creative, asset.GetImg())
			} else if asset.GetVideo() != nil {
				_ = impl.parseAdmVideo(creative, asset.GetVideo())
			}
		}
	}

	if !hasTitle {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}
	if !hasDesc {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}

func (impl *AdprofDspBroker) parseAdmImage(creative *entity.Creative, img *adprof_proto.Response_SeatBid_Bid_AdmObject_Native_Asset_Img) error {
	switch img.GetType() {
	case 1, 2:
		{
			material := &entity.Material{
				MaterialType: entity.MaterialTypeIcon,
				Url:          img.GetUrl(),
				Width:        int32(img.GetW()),
				Height:       int32(img.GetH()),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	default:
		{
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img.GetUrl(),
				Width:        int32(img.GetW()),
				Height:       int32(img.GetH()),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	return nil
}

func (impl *AdprofDspBroker) parseAdmVideo(creative *entity.Creative, video *adprof_proto.Response_SeatBid_Bid_AdmObject_Native_Asset_Video) error {
	material := &entity.Material{
		MaterialType: entity.MaterialTypeVideo,
		Url:          video.Url,
		Duration:     float64(video.Duration),
	}
	creative.MaterialList = append(creative.MaterialList, material)

	if len(video.Cover) > 0 {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          video.Cover,
		}
		creative.MaterialList = append(creative.MaterialList, materialImg)
	}

	return nil
}

func (impl *AdprofDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.PlaintextEncode(uint64(chargePrice))
	//result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *AdprofDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *adprof_proto.Response_SeatBid_Bid) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      make([]string, 0),
		LandingAction:         entity.LandingTypeInWebView,
		LandingUrl:            bid.Target,
	}

	switch bid.Action {
	case "2":
		tracking.LandingAction = entity.LandingTypeDownload
		tracking.DownloadUrl = bid.DownloadUrl
	default:
		if len(bid.Deeplink) > 0 || len(bid.UniversalLink) > 0 {
			tracking.LandingAction = entity.LandingTypeDeepLink
			tracking.DeepLinkUrl = bid.Deeplink
			if len(bid.UniversalLink) > 0 {
				tracking.DeepLinkUrl = bid.UniversalLink
			}
		} else if len(bid.Wxappid) > 0 && len(bid.Wxpath) > 0 {
			tracking.LandingAction = entity.LandingTypeWeChatProgram
		}
	}

	if len(bid.Nurl) > 0 {
		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.Nurl)
	}

	if bid.Events != nil {
		evt := bid.Events
		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, evt.ImpUrls...)
		tracking.ClickMonitorList = append(tracking.ClickMonitorList, evt.ClickUrls...)
		tracking.AppDownloadStartedMonitorList = evt.StartDodUrls
		tracking.AppDownloadFinishedMonitorList = evt.FinishDodUrls
		tracking.AppInstallStartMonitorList = evt.StartInstallUrls
		tracking.AppInstalledFinishMonitorList = evt.FinishInstallUrls
		tracking.AppOpenMonitorList = evt.ActiveUrls
		tracking.VideoStartUrlList = evt.StartPlayUrls
		tracking.VideoCloseUrlList = evt.FinishPlayUrls
		tracking.DeepLinkMonitorList = evt.DeeplinkUrls
		tracking.DeepLinkFailedMonitorList = evt.DeeplinkFurls
	}

	tracking.LandingUrl = macro_builder.MacroReplace(tracking.LandingUrl, impl.macroInfo)
	tracking.ImpressionMonitorList = macro_builder.MacroReplaceList(tracking.ImpressionMonitorList, impl.macroInfo)
	tracking.ClickMonitorList = macro_builder.MacroReplaceList(tracking.ClickMonitorList, impl.macroInfo)
	tracking.DeepLinkMonitorList = macro_builder.MacroReplaceList(tracking.DeepLinkMonitorList, impl.macroInfo)
	tracking.DeepLinkFailedMonitorList = macro_builder.MacroReplaceList(tracking.DeepLinkFailedMonitorList, impl.macroInfo)

	return tracking
}
