package ruixunda_dsp_broker

import (
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/ruixunda_dsp_broker/ruixunda_dsp_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/hash_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"
	"fmt"
)

type RuiXunDaDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *RuiXunDaSlotRegister
	log          *zap.Logger
	macroInfo    macro_builder.MonitorMacroInfo
}

func NewRuiXunDaDspBroker(dspId utils.ID) *RuiXunDaDspBroker {
	return &RuiXunDaDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		slotRegister: NewRuiXunDaSlotRegister(dspId),
		log:          zap.L().With(zap.String("broker", "RuiXunDaDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__WIN_PRICE__",
			MacroClickDownX: "__DOWN_X__",
			MacroClickDownY: "__DOWN_Y__",
			MacroClickUpX:   "__UP_X__",
			MacroClickUpY:   "__UP_Y__",
			MacroHWSld:      "__SLD__",
		},
	}
}

func (a *RuiXunDaDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		a.log.WithField("candidates", len(candidateList)).Error("too many candidates")
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := a.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	//dspSlotId
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	imp := &ruixunda_dsp_proto.Imp{
		Id:       1,
		Width:    int32(trafficData.GetSlotWidth()),
		Height:   int32(trafficData.GetSlotHeight()),
		BidFloor: float64(candidate.GetBidFloor().Price),
		User:     &ruixunda_dsp_proto.User{},
		Installs: request.App.InstalledApp,
		Video: &ruixunda_dsp_proto.Video{
			MinDuration: request.VideoMinDuration,
			MaxDuration: request.VideoMaxDuration,
		},
	}
	if request.VideoMinDuration > 0 && trafficData.GetSlotType() == entity.SlotTypeRewardVideo {
		imp.Video.VideoType = 2
	} else if request.VideoMinDuration > 0 && trafficData.GetSlotType() == entity.SlotTypeFeeds {
		imp.Video.VideoType = 1
	}

	switch request.UserGender {
	case entity.UserGenderMan:
		imp.User.Gender = "1"
	case entity.UserGenderWoman:
		imp.User.Gender = "2"
	}

	sid, err := strconv.ParseInt(slotId, 10, 32)
	if err != nil {
		return nil, err
	}
	imp.TagId = int32(sid)

	if dspSlot.Width > 0 && dspSlot.Height > 0 {
		imp.Width = int32(dspSlot.Width)
		imp.Height = int32(dspSlot.Height)
	}

	var apid uint64
	if trafficData.GetAppBundle() != "" {
		apid = hash_utils.XXHash(trafficData.GetAppBundle())
	}

	bidRequest := &ruixunda_dsp_proto.AdRequest{
		RequestId: request.GetRequestId(),
		Imps:      []*ruixunda_dsp_proto.Imp{imp},
		App: &ruixunda_dsp_proto.App{
			Id:      type_convert.GetAssertString(apid),
			AppName: trafficData.GetAppName(),
			PkgName: trafficData.GetAppBundle(),
			Version: trafficData.GetAppVersion(),
		},
		Device: &ruixunda_dsp_proto.Device{
			Network:      mappingConnectionType(trafficData.GetConnectionType()),
			DeviceType:   mappingDeviceType(trafficData.GetDeviceType()),
			Os:           mappingDeviceOs(trafficData.GetOsType()),
			OsVersion:    trafficData.GetOsVersion(),
			Imei:         trafficData.GetImei(),
			ImeiMd5:      trafficData.GetMd5Imei(),
			Oaid:         trafficData.GetOaid(),
			OaidMd5:      trafficData.GetMd5Oaid(),
			AndroidId:    trafficData.GetAndroidId(),
			AndroidIdMd5: trafficData.GetMd5AndroidId(),
			Idfa:         trafficData.GetIdfa(),
			IdfaMd5:      trafficData.GetMd5Idfa(),
			Idfv:         trafficData.GetIdfv(),
			Paid:         request.Device.Paid,
			Dpi:          request.Device.DPI,
			Brand:        trafficData.GetBrand(),
			Model:        trafficData.GetModel(),
			Carrier:      mappingCarrier(trafficData.GetOperatorType()),
			Mac:          trafficData.GetMac(),
			MacMd5:       trafficData.GetMd5Mac(),
			Ip:           trafficData.GetRequestIp(),
			UserAgent:    trafficData.GetUserAgent(),
			ScreenHeight: trafficData.GetScreenHeight(),
			ScreenWidth:  trafficData.GetScreenWidth(),
			Orientation:  mappingOrientation(trafficData.GetScreenOrientation()),
			BootMark:     trafficData.GetBootMark(),
			UpdateMark:   trafficData.GetUpdateMark(),
			Geo: &ruixunda_dsp_proto.Geo{
				Latitude:  trafficData.GetGeoLatitude(),
				Longitude: trafficData.GetGeoLongitude(),
			},
			Ppi:     request.Device.PPI,
			Density: float64(trafficData.GetScreenDensity()),
			Imsi:    request.Device.Imsi,
			HmsCore: request.Device.VercodeHms,
			Sst:     trafficData.GetDeviceStartupTime(),
			Sut:     trafficData.GetDeviceUpgradeTime(),
			Caids:   make([]*ruixunda_dsp_proto.Caid, 0),
		},
		Version: "1.1",
	}

	if trafficData.GetOsType() == entity.OsTypeIOS && request.Device.HardwareMachineCode != "" {
		request.Device.Model = request.Device.HardwareMachineCode
	}

	if len(trafficData.GetCaid()) != 0 {
		bidRequest.Device.Caids = append(bidRequest.Device.Caids, &ruixunda_dsp_proto.Caid{
			Caid:    device_utils.GetCaidRaw(trafficData.GetCaid()),
			Version: device_utils.GetCaidVersion(trafficData.GetCaid()),
		})
	}

	if len(request.Device.Caids) > 0 {
		for _, caidItem := range request.Device.Caids {
			if caidItem == trafficData.GetCaid() {
				continue
			}
			bidRequest.Device.Caids = append(bidRequest.Device.Caids, &ruixunda_dsp_proto.Caid{
				Caid:    device_utils.GetCaidRaw(caidItem),
				Version: device_utils.GetCaidVersion(caidItem),
			})
		}
	}

	if request.Device.IsIp6 {
		bidRequest.Device.Ipv6 = trafficData.GetRequestIp()
		bidRequest.Device.Ip = ""
	}

	if len(trafficData.GetWebviewUA()) > 0 {
		bidRequest.Device.UserAgent = trafficData.GetWebviewUA()
	}
	if len(dspSlot.APPID) > 0 {
		bidRequest.App.Id = dspSlot.APPID
	}
	if len(dspSlot.PkgName) > 0 {
		bidRequest.App.PkgName = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidRequest.App.AppName = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidRequest.App.Version = dspSlot.AppVersion
	}

	if request.IsDebug {
		payload, _ := sonic.Marshal(bidRequest)
		a.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}

	httpReq, _, err := a.BuildPbHttpHttpRequest(bidRequest)
	if err != nil {
		a.log.WithError(err).Error("BuildPbHttpHttpRequest error")
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}
	httpReq.Header.Set("Content-Type", "application/x-protobuf;charset=utf-8")
	a.SampleDspBroadcastProtobufRequest(a.DspId, trafficData.GetDspSlotId(), candidate, bidRequest)
	return httpReq, nil
}

func mappingDeviceOs(osType entity.OsType) string {
	switch osType {
	case entity.OsTypeIOS:
		return "ios"
	case entity.OsTypeAndroid:
		return "android"
	default:
		return "android"
	}
}

func mappingCarrier(carrier entity.OperatorType) int32 {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 2
	case entity.OperatorTypeChinaTelecom:
		return 3
	default:
		return 0
	}
}
func mappingOrientation(orientation entity.ScreenOrientationType) int32 {
	switch orientation {
	case entity.ScreenOrientationTypePortrait:
		return 1
	case entity.ScreenOrientationTypeLandscape:
		return 2
	default:
		return 0
	}
}
func mappingConnectionType(connectionType entity.ConnectionType) int32 {
	switch connectionType {
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 4
	}
}

func mappingDeviceType(dy entity.DeviceType) int32 {
	switch dy {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypePc:
		return 3
	case entity.DeviceTypeOtt:
		return 4
	default:
		return 1
	}
}

func (a *RuiXunDaDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]
	if response.StatusCode != 200 {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(response.StatusCode), type_convert.GetAssertString(response.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse
	}

	resp := &ruixunda_dsp_proto.AdResponse{}
	err = a.ParsePbHttpHttpResponse(response, data, resp)
	if err != nil {
		a.log.WithError(err).WithField("response", response).Error("ParseSonicJsonHttpResponse error")
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		return nil, err_code.ErrBrokerParseError.Wrap(err)
	}

	a.SampleDspBroadcastProtobufResponse(a.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, resp)
	if request.IsDebug {
		resBody, _ := sonic.Marshal(resp)
		zap.L().Info("RuiXunDaDspBroker.ParseResponse, body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}
	//a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "0", resp.Msg)

	if len(resp.Ads) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0, 1)
	for _, bid := range resp.Ads {
		ad := &entity.Ad{
			DspId:      a.DspId,
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
			AppInfo:    &entity.AppInfo{},
		}
		if bid.AppInfo != nil {
			ad.AppInfo.PackageName = bid.AppInfo.PkgName
			ad.AppInfo.AppName = bid.AppInfo.Name
			ad.AppInfo.AppVersion = bid.AppInfo.Version
			ad.AppInfo.Develop = bid.AppInfo.Developer
			ad.AppInfo.AppDesc = bid.AppInfo.Desc
			ad.AppInfo.Privacy = bid.AppInfo.Privacy
			ad.AppInfo.Permission = bid.AppInfo.Permission
			ad.AppInfo.PackageSize = int(bid.AppInfo.Size_)
		}

		adMonitorInfo, creative := a.parseCallbacksAndCreative(bid)

		ad.AdMonitorInfo = adMonitorInfo
		if creative == nil {
			return nil, err_code.ErrCreativeNotFound
		}

		candidate := ad_service.NewDspAdCandidateWithPool(ad)
		candidate.SetAdCandidateChargePriceEncoder(a.chargePriceEncoder)
		candidate.SetBidPrice(uint32(bid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(creative)
		candidate.SetDspAdID(bid.CreativeId)
		candidate.SetDspProtocol(a.DspProtocol)
		result = append(result, candidate)
	}

	return result, nil
}

func (a *RuiXunDaDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := a.PriceManager.GetDspCoder(a.DspProtocol).EncodeWithKey(uint64(chargePrice), a.GetIKey(), a.GetEKey())
	if err != nil {
		a.log.WithError(err).WithField("price", chargePrice).Errorf("chargePriceEncoder error")
		return ""
	}
	return result
}

func (a *RuiXunDaDspBroker) parseCallbacksAndCreative(data *ruixunda_dsp_proto.Ads) (*entity.AdMonitorInfo, *entity.Creative) {
	info := &entity.AdMonitorInfo{
		LandingUrl: data.Link,
	}
	if data.Download == 1 {
		info.LandingAction = entity.LandingTypeDownload
	} else {
		info.LandingAction = entity.LandingTypeInWebView
	}

	if len(data.NoticeUrl) > 0 {
		info.ImpressionMonitorList = append(info.ImpressionMonitorList, data.NoticeUrl)
	}

	for _, expurl := range data.ImpTrackUrls {
		timenow := time.Now()
		expurl = strings.Replace(expurl, "__UTC_TS__", type_convert.GetAssertString(timenow.Unix()), -1)
		info.ImpressionMonitorList = append(info.ImpressionMonitorList, strings.Replace(expurl, "__UTC_TMS__", type_convert.GetAssertString(timenow.UnixMilli()), -1))
	}

	for _, clkurl := range data.ClickTrackUrls {
		timenow := time.Now()
		clkurl = strings.Replace(clkurl, "__UTC_TS__", type_convert.GetAssertString(timenow.Unix()), -1)
		info.ClickMonitorList = append(info.ClickMonitorList, strings.Replace(clkurl, "__UTC_TMS__", type_convert.GetAssertString(timenow.UnixMilli()), -1))
	}

	//assets
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        data.CreativeId,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}
	if len(data.Images) > 0 {
		for _, img := range data.Images {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img.Url,
				Height:       img.Height,
				Width:        img.Width,
			})
		}
	}
	if len(data.AdIcon) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          data.AdIcon,
		})
	}

	video25 := 0
	video50 := 0
	video75 := 0
	video100 := 0
	vid := data.Video
	if vid != nil {
		video100 = int(vid.Duration / 1000)

		video25 = video100 / 4
		video50 = video100 / 2
		video75 = video100 * 75 / 100

		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          vid.VideoUrl,
			Height:       vid.Height,
			Width:        vid.Width,
			Duration:     float64(video100),
			FileSize:     vid.Length,
		})

		if vid.CoverUrl != "" {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeCoverImage,
				Url:          vid.CoverUrl,
			})
		}
	}

	title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: data.Title}
	if len(data.Title) == 0 {
		title.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, title)

	desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: data.Desc}
	if len(data.Desc) == 0 {
		desc.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, desc)

	appinfo := data.AppInfo
	if appinfo != nil {
		if appinfo.DeepLink != "" {
			info.DeepLinkUrl = appinfo.DeepLink
			info.LandingAction = entity.LandingTypeDeepLink
		}

		if len(appinfo.DsUrls) > 0 {
			info.AppDownloadStartedMonitorList = append(info.AppDownloadStartedMonitorList, appinfo.DsUrls...)
		}
		if len(appinfo.DfUrls) > 0 {
			info.AppDownloadFinishedMonitorList = append(info.AppDownloadFinishedMonitorList, appinfo.DfUrls...)
		}
		if len(appinfo.SsUrls) > 0 {
			info.AppInstallStartMonitorList = append(info.AppInstallStartMonitorList, appinfo.SsUrls...)
		}
		if len(appinfo.SfUrls) > 0 {
			info.AppInstalledFinishMonitorList = append(info.AppInstalledFinishMonitorList, appinfo.SfUrls...)
		}

		if len(appinfo.EventTracks) > 0 {
			for _, event := range appinfo.EventTracks {
				switch event.EventType {
				case 1:
					info.VideoStartUrlList = append(info.VideoStartUrlList, event.EventTrackUrls...)

				case 2:
					for _, item := range event.EventTrackUrls {
						info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
							Url:   item,
							Delay: video25,
						})
					}

				case 3:
					for _, item := range event.EventTrackUrls {
						info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
							Url:   item,
							Delay: video50,
						})
					}

				case 4:
					for _, item := range event.EventTrackUrls {
						info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
							Url:   item,
							Delay: video75,
						})
					}
					//播放完成，播放完成关闭都可以放在5，中途关闭放在视频close
				case 5:
					for _, item := range event.EventTrackUrls {
						info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
							Url:   item,
							Delay: video100,
						})
					}

				case 13:
					info.DeepLinkFailedMonitorList = append(info.DeepLinkFailedMonitorList, event.EventTrackUrls...)

				case 14:
					info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, event.EventTrackUrls...)

				}
			}
		}
	}

	//宏替换
	info.ImpressionMonitorList = macro_builder.MacroReplaceList(info.ImpressionMonitorList, a.macroInfo)
	info.ClickMonitorList = macro_builder.MacroReplaceList(info.ClickMonitorList, a.macroInfo)

	return info, creative
}

func (a *RuiXunDaDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return a.slotRegister
}
