package ruixunda_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"fmt"
)

type RuiXunDaSlotInfo struct {
	*entity.DspSlotInfo
	APPID      string `json:"appid"`
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`
}

func (info *RuiXunDaSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo
	info.APPID, _ = dspSlotInfo.ExtraData.GetString("appid")
	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	info.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	info.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")
	return nil
}

type RuiXunDaSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*RuiXunDaSlotInfo
}

func NewRuiXunDaSlotRegister(dspId utils.ID) *RuiXunDaSlotRegister {
	return &RuiXunDaSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*RuiXunDaSlotInfo),
	}
}

func (r *RuiXunDaSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *RuiXunDaSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*RuiXunDaSlotInfo)
	for _, slot := range list {
		dspSlot := &RuiXunDaSlotInfo{}
		if err := dspSlot.Init(slot); err != nil {
			zap.L().Error("[RuiXunDaSlotRegister] init slot failed", zap.Error(err), zap.String("slot", fmt.Sprintf("%v", slot.Id)))
			continue
		}

		slotMap[slot.Id] = dspSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *RuiXunDaSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *RuiXunDaSlotRegister) GetSlotInfo(slotId utils.ID) *RuiXunDaSlotInfo {
	return r.dspSlotMap[slotId]
}
