package keji_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type KeJiSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height int `json:"height"`
	Width  int `json:"width"`
}

func (info *KeJiSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	return nil
}

type KeJiDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*KeJiSlotSlotInfo
}

func NewPddDspSlotRegister(dspId utils.ID) *KeJiDspSlotRegister {
	return &KeJiDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*KeJiSlotSlotInfo),
	}
}

func (r *KeJiDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *KeJiDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*KeJiSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &KeJiSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[KeJiDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *KeJiDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *KeJiDspSlotRegister) GetSlotInfo(slotId utils.ID) *KeJiSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
