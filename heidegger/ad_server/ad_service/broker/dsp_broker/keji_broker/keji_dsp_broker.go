package keji_broker

import (
	"bytes"
	"github.com/mailru/easyjson"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/keji_broker/keji_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"io"
	"net/http"
	"strings"
	"fmt"
)

type KeJiDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *KeJiDspSlotRegister
	bidUrl       string
	dspId        utils.ID

	dspProtocol   string
	iKey          string
	eKey          string
	MacroWinPrice string
}

func NewKeJiDspBroker(dspId utils.ID) *KeJiDspBroker {
	return &KeJiDspBroker{
		slotRegister:  NewPddDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "__AUCTION_PRICE__",
	}
}

func (impl *KeJiDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *KeJiDspBroker) GetDspId() utils.ID {
	return impl.dspId
}

func (impl *KeJiDspBroker) UpdateDspInfo(dsp *entity.Dsp) error {
	impl.bidUrl = dsp.BidUrl
	impl.iKey = dsp.Ikey
	impl.eKey = dsp.Ekey
	impl.dspProtocol = dsp.Protocol
	return nil
}

func (impl *KeJiDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("KeJiDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	keJiJsonRequest := &keji_broker_entity.KeJiRequest{
		Id:           trafficData.GetRequestId(),
		ApiVersion:   "1.0",
		SupportHttps: 1,
		App:          impl.encodeApp(trafficData),
		Device:       impl.encodeDevice(request, trafficData),
		Test:         0,
	}

	imp, err := impl.encodeImp(request, trafficData, candidate)
	if err != nil {
		return nil, err_code.ErrInvalidImpression

	}

	keJiJsonRequest.Imp = imp

	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()
	_, err = easyjson.MarshalToWriter(keJiJsonRequest, buffer)
	if err != nil {
		zap.L().Error("KeJiDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err_code.ErrBroadcastRequestBuildFail
	}

	body := buffer.Get()

	if request.IsDebug {
		zap.L().Info("KeJiDspBroker.EncodeRequest end, kejiRequest", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(body))))))
	}

	req, err := http.NewRequest(http.MethodPost, impl.bidUrl, bytes.NewBuffer(body))
	if err != nil {
		zap.L().Error("KeJiDspBroker http NewRequest err", zap.Error(err))
		return nil, err
	}
	req.Header["Content-Type"] = []string{"application/json"}

	impl.SampleDspBroadcastRequest(impl.dspId, dspSlot.Id, candidate, body)

	return req, nil

}

func (impl *KeJiDspBroker) encodeImp(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData, candidate *ad_service.AdCandidate) ([]*keji_broker_entity.KeJiImp, error) {
	imps := make([]*keji_broker_entity.KeJiImp, 0)

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return imps, errors.New("empty slot")
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, errors.New("empty slot")
	}

	bidFloor := candidate.GetBidFloor()

	imp := &keji_broker_entity.KeJiImp{
		Id:       request.ImpressionId,
		Tagid:    slotId,
		Bidfloor: int(bidFloor.Price),
		W:        dspSlot.Width,
		H:        dspSlot.Height,
	}

	if imp.W == 0 || imp.H == 0 {
		if len(request.SlotSize) > 0 {
			imp.W = int(request.SlotSize[0].Width)
			imp.H = int(request.SlotSize[0].Height)
		}
	}
	imps = append(imps, imp)

	return imps, nil
}

func (impl *KeJiDspBroker) encodeApp(trafficData ad_service_entity.TrafficData) *keji_broker_entity.KeJiApp {
	app := &keji_broker_entity.KeJiApp{}
	//app.Id = bidRequest.Mobile.
	app.Name = trafficData.GetAppName()
	app.Bundle = trafficData.GetAppBundle()
	app.Ver = trafficData.GetAppVersion()
	return app
}

func (impl *KeJiDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) *keji_broker_entity.KeJiDevice {
	device := &keji_broker_entity.KeJiDevice{}
	device.Ua = trafficData.GetUserAgent()
	device.Ip = trafficData.GetRequestIp()
	device.Devicetype = impl.mappingDeviceType(trafficData.GetDeviceType())

	device.Make = trafficData.GetBrand()
	device.Model = trafficData.GetModel()
	device.Os = impl.mappingOs(trafficData.GetOsType())
	device.Osv = trafficData.GetOsVersion()
	device.W = int(trafficData.GetScreenWidth())
	device.H = int(trafficData.GetScreenHeight())
	device.Connectiontype = impl.mappingConnectionType(trafficData.GetConnectionType())
	device.Mccmnc = impl.mappingCarrier(trafficData.GetOperatorType())
	device.Imei = trafficData.GetImei()
	device.ImeiMd5 = trafficData.GetMd5Imei()
	device.AndroidId = trafficData.GetAndroidId()
	device.AndroidIdMd5 = trafficData.GetMd5AndroidId()
	device.Idfa = trafficData.GetIdfa()
	device.IdfaMd5 = trafficData.GetMd5Idfa()
	device.Oaid = trafficData.GetOaid()
	device.OaidMd5 = trafficData.GetMd5Oaid()
	//device.Caid = trafficData.GetCaid()

	if request.Device.Lat != 0 || request.Device.Lon != 0 {
		device.Geo = &keji_broker_entity.KeJiGeo{
			Lat: request.Device.Lat,
			Lon: request.Device.Lon,
		}
	}
	device.System = &keji_broker_entity.KeJiSystem{
		SystemBootMark:   trafficData.GetBootMark(),
		SystemUpdateMark: trafficData.GetUpdateMark(),
	}
	return device
}

func (impl *KeJiDspBroker) mappingDeviceType(deviceType entity.DeviceType) int {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	default:
		return 1
	}
}

func (impl *KeJiDspBroker) mappingOs(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "iOS"
	case entity.OsTypeAndroid:
		return "Android"
	default:
		return ""
	}
}

func (impl *KeJiDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionTypeUnknown:
		return 3
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 3
	}
}

func (impl *KeJiDspBroker) mappingCarrier(carrier entity.OperatorType) string {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return "46000"
	case entity.OperatorTypeChinaTelecom, entity.OperatorTypeTietong:
		return "46003"
	case entity.OperatorTypeChinaUnicom:
		return "46000"
	default:
		return ""
	}
}

func (impl *KeJiDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("KeJiDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &keji_broker_entity.KeJiResponse{}
	err = easyjson.Unmarshal(data, response)
	if err != nil {
		zap.L().Error("KeJiDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, data)

	if request.IsDebug {
		zap.L().Info("KeJiDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data)))))
	}

	if response.Nbr != 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	if len(response.Seatbid) < 1 || len(response.Seatbid[0].Bidcn) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)
	for _, v := range response.Seatbid {
		for _, resBid := range v.Bidcn {
			candidateAd := &entity.Ad{
				DspId:      impl.GetDspId(),
				DspSlotId:  broadcastCandidate.GetDspSlotId(),
				DspSlotKey: broadcastCandidate.GetDspSlotKey(),
			}

			monitor := impl.ParseTrackingData(request, resBid)
			if monitor == nil {
				return nil, err_code.ErrBrokerResponseInternalFail
			}

			candidateAd.AdMonitorInfo = monitor

			if resBid.DownloadApp != nil {
				candidateAd.AppInfo = &entity.AppInfo{
					PackageName: resBid.DownloadApp.PackageName,
					AppName:     resBid.DownloadApp.AppName,
					Icon:        resBid.DownloadApp.AppIcon,
					AppVersion:  resBid.DownloadApp.AppVersion,
					PackageSize: resBid.DownloadApp.PackageSizeBytes,
					Privacy:     resBid.DownloadApp.AppPrivacy,
					Permission:  resBid.DownloadApp.PermissionsUrl,
					Develop:     resBid.DownloadApp.AdvertiserName,
				}
			}

			candidateCreative := impl.ParseCreativeData(resBid)
			//if candidateCreative == nil {
			//	return nil, err_code.ErrBrokerResponseInternalFail
			//}

			candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
			candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
			candidate.SetBidPrice(uint32(resBid.Price))
			candidate.SetBidType(entity.BidTypeCpm)
			candidate.SetCreative(candidateCreative)
			candidate.SetDspProtocol(impl.GetDspProtocol())
			result = append(result, candidate)
			break

			//if resBid.DealId != "" {
			//	bidInfo.CreativeData.SetDealId(resBid.DealId)
			//}
		}

	}

	return result, nil
}

func (impl *KeJiDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.dspProtocol).EncodeWithKey(uint64(chargePrice), impl.iKey, impl.eKey)
	if err != nil {
		return ""
	}

	return result
}

func (impl *KeJiDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *keji_broker_entity.KeJiBidcn) *entity.AdMonitorInfo {
	if bid.Action == nil {
		return nil
	}

	if bid.Tracking == nil {
		return nil
	}

	trackings := &entity.AdMonitorInfo{
		LandingUrl:            bid.Action.LandingPageUrl,
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      bid.Tracking.Clkurl,
		LandingAction:         entity.LandingTypeInWebView,
	}

	for _, impTrack := range bid.Tracking.Nurl {
		if strings.Contains(impTrack, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impTrack, impl.MacroWinPrice, "__DSPWPRICE__")
			trackings.AddWinNoticeUrl(newImpTrack)
		} else {
			trackings.AddWinNoticeUrl(impTrack)
		}

	}

	for _, impTrack := range bid.Tracking.Impurl {
		if strings.Contains(impTrack, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impTrack, impl.MacroWinPrice, "__DSPWPRICE__")
			trackings.ImpressionMonitorList = append(trackings.ImpressionMonitorList, newImpTrack)
		} else {
			trackings.ImpressionMonitorList = append(trackings.ImpressionMonitorList, impTrack)
		}
	}

	trackings.DeepLinkUrl = bid.Action.DeeplinkUrl

	if len(bid.Action.AppDownloadUrl) > 0 {
		trackings.LandingAction = entity.LandingTypeDownload
		trackings.LandingUrl = bid.Action.AppDownloadUrl
	}
	if bid.Tracking.Em != nil {
		if len(bid.Tracking.Em.VideoPlay) > 0 {
			trackings.VideoStartUrlList = bid.Tracking.Em.VideoPlay
		}

		if len(bid.Tracking.Em.VideoOver) > 0 {
			trackings.VideoCloseUrlList = bid.Tracking.Em.VideoOver
		}
	}
	return trackings
}

func (impl *KeJiDspBroker) ParseCreativeData(bid *keji_broker_entity.KeJiBidcn) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.Adid,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if bid.Ad != nil {
		hasTxt := false
		hasImage := false
		hasVideo := false
		if bid.Ad.Title != "" {
			hasTxt = true
			material := &entity.Material{
				MaterialType: entity.MaterialTypeTitle,
				Data:         bid.Ad.Title,
			}
			creative.MaterialList = append(creative.MaterialList, material)

		}
		if bid.Ad.Desc != "" {
			hasTxt = true
			material := &entity.Material{
				MaterialType: entity.MaterialTypeDesc,
				Data:         bid.Ad.Desc,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

		//if bid.Adm.Button != "" {
		//	hasTxt = true
		//	material.Resource = append(material.Resource, ad_entity.Resource{
		//		Type:     dict.MaterialResourceTypeText,
		//		AttrName: dict.MaterialResourceLayoutButtonText,
		//		Text: &ad_entity.Text{
		//			Text: bid.Adm.Button,
		//		},
		//	})
		//}

		if bid.Ad.Video != nil {
			hasVideo = true

			material := &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          bid.Ad.Video.Url,
				Duration:     float64(bid.Ad.Video.Duration),
				Width:        int32(bid.Ad.Video.W),
				Height:       int32(bid.Ad.Video.H),
			}
			creative.MaterialList = append(creative.MaterialList, material)

			if bid.Ad.Video.Cover != "" {
				materialImg := &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          bid.Ad.Video.Cover,
					Width:        int32(bid.Ad.Video.CoverW),
					Height:       int32(bid.Ad.Video.CoverH),
				}
				creative.MaterialList = append(creative.MaterialList, materialImg)
			}
		}

		if bid.Ad.Icon != nil {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeIcon,
				Url:          bid.Ad.Icon.Url,
				Width:        int32(bid.Ad.Icon.W),
				Height:       int32(bid.Ad.Icon.H),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

		for _, img := range bid.Ad.Images {
			hasImage = true
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img.Url,
				Width:        int32(img.W),
				Height:       int32(img.H),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
		if hasImage && !hasVideo && !hasTxt {
			//material.MimeType = dict.MimeJpg
		} else if !hasImage && hasVideo && !hasTxt {
			//material.MimeType = dict.MimeMp4
		} else {
			//material.MimeType = dict.MimeFeed
		}
	}
	return creative
}
