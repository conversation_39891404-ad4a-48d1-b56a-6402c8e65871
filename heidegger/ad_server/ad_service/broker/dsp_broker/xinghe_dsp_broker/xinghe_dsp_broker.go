package xinghe_dsp_broker

import (
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/xinghe_dsp_broker/xinghe_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"fmt"
)

type XingHeDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *XingHeDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string
}

func NewXingHeDspBroker(dspId utils.ID) *XingHeDspBroker {
	return &XingHeDspBroker{
		slotRegister:  NewXingHeDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "__win_price__",
	}
}

func (impl *XingHeDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *XingHeDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("XingHeDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("XingHeDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))

		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("XingHeDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	requestId := slotId + type_convert.GetAssertString(time.Now().UnixMilli())

	xhRequest := &xinghe_dsp_entity.BidRequest{
		RequestId: requestId,
		Device:    impl.encodeDevice(request, trafficData),
		Imps:      impl.encodeImps(request, candidate, trafficData),
		App: xinghe_dsp_entity.App{
			AppName: trafficData.GetAppName(),
			PkgName: trafficData.GetAppBundle(),
			Version: trafficData.GetAppVersion(),
		},
		Version: "1.1.0",
	}

	if len(dspSlot.PkgName) > 0 {
		xhRequest.App.PkgName = dspSlot.PkgName
	}

	if len(dspSlot.AppName) > 0 {
		xhRequest.App.AppName = dspSlot.AppName
	}

	if len(dspSlot.AppVersion) > 0 {
		xhRequest.App.Version = dspSlot.AppVersion
	}

	req, _, err := impl.BuildSonicJsonHttpRequest(xhRequest)
	if err != nil {
		zap.L().Error("XingHeDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}

	//reqBody, _ := easyjson.Marshal(xhRequest)
	//
	//req, err := http.NewRequest(http.MethodPost, impl.GetBidUrl(), bytes.NewBuffer(reqBody))
	//if err != nil {
	//	zap.L().Error("XingHeDspBroker http BuildJsonHttpRequest err", zap.Error(err))
	//	return nil, err
	//}
	//req.Header.Set("Accept-Encoding", "gzip")
	//req.Header.Set("Content-Type", "application/json")

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(xhRequest)
		zap.L().Info("XingHeDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	impl.SampleDspBroadcastSonicJsonRequest(impl.dspId, dspSlot.Id, candidate, xhRequest)

	return req, nil
}

func (impl *XingHeDspBroker) encodeImps(request *ad_service.AdRequest, candidate *ad_service.AdCandidate, trafficData ad_service_entity.TrafficData) []xinghe_dsp_entity.Imp {
	imps := make([]xinghe_dsp_entity.Imp, 0)

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return imps
	}

	bidFloor := candidate.GetBidFloor()
	imp := xinghe_dsp_entity.Imp{
		Id:       1,
		Bidfloor: float64(bidFloor.Price),
		Width:    dspSlot.Width,
		Height:   dspSlot.Height,
		Count:    1,
	}

	if imp.Width == 0 || imp.Height == 0 {
		imp.Width = int(trafficData.GetSlotWidth())
		imp.Height = int(trafficData.GetSlotHeight())
	}
	if imp.Width == 0 || imp.Height == 0 {
		if len(request.SlotSize) > 0 {
			imp.Width = int(request.SlotSize[0].Width)
			imp.Height = int(request.SlotSize[0].Height)
		}
	}

	if dspSlot.TemplateId == "video" {
		video := &xinghe_dsp_entity.RequestVideo{
			VideoType:   1,
			MinDuration: int(request.VideoMinDuration),
			MaxDuration: int(request.VideoMaxDuration),
			MimeTypes:   []string{"mp4"},
			Delivery:    0,
			Orientation: 0,
		}

		if trafficData.GetScreenOrientation() == entity.ScreenOrientationTypePortrait {
			video.Orientation = 1
		} else if trafficData.GetScreenOrientation() == entity.ScreenOrientationTypeLandscape {
			video.Orientation = 2
		}

		if len(request.SlotSize) > 0 {
			video.Sizes = make([]xinghe_dsp_entity.Size, 0)

			for _, size := range request.SlotSize {
				video.Sizes = append(video.Sizes, xinghe_dsp_entity.Size{
					Width:  int(size.Width),
					Height: int(size.Height),
				})
			}
		}

		imp.Video = video
	}

	cInfo := &xinghe_dsp_entity.CustomizedInfo{
		InstallPkgs: request.App.InstalledApp,
	}
	imp.CustomizedInfo = cInfo

	imps = append(imps, imp)

	return imps

}

func (impl *XingHeDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) xinghe_dsp_entity.Device {
	deviceId, _ := trafficData.GetDeviceIdWithType()

	deviceInfo := xinghe_dsp_entity.Device{
		DeviceId:     deviceId,
		Network:      impl.mappingConnectionType(trafficData.GetConnectionType()),
		DeviceType:   impl.mappingDeviceType(trafficData.GetDeviceType()),
		Os:           impl.mappingOsType(trafficData.GetOsType()),
		OsVersion:    trafficData.GetOsVersion(),
		Imei:         trafficData.GetImei(),
		ImeiMd5:      trafficData.GetMd5Imei(),
		Oaid:         trafficData.GetOaid(),
		OaidMd5:      trafficData.GetMd5Oaid(),
		AndroidId:    trafficData.GetAndroidId(),
		AndroidIdMd5: "",
		Idfa:         trafficData.GetIdfa(),
		IdfaMd5:      trafficData.GetMd5Idfa(),
		Caid:         device_utils.GetCaidRaw(trafficData.GetCaid()),
		CaidVersion:  device_utils.GetCaidVersion(trafficData.GetCaid()),
		Brand:        trafficData.GetBrand(),
		Model:        trafficData.GetModel(),
		Carrier:      impl.mappingCarrier(trafficData.GetOperatorType()),
		Mac:          trafficData.GetMac(),
		MacMd5:       trafficData.GetMd5Mac(),
		Ip:           trafficData.GetRequestIp(),
		IpMd5:        "",
		UserAgent:    trafficData.GetUserAgent(),
		ScreenWidth:  int(trafficData.GetScreenWidth()),
		ScreenHeight: int(trafficData.GetScreenHeight()),
		Orientation:  impl.mappingOrientation(trafficData.GetScreenOrientation()),
		BootMark:     trafficData.GetBootMark(),
		UpdateMark:   trafficData.GetUpdateMark(),
		Geo: &xinghe_dsp_entity.Geo{
			Latitude:  trafficData.GetGeoLatitude(),
			Longitude: trafficData.GetGeoLongitude(),
		},
		Ppi:          int(request.Device.PPI),
		Density:      float64(trafficData.GetScreenDensity()),
		Imsi:         "",
		HmsCore:      request.Device.VercodeHms,
		OsUpdateTime: trafficData.GetDeviceUpgradeTime(),
		DIT:          trafficData.GetDeviceInitTime(),
		SUT:          trafficData.GetDeviceUpgradeTime(),
		SST:          trafficData.GetDeviceStartupTime(),
	}

	return deviceInfo
}

func (impl *XingHeDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 6
	case entity.ConnectionTypeWifi:
		return 6
	case entity.ConnectionTypeCellular:
		return 4
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 1
	}
}

func (impl *XingHeDspBroker) mappingOsType(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "ios"
	case entity.OsTypeAndroid:
		return "android"
	default:
		return ""
	}
}

func (impl *XingHeDspBroker) mappingCarrier(carrier entity.OperatorType) int {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaTelecom:
		return 3
	case entity.OperatorTypeTietong:
		return 3
	case entity.OperatorTypeChinaUnicom:
		return 2
	default:
		return 0
	}
}

func (impl *XingHeDspBroker) mappingOrientation(s entity.ScreenOrientationType) int {
	switch s {
	case entity.ScreenOrientationTypeLandscape:
		return 2
	case entity.ScreenOrientationTypePortrait:
		return 1
	}

	return 0
}

func (impl *XingHeDspBroker) mappingDeviceType(s entity.DeviceType) int {
	switch s {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	case entity.DeviceTypePc:
		return 4
	}

	return 1
}

func (impl *XingHeDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *XingHeDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("XingHeDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 && resp.StatusCode != 206 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &xinghe_dsp_entity.BidResponse{}

	resBody, err := impl.ParseSonicJsonHttpResponse(resp, data, response)
	//err = easyjson.Unmarshal(data, response)
	if err != nil {
		zap.L().Error("XingHeDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := sonic.Marshal(response)
		zap.L().Info("XingHeDspBroker raw reponse1", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	if response.Code != 200 || len(response.Data.Groups) == 0 || len(response.Data.Groups[0].Ads) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, resBid := range response.Data.Groups[0].Ads {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = &entity.AppInfo{
			PackageName: resBid.AppInfo.PkgName,
			AppName:     resBid.AppInfo.Name,
			Icon:        resBid.AppInfo.LogoUrl,
			WechatExt:   nil,
			AppVersion:  resBid.AppInfo.Version,
			PackageSize: int(resBid.AppInfo.Size),
			Privacy:     resBid.AppInfo.SensitiveUrl,
			Permission:  resBid.AppInfo.UsesPermission,
			AppDesc:     resBid.AppInfo.Desc,
			AppDescURL:  "",
			Develop:     resBid.AppInfo.SoftCorpName,
			AppBeian:    "",
			AppAge:      "",
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

		candidateCreative := impl.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(resBid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(type_convert.GetAssertString(resBid.Id))
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break
	}

	return result, nil
}

func (impl *XingHeDspBroker) ParseCreativeData(bid xinghe_dsp_entity.Ad) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.CreativeId,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	item := bid

	if len(item.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         item.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Desc) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         item.Desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.AdIcon) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          item.AdIcon,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	for _, image := range item.Imgs {
		if len(image.Url) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          image.Url,
				Width:        int32(image.Width),
				Height:       int32(image.Height),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if item.Video != nil {
		if len(item.Video.VideoUrl) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          item.Video.VideoUrl,
				Width:        int32(item.Video.Width),
				Height:       int32(item.Video.Height),
				Duration:     float64(item.Video.Duration),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

		if len(item.Video.CoverUrl) > 0 {
			material1 := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          item.Video.CoverUrl,
				Width:        int32(item.Video.Width),
				Height:       int32(item.Video.Height),
			}
			creative.MaterialList = append(creative.MaterialList, material1)
		}

	}

	return creative

}

func (impl *XingHeDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid xinghe_dsp_entity.Ad) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      bid.ClickTrackUrls,
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkUrl:           bid.AppInfo.DeepLink,
		LandingUrl:            bid.Link,
		H5LandingUrl:          bid.Link,
		DownloadUrl:           bid.AppInfo.DownUrl,
	}

	if bid.DownloadAd == 1 {
		tracking.LandingAction = entity.LandingTypeDownload
		if len(bid.AppInfo.DownUrl) != 0 {
			tracking.LandingUrl = bid.AppInfo.DownUrl
		}
	}

	if len(tracking.LandingUrl) == 0 && bid.Video != nil {
		tracking.LandingUrl = bid.Video.CUrl
	}

	for _, impUrl := range bid.ImpTrackUrls {
		if strings.Contains(impUrl, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impUrl, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impUrl)
		}
	}

	if len(bid.NoticeUrl) > 0 {
		if strings.Contains(bid.NoticeUrl, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(bid.NoticeUrl, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.NoticeUrl)
		}
	}

	if len(bid.AppInfo.DsUrls) > 0 {
		tracking.AppDownloadStartedMonitorList = append(tracking.AppDownloadStartedMonitorList, bid.AppInfo.DsUrls...)
	}

	if len(bid.AppInfo.DfUrls) > 0 {
		tracking.AppDownloadFinishedMonitorList = append(tracking.AppDownloadFinishedMonitorList, bid.AppInfo.DfUrls...)
	}

	if len(bid.AppInfo.SsUrls) > 0 {
		tracking.AppInstallStartMonitorList = append(tracking.AppInstallStartMonitorList, bid.AppInfo.SsUrls...)
	}

	if len(bid.AppInfo.SfUrls) > 0 {
		tracking.AppInstalledFinishMonitorList = append(tracking.AppInstalledFinishMonitorList, bid.AppInfo.SfUrls...)
	}

	for _, event := range bid.AppInfo.EventTracks {
		switch event.EventType {
		case 12, 14:
			tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, event.EventTrackUrls...)
		}
	}

	if bid.Video != nil && len(bid.Video.EventTracks) > 0 {
		for _, event := range bid.Video.EventTracks {
			switch event.EventType {
			case 1:
				tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, event.EventTrackUrls...)
			case 5:
				tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, event.EventTrackUrls...)
			}
		}
	}

	return tracking

}
