package xinghe_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"strings"
)

type XingHeSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`
	TemplateId string `json:"template_id"`
}

func (info *XingHeSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {

	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	templateId, err := dspSlotInfo.ExtraData.GetString("template_id")
	if err != nil {
	}

	templateIds := strings.Split(templateId, ",")
	for _, item := range templateIds {
		if item != "" {
			info.TemplateId = item
			break
		}
	}

	return nil
}

type XingHeDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*XingHeSlotSlotInfo
}

func NewXingHeDspSlotRegister(dspId utils.ID) *XingHeDspSlotRegister {
	return &XingHeDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*XingHeSlotSlotInfo),
	}
}

func (r *XingHeDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *XingHeDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*XingHeSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &XingHeSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[XingHeDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *XingHeDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *XingHeDspSlotRegister) GetSlotInfo(slotId utils.ID) *XingHeSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
