package yiguang_dsp_broker

import (
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jd_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/yiguang_dsp_broker/yiguang_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"strings"
	"time"
	"fmt"
)

type YiGuangDspBroker struct {
	dsp_broker.DspBrokerBase

	jdScore jd_broker.JdUserScore

	slotRegister *YiGuangDspSlotRegister

	MacroWinPrice string
}

func NewYiGuangDspBroker(dspId utils.ID) *YiGuangDspBroker {
	return &YiGuangDspBroker{
		slotRegister:  NewYiGuangDspSlotRegister(dspId),
		MacroWinPrice: "__WIN_PRICE__",
	}
}

func (impl *YiGuangDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *YiGuangDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("YiGuangDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("YiGuangDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("YiGuangDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidFloor := candidate.GetBidFloor()
	timeNowMs := type_convert.GetAssertString(time.Now().UnixMilli())

	reqId := strings.ReplaceAll(utils.NewUUID(), "-", "")

	ygRequest := &yiguang_dsp_entity.BidRequest{
		Version:      "3.1.5",
		Time:         timeNowMs,
		ReqID:        reqId,
		AppID:        dspSlot.AppId,
		AppVer:       dspSlot.AppVersion,
		AdSpotID:     slotId,
		BidFloor:     float64(bidFloor.Price),
		Bundle:       trafficData.GetAppBundle(),
		UA:           trafficData.GetUserAgent(),
		Make:         trafficData.GetBrand(),
		Model:        trafficData.GetModel(),
		OS:           impl.mappingOs(trafficData.GetOsType()),
		OSVersion:    trafficData.GetOsVersion(),
		Carrier:      impl.mappingCarrier(trafficData.GetOperatorType()),
		Network:      impl.mappingConnectionType(trafficData.GetConnectionType()),
		ScreenW:      int(trafficData.GetScreenWidth()),
		ScreenH:      int(trafficData.GetScreenHeight()),
		PPI:          int(request.Device.PPI),
		DPI:          0,
		Density:      float64(trafficData.GetScreenDensity()),
		OAID:         trafficData.GetOaid(),
		IMEI:         trafficData.GetImei(),
		IMEIMD5:      trafficData.GetMd5Imei(),
		MAC:          trafficData.GetMac(),
		AndroidID:    trafficData.GetAndroidId(),
		AndroidIDMD5: trafficData.GetMd5AndroidId(),
		IMSI:         "",
		IMSIMD5:      "",
		IDFA:         trafficData.GetIdfa(),
		IDFAMD5:      trafficData.GetMd5Idfa(),
		IDFV:         trafficData.GetIdfv(),
		IDFVMD5:      trafficData.GetMd5Idfv(),
		ImpSize:      1,
		Lat:          trafficData.GetGeoLatitude(),
		Lon:          trafficData.GetGeoLongitude(),
		DeviceType:   impl.mappingDeviceType(trafficData.GetDeviceType()),
		Orientation:  impl.mappingOrientation(trafficData.GetScreenOrientation()),
		DoNotTrack:   0,
		MaxDuration:  120,
		UniqueID:     "",
		AppStoreVer:  "",
		Device: yiguang_dsp_entity.Device{
			AAID:        trafficData.GetAaid(),
			BootMark:    trafficData.GetBootMark(),
			UpdateMark:  trafficData.GetUpdateMark(),
			BirthTime:   trafficData.GetDeviceInitTime(),
			BootTime:    trafficData.GetDeviceStartupTime(),
			UpdateTime:  trafficData.GetDeviceUpgradeTime(),
			Paid:        "",
			CaidVersion: device_utils.GetCaidVersion(trafficData.GetCaid()),
			Caid:        device_utils.GetCaidRaw(trafficData.GetCaid()),
		},
		Ext: yiguang_dsp_entity.RequestExt{
			VerCodeOfHms: request.Device.VercodeHms,
			VerCodeOfAG:  request.Device.VercodeAg,
		},
	}

	if trafficData.GetOsType() == entity.OsTypeAndroid {
		ygRequest.OSVersion = impl.mappingOsVersion(trafficData.GetOsVersion())
	}

	if ygRequest.Orientation == 0 {
		if ygRequest.ScreenW < ygRequest.ScreenH {
			ygRequest.Orientation = 1
		} else if ygRequest.ScreenW > ygRequest.ScreenH {
			ygRequest.Orientation = 2
		}
	}

	if len(ygRequest.Device.BootMark) == 0 && len(ygRequest.Device.UpdateMark) > 0 {
		ygRequest.Device.BootMark = ygRequest.Device.UpdateMark
	}

	if len(dspSlot.PkgName) > 0 {
		ygRequest.Bundle = dspSlot.PkgName
	}

	if len(dspSlot.AppVersion) > 0 {
		ygRequest.AppVer = dspSlot.AppVersion
	}

	if len(request.Device.Caids) > 0 {
		for _, caidItem := range request.Device.Caids {
			if caidItem == trafficData.GetCaid() {
				continue
			}
			deviceCaid1 := yiguang_dsp_entity.Caid{
				Caid:    device_utils.GetCaidRaw(caidItem),
				Version: device_utils.GetCaidVersion(caidItem),
			}
			ygRequest.Device.CaidList = append(ygRequest.Device.CaidList, deviceCaid1)
		}
	}

	if request.Device.IsIp6 {
		ygRequest.Ipv6 = trafficData.GetRequestIp()
	} else {
		ygRequest.IP = trafficData.GetRequestIp()
	}

	ygRequest.Token = md5_utils.GetMd5String(dspSlot.AppId + dspSlot.AppKey + timeNowMs)

	req, _, err := impl.BuildSonicJsonHttpRequest(ygRequest)
	if err != nil {
		zap.L().Error("YiGuangDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}
	if request.IsDebug {
		reqbody, _ := sonic.Marshal(ygRequest)
		zap.L().Info("YiGuangDspBroker.EncodeRequest end, raw Request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqbody)))))
	}

	impl.SampleDspBroadcastSonicJsonRequest(impl.GetDspId(), dspSlot.Id, candidate, ygRequest)
	return req, nil
}

func (impl *YiGuangDspBroker) mappingOrientation(s entity.ScreenOrientationType) int {
	switch s {
	case entity.ScreenOrientationTypePortrait:
		return 1
	case entity.ScreenOrientationTypeLandscape:
		return 2
	default:
		return 0
	}
}

func (impl *YiGuangDspBroker) mappingDeviceType(s entity.DeviceType) int {
	switch s {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	default:
		return 1
	}
}

func (impl *YiGuangDspBroker) mappingOsVersion(osv string) string {
	result := osv
	if strings.HasPrefix(osv, "34") {
		result = strings.ReplaceAll(osv, "34", "14")
	} else if strings.HasPrefix(osv, "33") {
		result = strings.ReplaceAll(osv, "33", "13")
	} else if strings.HasPrefix(osv, "31") {
		result = strings.ReplaceAll(osv, "31", "12")
	} else if strings.HasPrefix(osv, "30") {
		result = strings.ReplaceAll(osv, "30", "11")
	} else if strings.HasPrefix(osv, "29") {
		result = strings.ReplaceAll(osv, "29", "10")
	}

	return result
}

func (impl *YiGuangDspBroker) mappingOs(os entity.OsType) int {
	switch os {
	case entity.OsTypeIOS:
		return 1
	case entity.OsTypeAndroid:
		return 2
	case entity.OsTypeWindows:
		return 3
	default:
		return 0
	}
}

func (impl *YiGuangDspBroker) mappingCarrier(carrier entity.OperatorType) string {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return "46000"
	case entity.OperatorTypeChinaTelecom:
		return "46003"
	case entity.OperatorTypeTietong:
		return "46020"
	case entity.OperatorTypeChinaUnicom:
		return "46001"
	default:
		return ""
	}
}

func (impl *YiGuangDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionTypeCellular:
		return 7
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (impl *YiGuangDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		zap.L().Error("[YiGuangDspBroker] encode price error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return ""
	}

	return result
}

func (impl *YiGuangDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("YiGuangDspBroker.ParseResponse Enter")
	broadcastCandidate := broadcastCandidateList[0]

	if resp.StatusCode != 200 {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.StatusCode), type_convert.GetAssertString(resp.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &yiguang_dsp_entity.BidResponse{}
	resBody, err := impl.ParseSonicJsonHttpResponse(resp, data, response)
	if err != nil {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		zap.L().Error("YiGuangDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	impl.SampleDspBroadcastResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := sonic.Marshal(response)
		zap.L().Info("YiGuangDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	msg := response.Msg
	if len(msg) > 0 {
		msgArr := strings.Split(msg, "|")
		if len(msgArr) > 0 {
			msg = msgArr[0]
		}
	}

	impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(response.Code), msg)

	if response.Code != 200 || len(response.Imp) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)
	for _, resBid := range response.Imp {

		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = &entity.AppInfo{
			PackageName: resBid.DownloadApp.Bundle,
			AppName:     resBid.DownloadApp.Name,
			Icon:        resBid.DownloadApp.Icon,
			AppVersion:  resBid.DownloadApp.AppVer,
			PackageSize: resBid.DownloadApp.Size,
			Privacy:     resBid.DownloadApp.PrivacyURL,
			Permission:  resBid.DownloadApp.PermissionURL,
			AppDesc:     resBid.DownloadApp.Desc,
			AppDescURL:  resBid.DownloadApp.DescURL,
			Develop:     resBid.DownloadApp.Developer,
		}

		candidateAd.AppInfo.WechatExt = &entity.WechatExt{
			ProgramId:   resBid.Ext.AppID,
			ProgramPath: resBid.Ext.Path,
		}

		if len(candidateAd.AppInfo.PackageName) == 0 {
			candidateAd.AppInfo.PackageName = resBid.PackageName
		}

		if len(candidateAd.AppInfo.AppName) == 0 {
			candidateAd.AppInfo.AppName = resBid.AppName
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)
		candidateCreative := impl.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		if len(candidateAd.AdMonitorInfo.DeepLinkUrl) > 0 {
			appName, pkgName, icon := impl.ParseAppInfo(candidateAd.AdMonitorInfo.DeepLinkUrl, request.Device.OsType)
			if len(pkgName) > 0 && len(candidateAd.AppInfo.PackageName) == 0 {
				candidateAd.AppInfo.PackageName = pkgName
				candidateAd.AppInfo.AppName = appName
				candidateAd.AppInfo.Icon = icon
			}
		}

		if len(candidateCreative.CreativeKey) == 0 {
			candidateCreative.CreativeKey = candidateAd.GetAdId().String()
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(resBid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break

	}

	return result, nil
}

func (impl *YiGuangDspBroker) ParseCreativeData(bid yiguang_dsp_entity.Imp) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.MaterialID,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if len(bid.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         bid.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Desc) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         bid.Desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Logo) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.Logo,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	for _, image := range bid.Image {
		if len(image) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          image,
				Width:        int32(bid.Width),
				Height:       int32(bid.Height),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if len(bid.VURL) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          bid.VURL,
			Width:        int32(bid.Width),
			Height:       int32(bid.Height),
			Duration:     float64(bid.Duration),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.VideoImage) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          bid.VideoImage,
			Width:        int32(bid.Width),
			Height:       int32(bid.Height),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}
	return creative
}

func (impl *YiGuangDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid yiguang_dsp_entity.Imp) *entity.AdMonitorInfo {

	tracking := &entity.AdMonitorInfo{
		LandingUrl:            bid.Link,
		H5LandingUrl:          bid.Link,
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      bid.ClickTk,
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkMonitorList:   bid.DeeplinkTk,
		DeepLinkUrl:           bid.Deeplink,
	}

	if bid.Action == 2 {
		tracking.LandingAction = entity.LandingTypeDownload
	} else if bid.Action == 4 {
		tracking.LandingAction = entity.LandingTypeWeChatProgram
	}

	if len(bid.ImpTk) > 0 {
		for _, impTrack := range bid.ImpTk {
			if strings.Contains(impTrack, impl.MacroWinPrice) {
				newImpTrack := strings.ReplaceAll(impTrack, impl.MacroWinPrice, "__DSPWPRICE__")
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
			} else {
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impTrack)
			}
		}
	}

	if len(bid.DownloadTk) > 0 {
		tracking.AppDownloadStartedMonitorList = bid.DownloadTk
	}

	if len(bid.DownloadedTk) > 0 {
		tracking.AppDownloadFinishedMonitorList = bid.DownloadedTk
	}

	if len(bid.InstallTk) > 0 {
		tracking.AppInstallStartMonitorList = bid.InstallTk
	}

	if len(bid.InstalledTk) > 0 {
		tracking.AppInstalledFinishMonitorList = bid.InstalledTk
	}

	if len(bid.StartTk) > 0 {
		tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, bid.StartTk...)
	}

	if len(bid.EndTk) > 0 {
		tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, bid.EndTk...)
	}

	if len(bid.CloseTk) > 0 {
		tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, bid.CloseTk...)
	}

	return tracking

}
