package yiguang_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type YiGuangSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height       int    `json:"height"`
	Width        int    `json:"width"`
	AppName      string `json:"app_name"`
	PkgName      string `json:"pkg_name"`
	AppVersion   string `json:"app_version"`
	AppId        string `json:"app_id"`
	AppKey       string `json:"app_key"`
	CreativeType int    `json:"creative_type"`
}

func (info *YiGuangSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {

	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.AppId, err = dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get app_id from extra_data failed, err: %v", err)
	}

	info.AppKey, err = dspSlotInfo.ExtraData.GetString("app_key")
	if err != nil {
		return fmt.Errorf("get app_key from extra_data failed, err: %v", err)
	}

	creativeType, err := dspSlotInfo.ExtraData.GetString("creative_type")
	if err == nil {
		switch creativeType {
		case "img":
			info.CreativeType = 0
		case "video":
			info.CreativeType = 1
		}
	}

	return nil
}

type YiGuangDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*YiGuangSlotSlotInfo
}

func NewYiGuangDspSlotRegister(dspId utils.ID) *YiGuangDspSlotRegister {
	return &YiGuangDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*YiGuangSlotSlotInfo),
	}
}

func (r *YiGuangDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *YiGuangDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*YiGuangSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &YiGuangSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[YiGuangDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *YiGuangDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *YiGuangDspSlotRegister) GetSlotInfo(slotId utils.ID) *YiGuangSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
