package diansi_dsp_broker

import (
	"io"
	"net/http"
	"strconv"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/diansi_dsp_broker/diansi_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type DianSiDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *DianSiDspSlotRegister
	log          *zap.Logger
	monitorMacro *macro_builder.MonitorMacroInfo
}

func NewDianSiDspBroker(dspId utils.ID) *DianSiDspBroker {
	return &DianSiDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{DspId: dspId},
		slotRegister:  NewDianSiDspSlotRegister(dspId),
		log:           zap.L().With(zap.String("broker", "DianSiDspBroker")),
		monitorMacro: &macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__win_price__",
			MacroClickDownX: "__down_x__",
			MacroClickDownY: "__down_y__",
			MacroClickUpX:   "__up_x__",
			MacroClickUpY:   "__up_y__",
			MacroHWSld:      "__sld__",
		},
	}
}

func (impl *DianSiDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *DianSiDspBroker) BuildRequest(adRequest *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(dspSlot.Id)
	candidate.SetDspSlotKey(slotId)

	bidReq := &diansi_dsp_entity.BidRequest{
		Id:     trafficData.GetRequestId(),
		Device: impl.encodeDevice(adRequest, trafficData),
		App: diansi_dsp_entity.App{
			Name:    trafficData.GetAppName(),
			PkgName: trafficData.GetAppBundle(),
			Version: trafficData.GetAppVersion(),
		},
		Timeout: 300,
		Version: "2.2",
	}
	imps, err := impl.encodeImps(adRequest, candidate, trafficData)
	if err != nil {
		return nil, err
	}
	bidReq.Imps = imps

	if len(dspSlot.PkgName) > 0 {
		bidReq.App.PkgName = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidReq.App.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidReq.App.Version = dspSlot.AppVersion
	}

	httpReq, _, err := impl.BuildSonicJsonHttpRequest(bidReq)
	if err != nil {
		impl.log.WithError(err).Error("BuildSonicJsonHttpRequest error")
		return nil, err
	}

	impl.SampleDspBroadcastSonicJsonRequest(impl.GetDspId(), dspSlot.Id, candidate, bidReq)
	if adRequest.IsDebug {
		payload, _ := sonic.Marshal(bidReq)
		impl.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}

	return httpReq, nil
}

func (impl *DianSiDspBroker) encodeImps(adRequest *ad_service.AdRequest, candidate *ad_service.AdCandidate, trafficData ad_service_entity.TrafficData) ([]diansi_dsp_entity.Imp, error) {
	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	bidFloor := candidate.GetBidFloor()
	slotKey := candidate.GetDspSlotKey()
	slotKeyInt, err := strconv.Atoi(slotKey)
	if err != nil {
		return nil, err_code.ErrDspSlotNotFound.Wrap(err)
	}

	imps := make([]diansi_dsp_entity.Imp, 0)
	imp := diansi_dsp_entity.Imp{
		Id:       1,
		PosId:    slotKeyInt,
		BidFloor: int(bidFloor.Price),
		Width:    dspSlot.Width,
		Height:   dspSlot.Height,
	}

	if imp.Width == 0 || imp.Height == 0 {
		imp.Width = int(trafficData.GetSlotWidth())
		imp.Height = int(trafficData.GetSlotHeight())
	}
	if (imp.Width == 0 || imp.Height == 0) && len(adRequest.SlotSize) > 0 {
		imp.Width = int(adRequest.SlotSize[0].Width)
		imp.Height = int(adRequest.SlotSize[0].Height)
	}

	// video
	if dspSlot.TemplateId == 1 {
		video := &diansi_dsp_entity.Video{
			VideoType:   1,
			MinDuration: int(adRequest.VideoMinDuration),
			MaxDuration: int(adRequest.VideoMaxDuration),
			MimeTypes:   []string{"1"},
			Delivery:    0,
		}

		if trafficData.GetSlotType() == entity.SlotTypeRewardVideo {
			video.VideoType = 2
		}

		if trafficData.GetScreenOrientation() == entity.ScreenOrientationTypePortrait {
			video.Orientation = 1
		} else if trafficData.GetScreenOrientation() == entity.ScreenOrientationTypeLandscape {
			video.Orientation = 2
		}

		// use origin slot size
		if trafficData.GetSlotWidth() > 0 && trafficData.GetSlotHeight() > 0 {
			video.Sizes = &diansi_dsp_entity.VideoSize{
				Width:  int(trafficData.GetSlotWidth()),
				Height: int(trafficData.GetSlotHeight()),
			}
		}

		imp.Video = video
	}

	cInfo := &diansi_dsp_entity.CustomizedInfo{
		InstallPkgs: adRequest.App.InstalledApp,
	}
	imp.CustomizedInfo = cInfo

	imps = append(imps, imp)

	return imps, nil
}

func (impl *DianSiDspBroker) encodeDevice(adRequest *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) diansi_dsp_entity.Device {
	deviceInfo := diansi_dsp_entity.Device{
		DeviceType:   impl.mappingDeviceType(trafficData.GetDeviceType()),
		Os:           impl.mappingOsType(trafficData.GetOsType()),
		OsVersion:    trafficData.GetOsVersion(),
		Oaid:         trafficData.GetOaid(),
		OaidMd5:      trafficData.GetMd5Oaid(),
		AndroidId:    trafficData.GetAndroidId(),
		AndroidIdMd5: trafficData.GetMd5AndroidId(),
		Imei:         trafficData.GetImei(),
		ImeiMd5:      trafficData.GetMd5Imei(),
		Idfa:         trafficData.GetIdfa(),
		IdfaMd5:      trafficData.GetMd5Idfa(),
		Caid:         device_utils.GetCaidRaw(trafficData.GetCaid()),
		CaidVersion:  device_utils.GetCaidVersion(trafficData.GetCaid()),
		Ip:           trafficData.GetRequestIp(),
		IpMd5:        "",
		Mac:          trafficData.GetMac(),
		MacMd5:       trafficData.GetMd5Mac(),
		Network:      impl.mappingConnectionType(trafficData.GetConnectionType()),
		Carrier:      impl.mappingCarrier(trafficData.GetOperatorType()),
		Ua:           trafficData.GetUserAgent(),
		Geo: &diansi_dsp_entity.Geo{
			Lat:     trafficData.GetGeoLatitude(),
			Lon:     trafficData.GetGeoLongitude(),
			GpsType: impl.mappingGeoType(adRequest.Device.GeoStandard),
		},
		ScreenWidth:     int(trafficData.GetScreenWidth()),
		ScreenHeight:    int(trafficData.GetScreenHeight()),
		Orientation:     impl.mappingOrientation(trafficData.GetScreenOrientation()),
		Make:            adRequest.Device.Vendor,
		Brand:           trafficData.GetBrand(),
		Model:           trafficData.GetModel(),
		Ppi:             int(adRequest.Device.PPI),
		Density:         float64(trafficData.GetScreenDensity()),
		BootMark:        trafficData.GetBootMark(),
		UpdateMark:      trafficData.GetUpdateMark(),
		BootTime:        trafficData.GetDeviceStartupTime(),
		BirthTime:       trafficData.GetDeviceInitTime(),
		Imsi:            adRequest.Device.Imsi,
		HmsVersion:      adRequest.Device.VercodeHms,
		AppstoreVersion: adRequest.Device.AppStoreVersion,
		Paid:            adRequest.Device.Paid,
		Caids:           make([]*diansi_dsp_entity.CaidInfo, 0),
	}

	if adRequest.Device.IsIp6 {
		deviceInfo.Ipv6 = trafficData.GetRequestIp()
		deviceInfo.Ip = ""
	}
	if len(trafficData.GetCaid()) > 0 {
		deviceInfo.Caids = append(deviceInfo.Caids, &diansi_dsp_entity.CaidInfo{
			Caid:    device_utils.GetCaidRaw(trafficData.GetCaid()),
			Version: device_utils.GetCaidVersion(trafficData.GetCaid()),
		})
	}
	if len(trafficData.GetCaids()) > 0 {
		for _, item := range trafficData.GetCaids() {
			if item == trafficData.GetCaid() {
				continue
			}
			deviceInfo.Caids = append(deviceInfo.Caids, &diansi_dsp_entity.CaidInfo{
				Caid:    device_utils.GetCaidRaw(item),
				Version: device_utils.GetCaidVersion(item),
			})
		}
	}
	if adRequest.Device.SystemTotalMem > 0 {
		deviceInfo.SysMemory = strconv.FormatInt(adRequest.Device.SystemTotalMem, 10)
	}
	if adRequest.Device.SystemTotalDisk > 0 {
		deviceInfo.SysDiskSize = strconv.FormatInt(adRequest.Device.SystemTotalDisk, 10)
	}

	return deviceInfo
}

func (impl *DianSiDspBroker) mappingGeoType(geoType int) string {
	switch geoType {
	case 0, 3:
		return "BD-09"
	case 1:
		return "GCJ-02"
	case 2:
		return "WGS-84"
	default:
		return ""
	}
}

func (impl *DianSiDspBroker) mappingDeviceType(deviceType entity.DeviceType) int {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	case entity.DeviceTypePc:
		return 4
	default:
		return 9
	}
}

func (impl *DianSiDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 6
	case entity.ConnectionTypeWifi:
		return 6
	case entity.ConnectionTypeCellular:
		return 4
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 1
	}
}

func (impl *DianSiDspBroker) mappingOsType(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "ios"
	case entity.OsTypeAndroid:
		return "android"
	default:
		return "other"
	}
}

func (impl *DianSiDspBroker) mappingCarrier(carrier entity.OperatorType) int {
	switch carrier {
	case entity.OperatorTypeChinaMobile, entity.OperatorTypeTietong:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 2
	case entity.OperatorTypeChinaTelecom:
		return 3
	default:
		return 0
	}
}

func (impl *DianSiDspBroker) mappingOrientation(s entity.ScreenOrientationType) int {
	switch s {
	case entity.ScreenOrientationTypeLandscape:
		return 2
	case entity.ScreenOrientationTypePortrait:
		return 1
	}

	return 0
}

func (impl *DianSiDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		impl.log.WithError(err).WithField("price", chargePrice).Error("chargePriceEncoder error")
		return ""
	}

	return result
}

func (impl *DianSiDspBroker) ParseResponse(adRequest *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, httpResp *http.Response) (ad_service.DspAdCandidateList, error) {
	if httpResp.StatusCode != 200 && httpResp.StatusCode != 206 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	bidResp := new(diansi_dsp_entity.BidResponse)
	payload, err := impl.ParseSonicJsonHttpResponse(httpResp, data, bidResp)
	if err != nil {
		impl.log.WithError(err).Warn("ParseResponse error")
		return nil, err_code.ErrBrokerParseError.Wrap(err)
	}

	broadcastCandidate := broadcastCandidateList[0]

	impl.SampleDspBroadcastResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, httpResp.StatusCode, payload)
	if adRequest.IsDebug {
		payload, _ := sonic.Marshal(bidResp)
		impl.log.WithField("response", string(payload)).Info("ParseResponse debug")
	}

	if bidResp.Code != 0 || len(bidResp.SeatBid) == 0 || len(bidResp.SeatBid[0].Bids) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, bid := range bidResp.SeatBid[0].Bids {
		candidateAd := &entity.Ad{
			DspId:         impl.GetDspId(),
			DspSlotId:     broadcastCandidate.GetDspSlotId(),
			DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
			AdMonitorInfo: impl.ParseTrackingData(adRequest, &bid),
		}

		if bid.App != nil {
			candidateAd.AppInfo = &entity.AppInfo{
				AppName:     bid.App.Name,
				PackageName: bid.App.PkgName,
				AppVersion:  bid.App.Version,
				Icon:        bid.App.LogoUrl,
				PackageSize: bid.App.Size,
				Permission:  bid.App.PermUrl,
				Privacy:     bid.App.PrivacyUrl,
				Develop:     bid.App.Developer,
				AppDesc:     bid.App.AppIntro,
			}
			if len(bid.App.AppIntro) == 0 {
				candidateAd.AppInfo.AppDesc = bid.App.FunctionIntro
			}
		}

		creative := impl.ParseCreative(&bid)
		if creative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		impl.replaceMacro(candidateAd.AdMonitorInfo)

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(bid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(creative)
		candidate.SetDspAdID(type_convert.GetAssertString(bid.Id))
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break
	}

	return result, nil
}

func (impl *DianSiDspBroker) ParseCreative(bid *diansi_dsp_entity.Bid) *entity.Creative {
	creative := &entity.Creative{
		CreativeKey: bid.CreativeId,
	}

	item := bid

	if len(item.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         item.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Desc) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         item.Desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.IconUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          item.IconUrl,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	for _, image := range item.Images {
		if len(image.Url) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          image.Url,
				Width:        int32(image.Width),
				Height:       int32(image.Height),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if item.Video != nil {
		if len(item.Video.Url) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          item.Video.Url,
				Width:        int32(item.Video.Width),
				Height:       int32(item.Video.Height),
				Duration:     float64(item.Video.Duration),
				FileSize:     int32(item.Video.Size),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

		if len(item.Video.CoverUrl) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          item.Video.CoverUrl,
				Width:        int32(item.Video.Width),
				Height:       int32(item.Video.Height),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	return creative
}

func (impl *DianSiDspBroker) ParseTrackingData(adRequest *ad_service.AdRequest, bid *diansi_dsp_entity.Bid) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: bid.ImpUrls,
		ClickMonitorList:      bid.ClickUrls,
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkUrl:           bid.DeepLink,
		LandingUrl:            bid.Durl,
		H5LandingUrl:          bid.Durl,
	}

	if len(bid.Nurl) > 0 {
		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.Nurl)
	}

	if bid.DurlType == 1 {
		tracking.LandingAction = entity.LandingTypeDownload
		if bid.App != nil {
			tracking.DownloadUrl = bid.App.DownUrl
		}
	}

	if bid.App != nil {
		if len(bid.App.DStartUrls) > 0 {
			tracking.AppDownloadStartedMonitorList = append(tracking.AppDownloadStartedMonitorList, bid.App.DStartUrls...)
		}

		if len(bid.App.DSuccessUrls) > 0 {
			tracking.AppDownloadFinishedMonitorList = append(tracking.AppDownloadFinishedMonitorList, bid.App.DSuccessUrls...)
		}

		if len(bid.App.IStartUrls) > 0 {
			tracking.AppInstallStartMonitorList = append(tracking.AppInstallStartMonitorList, bid.App.IStartUrls...)
		}

		if len(bid.App.ISuccessUrls) > 0 {
			tracking.AppInstalledFinishMonitorList = append(tracking.AppInstalledFinishMonitorList, bid.App.ISuccessUrls...)
		}
	}

	for _, event := range bid.EventTracks {
		switch event.EventType {
		case 1:
			tracking.VideoStartUrlList = event.EventUrls
		case 5, 9:
			tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, event.EventUrls...)
		case 11, 13:
			tracking.DeepLinkFailedMonitorList = append(tracking.DeepLinkFailedMonitorList, event.EventUrls...)
		case 12, 14:
			tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, event.EventUrls...)
		}
	}

	return tracking
}

// Replace macros in monitor urls
func (impl *DianSiDspBroker) replaceMacro(monitor *entity.AdMonitorInfo) {
	if len(monitor.ImpressionMonitorList) > 0 {
		monitor.ImpressionMonitorList = impl.monitorMacro.MacroReplaceList(monitor.ImpressionMonitorList)
	}
	if len(monitor.ClickMonitorList) > 0 {
		monitor.ClickMonitorList = impl.monitorMacro.MacroReplaceList(monitor.ClickMonitorList)
	}
	if len(monitor.DeepLinkMonitorList) > 0 {
		monitor.DeepLinkMonitorList = impl.monitorMacro.MacroReplaceList(monitor.DeepLinkMonitorList)
	}
}
