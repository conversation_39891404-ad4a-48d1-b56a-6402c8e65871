package diansi_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"fmt"
)

/*
{"dsp_slot_ext_attrs":[{"key":"width","label":"广告位宽","type":"input","required":false,"is_number":true,"multiple":false,"comma_separated":false},{"key":"height","label":"广告位高","type":"input","required":false,"is_number":true,"multiple":false,"comma_separated":false},{"key":"app_name","label":"APP名","type":"input","required":false,"is_number":false,"multiple":false,"comma_separated":false},{"key":"pkg_name","label":"包名","type":"input","required":false,"is_number":false,"multiple":false,"comma_separated":false},{"key":"app_version","label":"APP版本","type":"input","required":false,"is_number":false,"multiple":false,"comma_separated":false},{"key":"template_id","label":"创意模板","type":"input","required":false,"is_number":true,"multiple":false,"comma_separated":false,"options":[{"label":"单图文(默认)","value":0},{"label":"单图文视频","value":1}]}]}
*/
type DianSiSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`
	/* 创意模板
	0 单图文(默认)
	1 单视频图文
	*/
	TemplateId int `json:"template_id"`
}

func (info *DianSiSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	info.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	info.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")
	info.TemplateId, _ = dspSlotInfo.ExtraData.GetInt("template_id")

	return nil
}

type DianSiDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*DianSiSlotSlotInfo
}

func NewDianSiDspSlotRegister(dspId utils.ID) *DianSiDspSlotRegister {
	return &DianSiDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*DianSiSlotSlotInfo),
	}
}

func (r *DianSiDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *DianSiDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*DianSiSlotSlotInfo)
	for _, slot := range list {
		dspSlot := &DianSiSlotSlotInfo{}
		if err := dspSlot.Init(slot); err != nil {
			zap.L().Error("[DianSiDspSlotRegister] init slot failed", zap.Error(err), zap.String("slot", fmt.Sprintf("%v", slot.Id)))
			continue
		}

		slotMap[slot.Id] = dspSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *DianSiDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *DianSiDspSlotRegister) GetSlotInfo(slotId utils.ID) *DianSiSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
