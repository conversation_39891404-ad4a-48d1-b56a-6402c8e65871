package xunyu_dsp_broker

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/xunyu_dsp_broker/protos"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	objectpool "gitlab.com/dev/heidegger/library/object_pool"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"go.uber.org/zap"
)

var (
	bidRequestPool = objectpool.NewObjectPool(func() *protos.BidRequest {
		return new(protos.BidRequest)
	})
	bidRequestDevicePool = objectpool.NewObjectPool(func() *protos.BidRequest_Device {
		return new(protos.BidRequest_Device)
	})
	bidRequestDeviceGeoPool = objectpool.NewObjectPool(func() *protos.BidRequest_DeviceGeo {
		return new(protos.BidRequest_DeviceGeo)
	})
	bidRequestImpPool = objectpool.NewObjectPool(func() *protos.BidRequest_Imp {
		return new(protos.BidRequest_Imp)
	})
	bidRequestAppPool = objectpool.NewObjectPool(func() *protos.BidRequest_App {
		return new(protos.BidRequest_App)
	})
	bidRequestUserPool = objectpool.NewObjectPool(func() *protos.BidRequest_User {
		return new(protos.BidRequest_User)
	})
	bidResponsePool = objectpool.NewObjectPool(func() *protos.BidResponse {
		return new(protos.BidResponse)
	})
)

type XunYuDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *XunYuDspSlotRegister
}

func NewXunYuDspBroker(dspId utils.ID) *XunYuDspBroker {
	return &XunYuDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{DspId: dspId},
		slotRegister:  NewXunYuDspSlotRegister(dspId),
	}
}

// TODO: cycly import
// var _ DspBrokerInterface = (*XunYuDspBroker)(nil)

func (xy *XunYuDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	// XXX: XunYu only needs 1 slot
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := xy.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	// Slot id in DSP side
	slotIdStr := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotIdStr) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}
	slotId, err := strconv.ParseInt(slotIdStr, 10, 64)
	if err != nil {
		return nil, err_code.ErrDspSlotInfo.Wrap(err)
	}
	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	// Request info
	req := bidRequestPool.Get()
	defer bidRequestPool.Put(req)
	req.RequestId = trafficData.GetRequestId()
	req.Channel = dspSlot.Channel
	// the maximum precision of `time_utils` is 1 second
	req.RequestTime = time_utils.GetTimeUnixSecond() * 1000
	req.Sign = strings.ToLower(md5_utils.GetMd5String(req.GetRequestId() + strconv.FormatInt(req.GetRequestTime(), 10) + xy.GetEKey()))

	// Device info
	device := bidRequestDevicePool.Get()
	defer bidRequestDevicePool.Put(device)
	req.Device = device
	device.Ua = trafficData.GetUserAgent()
	device.Ip = trafficData.GetRequestIp()
	device.DeviceType = xy.mappingDeviceType(trafficData.GetDeviceType())
	device.Os = xy.mappingOsType(trafficData.GetOsType())
	device.Osv = trafficData.GetOsVersion()
	// TODO: device.Av =
	device.Meid = trafficData.GetImei()

	geo := bidRequestDeviceGeoPool.Get()
	defer bidRequestDeviceGeoPool.Put(geo)
	device.Geo = geo
	geo.Lat = trafficData.GetGeoLatitude()
	geo.Lon = trafficData.GetGeoLongitude()
	device.W = trafficData.GetScreenWidth()
	device.H = trafficData.GetScreenHeight()
	device.Model = trafficData.GetModel()
	device.ConnectionType = xy.mappingConnectionType(trafficData.GetConnectionType())
	device.OperatorType = xy.mappingOperatorType(trafficData.GetOperatorType())
	did, didType := trafficData.GetDeviceIdWithType()
	if dType, err := xy.mappingDeviceIdType(didType); err == nil {
		device.DidType = dType
		device.Did = did
	}

	// slot size
	var width, height int32 = 0, 0
	if len(request.SlotSize) > 0 {
		width = int32(request.SlotSize[0].Width)
		height = int32(request.SlotSize[0].Height)
	} else {
		width = int32(trafficData.GetSlotWidth())
		height = int32(trafficData.GetSlotHeight())
	}

	// Imp info
	imp := bidRequestImpPool.Get()
	defer bidRequestImpPool.Put(imp)
	req.Imp = imp
	imp.MediaType = dspSlot.MediaType
	imp.Tagid = slotId
	imp.W = width
	imp.H = height
	imp.AdType = xy.mappingSlotType(trafficData.GetSlotType())
	bidFloor := candidate.GetBidFloor()
	imp.BidFloor = int64(bidFloor.Price)
	imp.ChargeType = protos.ChargeType_Cpm
	if trafficData.GetSlotType() == entity.SlotTypeFeeds {
		imp.Style = int32(dspSlot.TemplateId)
	}

	// App info
	app := bidRequestAppPool.Get()
	defer bidRequestAppPool.Put(app)
	req.App = app
	app.Id = dspSlot.AppId
	app.Bundle = trafficData.GetAppBundle()
	app.Name = trafficData.GetAppName()
	app.Ver = trafficData.GetAppVersion()

	// User info
	user := bidRequestUserPool.Get()
	defer bidRequestUserPool.Put(user)
	req.User = user
	user.BuyerUid = did
	user.Age = request.UserAge
	user.Gender = xy.mappingUserGender(request.UserGender)
	user.UserTags = request.App.InstalledApp // DSP要求

	// Build
	httpReq, _, err := xy.BuildPbHttpHttpRequest(req)
	if err != nil {
		zap.L().Error("[XunYuDspBroker]BuildPbHttpHttpRequest error", zap.Error(err))
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}
	xy.SampleDspBroadcastProtobufRequest(xy.GetDspId(), trafficData.GetDspSlotId(), candidate, req)
	if request.IsDebug {
		payload, _ := json.Marshal(req)
		zap.L().Info("[XunYuDspBroker]BuildRequest debug", zap.String("request", zap.String("value2", fmt.Sprintf("%v", string(payload)))))
	}
	return httpReq, nil
}

func (xy *XunYuDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	if response.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	// Body will be closed in `BroadcastDspClient`
	data, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse
	}

	resp := bidResponsePool.Get()
	defer bidResponsePool.Put(resp)
	if err := xy.ParsePbHttpHttpResponse(response, data, resp); err != nil {
		zap.L().Debug("[XunYuDspBroker]ParseResponse error", zap.Error(err))
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	broadcastCandidate := broadcastCandidateList[0]

	xy.SampleDspBroadcastProtobufResponse(xy.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, resp)
	if request.IsDebug {
		payload, _ := json.Marshal(resp)
		zap.L().Info("[XunYuDspBroker]ParseResponse debug", zap.String("response", zap.String("value2", fmt.Sprintf("%v", string(payload)))))
	}

	if resp.GetCode() != 200 {
		zap.L().Debug("[XunYuDspBroker]ParseResponse code != 200", zap.String("msg", zap.String("value2", fmt.Sprintf("%v", resp.GetMsg()))))
		return nil, err_code.ErrBroadcastNoBidding
	}
	respData := resp.GetData()
	if respData == nil || respData.GetMoney() <= 0 || len(respData.GetImgUrls()) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	// XunYu only returns 1 ad
	result := make(ad_service.DspAdCandidateList, 0, 1)
	ad := &entity.Ad{
		DspId:         xy.GetDspId(),
		DspSlotId:     broadcastCandidate.GetDspSlotId(),
		DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
		AdMonitorInfo: xy.parseCallbacks(request, respData, broadcastCandidate.GetModifiedTrafficData()),
	}
	var permissionDesc []entity.PermissionDesc
	if len(respData.GetAppPermissions()) > 0 {
		permissionDesc = make([]entity.PermissionDesc, 0, len(respData.GetAppPermissions()))
		descs := respData.GetAppPermissionsDesc()
		for i, perm := range respData.GetAppPermissions() {
			var desc string
			if i < len(descs) {
				desc = descs[i]
			}
			permissionDesc = append(permissionDesc, entity.PermissionDesc{
				PermissionLab:  perm,
				PermissionDesc: desc,
			})
		}
	}
	ad.AppInfo = &entity.AppInfo{
		PackageName:    respData.GetPackageName(),
		AppName:        respData.GetAppName(),
		Icon:           respData.GetAppIcon(),
		AppVersion:     respData.GetAppVersion(),
		Privacy:        respData.GetAppPrivacy(),
		PermissionDesc: permissionDesc,
		Develop:        respData.GetAppDeveloper(),
	}

	creative := xy.parseCreative(respData)
	if creative == nil {
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	candidate := ad_service.NewDspAdCandidateWithPool(ad)
	candidate.SetAdCandidateChargePriceEncoder(xy.chargePriceEncoder)
	candidate.SetBidPrice(uint32(respData.GetMoney()))
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetCreative(creative)
	candidate.SetDspProtocol(xy.GetDspProtocol())
	result = append(result, candidate)

	return result, nil
}

func (xy *XunYuDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return xy.slotRegister
}

func (xy *XunYuDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := xy.PriceManager.GetDspCoder(xy.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), xy.GetIKey(), xy.GetEKey())
	if err != nil {
		zap.L().Error("[XunYuDspBroker]chargePriceEncoder error", zap.Error(err), zap.String("price", fmt.Sprintf("%v", chargePrice)))
		return ""
	}

	return result
}

func (xy *XunYuDspBroker) mappingDeviceType(deviceType entity.DeviceType) protos.DeviceType {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return protos.DeviceType_Mobile
	case entity.DeviceTypePad:
		return protos.DeviceType_Pad
	case entity.DeviceTypeOtt:
		return protos.DeviceType_TV
	default:
		return protos.DeviceType_Other
	}
}

func (xy *XunYuDspBroker) mappingOsType(os entity.OsType) protos.OsType {
	switch os {
	case entity.OsTypeIOS:
		return protos.OsType_IOS
	case entity.OsTypeAndroid:
		return protos.OsType_Android
	case entity.OsTypeWindowsPhone:
		return protos.OsType_WindowsPhone
	default:
		return protos.OsType_OtherOS
	}
}

func (xy *XunYuDspBroker) mappingConnectionType(connectionType entity.ConnectionType) protos.ConnectionType {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return protos.ConnectionType_Ethernet
	case entity.ConnectionTypeWifi:
		return protos.ConnectionType_Wifi
	case entity.ConnectionTypeCellular:
		return protos.ConnectionType_CellularNetwork
	case entity.ConnectionType2G:
		return protos.ConnectionType_CellularNetwork2G
	case entity.ConnectionType3G:
		return protos.ConnectionType_CellularNetwork3G
	case entity.ConnectionType4G:
		return protos.ConnectionType_CellularNetwork4G
	case entity.ConnectionType5G:
		return protos.ConnectionType_CellularNetwork5G
	default:
		return protos.ConnectionType_UnknownConnection
	}
}

func (xy *XunYuDspBroker) mappingOperatorType(operatorType entity.OperatorType) protos.OperatorType {
	switch operatorType {
	case entity.OperatorTypeChinaTelecom:
		return protos.OperatorType_ChinaTelecom
	case entity.OperatorTypeChinaUnicom:
		return protos.OperatorType_ChinaUnicom
	case entity.OperatorTypeChinaMobile, entity.OperatorTypeTietong:
		return protos.OperatorType_ChinaMobile
	case entity.OperatorTypeUnknown:
		return protos.OperatorType_UnknownOperator
	default:
		return protos.OperatorType_OtherOperator
	}
}

func (xy *XunYuDspBroker) mappingDeviceIdType(didType entity.DeviceIdType) (protos.DidType, error) {
	switch didType {
	case entity.DeviceIdTypeRawOaid:
		return protos.DidType_OAID, nil
	case entity.DeviceIdTypeMd5Oaid:
		return protos.DidType_OAID_MD5, nil
	case entity.DeviceIdTypeRawImei:
		return protos.DidType_IMEI, nil
	case entity.DeviceIdTypeMd5Imei:
		return protos.DidType_IMEI_MD5, nil
	case entity.DeviceIdTypeRawIdfa:
		return protos.DidType_IDFA, nil
	case entity.DeviceIdTypeMd5Idfa:
		return protos.DidType_IDFA_MD5, nil
	case entity.DeviceIdTypeRawCaid:
		return protos.DidType_CAID, nil
	default:
		return 0, errors.New("[XunYuDsp]no mapping device id type")
	}
}

func (xy *XunYuDspBroker) mappingSlotType(slotType entity.SlotType) protos.AdType {
	switch slotType {
	case entity.SlotTypeFeeds:
		return protos.AdType_NativeAds
	case entity.SlotTypeBanner:
		return protos.AdType_Banner
	case entity.SlotTypeOpening:
		return protos.AdType_Splash
	case entity.SlotTypePopup:
		return protos.AdType_Interstitial
	case entity.SlotTypeVideo:
		return protos.AdType_NativeVideo
	case entity.SlotTypeRewardVideo:
		return protos.AdType_RewardedVideo
	default:
		return protos.AdType_OtherAd
	}
}

func (xy *XunYuDspBroker) mappingUserGender(gender entity.UserGenderType) protos.Gender {
	switch gender {
	case entity.UserGenderMan:
		return protos.Gender_Male
	case entity.UserGenderWoman:
		return protos.Gender_Female
	default:
		return protos.Gender_All
	}
}

// Replace macros in monitor urls
func (xy *XunYuDspBroker) replaceMacro(request *ad_service.AdRequest, monitor *entity.AdMonitorInfo, trafficData ad_service_entity.TrafficData) {
	/*
		var (
			os           = "__OS__"
			model        = url.QueryEscape(trafficData.GetModel())
			ip           = url.QueryEscape(trafficData.GetRequestIp())
			ua           = url.QueryEscape(trafficData.GetUserAgent())
			csite        = "__CSITE__"
			ts           = strconv.FormatInt(time_utils.GetTimeUnixSecond()*1000, 10)
			tagId        = "__TAGID__"
			source       = "__SOURCE__"
			idfa         = url.QueryEscape(trafficData.GetIdfa())
			idfaMd5      = url.QueryEscape(trafficData.GetMd5Idfa())
			caid         = "__CAID__"
			caidMd5      = "__CAID_MD5__"
			oaid         = url.QueryEscape(trafficData.GetOaid())
			oaidMd5      = url.QueryEscape(trafficData.GetMd5Oaid())
			imei         = url.QueryEscape(trafficData.GetImei())
			imeiMd5      = url.QueryEscape(trafficData.GetMd5Imei())
			androidId    = url.QueryEscape(trafficData.GetAndroidId())
			androidIdMd5 = url.QueryEscape(trafficData.GetMd5AndroidId())
			app          = url.QueryEscape(trafficData.GetAppName())
			ap           = "0"
			ar           = "0"
		)
		dspSlot := xy.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
		if dspSlot != nil {
			tagId = dspSlot.GetDspSlotIdByTrafficContext(trafficData)
			source = url.QueryEscape(dspSlot.MediaType)
		}
		if trafficData.GetOsType() != entity.OsTypeUnknown {
			switch trafficData.GetOsType() {
			case entity.OsTypeAndroid:
				os = "0"
			case entity.OsTypeIOS:
				os = "1"
			case entity.OsTypeWindowsPhone:
				os = "2"
			default:
				os = "3"
			}
		}
		switch trafficData.GetSlotType() {
		case entity.SlotTypeFeeds:
			csite = "1"
		case entity.SlotTypeBanner:
			csite = "2"
		case entity.SlotTypeOpening:
			csite = "3"
		case entity.SlotTypePopup:
			csite = "4"
		case entity.SlotTypeVideo:
			csite = "5"
		case entity.SlotTypeRewardVideo:
			csite = "6"
		default:
			csite = "9"
		}

		replacer := strings.NewReplacer(
			"__OS__", os,
			"__MODEL__", model,
			"__IP__", ip,
			"__UA__", ua,
			"__CSITE__", csite,
			"__PRICE__", "__DSPWPRICE__",
			"__TS__", ts,
			"__TAGID__", tagId,
			"__SOURCE__", source,
			"__IDFA__", idfa,
			"__IDFA_MD5__", idfaMd5,
			"__CAID__", caid,
			"__CAID_MD5__", caidMd5,
			"__OAID__", oaid,
			"__OAID_MD5__", oaidMd5,
			"__IMEI__", imei,
			"__IMEI_MD5__", imeiMd5,
			"__ANDROIDID__", androidId,
			"__ANDROIDID_MD5__", androidIdMd5,
			"__APP__", app,
			"__AUTOPLAY__", ap,
			"__AUTOREFRESH__", ar,
		)
	*/

	replacer := strings.NewReplacer(
		"__PRICE__", "__DSPWPRICE__",
	)

	if len(monitor.ImpressionMonitorList) > 0 {
		for idx, url := range monitor.ImpressionMonitorList {
			monitor.ImpressionMonitorList[idx] = replacer.Replace(url)
		}
	}
	if len(monitor.ClickMonitorList) > 0 {
		for idx, url := range monitor.ClickMonitorList {
			monitor.ClickMonitorList[idx] = replacer.Replace(url)
		}
	}
	if len(monitor.DeepLinkMonitorList) > 0 {
		for idx, url := range monitor.DeepLinkMonitorList {
			monitor.DeepLinkMonitorList[idx] = replacer.Replace(url)
		}
	}
}

func (xy *XunYuDspBroker) parseCallbacks(request *ad_service.AdRequest, data *protos.BidResponse_BidResponseData, trafficData ad_service_entity.TrafficData) *entity.AdMonitorInfo {
	info := &entity.AdMonitorInfo{
		LandingUrl:                data.GetCurl(),
		DeepLinkUrl:               data.GetDeeplinkUrl(),
		ImpressionMonitorList:     data.GetCmurl(),
		ClickMonitorList:          data.GetMurl(),
		DeepLinkMonitorList:       data.GetDeeplinkMurl(),
		DeepLinkFailedMonitorList: data.GetDeeplinkFailure(),
		DownloadUrl:               data.GetAppstoreLink(),
		H5LandingUrl:              data.GetCurl(),
		LandingAction:             entity.LandingTypeUnknown,
	}

	switch data.InteractionType {
	case protos.InteractionType_Deeplink:
		info.LandingAction = entity.LandingTypeDeepLink
	case protos.InteractionType_Download:
		info.LandingAction = entity.LandingTypeDownload
	default:
		info.LandingAction = entity.LandingTypeInWebView
	}

	xy.replaceMacro(request, info, trafficData)
	return info
}

func (xy *XunYuDspBroker) parseCreative(data *protos.BidResponse_BidResponseData) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        md5_utils.GetMd5String(data.ImgUrls[0]),
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	switch data.MaterialType {
	case protos.MaterialType_Video:
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          data.ImgUrls[0],
			Height:       data.GetImgh(),
			Width:        data.GetImgw(),
			Duration:     float64(data.GetDuration()),
		}
		creative.MaterialList = append(creative.MaterialList, material)

		if data.GetVideoImgUrl() != "" {
			materialImg := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          data.GetVideoImgUrl(),
				Height:       data.GetImgh(),
				Width:        data.GetImgw(),
			}
			creative.MaterialList = append(creative.MaterialList, materialImg)
		}
	default:
		for _, url := range data.GetImgUrls() {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          url,
				Width:        data.GetImgw(),
				Height:       data.GetImgh(),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if len(data.GetTitle()) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         data.GetTitle(),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}
	if len(data.GetDesc()) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         data.GetDesc(),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}
