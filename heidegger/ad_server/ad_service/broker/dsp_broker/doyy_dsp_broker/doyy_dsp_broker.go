package doyy_dsp_broker

import (
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/doyy_dsp_broker/doyy_dsp_proto"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/hash_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"fmt"
)

type DoyyDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *DoyySlotRegister
	log          *zap.Logger
	macroInfo    macro_builder.MonitorMacroInfo
}

func NewDoyyDspBroker(dspId utils.ID) *DoyyDspBroker {
	return &DoyyDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		slotRegister: NewDoyySlotRegister(dspId),
		log:          zap.L().With(zap.String("broker", "DoyyDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroClickDownX: "__CLICK_DOWN_X__",
			MacroClickDownY: "__CLICK_DOWN_Y__",
			MacroClickUpX:   "__CLICK_UP_X__",
			MacroClickUpY:   "__CLICK_UP_Y__",
		},
	}
}

func (a *DoyyDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		a.log.WithField("candidates", len(candidateList)).Error("too many candidates")
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := a.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	//dspSlotId
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	var impid int32 = 0
	imp := &doyy_dsp_proto.Request_Impression{
		Id:       &impid,
		Pid:      slotId,
		Width:    int32(trafficData.GetSlotWidth()),
		Height:   int32(trafficData.GetSlotHeight()),
		BidFloor: candidate.GetBidFloor().Price,
	}

	if dspSlot.Width > 0 && dspSlot.Height > 0 {
		imp.Width = int32(dspSlot.Width)
		imp.Height = int32(dspSlot.Height)
	}

	var apid uint64
	if trafficData.GetAppBundle() != "" {
		apid = hash_utils.XXHash(trafficData.GetAppBundle())
	}

	var operator = mappingCarrier(trafficData.GetOperatorType())
	var network = mappingConnectionType(trafficData.GetConnectionType())
	var orientation = mappingOrientation(trafficData.GetScreenOrientation())
	var countryCode = "CN"
	var language = "zh"

	bidRequest := &doyy_dsp_proto.Request{
		Id:  request.GetRequestId(),
		Imp: []*doyy_dsp_proto.Request_Impression{imp},
		App: &doyy_dsp_proto.Request_App{
			AppId:   type_convert.GetAssertString(apid),
			Name:    trafficData.GetAppName(),
			Bundle:  trafficData.GetAppBundle(),
			Version: trafficData.GetAppVersion(),
		},
		Device: &doyy_dsp_proto.Request_Device{
			Ip: trafficData.GetRequestIp(),
			Geo: &doyy_dsp_proto.Request_Device_Geo{
				Lat: trafficData.GetGeoLatitude(),
				Lon: trafficData.GetGeoLongitude(),
			},
			UserAgent:   trafficData.GetUserAgent(),
			DeviceType:  mappingDeviceType(trafficData.GetDeviceType()),
			Make:        trafficData.GetBrand(),
			Model:       trafficData.GetModel(),
			Os:          mappingDeviceOs(trafficData.GetOsType()),
			Osv:         trafficData.GetOsVersion(),
			Operator:    &operator,
			Network:     &network,
			Idfa:        trafficData.GetIdfa(),
			Idfamd5:     trafficData.GetMd5Idfa(),
			Idfv:        trafficData.GetIdfv(),
			Imei:        trafficData.GetImei(),
			ImeiMd5:     trafficData.GetMd5Imei(),
			Oaid:        trafficData.GetOaid(),
			Oaidmd5:     trafficData.GetMd5Oaid(),
			Aidplain:    trafficData.GetAndroidId(),
			Aidmd5:      trafficData.GetMd5AndroidId(),
			Orientation: &orientation,
			Height:      trafficData.GetScreenHeight(),
			Width:       trafficData.GetScreenWidth(),
			Ppi:         request.Device.PPI,
			Mac:         trafficData.GetMac(),
			Hmsv:        request.Device.VercodeHms,
			Mosn:        trafficData.GetRomName(),
			Mosv:        trafficData.GetRomVersion(),
			Mappv:       request.Device.VercodeAg,
			BootMark:    trafficData.GetBootMark(),
			UpdateMark:  trafficData.GetUpdateMark(),
			BirthTime:   trafficData.GetDeviceInitTime(),
			BootTime:    trafficData.GetDeviceStartupTime(),
			UpdateTime:  trafficData.GetDeviceUpgradeTime(),
			SystemMem:   request.Device.SystemTotalMem,
			SystemDisk:  request.Device.SystemTotalDisk,
			CountryCode: &countryCode,
			Language:    &language,
			Installs:    request.App.InstalledApp,
			Caids:       make([]*doyy_dsp_proto.Request_Device_Caid, 0),
			Aaid:        request.Device.Aaid,
			Paid:        request.Device.Paid,
			Openudid:    trafficData.GetOpenUdid(),
			PhoneName:   request.Device.DeviceName,
		},
		User: &doyy_dsp_proto.Request_User{},
	}
	var man int32 = 1
	var won int32 = 2

	switch request.UserGender {
	case entity.UserGenderMan:
		bidRequest.User.Gender = &man
	case entity.UserGenderWoman:
		bidRequest.User.Gender = &won
	}

	if trafficData.GetOsType() == entity.OsTypeIOS && request.Device.HardwareMachineCode != "" {
		request.Device.Model = request.Device.HardwareMachineCode
	}

	if len(trafficData.GetCaid()) != 0 {
		bidRequest.Device.Caids = append(bidRequest.Device.Caids, &doyy_dsp_proto.Request_Device_Caid{
			Id:      device_utils.GetCaidRaw(trafficData.GetCaid()),
			Version: device_utils.GetCaidVersion(trafficData.GetCaid()),
		})
	}

	if len(request.Device.Caids) > 0 {
		for _, caidItem := range request.Device.Caids {
			if caidItem == trafficData.GetCaid() {
				continue
			}
			caid := &doyy_dsp_proto.Request_Device_Caid{
				Id:      device_utils.GetCaidRaw(caidItem),
				Version: device_utils.GetCaidVersion(caidItem),
			}
			bidRequest.Device.Caids = append(bidRequest.Device.Caids, caid)
		}
	}

	if request.Device.IsIp6 {
		bidRequest.Device.Ipv6 = trafficData.GetRequestIp()
		bidRequest.Device.Ip = ""
	}

	if len(trafficData.GetWebviewUA()) > 0 {
		bidRequest.Device.UserAgent = trafficData.GetWebviewUA()
	}
	if len(dspSlot.APPID) > 0 {
		bidRequest.App.AppId = dspSlot.APPID
	}

	if len(dspSlot.PkgName) > 0 {
		bidRequest.App.Bundle = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidRequest.App.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidRequest.App.Version = dspSlot.AppVersion
	}

	if request.IsDebug {
		payload, _ := sonic.Marshal(bidRequest)
		a.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}

	httpReq, _, err := a.BuildPbHttpHttpRequest(bidRequest)
	if err != nil {
		a.log.WithError(err).Error("BuildPbHttpHttpRequest error")
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}
	httpReq.Header.Set("Content-Type", "application/x-protobuf")
	a.SampleDspBroadcastProtobufRequest(a.DspId, trafficData.GetDspSlotId(), candidate, bidRequest)
	return httpReq, nil
}

func mappingDeviceOs(osType entity.OsType) string {
	switch osType {
	case entity.OsTypeIOS:
		return "iOS"
	case entity.OsTypeAndroid:
		return "Android"
	default:
		return "Others"
	}
}

func mappingCarrier(carrier entity.OperatorType) int32 {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 3
	case entity.OperatorTypeChinaTelecom:
		return 2
	default:
		return 0
	}
}
func mappingOrientation(orientation entity.ScreenOrientationType) int32 {
	switch orientation {
	case entity.ScreenOrientationTypePortrait:
		return 1
	case entity.ScreenOrientationTypeLandscape:
		return 2
	default:
		return 0
	}
}
func mappingConnectionType(connectionType entity.ConnectionType) int32 {
	switch connectionType {
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func mappingDeviceType(dy entity.DeviceType) int32 {
	switch dy {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypePc:
		return 3
	case entity.DeviceTypeOtt:
		return 4
	default:
		return 1
	}
}

func (a *DoyyDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if response.StatusCode != 200 {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(response.StatusCode), type_convert.GetAssertString(response.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse
	}

	resp := &doyy_dsp_proto.Response{}
	err = a.ParsePbHttpHttpResponse(response, data, resp)
	if err != nil {
		a.log.WithError(err).WithField("response", response).Error("ParseSonicJsonHttpResponse error")
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		return nil, err_code.ErrBrokerParseError.Wrap(err)
	}

	a.SampleDspBroadcastProtobufResponse(a.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, resp)
	if request.IsDebug {
		resBody, _ := sonic.Marshal(resp)
		zap.L().Info("DoyyDspBroker.ParseResponse, body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}

	a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.Code), type_convert.GetAssertString(resp.Errmsg))

	if type_convert.GetAssertInt32(resp.Code) != 0 || len(resp.Seat) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0, 1)
	for _, bid := range resp.Seat[0].Ad {
		ad := &entity.Ad{
			DspId:      a.DspId,
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
			AppInfo:    &entity.AppInfo{},
		}

		if bid.AppPackage != "" {
			ad.AppInfo.PackageName = bid.AppPackage
			ad.AppInfo.AppName = bid.AppName
			ad.AppInfo.AppVersion = bid.AppVersion
			ad.AppInfo.Develop = bid.AppDeveloper
			ad.AppInfo.AppDesc = bid.AppDescription
			ad.AppInfo.AppDescURL = bid.AppDescriptionUrl
			ad.AppInfo.Icon = bid.AppIcon
			ad.AppInfo.Privacy = bid.AppPrivacyUrl
			ad.AppInfo.Permission = bid.AppPermissionUrl
			ad.AppInfo.PackageSize = int(bid.AppSize)
		}

		if bid.Wxoid != "" && bid.Wxopath != "" {
			ad.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   bid.Wxoid,
				ProgramPath: bid.Wxopath,
			}
		}

		adMonitorInfo, creative := a.parseCallbacksAndCreative(bid)
		if request.Device.GetOsType() == entity.OsTypeIOS && len(bid.Ulk) > 0 {
			adMonitorInfo.DeepLinkUrl = bid.Ulk
		}

		ad.AdMonitorInfo = adMonitorInfo
		if creative == nil {
			return nil, err_code.ErrCreativeNotFound
		}

		candidate := ad_service.NewDspAdCandidateWithPool(ad)
		candidate.SetAdCandidateChargePriceEncoder(a.chargePriceEncoder)
		candidate.SetBidPrice(uint32(bid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(creative)
		candidate.SetDspAdID(bid.CreativeId)
		candidate.SetDspProtocol(a.DspProtocol)
		result = append(result, candidate)
	}

	return result, nil
}

func (a *DoyyDspBroker) chargePriceEncoder(chargePrice uint32) string {
	return ""
	//result, err := a.PriceManager.GetDspCoder(a.DspProtocol).EncodeWithKey(uint64(chargePrice), a.GetIKey(), a.GetEKey())
	//if err != nil {
	//	a.log.WithError(err).WithField("price", chargePrice).Errorf("chargePriceEncoder error")
	//	return ""
	//}

	//return result
}

func (a *DoyyDspBroker) parseCallbacksAndCreative(data *doyy_dsp_proto.Response_Seat_Ad) (*entity.AdMonitorInfo, *entity.Creative) {
	info := &entity.AdMonitorInfo{
		LandingUrl:  data.ClickUrl,
		DeepLinkUrl: data.DeeplinkUrl,
		//ImpressionMonitorList: data.ExposeTrackingUrl,
		//ClickMonitorList: data.ClickTrackingUrl,
		LandingAction: mappingLandingType(data),
	}

	if len(data.Nurl) > 0 {
		info.ImpressionMonitorList = append(info.ImpressionMonitorList, data.Nurl...)
	}

	for _, expurl := range data.ExposeTrackingUrl {
		timenow := time.Now()
		expurl = strings.Replace(expurl, "__TS__", type_convert.GetAssertString(timenow.Unix()), -1)
		info.ImpressionMonitorList = append(info.ImpressionMonitorList, strings.Replace(expurl, "__TMS__", type_convert.GetAssertString(timenow.UnixMilli()), -1))
	}

	for _, clkurl := range data.ClickTrackingUrl {
		timenow := time.Now()
		clkurl = strings.Replace(clkurl, "__TS__", type_convert.GetAssertString(timenow.Unix()), -1)
		info.ClickMonitorList = append(info.ClickMonitorList, strings.Replace(clkurl, "__TMS__", type_convert.GetAssertString(timenow.UnixMilli()), -1))
	}

	if len(data.DownloadStart) > 0 {
		info.AppDownloadStartedMonitorList = append(info.AppDownloadStartedMonitorList, data.DownloadStart...)
	}
	if len(data.DownloadEnd) > 0 {
		info.AppDownloadFinishedMonitorList = append(info.AppDownloadFinishedMonitorList, data.DownloadEnd...)
	}
	if len(data.InstallStart) > 0 {
		info.AppInstallStartMonitorList = append(info.AppInstallStartMonitorList, data.InstallStart...)
	}
	if len(data.InstallEnd) > 0 {
		info.AppInstalledFinishMonitorList = append(info.AppInstalledFinishMonitorList, data.InstallEnd...)
	}
	if len(data.DpTryUrl) > 0 {
		info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, data.DpTryUrl...)
	}
	if len(data.DpSuccUrl) > 0 {
		info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, data.DpSuccUrl...)
	}
	if len(data.DpFailUrl) > 0 {
		info.DeepLinkFailedMonitorList = append(info.DeepLinkFailedMonitorList, data.DpFailUrl...)
	}
	if len(data.ActivateApp) > 0 {
		info.AppOpenMonitorList = data.ActivateApp
	}

	//assets
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        data.CreativeId,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if len(data.Images) > 0 {
		for _, img := range data.Images {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img.Url,
				Height:       img.H,
				Width:        img.W,
			})
		}
	}

	vid := data.Video
	if vid != nil {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          vid.Url,
			Height:       vid.H,
			Width:        vid.W,
			Duration:     float64(vid.Duration / 1000),
		})
		//if len(vid.VMPStart) > 0 {
		//	info.VideoStartUrlList = append(info.VideoStartUrlList, vid.VMPStart...)
		//}
		//if len(vid.VMPSucc) > 0 {
		//	info.VideoCloseUrlList = append(info.VideoCloseUrlList, vid.VMPSucc...)
		//}
	}

	title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: data.Title}
	if len(data.Title) == 0 {
		title.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, title)

	desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: data.Description}
	if len(data.Description) == 0 {
		desc.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, desc)

	//宏替换
	info.ImpressionMonitorList = macro_builder.MacroReplaceList(info.ImpressionMonitorList, a.macroInfo)
	info.ClickMonitorList = macro_builder.MacroReplaceList(info.ClickMonitorList, a.macroInfo)

	return info, creative
}

// 映射落地页类型
func mappingLandingType(bid *doyy_dsp_proto.Response_Seat_Ad) entity.LandingType {
	switch bid.LandingType {
	case 1, 4:
		return entity.LandingTypeDeepLink
	case 2:
		return entity.LandingTypeDownload
	case 3:
		return entity.LandingTypeWeChatProgram
	default:
		return entity.LandingTypeInWebView
	}
}

//func getCodeMsg(code string) string {
//	codeTemp := type_convert.GetAssertInt32(code)
//	switch codeTemp {
//	case 0:
//		return "响应成功"
//	case 204:
//		return "不参与竞价"
//	case 1000:
//		return "设备参数缺失"
//	case 1001:
//		return "服务器繁忙，稍后重试"
//	case 1002:
//		return "UA参数不合法"
//	case 1003:
//		return "IP或IPV6参数异常"
//	case 1004:
//		return "厂商、品牌、型号参数异常"
//	case 1005:
//		return "操作系统与APP不匹配"
//	case 1006:
//		return "请求Imp尺寸异常"
//	case 1007:
//		return "无适配素材"
//	case 9996:
//		return "其它参数异常"
//	case 9997:
//		return "应用停用"
//	case 9998:
//		return "广告位停用"
//	case 9999:
//		return "未知异常"
//	default:
//		return "其他错误"
//	}
//}

func (a *DoyyDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return a.slotRegister
}
