package didi_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"strings"
)

type DiDiSlotSlotInfo struct {
	*entity.DspSlotInfo
	TemplateIds []string `json:"template_id"`
	Height      int      `json:"height"`
	Width       int      `json:"width"`
}

func (info *DiDiSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	templateId, err := dspSlotInfo.ExtraData.GetString("template_id")
	if err != nil {
		return fmt.Errorf("get template_id from extra_data failed, err: %v", err)
	}
	info.TemplateIds = strings.Split(templateId, ",")

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	//if err != nil {
	//	return fmt.Errorf("get height from extra_data failed, err: %v", err)
	//}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	//if err != nil {
	//	return fmt.Errorf("get width from extra_data failed, err: %v", err)
	//}

	return nil
}

type DiDiDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*DiDiSlotSlotInfo
}

func NewDiDiDspSlotRegister(dspId utils.ID) *DiDiDspSlotRegister {
	return &DiDiDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*DiDiSlotSlotInfo),
	}
}

func (r *DiDiDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *DiDiDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*DiDiSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &DiDiSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[DiDiDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *DiDiDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *DiDiDspSlotRegister) GetSlotInfo(slotId utils.ID) *DiDiSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
