package didi_broker

import (
	"encoding/json"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/didi_dsp_broker/didi_proo"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"io"
	"net/http"
	"strings"
	"fmt"
)

type DiDiDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *DiDiDspSlotRegister

	dspId         utils.ID
	MacroWinPrice string
}

func NewDiDiDspBroker(dspId utils.ID) *DiDiDspBroker {
	return &DiDiDspBroker{
		slotRegister:  NewDiDiDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "__WIN_PRICE__",
	}
}

func (impl *DiDiDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *DiDiDspBroker) GetDspId() utils.ID {
	return impl.dspId
}

func (impl *DiDiDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("DiDiDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	if request.IsDebug {
		reqBody, _ := json.Marshal(request)
		zap.L().Info("DiDiDspBroker.EncodeRequest Ctx req", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidFloor := candidate.GetBidFloor()

	ddRequest := &didi_proto.BidRequest{
		Id: trafficData.GetRequestId(),
		Impression: &didi_proto.Impression{
			Templates:  dspSlot.TemplateIds,
			SlotId:     slotId,
			FloorPrice: int32(bidFloor.Price),
		},
		App: &didi_proto.App{
			Id:      "",
			Name:    trafficData.GetAppName(),
			Bundle:  trafficData.GetAppBundle(),
			Version: trafficData.GetAppVersion(),
		},
		Device: impl.encodeJdDeviceRequest(request, trafficData),
	}

	if trafficData.GetOsType() == entity.OsTypeIOS {
		if len(ddRequest.Device.Idfa) == 0 && len(ddRequest.Device.Imei) == 0 {
			return nil, err_code.ErrInvalidDeviceId
		}
	} else {
		if len(ddRequest.Device.Oaid) == 0 && len(ddRequest.Device.Imei) == 0 {
			return nil, err_code.ErrInvalidDeviceId
		}
	}

	httpRequest, _, err := impl.BuildPbHttpHttpRequest(ddRequest)
	if err != nil {
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	impl.SampleDspBroadcastProtobufRequest(impl.dspId, dspSlot.Id, candidate, ddRequest)

	if request.IsDebug {
		zap.L().Info("DiDiDspBroker url:, send:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", impl.GetBidUrl())))), ddRequest.String())
	}
	return httpRequest, err

}

func (impl *DiDiDspBroker) encodeJdDeviceRequest(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) *didi_proto.Device {
	device := &didi_proto.Device{
		DeviceType: impl.mappingDeviceType(trafficData.GetDeviceType()),
		Ua:         trafficData.GetUserAgent(),
		Geo: &didi_proto.Geo{
			Longitude: float32(request.Device.Lon),
			Latitude:  float32(request.Device.Lat),
		},
		Ip:             trafficData.GetRequestIp(),
		Os:             impl.mappingOs(trafficData.GetOsType()),
		OsVersion:      trafficData.GetOsVersion(),
		Brand:          0,
		Model:          trafficData.GetModel(),
		ConnectionType: impl.mappingConnectionType(trafficData.GetConnectionType()),
		Carrier:        impl.mappingCarrier(trafficData.GetOperatorType()),
		Idfa:           trafficData.GetIdfa(),
		Imei:           trafficData.GetImei(),
		Oaid:           trafficData.GetOaid(),
	}

	if trafficData.GetOsType() == entity.OsTypeIOS {
		device.Brand = didi_proto.Brand_brand_apple
	} else {
		device.Brand = impl.mappingBrand(request.Device.UserAgentParser.BrandId)
	}

	return device

}

func (impl *DiDiDspBroker) mappingOs(os entity.OsType) didi_proto.Os {
	switch os {
	case entity.OsTypeIOS:
		return didi_proto.Os_os_ios
	case entity.OsTypeAndroid:
		return didi_proto.Os_os_android
	default:
		return didi_proto.Os_os_unknown
	}
}

func (impl *DiDiDspBroker) mappingDeviceType(os entity.DeviceType) didi_proto.DeviceType {
	switch os {
	case entity.DeviceTypePad:
		return didi_proto.DeviceType_device_type_tablet
	case entity.DeviceTypeMobile:
		return didi_proto.DeviceType_device_type_phone
	default:
		return didi_proto.DeviceType_device_type_unknown
	}
}

func (impl *DiDiDspBroker) mappingConnectionType(connectionType entity.ConnectionType) didi_proto.ConnectionType {
	switch connectionType {
	case entity.ConnectionTypeWifi:
		return didi_proto.ConnectionType_connection_type_wifi
	case entity.ConnectionTypeUnknown:
		return didi_proto.ConnectionType_connection_type_4g
	case entity.ConnectionType2G:
		return didi_proto.ConnectionType_connection_type_2g
	case entity.ConnectionType3G:
		return didi_proto.ConnectionType_connection_type_3g
	case entity.ConnectionType4G:
		return didi_proto.ConnectionType_connection_type_4g
	case entity.ConnectionType5G:
		return didi_proto.ConnectionType_connection_type_5g
	default:
		return didi_proto.ConnectionType_connection_type_unknown
	}
}

func (impl *DiDiDspBroker) mappingCarrier(carrier entity.OperatorType) didi_proto.Carrier {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return didi_proto.Carrier_carrier_china_mobile
	case entity.OperatorTypeChinaTelecom, entity.OperatorTypeTietong:
		return didi_proto.Carrier_carrier_china_telecom
	case entity.OperatorTypeChinaUnicom:
		return didi_proto.Carrier_carrier_china_unicom
	default:
		return didi_proto.Carrier_carrier_unknown
	}
}

func (impl *DiDiDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("DiDiDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBroadcastResponseReadBodyFail.Wrap(err)
	}

	ddResp := &didi_proto.BidResponse{}
	if err := impl.ParsePbHttpHttpResponse(resp, body, ddResp); err != nil {
		zap.L().Error("DiDiDspBroker.DecodeResponse Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastProtobufResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, ddResp)

	if request.IsDebug {
		resBody, _ := json.Marshal(ddResp)
		zap.L().Info("DiDiDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}

	if ddResp.Bid == nil {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)
	candidateAd := &entity.Ad{
		DspId:      impl.GetDspId(),
		DspSlotId:  broadcastCandidate.GetDspSlotId(),
		DspSlotKey: broadcastCandidate.GetDspSlotKey(),
	}

	candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, ddResp.Bid)
	candidateCreative := impl.ParseCreativeData(ddResp.Bid)

	candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
	candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
	candidate.SetBidPrice(uint32(ddResp.Bid.Price))
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetDspProtocol(impl.GetDspProtocol())
	candidate.SetCreative(candidateCreative)
	result = append(result, candidate)

	return result, nil

}

func (impl *DiDiDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *DiDiDspBroker) ParseCreativeData(bid *didi_proto.Bid) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.CreativeId,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if len(bid.Creative.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         bid.Creative.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Creative.Content) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         bid.Creative.Content,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Creative.VideoUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          bid.Creative.VideoUrl,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Creative.ImageUrl) > 0 {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          bid.Creative.ImageUrl,
		}
		creative.MaterialList = append(creative.MaterialList, materialImg)
	}

	if len(bid.Creative.Img1Url) > 0 {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          bid.Creative.Img1Url,
		}
		creative.MaterialList = append(creative.MaterialList, materialImg)
	}

	if len(bid.Creative.Img2Url) > 0 {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          bid.Creative.Img2Url,
		}
		creative.MaterialList = append(creative.MaterialList, materialImg)
	}

	if len(bid.Creative.Img3Url) > 0 {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          bid.Creative.Img3Url,
		}
		creative.MaterialList = append(creative.MaterialList, materialImg)
	}

	return creative

}

func (impl *DiDiDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *didi_proto.Bid) *entity.AdMonitorInfo {
	if bid.Creative == nil {
		zap.L().Warn("DiDiDspBroker.ParseTrackingData bid creative is nil")
		return nil
	}

	tracking := &entity.AdMonitorInfo{
		LandingUrl:            bid.Creative.LandingUrl,
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      bid.ClickTrackingUrl,
		LandingAction:         entity.LandingTypeInWebView,
	}

	for _, impTrack := range bid.ImpressionTrackingUrl {
		if strings.Contains(impTrack, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impTrack, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impTrack)
		}
	}

	if len(bid.WinUrl) > 0 {
		if strings.Contains(bid.WinUrl, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(bid.WinUrl, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.WinUrl)
		}
	}

	tracking.DeepLinkUrl = bid.Creative.DeeplinkUrl

	if len(bid.Creative.DownloadUrl) > 0 {
		tracking.LandingUrl = bid.Creative.DownloadUrl
		tracking.LandingAction = entity.LandingTypeDownload
	}

	if request.Device.GetOsType() == entity.OsTypeIOS && len(bid.Creative.UniversalUrl) > 0 {
		tracking.DeepLinkUrl = bid.Creative.UniversalUrl
		tracking.LandingUrl = bid.Creative.UniversalUrl
	}

	return tracking

}

func (impl *DiDiDspBroker) mappingBrand(uaBrandId uint32) didi_proto.Brand {
	switch uaBrandId {
	case 5031, 5033, 9063:
		return didi_proto.Brand_brand_huawei
	case 5032, 5034, 9064:
		return didi_proto.Brand_brand_honor
	case 5060:
		return didi_proto.Brand_brand_mi
	case 5061:
		return didi_proto.Brand_brand_redmi
	case 6040:
		return didi_proto.Brand_brand_oppo
	case 6050:
		return didi_proto.Brand_brand_vivo
	case 3080:
		return didi_proto.Brand_brand_samsung
	case 6090:
		return didi_proto.Brand_brand_meizu
		//return didi_proto.Brand_brand_smartisan
	case 5050:
		return didi_proto.Brand_brand_zte
	case 8020:
		return didi_proto.Brand_brand_nubia
	case 3050:
		return didi_proto.Brand_brand_htc
	case 4050, 9050:
		return didi_proto.Brand_brand_sony
		//return didi_proto.Brand_brand_lg
	case 5045:
		return didi_proto.Brand_brand_lenovo
	case 7058:
		return didi_proto.Brand_brand_motorola
	case 4010:
		return didi_proto.Brand_brand_nokia
	//return didi_proto.Brand_brand_asus
	case 7099:
		return didi_proto.Brand_brand_philips
	case 8060:
		return didi_proto.Brand_brand_oneplus
	case 9021:
		return didi_proto.Brand_brand_realme
	case 8100:
		return didi_proto.Brand_brand_blackshark
	//return didi_proto.Brand_brand_rog
	//return didi_proto.Brand_brand_iqoo
	case 6070:
		return didi_proto.Brand_brand_ktouch
	case 6000:
		return didi_proto.Brand_brand_doov
	case 9008:
		return didi_proto.Brand_brand_xiaolajiao
	default:
		return didi_proto.Brand_brand_unknown
	}
}
