package csj_dsp_broker

import (
	"encoding/json"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/csj_dsp_broker/csj_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"strings"
	"fmt"
)

type CSJDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *CSJDspSlotRegister

	dspId         utils.ID
	MacroWinPrice string
}

func NewCSJDspBroker(dspId utils.ID) *CSJDspBroker {
	return &CSJDspBroker{
		slotRegister:  NewCSJDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "{AUCTION_PRICE}",
	}
}

func (impl *CSJDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *CSJDspBroker) GetDspId() utils.ID {
	return impl.dspId
}

func (impl *CSJDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("CSJDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("CSJDspBroker GetSlotInfo nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		zap.L().Error("CSJDspBroker GetDspSlotIdByTrafficContext slot id errors", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))

		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	requestId := request.GetRequestId()

	if len(requestId) > 32 {
		requestId = md5_utils.GetMd5String(requestId)
	}

	csjRequest := &csj_proto.BidRequest{
		RequestId:  requestId,
		ApiVersion: "1.4.8",
		Uid:        "",
		User: &csj_proto.BidRequest_User{
			Gender:   0,
			Age:      uint32(request.UserAge),
			PhoneNub: "",
			Keywords: "",
			AppList:  nil,
			Data:     nil,
		},
		SourceType: "app",
		Wap:        nil,
		App: &csj_proto.BidRequest_App{
			Appid:       dspSlot.AppId,
			Name:        trafficData.GetAppName(),
			PackageName: trafficData.GetAppBundle(),
			AppCategory: 0,
			Version:     trafficData.GetAppVersion(),
			Geo: &csj_proto.BidRequest_Geo{
				Latitude:  float32(trafficData.GetGeoLatitude()),
				Longitude: float32(trafficData.GetGeoLongitude()),
			},
			IsPaidApp: false,
			ApkSign:   "",
			ItunesId:  "",
		},
		Device: &csj_proto.BidRequest_Device{
			Did:              trafficData.GetIdfa(),
			Imei:             trafficData.GetImei(),
			Type:             impl.mappingDeviceType(trafficData.GetDeviceType()),
			Os:               impl.mappingOs(trafficData.GetOsType()),
			OsVersion:        trafficData.GetOsVersion(),
			Vendor:           trafficData.GetBrand(),
			Model:            trafficData.GetModel(),
			Language:         "zh-Hans-CN",
			ConnType:         impl.mappingConnectionType(trafficData.GetConnectionType()),
			Mac:              trafficData.GetMac(),
			ScreenWidth:      uint32(trafficData.GetScreenWidth()),
			ScreenHeight:     uint32(trafficData.GetScreenHeight()),
			AndroidId:        trafficData.GetAndroidId(),
			Uuid:             "",
			OpenUdid:         trafficData.GetOpenUdid(),
			Ssid:             "",
			WifiMac:          "",
			PhoneName:        "",
			Dsid:             "",
			PowerOnTime:      "",
			Imsi:             request.Device.Imsi,
			RomVersion:       request.Device.RomVersion,
			SysCompilingTime: "",
			Orientation:      impl.mappingOrientation(trafficData.GetScreenOrientation()),
			ImeiMd5:          trafficData.GetMd5Imei(),
			ImeiSha256:       "",
			Timezone:         "",
			GpVersion:        "",
			Idfv:             trafficData.GetIdfv(),
			Gaid:             "",
			Carrier:          impl.mappingCarrier(trafficData.GetOperatorType()),
			Oaid:             trafficData.GetOaid(),
			CarrierName:      impl.mappingCarrierName(trafficData.GetOperatorType()),
			LocalTzTime:      "",
			StartupTime:      trafficData.GetDeviceStartupTime(),
			MbTime:           trafficData.GetDeviceUpgradeTime(),
			CpuNum:           0,
			DiskTotal:        request.Device.SystemTotalDisk,
			MemTotal:         request.Device.SystemTotalMem,
			AuthStatus:       0,
			CountryCode:      "CN",
			DeviceType:       request.Device.DeviceName,
			CAID1:            device_utils.GetCaidRaw(trafficData.GetCaid()),
			CAID1Version:     device_utils.GetCaidVersion(trafficData.GetCaid()),
			CAID2:            "",
			CAID2Version:     "",
			SkanVersions:     nil,
		},
		Ua:          trafficData.GetUserAgent(),
		Ip:          trafficData.GetRequestIp(),
		Adslots:     make([]*csj_proto.BidRequest_AdSlot, 0),
		AdxName:     dspSlot.AdxName,
		AdxPassword: dspSlot.AdxPassword,
		Timeout:     300,
		NidList:     nil, //啥东西
		Query:       "",
	}

	if len(dspSlot.AppName) > 0 {
		csjRequest.App.Name = dspSlot.AppName
	}

	if len(dspSlot.PkgName) > 0 {
		csjRequest.App.PackageName = dspSlot.PkgName
	}

	if len(dspSlot.AppVersion) > 0 {
		csjRequest.App.Version = dspSlot.AppVersion
	}

	if len(csjRequest.App.PackageName) > 0 {
		csjRequest.App.ApkSign = md5_utils.GetSHA1String(csjRequest.App.PackageName)
	}

	if len(csjRequest.Device.StartupTime) == 0 {
		csjRequest.Device.StartupTime = trafficData.GetBootMark()
	}

	if len(csjRequest.Device.MbTime) == 0 {
		csjRequest.Device.MbTime = trafficData.GetUpdateMark()
	}
	bidFloor := candidate.GetBidFloor()
	adSlot := &csj_proto.BidRequest_AdSlot{
		Id:           slotId,
		Adtype:       impl.mappingSlotType(trafficData.GetSlotType()),
		AcceptedSize: make([]*csj_proto.BidRequest_AdSlot_Size, 0),
		AcceptedCreativeTypes: []csj_proto.BidRequest_AdSlot_CreativeType{csj_proto.BidRequest_AdSlot_IMAGE,
			csj_proto.BidRequest_AdSlot_GIF, csj_proto.BidRequest_AdSlot_VIDEO, csj_proto.BidRequest_AdSlot_TEXT_ICON},
		AcceptedInteractionType: []csj_proto.BidRequest_AdSlot_InteractionType{csj_proto.BidRequest_AdSlot_SURFING,
			csj_proto.BidRequest_AdSlot_IN_APP, csj_proto.BidRequest_AdSlot_DOWLOAD},
		MinimumCpm:              uint64(bidFloor.Price),
		AdCount:                 1,
		IsOriginAd:              false,
		IsSupportDpl:            true,
		AcceptedImageModes:      nil,
		RefreshCount:            0,
		RefreshType:             0,
		VideoMinDuration:        0,
		VideoMaxDuration:        uint32(request.VideoMaxDuration),
		Pmp:                     nil,
		SupportPkg:              nil,
		IsSupportUlink:          true,
		IsFilterUnlistedPackage: false,
		ParentPkg:               "",
		EasyPlayType:            0,
		WxAppletType:            0,
	}

	adSlot.Pos = impl.mappingPos(adSlot.Adtype)

	adSlot.AcceptedSize = append(adSlot.AcceptedSize, &csj_proto.BidRequest_AdSlot_Size{
		Width:        uint32(dspSlot.Width),
		Height:       uint32(dspSlot.Height),
		CreativeType: 1,
	})

	adSlot.AcceptedSize = append(adSlot.AcceptedSize, &csj_proto.BidRequest_AdSlot_Size{
		Width:        uint32(dspSlot.Width),
		Height:       uint32(dspSlot.Height),
		CreativeType: 4,
	})

	csjRequest.Adslots = append(csjRequest.Adslots, adSlot)

	httpRequest, _, err := impl.BuildPbHttpHttpRequest(csjRequest)
	if err != nil {
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	httpRequest.Header.Set("Content-Type", "application/octet-stream")

	impl.SampleDspBroadcastProtobufRequest(impl.dspId, dspSlot.Id, candidate, csjRequest)

	if request.IsDebug {
		reqBody, _ := json.Marshal(csjRequest)
		zap.L().Info("CSJDspBroker url:, send body:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", impl.GetBidUrl())))), reqBody)
	}
	return httpRequest, err

}

func (impl *CSJDspBroker) mappingDeviceType(deviceType entity.DeviceType) csj_proto.BidRequest_Device_DeviceType {
	switch deviceType {
	case entity.DeviceTypePad:
		return csj_proto.BidRequest_Device_TABLET
	case entity.DeviceTypeMobile:
		return csj_proto.BidRequest_Device_PHONE
	case entity.DeviceTypeOtt:
		return csj_proto.BidRequest_Device_TV
	default:
		return csj_proto.BidRequest_Device_DEVICE_UNKNOWN
	}
}

func (impl *CSJDspBroker) mappingOs(os entity.OsType) csj_proto.BidRequest_Device_OsType {
	switch os {
	case entity.OsTypeIOS:
		return csj_proto.BidRequest_Device_IOS
	case entity.OsTypeAndroid:
		return csj_proto.BidRequest_Device_ANDROID
	case entity.OsTypeWindows:
		return csj_proto.BidRequest_Device_WINDOWS
	default:
		return csj_proto.BidRequest_Device_OS_UNKNOWN
	}
}

func (impl *CSJDspBroker) mappingConnectionType(connectionType entity.ConnectionType) csj_proto.BidRequest_Device_ConnectionType {
	switch connectionType {
	case entity.ConnectionTypeWifi:
		return csj_proto.BidRequest_Device_WIFI
	case entity.ConnectionTypeUnknown:
		return csj_proto.BidRequest_Device_CONN_UNKNOWN
	case entity.ConnectionType2G:
		return csj_proto.BidRequest_Device_MOBILE_2G
	case entity.ConnectionType3G:
		return csj_proto.BidRequest_Device_MOBILE_3G
	case entity.ConnectionType4G:
		return csj_proto.BidRequest_Device_MOBILE_4G
	case entity.ConnectionType5G:
		return csj_proto.BidRequest_Device_MOBILE_5G
	default:
		return csj_proto.BidRequest_Device_CONN_UNKNOWN
	}
}

func (impl *CSJDspBroker) mappingOrientation(s entity.ScreenOrientationType) csj_proto.BidRequest_Device_Orientation {
	switch s {
	case entity.ScreenOrientationTypePortrait:
		return csj_proto.BidRequest_Device_VERTICAL
	case entity.ScreenOrientationTypeLandscape:
		return csj_proto.BidRequest_Device_HORIZONTAL
	default:
		return csj_proto.BidRequest_Device_UNKNOWN
	}
}

func (impl *CSJDspBroker) mappingCarrier(carrier entity.OperatorType) csj_proto.BidRequest_Device_CarrierType {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return csj_proto.BidRequest_Device_MOBILE
	case entity.OperatorTypeChinaTelecom, entity.OperatorTypeTietong:
		return csj_proto.BidRequest_Device_TELECOM
	case entity.OperatorTypeChinaUnicom:
		return csj_proto.BidRequest_Device_UNICOM
	default:
		return csj_proto.BidRequest_Device_CARRIER_UNKNOWN
	}
}

func (impl *CSJDspBroker) mappingCarrierName(carrier entity.OperatorType) string {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return "中国移动"
	case entity.OperatorTypeChinaTelecom, entity.OperatorTypeTietong:
		return "中国电信"
	case entity.OperatorTypeChinaUnicom:
		return "中国联通"
	default:
		return ""
	}
}

func (impl *CSJDspBroker) mappingSlotType(t entity.SlotType) csj_proto.BidRequest_AdSlot_AdType {
	switch t {
	case entity.SlotTypeOpening:
		return csj_proto.BidRequest_AdSlot_SPLASH
	case entity.SlotTypeVideo:
		return csj_proto.BidRequest_AdSlot_FULLSCREEN_VIDEO
	case entity.SlotTypeBanner:
		return csj_proto.BidRequest_AdSlot_BANNER
	case entity.SlotTypePopup:
		return csj_proto.BidRequest_AdSlot_INTERSTITIAL
	case entity.SlotTypeFeeds:
		return csj_proto.BidRequest_AdSlot_STREAM
	case entity.SlotTypeRewardVideo:
		return csj_proto.BidRequest_AdSlot_REWARD_VIDEO
	default:
		return csj_proto.BidRequest_AdSlot_STREAM
	}
}

func (impl *CSJDspBroker) mappingPos(t csj_proto.BidRequest_AdSlot_AdType) csj_proto.BidRequest_AdSlot_Position {
	switch t {
	case csj_proto.BidRequest_AdSlot_SPLASH, csj_proto.BidRequest_AdSlot_INTERSTITIAL:
		return csj_proto.BidRequest_AdSlot_FULLSCREEN
	default:
		return csj_proto.BidRequest_AdSlot_MIDDLE

	}
}

func (impl *CSJDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("CSJDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBroadcastResponseReadBodyFail.Wrap(err)
	}

	csjResp := &csj_proto.BidResponse{}

	if err := impl.ParsePbHttpHttpResponse(resp, body, csjResp); err != nil {
		zap.L().Error("CSJDspBroker.DecodeResponse Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastProtobufResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, csjResp)

	if request.IsDebug {
		resBody, _ := json.Marshal(csjResp)
		zap.L().Info("CSJDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}

	if csjResp.StatusCode != 0 || len(csjResp.Ads) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, resBid := range csjResp.Ads {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		if resBid.Creative == nil {
			return nil, err_code.ErrBroadcastNoBidding
		}

		if resBid.Creative != nil && resBid.Creative.AppInfo != nil {
			candidateAd.AppInfo = &entity.AppInfo{
				PackageName: resBid.Creative.AppInfo.PackageName,
				PackageSize: int(resBid.Creative.ProductSize),
				AppName:     resBid.Creative.AppInfo.AppName,
				Privacy:     resBid.Creative.AppInfo.PrivacyPolicyUrl,
				Permission:  resBid.Creative.AppInfo.PermissionsUrl,
				AppDescURL:  resBid.Creative.AppInfo.DescUrl,
				Develop:     resBid.Creative.AppInfo.DeveloperName,
				Icon:        resBid.Creative.Icon,
			}

			if len(resBid.Creative.AppInfo.Permissions) > 0 {
				for _, per := range resBid.Creative.AppInfo.Permissions {
					candidateAd.AppInfo.PermissionDesc = append(candidateAd.AppInfo.PermissionDesc, entity.PermissionDesc{
						PermissionLab:  per.PermissionName,
						PermissionDesc: per.PermissionDesc,
					})
				}
			}

			if len(candidateAd.AppInfo.AppName) == 0 {
				candidateAd.AppInfo.AppName = resBid.Creative.AppName
				candidateAd.AppInfo.PackageName = resBid.Creative.PackageName
			}

		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

		candidateCreative := impl.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}
		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(resBid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(resBid.AdId)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break

	}

	return result, nil

}

func (impl *CSJDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *CSJDspBroker) ParseCreativeData(bid *csj_proto.BidResponse_Ad) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        type_convert.GetAssertString(bid.Creative.CreativeId),
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	item := bid.Creative

	if len(item.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         item.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Description) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         item.Description,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Icon) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          item.Icon,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if item.Image != nil && len(item.Image.Url) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          item.Image.Url,
			Width:        int32(item.Image.Width),
			Height:       int32(item.Image.Height),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	for _, image := range item.ImageList {
		if len(image.Url) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          image.Url,
				Width:        int32(item.Image.Width),
				Height:       int32(item.Image.Height),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if item.Video != nil && len(item.Video.VideoUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          item.Video.VideoUrl,
			Width:        int32(item.Video.CoverWidth),
			Height:       int32(item.Video.CoverHeight),
			Duration:     float64(item.Video.VideoDuration),
		}
		creative.MaterialList = append(creative.MaterialList, material)

		if len(item.Video.CoverUrl) > 0 {
			material1 := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          item.Video.CoverUrl,
				Width:        int32(item.Video.CoverWidth),
				Height:       int32(item.Video.CoverHeight),
			}
			creative.MaterialList = append(creative.MaterialList, material1)
		}
	}

	return creative
}

func (impl *CSJDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *csj_proto.BidResponse_Ad) *entity.AdMonitorInfo {
	item := bid.Creative

	tracking := &entity.AdMonitorInfo{
		LandingUrl:            item.TargetUrl,
		H5LandingUrl:          item.TargetUrl,
		DownloadUrl:           item.DownloadUrl,
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      item.ClickUrl,
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkMonitorList:   make([]string, 0),
		DeepLinkUrl:           item.DeeplinkUrl,
	}

	if len(tracking.DeepLinkUrl) == 0 {
		tracking.DeepLinkUrl = item.UlinkUrl
	}

	if len(tracking.LandingUrl) == 0 {
		tracking.LandingUrl = item.MarketUrl
		tracking.H5LandingUrl = item.MarketUrl
	}

	if len(tracking.DownloadUrl) != 0 {
		tracking.LandingUrl = tracking.DownloadUrl
	}

	if item.InteractionType == csj_proto.BidResponse_Ad_MaterialMeta_DOWLOAD {
		tracking.LandingAction = entity.LandingTypeDownload
	} else if len(tracking.DeepLinkUrl) > 0 {
		tracking.LandingAction = entity.LandingTypeDeepLink
	}

	if len(item.ShowUrl) > 0 {
		for _, impTrack := range item.ShowUrl {
			if strings.Contains(impTrack, impl.MacroWinPrice) {
				newImpTrack := strings.ReplaceAll(impTrack, impl.MacroWinPrice, "__DSPWPRICE__")
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
			} else {
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impTrack)
			}
		}
	}

	if len(item.WinNoticeUrl) > 0 {
		for _, impTrack := range item.WinNoticeUrl {
			if strings.Contains(impTrack, impl.MacroWinPrice) {
				newImpTrack := strings.ReplaceAll(impTrack, impl.MacroWinPrice, "__DSPWPRICE__")
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
			} else {
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impTrack)
			}
		}
	}

	for _, event := range item.TrackingEvent {
		switch event.Event {
		case "open_url_app":
			tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, event.Url)
		case "dpl_success":
			tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, event.Url)
		case "download_start":
			tracking.AppDownloadStartedMonitorList = append(tracking.AppDownloadStartedMonitorList, event.Url)
		case "download_finish":
			tracking.AppDownloadFinishedMonitorList = append(tracking.AppInstalledFinishMonitorList, event.Url)
		case "install_finish":
			tracking.AppInstalledFinishMonitorList = append(tracking.AppInstalledFinishMonitorList, event.Url)
		case "feed_auto_play":
			tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, event.Url)
		case "feed_over":
			tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, event.Url)
		}

	}

	return tracking

}
