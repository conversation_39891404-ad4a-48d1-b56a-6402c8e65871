package csj_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type CSJSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height      int    `json:"height"`
	Width       int    `json:"width"`
	AppId       string `json:"app_id"`
	AdxName     string `json:"adx_name"`
	AdxPassword string `json:"adx_password"`
	AppVersion  string `json:"app_version"`
	PkgName     string `json:"pkg_name"`
	AppName     string `json:"app_name"`
}

func (info *CSJSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.AppId, err = dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get app_id from extra_data failed, err: %v", err)
	}

	info.AdxName, err = dspSlotInfo.ExtraData.GetString("adx_name")
	if err != nil {
		return fmt.Errorf("get adx_name from extra_data failed, err: %v", err)
	}

	info.AdxPassword, err = dspSlotInfo.ExtraData.GetString("adx_password")
	if err != nil {
		return fmt.Errorf("get adx_password from extra_data failed, err: %v", err)
	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	return nil
}

type CSJDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*CSJSlotSlotInfo
}

func NewCSJDspSlotRegister(dspId utils.ID) *CSJDspSlotRegister {
	return &CSJDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*CSJSlotSlotInfo),
	}
}

func (r *CSJDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *CSJDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*CSJSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &CSJSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[CSJDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *CSJDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *CSJDspSlotRegister) GetSlotInfo(slotId utils.ID) *CSJSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
