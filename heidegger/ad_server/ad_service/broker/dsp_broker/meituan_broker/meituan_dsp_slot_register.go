package meituan_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type MeiTuanSlotInfo struct {
	*entity.DspSlotInfo
	TemplateId string `json:"template_id"`
	NeedVideo  int    `json:"need_video"`
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	MediaId    string `json:"media_id"`
	Token      string `json:"token"`
	AppVersion string `json:"app_version"`
	PkgName    string `json:"pkg_name"`
	AppName    string `json:"app_name"`
}

func (info *MeiTuanSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error
	info.TemplateId, err = dspSlotInfo.ExtraData.GetString("template_id")
	if err != nil {
		return fmt.Errorf("get template_id from extra_data failed, err: %v", err)
	}

	info.NeedVideo, err = dspSlotInfo.ExtraData.GetInt("need_video")
	if err != nil {
		info.NeedVideo = 0
	}

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	info.MediaId, err = dspSlotInfo.ExtraData.GetString("media_id")
	if err != nil {
	}

	info.Token, err = dspSlotInfo.ExtraData.GetString("token")
	if err != nil {
	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	return nil
}

type MeituanDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*MeiTuanSlotInfo
}

func NewMeituanDspSlotRegister(dspId utils.ID) *MeituanDspSlotRegister {
	return &MeituanDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*MeiTuanSlotInfo),
	}
}

func (r *MeituanDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *MeituanDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*MeiTuanSlotInfo)
	for _, slot := range list {
		meiSlot := &MeiTuanSlotInfo{}
		if err := meiSlot.Init(slot); err != nil {
			zap.L().Error("[MeituanDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = meiSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *MeituanDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *MeituanDspSlotRegister) GetSlotInfo(slotId utils.ID) *MeiTuanSlotInfo {
	return r.dspSlotMap[slotId]
}
