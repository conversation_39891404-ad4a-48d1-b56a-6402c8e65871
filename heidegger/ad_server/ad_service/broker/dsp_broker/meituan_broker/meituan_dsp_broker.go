package meituan_broker

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/meituan_broker/meituan_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
)

const meiTuanAdxName = ""
const meiTuanAdxToken = ""

const meiTuanAppPkgNameIos = "com.meituan.imeituan"
const meiTuanAppPkgNameAndroid = "com.sankuai.meituan"

type (
	MeiTuanDspBroker struct {
		dsp_broker.DspBrokerBase

		slotRegister *MeituanDspSlotRegister
		dspId        utils.ID

		MacroWinPrice string
	}
)

func NewMeiTuanDspBroker(dspId utils.ID) *MeiTuanDspBroker {
	return &MeiTuanDspBroker{
		slotRegister:  NewMeituanDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "{AUCTION_PRICE}",
	}
}

func (impl *MeiTuanDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *MeiTuanDspBroker) GetDspId() utils.ID {
	return impl.dspId
}

func (impl *MeiTuanDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	meiTuanRequest := &meituan_proto.RtbRequest{
		RequestId: trafficData.GetRequestId(),
		AdxName:   dspSlot.MediaId,
		Device:    impl.BuildDevice(trafficData),
		Location:  impl.BuildLocation(request, trafficData),
		User:      impl.BuildUser(request),
		Adslots:   impl.BuildAdSlots(request, trafficData, candidate),
		App:       impl.BuildApp(trafficData),
		Token:     dspSlot.Token,
	}

	if len(meiTuanRequest.AdxName) == 0 {
		meiTuanRequest.AdxName = meiTuanAdxName
	}

	if len(meiTuanRequest.Token) == 0 {
		meiTuanRequest.Token = meiTuanAdxToken
	}

	if len(dspSlot.AppVersion) > 0 {
		meiTuanRequest.App.Version = dspSlot.AppVersion
	}

	if len(dspSlot.PkgName) > 0 {
		meiTuanRequest.App.PackageName = dspSlot.PkgName
	}

	if len(dspSlot.AppName) > 0 {
		meiTuanRequest.App.AppName = dspSlot.AppName
	}

	if len(meiTuanRequest.Adslots) == 0 {
		return nil, err_code.ErrInvalidImpression
	}

	//buffer := buffer_pool.NewBuffer()
	//defer buffer.Release()
	//buffer.EnsureSize(meiTuanRequest.Size())
	//
	//_, err := meiTuanRequest.MarshalToSizedBuffer(buffer.Get())
	//if err != nil {
	//	zap.L().Error("MeiTuanDspBroker MarshalToSizedBuffer err", zap.Error(err))
	//	return nil, err
	//}
	//
	//req, err := http.NewRequest("POST", impl.bidUrl, buffer.GetReadCloser())
	//if err != nil {
	//	zap.L().Error("MeiTuanDspBroker http NewRequest err", zap.Error(err))
	//	return nil, err
	//}

	req, _, err := impl.BuildPbHttpHttpRequest(meiTuanRequest)
	if err != nil {
		zap.L().Error("MeiTuanDspBroker MarshalToSizedBuffer err", zap.Error(err))
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}
	req.Header.Set("Content-Type", "application/octet-stream")
	impl.SampleDspBroadcastProtobufRequest(impl.dspId, dspSlot.Id, candidate, meiTuanRequest)

	if request.IsDebug {
		reqbody, _ := json.Marshal(meiTuanRequest)
		zap.L().Info("MeiTuanDspBroker url:, send:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", impl.GetBidUrl())))), reqbody)
	}
	return req, err
}

func (impl *MeiTuanDspBroker) BuildAdSlots(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData, candidate *ad_service.AdCandidate) []*meituan_proto.AdSlot {

	meituanSlots := make([]*meituan_proto.AdSlot, 0)

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return meituanSlots
	}

	meituanTemplate := &meituan_proto.TemplateInfo{
		TemplateId: dspSlot.TemplateId,
	}

	needVideo := dspSlot.NeedVideo

	if needVideo == 1 {
		meituanTemplate.Video = &meituan_proto.Video{
			VideoTypes:  []meituan_proto.VideoType{meituan_proto.VideoType_VT_FLV, meituan_proto.VideoType_VT_MP4, meituan_proto.VideoType_VT_AVI},
			Minduration: request.VideoMinDuration,
			Maxduration: request.VideoMaxDuration,
		}

		if meituanTemplate.Video.GetMaxduration() == 0 {
			meituanTemplate.Video.Maxduration = 120
		}
	}

	//meiTSlotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	//if meiTSlotId == "" {
	//	meiTSlotId = trafficData.GetSourceSlotId()
	//}

	meiTSlotId := trafficData.GetSourceSlotId() + "_" + trafficData.GetMediaSlotKey()
	if len(dspSlot.PkgName) > 0 {
		meiTSlotId += dspSlot.PkgName
	} else {
		meiTSlotId += trafficData.GetAppBundle()
	}
	bidFloor := candidate.GetBidFloor()

	meituanSlot := &meituan_proto.AdSlot{
		AdslotId:  meiTSlotId,
		Templates: []*meituan_proto.TemplateInfo{meituanTemplate},
		Floor:     uint64(bidFloor.Price),
		IsHttps:   true,
		//Pmps:       nil,
		PriceTypes: []meituan_proto.PriceType{meituan_proto.PriceType_CPM_PRICE},
		//CpcFloor:   nil,
	}

	meituanSlots = append(meituanSlots, meituanSlot)

	return meituanSlots
}

func (impl *MeiTuanDspBroker) BuildLocation(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) *meituan_proto.LocationInfo {

	meituanLocation := &meituan_proto.LocationInfo{
		Latitude:  request.Device.Lat,
		Longitude: request.Device.Lon,
		//Ipv4:      trafficData.GetRequestIp(),
		//Ipv6:       nil,
		//LatLonType: nil,
	}

	if request.Device.IsIp6 {
		meituanLocation.Ipv6 = trafficData.GetRequestIp()
	} else {
		meituanLocation.Ipv4 = trafficData.GetRequestIp()
	}

	return meituanLocation
}

func (impl *MeiTuanDspBroker) BuildUser(request *ad_service.AdRequest) *meituan_proto.UserInfo {
	meituanUser := &meituan_proto.UserInfo{
		Uid: request.UserId,
	}

	return meituanUser
}

func (impl *MeiTuanDspBroker) BuildApp(trafficData ad_service_entity.TrafficData) *meituan_proto.App {
	meituanApp := &meituan_proto.App{
		AppName:     trafficData.GetAppName(),
		PackageName: trafficData.GetAppBundle(),
		Version:     trafficData.GetAppVersion(),
		//AppCategory: nil,
	}

	return meituanApp
}

func (impl *MeiTuanDspBroker) BuildDevice(trafficData ad_service_entity.TrafficData) *meituan_proto.Device {

	meiTuanDevice := &meituan_proto.Device{
		DeviceIds:   make([]*meituan_proto.DeviceId, 0),
		Brand:       trafficData.GetBrand(),
		Model:       trafficData.GetModel(),
		OsVersion:   trafficData.GetOsVersion(),
		DevicePixel: trafficData.GetScreenDensity(),
		ScreenSize: &meituan_proto.Size{
			Width:  trafficData.GetScreenWidth(),
			Height: trafficData.GetScreenHeight(),
		},
		DeviceScreenOrientation: impl.ToScreenOrientation(trafficData.GetScreenOrientation()),
		Ua:                      trafficData.GetUserAgent(),
		PlateformType:           impl.ToMeiTuanOsType(trafficData.GetOsType()),
		OperatiorType:           impl.ToMeiTuanCarrier(trafficData.GetOperatorType()),
		NetworkType:             impl.ToMeiTuanNetType(trafficData.GetConnectionType()),
	}

	if len(trafficData.GetMac()) > 0 {
		mDevice := &meituan_proto.DeviceId{
			Id: trafficData.GetMac(),
		}
		mDevice.DeviceIdType = meituan_proto.DeviceIdType_MAC
		mDevice.EncryptionType = meituan_proto.EncryptionType_PLAINTEXT
		meiTuanDevice.DeviceIds = append(meiTuanDevice.DeviceIds, mDevice)
	}
	if len(trafficData.GetMd5Mac()) > 0 {
		mDevice := &meituan_proto.DeviceId{
			Id: strings.ToLower(trafficData.GetMd5Mac()),
		}

		mDevice.DeviceIdType = meituan_proto.DeviceIdType_MAC
		mDevice.EncryptionType = meituan_proto.EncryptionType_MD5

		meiTuanDevice.DeviceIds = append(meiTuanDevice.DeviceIds, mDevice)
	}

	if len(trafficData.GetCaid()) > 0 {
		mDevice := &meituan_proto.DeviceId{
			Id: device_utils.GetCaidRaw(trafficData.GetCaid()),
		}
		mDevice.DeviceIdType = meituan_proto.DeviceIdType_CAID
		mDevice.EncryptionType = meituan_proto.EncryptionType_PLAINTEXT
		meiTuanDevice.DeviceIds = append(meiTuanDevice.DeviceIds, mDevice)
	}

	if len(trafficData.GetMd5Caid()) > 0 {
		mDevice := &meituan_proto.DeviceId{
			Id: strings.ToLower(device_utils.GetCaidRaw(trafficData.GetMd5Caid())),
		}
		mDevice.DeviceIdType = meituan_proto.DeviceIdType_CAID
		mDevice.EncryptionType = meituan_proto.EncryptionType_MD5
		meiTuanDevice.DeviceIds = append(meiTuanDevice.DeviceIds, mDevice)
	}

	switch trafficData.GetOsType() {
	case entity.OsTypeAndroid:
		if len(trafficData.GetOaid()) > 0 {
			mDevice := &meituan_proto.DeviceId{
				Id: trafficData.GetOaid(),
			}
			mDevice.DeviceIdType = meituan_proto.DeviceIdType_OAID
			mDevice.EncryptionType = meituan_proto.EncryptionType_PLAINTEXT
			meiTuanDevice.DeviceIds = append(meiTuanDevice.DeviceIds, mDevice)
		}
		if len(trafficData.GetMd5Oaid()) > 0 {
			mDevice := &meituan_proto.DeviceId{
				Id: strings.ToLower(trafficData.GetMd5Oaid()),
			}
			mDevice.DeviceIdType = meituan_proto.DeviceIdType_OAID
			mDevice.EncryptionType = meituan_proto.EncryptionType_MD5
			meiTuanDevice.DeviceIds = append(meiTuanDevice.DeviceIds, mDevice)
		}
		if len(trafficData.GetAndroidId()) > 0 {
			mDevice := &meituan_proto.DeviceId{
				Id: trafficData.GetAndroidId(),
			}
			mDevice.DeviceIdType = meituan_proto.DeviceIdType_ANDROID_ID
			mDevice.EncryptionType = meituan_proto.EncryptionType_PLAINTEXT
			meiTuanDevice.DeviceIds = append(meiTuanDevice.DeviceIds, mDevice)
		} else if len(trafficData.GetMd5AndroidId()) > 0 {
			mDevice := &meituan_proto.DeviceId{
				Id: strings.ToLower(trafficData.GetMd5AndroidId()),
			}
			mDevice.DeviceIdType = meituan_proto.DeviceIdType_ANDROID_ID
			mDevice.EncryptionType = meituan_proto.EncryptionType_MD5
			meiTuanDevice.DeviceIds = append(meiTuanDevice.DeviceIds, mDevice)
		}
		if len(trafficData.GetImei()) > 0 {
			mDevice := &meituan_proto.DeviceId{
				Id: trafficData.GetImei(),
			}
			mDevice.DeviceIdType = meituan_proto.DeviceIdType_IMEI
			mDevice.EncryptionType = meituan_proto.EncryptionType_PLAINTEXT
			meiTuanDevice.DeviceIds = append(meiTuanDevice.DeviceIds, mDevice)
		}
		if len(trafficData.GetMd5Imei()) > 0 {
			mDevice := &meituan_proto.DeviceId{
				Id: strings.ToLower(trafficData.GetMd5Imei()),
			}
			mDevice.DeviceIdType = meituan_proto.DeviceIdType_IMEI
			mDevice.EncryptionType = meituan_proto.EncryptionType_MD5
			meiTuanDevice.DeviceIds = append(meiTuanDevice.DeviceIds, mDevice)
		}
	case entity.OsTypeIOS:
		if len(trafficData.GetIdfa()) > 0 {
			mDevice := &meituan_proto.DeviceId{
				Id: trafficData.GetIdfa(),
			}
			mDevice.DeviceIdType = meituan_proto.DeviceIdType_IDFA
			mDevice.EncryptionType = meituan_proto.EncryptionType_PLAINTEXT
			meiTuanDevice.DeviceIds = append(meiTuanDevice.DeviceIds, mDevice)
		}
		if len(trafficData.GetMd5Idfa()) > 0 {
			mDevice := &meituan_proto.DeviceId{
				Id: strings.ToLower(trafficData.GetMd5Idfa()),
			}

			mDevice.DeviceIdType = meituan_proto.DeviceIdType_IDFA
			mDevice.EncryptionType = meituan_proto.EncryptionType_MD5
			meiTuanDevice.DeviceIds = append(meiTuanDevice.DeviceIds, mDevice)
		}
	}

	return meiTuanDevice
}

func (impl *MeiTuanDspBroker) ToScreenOrientation(t entity.ScreenOrientationType) int32 {
	switch t {
	case entity.ScreenOrientationTypeLandscape:
		return 2
	case entity.ScreenOrientationTypePortrait:
		return 1
	}
	return 0
}

func (impl *MeiTuanDspBroker) ToMeiTuanOsType(t entity.OsType) meituan_proto.DevicePlatformType {
	switch t {
	case entity.OsTypeAndroid:
		return meituan_proto.DevicePlatformType_DPT_ANDROID
	case entity.OsTypeIOS:
		return meituan_proto.DevicePlatformType_DPT_IOS
	}
	return meituan_proto.DevicePlatformType_DPT_UNKNOWN
}

//func (impl *MeiTuanDspBroker) ToMeiTuanDeviceType(t dict.DeviceType) int32 {
//	switch t {
//	case dict.DeviceTypePC:
//		return 3
//	case dict.DeviceTypePhone:
//		return 4
//	case dict.DeviceTypePad:
//		return 5
//	case dict.DeviceMobile:
//		return 6
//	case dict.DeviceTypeOTT:
//		return 7
//	}
//	return 0
//}

func (impl *MeiTuanDspBroker) ToMeiTuanCarrier(t entity.OperatorType) meituan_proto.DeviceOperatorType {
	switch t {
	case entity.OperatorTypeChinaMobile:
	case entity.OperatorTypeTietong:
		return meituan_proto.DeviceOperatorType_DOT_MOBILE
	case entity.OperatorTypeChinaUnicom:
		return meituan_proto.DeviceOperatorType_DOT_UNICOM
	case entity.OperatorTypeChinaTelecom:
		return meituan_proto.DeviceOperatorType_DOT_TELECOM
	}
	return meituan_proto.DeviceOperatorType_DOT_UNKNOWN
}

func (impl *MeiTuanDspBroker) ToMeiTuanNetType(t entity.ConnectionType) meituan_proto.NetworkType {
	switch t {
	case entity.ConnectionTypeNetEthernet:
		return meituan_proto.NetworkType_NETWORK_UNKNOWN
	case entity.ConnectionTypeWifi:
		return meituan_proto.NetworkType_NETWORK_WIFI
	case entity.ConnectionType2G:
		return meituan_proto.NetworkType_NETWORK_2G
	case entity.ConnectionType3G:
		return meituan_proto.NetworkType_NETWORK_3G
	case entity.ConnectionType4G:
		return meituan_proto.NetworkType_NETWORK_4G
	case entity.ConnectionType5G:
		return meituan_proto.NetworkType_NETWORK_5G
	}
	return meituan_proto.NetworkType_NETWORK_UNKNOWN
}

func (impl *MeiTuanDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *MeiTuanDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("MeiTuanDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	buffer, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	meiTuanResponse := &meituan_proto.RtbResponse{}
	//if err := proto.Unmarshal(buffer, meiTuanResponse); err != nil {
	//	return nil, err_code.ErrBrokerResponse.Wrap(err)
	//}

	if err := impl.ParsePbHttpHttpResponse(resp, buffer, meiTuanResponse); err != nil {
		zap.L().Debug("MeiTuanDspBroker.DecodeResponse json.Unmarshal, err", zap.Error(err))
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastProtobufResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, meiTuanResponse)

	if request.IsDebug {
		resBody, _ := json.Marshal(meiTuanResponse)
		zap.L().Info("MeiTuan.ParseResponse response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}

	if meiTuanResponse.GetBlockDeviceId() {
		impl.markBlackDeviceCode(request, broadcastCandidate)
		return nil, err_code.ErrBroadcastNoBidding
	}

	if len(meiTuanResponse.Bids) < 1 || len(meiTuanResponse.Bids[0].GetAds()) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0, len(meiTuanResponse.Bids[0].GetAds()))
	for _, bid := range meiTuanResponse.Bids[0].GetAds() {

		candidateAd := &entity.Ad{
			DspId:         impl.GetDspId(),
			DspSlotId:     broadcastCandidate.GetDspSlotId(),
			DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
			AdMonitorInfo: impl.ParseTrackingData(request, bid),
		}

		candidateAd.AppInfo = &entity.AppInfo{}
		candidateAd.AppInfo.AppName = bid.GetAppName()
		candidateAd.AppInfo.PackageName = bid.GetPackageName()

		if request.Device.GetOsType() == entity.OsTypeIOS {
			if candidateAd.AppInfo.PackageName == "" {
				candidateAd.AppInfo.PackageName = meiTuanAppPkgNameIos
			}
		} else {
			if candidateAd.AppInfo.PackageName == "" {
				candidateAd.AppInfo.PackageName = meiTuanAppPkgNameAndroid
			}
		}

		//if bid.GetDealId() != "" {
		//	bidInfo.CreativeData.SetDealId(bid.GetDealId())
		//}

		candidateCreative := impl.ParseCreativeData(bid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(bid.GetBidPrice()))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break
	}

	return result, nil
}
func (impl *MeiTuanDspBroker) ParseCreativeData(bid *meituan_proto.Ad) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.GetAdId(),
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	//material := &data.MaterialData{
	//	Resource: make([]ad_entity.Resource, 0),
	//}

	hasTxt := false
	hasImage := false
	hasVideo := false

	width := int32(0)
	height := int32(0)
	if bid.GetImageSize() != nil {
		width = bid.GetImageSize().Width
		height = bid.GetImageSize().Height
	}

	if bid.GetTitle() != "" {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         bid.GetTitle(),
		}
		creative.MaterialList = append(creative.MaterialList, material)
		hasTxt = true
	}

	if bid.GetDescription() != "" {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         bid.GetDescription(),
		}
		creative.MaterialList = append(creative.MaterialList, material)
		hasTxt = true
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
		hasTxt = true
	}

	if bid.GetIcon() != "" {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.GetIcon(),
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.ImageUrls) > 0 {
		hasImage = true
		//width := int32(0)
		//height := int32(0)
		//if bid.GetImageSize() != nil {
		//	width = bid.GetImageSize().GetWidth()
		//	height = bid.GetImageSize().GetHeight()
		//
		//}
		for _, admImage := range bid.ImageUrls {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          admImage,
				Width:        width,
				Height:       height,
			}

			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if bid.GetVideo() != nil && bid.GetVideo().GetVideoUrl() != "" {
		hasVideo = true
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          bid.GetVideo().GetVideoUrl(),
			Duration:     float64(bid.GetVideo().GetVideoDuration()),
			Width:        width,
			Height:       height,
		}
		creative.MaterialList = append(creative.MaterialList, material)

		if bid.GetVideo().GetCoverUrl() != "" {
			materialImg := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          bid.GetVideo().GetCoverUrl(),
				Width:        width,
				Height:       height,
			}
			creative.MaterialList = append(creative.MaterialList, materialImg)
		}
	}

	if hasImage && !hasVideo && !hasTxt {
		//material.MimeType = dict.MimeJpg
	} else if !hasImage && hasVideo && !hasTxt {
		//material.MimeType = dict.MimeMp4
	} else {
		//material.MimeType = dict.MimeFeed
	}

	return creative
}

func (impl *MeiTuanDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *meituan_proto.Ad) *entity.AdMonitorInfo {
	trackings := &entity.AdMonitorInfo{
		LandingUrl:            bid.GetTargetUrl(),
		H5LandingUrl:          bid.GetTargetUrl(),
		DownloadUrl:           bid.GetDownloadUrl(),
		ClickMonitorList:      bid.GetClickUrls(),
		ImpressionMonitorList: make([]string, 0),
		VideoStartUrlList:     make([]string, 0),
		VideoCloseUrlList:     make([]string, 0),
		LandingAction:         entity.LandingTypeInWebView,
	}

	for _, impTrack := range bid.GetImpUrls() {

		if strings.Contains(impTrack, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impTrack, impl.MacroWinPrice, "__DSPWPRICE__")
			trackings.ImpressionMonitorList = append(trackings.ImpressionMonitorList, newImpTrack)
		} else {
			trackings.ImpressionMonitorList = append(trackings.ImpressionMonitorList, impTrack)
		}
	}

	if bid.GetDownloadUrl() != "" {
		trackings.LandingUrl = bid.GetDownloadUrl()
	}

	for _, impTrack := range bid.GetWinNoticeUrls() {
		if strings.Contains(impTrack, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impTrack, impl.MacroWinPrice, "__DSPWPRICE__")
			trackings.ImpressionMonitorList = append(trackings.ImpressionMonitorList, newImpTrack)
		} else {
			trackings.ImpressionMonitorList = append(trackings.ImpressionMonitorList, impTrack)
		}
	}

	trackings.DeepLinkUrl = bid.GetDeeplinkUrl()
	if request.Device.GetOsType() == entity.OsTypeIOS {
		if bid.GetUniversalLink() != "" {
			trackings.DeepLinkUrl = bid.GetUniversalLink()
		}
	}

	if len(trackings.DeepLinkUrl) > 0 && trackings.LandingAction == entity.LandingTypeInWebView {
		trackings.LandingAction = entity.LandingTypeDeepLink
	}

	//for _, downType := range bid.GetPromotionTypes() {
	//	if downType == meituan_proto.PromotionType_DOWNLOAD {
	//		trackings.LandingAction = entity.LandingTypeDownload
	//	}
	//}

	//if bid.GetWxMiniProgramLink() != nil {
	//	trackings.LandingDesc.WechatProgramId = bid.GetWxMiniProgramLink().GetMiniProgramId()
	//	trackings.LandingDesc.WechatProgramPath = bid.GetWxMiniProgramLink().GetMiniProgramPath()
	//}

	if len(bid.GetTracking()) > 0 {
		for _, bidTrack := range bid.GetTracking() {
			switch bidTrack.GetEvent() {
			case meituan_proto.Event_START_DOWNLOAD:
				trackings.AppDownloadStartedMonitorList = append(trackings.AppDownloadStartedMonitorList, bidTrack.GetUrls()...)
			case meituan_proto.Event_FINISH_DOWNLOAD:
				trackings.AppDownloadFinishedMonitorList = append(trackings.AppDownloadFinishedMonitorList, bidTrack.GetUrls()...)
			case meituan_proto.Event_FINISH_INSTALL:
				trackings.AppInstalledFinishMonitorList = append(trackings.AppInstalledFinishMonitorList, bidTrack.GetUrls()...)
			case meituan_proto.Event_VIDEO_PLAY_START:
				trackings.VideoStartUrlList = append(trackings.VideoStartUrlList, bidTrack.GetUrls()...)
			case meituan_proto.Event_VIDEO_PLAY_END:
				trackings.VideoCloseUrlList = append(trackings.VideoCloseUrlList, bidTrack.GetUrls()...)
			}

		}
	}

	return trackings
}

func (impl *MeiTuanDspBroker) markBlackDeviceCode(request *ad_service.AdRequest, candidate *ad_service.AdCandidate) {
	if impl.GetUserSegmentClient() == nil {
		return
	}

	trafficData := candidate.GetModifiedTrafficData()
	deviceId, _ := trafficData.GetDeviceIdWithType()

	// seconds to the end of the day
	//now := time.Now()
	expireTime := 1800

	if err := impl.GetUserSegmentClient().AddUserSegmentAsync(deviceId, entity.UserTagMeiTuanBlackDeviceTag, 1, uint32(expireTime), 0); err != nil {
		zap.L().Error("MeiTuanDspBroker markBlackDeviceCode AddUserSegmentAsync err", zap.Error(err))
		return
	}
}

func (impl *MeiTuanDspBroker) CheckBroadcastContext(ctx *ad_service.AdRequest, candidateList ad_service.AdCandidateList) error {
	if ctx.UserSegment.ContainsTag(entity.UserTagMeiTuanBlackDeviceTag) {
		return fmt.Errorf("meituanblackdevice")
	}

	return nil
}
