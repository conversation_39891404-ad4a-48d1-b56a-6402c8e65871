package adx2345_dsp_broker

import (
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/adx2345_dsp_broker/adx2345_dsp_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/hash_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"fmt"
)

type Adx2345DspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *Adx2345SlotRegister
	log          *zap.Logger
	macroInfo    macro_builder.MonitorMacroInfo
}

func NewAdx2345DspBroker(dspId utils.ID) *Adx2345DspBroker {
	return &Adx2345DspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		slotRegister: NewAdx2345SlotRegister(dspId),
		log:          zap.L().With(zap.String("broker", "Adx2345DspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__AUCTION_PRICE__",
			MacroClickDownX: "__down_x__",
			MacroClickDownY: "__down_y__",
			MacroClickUpX:   "__up_x__",
			MacroClickUpY:   "__up_y__",
		},
	}
}

func (a *Adx2345DspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		a.log.WithField("candidates", len(candidateList)).Error("too many candidates")
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := a.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	//dspSlotId
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	imp := &adx2345_dsp_proto.AdRequest_AdSlot{
		Id:         slotId,
		ImpId:      request.GetRequestId(),
		Type:       mappingSlotType(trafficData.GetSlotType()),
		Width:      int32(trafficData.GetSlotWidth()),
		Height:     int32(trafficData.GetSlotHeight()),
		FloorPrice: candidate.GetBidFloor().Price,
		BidType:    int32(dspSlot.BidType),
		BillType:   0,
		ActionType: []int32{0},
	}

	width := int32(0)
	height := int32(0)

	if dspSlot.Width > 0 && dspSlot.Height > 0 {
		imp.Width = int32(dspSlot.Width)
		imp.Height = int32(dspSlot.Height)
		width = int32(dspSlot.Width)
		height = int32(dspSlot.Height)
	} else if len(request.SlotSize) > 0 {
		imp.Width = int32(request.SlotSize[0].Width)
		imp.Height = int32(request.SlotSize[0].Height)
		width = int32(request.SlotSize[0].Width)
		height = int32(request.SlotSize[0].Height)
	}

	for _, templateId := range dspSlot.TemplateIds {
		templates := &adx2345_dsp_proto.AdRequest_AdSlot_Template{
			Id:     templateId,
			Width:  width,
			Height: height,
		}
		imp.Template = append(imp.Template, templates)
	}

	var appID string
	if trafficData.GetAppBundle() != "" {
		appID = strconv.FormatUint(hash_utils.XXHash(trafficData.GetAppBundle()), 10)
	}

	bidRequest := &adx2345_dsp_proto.AdRequest{
		Reqid:      request.GetRequestId(),
		ApiVersion: "1.0",
		AdSlotList: []*adx2345_dsp_proto.AdRequest_AdSlot{imp},
		App: &adx2345_dsp_proto.AdRequest_App{
			AppId:       appID,
			Name:        trafficData.GetAppName(),
			PackageName: trafficData.GetAppBundle(),
			Version:     trafficData.GetAppVersion(),
		},
		Device: &adx2345_dsp_proto.AdRequest_Device{
			Ua:          trafficData.GetUserAgent(),
			Type:        mappingDeviceType(trafficData.GetDeviceType()),
			Brand:       trafficData.GetBrand(),
			Model:       trafficData.GetModel(),
			Make:        trafficData.GetBrand(),
			HmsVersion:  request.Device.VercodeHms,
			AsVersion:   request.Device.VercodeAg,
			Os:          mappingDeviceOs(trafficData.GetOsType()),
			OsVersion:   trafficData.GetOsVersion(),
			Ppi:         request.Device.PPI,
			Height:      trafficData.GetScreenHeight(),
			Width:       trafficData.GetScreenWidth(),
			Carrier:     mappingCarrier(trafficData.GetOperatorType()),
			Network:     mappingConnectionType(trafficData.GetConnectionType()),
			Orientation: mappingOrientation(trafficData.GetScreenOrientation()),
			Ip:          trafficData.GetRequestIp(),
			Imei:        trafficData.GetImei(),
			ImeiMD5:     trafficData.GetMd5Imei(),
			Oaid:        trafficData.GetOaid(),
			OaidMD5:     trafficData.GetMd5Oaid(),
			Dpid:        trafficData.GetAndroidId(),
			DpidMD5:     trafficData.GetMd5AndroidId(),
			Mac:         trafficData.GetMac(),
			MacMD5:      trafficData.GetMd5Mac(),
			Idfa:        trafficData.GetIdfa(),
			IdfaMD5:     trafficData.GetMd5Idfa(),
			Idfv:        trafficData.GetIdfv(),
			BootMark:    trafficData.GetBootMark(),
			UpdateMark:  trafficData.GetUpdateMark(),
			BirthTime:   trafficData.GetDeviceInitTime(),
			UpdateTime:  trafficData.GetDeviceUpgradeTime(),
			Language:    request.Device.Language,
			Geo: &adx2345_dsp_proto.AdRequest_Device_Geo{
				Lat: trafficData.GetGeoLatitude(),
				Lon: trafficData.GetGeoLongitude(),
			},
			Paid: request.Device.Paid,
			Aaid: request.Device.Aaid,
			Caid: &adx2345_dsp_proto.AdRequest_Device_Caid{
				BootTimeInSec: trafficData.GetDeviceStartupTime(),
				CountryCode:   request.Device.CountryCode,
				Language:      request.Device.Language,
				DeviceName:    request.Device.DeviceName,
				Model:         request.Device.Model,
				SystemVersion: trafficData.GetOsVersion(),
				Machine:       request.Device.HardwareMachineCode,
				TimeZone:      strconv.Itoa(int(request.Device.TimeZone)),
				InitTime:      trafficData.GetDeviceInitTime(),
				SysFileTime:   trafficData.GetDeviceUpgradeTime(),
				Disk:          strconv.FormatInt(request.Device.SystemTotalDisk, 10),
				Memory:        strconv.FormatInt(request.Device.SystemTotalMem, 10),
			},
			ElapseTime:  request.Device.SystemElapseTime,
			SysCpuNum:   int32(request.Device.SystemTotalCpu),
			RomVersion:  trafficData.GetRomVersion(),
			SdFreeSpace: strconv.FormatInt(request.Device.SystemFreeDisk, 10),
			AnDisk:      strconv.FormatInt(request.Device.SystemTotalDisk, 10),
			AnMemory:    strconv.FormatInt(request.Device.SystemTotalMem, 10),
		},
		AppList: strings.Join(a.GetInstalledAppIDs(request, a.GetDspId()), ","),
	}

	deviceID, _ := trafficData.GetDeviceIdWithType()
	bidRequest.User = &adx2345_dsp_proto.AdRequest_User{
		UserId: deviceID,
	}

	if len(bidRequest.App.Name) < 1 {
		bidRequest.App.Name = bidRequest.App.PackageName
	}

	if len(request.Device.Referer) > 0 {
		bidRequest.Site.Refer = request.Device.Referer
	}
	switch request.UserGender {
	case entity.UserGenderMan:
		bidRequest.User.Gender = "M"
	case entity.UserGenderWoman:
		bidRequest.User.Gender = "F"
	}

	if len(request.Device.SystemBatteryStatus) > 0 {
		istatus, _ := strconv.Atoi(request.Device.SystemBatteryStatus)
		bidRequest.Device.BatteryState = int32(istatus)
	}

	if len(trafficData.GetCaid()) != 0 {
		bidRequest.Device.Caid.Id = device_utils.GetCaidRaw(trafficData.GetCaid())
		bidRequest.Device.Caid.Version = device_utils.GetCaidVersion(trafficData.GetCaid())
	}
	if len(request.Device.Caids) > 0 {
		for _, caidItem := range request.Device.Caids {
			if caidItem == trafficData.GetCaid() {
				continue
			}
			bidRequest.Device.Caid.Id = device_utils.GetCaidRaw(caidItem)
			bidRequest.Device.Caid.Version = device_utils.GetCaidVersion(caidItem)
		}
	}

	if request.Device.IsIp6 {
		bidRequest.Device.Ipv6 = trafficData.GetRequestIp()
		bidRequest.Device.Ip = ""
	}

	if len(trafficData.GetWebviewUA()) > 0 {
		bidRequest.Device.Ua = trafficData.GetWebviewUA()
	}
	if len(dspSlot.APPID) > 0 {
		bidRequest.App.AppId = dspSlot.APPID
	}
	if len(dspSlot.PkgName) > 0 {
		bidRequest.App.PackageName = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidRequest.App.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidRequest.App.Version = dspSlot.AppVersion
	}

	if request.IsDebug {
		payload, _ := sonic.Marshal(bidRequest)
		a.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}

	httpReq, _, err := a.BuildPbHttpHttpRequest(bidRequest)
	if err != nil {
		a.log.WithError(err).Error("BuildPbHttpHttpRequest error")
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}
	httpReq.Header.Set("Content-Type", "application/x-protobuf")
	a.SampleDspBroadcastProtobufRequest(a.DspId, trafficData.GetDspSlotId(), candidate, bidRequest)
	return httpReq, nil
}
func mappingSlotType(t entity.SlotType) int32 {
	switch t {
	case entity.SlotTypeOpening, entity.SlotTypeVideoOpening:
		return 1
	case entity.SlotTypePopup:
		return 2
	case entity.SlotTypeFeeds:
		return 3
	case entity.SlotTypeRewardVideo:
		return 4
	default:
		return 1
	}
}
func mappingBidType(t entity.BidType) int32 {
	switch t {
	case entity.BidTypeCpm:
		return 0
	default:
		return 1
	}
}

func mappingDeviceOs(osType entity.OsType) int32 {
	switch osType {
	case entity.OsTypeIOS:
		return 3
	case entity.OsTypeAndroid:
		return 4
	case entity.OsTypeWindows:
		return 0
	case entity.OsTypeMacOs:
		return 1
	default:
		return 4
	}
}

func mappingCarrier(carrier entity.OperatorType) int32 {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 2
	case entity.OperatorTypeChinaTelecom:
		return 0
	default:
		return 4
	}
}
func mappingOrientation(orientation entity.ScreenOrientationType) int32 {
	switch orientation {
	case entity.ScreenOrientationTypePortrait:
		return 1
	case entity.ScreenOrientationTypeLandscape:
		return 2
	default:
		return 0
	}
}
func mappingConnectionType(connectionType entity.ConnectionType) int32 {
	switch connectionType {
	case entity.ConnectionTypeWifi:
		return 0
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 6
	}
}

func mappingDeviceType(dy entity.DeviceType) int32 {
	switch dy {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypePc:
		return 0
	case entity.DeviceTypeOtt:
		return 3
	default:
		return 1
	}
}

func (a *Adx2345DspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]
	if response.StatusCode != 200 {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(response.StatusCode), type_convert.GetAssertString(response.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse
	}

	resp := &adx2345_dsp_proto.AdResponse{}
	err = a.ParsePbHttpHttpResponse(response, data, resp)

	if err != nil {
		a.log.WithError(err).WithField("response", response).Error("ParseSonicJsonHttpResponse error")
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		return nil, err_code.ErrBrokerParseError.Wrap(err)
	}

	a.SampleDspBroadcastProtobufResponse(a.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, resp)
	if request.IsDebug {
		resBody, _ := sonic.Marshal(resp)
		zap.L().Info("Adx2345DspBroker.ParseResponse, body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
		//a.log.WithField("response", string(resBody)).Info("BuildResponse debug")
	}
	a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.Code), getCodeMsg(resp.Code))

	if resp.Code != 0 || len(resp.Ads) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0, 1)
	for _, bid := range resp.Ads {
		ad := &entity.Ad{
			DspId:      a.DspId,
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
			AppInfo:    &entity.AppInfo{},
		}

		adMonitorInfo, creative := a.parseCallbacksAndCreative(bid, request.Device.OsType)

		ad.AdMonitorInfo = adMonitorInfo
		if creative == nil {
			return nil, err_code.ErrCreativeNotFound
		}

		candidate := ad_service.NewDspAdCandidateWithPool(ad)
		candidate.SetAdCandidateChargePriceEncoder(a.chargePriceEncoder)
		candidate.SetBidPrice(uint32(bid.Price))
		//candidate.SetBidPrice(uint32(2800))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(creative)
		candidate.SetDspAdID(bid.CreativeId)
		candidate.SetDspProtocol(a.DspProtocol)
		result = append(result, candidate)
		break
	}

	return result, nil
}

func (a *Adx2345DspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := a.PriceManager.GetDspCoder(a.DspProtocol).EncodeWithKey(uint64(chargePrice), a.GetIKey(), a.GetEKey())
	if err != nil {
		a.log.WithError(err).WithField("price", chargePrice).Errorf("chargePriceEncoder error")
		return ""
	}
	return result
}

func (a *Adx2345DspBroker) parseCallbacksAndCreative(data *adx2345_dsp_proto.AdResponse_AdInfo, ostype entity.OsType) (*entity.AdMonitorInfo, *entity.Creative) {
	material := data.Material
	video := material.Video
	tracking := data.Tracking
	action := data.Action

	info := &entity.AdMonitorInfo{
		LandingUrl: data.LandingPage,
	}

	if action != nil {
		info.LandingAction = mappingLandingType(action.Type)
		info.DeepLinkUrl = action.Deeplink
		if ostype == entity.OsTypeIOS {
			info.DeepLinkUrl = action.UniversalLink
		}

		if action.App != nil {
			appinfo := action.App
			info.DownloadUrl = appinfo.DownloadUrl
			info.AppInfo.PackageName = appinfo.PackageName
			info.AppInfo.AppName = appinfo.Name
			info.AppInfo.AppVersion = appinfo.Version
			info.AppInfo.Develop = appinfo.Developer
			info.AppInfo.AppDescURL = appinfo.FunctionDesc
			info.AppInfo.AppDesc = appinfo.Intro
			info.AppInfo.PackageSize = int(appinfo.Size_)
			info.AppInfo.Privacy = appinfo.PrivacyUrl
			info.AppInfo.Permission = appinfo.PermissionUrl
			if appinfo.Logo != nil {
				info.AppInfo.Icon = appinfo.Logo.Url
			}
			if len(appinfo.Permissions) > 0 {
				for _, perm := range appinfo.Permissions {
					info.AppInfo.PermissionDesc = append(info.AppInfo.PermissionDesc, entity.PermissionDesc{
						PermissionLab:  perm.Title,
						PermissionDesc: perm.Desc,
					})
				}
			}
		}

		if action.MiniProgram != nil {
			info.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   action.MiniProgram.Username,
				ProgramPath: action.MiniProgram.Path,
			}
		}
	}

	//assets
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        data.CreativeId,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}
	if len(material.Images) > 0 {
		for _, img := range material.Images {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img.Url,
				Height:       img.Height,
				Width:        img.Width,
			})
		}
	}

	if material.Icon != nil {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          material.Icon.Url,
			Height:       material.Icon.Height,
			Width:        material.Icon.Width,
		})
	}
	video25 := 0
	video50 := 0
	video75 := 0
	video100 := 0
	if video != nil {
		video100 = int(video.Duration)
		video25 = video100 / 4
		video50 = video100 / 2
		video75 = video100 * 75 / 100

		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          video.Url,
			Duration:     float64(video100),
			FileSize:     video.Size_,
		})

		if video.Cover != nil {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeCoverImage,
				Url:          video.Cover.Url,
				Width:        video.Cover.Width,
				Height:       video.Cover.Height,
			})
		}
	}

	title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: material.Title}
	if len(material.Title) == 0 {
		title.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, title)

	desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: material.Description}
	if len(material.Description) == 0 {
		desc.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, desc)

	if len(data.Nurl) > 0 {
		info.ImpressionMonitorList = append(info.ImpressionMonitorList, data.Nurl...)
	}

	if tracking != nil {

		if len(tracking.ImpUrls) > 0 {
			info.ImpressionMonitorList = append(info.ImpressionMonitorList, tracking.ImpUrls...)
		}

		if len(tracking.ClickUrls) > 0 {
			info.ClickMonitorList = append(info.ClickMonitorList, tracking.ClickUrls...)
		}

		//for _, expurl := range data.ImpTrackUrls {
		//	timenow := time.Now()
		//	expurl = strings.Replace(expurl, "__UTC_TS__", type_convert.GetAssertString(timenow.Unix()), -1)
		//	info.ImpressionMonitorList = append(info.ImpressionMonitorList, strings.Replace(expurl, "__UTC_TMS__", type_convert.GetAssertString(timenow.UnixMilli()), -1))
		//}

		if len(tracking.VideoStartUrls) > 0 {
			for _, item := range tracking.VideoStartUrls {
				info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   item,
					Delay: 0,
				})
			}
		}
		if len(tracking.VideoFirstQUrls) > 0 {
			for _, item := range tracking.VideoFirstQUrls {
				info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   item,
					Delay: video25,
				})
			}
		}
		if len(tracking.VideoMidUrls) > 0 {
			for _, item := range tracking.VideoMidUrls {
				info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   item,
					Delay: video50,
				})
			}
		}
		if len(tracking.VideoThirdQUrls) > 0 {
			for _, item := range tracking.VideoThirdQUrls {
				info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   item,
					Delay: video75,
				})
			}
		}
		if len(tracking.VideoFinUrls) > 0 {
			for _, item := range tracking.VideoFinUrls {
				info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   item,
					Delay: video100,
				})
			}
		}
		if len(tracking.DownStartUrls) > 0 {
			info.AppDownloadStartedMonitorList = append(info.AppDownloadStartedMonitorList, tracking.DownStartUrls...)
		}
		if len(tracking.DownFinUrls) > 0 {
			info.AppDownloadFinishedMonitorList = append(info.AppDownloadFinishedMonitorList, tracking.DownFinUrls...)
		}

		if len(tracking.DownInstallStartUrls) > 0 {
			info.AppInstallStartMonitorList = append(info.AppInstallStartMonitorList, tracking.DownInstallStartUrls...)
		}
		if len(tracking.DownInstallUrls) > 0 {
			info.AppInstalledFinishMonitorList = append(info.AppInstalledFinishMonitorList, tracking.DownInstallUrls...)
		}
		if len(tracking.DpSuccessUrls) > 0 {
			info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, tracking.DpSuccessUrls...)
		}
		if len(tracking.DpFailedUrls) > 0 {
			info.DeepLinkFailedMonitorList = append(info.DeepLinkFailedMonitorList, tracking.DpFailedUrls...)
		}

		if len(tracking.MiniSuccessUrls) > 0 {
			info.DeepLinkMonitorList = append(info.DeepLinkFailedMonitorList, tracking.MiniSuccessUrls...)
		}
	}

	//宏替换
	info.ImpressionMonitorList = macro_builder.MacroReplaceList(info.ImpressionMonitorList, a.macroInfo)
	info.ClickMonitorList = macro_builder.MacroReplaceList(info.ClickMonitorList, a.macroInfo)

	return info, creative
}

func getCodeMsg(code int32) string {
	switch code {
	case 0:
		return "正常返回素材"
	case 1001:
		return "广告请求异常（含客户被禁用的情况）"
	case 1002:
		return "缺少必填参数"
	case 1004:
		return "广告位 ID 无效"
	case 1005:
		return "广告样式 ID 无效"
	case 1006:
		return "请求的广告位 ID 与广告类型不匹配"
	case 1007:
		return "请求参数异常"
	case 1008:
		return "请求流量受限"
	default:
		return "其他错误"
	}
}

// 映射落地页类型
func mappingLandingType(interactType int32) entity.LandingType {
	switch interactType {
	case 1:
		return entity.LandingTypeInWebView
	case 2:
		return entity.LandingTypeDownload
	case 3:
		return entity.LandingTypeDeepLink
	case 4:
		return entity.LandingTypeWeChatProgram
	default:
		return entity.LandingTypeInWebView
	}
}

func (a *Adx2345DspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return a.slotRegister
}
