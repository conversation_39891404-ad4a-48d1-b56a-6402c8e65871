package iqiyi_dsp_broker

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/iqiyi_dsp_broker/protos"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	objectpool "gitlab.com/dev/heidegger/library/object_pool"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"go.uber.org/zap"
)

var (
	bidRequestPool = objectpool.NewObjectPool(func() *protos.BidRequest {
		return new(protos.BidRequest)
	})
	bidRequestImpPool = objectpool.NewObjectPool(func() *protos.BidRequest_Imp {
		return new(protos.BidRequest_Imp)
	})
	bidRequestVideoPool = objectpool.NewObjectPool(func() *protos.BidRequest_Imp_Video {
		return new(protos.BidRequest_Imp_Video)
	})
	bidRequestNativePool = objectpool.NewObjectPool(func() *protos.BidRequest_Imp_Native {
		return new(protos.BidRequest_Imp_Native)
	})
	bidRequestSitePool = objectpool.NewObjectPool(func() *protos.BidRequest_Site {
		return new(protos.BidRequest_Site)
	})
	bidRequestDevicePool = objectpool.NewObjectPool(func() *protos.BidRequest_Device {
		return new(protos.BidRequest_Device)
	})
	bidRequestGeoPool = objectpool.NewObjectPool(func() *protos.BidRequest_Geo {
		return new(protos.BidRequest_Geo)
	})
	bidRequestAppPool = objectpool.NewObjectPool(func() *protos.BidRequest_App {
		return new(protos.BidRequest_App)
	})
	bidRequestUserPool = objectpool.NewObjectPool(func() *protos.BidRequest_User {
		return new(protos.BidRequest_User)
	})
	bidResponsePool = objectpool.NewObjectPool(func() *protos.BidResponse {
		return new(protos.BidResponse)
	})
)

type IQiYiDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *IQiYiDspSlotRegister
}

func NewIQiYiDspBroker(dspId utils.ID) *IQiYiDspBroker {
	return &IQiYiDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{DspId: dspId},
		slotRegister:  NewIQiYiDspSlotRegister(dspId),
	}
}

// var _ DspBrokerInterface = (*IQiYiDspBroker)(nil)

func (iqy *IQiYiDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := iqy.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	// Slot id in DSP side
	slotKey := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotKey) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}
	candidate.SetDspSlotId(dspSlot.Id)
	candidate.SetDspSlotKey(slotKey)

	// slot size
	var width, height int32
	if len(request.SlotSize) > 0 {
		width = int32(request.SlotSize[0].Width)
		height = int32(request.SlotSize[0].Height)
	} else {
		width = int32(trafficData.GetSlotWidth())
		height = int32(trafficData.GetSlotHeight())
	}

	// Request info
	req := bidRequestPool.Get()
	defer bidRequestPool.Put(req)
	req.Id = trafficData.GetRequestId()
	// no need to use milli precision time
	req.Timestamp = time_utils.GetTimeUnixSecond() * 1000
	if request.App.GetRealAppBundle() == "" && request.Url != "" {
		req.Resourcetype = 0
	} else {
		req.Resourcetype = 1
	}

	// Imp info
	imp := bidRequestImpPool.Get()
	defer bidRequestImpPool.Put(imp)
	// only 1 Imp
	req.Imp = append(req.Imp, imp)
	imp.AdzoneId = slotKey
	imp.MediaAdzoneId = trafficData.GetSourceSlotId()
	imp.AdType = protos.BidRequest_Imp_AdType(dspSlot.AdType) // iqy.mappingSlotType(trafficData.GetSlotType())
	bidFloor := candidate.GetBidFloor()
	imp.Bidfloor = int32(bidFloor.Price)

	switch imp.AdType {
	case protos.BidRequest_Imp_ROLL:
		{
			video := bidRequestVideoPool.Get()
			defer bidRequestVideoPool.Put(video)
			imp.Video = video

			switch trafficData.GetSlotType() {
			case entity.SlotTypeVideoPause:
				video.Linearity = protos.BidRequest_Imp_Video_PAUSE
			case entity.SlotTypeVideoOpening:
				video.Linearity = protos.BidRequest_Imp_Video_LINEAR
			case entity.SlotTypePopup:
				video.Linearity = protos.BidRequest_Imp_Video_NON_LINEAR
			default:
				video.Linearity = protos.BidRequest_Imp_Video_LINEAR
			}
			// currently only support video
			video.AcceptedCreativeTypes = 2
			video.W, video.H = width, height
			video.Minduration, video.Maxduration = request.VideoMinDuration, request.VideoMaxDuration
			video.Startdelay = 0
			video.Maxadscount = 1
			video.Format = protos.BidRequest_Imp_VIDEO_MP4
		}
	case protos.BidRequest_Imp_FEEDS:
		{
			native := bidRequestNativePool.Get()
			defer bidRequestNativePool.Put(native)
			imp.Native = native

			native.TitleLen = 0
			native.Maxadscount = 1
			switch dspSlot.TemplateId {
			case 0:
				{
					native.TitleLen = 90
					native.Imgs = append(native.Imgs, &protos.BidRequest_Imp_Native_Image{
						Type: protos.BidRequest_Imp_Native_Image_MAIN,
						W:    width,
						H:    height,
					})
				}
			case 1:
				{
					native.TitleLen = 90
					native.Imgs = append(native.Imgs, &protos.BidRequest_Imp_Native_Image{
						Type: protos.BidRequest_Imp_Native_Image_MAIN,
						W:    width,
						H:    height,
					})
					native.Video = &protos.BidRequest_Imp_Native_Video{
						W:           width,
						H:           height,
						Minduration: request.VideoMinDuration,
						Maxduration: request.VideoMaxDuration,
						Format:      protos.BidRequest_Imp_VIDEO_MP4,
					}
				}
			default:
				{
					// default choose first template
					for _, key := range request.GetCreativeTemplateKeyList() {
						creativeTemplateKey := creative_entity.CreativeTemplateKey(key)
						if creativeTemplateKey.Title().GetRequiredCount() > 0 {
							native.TitleLen = 90
						}
						if creativeTemplateKey.Image().GetRequiredCount() > 0 {
							for range creativeTemplateKey.Image().GetRequiredCount() {
								native.Imgs = append(native.Imgs, &protos.BidRequest_Imp_Native_Image{
									Type: protos.BidRequest_Imp_Native_Image_MAIN,
									W:    width,
									H:    height,
								})
							}
						}
						if creativeTemplateKey.Icon().GetRequiredCount() > 0 {
							native.Imgs = append(native.Imgs, &protos.BidRequest_Imp_Native_Image{
								Type: protos.BidRequest_Imp_Native_Image_ICON,
							})
						}
						if creativeTemplateKey.Video().GetRequiredCount() > 0 {
							native.Video = &protos.BidRequest_Imp_Native_Video{
								W:           width,
								H:           height,
								Minduration: request.VideoMinDuration,
								Maxduration: request.VideoMaxDuration,
								Format:      protos.BidRequest_Imp_VIDEO_MP4,
							}
						}
						break
					}
				}
			}
		}
	}

	if request.Url != "" {
		// Site info
		site := bidRequestSitePool.Get()
		defer bidRequestSitePool.Put(site)
		req.Site = site
		site.Page = request.Url
		site.Ref = request.Referer
	} else {
		// App info
		app := bidRequestAppPool.Get()
		defer bidRequestAppPool.Put(app)
		req.App = app
		app.Name = trafficData.GetAppName()
		app.Bundle = trafficData.GetAppBundle()
		app.Ver = trafficData.GetAppVersion()
	}

	// Device info
	device := bidRequestDevicePool.Get()
	defer bidRequestDevicePool.Put(device)
	req.Device = device

	geo := bidRequestGeoPool.Get()
	defer bidRequestGeoPool.Put(geo)
	geo.Lat = trafficData.GetGeoLatitude()
	geo.Lon = trafficData.GetGeoLongitude()
	device.Geo = geo

	device.Ua = trafficData.GetUserAgent()
	device.Ip = trafficData.GetRequestIp()
	device.Idfa = trafficData.GetIdfa()
	device.IdfaMd5 = trafficData.GetMd5Idfa()
	device.ImeiMd5 = trafficData.GetMd5Imei()
	device.Androidid = trafficData.GetAndroidId()
	device.AndroididMd5 = trafficData.GetMd5AndroidId()
	device.Oaid = trafficData.GetOaid()
	device.OaidMd5 = trafficData.GetMd5Oaid()
	device.Mac = trafficData.GetMac()
	device.ProcessedMacMd5 = strings.ToLower(trafficData.GetMd5Mac())
	device.Make = iqy.mappingDeviceBrand(trafficData.GetBrand())
	device.Model = trafficData.GetModel()
	device.Osv = trafficData.GetOsVersion()
	device.Os = trafficData.GetOsType().String()
	device.H = trafficData.GetScreenHeight()
	device.W = trafficData.GetScreenWidth()
	device.Devicetype = iqy.mappingDeviceType(trafficData.GetDeviceType())
	device.Carrier, device.CarrierName = iqy.mappingOperatorType(trafficData.GetOperatorType())
	device.Connectiontype = iqy.mappingConnectionType(trafficData.GetConnectionType())
	if len(request.Device.DeviceName) > 0 {
		device.DeviceNameMd5 = md5_utils.GetMd5String(request.Device.DeviceName)
	}
	device.DeviceLanguage = trafficData.GetLanguage()
	device.MachineOfDevice = request.Device.HardwareMachineCode
	device.BootMark = trafficData.GetBootMark()
	device.UpdateMark = trafficData.GetUpdateMark()
	device.DiskTotal = request.Device.SystemTotalDisk
	device.MemTotal = request.Device.SystemTotalMem
	device.FileInitTime = trafficData.GetDeviceInitTime()

	caids := make([]*protos.BidRequest_Device_Caid, 0)
	if len(trafficData.GetCaid()) > 0 {
		caids = append(caids, &protos.BidRequest_Device_Caid{
			Version: device_utils.GetCaidVersion(trafficData.GetCaid()),
			Caid:    device_utils.GetCaidRaw(trafficData.GetCaid()),
		})
	}
	if len(request.Device.Caids) > 0 {
		for _, item := range request.Device.Caids {
			if item == trafficData.GetCaid() {
				continue
			}
			caids = append(caids, &protos.BidRequest_Device_Caid{
				Version: device_utils.GetCaidVersion(item),
				Caid:    device_utils.GetCaidRaw(item),
			})
		}
	}
	if len(caids) > 0 {
		device.CaidInfo = &protos.BidRequest_Device_CaidInfo{
			Caid: caids,
		}
	}

	did, _ := trafficData.GetDeviceIdWithType()

	// User info
	user := bidRequestUserPool.Get()
	defer bidRequestUserPool.Put(user)
	req.User = user
	user.Id = did
	user.Age = iqy.mappingUserAge(request.UserAge)
	user.Gender = iqy.mappingUserGender(request.UserGender)
	user.Applist = strings.Join(request.App.InstalledApp, ",")

	// Build
	httpReq, _, err := iqy.BuildPbHttpHttpRequest(req)
	if err != nil {
		zap.L().Error("[IQiYiDspBroker]BuildPbHttpHttpRequest error", zap.Error(err))
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}
	iqy.SampleDspBroadcastProtobufRequest(iqy.GetDspId(), trafficData.GetDspSlotId(), candidate, req)
	if request.IsDebug {
		payload, _ := json.Marshal(req)
		zap.L().Info("[IQiYiDspBroker]BuildRequest debug", zap.String("request", zap.String("value2", fmt.Sprintf("%v", string(payload)))))
	}
	return httpReq, nil
}

func (iqy *IQiYiDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if response.StatusCode != 200 {
		iqy.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(response.StatusCode), type_convert.GetAssertString(response.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	// Body will be closed in `BroadcastDspClient`
	data, err := io.ReadAll(response.Body)
	if err != nil {
		iqy.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse
	}

	resp := bidResponsePool.Get()
	defer bidResponsePool.Put(resp)
	if err := iqy.ParsePbHttpHttpResponse(response, data, resp); err != nil {
		zap.L().Debug("[IQiYiDspBroker]ParseResponse error", zap.Error(err))
		iqy.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	iqy.SampleDspBroadcastProtobufResponse(iqy.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, resp)
	if request.IsDebug {
		payload, _ := json.Marshal(resp)
		zap.L().Info("[IQiYiDspBroker]ParseResponse debug", zap.String("response", zap.String("value2", fmt.Sprintf("%v", string(payload)))))
	}
	iqy.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.Status), iqy.GetCodeMsg(resp.Status))

	if resp.GetStatus() != 0 {
		//logrus.WithField("status", resp.GetStatus()).Warn("[IQiYiDspBroker]ParseResponse status != 0")
		return nil, err_code.ErrBroadcastResponseStatusFail
	}
	bids := resp.GetBid()
	if len(bids) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}
	// there will only 1 Imp in BidRequest
	bid := bids[0]

	result := make(ad_service.DspAdCandidateList, 0, 1)
	ad := &entity.Ad{
		DspId:         iqy.GetDspId(),
		DspSlotId:     broadcastCandidate.GetDspSlotId(),
		DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
		AdMonitorInfo: iqy.parseCallbacks(bid),
	}

	ad.AppInfo = &entity.AppInfo{
		PackageName: bid.GetApkName(),
		AppName:     bid.GetAppName(),
		AppVersion:  bid.GetAppVersion(),
		Develop:     bid.GetAppDeveloper(),
		Permission:  bid.GetAppPermission(),
		Privacy:     bid.GetAppPrivacy(),
		AppDescURL:  bid.GetAppFeature(),
	}

	if len(ad.AppInfo.AppDescURL) == 0 {
		ad.AppInfo.AppDescURL = bid.GetAppDescPageUrl()
		if len(ad.AppInfo.AppDescURL) == 0 {
			ad.AppInfo.AppDescURL = bid.GetDetailPageUrl()
		}
	}

	// wechat mini program
	if ad.AdMonitorInfo.LandingAction == entity.LandingTypeWeChatProgram {
		path, err := url.QueryUnescape(bid.GetMiniAppPath())
		if err == nil {
			ad.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   bid.GetMiniAppName(),
				ProgramPath: path,
			}
		} else { // fallback to webview
			ad.AdMonitorInfo.LandingAction = entity.LandingTypeInWebView
		}
	}

	// may set monitor info & app info in parseCreative
	creative := iqy.parseCreative(bid, ad)
	if creative == nil {
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	// must replace after parse creative
	iqy.replaceMacro(request, ad.AdMonitorInfo, broadcastCandidate.GetModifiedTrafficData())

	candidate := ad_service.NewDspAdCandidateWithPool(ad)
	candidate.SetAdCandidateChargePriceEncoder(iqy.chargePriceEncoder)
	candidate.SetBidPrice(uint32(bid.GetPrice()))
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetCreative(creative)
	candidate.SetDspProtocol(iqy.GetDspProtocol())
	result = append(result, candidate)

	return result, nil
}

func (iqy *IQiYiDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return iqy.slotRegister
}

func (iqy *IQiYiDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := iqy.PriceManager.GetDspCoder(iqy.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), iqy.GetIKey(), iqy.GetEKey())
	if err != nil {
		zap.L().Error("[IQiYiDspBroker]chargePriceEncoder error", zap.Error(err), zap.String("price", fmt.Sprintf("%v", chargePrice)))
		return ""
	}

	return result
}

func (iqy *IQiYiDspBroker) mappingDeviceType(deviceType entity.DeviceType) protos.BidRequest_Device_DeviceType {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return protos.BidRequest_Device_PHONE
	case entity.DeviceTypePad:
		return protos.BidRequest_Device_PAD
	case entity.DeviceTypeOtt:
		return protos.BidRequest_Device_TV
	case entity.DeviceTypePc:
		return protos.BidRequest_Device_PERSONAL_COMPUTER
	default:
		return protos.BidRequest_Device_PHONE
	}
}

func (iqy *IQiYiDspBroker) mappingConnectionType(connectionType entity.ConnectionType) protos.BidRequest_Device_ConnectionType {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return protos.BidRequest_Device_ETHERNET
	case entity.ConnectionTypeWifi:
		return protos.BidRequest_Device_WIFI
	case entity.ConnectionTypeCellular:
		return protos.BidRequest_Device_CELL_UNKNOWN
	case entity.ConnectionType2G:
		return protos.BidRequest_Device_CELL_2G
	case entity.ConnectionType3G:
		return protos.BidRequest_Device_CELL_3G
	case entity.ConnectionType4G:
		return protos.BidRequest_Device_CELL_4G
	case entity.ConnectionType5G:
		return protos.BidRequest_Device_CELL_5G
	default:
		return protos.BidRequest_Device_CONNECTION_UNKNOWN
	}
}

// map 设备品牌信息
var deviceBrandMap = map[string]string{
	"apple":     "apple",
	"iphone":    "iphone",
	"苹果":        "",
	"vivo":      "vivo",
	"oppo":      "oppo",
	"华为":        "huawei",
	"huawei":    "huawei",
	"小米":        "xiaomi",
	"xiaomi":    "xiaomi",
	"三星":        "samsung",
	"samsung":   "samsung",
	"魅族":        "meizu",
	"meizu":     "meizu",
	"摩托摩拉":      "motorola",
	"motorola":  "motorola",
	"诺基亚":       "nokia",
	"nokia":     "nokia",
	"索尼":        "sony",
	"sony":      "sony",
	"lg":        "lge",
	"锤子":        "smartisan",
	"smartisan": "smartisan",
	"一加":        "oneplLs",
	"oneplLs":   "oneplLs",
	"谷歌":        "nexus",
	"nexus":     "nexus",
	"荣耀":        "honor",
	"honor":     "honor",
	"红米":        "redmi",
	"redmi":     "redmi",
	"realme":    "realme",
	"iqoo":      "iqoo",
	"联想":        "lenovo",
	"lenovo":    "lenovo",
	"金立":        "glonee",
	"glonee":    "glonee",
}

func (iqy *IQiYiDspBroker) mappingDeviceBrand(brand string) string {
	// 判断 brand 是否存在于 deviceBrandMap 中
	lowerBrand := strings.ToLower(brand)
	if value, exists := deviceBrandMap[lowerBrand]; exists {
		return value
	}
	return "other"
}

func (iqy *IQiYiDspBroker) mappingOperatorType(operatorType entity.OperatorType) (int32, string) {
	switch operatorType {
	case entity.OperatorTypeChinaTelecom:
		return 3, "中国电信"
	case entity.OperatorTypeChinaUnicom:
		return 2, "中国联通"
	case entity.OperatorTypeChinaMobile, entity.OperatorTypeTietong:
		return 1, "中国移动"
	default:
		return 0, "unknown"
	}
}

func (iqy *IQiYiDspBroker) mappingSlotType(slotType entity.SlotType) protos.BidRequest_Imp_AdType {
	switch slotType {
	case entity.SlotTypeOpening:
		return protos.BidRequest_Imp_OPENING
	case entity.SlotTypeVideoOpening:
		return protos.BidRequest_Imp_ROLL
	case entity.SlotTypeVideoPause:
		return protos.BidRequest_Imp_PAUSE
	default:
		return protos.BidRequest_Imp_FEEDS
	}
}

func (iqy *IQiYiDspBroker) mappingUserAge(age int32) int32 {
	if age < 1 {
		return 0
	} else if age < 19 {
		return 1
	} else if age < 25 {
		return 2
	} else if age < 31 {
		return 3
	} else if age < 36 {
		return 4
	} else if age < 41 {
		return 5
	} else {
		return 6
	}
}

func (iqy *IQiYiDspBroker) mappingUserGender(gender entity.UserGenderType) string {
	switch gender {
	case entity.UserGenderMan:
		return "M"
	case entity.UserGenderWoman:
		return "F"
	default:
		return ""
	}
}

// Replace macros in monitor urls
func (iqy *IQiYiDspBroker) replaceMacro(request *ad_service.AdRequest, monitor *entity.AdMonitorInfo, trafficData ad_service_entity.TrafficData) {
	replacer := strings.NewReplacer(
		"${AUCTION_PRICE}", "__DSPWPRICE__",
		"CUPID_CCN", "20001",
		"__TARGET_APP_INSTALL__", "__APP_INSTALL__",
	)

	if len(monitor.ImpressionMonitorList) > 0 {
		for idx, url := range monitor.ImpressionMonitorList {
			monitor.ImpressionMonitorList[idx] = replacer.Replace(url)
		}
	}
	if len(monitor.ClickMonitorList) > 0 {
		for idx, url := range monitor.ClickMonitorList {
			monitor.ClickMonitorList[idx] = replacer.Replace(url)
		}
	}
	if len(monitor.AppOpenMonitorList) > 0 {
		for idx, url := range monitor.AppOpenMonitorList {
			monitor.AppOpenMonitorList[idx] = replacer.Replace(url)
		}
	}
}

func (iqy *IQiYiDspBroker) parseCallbackLinks(info *entity.AdMonitorInfo, link *protos.BidResponse_Bid_Link) error {
	info.LandingUrl = link.GetCurl()
	info.DeepLinkUrl = link.GetDeeplink()
	info.ImpressionMonitorList = append(info.ImpressionMonitorList, link.GetImptrackers()...)
	info.ClickMonitorList = append(info.ClickMonitorList, link.GetClicktrackers()...)
	info.VideoStartUrlList = append(info.VideoStartUrlList, link.GetStarttrackers()...)
	info.VideoCloseUrlList = append(info.VideoCloseUrlList, link.GetCompletetrackers()...)
	info.AppOpenMonitorList = append(info.AppOpenMonitorList, link.GetConversionTrackers()...)
	dt := link.GetDownloadtrackers()
	if dt != nil {
		info.AppDownloadStartedMonitorList = append(info.AppDownloadStartedMonitorList, dt.GetStartdownload()...)
		info.AppDownloadFinishedMonitorList = append(info.AppDownloadFinishedMonitorList, dt.GetFinishdownload()...)
		info.AppInstallStartMonitorList = append(info.AppInstallStartMonitorList, dt.GetStartinstall()...)
		info.AppInstalledFinishMonitorList = append(info.AppInstalledFinishMonitorList, dt.GetFinishinstall()...)
	}
	return nil
}

func (iqy *IQiYiDspBroker) parseCallbacks(data *protos.BidResponse_Bid) *entity.AdMonitorInfo {
	info := new(entity.AdMonitorInfo)
	link := data.GetLink()
	if link != nil {
		_ = iqy.parseCallbackLinks(info, link)
	}

	if len(data.GetWinNoticeUrl()) > 0 {
		info.ImpressionMonitorList = append(info.ImpressionMonitorList, data.GetWinNoticeUrl()...)
	}

	switch data.GetAction() {
	case protos.AdActionType_OPEN_APP_DEEPLINK:
		info.LandingAction = entity.LandingTypeDeepLink
	case protos.AdActionType_DOWNLOAD_APP:
		info.LandingAction = entity.LandingTypeDownload
	case protos.AdActionType_OPEN_MINI_APP:
		info.LandingAction = entity.LandingTypeWeChatProgram
	default:
		info.LandingAction = entity.LandingTypeInWebView
	}

	// download app
	if info.LandingAction == entity.LandingTypeDownload {
		if len(data.GetAppDescPageUrl()) > 0 {
			info.LandingUrl = data.GetAppDescPageUrl()
			info.H5LandingUrl = data.GetAppDescPageUrl()
		} else if len(data.GetDetailPageUrl()) > 0 {
			info.LandingUrl = data.GetDetailPageUrl()
			info.H5LandingUrl = data.GetDetailPageUrl()
		}
		if link != nil {
			info.DownloadUrl = link.GetCurl()
		} else {
			info.DownloadUrl = info.LandingUrl
		}
	}

	return info
}

func (iqy *IQiYiDspBroker) parseAdmImage(creative *entity.Creative, img *protos.BidResponse_Bid_Image) error {
	switch img.GetType() {
	case protos.BidResponse_Bid_Image_ICON, protos.BidResponse_Bid_Image_LOGO:
		{
			material := &entity.Material{
				MaterialType: entity.MaterialTypeIcon,
				Url:          img.GetUrl(),
				Width:        img.GetW(),
				Height:       img.GetH(),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	default:
		{
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img.GetUrl(),
				Width:        img.GetW(),
				Height:       img.GetH(),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}
	return nil
}

func (iqy *IQiYiDspBroker) parseAdmVideo(creative *entity.Creative, video *protos.BidResponse_Bid_Video) error {
	material := &entity.Material{
		MaterialType: entity.MaterialTypeVideo,
		Url:          video.GetUrl(),
		Height:       video.GetH(),
		Width:        video.GetW(),
		Duration:     float64(video.GetDuration()),
	}
	creative.MaterialList = append(creative.MaterialList, material)

	if len(video.GetStartCover()) > 0 {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          video.GetStartCover(),
			Height:       video.GetH(),
			Width:        video.GetW(),
		}
		creative.MaterialList = append(creative.MaterialList, materialImg)
	}
	return nil
}

func (iqy *IQiYiDspBroker) parseCreative(data *protos.BidResponse_Bid, ad *entity.Ad) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        data.GetCrid(),
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	// adtype: video opening
	if data.GetAdmvideo() != nil {
		adm := data.GetAdmvideo()
		for _, img := range adm.GetImgs() {
			_ = iqy.parseAdmImage(creative, img)
		}
		if adm.GetVideo() != nil {
			_ = iqy.parseAdmVideo(creative, adm.GetVideo())
		}
		if len(adm.GetTitle()) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeTitle,
				Data:         adm.GetTitle(),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
		if len(adm.GetDesc()) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeDesc,
				Data:         adm.GetDesc(),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
		// monitor info & app
		if adm.GetLink() != nil {
			_ = iqy.parseCallbackLinks(ad.AdMonitorInfo, adm.GetLink())
		}
		if len(adm.GetPackageName()) > 0 {
			ad.AppInfo.PackageName = adm.GetPackageName()
		}
		if len(adm.GetAppName()) > 0 {
			ad.AppInfo.AppName = adm.GetAppName()
		}
		ad.AppInfo.Icon = adm.GetAppIcon()
		if len(adm.GetAppVersion()) > 0 {
			ad.AppInfo.AppVersion = adm.GetAppVersion()
		}
		// adm.GetAdSourceMark()
	}

	// adtype: native
	if data.GetAdmnative() != nil {
		adm := data.GetAdmnative()
		if len(adm.GetTitle()) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeTitle,
				Data:         adm.GetTitle(),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
		for _, img := range adm.GetImgs() {
			_ = iqy.parseAdmImage(creative, img)
		}
		if adm.GetVideo() != nil {
			_ = iqy.parseAdmVideo(creative, adm.GetVideo())
		}
		// monitor info & app
		if adm.GetLink() != nil {
			_ = iqy.parseCallbackLinks(ad.AdMonitorInfo, adm.GetLink())
		}
		if len(adm.GetPackageName()) > 0 {
			ad.AppInfo.PackageName = adm.GetPackageName()
		}
		if len(adm.GetAppName()) > 0 {
			ad.AppInfo.AppName = adm.GetAppName()
		}
		ad.AppInfo.Icon = adm.GetAppIcon()
		if len(adm.GetAppVersion()) > 0 {
			ad.AppInfo.AppVersion = adm.GetAppVersion()
		}
	}

	// adtype: opening
	// data.GetOpening()

	// adtype: opening, pause
	switch data.GetCreativeType() {
	case protos.BidResponse_Bid_VIDEO:
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          data.GetAdUrl(),
			Height:       data.GetAdHeight(),
			Width:        data.GetAdWidth(),
			Duration:     float64(data.GetAdVideo().GetDuration()),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	default:
		material := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          data.GetAdUrl(),
			Height:       data.GetAdHeight(),
			Width:        data.GetAdWidth(),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	// adtype: opening, pause
	if len(data.GetTitle()) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         data.GetTitle(),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}
	if len(data.GetDescription()) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         data.GetDescription(),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}

func (iqy *IQiYiDspBroker) GetCodeMsg(code int32) string {
	switch code {
	case 0:
		return "正常返回"
	case 1:
		return "required必填信息缺失"
	case 2:
		return "贴片缺失video"
	case 3:
		return "信息流缺失native"
	case 4:
		return "native中video或者img至少填一项"
	case 5:
		return "用户ip异常"
	case 6:
		return "devicetype不合法"
	default:
		return utils.EmptyString
	}
}
