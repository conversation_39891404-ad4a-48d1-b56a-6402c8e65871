package sinax_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"strings"
)

type SinaXSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height      int      `json:"height"`
	Width       int      `json:"width"`
	AppId       string   `json:"app_id"`
	AppName     string   `json:"app_name"`
	PkgName     string   `json:"pkg_name"`
	AppVersion  string   `json:"app_version"`
	TemplateIds []string `json:"template_id"`
	AdType      string   `json:"ad_type"`
}

func (info *SinaXSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.AppId, err = dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("[SinaXSlotSlotInfo]get app_id from extra_data failed, err: %v", err)
	}

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	templateIds, err := dspSlotInfo.ExtraData.GetString("template_id")
	if err != nil {

	}
	if len(templateIds) > 0 {
		info.TemplateIds = strings.Split(templateIds, ",")
	}

	info.AdType, err = dspSlotInfo.ExtraData.GetString("ad_type")
	if err != nil {
	}

	return nil
}

type SinaXDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*SinaXSlotSlotInfo
}

func NewSinaXDspSlotRegister(dspId utils.ID) *SinaXDspSlotRegister {
	return &SinaXDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*SinaXSlotSlotInfo),
	}
}

func (r *SinaXDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *SinaXDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*SinaXSlotSlotInfo)
	for _, slot := range list {
		sSlot := &SinaXSlotSlotInfo{}
		if err := sSlot.Init(slot); err != nil {
			zap.L().Error("[SinaXDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = sSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *SinaXDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *SinaXDspSlotRegister) GetSlotInfo(slotId utils.ID) *SinaXSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
