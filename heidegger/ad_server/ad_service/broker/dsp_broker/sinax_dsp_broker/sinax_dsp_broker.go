package sinax_dsp_broker

import (
	"io"
	"net/http"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/sinax_dsp_broker/sinax_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"fmt"
)

type SinaXDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *SinaXDspSlotRegister

	monitorMacro macro_builder.MonitorMacroInfo
}

func NewSinaXDspBroker(dspId utils.ID) *SinaXDspBroker {
	return &SinaXDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{DspId: dspId},
		slotRegister:  NewSinaXDspSlotRegister(dspId),
		monitorMacro: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__AUCTION_PRICE__",
			MacroClickDownX: "__CLKX__",
			MacroClickDownY: "__CLKY__",
			MacroClickUpX:   "__UPX__",
			MacroClickUpY:   "__UPY__",
		},
	}
}

func (impl *SinaXDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *SinaXDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("SinaXDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	sRequest := &sinax_dsp_entity.SinaXBrokerRequest{
		Id:      trafficData.GetRequestId(),
		AdSlots: make([]*sinax_dsp_entity.SinaXAdSlot, 0),
		Device:  impl.encodeDevice(request, trafficData),
		App: &sinax_dsp_entity.SinaXApp{
			AppId:       dspSlot.AppId, //todo
			Name:        trafficData.GetAppName(),
			PackageName: trafficData.GetAppBundle(),
			AppBundleId: trafficData.GetAppBundle(),
			Version:     trafficData.GetAppVersion(),
		},
		//Page: nil,
	}

	if len(dspSlot.PkgName) > 0 {
		sRequest.App.PackageName = dspSlot.PkgName
	}

	if len(dspSlot.AppName) > 0 {
		sRequest.App.Name = dspSlot.AppName
	}

	adSlot := &sinax_dsp_entity.SinaXAdSlot{
		TagId:      slotId,
		TemplateId: []string{"64", "66", "68", "107", "108"},
		SequenceId: 1,
		AdType:     "",
	}

	if len(dspSlot.AdType) > 0 {
		adSlot.AdType = dspSlot.AdType
	} else {
		switch trafficData.GetSlotType() {
		case entity.SlotTypeOpening:
			adSlot.AdType = "screen"
		case entity.SlotTypeBanner:
			adSlot.AdType = "banner"
		case entity.SlotTypePopup:
			adSlot.AdType = "insertScreen"
		default:
			adSlot.AdType = "native"
		}
	}

	if len(dspSlot.TemplateIds) > 0 {
		adSlot.TemplateId = dspSlot.TemplateIds
	} else if adSlot.AdType == "screen" {
		adSlot.TemplateId = []string{"109"}
	}

	sRequest.AdSlots = append(sRequest.AdSlots, adSlot)

	deviceId, _ := trafficData.GetDeviceIdWithType()
	user := &sinax_dsp_entity.SinaXUser{
		UserId:           deviceId,
		Gender:           "UNKNOWN",
		UserCategory:     nil,
		InstalledAppList: request.App.InstalledApp, // UPDATE: Use origin pkg name, not Sina App ID
	}

	switch request.UserGender {
	case entity.UserGenderMan:
		user.Gender = "MALE"
	case entity.UserGenderWoman:
		user.Gender = "FEMALE"
	}

	sRequest.User = user

	req, _, err := impl.BuildSonicJsonHttpRequest(sRequest)
	if err != nil {
		zap.L().Error("SinaXDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}

	//buffer := buffer_pool.NewBufferWriter()
	//defer buffer.Release()
	//_, err := easyjson.MarshalToWriter(sRequest, buffer)
	//if err != nil {
	//	zap.L().Error("SinaXDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
	//	return nil, err_code.ErrBroadcastRequestBuildFail
	//}
	//
	//body := buffer.Get()

	//req, err := http.NewRequest(http.MethodPost, impl.bidUrl, bytes.NewBuffer(body))
	//if err != nil {
	//	zap.L().Error("SinaXDspBroker http NewRequest err", zap.Error(err))
	//	return nil, err
	//}
	//req.Header["Content-Type"] = []string{"application/json"}

	if request.IsDebug {
		requestBody, _ := sonic.Marshal(sRequest)
		zap.L().Info("SinaXDspBroker.EncodeRequest end, saxRequest", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(requestBody))))))
	}

	impl.SampleDspBroadcastSonicJsonRequest(impl.GetDspId(), dspSlot.Id, candidate, sRequest)

	return req, nil

}

func (impl *SinaXDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) *sinax_dsp_entity.SinaXDevice {
	device := &sinax_dsp_entity.SinaXDevice{
		Ua:        trafficData.GetUserAgent(),
		WebviewUa: trafficData.GetWebviewUA(),
		Ip:        trafficData.GetRequestIp(),
		Ipv6:      "",
		Geo: &sinax_dsp_entity.SinaXGeo{
			Lat: float32(request.Device.Lat),
			Lon: float32(request.Device.Lon),
		},
		Brand:          trafficData.GetBrand(),
		Model:          trafficData.GetModel(),
		DeviceType:     impl.mappingDeviceType(trafficData.GetDeviceType()),
		Os:             impl.mappingOs(trafficData.GetOsType()),
		Osv:            trafficData.GetOsVersion(),
		ConnectionType: impl.mappingConnectionType(trafficData.GetConnectionType()),
		Carrier:        impl.mappingCarrier(trafficData.GetOperatorType()),
		Idfa:           trafficData.GetIdfa(),
		IdfaMd5:        trafficData.GetMd5Idfa(),
		Caid:           trafficData.GetCaid(),
		Imei:           trafficData.GetImei(),
		ImeiMd5:        trafficData.GetMd5Idfa(),
		Oaid:           trafficData.GetOaid(),
		AndroidId:      trafficData.GetAndroidId(),
		Sw:             trafficData.GetScreenWidth(),
		Sh:             trafficData.GetScreenHeight(),
	}

	if len(device.WebviewUa) == 0 {
		device.WebviewUa = trafficData.GetUserAgent()
	}

	if trafficData.GetOsType() == entity.OsTypeIOS {
		device.Brand = "apple"
	} else {
		if request.Device.UserAgentParser.BrandId != 0 {
			device.Brand, device.Model = impl.mappingBrandModelById(request.Device.UserAgentParser.BrandId, trafficData.GetBrand(), trafficData.GetModel())
		} else {
			device.Brand, device.Model = impl.mappingBrandModel(trafficData.GetBrand(), trafficData.GetModel())
		}
	}

	return device

}

func (impl *SinaXDspBroker) mappingBrandModelById(brandId uint32, brand, model string) (string, string) {
	switch brandId {
	case 5031, 5033, 9038, 9063:
		return "HUAWEI", "HUAWEI__HUAWEI__" + model
	case 5060:
		return "Xiaomi", "Xiaomi__Xiaomi__" + model
	case 5061:
		return "Xiaomi", "Xiaomi__Redmi__" + model
	case 5032, 5034, 9058, 9064:
		return "HONOR", "HONOR__HONOR__" + model
	case 6040:
		return "OPPO", "OPPO__OPPO__" + model
	case 6050:
		return "vivo", "vivo__vivo__" + model
	case 3080:
		return "samsung", "samsung__samsung__" + model
	case 8060:
		return "OnePlus", "OnePlus__OnePlus__" + model
	case 9021:
		return "realme", "realme__realme__" + model
	default:
		return brand, model
	}
}

func (impl *SinaXDspBroker) mappingBrandModel(brand string, model string) (string, string) {
	if len(brand) == 0 {
		return brand, model
	}
	brandUpper := strings.ToUpper(brand)
	newBrand := ""
	newModel := ""
	if strings.Contains(brandUpper, "HUAWEI") {
		newBrand = "HUAWEI"
		newModel = "HUAWEI__HUAWEI__" + model
	} else if strings.Contains(brandUpper, "XIAOMI") {
		newBrand = "Xiaomi"
		newModel = "Xiaomi__Xiaomi__" + model
	} else if strings.Contains(brandUpper, "REDMI") {
		newBrand = "Xiaomi"
		newModel = "Xiaomi__Redmi__" + model
	} else if strings.Contains(brandUpper, "HONOR") {
		newBrand = "HONOR"
		newModel = "HONOR__HONOR__" + model
	} else if strings.Contains(brandUpper, "OPPO") {
		newBrand = "OPPO"
		newModel = "OPPO__OPPO__" + model
	} else if strings.Contains(brandUpper, "VIVO") {
		newBrand = "vivo"
		newModel = "vivo__vivo__" + model
	} else if strings.Contains(brandUpper, "SAMSUNG") {
		newBrand = "samsung"
		newModel = "samsung__samsung__" + model
	} else if strings.Contains(brandUpper, "ONEPLUS") {
		newBrand = "OnePlus"
		newModel = "OnePlus__OnePlus__" + model
	} else if strings.Contains(brandUpper, "REALME") {
		newBrand = "realme"
		newModel = "realme__realme__" + model
	} else {
		newBrand = brand
		newModel = model
	}

	return newBrand, newModel
}

func (impl *SinaXDspBroker) mappingDeviceType(s entity.DeviceType) int32 {
	switch s {
	case entity.DeviceTypeMobile:
		return 0
	case entity.DeviceTypePad:
		return 1
	default:
		return 0
	}
}

func (impl *SinaXDspBroker) mappingOs(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "ios"
	case entity.OsTypeAndroid:
		return "android"
	default:
		return ""
	}
}

func (impl *SinaXDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int32 {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionTypeCellular:
		return 3
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 0
	}
}

func (impl *SinaXDspBroker) mappingCarrier(carrier entity.OperatorType) string {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return "46000"
	case entity.OperatorTypeChinaTelecom:
		return "46003"
	case entity.OperatorTypeTietong:
		return "46020"
	case entity.OperatorTypeChinaUnicom:
		return "46001"
	default:
		return ""
	}
}

func (impl *SinaXDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		zap.L().Error("[SinaXDspBroker] encode charge price error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return ""
	}

	return result
}

func (impl *SinaXDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("SinaXDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &sinax_dsp_entity.SinaXBidResponse{}

	//err = easyjson.Unmarshal(data, response)
	//if err != nil {
	//	zap.L().Error("SinaXDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
	//	return nil, err_code.ErrBrokerResponseInternalFail
	//}

	resBody, err := impl.ParseSonicJsonHttpResponse(resp, data, response)
	//err = easyjson.Unmarshal(data, response)
	if err != nil {
		zap.L().Error("SinaXDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerParseError
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := sonic.Marshal(response)
		zap.L().Info("SinaXDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	if len(response.SeatBid) < 1 || len(response.SeatBid[0].Bid) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)
	for _, resBid := range response.SeatBid[0].Bid {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		if resBid.Ext.AppInfo != nil {
			candidateAd.AppInfo = &entity.AppInfo{
				PackageName: resBid.Ext.AppInfo.PackageName,
				AppName:     resBid.Ext.AppInfo.Name,
				Icon:        resBid.Ext.AppInfo.IconUrl,
				AppVersion:  resBid.Ext.AppInfo.VersionName,
				AppDesc:     resBid.Ext.AppInfo.Description,
				AppDescURL:  resBid.Ext.AppInfo.DescriptionUrl,
				Develop:     resBid.Ext.AppInfo.Developer,
				Permission:  resBid.Ext.AppInfo.PermissionUrl,
				Privacy:     resBid.Ext.AppInfo.PrivacyUrl,
			}
		}

		if len(resBid.Ext.MiniProgramId) > 0 && len(resBid.Ext.MiniProgramPath) > 0 {
			if candidateAd.AppInfo == nil {
				candidateAd.AppInfo = new(entity.AppInfo)
			}
			candidateAd.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   resBid.Ext.MiniProgramId,
				ProgramPath: resBid.Ext.MiniProgramPath,
			}
		}

		candidateAd.AdExtInfo = &entity.AdExtInfo{
			XRequestWithPackageName: "com.sina.news",
			ReplacedHost:            "saxn.sina.com.cn",
			DeliveryType:            resBid.Ext.DeliveryType,
		}

		if len(resBid.Ext.Host) > 0 {
			candidateAd.AdExtInfo.ReplacedHost = resBid.Ext.Host
		}

		if resBid.Ext.HeadInfo != nil {
			if len(resBid.Ext.HeadInfo.XRequestedWith) > 0 {
				candidateAd.AdExtInfo.XRequestWithPackageName = resBid.Ext.HeadInfo.XRequestedWith
			}

			candidateAd.AdExtInfo.XUserAgent = resBid.Ext.HeadInfo.XUserAgent
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

		candidateCreative := impl.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(resBid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break

	}
	return result, nil
}

func (impl *SinaXDspBroker) ParseCreativeData(bid sinax_dsp_entity.SinaXBrokerResponseSeatBidBid) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.AdId,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if len(bid.Ext.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         bid.Ext.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Ext.Desc) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         bid.Ext.Desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Ext.ImgUrls) > 0 {
		for _, img := range bid.Ext.ImgUrls {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img,
				Width:        bid.Ext.ImgWidth,
				Height:       bid.Ext.ImgHeight,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if bid.Ext.AppInfo != nil && len(bid.Ext.AppInfo.IconUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.Ext.AppInfo.IconUrl,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if bid.Ext.Video != nil && len(bid.Ext.Video.VideoUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          bid.Ext.Video.VideoUrl,
			Duration:     float64(bid.Ext.Video.Duration),
			Width:        bid.Ext.ImgWidth,
			Height:       bid.Ext.ImgHeight,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative

}

func (impl *SinaXDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid sinax_dsp_entity.SinaXBrokerResponseSeatBidBid) *entity.AdMonitorInfo {

	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      impl.monitorMacro.MacroReplaceList(bid.Ext.Cm),
		LandingAction:         entity.LandingTypeInWebView,
		LandingUrl:            bid.Ext.Url,
		H5LandingUrl:          bid.Ext.Url,
	}

	if len(bid.NUrl) > 0 {
		newImpTrack := strings.ReplaceAll(bid.NUrl, "${AUCTION_PRICE}", "__DSPWPRICE__")
		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
	}

	for _, impTrack := range bid.Ext.Pm {
		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impl.monitorMacro.MacroReplace(impTrack))
	}

	for _, impTrack := range bid.Ext.Pv {
		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impl.monitorMacro.MacroReplace(impTrack))
	}
	tracking.DeepLinkUrl = bid.Ext.Deeplink

	if bid.Ext.ActionType == "download" {
		tracking.LandingAction = entity.LandingTypeDownload
		if bid.Ext.AppInfo != nil && len(bid.Ext.AppInfo.DownloadUrl) > 0 {
			tracking.DownloadUrl = bid.Ext.AppInfo.DownloadUrl
			tracking.LandingUrl = bid.Ext.AppInfo.DownloadUrl
		}
	}

	if len(bid.Ext.MiniProgramId) > 0 && len(bid.Ext.MiniProgramPath) > 0 {
		tracking.LandingAction = entity.LandingTypeWeChatProgram
	}

	if bid.Ext.ConversionMonitorInfo != nil {
		monitor := bid.Ext.ConversionMonitorInfo
		tracking.VideoStartUrlList = monitor.FeedPlay
		tracking.VideoCloseUrlList = monitor.FeedOver
		tracking.DeepLinkMonitorList = monitor.DplSuccess
		tracking.DeepLinkFailedMonitorList = monitor.DplFailed
		tracking.AppDownloadStartedMonitorList = monitor.DownloadStart
		tracking.AppDownloadFinishedMonitorList = monitor.DownloadFinish
		tracking.AppInstallStartMonitorList = monitor.InstallStart
		tracking.AppInstalledFinishMonitorList = monitor.InstallFinish
		tracking.AppOpenMonitorList = monitor.AppOpen
		tracking.AppCopyMonitorList = monitor.CopyInApp
		tracking.ClkInLandingMonitorList = monitor.ClickInApp

	}

	return tracking
}
