package magic_dsp_broker

import (
	"io"
	"net/http"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/magic_dsp_broker/magic_dsp_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/hash_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type MagicDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *MagicSlotRegister
	log          *zap.Logger
	macroInfo    macro_builder.MonitorMacroInfo
}

func NewMagicDspBroker(dspId utils.ID) *MagicDspBroker {
	return &MagicDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		slotRegister: NewMagicSlotRegister(dspId),
		log:          zap.L().With(zap.String("broker", "MagicDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice: "${AUCTION_PRICE}",
		},
	}
}

func (a *MagicDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		a.log.WithField("candidates", len(candidateList)).Error("too many candidates")
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := a.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	//dspSlotId
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	imp := &magic_dsp_broker_entity.Imp{
		ID:              request.GetRequestId(),
		Tagid:           slotId,
		Width:           int(trafficData.GetSlotWidth()),
		Height:          int(trafficData.GetSlotHeight()),
		Bidfloor:        int(candidate.GetBidFloor().Price),
		Secure:          1,
		Dp:              1,
		SupportDownload: 1,
	}

	switch trafficData.GetSlotType() {
	case entity.SlotTypeBanner:
		imp.Imptype = 1
	case entity.SlotTypeFeeds:
		imp.Imptype = 2
	case entity.SlotTypeRewardVideo:
		imp.Imptype = 3
	case entity.SlotTypePopup:
		imp.Imptype = 4
	case entity.SlotTypeOpening, entity.SlotTypeVideoOpening:
		imp.Imptype = 5
	default:
		imp.Imptype = 2
	}

	if dspSlot.Width > 0 && dspSlot.Height > 0 {
		imp.Width = dspSlot.Width
		imp.Height = dspSlot.Height
	}

	var apid uint64
	if trafficData.GetAppBundle() != "" {
		apid = hash_utils.XXHash(trafficData.GetAppBundle())
	}

	bidRequest := &magic_dsp_broker_entity.BidRequest{
		ID:  request.GetRequestId(),
		Imp: []*magic_dsp_broker_entity.Imp{imp},
		App: &magic_dsp_broker_entity.App{
			ID:     type_convert.GetAssertString(apid),
			Name:   trafficData.GetAppName(),
			Bundle: trafficData.GetAppBundle(),
			Ver:    trafficData.GetAppVersion(),
		},
		Device: &magic_dsp_broker_entity.Device{
			Ua:             trafficData.GetUserAgent(),
			Ip:             trafficData.GetRequestIp(),
			DeviceType:     mappingDeviceType(trafficData.GetDeviceType()),
			Make:           trafficData.GetBrand(),
			Model:          trafficData.GetModel(),
			Os:             mappingDeviceOs(trafficData.GetOsType()),
			Osv:            trafficData.GetOsVersion(),
			W:              int(trafficData.GetScreenHeight()),
			H:              int(trafficData.GetScreenWidth()),
			Ppi:            int(request.Device.PPI),
			ConnectionType: mappingConnectionType(trafficData.GetConnectionType()),
			Did:            trafficData.GetImei(),
			DidMd5:         trafficData.GetMd5Imei(),
			Oaid:           trafficData.GetOaid(),
			OaidMd5:        trafficData.GetMd5Oaid(),
			AndroidId:      trafficData.GetAndroidId(),
			AndroidIdMd5:   trafficData.GetMd5AndroidId(),
			Carrier:        mappingCarrier(trafficData.GetOperatorType()),
			Geo: &magic_dsp_broker_entity.Geo{
				Lat: trafficData.GetGeoLatitude(),
				Lon: trafficData.GetGeoLongitude(),
			},
			Region:     "CN",
			Lang:       "zh_CN",
			BootMark:   trafficData.GetBootMark(),
			UpdateMark: trafficData.GetUpdateMark(),
			Idfa:       trafficData.GetIdfa(),
			IdfaMd5:    trafficData.GetMd5Idfa(),
			Idfv:       trafficData.GetIdfv(),
			CaidList:   make([]*magic_dsp_broker_entity.CaidList, 0),
			Mac:        trafficData.GetMac(),
			MacMd5:     trafficData.GetMd5Mac(),
			Aaid:       request.Device.Aaid,
			Paid:       request.Device.Paid,
			BootTime:   trafficData.GetDeviceStartupTime(),
			UpdateTime: trafficData.GetDeviceUpgradeTime(),
			BirthTime:  trafficData.GetDeviceInitTime(),
		},
		User: &magic_dsp_broker_entity.User{
			ID:         request.UserId,
			Strategies: request.App.InstalledApp,
		},
	}

	switch request.UserGender {
	case entity.UserGenderMan:
		bidRequest.User.Gender = "M"
	case entity.UserGenderWoman:
		bidRequest.User.Gender = "F"
	}

	if len(trafficData.GetCaid()) != 0 {
		bidRequest.Device.CaidList = append(bidRequest.Device.CaidList, &magic_dsp_broker_entity.CaidList{
			Caid: device_utils.GetCaidRaw(trafficData.GetCaid()),
			Ver:  device_utils.GetCaidVersion(trafficData.GetCaid()),
		})
	}

	if len(request.Device.Caids) > 0 {
		for _, caidItem := range request.Device.Caids {
			if caidItem == trafficData.GetCaid() {
				continue
			}
			caid := &magic_dsp_broker_entity.CaidList{
				Caid: device_utils.GetCaidRaw(caidItem),
				Ver:  device_utils.GetCaidVersion(caidItem),
			}
			bidRequest.Device.CaidList = append(bidRequest.Device.CaidList, caid)
		}
	}

	if request.Device.IsIp6 {
		bidRequest.Device.IpV6 = trafficData.GetRequestIp()
		bidRequest.Device.Ip = ""
	}

	if len(trafficData.GetWebviewUA()) > 0 {
		bidRequest.Device.Ua = trafficData.GetWebviewUA()
	}

	if len(dspSlot.PkgName) > 0 {
		bidRequest.App.Bundle = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidRequest.App.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidRequest.App.Ver = dspSlot.AppVersion
	}

	httpReq, _, err := a.BuildSonicJsonHttpRequest(bidRequest)
	if err != nil {
		a.log.WithError(err).Error("BuildSonicJsonHttpRequest error")
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	a.SampleDspBroadcastSonicJsonRequest(a.DspId, trafficData.GetDspSlotId(), candidate, bidRequest)
	if request.IsDebug {
		payload, _ := sonic.Marshal(bidRequest)
		a.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}
	return httpReq, nil
}

func mappingDeviceOs(osType entity.OsType) string {
	switch osType {
	case entity.OsTypeIOS:
		return "ios"
	case entity.OsTypeAndroid:
		return "android"
	default:
		return "android"
	}
}

func mappingCarrier(carrier entity.OperatorType) string {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return "mobile"
	case entity.OperatorTypeChinaUnicom:
		return "unicom"
	case entity.OperatorTypeChinaTelecom:
		return "telecom"
	default:
		return "unknown"
	}
}

func mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 0
	}
}

func mappingDeviceType(dy entity.DeviceType) int {
	switch dy {
	case entity.DeviceTypeMobile:
		return 4
	case entity.DeviceTypePad:
		return 5
	default:
		return 4
	}
}

func (a *MagicDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if response.StatusCode != 200 {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(response.StatusCode), type_convert.GetAssertString(response.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse
	}

	resp := new(magic_dsp_broker_entity.BidResponse)
	payload, err := a.ParseSonicJsonHttpResponse(response, data, resp)
	if err != nil {
		a.log.WithError(err).WithField("response", response).Error("ParseSonicJsonHttpResponse error")
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		return nil, err_code.ErrBrokerParseError.Wrap(err)
	}

	a.SampleDspBroadcastResponse(a.DspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, payload)
	if request.IsDebug {
		a.log.WithField("resp", string(payload)).Info("ParseResponse debug")
	}
	//a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), resp.ID, "无填充以及响应码")

	if len(resp.Seatbid) < 1 || len(resp.Seatbid[0].Bid) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}
	result := make(ad_service.DspAdCandidateList, 0, 1)
	for _, bid := range resp.Seatbid[0].Bid {
		ad := &entity.Ad{
			DspId:      a.DspId,
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
			AppInfo:    &entity.AppInfo{},
		}

		if bid.DownloadInfo != nil {
			ad.AppInfo.PackageName = bid.DownloadInfo.PkgName
			ad.AppInfo.AppName = bid.DownloadInfo.AppName
			ad.AppInfo.AppVersion = bid.DownloadInfo.AppVersion
			ad.AppInfo.Develop = bid.DownloadInfo.Developer
			ad.AppInfo.AppDescURL = bid.DownloadInfo.Introduction
			ad.AppInfo.AppID = bid.DownloadInfo.BundleID
			ad.AppInfo.Privacy = bid.DownloadInfo.AppPrivacy
			ad.AppInfo.Permission = bid.DownloadInfo.AppPermission
			ad.AppInfo.PackageSize = bid.DownloadInfo.AppSize
		}

		if bid.WxAppid != "" && bid.WxPath != "" {
			ad.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   bid.WxAppid,
				ProgramPath: bid.WxPath,
			}
		}

		adMonitorInfo, creative := a.parseCallbacksAndCreative(bid)
		ad.AdMonitorInfo = adMonitorInfo
		if creative == nil {
			return nil, err_code.ErrCreativeNotFound
		}

		candidate := ad_service.NewDspAdCandidateWithPool(ad)
		candidate.SetAdCandidateChargePriceEncoder(a.chargePriceEncoder)
		candidate.SetBidPrice(uint32(bid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(creative)
		candidate.SetDspAdID(bid.Crid)
		candidate.SetDspProtocol(a.DspProtocol)
		result = append(result, candidate)
		break
	}

	return result, nil
}

func (a *MagicDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := a.PriceManager.GetDspCoder(a.DspProtocol).EncodeWithKey(uint64(chargePrice), a.GetIKey(), a.GetEKey())
	if err != nil {
		a.log.WithError(err).WithField("price", chargePrice).Errorf("chargePriceEncoder error")
		return ""
	}

	return result
}

func (a *MagicDspBroker) parseCallbacksAndCreative(data *magic_dsp_broker_entity.Bid) (*entity.AdMonitorInfo, *entity.Creative) {
	info := &entity.AdMonitorInfo{
		LandingUrl:            data.Ldp,
		DeepLinkUrl:           data.Deeplink,
		ImpressionMonitorList: data.Imptrackers,
		ClickMonitorList:      data.Clicktrackers,
		LandingAction:         mappingLandingType(data),
	}
	if info.LandingAction == entity.LandingTypeDownload {
		info.DownloadUrl = data.Ldp
	}

	if len(data.DownloadStartTrackers) > 0 {
		info.AppDownloadStartedMonitorList = append(info.AppDownloadStartedMonitorList, data.DownloadStartTrackers...)
	}
	if len(data.DownloadCompleteTrackers) > 0 {
		info.AppDownloadFinishedMonitorList = append(info.AppDownloadFinishedMonitorList, data.DownloadCompleteTrackers...)
	}
	if len(data.InstallStartTrackers) > 0 {
		info.AppInstallStartMonitorList = append(info.AppInstallStartMonitorList, data.InstallStartTrackers...)
	}
	if len(data.InstallStartTrackers) > 0 {
		info.AppInstalledFinishMonitorList = append(info.AppInstalledFinishMonitorList, data.InstallCompleteTrackers...)
	}
	if len(data.DpSuccTrackers) > 0 {
		info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, data.DpSuccTrackers...)
	}
	if len(data.DpFailTrackers) > 0 {
		info.DeepLinkFailedMonitorList = append(info.DeepLinkFailedMonitorList, data.DpFailTrackers...)
	}
	if len(data.ActiveAppTrackers) > 0 {
		info.AppOpenMonitorList = data.ActiveAppTrackers
	}

	//assets
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        data.Crid,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}
	if len(data.BrandLogo) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeLogo,
			Url:          data.BrandLogo,
		})
	}

	for _, img := range data.Image {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          img.URL,
			Height:       int32(img.Height),
			Width:        int32(img.Width),
		})
	}

	vid := data.Video
	if vid != nil {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          vid.Vurl,
			Height:       int32(vid.Vw),
			Width:        int32(vid.Vh),
			Duration:     float64(vid.Duration),
			FileSize:     int32(vid.Vsize),
		})
		if len(vid.VMPStart) > 0 {
			info.VideoStartUrlList = append(info.VideoStartUrlList, vid.VMPStart...)
		}
		if len(vid.VMPSucc) > 0 {
			info.VideoCloseUrlList = append(info.VideoCloseUrlList, vid.VMPSucc...)
		}
	}

	title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: data.Title}
	if len(data.Title) == 0 {
		title.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, title)

	desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: data.Desc}
	if len(data.Desc) == 0 {
		desc.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, desc)

	//宏替换
	info.ImpressionMonitorList = macro_builder.MacroReplaceList(info.ImpressionMonitorList, a.macroInfo)
	info.ClickMonitorList = macro_builder.MacroReplaceList(info.ClickMonitorList, a.macroInfo)

	return info, creative
}

// 映射落地页类型
func mappingLandingType(bid *magic_dsp_broker_entity.Bid) entity.LandingType {
	switch bid.Ldptype {
	case 2:
		return entity.LandingTypeDownload
	case 3:
		return entity.LandingTypeDeepLink
	case 4:
		return entity.LandingTypeWeChatProgram
	default:
		return entity.LandingTypeInWebView
	}
}

//func getCodeMsg(code int) string {
//	switch code {
//	case 1000:
//		return "广告请求成功"
//	case 1001:
//		return "请求成功，但是无广告内容"
//	case 1002:
//		return "协议错误"
//	case 1003:
//		return "关键信息无效"
//	default:
//		return "其他错误"
//	}
//}

func (a *MagicDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return a.slotRegister
}
