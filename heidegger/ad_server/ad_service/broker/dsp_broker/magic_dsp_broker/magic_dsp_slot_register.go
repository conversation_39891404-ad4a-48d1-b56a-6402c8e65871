package magic_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"fmt"
)

type MagicSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`
}

func (info *MagicSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	info.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	info.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")

	return nil
}

type MagicSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*MagicSlotInfo
}

func NewMagicSlotRegister(dspId utils.ID) *MagicSlotRegister {
	return &MagicSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*MagicSlotInfo),
	}
}

func (r *MagicSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *MagicSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*MagicSlotInfo)
	for _, slot := range list {
		dspSlot := &MagicSlotInfo{}
		if err := dspSlot.Init(slot); err != nil {
			zap.L().Error("[MagicSlotRegister] init slot failed", zap.Error(err), zap.String("slot", fmt.Sprintf("%v", slot.Id)))
			continue
		}

		slotMap[slot.Id] = dspSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *MagicSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *MagicSlotRegister) GetSlotInfo(slotId utils.ID) *MagicSlotInfo {
	return r.dspSlotMap[slotId]
}
