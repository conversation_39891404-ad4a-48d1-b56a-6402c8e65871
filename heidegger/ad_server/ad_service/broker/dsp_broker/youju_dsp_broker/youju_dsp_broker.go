package youju_dsp_broker

import (
	"bytes"
	"fmt"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/youju_dsp_broker/youju_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"io"
	"net/http"
)

type YouJuDspBroker struct {
	dsp_broker.DspBrokerBase

	dspSlotRegister *YouJuDspSlotRegister

	bidUrl      string
	dspId       utils.ID
	iKey        string
	eKey        string
	dspProtocol string
}

func NewYouJuDspBroker(dspId utils.ID) *YouJuDspBroker {
	return &YouJuDspBroker{
		dspId:           dspId,
		dspSlotRegister: NewYouJuDspSlotRegister(dspId),
	}
}

func (b *YouJuDspBroker) GetDspId() utils.ID {
	return b.dspId
}

func (b *YouJuDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.dspSlotRegister
}

func (b *YouJuDspBroker) UpdateDspInfo(dsp *entity.Dsp) error {
	b.bidUrl = dsp.BidUrl
	b.iKey = dsp.Ikey
	b.eKey = dsp.Ekey
	b.dspProtocol = dsp.Protocol
	return nil
}

func (b *YouJuDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("YouJuDspBroker.EncodeRequest Enter")
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := b.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound.Wrap(fmt.Errorf("slot:%s", trafficData.GetDspSlotId()))
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, fmt.Errorf("empty slot")
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidRequest := &youju_dsp_entity.YouJuRequest{
		Dealid: slotId,
		App: youju_dsp_entity.App{
			ID:     "",
			Name:   dspSlot.AppName,
			Bundle: dspSlot.PkgName,
			Ver:    trafficData.GetAppVersion(),
		},
		Device: youju_dsp_entity.Device{
			DeviceType:   b.mappingDeviceType(trafficData.GetDeviceType()),
			UA:           trafficData.GetUserAgent(),
			IP:           trafficData.GetRequestIp(),
			Make:         trafficData.GetBrand(),
			Model:        trafficData.GetModel(),
			OS:           b.mappingOs(trafficData.GetOsType()),
			OSV:          trafficData.GetOsVersion(),
			IDFA:         trafficData.GetIdfa(),
			IDFAMD5:      trafficData.GetMd5Idfa(),
			IMEI:         trafficData.GetImei(),
			IMEIMD5:      trafficData.GetMd5Imei(),
			OAID:         trafficData.GetOaid(),
			OAIDMD5:      trafficData.GetMd5Oaid(),
			AndroidID:    trafficData.GetAndroidId(),
			AndroidIDMD5: trafficData.GetMd5AndroidId(),
			MAC:          trafficData.GetMac(),
			MACMD5:       trafficData.GetMd5Mac(),
			OpenUDID:     trafficData.GetOpenUdid(),
			CAID:         trafficData.GetCaid(),
			CAIDMD5:      trafficData.GetMd5Caid(),
			Width:        int(trafficData.GetScreenWidth()),
			Height:       int(trafficData.GetScreenHeight()),
		},
	}

	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()
	_, err := easyjson.MarshalToWriter(bidRequest, buffer)
	if err != nil {
		zap.L().Error("YouJuDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err_code.ErrBroadcastRequestBuildFail
	}

	body := buffer.Get()

	if request.IsDebug {
		zap.L().Info("YouJuDspBroker.EncodeRequest end, YouJuRequest", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(body))))))
	}

	req, err := http.NewRequest(http.MethodPost, b.bidUrl, bytes.NewBuffer(body))
	if err != nil {
		zap.L().Error("YouJuDspBroker http NewRequest err", zap.Error(err))
		return nil, err
	}
	req.Header["Content-Type"] = []string{"application/json"}

	b.SampleDspBroadcastRequest(b.dspId, dspSlot.Id, candidate, body)

	return req, nil

}

func (b *YouJuDspBroker) mappingDeviceType(s entity.DeviceType) int {
	switch s {
	case entity.DeviceTypePc:
		return 1
	case entity.DeviceTypePad:
		return 4
	case entity.DeviceTypeMobile:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	default:
		return 0
	}
}

func (b *YouJuDspBroker) mappingOs(os entity.OsType) int {
	switch os {
	case entity.OsTypeIOS:
		return 2
	case entity.OsTypeAndroid:
		return 3
	case entity.OsTypeWindowsPhone:
		return 1
	default:
		return 0
	}
}

func (b *YouJuDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("YouJuDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	if request.IsDebug {
		zap.L().Info("YouJuDspBroker response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(data))))))
	}

	response := &youju_dsp_entity.YouJuResponse{}
	err = easyjson.Unmarshal(data, response)
	if err != nil {
		zap.L().Error("YouJuDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	b.SampleDspBroadcastResponse(b.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, data)

	if response.Code != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	candidateList := make(ad_service.DspAdCandidateList, 0)
	ad := response.Data

	if len(ad.Impressions) == 0 && len(ad.Clicks) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: ad.Impressions,
		ClickMonitorList:      ad.Clicks,
		LandingUrl:            ad.LandingPage,
		LandingAction:         entity.LandingTypeInWebView,
	}

	candidateAd := &entity.Ad{
		DspId:         b.GetDspId(),
		DspSlotId:     broadcastCandidate.GetDspSlotId(),
		DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
		AdMonitorInfo: tracking,
	}

	candidateCreative := &entity.Creative{
		MaterialList: make(entity.MaterialList, 0),
	}

	if len(ad.Images) > 0 {
		for _, img := range ad.Images {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img.URL,
				Width:        int32(img.Width),
				Height:       int32(img.Height),
			}
			candidateCreative.MaterialList = append(candidateCreative.MaterialList, material)
		}
	}

	if ad.Video != nil {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          ad.Video.URL,
			Width:        int32(ad.Video.Width),
			Height:       int32(ad.Video.Height),
		}
		candidateCreative.MaterialList = append(candidateCreative.MaterialList, material)
	}

	if len(ad.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         ad.Title,
		}
		candidateCreative.MaterialList = append(candidateCreative.MaterialList, material)
	}

	candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
	candidate.SetAdCandidateChargePriceEncoder(b.chargePriceEncoder)
	candidate.SetBidPrice(uint32(100))
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetCreative(candidateCreative)
	candidate.SetDspProtocol(b.GetDspProtocol())
	candidateList = append(candidateList, candidate)

	return candidateList, nil
}

func (b *YouJuDspBroker) chargePriceEncoder(chargePrice uint32) string {
	return ""
	result, err := b.PriceManager.GetDspCoder(b.dspProtocol).EncodeWithKey(uint64(chargePrice), b.iKey, b.eKey)
	if err != nil {
		return ""
	}

	return result
}
