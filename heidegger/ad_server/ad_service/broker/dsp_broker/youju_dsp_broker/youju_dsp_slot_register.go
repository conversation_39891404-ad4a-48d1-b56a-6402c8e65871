package youju_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type YouJuSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height  int    `json:"height"`
	Width   int    `json:"width"`
	AppName string `json:"app_name"`
	PkgName string `json:"pkg_name"`
}

func (info *YouJuSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error
	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	return nil
}

type YouJuDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*YouJuSlotSlotInfo
}

func NewYouJuDspSlotRegister(dspId utils.ID) *YouJuDspSlotRegister {
	return &YouJuDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*YouJuSlotSlotInfo),
	}
}

func (r *YouJuDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *YouJuDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*YouJuSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &YouJuSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[YouJuDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *YouJuDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *YouJuDspSlotRegister) GetSlotInfo(slotId utils.ID) *YouJuSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
