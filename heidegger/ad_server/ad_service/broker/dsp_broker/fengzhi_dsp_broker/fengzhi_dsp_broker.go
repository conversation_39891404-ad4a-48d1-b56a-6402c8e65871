package fengzhi_dsp_broker

import (
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	dsp_entity "gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/fengzhi_dsp_broker/fengzhi_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
)

type FengZhiDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *FengZhiDspSlotRegister
	log          *zap.Logger
}

func NewFengZhiDspBroker(dspId utils.ID) *FengZhiDspBroker {
	return &FengZhiDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{DspId: dspId},
		slotRegister:  NewFengZhiDspSlotRegister(dspId),
		log:           zap.L().With(zap.String("broker", "FengZhiDspBroker")),
	}
}

// var _ DspBrokerInterface = (*FengZhiDspBroker)(nil)

func (b *FengZhiDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := b.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	// Slot id in DSP side
	slotKey := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotKey) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}
	candidate.SetDspSlotId(dspSlot.Id)
	candidate.SetDspSlotKey(slotKey)

	// slot size
	var width, height int32
	if dspSlot.Width > 0 && dspSlot.Height > 0 {
		width = int32(dspSlot.Width)
		height = int32(dspSlot.Height)
	}
	if width == 0 || height == 0 {
		if len(request.SlotSize) > 0 {
			width = int32(request.SlotSize[0].Width)
			height = int32(request.SlotSize[0].Height)
		} else {
			width = int32(trafficData.GetSlotWidth())
			height = int32(trafficData.GetSlotHeight())
		}
	}

	// Request info
	req := new(dsp_entity.BidRequest)
	req.Id = trafficData.GetRequestId()
	req.Version = "3.0"
	if request.UseHttps {
		req.IsHttps = 1
	}

	bidFloor := candidate.GetBidFloor()

	// Imp info
	imp := dsp_entity.Impression{
		Id:          req.Id, // request.ImpressionId,
		TagId:       slotKey,
		BidFloor:    float64(bidFloor.Price),
		BidFloorCur: "CNY",
	}

	switch dspSlot.AdType {
	case "banner":
		{
			banner := new(dsp_entity.Banner)
			imp.Banner = banner

			banner.W = int(width)
			banner.H = int(height)
			banner.Pos = 0
			banner.Mimes = []string{"image/jpeg", "image/png", "image/gif"}
			// banner.Btype = []int{}
			// banner.Battr = []int{}
		}
	case "video":
		{
			video := new(dsp_entity.Video)
			imp.Video = video

			video.W = int(width)
			video.H = int(height)
			video.Pos = 0
			video.Mimes = []string{"video/mp4", "video/x-flv", "video/x-msvideo"}
			video.MaxDuration = int(request.VideoMaxDuration)
			video.MinDuration = int(request.VideoMinDuration)
			var videoType = 1
			if trafficData.GetSlotType() == entity.SlotTypeRewardVideo {
				videoType = 2
			}
			video.Ext = dsp_entity.VideoExt{
				Type: videoType,
			}
		}
	case "native":
		{
			native := new(dsp_entity.Native)
			imp.Native = native

			native.Ext = dsp_entity.NativeExt{
				W:              int(width),
				H:              int(height),
				Pos:            0,
				TitleLen:       30, // ref Google ads limit
				DescriptionLen: 90, // ref Google ads limit
				IconW:          100,
				IconH:          100,
			}

			switch dspSlot.TemplateId {
			case 0:
				{
					native.Ext.Img = 1
					native.Ext.Mimes = []string{"image/jpeg", "image/png", "image/gif"}
				}
			case 1:
				{
					native.Ext.Img = 1
					native.Ext.Mimes = []string{"video/mp4"}
				}
			default:
				{
					// default choose first template
					for _, key := range request.GetCreativeTemplateKeyList() {
						creativeTemplateKey := creative_entity.CreativeTemplateKey(key)
						if creativeTemplateKey.Image().GetRequiredCount() > 0 {
							native.Ext.Img = creativeTemplateKey.Image().GetRequiredCount()
							native.Ext.Mimes = append(native.Ext.Mimes, "image/jpeg", "image/png", "image/gif")
						}
						if creativeTemplateKey.Video().GetRequiredCount() > 0 {
							native.Ext.Mimes = append(native.Ext.Mimes, "video/mp4")
						}
						break
					}
					if len(native.Ext.Mimes) == 0 {
						// default allow images
						native.Ext.Img = 1
						native.Ext.Mimes = []string{"image/jpeg", "image/png", "image/gif"}
					}
				}
			}
		}
	}
	// only 1 Imp
	req.Imp = append(req.Imp, imp)

	if request.Url != "" && trafficData.GetAppBundle() == "" {
		// Site info
		site := new(dsp_entity.Site)
		req.Site = site
	} else {
		// App info
		app := new(dsp_entity.App)
		req.App = app
		app.Name = trafficData.GetAppName()
		app.Ver = trafficData.GetAppVersion()
		app.Bundle = trafficData.GetAppBundle()
	}

	// Device info
	device := dsp_entity.Device{
		Ua: trafficData.GetUserAgent(),
		Ip: trafficData.GetRequestIp(),
	}
	if len(trafficData.GetWebviewUA()) > 0 {
		device.Ua = trafficData.GetWebviewUA()
	}
	geo := new(dsp_entity.Geo)
	geo.Lat = trafficData.GetGeoLatitude()
	geo.Lon = trafficData.GetGeoLongitude()
	geo.GeoType = b.mappingGeoType(request.Device.GeoStandard)
	device.Geo = geo
	device.Orientation = b.mappingOrientation(trafficData.GetScreenOrientation())
	device.DeviceType, device.MachineType = b.mappingDeviceType(trafficData.GetDeviceType())
	device.Make = trafficData.GetBrand()
	device.Model = trafficData.GetModel()
	device.Os = trafficData.GetOsType().String()
	device.Osv = trafficData.GetOsVersion()
	device.Oaid = trafficData.GetOaid()
	device.OaidMd5 = trafficData.GetMd5Oaid()
	if len(request.Device.HardwareMachineCode) > 0 {
		device.Hwv = strings.Replace(strings.ToLower(request.Device.HardwareMachineCode), "iphone", "", 1)
	} else {
		device.Hwv = request.Device.HardwareMachineCode // TODO: Android
	}
	device.H = int(trafficData.GetScreenHeight())
	device.W = int(trafficData.GetScreenWidth())
	device.Ppi = int(request.Device.PPI)
	device.Language = trafficData.GetLanguage()
	device.Carrier = b.mappingOperatorType(trafficData.GetOperatorType())
	device.ConnectionType = b.mappingConnectionType(trafficData.GetConnectionType())
	device.Imei = trafficData.GetImei()
	if len(device.Imei) > 0 {
		device.DidSha1 = md5_utils.GetSHA1String(trafficData.GetImei())
	}
	device.DidMd5 = trafficData.GetMd5Imei()
	device.AndroidId = trafficData.GetAndroidId()
	if len(device.AndroidId) > 0 {
		device.DpidSha1 = md5_utils.GetSHA1String(trafficData.GetAndroidId())
	}
	device.Dpidmd5 = trafficData.GetMd5AndroidId()
	device.Mac = trafficData.GetMac()
	device.MacSha1 = md5_utils.GetSHA1String(trafficData.GetMac())
	device.MacMd5 = trafficData.GetMd5Mac()
	device.Idfa = trafficData.GetIdfa()
	device.IfaMd5 = trafficData.GetMd5Idfa()
	device.Idfv = trafficData.GetIdfv()
	device.IdfvMd5 = trafficData.GetMd5Idfv()
	device.OpenUdid = trafficData.GetOpenUdid()
	if len(device.OpenUdid) > 0 {
		device.OpenUdidMd5 = md5_utils.GetMd5String(trafficData.GetOpenUdid())
	}
	device.DeviceStartSec = trafficData.GetDeviceStartupTime()
	device.DeviceUpdateSoc = trafficData.GetDeviceUpgradeTime()
	device.HwName = md5_utils.GetMd5String(request.Device.DeviceName)
	device.BootMark = trafficData.GetBootMark()
	device.UpdateMark = trafficData.GetUpdateMark()
	device.HwMachine = trafficData.GetRomVersion() // TODO:
	device.CountryCode = "CN"
	device.SysMemory = strconv.FormatInt(request.Device.SystemTotalMem, 10)
	device.SysDiskSize = strconv.FormatInt(request.Device.SystemTotalDisk, 10)
	device.JailBreak = "0"
	device.InstalledClientApp = request.App.InstalledApp

	if len(trafficData.GetCaid()) > 0 {
		device.Caid = append(device.Caid, dsp_entity.Caid{
			Version: device_utils.GetCaidVersion(trafficData.GetCaid()),
			Id:      device_utils.GetCaidRaw(trafficData.GetCaid()),
		})
	}
	if len(request.Device.Caids) > 0 {
		for _, item := range request.Device.Caids {
			if item == trafficData.GetCaid() {
				continue
			}
			device.Caid = append(device.Caid, dsp_entity.Caid{
				Version: device_utils.GetCaidVersion(item),
				Id:      device_utils.GetCaidRaw(item),
			})
		}
	}
	req.Device = device

	// User info
	// did, _ := trafficData.GetDeviceIdWithType()
	req.User = &dsp_entity.User{}

	// Build
	httpReq, _, err := b.BuildSonicJsonHttpRequest(req)
	if err != nil {
		b.log.WithError(err).Error("BuildSonicJsonHttpRequest error")
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}
	// XXX: hardcoded here
	httpReq.Header.Set("Authorization", "ppS1DyW/SlZ1O7jHOBQmrA==")
	b.SampleDspBroadcastSonicJsonRequest(b.GetDspId(), trafficData.GetDspSlotId(), candidate, req)
	if request.IsDebug {
		payload, _ := sonic.Marshal(req)
		b.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}
	return httpReq, nil
}

func (b *FengZhiDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	if response.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	// Body will be closed in `BroadcastDspClient`
	data, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse
	}

	resp := new(dsp_entity.BidResponse)
	payload, err := b.ParseSonicJsonHttpResponse(response, data, resp)
	if err != nil {
		b.log.WithError(err).Warn("ParseResponse error")
		return nil, err_code.ErrBrokerParseError.Wrap(err)
	}

	broadcastCandidate := broadcastCandidateList[0]

	b.SampleDspBroadcastResponse(b.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, payload)
	if request.IsDebug {
		payload, _ := sonic.Marshal(resp)
		b.log.WithField("response", string(payload)).Info("ParseResponse debug")
	}

	if len(resp.SeatBid) < 1 || len(resp.SeatBid[0].Bid) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0, 1)
	for _, bid := range resp.SeatBid[0].Bid {
		ad := &entity.Ad{
			DspId:         b.GetDspId(),
			DspSlotId:     broadcastCandidate.GetDspSlotId(),
			DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
			AdMonitorInfo: b.parseCallbacks(&bid),
		}

		ad.AppInfo = &entity.AppInfo{}
		if len(bid.PackageName) > 0 {
			ad.AppInfo.PackageName = bid.PackageName
		}

		creative := b.parseCreative(&bid)
		if creative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		b.replaceMacro(request, ad.AdMonitorInfo, broadcastCandidate.GetModifiedTrafficData())

		candidate := ad_service.NewDspAdCandidateWithPool(ad)
		candidate.SetAdCandidateChargePriceEncoder(b.chargePriceEncoder)
		candidate.SetBidPrice(uint32(bid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(creative)
		candidate.SetDspProtocol(b.GetDspProtocol())
		result = append(result, candidate)
	}

	return result, nil
}

func (b *FengZhiDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.slotRegister
}

func (b *FengZhiDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := b.PriceManager.GetDspCoder(b.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), b.GetIKey(), b.GetEKey())
	if err != nil {
		b.log.WithError(err).WithField("price", chargePrice).Error("chargePriceEncoder error")
		return ""
	}

	return result
}

func (b *FengZhiDspBroker) mappingGeoType(geoStandard int) int {
	switch geoStandard {
	case 0:
		return 3
	case 1:
		return 1
	case 2:
		return 2
	default:
		return 0
	}
}

func (b *FengZhiDspBroker) mappingOrientation(oType entity.ScreenOrientationType) int {
	switch oType {
	case entity.ScreenOrientationTypeLandscape:
		return 2
	case entity.ScreenOrientationTypePortrait:
		return 1
	default:
		return 0
	}
}

func (b *FengZhiDspBroker) mappingDeviceType(deviceType entity.DeviceType) (int, string) {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return 4, "phone"
	case entity.DeviceTypePad:
		return 5, "ipad"
	case entity.DeviceTypePc:
		return 2, "pc"
	case entity.DeviceTypeOtt:
		return 3, "phone"
	default:
		return 1, "phone"
	}
}

func (b *FengZhiDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeWifi, entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (b *FengZhiDspBroker) mappingOperatorType(operatorType entity.OperatorType) string {
	switch operatorType {
	case entity.OperatorTypeChinaTelecom:
		return "3"
	case entity.OperatorTypeChinaUnicom:
		return "2"
	case entity.OperatorTypeChinaMobile, entity.OperatorTypeTietong:
		return "1"
	default:
		return ""
	}
}

// Replace macros in monitor urls
func (b *FengZhiDspBroker) replaceMacro(request *ad_service.AdRequest, monitor *entity.AdMonitorInfo, trafficData ad_service_entity.TrafficData) {
	replacer := strings.NewReplacer(
		"${AUCTION_PRICE}", "__DSPWPRICE__",
	)

	if len(monitor.ImpressionMonitorList) > 0 {
		for idx, url := range monitor.ImpressionMonitorList {
			monitor.ImpressionMonitorList[idx] = replacer.Replace(url)
		}
	}
	if len(monitor.ClickMonitorList) > 0 {
		for idx, url := range monitor.ClickMonitorList {
			monitor.ClickMonitorList[idx] = replacer.Replace(url)
		}
	}
	if len(monitor.AppOpenMonitorList) > 0 {
		for idx, url := range monitor.AppOpenMonitorList {
			monitor.AppOpenMonitorList[idx] = replacer.Replace(url)
		}
	}
	if len(monitor.DeepLinkMonitorList) > 0 {
		for idx, url := range monitor.DeepLinkMonitorList {
			monitor.DeepLinkMonitorList[idx] = replacer.Replace(url)
		}
	}
}

func (b *FengZhiDspBroker) parseCallbacks(data *dsp_entity.Bid) *entity.AdMonitorInfo {
	info := new(entity.AdMonitorInfo)
	info.LandingUrl = data.ClkUrl
	info.DeepLinkUrl = data.Deeplink
	if len(info.DeepLinkUrl) == 0 && len(data.Scheme) > 0 {
		info.DeepLinkUrl = data.Scheme
	}
	info.ImpressionMonitorList = append(info.ImpressionMonitorList, data.Wurl...)
	info.ImpressionMonitorList = append(info.ImpressionMonitorList, data.ImpMonUrl...)
	info.ClickMonitorList = append(info.ClickMonitorList, data.ClkMonUrl...)
	if data.DownloadAd == 0 && len(data.DownloadUrl) > 0 {
		info.DownloadUrl = data.DownloadUrl
	}
	info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, data.Durl...)

	if data.Ext != nil {
		ext := data.Ext
		info.VideoStartUrlList = append(info.VideoStartUrlList, ext.StartPalyUrl...)
		info.VideoCloseUrlList = append(info.VideoCloseUrlList, ext.ClosePalyUrl...)
		info.VideoStartUrlList = append(info.VideoStartUrlList, ext.VideoProgress...) // XXX:
		info.VideoCloseUrlList = append(info.VideoCloseUrlList, ext.Complete...)

		info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, ext.DeeplinkAppInvokeSuccess...)
		info.DeepLinkFailedMonitorList = append(info.DeepLinkFailedMonitorList, ext.DeeplinkAppInvokeFailed...)

		info.AppDownloadStartedMonitorList = append(info.AppDownloadStartedMonitorList, ext.DownloadUrl...)
		info.AppDownloadFinishedMonitorList = append(info.AppDownloadFinishedMonitorList, ext.DownloadedUrl...)
		info.AppInstallStartMonitorList = append(info.AppInstallStartMonitorList, ext.InstallUrl...)
		info.AppInstalledFinishMonitorList = append(info.AppInstalledFinishMonitorList, ext.InstalledUrl...)
		if len(ext.ActivedTrackUrl) > 0 {
			info.ActionCallbackUrl = ext.ActivedTrackUrl[0] // XXX: only allow 1 action callback url?
		}
	}

	if len(info.DeepLinkUrl) > 0 {
		info.LandingAction = entity.LandingTypeDeepLink
	} else if len(info.DownloadUrl) > 0 {
		// TODO: fengzhi does not support download ad, so use webview ad for now
		// info.LandingAction = entity.LandingTypeDownload
		info.LandingAction = entity.LandingTypeInWebView
		info.H5LandingUrl = info.LandingUrl
		info.LandingUrl = data.DownloadUrl
	} else {
		info.LandingAction = entity.LandingTypeInWebView
	}

	return info
}

func (b *FengZhiDspBroker) parseCreative(data *dsp_entity.Bid) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}
	if len(data.CrId) > 0 {
		creative.CreativeKey = data.CrId
	}

	if len(data.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         data.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(data.Description) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         data.Description,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(data.IconUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          data.IconUrl,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	var isVideo = false
	if strings.Contains(data.AdUrl, ".mp4") ||
		strings.Contains(data.AdUrl, ".flv") ||
		strings.Contains(data.AdUrl, ".3gp") ||
		strings.Contains(data.AdUrl, ".avi") {
		isVideo = true
	}

	for _, url := range strings.Split(data.AdUrl, "|") {
		if isVideo {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          url,
				Height:       int32(data.H),
				Width:        int32(data.W),
				Duration:     float64(data.VideoDuration),
				FileSize:     int32(data.VideoSize),
			}
			creative.MaterialList = append(creative.MaterialList, material)
			if len(data.CoverUrl) > 0 {
				cover := &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          data.CoverUrl,
					Height:       int32(data.H),
					Width:        int32(data.W),
				}
				creative.MaterialList = append(creative.MaterialList, cover)
			}
		} else {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          url,
				Height:       int32(data.H),
				Width:        int32(data.W),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	return creative
}
