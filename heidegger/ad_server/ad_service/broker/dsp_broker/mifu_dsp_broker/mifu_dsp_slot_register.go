package mifu_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type MiFuSlotSlotInfo struct {
	*entity.DspSlotInfo
	PkgName    string `json:"pkg_name"`
	AppName    string `json:"app_name"`
	AppVersion string `json:"app_version"`
	ReqType    string `json:"req_type"`
	Did        string `json:"did"`
	Height     int    `json:"height"`
	Width      int    `json:"width"`
}

func (info *MiFuSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.ReqType, err = dspSlotInfo.ExtraData.GetString("req_type")
	if err != nil {
		info.ReqType = "24"
	}

	info.Did, err = dspSlotInfo.ExtraData.GetString("did")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	//if err != nil {
	//	return fmt.Errorf("get height from extra_data failed, err: %v", err)
	//}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	//if err != nil {
	//	return fmt.Errorf("get width from extra_data failed, err: %v", err)
	//}

	return nil
}

type MiFuDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*MiFuSlotSlotInfo
}

func NewMiFuDspSlotRegister(dspId utils.ID) *MiFuDspSlotRegister {
	return &MiFuDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*MiFuSlotSlotInfo),
	}
}

func (r *MiFuDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *MiFuDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*MiFuSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &MiFuSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[MiFuDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *MiFuDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *MiFuDspSlotRegister) GetSlotInfo(slotId utils.ID) *MiFuSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
