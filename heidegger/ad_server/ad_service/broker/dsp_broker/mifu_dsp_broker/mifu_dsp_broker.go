package mifu_dsp_broker

import (
	"encoding/json"
	"github.com/google/go-querystring/query"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/mifu_dsp_broker/mifu_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"io"
	"net/http"
	"net/url"
	"fmt"
)

type MiFuDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *MiFuDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string
}

func NewMiFuDspBroker(dspId utils.ID) *MiFuDspBroker {
	return &MiFuDspBroker{
		slotRegister:  NewMiFuDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "",
	}
}

func (impl *MiFuDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *MiFuDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("MiFuDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("MiFuDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))

		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("MiFuDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	mfRequest := mifu_dsp_entity.BidRequest{
		T:          dspSlot.ReqType,
		A:          slotId,
		Pgn:        trafficData.GetAppBundle(),
		Appname:    trafficData.GetAppName(),
		Appversion: trafficData.GetAppVersion(),
		Ua:         trafficData.GetUserAgent(),
		Nt:         impl.mappingConnectionType(trafficData.GetConnectionType()),
		O:          impl.mappingOperatorType(trafficData.GetOperatorType()),
		Plf:        impl.mappingDeviceType(trafficData.GetDeviceType()),
		Os:         impl.mappingOsType(trafficData.GetOsType()),
		Osv:        trafficData.GetOsVersion(),
		Dev:        trafficData.GetBrand(),
		Md:         trafficData.GetModel(),
		Aid:        trafficData.GetAndroidId(),
		Aaid:       trafficData.GetAaid(),
		Idfa:       trafficData.GetIdfa(),
		Caid:       trafficData.GetCaid(),
		Oid:        trafficData.GetOpenUdid(),
		Uid:        "",
		W:          int(trafficData.GetScreenWidth()),
		H:          int(trafficData.GetScreenHeight()),
		Lon:        trafficData.GetGeoLongitude(),
		Lat:        trafficData.GetGeoLatitude(),
		Mpn:        "",
		Gd:         0,
		Bd:         "",
		Ip:         trafficData.GetRequestIp(),
		Z:          "",
		K:          "",
		Debug:      0,
		Adw:        dspSlot.Width,
		Adh:        dspSlot.Height,
		Sc:         0,
		Pr:         int(trafficData.GetScreenDensity()),
		Ort:        0,
		Tz:         0,
		Did:        dspSlot.Did,
		RequestID:  trafficData.GetRequestId(),
	}

	if len(dspSlot.PkgName) > 0 {
		mfRequest.Pgn = dspSlot.PkgName
	}

	if len(dspSlot.AppName) > 0 {
		mfRequest.Appname = dspSlot.AppName
	}

	if len(dspSlot.AppVersion) > 0 {
		mfRequest.Appversion = dspSlot.AppVersion
	}

	if len(trafficData.GetImei()) > 0 {
		mfRequest.Imeio = trafficData.GetImei()
	} else if len(trafficData.GetMd5Imei()) > 0 {
		mfRequest.Imei = trafficData.GetMd5Imei()
	}

	if len(trafficData.GetMac()) > 0 {
		mfRequest.Mc = trafficData.GetMac()
	} else if len(trafficData.GetMd5Mac()) > 0 {
		mfRequest.Mc = trafficData.GetMd5Mac()
	}

	if len(trafficData.GetOaid()) > 0 {
		mfRequest.Oaid = trafficData.GetOaid()
	} else if len(trafficData.GetMd5Oaid()) > 0 {
		mfRequest.Oaidmd5 = trafficData.GetMd5Oaid()
	}

	querys, _ := query.Values(mfRequest)

	baseURL, _ := url.Parse(impl.GetBidUrl())
	baseURL.RawQuery = querys.Encode()

	bidUrl := baseURL.String()

	if request.IsDebug {
		reqbody, _ := json.Marshal(mfRequest)
		zap.L().Info("MiFuDspBroker.EncodeRequest end, MiFuRequest json Body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(reqbody))))))
		zap.L().Info("MiFuDspBroker.EncodeRequest end, MiFuRequest url", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", bidUrl)))))
	}

	req, err := http.NewRequest(http.MethodGet, bidUrl, nil)
	if err != nil {
		zap.L().Error("MiFuDspBroker http NewRequest err", zap.Error(err))
		return nil, err
	}

	impl.SampleDspBroadcastRequest(impl.dspId, dspSlot.Id, candidate, []byte(bidUrl))
	return req, nil
}

func (impl *MiFuDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionTypeCellular:
		return 4
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (impl *MiFuDspBroker) mappingOperatorType(carrier entity.OperatorType) int {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaTelecom:
		return 3
	case entity.OperatorTypeTietong:
		return 3
	case entity.OperatorTypeChinaUnicom:
		return 2
	default:
		return 0
	}
}

func (impl *MiFuDspBroker) mappingDeviceType(deviceType entity.DeviceType) int {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return 2
	case entity.DeviceTypePad:
		return 1
	default:
		return 2
	}
}

func (impl *MiFuDspBroker) mappingOsType(os entity.OsType) int {
	switch os {
	case entity.OsTypeIOS:
		return 2
	case entity.OsTypeAndroid:
		return 1
	case entity.OsTypeWindowsPhone:
		return 3
	case entity.OsTypeOtt:
		return 4
	default:
		return 1
	}
}

func (impl *MiFuDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("MiFuDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &mifu_dsp_entity.BidResponse{}
	resBody, err := impl.ParseJsonHttpResponse(resp, data, response)
	if err != nil {
		zap.L().Error("MiFuDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		zap.L().Info("MiFuDspBroker raw reponse1", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}

	if len(response.Pm) == 0 && len(response.Cm) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	candidateAd := &entity.Ad{
		DspId:      impl.GetDspId(),
		DspSlotId:  broadcastCandidate.GetDspSlotId(),
		DspSlotKey: broadcastCandidate.GetDspSlotKey(),
	}
	candidateAd.AdMonitorInfo = &entity.AdMonitorInfo{
		ImpressionMonitorList: response.Pm,
		ClickMonitorList:      response.Cm,
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkUrl:           response.Dp,
		LandingUrl:            response.Lp,
		H5LandingUrl:          response.Lp,
	}

	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        response.CrID,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if len(response.CrID) == 0 {
		creative.CreativeKey = response.MediaCrID
	}

	if len(response.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         response.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(response.Desc) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         response.Desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	for _, img := range response.Img {
		if len(img) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if len(response.Icon) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          response.Icon,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(response.MediaFile) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          response.MediaFile,
			Duration:     float64(response.Duration),
		}
		creative.MaterialList = append(creative.MaterialList, material)

		if len(response.Cover) > 0 {

			material1 := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          response.Cover[0],
			}
			creative.MaterialList = append(creative.MaterialList, material1)
		}
	}

	candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
	candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)

	candidate.SetBidPrice(uint32(100))
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetCreative(creative)
	candidate.SetDspProtocol(impl.GetDspProtocol())
	result = append(result, candidate)

	return result, nil
}

func (impl *MiFuDspBroker) chargePriceEncoder(chargePrice uint32) string {
	//明文替换
	result, err := impl.PriceManager.PlaintextEncode(uint64(chargePrice))
	if err != nil {
		return ""
	}

	return result
}
