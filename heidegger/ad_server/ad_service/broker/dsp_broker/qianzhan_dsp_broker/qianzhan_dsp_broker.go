package qianzhan_dsp_broker

import (
	"encoding/json"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/qianzhan_dsp_broker/qianzhan_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"io"
	"net/http"
	"strings"
	"fmt"
)

type QianZhanDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *QianZhanDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string
}

func NewQianZhanDspBroker(dspId utils.ID) *QianZhanDspBroker {
	return &QianZhanDspBroker{
		slotRegister:  NewQianZhanDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "__WIN_PRICE__",
	}
}

func (impl *QianZhanDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *QianZhanDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("QianZhanDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("QianZhanDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))

		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("QianZhanDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidFloor := candidate.GetBidFloor()

	qzRequest := &qianzhan_dsp_entity.Request{
		RequestID: trafficData.GetRequestId(),
		App: qianzhan_dsp_entity.App{
			AppID:      dspSlot.AppId,
			AppPackage: trafficData.GetAppBundle(),
			AppVersion: trafficData.GetAppVersion(),
			AppName:    trafficData.GetAppName(),
		},
		Slot: qianzhan_dsp_entity.Slot{
			SlotID:      slotId,
			SlotWidth:   dspSlot.Width,
			SlotHeight:  dspSlot.Height,
			AdType:      impl.mappingSlotType(trafficData.GetSlotType()),
			MinDuration: int(request.VideoMinDuration),
			MaxDuration: int(request.VideoMaxDuration),
			MaxAdsCount: 1,
			VideoType:   0,
			BidPrice:    int(bidFloor.Price),
		},
		Device: impl.encodeDevice(request, trafficData),
		Network: qianzhan_dsp_entity.Network{
			IP:             trafficData.GetRequestIp(),
			OperatorType:   impl.mappingOperatorType(trafficData.GetOperatorType()),
			ConnectionType: impl.mappingConnectionType(trafficData.GetConnectionType()),
			Lat:            trafficData.GetGeoLatitude(),
			Lon:            trafficData.GetGeoLongitude(),
			CellularID:     "",
		},
	}

	if dspSlot.PkgName != "" {
		qzRequest.App.AppPackage = dspSlot.PkgName
	}

	if request.Device.IsIp6 {
		qzRequest.Network.IPv6 = qzRequest.Network.IP
		qzRequest.Network.IP = ""
	}

	req, _, err := impl.BuildEasyJsonHttpRequest(qzRequest)
	if err != nil {
		zap.L().Error("QianZhanDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}

	//req, err := http.NewRequest(http.MethodPost, impl.GetBidUrl(), bytes.NewBuffer(body))
	//if err != nil {
	//	zap.L().Error("QianZhanDspBroker http NewRequest err", zap.Error(err))
	//	return nil, err
	//}
	//req.Header["Content-Type"] = []string{"application/json"}

	if request.IsDebug {
		zap.L().Info("QianZhanDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", qzRequest.DumpJson())))))
	}

	impl.SampleDspBroadcastEasyjsonRequest(impl.dspId, dspSlot.Id, candidate, qzRequest)

	return req, nil
}

func (impl *QianZhanDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) qianzhan_dsp_entity.Device {
	deviceInfo := qianzhan_dsp_entity.Device{
		IDFA:               trafficData.GetIdfa(),
		IDFAMD5:            trafficData.GetMd5Idfa(),
		IDFV:               trafficData.GetIdfv(),
		IMEI:               trafficData.GetImei(),
		IMEIMD5:            trafficData.GetMd5Imei(),
		AndroidID:          trafficData.GetAndroidId(),
		AndroidIDMD5:       trafficData.GetMd5AndroidId(),
		MAC:                trafficData.GetMac(),
		MACMD5:             trafficData.GetMd5Mac(),
		OAID:               trafficData.GetOaid(),
		OSType:             impl.mappingOsType(trafficData.GetOsType()),
		OSVersion:          trafficData.GetOsVersion(),
		AppstoreVersion:    request.Device.VercodeAg,
		HMSCore:            request.Device.VercodeHms,
		MiuiVersion:        "",
		DeviceType:         impl.mappingDeviceType(trafficData.GetDeviceType()),
		Vendor:             trafficData.GetBrand(),
		Brand:              trafficData.GetBrand(),
		Model:              trafficData.GetModel(),
		ScreenWidth:        int(trafficData.GetScreenWidth()),
		ScreenHeight:       int(trafficData.GetScreenHeight()),
		UA:                 trafficData.GetUserAgent(),
		PPI:                int(request.Device.PPI),
		ScreenOrientation:  0,
		IMSI:               "",
		AppList:            "",
		BootMark:           trafficData.GetBootMark(),
		UpdateMark:         trafficData.GetUpdateMark(),
		DeviceStartSec:     trafficData.GetBootMark(),
		DeviceNameMD5:      md5_utils.GetMd5String(trafficData.GetBrand()),
		HardwareMachine:    request.Device.HardwareMachineCode,
		PhysicalMemoryByte: "",
		HarddiskSizeByte:   "",
		SystemUpdateSec:    trafficData.GetUpdateMark(),
		HardwareModel:      request.Device.DeviceName,
		Country:            "CN",
		Language:           "zh-Hans-CN",
		TimeZone:           "28800",
		WXAPIVer:           0,
		WXInstalled:        true,
		SDFreeSpace:        "",
		WifiName:           "",
		Paid:               request.Device.Paid,
		Density:            int(trafficData.GetScreenDensity()),
	}

	if len(trafficData.GetCaid()) > 0 {
		deviceInfo.Caid = append(deviceInfo.Caid, qianzhan_dsp_entity.Caid{
			ID:      device_utils.GetCaidRaw(trafficData.GetCaid()),
			Version: device_utils.GetCaidVersion(trafficData.GetCaid()),
		})
	}

	if len(request.Device.Caids) > 0 {
		for _, caidItem := range request.Device.Caids {
			if caidItem == trafficData.GetCaid() {
				continue
			}
			deviceCaid1 := qianzhan_dsp_entity.Caid{
				ID:      device_utils.GetCaidRaw(caidItem),
				Version: device_utils.GetCaidVersion(caidItem),
			}
			deviceInfo.Caid = append(deviceInfo.Caid, deviceCaid1)
		}
	}

	if trafficData.GetScreenOrientation() == entity.ScreenOrientationTypePortrait {
		deviceInfo.ScreenOrientation = 1
	} else if trafficData.GetScreenOrientation() == entity.ScreenOrientationTypeLandscape {
		deviceInfo.ScreenOrientation = 2
	}

	return deviceInfo

}

func (impl *QianZhanDspBroker) mappingDeviceType(deviceType entity.DeviceType) int {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	default:
		return 1
	}
}

func (impl *QianZhanDspBroker) mappingOsType(os entity.OsType) int {
	switch os {
	case entity.OsTypeIOS:
		return 2
	case entity.OsTypeAndroid:
		return 1
	default:
		return 0
	}
}

func (impl *QianZhanDspBroker) mappingSlotType(s entity.SlotType) int {
	switch s {
	case entity.SlotTypeBanner:
		return 1
	case entity.SlotTypeOpening:
		return 2
	case entity.SlotTypePopup:
		return 3
	case entity.SlotTypeFeeds:
		return 4
	case entity.SlotTypeRewardVideo:
		return 5
	case entity.SlotTypeVideo:
		return 7
	default:
		return 4
	}
}

func (impl *QianZhanDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 101
	case entity.ConnectionTypeWifi:
		return 100
	case entity.ConnectionTypeCellular:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (impl *QianZhanDspBroker) mappingOperatorType(carrier entity.OperatorType) int {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaTelecom:
		return 2
	case entity.OperatorTypeTietong:
		return 2
	case entity.OperatorTypeChinaUnicom:
		return 3
	default:
		return 0
	}
}

func (impl *QianZhanDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("QianZhanDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &qianzhan_dsp_entity.Response{}

	resBody, err := impl.ParseJsonHttpResponse(resp, data, response)
	//err = easyjson.Unmarshal(data, response)
	if err != nil {
		zap.L().Error("QianZhanDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := json.Marshal(response)
		zap.L().Info("QianZhanDspBroker raw reponse1", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	if response.ErrorCode != 200 || len(response.Ads) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, resBid := range response.Ads {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		if len(resBid.MetaGroup) == 0 {
			continue
		}

		meta := resBid.MetaGroup[0]

		candidateAd.AppInfo = &entity.AppInfo{
			PackageName: meta.PackageName,
			AppName:     meta.BrandName,
			WechatExt:   nil,
			AppID:       meta.IOSAppID,
			AppVersion:  meta.AppVersion,
			PackageSize: meta.AppSize,
			Privacy:     meta.PrivacyPolicyURL,
			Permission:  meta.PermissionURL,
			AppDescURL:  meta.AppDescURL,
			Develop:     meta.DeveloperName,
		}

		for _, per := range meta.Permission {
			candidateAd.AppInfo.PermissionDesc = append(candidateAd.AppInfo.PermissionDesc, entity.PermissionDesc{
				PermissionLab:  per.PermissionName,
				PermissionDesc: per.PermissionDesc,
			})
		}

		if len(meta.IconURLs) > 0 {
			candidateAd.AppInfo.Icon = meta.IconURLs[0]
		}

		if meta.WechatAppID != "" && meta.WechatAppPath != "" {
			candidateAd.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   meta.WechatAppID,
				ProgramPath: meta.WechatAppPath,
			}
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

		candidateCreative := impl.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(resBid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(resBid.AdKey)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break

	}
	return result, nil
}

func (impl *QianZhanDspBroker) chargePriceEncoder(chargePrice uint32) string {
	//明文替换
	result, err := impl.PriceManager.PlaintextEncode(uint64(chargePrice))
	//result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *QianZhanDspBroker) ParseCreativeData(bid qianzhan_dsp_entity.Ad) *entity.Creative {
	item := bid.MetaGroup[0]

	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.AdKey,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if len(item.AdTitle) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         item.AdTitle,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Descs) > 0 {
		for _, desc := range item.Descs {
			if len(desc) > 0 {
				material := &entity.Material{
					MaterialType: entity.MaterialTypeDesc,
					Data:         desc,
				}
				creative.MaterialList = append(creative.MaterialList, material)
			}
		}
	}

	for _, icon := range item.IconURLs {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          icon,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	for _, image := range item.ImageURL {
		if len(image) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          image,
				Width:        int32(item.MaterialWidth),
				Height:       int32(item.MaterialHeight),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if len(item.VideoURL) > 0 {

		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          item.VideoURL,
			Width:        int32(item.MaterialWidth),
			Height:       int32(item.MaterialHeight),
			Duration:     float64(item.VideoDuration),
		}
		creative.MaterialList = append(creative.MaterialList, material)

		if len(item.VideoCoverURL) > 0 {
			material1 := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          item.VideoCoverURL,
				Width:        int32(item.VideoCoverWidth),
				Height:       int32(item.VideoCoverHeight),
			}
			creative.MaterialList = append(creative.MaterialList, material1)
		}

	}

	return creative

}

func (impl *QianZhanDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid qianzhan_dsp_entity.Ad) *entity.AdMonitorInfo {
	meta := bid.MetaGroup[0]

	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      meta.WinCNoticeUrls,
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkUrl:           meta.DeepLinkURL,
		LandingUrl:            meta.ClickURL,
	}

	if meta.InteractionType == 2 {
		tracking.LandingAction = entity.LandingTypeDownload
	} else if meta.DeepLink {
		tracking.LandingAction = entity.LandingTypeDeepLink
	}

	if request.Device.OsType == entity.OsTypeIOS && meta.UniversalLink != "" {
		tracking.DeepLinkUrl = meta.UniversalLink
	}

	if len(meta.WinNoticeUrls) > 0 {
		for _, impUrl := range meta.WinNoticeUrls {
			if strings.Contains(impUrl, impl.MacroWinPrice) {
				newImpTrack := strings.ReplaceAll(impUrl, impl.MacroWinPrice, "__DSPWPRICE__")
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
			} else {
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impUrl)
			}
		}
	}

	//if len(meta.WinCNoticeUrls) > 0 {
	//	tracking.ClickMonitorList = append(tracking.ClickMonitorList, meta.WinCNoticeUrls...)
	//}

	if len(meta.WinLoadUrls) > 0 {
		tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, meta.WinLoadUrls...)
	}

	if len(meta.WinDownloadUrls) > 0 {
		tracking.AppDownloadStartedMonitorList = append(tracking.AppDownloadStartedMonitorList, meta.WinDownloadUrls...)
	}

	if len(meta.WinDownloadEndUrls) > 0 {
		tracking.AppDownloadFinishedMonitorList = append(tracking.AppDownloadFinishedMonitorList, meta.WinDownloadEndUrls...)
	}

	if len(meta.WinInstallUrls) > 0 {
		tracking.AppInstallStartMonitorList = append(tracking.AppInstallStartMonitorList, meta.WinInstallUrls...)
	}

	if len(meta.WinInstallUrls) > 0 {
		tracking.AppInstalledFinishMonitorList = append(tracking.AppInstalledFinishMonitorList, meta.WinInstallUrls...)
	}

	if len(meta.WinActiveUrls) > 0 {
		tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, meta.WinActiveUrls...)
	}

	if len(meta.WinDeepLinkUrls) > 0 {
		tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, meta.WinDeepLinkUrls...)
	}

	if len(meta.WinDeepLinkSuccessUrls) > 0 {
		tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, meta.WinDeepLinkSuccessUrls...)
	}

	video25 := 0
	video50 := 0
	video75 := 0

	if meta.VideoDuration > 0 {
		video25 = meta.VideoDuration / 4
		video50 = meta.VideoDuration / 2
		video75 = meta.VideoDuration * 75 / 100
	}

	for _, track := range bid.Tracks {
		switch track.Type {
		case 0:
			tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, track.Urls...)
		case 1:
			if video25 != 0 {
				for _, item := range track.Urls {
					tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
						Url:   item,
						Delay: video25,
					})
				}
			}
		case 2:
			if video50 != 0 {
				for _, item := range track.Urls {
					tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
						Url:   item,
						Delay: video50,
					})
				}
			}
		case 3:
			if video75 != 0 {
				for _, item := range track.Urls {
					tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
						Url:   item,
						Delay: video75,
					})
				}
			}
		case 4:
			tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, track.Urls...)
		}

	}

	return tracking

}
