package qianzhan_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type QianZhanSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height  int    `json:"height"`
	Width   int    `json:"width"`
	AppId   string `json:"app_id"`
	PkgName string `json:"pkg_name"`
}

func (info *QianZhanSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error
	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	info.AppId, err = dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get app_id from extra_data failed, err: %v", err)
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	return nil
}

type QianZhanDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*QianZhanSlotSlotInfo
}

func NewQianZhanDspSlotRegister(dspId utils.ID) *QianZhanDspSlotRegister {
	return &QianZhanDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*QianZhanSlotSlotInfo),
	}
}

func (r *QianZhanDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *QianZhanDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*QianZhanSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &QianZhanSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[QianZhanDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *QianZhanDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *QianZhanDspSlotRegister) GetSlotInfo(slotId utils.ID) *QianZhanSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
