package chengxiao_dsp_broker

import (
	"io"
	"net/http"
	"strconv"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/chengxiao_dsp_broker/chengxiao_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	objectpool "gitlab.com/dev/heidegger/library/object_pool"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

var (
	bidRequestPool = objectpool.NewObjectPool(func() *chengxiao_proto.BidRequest3 {
		return new(chengxiao_proto.BidRequest3)
	})
	bidRequestDevicePool = objectpool.NewObjectPool(func() *chengxiao_proto.BidRequestDevice {
		return new(chengxiao_proto.BidRequestDevice)
	})
	bidRequestGeoPool = objectpool.NewObjectPool(func() *chengxiao_proto.BidRequestGeo {
		return new(chengxiao_proto.BidRequestGeo)
	})
	bidRequestImpPool = objectpool.NewObjectPool(func() *chengxiao_proto.BidRequestImp {
		return new(chengxiao_proto.BidRequestImp)
	})
	bidRequestAppPool = objectpool.NewObjectPool(func() *chengxiao_proto.BidRequestApp {
		return new(chengxiao_proto.BidRequestApp)
	})
	bidRequestUserPool = objectpool.NewObjectPool(func() *chengxiao_proto.BidRequestUser {
		return new(chengxiao_proto.BidRequestUser)
	})
	bidResponsePool = objectpool.NewObjectPool(func() *chengxiao_proto.BidResponse3 {
		return new(chengxiao_proto.BidResponse3)
	})
)

type ChengXiaoDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *ChengXiaoDspSlotRegister
	log          *zap.Logger
	macroInfo    macro_builder.MonitorMacroInfo
}

func NewChengXiaoDspBroker(dspId utils.ID) *ChengXiaoDspBroker {
	return &ChengXiaoDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{DspId: dspId},
		slotRegister:  NewChengXiaoDspSlotRegister(dspId),
		log:           zap.L().With(zap.String("broker", "ChengXiaoDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__win_price__",
			MacroClickDownX: "__clk_x__",
			MacroClickDownY: "__clk_y__",
			MacroClickUpX:   "__clk_up_x__",
			MacroClickUpY:   "__clk_up_y__",
		},
	}
}

func (impl *ChengXiaoDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *ChengXiaoDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		impl.log.Errorf("candidateList len: %d", len(candidateList))
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		impl.log.Errorf("dspSlot not found: %d", trafficData.GetDspSlotId())
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidReq := bidRequestPool.Get()
	defer bidRequestPool.Put(bidReq)

	bidReq.Id = request.GetRequestId()

	bidReqApp := bidRequestAppPool.Get()
	defer bidRequestAppPool.Put(bidReqApp)
	bidReq.App = bidReqApp

	bidReqApp.Id = dspSlot.AppId
	bidReqApp.Name = trafficData.GetAppName()
	bidReqApp.Bundle = trafficData.GetAppBundle()
	bidReqApp.Version = trafficData.GetAppVersion()

	bidReqDevice := bidRequestDevicePool.Get()
	defer bidRequestDevicePool.Put(bidReqDevice)
	bidReq.Device = bidReqDevice

	impl.buildDevice(bidReqDevice, request, trafficData)

	bidReqGeo := bidRequestGeoPool.Get()
	defer bidRequestGeoPool.Put(bidReqGeo)
	bidReq.Geo = bidReqGeo

	bidReqGeo.Ctype = impl.mappingGeoType(request.Device.GeoStandard)
	bidReqGeo.Lat = float32(trafficData.GetGeoLatitude())
	bidReqGeo.Lon = float32(trafficData.GetGeoLongitude())

	bidReqUser := bidRequestUserPool.Get()
	defer bidRequestUserPool.Put(bidReqUser)
	bidReq.User = bidReqUser

	bidReqUser.Uid = request.UserId
	bidReqUser.Age = request.UserAge
	bidReqUser.Gender = chengxiao_proto.MobReqGenderType(request.UserGender)

	bidReqImp := bidRequestImpPool.Get()
	defer bidRequestImpPool.Put(bidReqImp)
	bidReq.Imp = bidReqImp

	impl.buildImp(bidReqImp, request, candidate, trafficData, dspSlot)

	if len(dspSlot.PkgName) > 0 {
		bidReqApp.Bundle = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidReqApp.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidReqApp.Version = dspSlot.AppVersion
	}

	httpReq, _, err := impl.BuildPbHttpHttpRequest(bidReq)
	if err != nil {
		impl.log.WithError(err).Error("BuildPbHttpHttpRequest error")
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}
	httpReq.Header.Set("Content-Type", "application/x-protobuf")
	httpReq.Header.Set("Version", "2.2")
	impl.SampleDspBroadcastProtobufRequest(impl.GetDspId(), trafficData.GetDspSlotId(), candidate, bidReq)

	if request.IsDebug {
		payload, _ := sonic.Marshal(bidReq)
		impl.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}

	return httpReq, nil
}

func (impl *ChengXiaoDspBroker) buildDevice(bidReqDevice *chengxiao_proto.BidRequestDevice, request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) {
	bidReqDevice.Ua = trafficData.GetUserAgent()
	if request.Device.IsIp6 {
		bidReqDevice.Ipv6 = trafficData.GetRequestIp()
	} else {
		bidReqDevice.Ip = trafficData.GetRequestIp()
	}
	bidReqDevice.DeviceType = impl.mappingDeviceType(trafficData.GetDeviceType())
	bidReqDevice.Make = trafficData.GetBrand()
	bidReqDevice.Model = trafficData.GetModel()
	bidReqDevice.Os = impl.mappingOsType(trafficData.GetOsType())
	bidReqDevice.Osv = trafficData.GetOsVersion()
	bidReqDevice.Carrier = impl.mappingCarrier(trafficData.GetOperatorType())
	bidReqDevice.ConnectionType = impl.mappingConnectionType(trafficData.GetConnectionType())
	bidReqDevice.Idfa = trafficData.GetIdfa()
	bidReqDevice.IdfaMd5 = trafficData.GetMd5Idfa()
	bidReqDevice.OpenUdId = trafficData.GetOpenUdid()
	bidReqDevice.Idfv = trafficData.GetIdfv()
	bidReqDevice.IdfvMd5 = trafficData.GetMd5Idfv()
	bidReqDevice.Imei = trafficData.GetImei()
	bidReqDevice.ImeiMd5 = trafficData.GetMd5Imei()
	bidReqDevice.AndroidId = trafficData.GetAndroidId()
	bidReqDevice.AndroidIdMd5 = trafficData.GetMd5AndroidId()
	bidReqDevice.Mac = trafficData.GetMac()
	bidReqDevice.MacMd5 = trafficData.GetMd5Mac()
	bidReqDevice.W = trafficData.GetScreenWidth()
	bidReqDevice.H = trafficData.GetScreenHeight()
	bidReqDevice.Oaid = trafficData.GetOaid()
	bidReqDevice.OaidMd5 = trafficData.GetMd5Oaid()
	bidReqDevice.Dpr = float32(request.Device.PPI / 160.0)
	bidReqDevice.Ppi = request.Device.PPI
	bidReqDevice.Caid = device_utils.GetCaidRaw(trafficData.GetCaid())
	bidReqDevice.CaidVer = device_utils.GetCaidVersion(trafficData.GetCaid())
	bidReqDevice.BootMark = trafficData.GetBootMark()
	bidReqDevice.UpdateMark = trafficData.GetUpdateMark()
	bidReqDevice.RomV = trafficData.GetRomVersion()
	bidReqDevice.HmsCore = request.Device.VercodeHms
	bidReqDevice.HmsV = request.Device.VercodeAg
	// bidReqDevice.MiuiV =
	bidReqDevice.Syst = request.Device.SystemCompileTime
	bidReqDevice.SystemUpdateSec = trafficData.GetDeviceUpgradeTime()
	bidReqDevice.SystemInitSec = trafficData.GetDeviceInitTime()
	bidReqDevice.SystemStartSec = trafficData.GetDeviceStartupTime()
	bidReqDevice.DeviceName = request.Device.DeviceName
	bidReqDevice.St = impl.mappingOrientation(trafficData.GetScreenOrientation())
	bidReqDevice.Paid = request.Device.Paid
	bidReqDevice.TimeZone = "Asia/Shanghai"
	if request.Device.SystemTotalMem > 0 {
		bidReqDevice.MemorySize = strconv.FormatInt(request.Device.SystemTotalMem, 10)
	}
	if request.Device.SystemTotalDisk > 0 {
		bidReqDevice.DiskSize = strconv.FormatInt(request.Device.SystemTotalDisk, 10)
	}
	bidReqDevice.AliAdId = trafficData.GetAaid()
	// bidReqDevice.CpuFreq =
	// bidReqDevice.CpuNumber =
	bidReqDevice.BatteryStatus = 1
	bidReqDevice.BatteryPower = 0
	bidReqDevice.Con = "CN"
	bidReqDevice.Lan = "zh"
	bidReqDevice.HardwareMachine = request.Device.HardwareMachineCode
	if len(bidReqDevice.Idfa) > 0 {
		bidReqDevice.AuthStatus = 3
	} else {
		bidReqDevice.AuthStatus = 1
	}
	// bidReqDevice.Skanvs =
	bidReqDevice.AppList = request.App.InstalledApp
	// bidReqDevice.ApiLevel =
	bidReqDevice.AppStoreVer = request.Device.AppStoreVersion

	if len(trafficData.GetWebviewUA()) > 0 {
		bidReqDevice.Ua = trafficData.GetWebviewUA()
	}

	if trafficData.GetOsType() == entity.OsTypeIOS {
		bidReqDevice.HardwareModel = trafficData.GetModel()
	}

	if len(bidReqDevice.DeviceName) == 0 {
		bidReqDevice.DeviceName = trafficData.GetRomName()
		if len(bidReqDevice.DeviceName) == 0 {
			bidReqDevice.DeviceName = trafficData.GetModel()
		}
	}

	if len(bidReqDevice.DeviceName) > 0 {
		bidReqDevice.DeviceNameMd5 = md5_utils.GetMd5String(bidReqDevice.DeviceName)
	}

	if trafficData.GetOsType() == entity.OsTypeIOS &&
		len(bidReqDevice.Paid) == 0 &&
		len(trafficData.GetDeviceInitTime()) > 0 &&
		len(trafficData.GetDeviceUpgradeTime()) > 0 &&
		len(trafficData.GetDeviceStartupTime()) > 0 {
		bidReqDevice.Paid = string_utils.ConcatString(
			md5_utils.GetMd5String(trafficData.GetDeviceInitTime()),
			"-",
			md5_utils.GetMd5String(trafficData.GetDeviceUpgradeTime()),
			"-",
			md5_utils.GetMd5String(trafficData.GetDeviceStartupTime()))
	}
}

func (impl *ChengXiaoDspBroker) buildImp(
	bidReqImp *chengxiao_proto.BidRequestImp,
	request *ad_service.AdRequest,
	candidate *ad_service.AdCandidate,
	trafficData ad_service_entity.TrafficData,
	dspSlot *ChengXiaoDspSlotInfo) {
	bidReqImp.Id = request.ImpressionId
	if len(bidReqImp.Id) == 0 {
		bidReqImp.Id = request.GetRequestId()
	}
	bidReqImp.SlotId = candidate.GetDspSlotKey()
	bidReqImp.SlotType = impl.mappingSlotType(trafficData.GetSlotType())
	bidReqImp.Bidfloor = candidate.GetBidFloor().Price
	if request.UseHttps {
		bidReqImp.IsSupportHttps = chengxiao_proto.MobReqSupportHttpsType_MobReqSupportHttpsType_Https
	}
	bidReqImp.IsDeeplink = 0
	bidReqImp.TitleLen = 90
	bidReqImp.DesLen = 90
	bidReqImp.IsSupport302 = 1

	var w, h = uint32(dspSlot.Width), uint32(dspSlot.Height)
	if w == 0 || h == 0 {
		w = trafficData.GetSlotWidth()
		h = trafficData.GetSlotHeight()
	}
	if (w == 0 || h == 0) && len(request.SlotSize) > 0 {
		w = uint32(request.SlotSize[0].Width)
		h = uint32(request.SlotSize[0].Height)
	}

	matCount := 0
	for _, key := range request.GetCreativeTemplateKeyList() {
		creativeTemplateKey := creative_entity.CreativeTemplateKey(key)
		matCount = creativeTemplateKey.Image().GetRequiredCount()
		if creativeTemplateKey.Video().GetRequiredCount() > 0 {
			videoObj := &chengxiao_proto.BidRequestVideo{
				W:           int32(w),
				H:           int32(h),
				Mimes:       []string{"video/mp4"},
				MinDuration: request.VideoMinDuration,
				MaxDuration: request.VideoMaxDuration,
				VideoType:   chengxiao_proto.MobReqVideoType_MobReqVideoType_Ordinary,
				Skip:        1,
			}
			if trafficData.GetSlotType() == entity.SlotTypeRewardVideo {
				videoObj.VideoType = chengxiao_proto.MobReqVideoType_MobReqVideoType_Incentive
			}
			bidReqImp.Video = videoObj
			bidReqImp.IsSupportVideo = 1
			matCount += 1
			break
		}
	}

	bidReqImp.Image = &chengxiao_proto.BidRequestImage{
		W:     int32(w),
		H:     int32(h),
		Mimes: []string{"image/jpeg", "image/png"},
	}
	bidReqImp.Count = int32(matCount)
}

func (impl *ChengXiaoDspBroker) mappingGeoType(geoStandard int) chengxiao_proto.MobReqGpsType {
	switch geoStandard {
	case 0, 3:
		return chengxiao_proto.MobReqGpsType_MobReqGpsType_Bd09
	case 1:
		return chengxiao_proto.MobReqGpsType_MobReqGpsType_Cgcs2000
	default:
		return chengxiao_proto.MobReqGpsType_MobReqGpsType_Gps
	}
}

func (impl *ChengXiaoDspBroker) mappingCarrier(carrier entity.OperatorType) chengxiao_proto.MobReqDeviceCarrierType {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return chengxiao_proto.MobReqDeviceCarrierType_MobReqDeviceCarrierType_Mobile
	case entity.OperatorTypeChinaTelecom:
		return chengxiao_proto.MobReqDeviceCarrierType_MobReqDeviceCarrierType_Telecom
	case entity.OperatorTypeChinaUnicom, entity.OperatorTypeTietong:
		return chengxiao_proto.MobReqDeviceCarrierType_MobReqDeviceCarrierType_Unicom
	default:
		return chengxiao_proto.MobReqDeviceCarrierType_MobReqDeviceCarrierType_Other
	}
}

func (impl *ChengXiaoDspBroker) mappingDeviceType(s entity.DeviceType) chengxiao_proto.MobReqDeviceType {
	switch s {
	case entity.DeviceTypeMobile:
		return chengxiao_proto.MobReqDeviceType_MobReqDeviceType_Phone
	case entity.DeviceTypePad:
		return chengxiao_proto.MobReqDeviceType_MobReqDeviceType_Tablets
	case entity.DeviceTypeOtt:
		return chengxiao_proto.MobReqDeviceType_MobReqDeviceType_Tv
	case entity.DeviceTypePc:
		return chengxiao_proto.MobReqDeviceType_MobReqDeviceType_Pc
	default:
		return chengxiao_proto.MobReqDeviceType_MobReqDeviceType_Phone
	}
}

func (impl *ChengXiaoDspBroker) mappingOsType(os entity.OsType) chengxiao_proto.MobReqDeviceOsType {
	switch os {
	case entity.OsTypeIOS:
		return chengxiao_proto.MobReqDeviceOsType_MobReqDeviceOsType_IOS
	case entity.OsTypeAndroid:
		return chengxiao_proto.MobReqDeviceOsType_MobReqDeviceOsType_Android
	case entity.OsTypeWindows:
		return chengxiao_proto.MobReqDeviceOsType_MobReqDeviceOsType_Windows
	case entity.OsTypeMacOs:
		return chengxiao_proto.MobReqDeviceOsType_MobReqDeviceOsType_Macos
	default:
		return chengxiao_proto.MobReqDeviceOsType_MobReqDeviceOsType_Android
	}
}

func (impl *ChengXiaoDspBroker) mappingConnectionType(connectionType entity.ConnectionType) chengxiao_proto.MobDeviceConnectionType {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet, entity.ConnectionTypeWifi:
		return chengxiao_proto.MobDeviceConnectionType_MobReqDeviceConnectionType_Wifi
	case entity.ConnectionType2G:
		return chengxiao_proto.MobDeviceConnectionType_MobReqDeviceConnectionType_2G
	case entity.ConnectionType3G:
		return chengxiao_proto.MobDeviceConnectionType_MobReqDeviceConnectionType_3G
	case entity.ConnectionType4G, entity.ConnectionTypeCellular:
		return chengxiao_proto.MobDeviceConnectionType_MobReqDeviceConnectionType_4G
	case entity.ConnectionType5G:
		return chengxiao_proto.MobDeviceConnectionType_MobReqDeviceConnectionType_5G
	default:
		return chengxiao_proto.MobDeviceConnectionType_MobReqDeviceConnectionType_UnKnow
	}
}

func (impl *ChengXiaoDspBroker) mappingOrientation(ori entity.ScreenOrientationType) int32 {
	switch ori {
	case entity.ScreenOrientationTypeLandscape:
		return 1
	default:
		return 0
	}
}

func (impl *ChengXiaoDspBroker) mappingSlotType(stype entity.SlotType) int32 {
	switch stype {
	case entity.SlotTypeBanner:
		return 1
	case entity.SlotTypeOpening, entity.SlotTypeVideoOpening:
		return 3
	case entity.SlotTypeRewardVideo, entity.SlotTypeVideo:
		return 4
	case entity.SlotTypePopup:
		return 5
	default:
		return 2
	}
}

func (impl *ChengXiaoDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if resp.StatusCode != 200 {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			type_convert.GetAssertString(resp.StatusCode),
			type_convert.GetAssertString(resp.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			"1", "读取body失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	bidResp := bidResponsePool.Get()
	defer bidResponsePool.Put(bidResp)
	if err := impl.ParsePbHttpHttpResponse(resp, data, bidResp); err != nil {
		impl.log.WithError(err).Debug("ParseResponse error")
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			"2", "body反序列化失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	impl.SampleDspBroadcastProtobufResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, bidResp)
	if request.IsDebug {
		payload, _ := sonic.Marshal(bidResp)
		impl.log.WithField("response", string(payload)).Info("ParseResponse debug")
	}
	impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
		broadcastCandidate.GetDspSlotId().String(),
		type_convert.GetAssertString(bidResp.Code),
		bidResp.Msg)

	if bidResp.Code != 0 || bidResp.SeatBid == nil || len(bidResp.SeatBid.GetBid()) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)

	for _, bid := range bidResp.GetSeatBid().GetBid() {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = &entity.AppInfo{}
		if bid.App != nil {
			app := bid.App
			candidateAd.AppInfo.AppName = app.Name
			candidateAd.AppInfo.PackageName = app.Bundle
			candidateAd.AppInfo.Icon = app.Icon
			candidateAd.AppInfo.AppVersion = app.Version
			candidateAd.AppInfo.PackageSize = int(app.Size_)
			candidateAd.AppInfo.Develop = app.Developer
			candidateAd.AppInfo.Privacy = app.PrivatePolicy
			candidateAd.AppInfo.Permission = app.PermissionsLink
			candidateAd.AppInfo.AppDesc = app.AppDesc
		}

		if bid.WxId != "" && bid.WxPath != "" {
			candidateAd.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   bid.WxId,
				ProgramPath: bid.WxPath,
			}
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, bid)

		creative := impl.ParseCreativeData(bid)
		if creative == nil {
			return nil, err_code.ErrCreativeNotFound
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(bid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(creative)
		candidate.SetDspAdID(bid.Cid)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break
	}

	return result, nil
}

func (impl *ChengXiaoDspBroker) ParseCreativeData(bid *chengxiao_proto.BidResponseBid) *entity.Creative {
	creative := &entity.Creative{
		CreativeKey: bid.Cid,
	}

	if len(bid.ImageUrl) == 0 && len(bid.VideoUrl) == 0 {
		return nil
	}

	hasTitle, hasDesc := false, false
	if len(bid.VideoUrl) != 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          bid.VideoUrl,
			Width:        bid.W,
			Height:       bid.H,
			Duration:     float64(bid.VideoDuration),
		}
		creative.MaterialList = append(creative.MaterialList, material)
		if len(bid.CoverImage) > 0 {
			materialImg := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          bid.CoverImage,
				Width:        bid.W,
				Height:       bid.H,
			}
			creative.MaterialList = append(creative.MaterialList, materialImg)
		}
		if len(bid.EndImage) > 0 {
			materialImg := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          bid.EndImage,
				Width:        bid.W,
				Height:       bid.H,
			}
			creative.MaterialList = append(creative.MaterialList, materialImg)
		}
		if len(bid.VideoTitle) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeTitle,
				Data:         bid.VideoTitle,
			}
			creative.MaterialList = append(creative.MaterialList, material)
			hasTitle = true
		}
		if len(bid.VideoDesc) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeDesc,
				Data:         bid.VideoDesc,
			}
			creative.MaterialList = append(creative.MaterialList, material)
			hasDesc = true
		}
	}

	for _, url := range bid.ImageUrl {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          url,
			Width:        bid.W,
			Height:       bid.H,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if !hasTitle && len(bid.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         bid.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
		hasTitle = true
	}

	if !hasDesc && len(bid.Desc) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         bid.Desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
		hasDesc = true
	}

	if !hasTitle {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}
	if !hasDesc {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}

func (impl *ChengXiaoDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *ChengXiaoDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *chengxiao_proto.BidResponseBid) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      make([]string, 0),
		LandingAction:         entity.LandingTypeInWebView,
		LandingUrl:            bid.Lp,
	}

	switch bid.Action {
	case 1:
		tracking.LandingAction = entity.LandingTypeDownload
		tracking.DownloadUrl = bid.Lp
		tracking.DeepLinkUrl = bid.MarketUrl
	default:
		if len(bid.DeepLink) > 0 || len(bid.ULink) > 0 {
			tracking.LandingAction = entity.LandingTypeDeepLink
			tracking.DeepLinkUrl = bid.DeepLink
			if len(bid.ULink) > 0 && request.Device.OsType == entity.OsTypeIOS {
				tracking.DeepLinkUrl = bid.ULink
			}
		} else if len(bid.WxId) > 0 && len(bid.WxPath) > 0 {
			tracking.LandingAction = entity.LandingTypeWeChatProgram
		}
	}

	if len(bid.NoticeUrl) > 0 {
		tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.NoticeUrl)
	}
	tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.Imps...)
	tracking.ClickMonitorList = append(tracking.ClickMonitorList, bid.Clk...)
	tracking.DeepLinkMonitorList = bid.Dtt
	tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, bid.Dt...)
	tracking.DeepLinkFailedMonitorList = bid.Dtt
	tracking.DeepLinkFailedMonitorList = append(tracking.DeepLinkFailedMonitorList, bid.Df...)
	tracking.VideoStartUrlList = bid.Vs
	tracking.VideoCloseUrlList = bid.Ve

	if bid.App != nil {
		tracking.AppDownloadStartedMonitorList = bid.App.Ds
		tracking.AppDownloadFinishedMonitorList = bid.App.Df
		tracking.AppInstallStartMonitorList = bid.App.Is
		tracking.AppInstalledFinishMonitorList = bid.App.If
		tracking.AppOpenMonitorList = bid.App.Ac
	}

	// TODO: 汇川点击上报: Post JSON
	// if len(bid.UcClickUrl) > 0 {
	// 	tracking.ClickMonitorList = append(tracking.ClickMonitorList, bid.UcClickUrl)
	// }

	tracking.LandingUrl = macro_builder.MacroReplace(tracking.LandingUrl, impl.macroInfo)
	tracking.ImpressionMonitorList = macro_builder.MacroReplaceList(tracking.ImpressionMonitorList, impl.macroInfo)
	tracking.ClickMonitorList = macro_builder.MacroReplaceList(tracking.ClickMonitorList, impl.macroInfo)
	tracking.DeepLinkMonitorList = macro_builder.MacroReplaceList(tracking.DeepLinkMonitorList, impl.macroInfo)
	tracking.DeepLinkFailedMonitorList = macro_builder.MacroReplaceList(tracking.DeepLinkFailedMonitorList, impl.macroInfo)

	return tracking
}
