package chengxiao_dsp_broker

import (
	"fmt"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type ChengXiaoDspSlotInfo struct {
	*entity.DspSlotInfo
	AppId      string `json:"app_id"`
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`
}

func (info *ChengXiaoDspSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error
	info.AppId, err = dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get [app_id] from extra_data failed, err: %v", err)
	}
	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	info.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	info.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")

	return nil
}

type ChengXiaoDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*ChengXiaoDspSlotInfo
}

func NewChengXiaoDspSlotRegister(dspId utils.ID) *ChengXiaoDspSlotRegister {
	return &ChengXiaoDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*ChengXiaoDspSlotInfo),
	}
}

func (r *ChengXiaoDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *ChengXiaoDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*ChengXiaoDspSlotInfo)
	for _, slot := range list {
		pddSlot := &ChengXiaoDspSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[ChengXiaoDspSlotRegister] init slot failed, slot: , err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *ChengXiaoDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *ChengXiaoDspSlotRegister) GetSlotInfo(slotId utils.ID) *ChengXiaoDspSlotInfo {
	return r.dspSlotMap[slotId]
}
