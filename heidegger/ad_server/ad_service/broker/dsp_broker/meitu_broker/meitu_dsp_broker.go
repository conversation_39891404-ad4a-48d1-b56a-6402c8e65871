package meitu_broker

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"strconv"
	"time"
)

const (
	MeituCodeSuccessWithAd   = "2000"
	MeituCodeSuccessWithNoAd = "2004"
)

type MeituDspBroker struct {
	dsp_broker.DspBrokerBase

	bidUrl string
	dspId  utils.ID

	dspSlotRegister *dsp_slot_register.SimpleDspSlotRegister
}

func NewMeituDspBroker(dspId utils.ID) *MeituDspBroker {
	return &MeituDspBroker{
		dspId:           dspId,
		dspSlotRegister: dsp_slot_register.NewSimpleDspSlotRegister(dspId),
	}
}

func (b *MeituDspBroker) GetDspId() utils.ID {
	return b.dspId
}

func (b *MeituDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.dspSlotRegister
}

func (b *MeituDspBroker) UpdateDspInfo(dsp *entity.Dsp) error {
	b.bidUrl = dsp.BidUrl
	return nil
}

func (b *MeituDspBroker) BuildRequest(adRequest *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := b.dspSlotRegister.GetDspSlotById(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	appId, _ := dspSlot.ExtraData.GetInt("app_id")
	taskId, _ := dspSlot.ExtraData.GetInt("task_id")
	token, _ := dspSlot.ExtraData.GetString("token")

	brokerRequest := &MeituRequest{
		SessionId: utils.NewUUID(),
		TaskId:    taskId,
		Device: MeituDevice{
			DeviceBrand:  trafficData.GetBrand(),
			DeviceModel:  trafficData.GetModel(),
			OsType:       b.ToOsType(trafficData.GetOsType()),
			OsVersion:    trafficData.GetOsVersion(),
			ScreenWidth:  type_convert.GetAssertString(trafficData.GetScreenWidth()),
			ScreenHeight: type_convert.GetAssertString(trafficData.GetScreenHeight()),
			Carrier:      b.ToCarrierType(trafficData.GetOperatorType()),
			Network:      b.ToNetworkType(trafficData.GetConnectionType()),
			Ip:           trafficData.GetRequestIp(),
			Ua:           trafficData.GetUserAgent(),
			MacAddress:   trafficData.GetMd5Mac(),
			Aaid:         trafficData.GetAaid(),
			Caid:         trafficData.GetCaid(),
			BootMark:     trafficData.GetBootMark(),
			UpdateMark:   trafficData.GetUpdateMark(),
		},
	}

	if len(trafficData.GetImei()) != 0 {
		brokerRequest.Device.Imei = trafficData.GetImei()
	} else {
		brokerRequest.Device.Imei = trafficData.GetMd5Imei()
	}

	if len(trafficData.GetAndroidId()) != 0 {
		brokerRequest.Device.AndroidId = trafficData.GetAndroidId()
	} else {
		brokerRequest.Device.AndroidId = trafficData.GetMd5AndroidId()
	}

	if len(trafficData.GetOaid()) != 0 {
		brokerRequest.Device.Oaid = trafficData.GetOaid()
	} else {
		brokerRequest.Device.Oaid = trafficData.GetMd5Oaid()
	}

	if len(trafficData.GetIdfa()) != 0 {
		brokerRequest.Device.Idfa = trafficData.GetIdfa()
	} else {
		brokerRequest.Device.Idfa = trafficData.GetMd5Idfa()
	}

	writer := buffer_pool.NewBufferWriter()
	if _, err := easyjson.MarshalToWriter(brokerRequest, writer); err != nil {
		return nil, err
	}
	defer writer.Release()

	url := b.bidUrl

	nonce := time.Now().Unix()
	timestamp := nonce
	sign := b.Sign(strconv.Itoa(appId), token, nonce, timestamp, writer.Get())

	b.SampleDspBroadcastRequest(b.dspId, dspSlot.Id, candidate, writer.Get())

	zap.L().Debug(fmt.Sprintf("MeituDspBroker url:%s, appId:%d, taskId:%d, token:%s, nonce:%d, timestamp:%d, sign:%s, send body:%s",
		b.bidUrl, appId, taskId, token, nonce, timestamp,
		sign, string(writer.Get())))

	request, err := http.NewRequest(http.MethodPost, url, writer.GetReadCloser())
	if err != nil {
		return nil, err
	}
	request.Header["Content-Type"] = []string{"application/json"}
	request.Header["App-Id"] = []string{strconv.Itoa(appId)}
	request.Header["Nonce"] = []string{strconv.Itoa(int(nonce))}
	request.Header["Timestamp"] = []string{strconv.Itoa(int(timestamp))}
	request.Header["Sign"] = []string{sign}

	return request, nil
}

func (b *MeituDspBroker) ParseResponse(adRequest *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}
	zap.L().Info("MeituDspBroker response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(data))))))

	broadcastCandidate := broadcastCandidateList[0]
	b.SampleDspBroadcastResponse(b.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, data)

	response := MeituResponse{}
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	if response.Code != MeituCodeSuccessWithAd {
		if response.Code == MeituCodeSuccessWithNoAd {
			return nil, err_code.ErrBroadcastNoBidding
		} else {
			return nil, err_code.ErrBrokerResponse.Wrap(fmt.Errorf(response.Message))
		}
	}

	candidateAd := &entity.Ad{
		AdMonitorInfo: &entity.AdMonitorInfo{
			LandingUrl:            response.Data.Creative.LandingUrl,
			DeepLinkUrl:           response.Data.Creative.DeepLinkUrl,
			ClickMonitorList:      response.Data.Tracking.ClickTracking,
			ImpressionMonitorList: response.Data.Tracking.ImpressionTracking,
			VideoStartUrlList:     response.Data.Tracking.VideoBeginTracking,
			VideoCloseUrlList:     response.Data.Tracking.VideoEndTracking,
		},
	}

	candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)

	return []*ad_service.DspAdCandidate{candidate}, nil
}

func (b *MeituDspBroker) Sign(appid, token string, nonce, timestamp int64, body []byte) string {
	h := md5.New()
	io.WriteString(h, string(body))
	io.WriteString(h, appid)
	io.WriteString(h, token)
	io.WriteString(h, strconv.FormatInt(nonce, 10))
	io.WriteString(h, strconv.FormatInt(timestamp, 10))
	return hex.EncodeToString(h.Sum(nil))
}

func (b *MeituDspBroker) ToOsType(osType entity.OsType) string {
	switch osType {
	case entity.OsTypeAndroid:
		return "android"
	case entity.OsTypeIOS:
		return "iOS"
	default:
		return "android"
	}
}

func (b *MeituDspBroker) ToCarrierType(operatorType entity.OperatorType) string {
	switch operatorType {
	case entity.OperatorTypeChinaMobile:
		return "46000"
	case entity.OperatorTypeChinaUnicom:
		return "46001"
	case entity.OperatorTypeChinaTelecom:
		return "46003"
	default:
		return "1000"
	}
}

func (b *MeituDspBroker) ToNetworkType(connectionType entity.ConnectionType) string {
	switch connectionType {
	case entity.ConnectionTypeWifi:
		return "wifi"
	case entity.ConnectionType2G:
		return "2g"
	case entity.ConnectionType3G:
		return "3g"
	case entity.ConnectionType4G:
		return "4g"
	case entity.ConnectionType5G:
		return "5g"
	default:
		return "4g"
	}
}
