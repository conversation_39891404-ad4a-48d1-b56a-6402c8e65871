package qihoo360_dsp_broker

import (
	"fmt"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/qihoo360_dsp_broker/qihoo360_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"io"
	"net/http"
	"strconv"
	"time"
)

type Qihoo360DspBroker struct {
	dsp_broker.DspBrokerBase
	dspSlotRegister *Qihoo360DspSlotRegister
	log             *zap.Logger
	macroInfo       macro_builder.MonitorMacroInfo
}

func NewQihoo360DspBroker(dspId utils.ID) *Qihoo360DspBroker {
	return &Qihoo360DspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		dspSlotRegister: NewQihoo360DspSlotRegister(dspId),
		log:             zap.L().With(zap.String("broker", "Qihoo360DspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__win_price__",
			MacroClickDownX: "__down_x__",
			MacroClickDownY: "__down_y__",
			MacroClickUpX:   "__up_x__",
			MacroClickUpY:   "__up_y__",
		},
	}
}

func (q *Qihoo360DspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := q.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) == 0 {
		return nil, err_code.ErrDspSlotNotFound
	}
	candidate.SetDspSlotId(dspSlot.Id)
	candidate.SetDspSlotKey(slotId)

	reqId := fmt.Sprintf("%s%07s%d", dspSlot.PlatformId, slotId, time.Now().UnixMilli())
	bidRequest := &qihoo360_dsp_entity.AdRequest{
		RequestId: reqId,
		Auth:      md5_utils.GetMd5StringUpper(reqId + dspSlot.Token),
		Device: &qihoo360_dsp_entity.Device{
			DeviceId:        getDeviceId(trafficData),
			Network:         mappingNetwork(trafficData.GetConnectionType()),
			DeviceType:      mappingDeviceType(trafficData.GetDeviceType()),
			Imei:            trafficData.GetImei(),
			ImeiMd5:         md5_utils.GetMd5StringUpper(trafficData.GetImei()),
			Idfa:            trafficData.GetIdfa(),
			IdfaMd5:         md5_utils.GetMd5StringUpper(trafficData.GetIdfa()),
			AndroidId:       trafficData.GetAndroidId(),
			AndroidIdMd5:    md5_utils.GetMd5StringUpper(trafficData.GetAndroidId()),
			Oaid:            trafficData.GetOaid(),
			OaidMd5:         md5_utils.GetMd5StringUpper(trafficData.GetOaid()),
			Paid:            request.Device.Paid,
			Brand:           trafficData.GetBrand(),
			Model:           trafficData.GetModel(),
			Os:              trafficData.GetOsType().String(),
			OsVersion:       trafficData.GetOsVersion(),
			Carrier:         mappingCarrier(trafficData.GetOperatorType()),
			Mac:             trafficData.GetMac(),
			MacMd5:          md5_utils.GetMd5StringUpper(trafficData.GetMac()),
			IP:              trafficData.GetRequestIp(),
			UserAgent:       trafficData.GetUserAgent(),
			Orientation:     int(trafficData.GetScreenOrientation()),
			ScreenWidth:     int(trafficData.GetScreenWidth()),
			ScreenHeight:    int(trafficData.GetScreenHeight()),
			BootMark:        trafficData.GetBootMark(),
			UpdateMark:      trafficData.GetUpdateMark(),
			SyscoreVersion:  request.Device.VercodeHms,
			AppstoreVersion: request.Device.VercodeAg,
			Geo: &qihoo360_dsp_entity.Geo{
				Longitude: trafficData.GetGeoLongitude(),
				Latitude:  trafficData.GetGeoLatitude(),
				GpsType:   "WGS-84",
			},
			Caid: &qihoo360_dsp_entity.Caid{
				Caid:        device_utils.GetCaidRaw(trafficData.GetCaid()),
				CaidVersion: device_utils.GetCaidVersion(trafficData.GetCaid()),
			},
		},
		App: &qihoo360_dsp_entity.ReqApp{
			PkgName: trafficData.GetAppBundle(),
			AppName: trafficData.GetAppName(),
			Version: trafficData.GetAppVersion(),
		},
		Timeout: 300,
		Version: "1.4.2",
	}
	if len(dspSlot.PkgName) > 0 {
		request.App.AppBundle = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		request.App.AppName = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		request.App.AppVersion = dspSlot.AppVersion
	}
	imp, err := q.makeImp(request, candidate, trafficData)
	if err != nil {
		return nil, err
	}
	bidRequest.Imps = []*qihoo360_dsp_entity.Imp{imp}

	if request.IsDebug {
		resBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("Qihoo360DspBroker.BuildSonicJsonHttpRequest, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}

	httpRequest, _, err := q.BuildSonicJsonHttpRequest(bidRequest)
	if err != nil {
		q.log.WithError(err).Error("BuildPbHttpHttpRequest error")
		return nil, err
	}

	q.SampleDspBroadcastSonicJsonRequest(q.GetDspId(), dspSlot.Id, candidate, bidRequest)
	return httpRequest, nil
}

func getDeviceId(trafficData ad_service_entity.TrafficData) string {
	id := trafficData.GetImei()
	if trafficData.GetOsType() == entity.OsTypeAndroid {
		if len(id) == 0 {
			id = trafficData.GetOaid()
		}
	} else if trafficData.GetOsType() == entity.OsTypeIOS {
		id = trafficData.GetIdfa()
		if id == "" {
			id = trafficData.GetCaid()
		}
	}

	return md5_utils.GetMd5StringUpper(id)
}

func (q *Qihoo360DspBroker) makeImp(request *ad_service.AdRequest, candidate *ad_service.AdCandidate, trafficData ad_service_entity.TrafficData) (*qihoo360_dsp_entity.Imp, error) {
	dspSlot := q.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	imp := &qihoo360_dsp_entity.Imp{
		Id:          1,
		BidFloor:    float64(candidate.GetBidFloor().Price),
		Width:       dspSlot.Width,
		Height:      dspSlot.Height,
		TitleMaxLen: 128,
		DescMaxLen:  256,
		Count:       1,
	}

	if trafficData.GetSlotType() == entity.SlotTypeVideo || trafficData.GetSlotType() == entity.SlotTypeRewardVideo || trafficData.GetSlotType() == entity.SlotTypeVideoOpening {
		imp.Video = &qihoo360_dsp_entity.ReqVideo{
			MinDuration: int(request.VideoMinDuration),
			MaxDuration: int(request.VideoMaxDuration),
			MimeTypes:   []string{"video/mp4"},
			VideoType:   1,
		}
		if trafficData.GetSlotType() == entity.SlotTypeRewardVideo {
			imp.Video.VideoType = 2
			imp.Video.Orientation = int(trafficData.GetScreenOrientation())
		}
	}
	if imp.Width == 0 || imp.Height == 0 {
		imp.Width = int(trafficData.GetSlotWidth())
		imp.Height = int(trafficData.GetSlotHeight())
	}
	if (imp.Width == 0 || imp.Height == 0) && len(request.SlotSize) > 0 {
		imp.Width = int(request.SlotSize[0].Width)
		imp.Height = int(request.SlotSize[0].Height)
	}
	return imp, nil
}

func (q *Qihoo360DspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	if response.StatusCode != http.StatusOK {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		q.log.WithError(err).Error("Read response error")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	bidResponse := &qihoo360_dsp_entity.AdResponse{}
	payload, err := q.ParseSonicJsonHttpResponse(response, data, bidResponse)
	if err != nil {
		q.log.WithError(err).Error("ParseSonicJsonHttpResponse error")
		return nil, err
	}

	broadcastCandidate := broadcastCandidateList[0]
	q.SampleDspBroadcastResponse(q.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, payload)

	if request.IsDebug {
		resBody, _ := sonic.Marshal(bidResponse)
		zap.L().Info("Qihoo360DspBroker.ParseResponse response,  body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}
	q.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
		strconv.Itoa(bidResponse.Code), mappingResponseMessage(bidResponse.Code))

	if bidResponse.Code != 0 || bidResponse.Data == nil || len(bidResponse.Data.Groups) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	list := ad_service.DspAdCandidateList{}
	for _, ad := range bidResponse.Data.Groups[0].Ads {
		dspAdCandidate, err := q.buildDspAdCandidate(ad, broadcastCandidate)
		if err != nil {
			q.log.WithError(err).Error("buildDspAdCandidate error")
			return nil, err_code.ErrBroadcastResponseCandidateError.Wrap(err)
		}
		dspAdCandidate.SetDspAdID(strconv.Itoa(bidResponse.Data.Groups[0].ImpID))
		list = append(list, dspAdCandidate)
	}

	return list, nil
}

func (q *Qihoo360DspBroker) buildDspAdCandidate(resAd *qihoo360_dsp_entity.Ad, broadcastCandidate *ad_service.AdCandidate) (*ad_service.DspAdCandidate, error) {
	ad := &entity.Ad{
		DspId:               q.GetDspId(),
		DspSlotId:           broadcastCandidate.GetDspSlotId(),
		DspSlotKey:          broadcastCandidate.GetDspSlotKey(),
		RankingModelControl: entity.AdRankingModelControl{},
		AdMonitorInfo:       q.buildMonitor(resAd),
	}

	if resAd.AppInfo != nil {
		ad.AppInfo = &entity.AppInfo{
			PackageName: resAd.AppInfo.PkgName,
			AppName:     resAd.AppInfo.Name,
			Icon:        resAd.AppInfo.LogoUrl,
			AppVersion:  resAd.AppInfo.Version,
			PackageSize: int(resAd.AppInfo.Size / 1024),
			Privacy:     resAd.AppInfo.SensitiveUrl,
			Permission:  resAd.AppInfo.UsesPermission,
			AppDesc:     resAd.AppInfo.Intro,
			AppDescURL:  resAd.AppInfo.DownUrl,
			Develop:     resAd.AppInfo.SoftCorpName,
		}
	}

	creative := q.buildCreative(resAd)
	if creative == nil {
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	adCandidate := ad_service.NewDspAdCandidateWithPool(ad)
	ad.SetCreative(creative)
	adCandidate.SetBidType(entity.BidTypeCpm)
	adCandidate.SetBidPrice(uint32(resAd.Price))
	adCandidate.SetAdCandidateChargePriceEncoder(q.chargePriceEncoder)
	adCandidate.SetDspProtocol(q.GetDspProtocol())

	return adCandidate, nil
}
func (q *Qihoo360DspBroker) buildMonitor(resAd *qihoo360_dsp_entity.Ad) *entity.AdMonitorInfo {
	if resAd == nil {
		return nil
	}

	monitor := &entity.AdMonitorInfo{}
	monitor.LandingUrl = resAd.Link
	if resAd.AdType == 7 {
		monitor.H5LandingUrl = resAd.Link
	}
	monitor.LandingAction = entity.LandingTypeInWebView
	if resAd.DownloadAd == 1 {
		monitor.LandingAction = entity.LandingTypeDownload
		monitor.DownloadUrl = resAd.Link
	}
	if resAd.AppInfo != nil {
		monitor.DeepLinkUrl = resAd.AppInfo.DeepLink
		if len(resAd.AppInfo.DeepLink) > 0 {
			monitor.LandingAction = entity.LandingTypeDeepLink
		}

		for _, track := range resAd.AppInfo.EventTracks {
			switch track.EventType {
			case 13:
				monitor.DeepLinkFailedMonitorList = append(monitor.DeepLinkFailedMonitorList, track.EventTrackUrls...)
			case 14:
				monitor.DeepLinkMonitorList = append(monitor.DeepLinkMonitorList, track.EventTrackUrls...)
			}
		}
		monitor.AppInstallStartMonitorList = append(monitor.AppInstallStartMonitorList, resAd.AppInfo.SsUrls...)
		monitor.AppInstalledFinishMonitorList = append(monitor.AppInstalledFinishMonitorList, resAd.AppInfo.SfUrls...)
		monitor.AppDownloadStartedMonitorList = append(monitor.AppDownloadStartedMonitorList, resAd.AppInfo.DsUrls...)
		monitor.AppDownloadFinishedMonitorList = append(monitor.AppDownloadFinishedMonitorList, resAd.AppInfo.DfUrls...)
	}
	if resAd.Video != nil {
		for _, track := range resAd.Video.EventTracks {
			switch track.EventType {
			case 1:
				monitor.VideoStartUrlList = append(monitor.VideoStartUrlList, track.EventTrackUrls...)
			case 5:
				monitor.VideoCloseUrlList = append(monitor.VideoCloseUrlList, track.EventTrackUrls...)
			}
		}
	}

	monitor.ClickMonitorList = append(monitor.ClickMonitorList, resAd.ClickTrackUrls...)
	monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, resAd.ImpTrackUrls...)

	// 宏替换
	monitor.ImpressionMonitorList = q.macroInfo.MacroReplaceList(monitor.ImpressionMonitorList)
	monitor.ClickMonitorList = q.macroInfo.MacroReplaceList(monitor.ClickMonitorList)
	monitor.DeepLinkMonitorList = q.macroInfo.MacroReplaceList(monitor.DeepLinkMonitorList)
	return monitor
}
func (q *Qihoo360DspBroker) buildCreative(resAd *qihoo360_dsp_entity.Ad) *entity.Creative {
	if resAd == nil {
		return nil
	}

	creative := &entity.Creative{
		CreativeKey: resAd.CreativeId,
	}
	title := &entity.Material{
		MaterialType: entity.MaterialTypeTitle,
		Data:         resAd.Title,
	}
	if len(title.Data) == 0 {
		title.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, title)
	desc := &entity.Material{
		MaterialType: entity.MaterialTypeDesc,
		Data:         resAd.Desc,
	}
	if len(desc.Data) == 0 {
		desc.Data = "点击查看详情"
	}
	if len(resAd.AdIcon) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Data:         resAd.AdIcon,
			Height:       100,
			Width:        100,
		})
	}
	if len(resAd.MatterIcon) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeLogo,
			Data:         resAd.MatterIcon,
		})
	}

	for _, img := range resAd.Imgs {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          img.URL,
			Height:       int32(img.Height),
			Width:        int32(img.Width),
		})
	}
	if resAd.Video != nil {
		if len(resAd.Video.CoverURL) > 0 {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeCoverImage,
				Url:          resAd.Video.CoverURL,
				Height:       int32(resAd.Video.Height),
				Width:        int32(resAd.Video.Width),
			})
		}
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          resAd.Video.VideoURL,
			Height:       int32(resAd.Video.Height),
			Width:        int32(resAd.Video.Width),
			Duration:     float64(resAd.Video.Duration),
		})
	}
	return creative
}

func (q *Qihoo360DspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return q.dspSlotRegister
}

func (q *Qihoo360DspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := q.PriceManager.GetDspCoder(q.DspProtocol).EncodeWithKey(uint64(chargePrice), q.GetIKey(), q.GetEKey())
	if err != nil {
		q.log.WithError(err).WithField("price", chargePrice).Errorf("chargePriceEncoder error")
		return ""
	}

	return result
}

// 映射设备网络类型
func mappingNetwork(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	case entity.ConnectionTypeWifi:
		return 6
	default:
		return 1
	}
}

// 映射设备类型
func mappingDeviceType(deviceType entity.DeviceType) int {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	case entity.DeviceTypePc:
		return 4
	default:
		return 5
	}
}

// 映射设备运营商类型
func mappingCarrier(carrierType entity.OperatorType) int {
	switch carrierType {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 2
	case entity.OperatorTypeChinaTelecom:
		return 3
	default:
		return 9
	}
}

func mappingResponseMessage(code int) string {
	switch code {
	case 0:
		return "成功"
	case 100111:
		return "广告请求解析错误"
	case 100113:
		return "requestID 参数错误，requestID 格式错误，不符合 5 位 platformId+7 位 posId+时间戳的格式"
	case 100115:
		return "无效的 platformId，传递的 platform id 错误，请确认传递的 platform id 是正确的"
	case 100117:
		return "无效的 pos id，当传递的 pos id 非法或者 platform id 与 pos id 不匹配时均会出现该错误"
	case 100121:
		return "认证失败。请认真参考文档中鉴权部分说明"
	case 100211:
		return "device 信息为空，设备信息为空"
	case 100213:
		return "设备类型 deviceType 字段非接口定义的字段"
	case 100215:
		return "imei 校验错误，imei 格式失败，为纯数字"
	case 100217:
		return "md5 值校验失败，md5 值校验失败，均为大写字母"
	case 100219:
		return "deviceId 信息错误，deviceId 信息错误"
	case 100221:
		return "idfa 校验失败，idfa 格式校验失败，idfa 明文为 16 进制 32 位长度，按 8-4-4-4-12 的长度分布"
	case 100223:
		return "brand 未填充，设备类型为手机/平板时，需要填充该字段"
	case 100225:
		return "model 未填充，设备类型为手机/平板时，需要填充该字段"
	case 100227:
		return "os 未填充，设备类型为手机/平板时，需要填充该字段"
	case 100231:
		return "osVersion 未填充，设备类型为手机/平板时，需要填充该字段"
	case 100233:
		return "carrier 未填充，设备类型为手机时，需要填充该字段"
	case 100235:
		return "androidId 错误，androidId 格式错误"
	case 100237:
		return "设备唯一标识未填写，当 os 为 android 时，imei, imeiMd5 作为必选字段，可以二选一。其中 imei 字段传输明文值；当 os 为 ios 设备时，idfa 字段为必填"
	case 100241:
		return "ip 地址错误，ip 信息非公网 IP"
	case 100243:
		return "mac 错误，mac 错误"
	case 100245:
		return "ua 错误，设备的客户端 UA 非传输设备的 webview 标准 ua"
	case 100311:
		return "imp 信息为空，imp 信息为空"
	case 100313:
		return "imp 信息不支持多个，imp 信息不支持多个"
	case 100315:
		return "impId 错误，impId 参数错误"
	case 100317:
		return "激励视频的视频播放形式未填充,激励视频的视频播放形式未填充"
	case 100411:
		return "过期的请求，系统会对传递的时间戳进行校验，如果传递的时间戳+允许时间差 < 服务器时间，该请求被视为过期的请求。出现错误时请确认传递的时间戳"
	case 100413:
		return "流量超限,该 pos 位的流量超出限制流量"
	case 200311:
		return "参数解析错误，参数解析错误"
	case 200411:
		return "无广告填充，无广告填充"
	case 900111:
		return "非法的请求参数错误，非法的请求参数错误"
	case 900113:
		return "参数类型匹配错误，参数类型匹配错误"
	case 900115:
		return "缺少请求参数错误，缺少请求参数错误"
	case 900211:
		return "系统错误, 系统错误"
	default:
		return "未知错误"
	}
}
