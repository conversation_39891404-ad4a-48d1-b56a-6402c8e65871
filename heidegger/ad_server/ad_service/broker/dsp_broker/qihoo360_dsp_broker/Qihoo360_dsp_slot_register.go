package qihoo360_dsp_broker

import (
	"errors"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type Qihoo360DspSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`

	Token      string `json:"token"`
	PlatformId string `json:"platform_id"`
}

func (info *Qihoo360DspSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	info.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	info.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")

	info.Token, _ = dspSlotInfo.ExtraData.GetString("token")
	if len(info.Token) == 0 {
		return errors.New("dsp slot token is empty")
	}
	info.PlatformId, _ = dspSlotInfo.ExtraData.GetString("platform_id")
	if len(info.PlatformId) == 0 || len(info.PlatformId) > 5 {
		return errors.New("dsp slot platformId is invalid")
	}
	info.PlatformId = fmt.Sprintf("%05s", info.PlatformId)

	return nil
}

type Qihoo360DspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*Qihoo360DspSlotInfo
}

func NewQihoo360DspSlotRegister(dspId utils.ID) *Qihoo360DspSlotRegister {
	return &Qihoo360DspSlotRegister{
		dspId:       dspId,
		dspSlotList: entity.DspSlotInfoList{},
		dspSlotMap:  make(map[utils.ID]*Qihoo360DspSlotInfo),
	}
}

func (q *Qihoo360DspSlotRegister) GetDspId() utils.ID {
	return q.dspId
}

func (q *Qihoo360DspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	dspSlotMap := make(map[utils.ID]*Qihoo360DspSlotInfo)
	for _, info := range list {
		slot := &Qihoo360DspSlotInfo{}
		err := slot.Init(info)
		if err != nil {
			zap.L().Error("[Qihoo360DspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}
		dspSlotMap[slot.Id] = slot
	}

	q.dspSlotList = list
	q.dspSlotMap = dspSlotMap
	return nil
}

func (q *Qihoo360DspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return q.dspSlotList
}

func (q *Qihoo360DspSlotRegister) GetSlotInfo(slotId utils.ID) *Qihoo360DspSlotInfo {
	return q.dspSlotMap[slotId]
}
