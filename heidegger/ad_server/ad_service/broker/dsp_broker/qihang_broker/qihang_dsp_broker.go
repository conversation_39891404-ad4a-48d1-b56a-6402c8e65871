package qihang_broker

import (
	"encoding/json"
	"io"
	"net/http"
	url1 "net/url"
	"strconv"
	"strings"
	"time"

	"gitlab.com/dev/heidegger/library/utils/type_convert"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/qihang_broker/qihang_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"fmt"
)

const taobaoAppPkgNameIos = "com.taobao.taobao4iphone"
const taobaoAppPkgNameAndroid = "com.taobao.taobao"
const QiHangTaskIdQueryParam = "taskId"

type QihangDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *QihangDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string
}

func NewQihangDspBroker(dspId utils.ID) *QihangDspBroker {
	return &QihangDspBroker{
		slotRegister:  NewQihangDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "${AUCTION_PRICE}",
	}
}

func (b *QihangDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.slotRegister
}

func (b *QihangDspBroker) GetDspId() utils.ID {
	return b.dspId
}

func (b *QihangDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := b.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) == 0 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	brokerRequest := &qihang_proto.BidRequest{
		Id:         trafficData.GetRequestId(),
		ApiVersion: "1.0.0",
		Imp:        make([]*qihang_proto.BidRequest_Imp, 0),
		App: &qihang_proto.BidRequest_App{
			Name:   trafficData.GetAppName(),
			Bundle: trafficData.GetAppBundle(),
			Verion: trafficData.GetOsVersion(),
		},
		Device: &qihang_proto.BidRequest_Device{
			Ua:             trafficData.GetUserAgent(),
			Ip:             trafficData.GetRequestIp(),
			DeviceType:     b.qihangDeviceType(trafficData.GetDeviceType()),
			Make:           trafficData.GetBrand(),
			Model:          trafficData.GetModel(),
			Os:             b.qihangOsType(trafficData.GetOsType()),
			Osv:            trafficData.GetOsVersion(),
			Carrier:        b.qihangCarrierType(trafficData.GetOperatorType()),
			ConnectionType: b.qihangConnectionType(trafficData.GetConnectionType()),
			Idfa:           trafficData.GetIdfa(),
			IdfaMd5:        trafficData.GetMd5Idfa(),
			Oaid:           trafficData.GetOaid(),
			OaidMd5:        trafficData.GetMd5Oaid(),
			Imei:           trafficData.GetImei(),
			ImeiMd5:        trafficData.GetMd5Imei(),
			AndroidId:      trafficData.GetAndroidId(),
			AndroidIdMd5:   trafficData.GetMd5AndroidId(),
			Mac:            trafficData.GetMac(),
			MacMd5:         trafficData.GetMd5Mac(),
			CaidVersion:    device_utils.GetCaidVersion(trafficData.GetCaid()),
			Caid:           device_utils.GetCaidRaw(trafficData.GetCaid()),
			CaidMd5:        trafficData.GetMd5Caid(),
			BootMark:       trafficData.GetBootMark(),
			UpdateMark:     trafficData.GetUpdateMark(),
		},
	}

	if len(request.Device.Caids) > 0 {
		brokerRequest.Device.CaidInfos = make([]*qihang_proto.BidRequest_Device_CaidInfo, 0)
		for _, caid := range request.Device.Caids {
			if len(caid) > 0 {
				brokerRequest.Device.CaidInfos = append(brokerRequest.Device.CaidInfos, &qihang_proto.BidRequest_Device_CaidInfo{
					Version: device_utils.GetCaidVersion(caid),
					Caid:    device_utils.GetCaidRaw(caid),
					CaidMd5: "",
				})
			}
		}
	}

	if len(dspSlot.AppVersion) > 0 {
		brokerRequest.App.Verion = dspSlot.AppVersion
	}

	if len(dspSlot.PkgName) > 0 {
		brokerRequest.App.Bundle = dspSlot.PkgName
	}

	if len(dspSlot.AppName) > 0 {
		brokerRequest.App.Name = dspSlot.AppName
	}

	imp := &qihang_proto.BidRequest_Imp{}

	imp.Id = candidate.GetAdRequest().ImpressionId
	if len(imp.Id) == 0 {
		imp.Id = trafficData.GetRequestId()
	}
	imp.TagId = slotId
	imp.SubTagId = candidate.GetAd().AdId.String() + brokerRequest.App.Bundle
	imp.AdType = b.qihangAdType(trafficData.GetSlotType())
	if dspSlot.AdType > 0 {
		imp.AdType = dspSlot.AdType
	}
	imp.BidType = int32(dspSlot.BidType)
	imp.Asset = []*qihang_proto.BidRequest_Imp_Asset{
		{
			TemplateId: dspSlot.TemplateId,
			Height:     int32(dspSlot.Height),
			Width:      int32(dspSlot.Width),
		},
	}
	bidFloor := candidate.GetBidFloor()
	imp.BidFloor = int64(bidFloor.Price)

	brokerRequest.Imp = append(brokerRequest.Imp, imp)
	brokerRequest.SspTime = time.Now().UnixNano() / 1e6

	if len(request.App.DspInstalledAppList) > 0 {
		brokerRequest.CustomizedUserTag = &qihang_proto.BidRequest_CustomizedUserTag{
			InstalledAppList: []*qihang_proto.BidRequest_CustomizedUserTag_InstalledApp{},
		}
		if ids, ok := request.App.DspInstalledAppList[b.dspId.String()]; ok {
			for _, v := range ids {
				id, err := strconv.Atoi(v)
				if err == nil {
					brokerRequest.CustomizedUserTag.InstalledAppList = append(brokerRequest.CustomizedUserTag.InstalledAppList, &qihang_proto.BidRequest_CustomizedUserTag_InstalledApp{
						Id: uint32(id),
					})
				}
			}
		}
	} else if len(request.App.InstalledApp) > 0 {
		brokerRequest.CustomizedUserTag = &qihang_proto.BidRequest_CustomizedUserTag{
			InstalledAppList: []*qihang_proto.BidRequest_CustomizedUserTag_InstalledApp{},
		}
		for _, v := range request.App.InstalledApp {
			brokerRequest.CustomizedUserTag.InstalledAppList = append(brokerRequest.CustomizedUserTag.InstalledAppList, &qihang_proto.BidRequest_CustomizedUserTag_InstalledApp{
				Name: v,
			})
		}
	}

	req, _, err := b.BuildPbHttpHttpRequest(brokerRequest)
	if err != nil {
		zap.L().Error("QihangDspBroker MarshalToSizedBuffer err", zap.Error(err))
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	//buffer := buffer_pool.NewBuffer()
	//defer buffer.Release()
	//buffer.EnsureSize(brokerRequest.Size())
	//
	//_, err := brokerRequest.MarshalToSizedBuffer(buffer.Get())
	//if err != nil {
	//	zap.L().Error("QihangDspBroker MarshalToSizedBuffer err", zap.Error(err))
	//	return nil, err
	//}
	//
	//req, err := http.NewRequest("POST", b.GetBidUrl(), buffer.GetReadCloser())
	//if err != nil {
	//	zap.L().Error("QihangDspBroker http NewRequest err", zap.Error(err))
	//	return nil, err
	//}

	b.SampleDspBroadcastProtobufRequest(b.dspId, dspSlot.Id, candidate, brokerRequest)
	if request.IsDebug {
		reqbody, _ := json.Marshal(brokerRequest)
		zap.L().Info("QihangDspBroker url:, send:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", b.GetBidUrl())))), reqbody)
	}
	return req, err
}

func (b *QihangDspBroker) ParseCreativeData(bid *qihang_proto.BidResponse_Bid) *entity.Creative {

	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.CreativeId,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}
	//material.MaterialId = util.GetAssertInt64(bid.Adm.GetTemplateId())
	//material.AdxTemplateId = bid.Adm.GetTemplateId()
	if bid.Adm.GetTitle() != "" {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         bid.Adm.GetTitle(),
		}

		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}

		creative.MaterialList = append(creative.MaterialList, material)
	}

	if bid.Adm.GetDesc() != "" {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         bid.Adm.GetDesc(),
		}
		creative.MaterialList = append(creative.MaterialList, material)

	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Adm.Image) > 0 {
		for _, admImage := range bid.Adm.Image {
			if admImage == nil {
				continue
			}
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          admImage.GetUrl(),
				Height:       admImage.GetHeight(),
				Width:        admImage.GetWidth(),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

	}

	if bid.Adm.Video != nil && bid.Adm.Video.GetVideoUrl() != "" {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          bid.Adm.Video.GetVideoUrl(),
			Height:       bid.Adm.Video.GetHeight(),
			Width:        bid.Adm.Video.GetWidth(),
			Duration:     float64(bid.Adm.Video.GetDuration()),
		}

		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.IconUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.IconUrl,
			Height:       100,
			Width:        100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}

func (b *QihangDspBroker) ParseTrackingData(bid *qihang_proto.BidResponse_Bid) *entity.AdMonitorInfo {
	trackings := &entity.AdMonitorInfo{
		LandingUrl:   bid.Adm.GetLandingSite(),
		H5LandingUrl: bid.Adm.GetLandingSite(),
		DeepLinkUrl:  bid.Adm.GetDeepLink(),
	}

	for _, clkTrack := range bid.GetClkTrackers() {
		if strings.Contains(clkTrack, "__CALLBACK_URL__") {
			newClkTrack := strings.ReplaceAll(clkTrack, "__CALLBACK_URL__", "__CBURL__")
			trackings.AddClickMonitor(newClkTrack)
		} else {
			//newClkTrack := clkTrack + "&callbackUrl=__CBURL__"
			trackings.AddClickMonitor(clkTrack)
		}
	}

	if len(bid.GetNurl()) > 0 {
		if strings.Contains(bid.GetNurl(), b.MacroWinPrice) {
			newNurl := strings.ReplaceAll(bid.GetNurl(), b.MacroWinPrice, "__DSPWPRICE__")
			trackings.AddWinNoticeUrl(newNurl)
		} else {
			trackings.AddWinNoticeUrl(bid.GetNurl())
		}
	}

	for _, impTrack := range bid.GetImpTrackers() {
		if strings.Contains(impTrack, b.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impTrack, b.MacroWinPrice, "__DSPWPRICE__")
			trackings.AddImpressionMonitor(newImpTrack)
		} else {
			trackings.AddImpressionMonitor(impTrack)
		}
	}

	switch bid.Adm.GetAdType() {
	case 1:
		trackings.LandingAction = entity.LandingTypeInWebView
	case 2:
		//trackings.LandingAction = entity.LandingTypeDownload
	case 3:
		trackings.LandingAction = entity.LandingTypeInWebView
	case 4:
		//trackings.LandingAction = entity.LandingTypeDownload
	case 5:
		//trackings.LandingAction = entity.LandingTypeDownload
	}

	return trackings
}

func (b *QihangDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]
	if resp.StatusCode != 200 {
		b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.StatusCode), type_convert.GetAssertString(resp.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	buffer, err := io.ReadAll(resp.Body)
	if err != nil {
		b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	brokerResponse := &qihang_proto.BidResponse{}
	if err := b.ParsePbHttpHttpResponse(resp, buffer, brokerResponse); err != nil {
		b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		zap.L().Debug("QihangDspBroker.DecodeResponse proto.Unmarshal, err:", zap.Error(err), zap.String("param2", fmt.Sprintf("%v", string(buffer))))
		return nil, err_code.ErrBrokerParseError
	}

	b.SampleDspBroadcastProtobufResponse(b.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, brokerResponse)
	if request.IsDebug {
		resBody, _ := json.Marshal(brokerResponse)
		zap.L().Info("QihangDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}
	b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(brokerResponse.GetNbr()), b.GetCodeMsg(brokerResponse.GetNbr()))

	if brokerResponse.GetNbr() == 9 && brokerResponse.GetColdEndTime() > 0 {
		b.markQiHangCode(request, broadcastCandidate, brokerResponse.GetColdEndTime())
		return nil, err_code.ErrBroadcastNoBidding
	}

	if len(brokerResponse.SeatBid) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	if len(brokerResponse.SeatBid[0].Bid) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0, len(brokerResponse.SeatBid[0].Bid))

	for _, bid := range brokerResponse.SeatBid[0].GetBid() {
		if bid.Adm == nil {
			continue
		}

		candidateAd := &entity.Ad{
			DspId:         b.GetDspId(),
			AdMonitorInfo: b.ParseTrackingData(bid),
			DspTaskId:     b.getDspTaskId(bid),
			DspSlotId:     broadcastCandidate.GetDspSlotId(),
			DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = &entity.AppInfo{}
		if request.Device.OsType == entity.OsTypeIOS {
			if bid.Adm.GetUniversalLink() != "" {
				candidateAd.AdMonitorInfo.DeepLinkUrl = bid.Adm.GetUniversalLink()
			}
			candidateAd.AppInfo.PackageName = taobaoAppPkgNameIos
		} else {
			candidateAd.AppInfo.PackageName = taobaoAppPkgNameAndroid
		}
		candidateAd.AppInfo.AppName = "淘宝"

		if len(bid.AppName) > 0 {
			candidateAd.AppInfo.AppName = bid.AppName
		}

		if len(bid.PackageName) > 0 {
			candidateAd.AppInfo.PackageName = bid.PackageName
		}

		if len(candidateAd.AdMonitorInfo.DeepLinkUrl) > 0 && candidateAd.AdMonitorInfo.LandingAction == entity.LandingTypeInWebView {
			candidateAd.AdMonitorInfo.LandingAction = entity.LandingTypeDeepLink
		}
		if len(bid.IconUrl) > 0 {
			candidateAd.AppInfo.Icon = bid.IconUrl
		}

		candidateCreative := b.ParseCreativeData(bid)

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(b.chargePriceEncoder)
		candidate.SetBidPrice(uint32(bid.GetPrice()))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(bid.Adm.TemplateId)
		candidate.SetDspProtocol(b.GetDspProtocol())
		candidate.SetDspUserScore(bid.UserScoreLevel)
		result = append(result, candidate)
		break
	}

	return result, nil
}

func (b *QihangDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := b.PriceManager.GetDspCoder(b.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), b.GetIKey(), b.GetEKey())
	if err != nil {
		zap.L().Error("QihangDspBroker encode price error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return ""
	}

	return result
}

func (b *QihangDspBroker) qihangDeviceType(deviceType entity.DeviceType) int32 {
	switch deviceType {
	case entity.DeviceTypePc:
		return 3
	case entity.DeviceTypeMobile:
		return 4
	case entity.DeviceTypePad:
		return 5
	case entity.DeviceTypeOtt:
		return 7
	default:
		return 0
	}
}

func (b *QihangDspBroker) qihangOsType(osType entity.OsType) string {
	switch osType {
	case entity.OsTypeAndroid:
		return "android"
	case entity.OsTypeIOS:
		return "ios"
	default:
		return "android"
	}
}

func (b *QihangDspBroker) qihangConnectionType(connectionType entity.ConnectionType) int32 {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionTypeCellular:
		return 3
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 6
	}
}

func (b *QihangDspBroker) qihangCarrierType(operatorType entity.OperatorType) int32 {
	switch operatorType {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 2
	case entity.OperatorTypeChinaTelecom, entity.OperatorTypeTietong:
		return 3
	default:
		return 0
	}
}

func (b *QihangDspBroker) qihangAdType(slotType entity.SlotType) int32 {
	switch slotType {
	case entity.SlotTypeFeeds:
		return 1
	case entity.SlotTypeOpening:
		return 2
	case entity.SlotTypeBanner:
		return 3
	case entity.SlotTypePopup:
		return 4
	case entity.SlotTypeRewardVideo:
		return 9
	default:
		return 1
	}
}

func (b *QihangDspBroker) CheckBroadcastContext(ctx *ad_service.AdRequest, candidateList ad_service.AdCandidateList) error {
	if ctx.UserSegment.ContainsTag(entity.UserTagQiHangColdTag) {
		return err_code.ErrBroadcastDspContextFilter
	}
	return nil
}

func (b *QihangDspBroker) markQiHangCode(request *ad_service.AdRequest, candidate *ad_service.AdCandidate, endTime int64) {
	if b.GetUserSegmentClient() == nil {
		return
	}

	trafficData := candidate.GetModifiedTrafficData()
	deviceId, _ := trafficData.GetDeviceIdWithType()

	// seconds to the end of the day
	now := time.Now()
	expireTime := endTime - now.Unix()
	if expireTime <= 0 {
		return
	}

	if err := b.GetUserSegmentClient().AddUserSegmentAsync(deviceId, entity.UserTagQiHangColdTag, 1, uint32(expireTime), 0); err != nil {
		zap.L().Error("qihang markQinghangCode AddUserSegmentAsync err", zap.Error(err))
		return
	}
}

func (b *QihangDspBroker) getDspTaskId(bid *qihang_proto.BidResponse_Bid) string {
	if len(bid.ClkTrackers) > 0 {
		for _, clk := range bid.ClkTrackers {
			taskId := b.getTaskId(clk)
			if len(taskId) > 0 {
				return taskId
			}
		}
	}

	return ""
}

func (b *QihangDspBroker) getTaskId(url string) string {
	if !strings.Contains(url, QiHangTaskIdQueryParam) {
		return ""
	}

	parsedURL, err := url1.Parse(url)
	if err != nil {
		zap.L().Error("[QihangDspBroker] sign parse url error:, url:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))), url)
		return ""
	}
	queryParams := parsedURL.Query()
	taskId := queryParams.Get(QiHangTaskIdQueryParam)

	return taskId
}

func (b *QihangDspBroker) GetCodeMsg(code int32) string {
	switch code {
	case 9:
		return "设备冷却"
	default:
		return utils.EmptyString
	}
}
