package qihang_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type QihangSlotSlotInfo struct {
	*entity.DspSlotInfo
	TemplateId string `json:"template_id"`
	BidType    int    `json:"bid_type"`
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AdType     int32  `json:"ad_type"`
	AppVersion string `json:"app_version"`
	PkgName    string `json:"pkg_name"`
	AppName    string `json:"app_name"`
}

func (info *QihangSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error
	info.TemplateId, err = dspSlotInfo.ExtraData.GetString("template_id")
	if err != nil {
		return fmt.Errorf("get template_id from extra_data failed, err: %v", err)
	}

	info.BidType, err = dspSlotInfo.ExtraData.GetInt("bid_type")
	if err != nil {
		info.BidType = 0
		//return fmt.Errorf("get bid_type from extra_data failed, err: %v", err)
	}

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
		return fmt.Errorf("get height from extra_data failed, err: %v", err)
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
		return fmt.Errorf("get width from extra_data failed, err: %v", err)
	}
	adType, err := dspSlotInfo.ExtraData.GetInt("ad_type")
	if err != nil {
	}
	info.AdType = int32(adType)

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	return nil
}

type QihangDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*QihangSlotSlotInfo
}

func NewQihangDspSlotRegister(dspId utils.ID) *QihangDspSlotRegister {
	return &QihangDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*QihangSlotSlotInfo),
	}
}

func (r *QihangDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *QihangDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*QihangSlotSlotInfo)
	for _, slot := range list {
		qihangSlot := &QihangSlotSlotInfo{}
		if err := qihangSlot.Init(slot); err != nil {
			zap.L().Error("[QihangDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = qihangSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *QihangDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *QihangDspSlotRegister) GetSlotInfo(slotId utils.ID) *QihangSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
