package youtui_dsp_broker

import (
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/youtui_dsp_broker/youtui_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"io"
	"net/http"
	"strconv"
	"fmt"
)

type YouTuiDspBroker struct {
	dsp_broker.DspBrokerBase
	dspSlotRegister *YouTuiDspSlotRegister
	log             *zap.Logger
	macroInfo       macro_builder.MonitorMacroInfo
}

func NewYouTuiDspBroker(dspId utils.ID) *YouTuiDspBroker {
	return &YouTuiDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		dspSlotRegister: NewYouTuiDspSlotRegister(dspId),
		log:             zap.L().With(zap.String("broker", "YouTuiDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__LEMON__UNPRICE__",
			MacroClickDownX: "__LEMON__DP_DOWN_X__",
			MacroClickDownY: "__LEMON__DP_DOWN_Y__",
			MacroClickUpX:   "__LEMON__DP_UP_X__",
			MacroClickUpY:   "__LEMON__DP_UP_Y__",
		},
	}
}

func (y *YouTuiDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := y.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(dspSlot.Id)
	candidate.SetDspSlotKey(slotId)

	bidRequest := &youtui_broker_proto.BidRequest{
		Id: trafficData.GetRequestId(),
		App: &youtui_broker_proto.BidRequest_App{
			Name:   trafficData.GetAppName(),
			Ver:    trafficData.GetAppVersion(),
			Bundle: trafficData.GetAppBundle(),
		},
		Device: &youtui_broker_proto.BidRequest_Device{
			Ua: trafficData.GetUserAgent(),
			Geo: &youtui_broker_proto.BidRequest_Geo{
				Lat: trafficData.GetGeoLatitude(),
				Lon: trafficData.GetGeoLongitude(),
			},
			Ip:             trafficData.GetRequestIp(),
			Devicetype:     mappingDeviceType(trafficData.GetDeviceType()),
			Os:             mappingOsType(trafficData.GetOsType()),
			Dpi:            strconv.Itoa(int(request.Device.DPI)),
			Ppi:            strconv.Itoa(int(request.Device.PPI)),
			Orientation:    strconv.Itoa(int(trafficData.GetScreenOrientation())),
			H:              uint32(trafficData.GetScreenHeight()),
			W:              uint32(trafficData.GetScreenWidth()),
			Carrier:        mappingCarrierType(trafficData.GetOperatorType()),
			Connectiontype: mappingConnectionType(trafficData.GetConnectionType()),
			Ifa:            trafficData.GetIdfa(),
			Idfv:           trafficData.GetIdfv(),
			Did:            trafficData.GetImei(),
			Didmd5:         trafficData.GetMd5Imei(),
			Dpid:           trafficData.GetAndroidId(),
			Dpidmd5:        trafficData.GetMd5AndroidId(),
			Mac:            trafficData.GetMac(),
			Macmd5:         trafficData.GetMd5Mac(),
			Make:           trafficData.GetBrand(),
			Model:          trafficData.GetModel(),
			Ext: &youtui_broker_proto.BidRequest_DeviceExt{
				Androidid: trafficData.GetAndroidId(),
				Brand:     trafficData.GetBrand(),
				Imei:      trafficData.GetImei(),
				Mac:       trafficData.GetMac(),
				Oaid:      trafficData.GetOaid(),
				Ver:       trafficData.GetAppVersion(),
			},
			Bootmark:     trafficData.GetBootMark(),
			Updatemark:   trafficData.GetUpdateMark(),
			Boottime:     trafficData.GetDeviceStartupTime(),
			Osupdatetime: trafficData.GetDeviceUpgradeTime(),
			DeviceName:   request.Device.DeviceName,
			Startuptime:  trafficData.GetDeviceStartupTime(),
			Birthtime:    trafficData.GetDeviceInitTime(),
			Paid:         request.Device.Paid,
		},
	}
	if len(dspSlot.PkgName) > 0 {
		bidRequest.App.Bundle = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidRequest.App.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidRequest.App.Ver = dspSlot.AppVersion
	}
	if request.Device.IsIp6 {
		bidRequest.Device.Ext.Ipv6 = trafficData.GetRequestIp()
	}
	if len(trafficData.GetCaid()) > 0 {
		bidRequest.Device.Caid = device_utils.GetCaidRaw(trafficData.GetCaid())
		bidRequest.Device.Caidver = device_utils.GetCaidVersion(trafficData.GetCaid())
	}

	imp, err := y.makeImp(request, candidate, trafficData)
	if err != nil {
		return nil, err
	}
	bidRequest.Imp = imp

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		y.log.Infof("BuildRequest request: %s", reqBody)
	}

	httpRequest, _, err := y.BuildPbHttpHttpRequest(bidRequest)
	if err != nil {
		y.log.WithError(err).Error("BuildPbHttpHttpRequest error")
		return nil, err
	}

	y.SampleDspBroadcastProtobufRequest(y.GetDspId(), dspSlot.Id, candidate, bidRequest)
	return httpRequest, nil
}

func (y *YouTuiDspBroker) makeImp(request *ad_service.AdRequest, candidate *ad_service.AdCandidate, trafficData ad_service_entity.TrafficData) (*youtui_broker_proto.BidRequest_Imp, error) {
	dspSlot := y.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	imp := &youtui_broker_proto.BidRequest_Imp{
		BidFloor: uint32(candidate.GetBidFloor().Price),
		Ext: &youtui_broker_proto.BidRequest_Ext{
			Platform: mappingPlatform(trafficData.GetOsType()),
			Stagid:   candidate.GetDspSlotKey(),
		},
	}

	if trafficData.GetSlotType() == entity.SlotTypeVideo || trafficData.GetSlotType() == entity.SlotTypeRewardVideo || trafficData.GetSlotType() == entity.SlotTypeVideoOpening {
		imp.Video = &youtui_broker_proto.BidRequest_Video{
			Minduration: uint32(request.VideoMinDuration),
			Maxduration: uint32(request.VideoMaxDuration),
		}
	} else {
		imp.Native = &youtui_broker_proto.BidRequest_Native{
			W: uint32(dspSlot.Width),
			H: uint32(dspSlot.Height),
		}
		if imp.Native.W == 0 || imp.Native.H == 0 {
			imp.Native.W = trafficData.GetSlotWidth()
			imp.Native.H = trafficData.GetSlotHeight()
		}
		if (imp.Native.W == 0 || imp.Native.H == 0) && len(request.SlotSize) > 0 {
			imp.Native.W = uint32(request.SlotSize[0].Width)
			imp.Native.H = uint32(request.SlotSize[0].Height)
		}
	}

	return imp, nil
}

func (y *YouTuiDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	if response.StatusCode != http.StatusOK {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		y.log.WithError(err).Error("Read response error")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	bidResponse := &youtui_broker_proto.BidResponse{}
	err = y.ParsePbHttpHttpResponse(response, data, bidResponse)
	if err != nil {
		y.log.WithError(err).Error("ParsePbHttpHttpResponse error")
		return nil, err
	}

	broadcastCandidate := broadcastCandidateList[0]
	y.SampleDspBroadcastProtobufResponse(y.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, bidResponse)

	if request.IsDebug {
		resBody, _ := sonic.Marshal(bidResponse)
		zap.L().Info("YouTuiDspBroker.ParseResponse response,  body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}
	y.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), strconv.FormatUint(uint64(bidResponse.GetCode()), 10), bidResponse.GetMessage())

	if bidResponse.GetCode() != 2000 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	list := ad_service.DspAdCandidateList{}
	for _, responseData := range bidResponse.GetData() {
		dspAdCandidate, err := y.buildDspAdCandidate(responseData, broadcastCandidate)
		if err != nil {
			y.log.WithError(err).Error("buildDspAdCandidate error")
			return nil, err_code.ErrBroadcastResponseCandidateError.Wrap(err)
		}
		list = append(list, dspAdCandidate)
	}

	return list, nil
}

func (y *YouTuiDspBroker) buildDspAdCandidate(bidData *youtui_broker_proto.BidResponse_Data, broadcastCandidate *ad_service.AdCandidate) (*ad_service.DspAdCandidate, error) {

	ad := &entity.Ad{
		DspId:         y.GetDspId(),
		DspSlotId:     broadcastCandidate.GetDspSlotId(),
		DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
		AdMonitorInfo: y.buildMonitor(bidData),
	}
	if bidData.Material != nil {
		ad.AppInfo = &entity.AppInfo{
			PackageName: bidData.Material.Packagename,
			AppName:     bidData.Material.Apkname,
			AppVersion:  bidData.Material.VersionName,
			Privacy:     bidData.Material.PrivacyUrl,
			Permission:  bidData.Material.PermissionUrl,
			AppDesc:     bidData.Material.AppIntro,
			Develop:     bidData.Material.AppPublisher,
		}
		parseUint, _ := strconv.ParseUint(bidData.Material.ApkSize, 10, 64)
		ad.AppInfo.PackageSize = int(parseUint * 1024)
	}
	creative := y.buildCreative(bidData)
	if creative == nil {
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	parsePrice, err := strconv.ParseUint(bidData.Price, 10, 64)
	if err != nil {
		y.log.WithError(err).Error("ParseUint price error")
		return nil, err
	}

	adCandidate := ad_service.NewDspAdCandidateWithPool(ad)
	adCandidate.SetCreative(creative)
	adCandidate.SetBidType(entity.BidTypeCpm)
	adCandidate.SetBidPrice(uint32(parsePrice))
	adCandidate.SetDspProtocol(y.GetDspProtocol())
	adCandidate.SetDspAdID(bidData.Sposid)

	return adCandidate, nil
}

func (y *YouTuiDspBroker) buildMonitor(bidData *youtui_broker_proto.BidResponse_Data) *entity.AdMonitorInfo {
	monitor := &entity.AdMonitorInfo{}
	if bidData.Material != nil {
		monitor.LandingUrl = bidData.Material.Ldp
		monitor.H5LandingUrl = bidData.Material.Ldp
		monitor.DeepLinkUrl = bidData.Material.Deeplink
		monitor.LandingAction = mappingLandingAction(bidData.Material.GetLdptype())
		if monitor.LandingAction == entity.LandingTypeDownload {
			monitor.DownloadUrl = bidData.Material.Deeplink
		}
	}
	if bidData.Monitor != nil {
		for _, pa := range bidData.Monitor.Pa {
			monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, pa.GetUrl())
		}
		for _, ca := range bidData.Monitor.Ca {
			monitor.ClickMonitorList = append(monitor.ClickMonitorList, ca.GetUrl())
		}
		// 竞胜通知监测也放到曝光里
		for _, nurl := range bidData.Monitor.Nurl {
			monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, nurl.GetUrl())
		}

		if bidData.Monitor.Advml != nil {
			for _, ledp := range bidData.Monitor.Advml.Ledp {
				monitor.DeepLinkMonitorList = append(monitor.DeepLinkMonitorList, ledp.GetUrl())
			}
			for _, ledpfailtr := range bidData.Monitor.Advml.Ledpfailtrs {
				monitor.DeepLinkFailedMonitorList = append(monitor.DeepLinkFailedMonitorList, ledpfailtr.GetUrl())
			}
			// 唤醒开始监测连接也放到deeplink monitor里
			for _, ledpreadytrs := range bidData.Monitor.Advml.Ledpreadytrs {
				monitor.DeepLinkMonitorList = append(monitor.AppInstallStartMonitorList, ledpreadytrs.GetUrl())
			}

			for _, ledownstarttr := range bidData.Monitor.Advml.Ledownstarttrs {
				monitor.AppDownloadStartedMonitorList = append(monitor.AppDownloadStartedMonitorList, ledownstarttr.GetUrl())
			}
			for _, ledowncomptr := range bidData.Monitor.Advml.Ledowncomptrs {
				monitor.AppDownloadFinishedMonitorList = append(monitor.AppDownloadFinishedMonitorList, ledowncomptr.GetUrl())
			}
			for _, leinstallstarttr := range bidData.Monitor.Advml.Leinstallstarttrs {
				monitor.AppInstallStartMonitorList = append(monitor.AppInstallStartMonitorList, leinstallstarttr.GetUrl())
			}
			for _, leinstallcomptr := range bidData.Monitor.Advml.Leinstallcomptrs {
				monitor.AppInstalledFinishMonitorList = append(monitor.AppInstalledFinishMonitorList, leinstallcomptr.GetUrl())
			}
			for _, levideoend := range bidData.Monitor.Advml.Levideoend {
				monitor.VideoCloseUrlList = append(monitor.VideoCloseUrlList, levideoend.GetUrl())
			}
			for _, levideostart := range bidData.Monitor.Advml.Levideostart {
				monitor.VideoStartUrlList = append(monitor.VideoStartUrlList, levideostart.GetUrl())
			}
		}
	}

	// 宏替换
	if len(monitor.ImpressionMonitorList) > 0 {
		monitor.ImpressionMonitorList = y.macroInfo.MacroReplaceList(monitor.ImpressionMonitorList)
	}
	if len(monitor.ClickMonitorList) > 0 {
		monitor.ClickMonitorList = y.macroInfo.MacroReplaceList(monitor.ClickMonitorList)
	}
	if len(monitor.DeepLinkMonitorList) > 0 {
		monitor.DeepLinkMonitorList = y.macroInfo.MacroReplaceList(monitor.DeepLinkMonitorList)
	}

	return monitor
}

func (y *YouTuiDspBroker) buildCreative(bid *youtui_broker_proto.BidResponse_Data) *entity.Creative {
	if bid.Material == nil {
		return nil
	}
	creative := &entity.Creative{
		CreativeKey: bid.Material.Crid,
	}

	title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: bid.Material.Title}
	if len(bid.Material.Title) == 0 {
		title.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, title)
	desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: bid.Material.Desc}
	if len(bid.Material.Desc) == 0 {
		desc.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, desc)
	if len(bid.Material.Iconurl) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.Material.Iconurl,
			Height:       100,
			Width:        100,
		})
	}
	width, _ := strconv.ParseUint(bid.Material.W, 10, 64)
	height, _ := strconv.ParseUint(bid.Material.H, 10, 64)
	if len(bid.Material.Videourl) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          bid.Material.Videourl,
			Duration:     float64(bid.Material.Duration),
			Height:       int32(height),
			Width:        int32(width),
		})
	}
	for _, imgUrl := range bid.Material.Imgurl {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          imgUrl,
			Height:       int32(height),
			Width:        int32(width),
		})
	}
	if len(bid.Material.Adm) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          bid.Material.Adm,
			Height:       int32(height),
			Width:        int32(width),
		})
	}

	return creative
}

func (y *YouTuiDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return y.dspSlotRegister
}

// 映射设备类型
func mappingDeviceType(deviceType entity.DeviceType) uint32 {
	switch deviceType {
	case entity.DeviceTypePc:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	case entity.DeviceTypeMobile:
		return 4
	case entity.DeviceTypePad:
		return 5
	default:
		return 4
	}
}

// 映射设备操作系统类型
func mappingOsType(os entity.OsType) string {
	s, ok := entity.OsTypeDict[os]
	if !ok {
		return "other"
	}
	return s
}

func mappingPlatform(os entity.OsType) uint32 {
	switch os {
	case entity.OsTypeIOS:
		return 1
	case entity.OsTypeAndroid:
		return 2
	case entity.OsTypeWindows, entity.OsTypeMacOs:
		return 3
	case entity.OsTypeOtt:
		return 6
	default:
		return 0
	}
}

// 映射设备运营商类型
func mappingCarrierType(carrierType entity.OperatorType) string {
	switch carrierType {
	case entity.OperatorTypeChinaMobile:
		return "46000"
	case entity.OperatorTypeChinaUnicom:
		return "46001"
	case entity.OperatorTypeChinaTelecom:
		return "46003"
	case entity.OperatorTypeTietong:
		return "46020"
	default:
		return "0"
	}
}

// 映射设备网络类型
func mappingConnectionType(connectionType entity.ConnectionType) uint32 {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 0
	}
}

func mappingLandingAction(landType uint32) entity.LandingType {
	switch landType {
	case 1:
		return entity.LandingTypeDownload
	case 2:
		return entity.LandingTypeDeepLink
	default:
		return entity.LandingTypeInWebView
	}
}
