package youtui_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type YouTuiSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`
}

func (info *YouTuiSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	info.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	info.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")

	return nil
}

type YouTuiDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*YouTuiSlotSlotInfo
}

func NewYouTuiDspSlotRegister(dspId utils.ID) *YouTuiDspSlotRegister {
	return &YouTuiDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*YouTuiSlotSlotInfo),
	}
}

func (y *YouTuiDspSlotRegister) GetDspId() utils.ID {
	return y.dspId
}

func (y *YouTuiDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	dspSlotMap := make(map[utils.ID]*YouTuiSlotSlotInfo)
	for _, slotInfo := range list {
		slot := &YouTuiSlotSlotInfo{}
		if err := slot.Init(slotInfo); err != nil {
			zap.L().Error("[YouTuiDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		dspSlotMap[slot.Id] = slot
	}

	y.dspSlotList = list
	y.dspSlotMap = dspSlotMap
	return nil
}

func (y *YouTuiDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return y.dspSlotList
}

func (y *YouTuiDspSlotRegister) GetSlotInfo(slotId utils.ID) *YouTuiSlotSlotInfo {
	return y.dspSlotMap[slotId]
}
