package huichuan_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"strings"
)

type HuichuanSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height     int     `json:"height"`
	Width      int     `json:"width"`
	Category   []int32 `json:"category"`
	AppName    string  `json:"app_name"`
	PkgName    string  `json:"pkg_name"`
	AppVersion string  `json:"app_version"`
}

func (info *HuichuanSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error
	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	categorys, err := dspSlotInfo.ExtraData.GetString("category")
	if err != nil {
		return fmt.Errorf("get category from extra_data failed, err: %v", err)
	}
	info.Category = make([]int32, 0)
	cateArr := strings.Split(categorys, ",")
	if len(cateArr) > 0 {
		for _, cate := range cateArr {
			info.Category = append(info.Category, type_convert.GetAssertInt32(cate))
		}
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	return nil
}

type HuichuanDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*HuichuanSlotSlotInfo
}

func NewHuichuanDspSlotRegister(dspId utils.ID) *HuichuanDspSlotRegister {
	return &HuichuanDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*HuichuanSlotSlotInfo),
	}
}

func (r *HuichuanDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *HuichuanDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*HuichuanSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &HuichuanSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[HuichuanDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *HuichuanDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *HuichuanDspSlotRegister) GetSlotInfo(slotId utils.ID) *HuichuanSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
