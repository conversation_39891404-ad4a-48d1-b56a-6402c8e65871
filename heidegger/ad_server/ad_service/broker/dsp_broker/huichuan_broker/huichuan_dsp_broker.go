package huichuan_broker

import (
	"bytes"
	"encoding/json"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/huichuan_broker/huichuan_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"
	"fmt"
)

type HuichuanDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *HuichuanDspSlotRegister
	bidUrl       string
	dspId        utils.ID

	dspProtocol   string
	iKey          string
	eKey          string
	MacroWinPrice string
}

func NewHuichuanDspBroker(dspId utils.ID) *HuichuanDspBroker {
	return &HuichuanDspBroker{
		slotRegister:  NewHuichuanDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "${AUCTION_PRICE}",
	}
}

func (impl *HuichuanDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *HuichuanDspBroker) GetDspId() utils.ID {
	return impl.dspId
}

func (impl *HuichuanDspBroker) UpdateDspInfo(dsp *entity.Dsp) error {
	impl.bidUrl = dsp.BidUrl
	impl.iKey = dsp.Ikey
	impl.eKey = dsp.Ekey
	impl.dspProtocol = dsp.Protocol
	return nil
}

func (impl *HuichuanDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("HuichuanDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("HuichuanDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))

		return nil, err_code.ErrTooManyCandidateForDsp
	}

	if request.IsDebug {
		reqBody, _ := json.Marshal(request)
		zap.L().Info("HuichuanDspBroker.EncodeRequest Ctx req", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))

	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	deviceId, deviceIdType := trafficData.GetDeviceIdWithType()
	if deviceId == utils.EmptyString || deviceIdType == entity.DeviceIdTypeUnknown {
		return nil, err_code.ErrInvalidDeviceId
	}

	if deviceIdType == entity.DeviceIdTypeRawCaid {
		caidVersion := device_utils.GetCaidVersion(deviceId)
		if len(caidVersion) == 0 {
			return nil, err_code.ErrInvalidDeviceId
		}
	}

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("HuichuanDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	hcRequest := &huichuan_broker_entity.HcAdRequest{
		AdDeviceInfo: impl.encodeDevice(request, trafficData),
		AdAppInfo:    impl.encodeApp(request, trafficData),
		AdGPSInfo: &huichuan_broker_entity.HcAdGPSInfo{
			LNG: type_convert.GetAssertString(request.Device.Lon),
			LAT: type_convert.GetAssertString(request.Device.Lat),
		},
		RequestID:  trafficData.GetRequestId(),
		AdUserInfo: impl.encodeUser(request),
	}
	hcRequest.AdAppInfo.Category = dspSlot.Category

	if len(dspSlot.PkgName) != 0 {
		hcRequest.AdAppInfo.PkgName = dspSlot.PkgName
	}

	if len(dspSlot.AppName) != 0 {
		hcRequest.AdAppInfo.AppName = dspSlot.AppName
	}

	if len(dspSlot.AppVersion) != 0 {
		hcRequest.AdAppInfo.PkgVer = dspSlot.AppVersion
	}

	imp, err := impl.encodeImp(request, trafficData, candidate)
	if err != nil {
		return nil, err_code.ErrInvalidImpression

	}
	hcRequest.AdPosInfo = imp

	//buffer := buffer_pool.NewBufferWriter()
	//defer buffer.Release()
	//16字节头
	dataHeader := make([]byte, 16)
	//buffer.Write(dataHeader)
	//
	//_, err = easyjson.MarshalToWriter(hcRequest, buffer)
	//if err != nil {
	//	zap.L().Error("HuichuanDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
	//	return nil, err_code.ErrInternalBidFail
	//}

	requestBody, err := sonic.Marshal(hcRequest)
	if err != nil {
		zap.L().Error("HuichuanDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err_code.ErrBroadcastRequestBuildFail
	}

	body := append(dataHeader, requestBody...)

	//body := buffer.Get()

	if request.IsDebug {
		zap.L().Info("HuichuanDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(body))))))
	}

	if len(body) <= 16 {
		return nil, err_code.ErrBroadcastRequestBuildFail
	}

	req, err := http.NewRequest(http.MethodPost, impl.bidUrl, bytes.NewBuffer(body))
	if err != nil {
		zap.L().Error("HuichuanDspBroker http NewRequest err", zap.Error(err))
		return nil, err
	}
	req.Header["Content-Type"] = []string{"application/json"}

	impl.SampleDspBroadcastRequest(impl.dspId, dspSlot.Id, candidate, body[16:])

	return req, nil

}

func (impl *HuichuanDspBroker) encodeImp(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData, candidate *ad_service.AdCandidate) ([]*huichuan_broker_entity.HcAdPosInfo, error) {
	imps := make([]*huichuan_broker_entity.HcAdPosInfo, 0)
	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return imps, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	bidFloor := candidate.GetBidFloor()

	imp := &huichuan_broker_entity.HcAdPosInfo{
		SlotType:         "0",
		SlotID:           slotId,
		MediaSlotId:      trafficData.GetSourceSlotId(),
		ReqCnt:           "1",
		AW:               type_convert.GetAssertString(dspSlot.Width),
		AH:               type_convert.GetAssertString(dspSlot.Height),
		Query:            "",
		CpmFloor:         type_convert.GetAssertString(bidFloor.Price),
		VideoMaxduration: type_convert.GetAssertString(request.VideoMaxDuration),
		VideoMinduration: type_convert.GetAssertString(request.VideoMinDuration),
	}

	if imp.AW == "0" || imp.AH == "0" {
		if len(request.SlotSize) > 0 {
			imp.AW = type_convert.GetAssertString(request.SlotSize[0].Width)
			imp.AH = type_convert.GetAssertString(request.SlotSize[0].Height)
		}
	}

	imps = append(imps, imp)

	return imps, nil

}

func (impl *HuichuanDspBroker) encodeUser(request *ad_service.AdRequest) *huichuan_broker_entity.HcAdUserInfo {
	user := &huichuan_broker_entity.HcAdUserInfo{}

	if request.UserGender == entity.UserGenderMan {
		user.Gender = "1"
	} else if request.UserGender == entity.UserGenderWoman {
		user.Gender = "2"
	}
	if request.UserAge > 0 {
		user.Age = strconv.Itoa(int(request.UserAge))
	}
	return user
}

func (impl *HuichuanDspBroker) encodeApp(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) *huichuan_broker_entity.HcAdAppInfo {
	appInfo := &huichuan_broker_entity.HcAdAppInfo{
		FR:      "other",
		IsSSL:   "1",
		PkgName: trafficData.GetAppBundle(),
		PkgVer:  trafficData.GetAppVersion(),
		AppName: trafficData.GetAppName(),
		UA:      trafficData.GetUserAgent(),
	}

	if trafficData.GetOsType() == entity.OsTypeIOS {
		appInfo.FR = "iphone"
	} else if trafficData.GetOsType() == entity.OsTypeAndroid {
		appInfo.FR = "android"
	}

	return appInfo

}

func (impl *HuichuanDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) *huichuan_broker_entity.HcAdDeviceInfo {
	device := &huichuan_broker_entity.HcAdDeviceInfo{
		AndroidID: trafficData.GetAndroidId(),
		IMEI:      trafficData.GetImei(),
		IMEIMd5:   trafficData.GetMd5Idfa(),
		OAID:      trafficData.GetOaid(),
		OAIDMd5:   trafficData.GetMd5Oaid(),
		AAID:      trafficData.GetAaid(),
		CAID:      "",
		UDID:      "",
		OpenUDID:  trafficData.GetOpenUdid(),
		IDFA:      trafficData.GetIdfa(),
		IDFAMd5:   trafficData.GetMd5Idfa(),
		Device:    trafficData.GetModel(),
		OS:        impl.mappingOs(trafficData.GetOsType()),
		OSV:       trafficData.GetOsVersion(),
		SW:        type_convert.GetAssertString(trafficData.GetScreenWidth()),
		SH:        type_convert.GetAssertString(trafficData.GetScreenHeight()),
		Access:    impl.mappingConnectionType(trafficData.GetConnectionType()),
		Carrier:   impl.mappingCarrier(trafficData.GetOperatorType()),
		AID:       "",
		ClientIP:  trafficData.GetRequestIp(),
		DIT:       trafficData.GetDeviceInitTime(),
		SUT:       trafficData.GetDeviceUpgradeTime(),
		SST:       trafficData.GetDeviceStartupTime(),
		Brand:     trafficData.GetBrand(),
	}

	if len(device.DIT) == 0 {
		device.DIT = trafficData.GetBootMark()
	}

	if len(device.SUT) == 0 {
		device.SUT = trafficData.GetUpdateMark()
	}

	if len(device.SST) == 0 {
		device.SST = trafficData.GetBootMark()
	}

	if len(trafficData.GetCaid()) > 0 {
		caidRaw := device_utils.GetCaidRaw(trafficData.GetCaid())
		caidVersion := device_utils.GetCaidVersion(trafficData.GetCaid())
		if len(caidRaw) > 0 && len(caidVersion) > 0 && caidVersion != "0.0" {
			hcCaids := make([]*huichuan_broker_entity.HcCaid, 0)
			hcCaids = append(hcCaids, &huichuan_broker_entity.HcCaid{
				Version: caidVersion,
				Caid:    caidRaw,
			})
			jsonBody, _ := json.Marshal(hcCaids)
			device.CAID = string(jsonBody)
		}
	}

	return device
}

func (impl *HuichuanDspBroker) mappingOs(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "ios"
	case entity.OsTypeAndroid:
		return "android"
	case entity.OsTypeWindowsPhone:
		return "wp"
	default:
		return ""
	}
}

func (impl *HuichuanDspBroker) mappingConnectionType(connectionType entity.ConnectionType) string {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return "Wi-Fi"
	case entity.ConnectionTypeWifi:
		return "Wi-Fi"
	case entity.ConnectionTypeUnknown:
		return "Unknown"
	case entity.ConnectionType2G:
		return "2G"
	case entity.ConnectionType3G:
		return "3G"
	case entity.ConnectionType4G:
		return "4G"
	case entity.ConnectionType5G:
		return "4G"
	default:
		return "Unknown"
	}
}

func (impl *HuichuanDspBroker) mappingCarrier(carrier entity.OperatorType) string {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return "ChinaMobile"
	case entity.OperatorTypeChinaTelecom:
		return "ChinaTelecom"
	case entity.OperatorTypeTietong:
		return "ChinaTietong"
	case entity.OperatorTypeChinaUnicom:
		return "ChinaUnicom"
	default:
		return "Unknown"
	}
}

func (impl *HuichuanDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.dspProtocol).EncodeWithKey(uint64(chargePrice), impl.iKey, impl.eKey)
	if err != nil {
		return ""
	}

	return result
}

func (impl *HuichuanDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("HuichuanDspBroker.ParseResponse Enter")
	broadcastCandidate := broadcastCandidateList[0]

	if resp.StatusCode != 200 {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.StatusCode), type_convert.GetAssertString(resp.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	if len(data) <= 16 {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "3", "body数据错误")
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	response := &huichuan_broker_entity.HcResponse{}
	err = sonic.Unmarshal(data[16:], response)
	if err != nil {
		zap.L().Error("HuichuanDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, data[16:])

	if request.IsDebug {
		resbody, _ := json.Marshal(response)
		zap.L().Info("HuichuanDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}
	impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), response.Code, "")

	if response.Code != "0" || len(response.SlotAd) < 1 || len(response.SlotAd[0].Ad) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)
	for _, resBid := range response.SlotAd[0].Ad {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		if resBid == nil || resBid.AdContent == nil {
			continue
		}

		candidateAd.AppInfo = &entity.AppInfo{
			PackageName:    resBid.AdContent.PackageName,
			AppName:        resBid.AdContent.AppName,
			Icon:           resBid.AdContent.AppLogo,
			AppID:          "",
			AppVersion:     resBid.AdContent.VersionName,
			PackageSize:    0,
			Privacy:        resBid.AdContent.Privacy,
			Permission:     resBid.AdContent.Permission,
			PermissionDesc: nil,
			AppDesc:        "",
			AppDescURL:     resBid.AdContent.FunctionDesc,
			Develop:        resBid.AdContent.Developer,
			AppBeian:       "",
			AppAge:         "",
		}

		if len(resBid.AdContent.MiniAppId) > 0 && len(resBid.AdContent.MiniAppPath) > 0 {
			candidateAd.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   resBid.AdContent.MiniAppId,
				ProgramPath: resBid.AdContent.MiniAppPath,
			}
		}

		candidateAd.AppInfo.AppName = resBid.AdContent.AppName
		candidateAd.AppInfo.PackageName = resBid.AdContent.PackageName

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

		candidateCreative := impl.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		if request.IsDebug {
			resbody, _ := json.Marshal(candidateCreative)
			zap.L().Info("HuichuanDspBroker candidateCreative", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(type_convert.GetAssertInt(resBid.AdContent.DspBidPrice)))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(resBid.AdID)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break
	}

	return result, nil
}

func (impl *HuichuanDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *huichuan_broker_entity.HcAd) *entity.AdMonitorInfo {

	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      bid.CURL,
		LandingAction:         entity.LandingTypeInWebView,
		VideoStartUrlList:     []string{bid.VideoPlayURL},
	}

	if len(bid.SchemeFeedbackURL) > 0 {
		bid.SchemeFeedbackURL += "event=scheme&appcode=0&jump_type=3&clickstm=" + type_convert.GetAssertString(time.Now().Unix())
		tracking.DeepLinkMonitorList = []string{bid.SchemeFeedbackURL}
	}

	if bid.AdAction != nil {
		if bid.AdAction.Action == "download" {
			tracking.LandingAction = entity.LandingTypeDownload
		}
	}

	if len(bid.TURL) == 1 {
		tracking.LandingUrl = bid.TURL[0]
		tracking.H5LandingUrl = bid.TURL[0]
	} else if len(bid.TURL) > 1 {
		tracking.LandingUrl = bid.TURL[0]
		tracking.H5LandingUrl = bid.TURL[0]
		tracking.DownloadUrl = bid.TURL[1]
	}

	for _, impTrack := range bid.VURL {
		if strings.Contains(impTrack, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impTrack, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impTrack)
		}
	}

	if len(bid.WNURL) > 0 {
		if strings.Contains(bid.WNURL, impl.MacroWinPrice) || strings.Contains(bid.WNURL, "${AUCTION_ID}") {
			newImpTrack := strings.ReplaceAll(bid.WNURL, impl.MacroWinPrice, "__DSPWPRICE__")
			newImpTrack = strings.ReplaceAll(newImpTrack, "${AUCTION_ID}", "__REQUESTID__")

			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.WNURL)
		}
	}

	if bid.AdContent != nil {
		if len(bid.AdContent.SchemeUrlAd) > 0 {
			tracking.DeepLinkUrl = bid.AdContent.SchemeUrlAd
		} else {
			tracking.DeepLinkUrl = bid.AdContent.Scheme
		}

		if request.Device.GetOsType() == entity.OsTypeIOS && len(bid.AdContent.AdmFixedUlk) != 0 {
			tracking.DeepLinkUrl = bid.AdContent.AdmFixedUlk
		}

		if request.Device.GetOsType() == entity.OsTypeIOS && len(bid.AdContent.AdmFixedUlk) != 0 {
			tracking.LandingUrl = bid.AdContent.AdmFixedUlk
		}
	}
	return tracking

}

func (impl *HuichuanDspBroker) ParseCreativeData(bid *huichuan_broker_entity.HcAd) *entity.Creative {
	//bid.Style
	//1-信息流大图/2-信息流小图/61-精准大图/62-精准小图，字段描述一致；
	//3-信息流三图/63-精准三图，仅由于为组图多出img_2、img_3相关的两组字段，其余字段描述一致；
	//4-信息流大图下载/5-信息流小图下载/64-精准大图下载/65-精准小图下载，字段描述一致；
	//34-信息流三图下载/66-精准三图下载，仅由于为组图多出img_2、img_3相关的两组字段，其余字段描述一致；
	//24-信息流横版视频/71-信息流竖版视频/73-精准横版视频/75-精准竖版视频，字段描述一致；
	//25-信息流横版视频下载/72-信息流竖版视频下载/74-精准横版视频下载/76-精准竖版视频下载，字段描述一致；
	//95-竖版大图
	//96-竖版大图下载
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.AdID,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	item := bid.AdContent

	if len(item.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         item.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Img1) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          item.Img1,
			Width:        type_convert.GetAssertInt32(item.Img1W),
			Height:       type_convert.GetAssertInt32(item.Img1H),
		}
		creative.MaterialList = append(creative.MaterialList, material)

	}

	if len(item.VideoAliyun1) > 0 {
		videoAliyun := &huichuan_broker_entity.HcAliyunVideoData{}

		err := json.Unmarshal([]byte(item.VideoAliyun1), videoAliyun)
		if err == nil {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          videoAliyun.FD,
				Duration:     type_convert.GetAssertFloat64(item.VideoDuration),
				Width:        type_convert.GetAssertInt32(item.Img1W),
				Height:       type_convert.GetAssertInt32(item.Img1H),
			}

			if len(videoAliyun.FD) == 0 {
				material.Url = videoAliyun.LD
			}

			videowh := videoAliyun.FDWidthHeight
			if len(videoAliyun.FDWidthHeight) == 0 {
				videowh = videoAliyun.LDWidthHeight
			}
			sizeArr := strings.Split(videowh, "*")
			if len(sizeArr) >= 2 {
				material.Width = type_convert.GetAssertInt32(sizeArr[0])
				material.Height = type_convert.GetAssertInt32(sizeArr[1])
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if len(item.AppLogo) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          item.AppLogo,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}
