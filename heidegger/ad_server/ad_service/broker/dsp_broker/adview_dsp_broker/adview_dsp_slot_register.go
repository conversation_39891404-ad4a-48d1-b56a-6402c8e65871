package adview_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type AdviewSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppId      string `json:"app_id"`
	MediaId    string `json:"media_id"`
	Token      string `json:"token"`
	AdType     int    `json:"ad_type"`
	AppVersion string `json:"app_version"`
	PkgName    string `json:"pkg_name"`
	AppName    string `json:"app_name"`
}

func (info *AdviewSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.AppId, err = dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get app_id from extra_data failed, err: %v", err)
	}

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	info.MediaId, err = dspSlotInfo.ExtraData.GetString("media_id")
	if err != nil {
		return fmt.Errorf("get media_id from extra_data failed, err: %v", err)
	}

	info.Token, err = dspSlotInfo.ExtraData.GetString("token")
	if err != nil {
		return fmt.Errorf("get token from extra_data failed, err: %v", err)
	}

	info.AdType, err = dspSlotInfo.ExtraData.GetInt("ad_type")
	if err != nil {
		return fmt.Errorf("get token from ad_type failed, err: %v", err)

	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	return nil
}

type AdviewDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*AdviewSlotInfo
}

func NewAdviewDspSlotRegister(dspId utils.ID) *AdviewDspSlotRegister {
	return &AdviewDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*AdviewSlotInfo),
	}
}

func (r *AdviewDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *AdviewDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*AdviewSlotInfo)
	for _, slot := range list {
		slotItem := &AdviewSlotInfo{}
		if err := slotItem.Init(slot); err != nil {
			zap.L().Error("[AdviewDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = slotItem
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *AdviewDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *AdviewDspSlotRegister) GetSlotInfo(slotId utils.ID) *AdviewSlotInfo {
	return r.dspSlotMap[slotId]
}
