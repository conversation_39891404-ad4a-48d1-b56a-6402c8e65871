package adview_dsp_broker

import (
	"github.com/bytedance/sonic"
	"github.com/google/go-querystring/query"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/adview_dsp_broker/adview_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
	"fmt"
)

type AdviewDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *AdviewDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string
}

func NewAdviewDspBroker(dspId utils.ID) *AdviewDspBroker {
	return &AdviewDspBroker{
		slotRegister:  NewAdviewDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "{WIN_PRICE}",
	}
}

func (impl *AdviewDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *AdviewDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("AdviewDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("AdviewDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))

		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("AdviewDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	advRequest := &adview_dsp_entity.AdViewRequest{
		Ver:         "4.0.8",
		N:           1,
		Appid:       dspSlot.AppId,
		Pt:          dspSlot.AdType,
		Adxid:       dspSlot.MediaId,
		HTML5:       2,
		Secure:      1,
		SupMacro:    1,
		PosId:       slotId,
		Pack:        trafficData.GetAppBundle(),
		W:           int(trafficData.GetSlotWidth()),
		H:           int(trafficData.GetSlotHeight()),
		Tab:         impl.mappingDeviceType(trafficData.GetDeviceType()),
		Ip:          trafficData.GetRequestIp(),
		OS:          impl.mappingOsType(trafficData.GetOsType()),
		Tp:          trafficData.GetModel(),
		Brd:         trafficData.GetBrand(),
		Ua:          trafficData.GetUserAgent(),
		Sw:          int(trafficData.GetScreenWidth()),
		Sh:          int(trafficData.GetScreenHeight()),
		Deny:        float64(trafficData.GetScreenDensity()),
		Sn:          impl.getSn(trafficData),
		Imei:        trafficData.GetImei(),
		Oaid:        trafficData.GetOaid(),
		OaidMd5:     trafficData.GetMd5Oaid(),
		Andid:       trafficData.GetAndroidId(),
		Mac:         trafficData.GetMac(),
		DidMd5:      strings.ToLower(trafficData.GetMd5Imei()),
		DpidMd5:     strings.ToLower(trafficData.GetMd5Idfa()),
		MacMd5:      strings.ToLower(trafficData.GetMd5Mac()),
		Nt:          impl.mappingConnectionType(trafficData.GetConnectionType()),
		Nop:         impl.mappingOperatorType(trafficData.GetOperatorType()),
		Country:     "cn",
		Language:    "zh",
		Lat:         float32(trafficData.GetGeoLatitude()),
		Lon:         float32(trafficData.GetGeoLongitude()),
		LocType:     1,
		Tm:          0,
		Time:        time.Now().UnixMilli(),
		Yob:         0,
		Gender:      "",
		Appver:      trafficData.GetAppVersion(),
		Idfa:        trafficData.GetIdfa(),
		Caid:        device_utils.GetCaidRaw(trafficData.GetCaid()),
		Caidver:     device_utils.GetCaidVersion(trafficData.GetCaid()),
		BootMark:    trafficData.GetBootMark(),
		UpdateMark:  trafficData.GetUpdateMark(),
		Pks:         "",
		Npks:        "",
		Pkgids:      impl.getInstallAppIds(impl.dspId, request.App.InstalledAppIds),
		AppstoreVer: request.Device.VercodeAg,
		Sut:         trafficData.GetDeviceUpgradeTime(),
		Devname:     request.Device.DeviceName,
		Hwmodel:     request.Device.HardwareMachineCode,
		Msize:       request.Device.SystemTotalMem,
		Dsize:       request.Device.SystemTotalDisk,
		Timezone:    "",
		Sbt:         trafficData.GetDeviceStartupTime(),
		Sit:         trafficData.GetDeviceInitTime(),
	}

	if dspSlot.Width > 0 {
		advRequest.W = dspSlot.Width
	}

	if dspSlot.Height > 0 {
		advRequest.H = dspSlot.Height
	}

	bidFloor := candidate.GetBidFloor()
	advRequest.BaseFloor = int(bidFloor.Price * 100)
	if trafficData.GetOsType() == entity.OsTypeIOS {
		advRequest.Brd = "apple"
		if len(request.Device.HardwareMachineCode) > 0 {
			advRequest.Tp = request.Device.HardwareMachineCode
		}
	}

	if strings.Contains(trafficData.GetOsVersion(), "(") && len(trafficData.GetOsVersion()) >= 6 {
		advRequest.Bdr = trafficData.GetOsVersion()[4:6]
	} else {
		advRequest.Bdr = trafficData.GetOsVersion()
	}

	advRequest.SetToken(dspSlot.Token)

	querys, _ := query.Values(advRequest)

	baseURL, _ := url.Parse(impl.GetBidUrl())
	baseURL.RawQuery = querys.Encode()

	bidUrl := baseURL.String()

	if request.IsDebug {
		reqbody, _ := sonic.Marshal(advRequest)
		zap.L().Info("AdviewDspBroker.EncodeRequest end, json Body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(reqbody))))))
		zap.L().Info("AdviewDspBroker.EncodeRequest end, req url", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", bidUrl)))))
	}

	req, err := http.NewRequest(http.MethodGet, bidUrl, nil)
	if err != nil {
		zap.L().Error("AdviewDspBroker http NewRequest err", zap.Error(err))
		return nil, err
	}

	impl.SampleDspBroadcastRequest(impl.dspId, dspSlot.Id, candidate, []byte(bidUrl))
	return req, nil

}

func (impl *AdviewDspBroker) getInstallAppIds(dspId utils.ID, appIds []int) string {
	if len(appIds) == 0 {
		return ""
	}

	dspIdStr := dspId.String()
	result := ""
	isFirst := true
	for _, appid := range appIds {
		key := dspIdStr + "_" + type_convert.GetAssertString(appid)
		externalMapping := impl.ExternalMappingLoader.GetDspAppMappingMapByKey(key)
		if externalMapping != nil {
			if !isFirst {
				result += ","
			}
			isFirst = false
			result += externalMapping.SourceValue
		}
	}

	return result
}

func (impl *AdviewDspBroker) mappingOperatorType(carrier entity.OperatorType) string {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return "46000"
	case entity.OperatorTypeChinaTelecom:
		return "46003"
	case entity.OperatorTypeTietong:
		return "46005"
	case entity.OperatorTypeChinaUnicom:
		return "46001"
	default:
		return ""
	}
}

func (impl *AdviewDspBroker) mappingConnectionType(connectionType entity.ConnectionType) string {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return "wifi"
	case entity.ConnectionTypeWifi:
		return "wifi"
	case entity.ConnectionTypeCellular:
		return "4g"
	case entity.ConnectionType2G:
		return "2g"
	case entity.ConnectionType3G:
		return "3g"
	case entity.ConnectionType4G:
		return "4g"
	case entity.ConnectionType5G:
		return "5g"
	default:
		return "4g"
	}
}

func (impl *AdviewDspBroker) getSn(trafficData ad_service_entity.TrafficData) string {
	if trafficData.GetOsType() == entity.OsTypeIOS {
		if len(trafficData.GetIdfa()) > 0 {
			return trafficData.GetIdfa()
		} else if len(trafficData.GetMd5Idfa()) > 0 {
			return strings.ToLower(trafficData.GetMd5Idfa())
		} else if len(trafficData.GetCaid()) > 0 {
			return device_utils.GetCaidRaw(trafficData.GetCaid())
		} else {
			return ""
		}
	} else {
		if len(trafficData.GetImei()) > 0 {
			return trafficData.GetImei()
		} else if len(trafficData.GetMd5Imei()) > 0 {
			return strings.ToLower(trafficData.GetMd5Imei())
		} else if len(trafficData.GetOaid()) > 0 {
			return trafficData.GetOaid()
		} else if len(trafficData.GetMd5Oaid()) > 0 {
			return strings.ToLower(trafficData.GetMd5Oaid())
		} else if len(trafficData.GetAndroidId()) > 0 {
			return trafficData.GetAndroidId()
		} else {
			return ""
		}
	}
}

func (impl *AdviewDspBroker) mappingOsType(os entity.OsType) int {
	switch os {
	case entity.OsTypeIOS:
		return 1
	case entity.OsTypeAndroid:
		return 0
	default:
		return 0
	}
}

func (impl *AdviewDspBroker) mappingAdType(slotType entity.SlotType) int {
	switch slotType {
	case entity.SlotTypeBanner:
		return 0
	case entity.SlotTypePopup:
		return 1
	case entity.SlotTypeOpening:
		return 4
	case entity.SlotTypeVideo:
		return 5
	case entity.SlotTypeFeeds:
		return 6
	default:
		return 6
	}
}

func (impl *AdviewDspBroker) mappingDeviceType(deviceType entity.DeviceType) int {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return 0
	case entity.DeviceTypePad:
		return 1
	default:
		return 0
	}
}

func (impl *AdviewDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("AdviewDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &adview_dsp_entity.AdViewResponse{}
	resBody, err := impl.ParseSonicJsonHttpResponse(resp, data, response)
	if err != nil {
		zap.L().Error("AdviewDspBroker.AdviewDspBroker json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerParseError
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		zap.L().Info("AdviewDspBroker raw reponse1", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}

	if response.Res != 1 || len(response.Ad) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}
	result := make(ad_service.DspAdCandidateList, 0)

	for _, resBid := range response.Ad {

		if resBid.At == 4 || resBid.At == 10 || resBid.At == 100 {
			return nil, err_code.ErrNotSupportGdt
		}

		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = &entity.AppInfo{
			PackageName: resBid.Dpn,
			AppName:     resBid.Dan,
			Icon:        resBid.Dai,
			AppVersion:  resBid.AppVersion,
			PackageSize: int(resBid.Das),
			Privacy:     resBid.Privacy,
			Permission:  resBid.Permission,
			AppDesc:     resBid.Intro,
			AppDescURL:  "",
			Develop:     resBid.DeveloperName,
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

		candidateCreative := impl.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(resBid.Price / 100))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(resBid.Adi)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break

	}

	return result, nil

}

func (impl *AdviewDspBroker) chargePriceEncoder(chargePrice uint32) string {
	//明文替换
	result, err := impl.PriceManager.PlaintextEncodeMulti(uint64(chargePrice), 100)
	//result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *AdviewDspBroker) ParseCreativeData(bid adview_dsp_entity.AdViewResponseAd) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.Adi,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	w, h := 0, 0

	if split := strings.Split(bid.As, "x"); len(split) == 2 {
		w, _ = strconv.Atoi(split[0])
		h, _ = strconv.Atoi(split[1])
	}

	for _, img := range bid.Api {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          img,
			Width:        int32(w),
			Height:       int32(h),
		}
		creative.MaterialList = append(creative.MaterialList, materialImg)
	}

	if len(bid.Aic) > 0 {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.Aic,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, materialImg)
	}

	if len(bid.Ati) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         bid.Ati,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Ast) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         bid.Ast,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Ate) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         bid.Ate,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Aic) == 0 && len(bid.AdLogo) > 0 {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.AdLogo,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, materialImg)
	}

	if bid.Native != nil {
		if len(bid.Native.Icon.Url) > 0 {
			materialImg := &entity.Material{
				MaterialType: entity.MaterialTypeIcon,
				Url:          bid.Native.Icon.Url,
				Width:        int32(bid.Native.Icon.W),
				Height:       int32(bid.Native.Icon.H),
			}
			creative.MaterialList = append(creative.MaterialList, materialImg)
		}

		for _, img := range bid.Native.Images {
			materialImg := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img.Url,
				Width:        int32(img.W),
				Height:       int32(img.H),
			}
			creative.MaterialList = append(creative.MaterialList, materialImg)
		}
	}

	bidVideo := bid.Video
	if bid.At == 8 && bid.Native != nil && len(bid.Native.Video.VideoUrl) > 0 {
		bidVideo = &bid.Native.Video
	}

	if bidVideo != nil {
		if len(bidVideo.VideoUrl) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          bidVideo.VideoUrl,
				Width:        int32(bidVideo.Width),
				Height:       int32(bidVideo.Height),
				Duration:     float64(bidVideo.Duration),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

		if len(bidVideo.IconUrl) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeIcon,
				Url:          bidVideo.IconUrl,
				Width:        100,
				Height:       100,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

		if len(bidVideo.Title) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeTitle,
				Data:         bidVideo.Title,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

		if len(bidVideo.Desc) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeDesc,
				Data:         bidVideo.Desc,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

		if len(bidVideo.Ext.Preimgurl) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          bidVideo.Ext.Preimgurl,
				Width:        int32(bidVideo.Width),
				Height:       int32(bidVideo.Height),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	return creative

}

func (impl *AdviewDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid adview_dsp_entity.AdViewResponseAd) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		LandingUrl:                     bid.Al,
		H5LandingUrl:                   bid.Al,
		DeepLinkUrl:                    bid.Dl,
		ImpressionMonitorList:          make([]string, 0),
		ClickMonitorList:               make([]string, 0),
		LandingAction:                  entity.LandingTypeInWebView,
		AppDownloadStartedMonitorList:  bid.Surl,
		AppDownloadFinishedMonitorList: bid.Furl,
		AppInstalledFinishMonitorList:  bid.Iurl,
	}

	if len(bid.Dlsuc) > 0 {
		tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, bid.Dlsuc...)
	}

	if len(bid.Ourl) > 0 {
		tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, bid.Ourl...)
	}

	if len(bid.Al) == 0 {
		tracking.LandingUrl = bid.Fallback
		tracking.H5LandingUrl = bid.Fallback
	}

	if len(bid.Wurl) != 0 {
		if strings.Contains(bid.Wurl, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(bid.Wurl, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.Wurl)
		}
	}

	for _, impList := range bid.Es {
		for _, impUrl := range impList {
			if strings.Contains(impUrl, impl.MacroWinPrice) {
				newImpTrack := strings.ReplaceAll(impUrl, impl.MacroWinPrice, "__DSPWPRICE__")
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
			} else {
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impUrl)
			}
		}
	}

	for _, clkUrl := range bid.Ec {
		if strings.Contains(clkUrl, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(clkUrl, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ClickMonitorList = append(tracking.ClickMonitorList, newImpTrack)
		} else {
			tracking.ClickMonitorList = append(tracking.ClickMonitorList, clkUrl)
		}
	}

	if bid.At == 3 || bid.At == 5 || bid.At == 6 || bid.At == 7 || bid.At == 8 {
		bidVideo := bid.Video
		if bid.At == 8 && bid.Native != nil {
			bidVideo = &bid.Native.Video
		}
		if bidVideo != nil {
			if len(bidVideo.Playmonurls) > 0 {
				for _, vUrl := range bidVideo.Playmonurls {
					if strings.Contains(vUrl, impl.MacroWinPrice) {
						newImpTrack := strings.ReplaceAll(vUrl, impl.MacroWinPrice, "__DSPWPRICE__")
						tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, newImpTrack)
					} else {
						tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, vUrl)
					}
				}
			}

			if len(bidVideo.Sptrackers) > 0 {
				for _, vUrl := range bidVideo.Sptrackers {
					if strings.Contains(vUrl, impl.MacroWinPrice) {
						newImpTrack := strings.ReplaceAll(vUrl, impl.MacroWinPrice, "__DSPWPRICE__")
						tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, newImpTrack)
					} else {
						tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, vUrl)
					}
				}
			}

			if len(bidVideo.Cptrackers) > 0 {
				for _, vUrl := range bidVideo.Cptrackers {
					if strings.Contains(vUrl, impl.MacroWinPrice) {
						newImpTrack := strings.ReplaceAll(vUrl, impl.MacroWinPrice, "__DSPWPRICE__")
						tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, newImpTrack)
					} else {
						tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, vUrl)
					}
				}
			}

			if len(bidVideo.Mptrackers) > 0 {
				for _, vUrl := range bidVideo.Mptrackers {
					if strings.Contains(vUrl, impl.MacroWinPrice) {
						newImpTrack := strings.ReplaceAll(vUrl, impl.MacroWinPrice, "__DSPWPRICE__")
						tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
							Url:   newImpTrack,
							Delay: bidVideo.Duration / 2,
						})
					} else {
						tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
							Url:   vUrl,
							Delay: bidVideo.Duration / 2,
						})
					}
				}
			}
		}

		if bid.Act == 2 {
			tracking.LandingAction = entity.LandingTypeDownload
		}

	}

	return tracking

}
