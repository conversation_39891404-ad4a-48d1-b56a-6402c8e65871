package tanx_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type TanxSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppVersion string `json:"app_version"`
	PkgName    string `json:"pkg_name"`
	AppName    string `json:"app_name"`
}

func (info *TanxSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
		return fmt.Errorf("get height from extra_data failed, err: %v", err)
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
		return fmt.Errorf("get width from extra_data failed, err: %v", err)
	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	return nil
}

type TanxDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*TanxSlotSlotInfo
}

func NewTanxDspSlotRegister(dspId utils.ID) *TanxDspSlotRegister {
	return &TanxDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*TanxSlotSlotInfo),
	}
}

func (r *TanxDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *TanxDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*TanxSlotSlotInfo)
	for _, slot := range list {
		qihangSlot := &TanxSlotSlotInfo{}
		if err := qihangSlot.Init(slot); err != nil {
			zap.L().Error("[TanxDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = qihangSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *TanxDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *TanxDspSlotRegister) GetSlotInfo(slotId utils.ID) *TanxSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
