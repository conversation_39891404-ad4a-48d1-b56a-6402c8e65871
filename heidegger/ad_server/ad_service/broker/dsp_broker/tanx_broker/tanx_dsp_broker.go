package tanx_broker

import (
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/tanx_broker/tanx_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

const (
	taobaoAppPkgNameIos     = "com.taobao.taobao4iphone"
	taobaoAppPkgNameAndroid = "com.taobao.taobao"
	TanxLowQualityTag       = 1000204
)

var (
	ErrTanxLowQualityTag = fmt.Errorf("TanxLowQualityTag")
)

type TanxDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister  *TanxDspSlotRegister
	MacroWinPrice string
}

func NewTanxDspBroker(dspId utils.ID) *TanxDspBroker {
	return &TanxDspBroker{
		slotRegister:  NewTanxDspSlotRegister(dspId),
		MacroWinPrice: "%%SETTLE_PRICE%%",
	}
}

func (b *TanxDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.slotRegister
}

func (b *TanxDspBroker) isDeviceEmpty(device *tanx_proto.Request_Device) bool {

	switch device.Os {
	case "Android":
		if len(device.Imei) == 0 && len(device.ImeiMd5) == 0 && len(device.Oaid) == 0 && len(device.OaidMd5) == 0 && len(device.AndroidId) == 0 {
			return true
		}
	case "iOS":
		if len(device.Imei) == 0 && len(device.ImeiMd5) == 0 && len(device.Idfa) == 0 && len(device.IdfaMd5) == 0 && len(device.Caids) == 0 {
			return true
		}
	}

	return false
}

func (b *TanxDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := b.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	tanxRequest := &tanx_proto.Request{
		Version:       2,
		Id:            trafficData.GetRequestId(),
		Imp:           make([]*tanx_proto.Request_Impression, 0),
		Site:          b.BuildTanxSiteDetails(trafficData),
		Device:        b.BuildTanxDeviceDetails(request, trafficData),
		App:           b.BuildTanxAppDetails(trafficData),
		User:          b.BuildTanxUserDetails(trafficData),
		HttpsRequired: proto.Bool(request.UseHttps),
	}

	if b.isDeviceEmpty(tanxRequest.Device) {
		return nil, err_code.ErrInvalidDeviceId
	}

	if len(dspSlot.AppVersion) > 0 {
		tanxRequest.App.Version = dspSlot.AppVersion
	}

	if len(dspSlot.PkgName) > 0 {
		tanxRequest.App.PackageName = dspSlot.PkgName
	}

	if len(dspSlot.AppName) > 0 {
		tanxRequest.App.AppName = dspSlot.AppName
	}

	appList := b.AppListLoader.GetAppByBundle(tanxRequest.App.PackageName)
	if appList != nil {
		tanxRequest.App.Category = []string{appList.Category}
	}

	impressions := make([]*tanx_proto.Request_Impression, 0)

	width := int32(dspSlot.Width)
	height := int32(dspSlot.Height)
	if width == 0 || height == 0 {
		if len(request.SlotSize) > 0 {
			width = int32(request.SlotSize[0].Width)
			height = int32(request.SlotSize[0].Height)
		}
	}

	tanxImpression := &tanx_proto.Request_Impression{
		Id:      0,
		Pid:     slotId,
		Width:   width,
		Height:  height,
		Pos:     1,
		Api:     []int32{1, 2, 3, 4, 5},
		SlotNum: proto.Int32(1),
		//Video:        nil,
		//IsFullscreen: nil,
		//Api:          nil,
		//Deal:         nil,
		//CampaignDate:     nil,
		//NativeTemplateId: nil,
		//TopCreatives:     nil,
	}

	bidFloor := candidate.GetBidFloor()
	tanxImpression.BidFloor = int32(bidFloor.Price)

	//deals := make(map[int64]*tanx_proto.Request_Impression_Deal)
	//for _, candidate := range ctx.Candidate.GetAllCandidates() {
	//	if candidate.GetSettleType() != dict.SettlementTypePdb && candidate.GetSettleType() != dict.SettlementTypePd {
	//		continue
	//	}
	//
	//	if tanxDeal, ok := deals[candidate.AdInfo.AdGroupInfo.Key]; ok {
	//		if uint64(tanxDeal.GetMinPrice()) > candidate.AdInfo.SettlePrice.StaticChargePrice {
	//			tanxDeal.MinPrice = proto.Int32(int32(candidate.AdInfo.SettlePrice.StaticChargePrice))
	//		}
	//	} else {
	//		deals[candidate.AdInfo.AdGroupInfo.Key] = &Tanx.Request_Impression_Deal{
	//			DealId:   proto.String(commonStrings.ToString(candidate.AdInfo.AdGroupInfo.Key)),
	//			MinPrice: proto.Int32(int32(candidate.AdInfo.SettlePrice.StaticChargePrice)),
	//		}
	//	}
	//}

	//tanxImpression.Deal = make([]*tanx_proto.Request_Impression_Deal, 0)
	//for _, deal := range deals {
	//	tanxImpression.Deal = append(tanxImpression.Deal, deal)
	//}

	if request.VideoMaxDuration > 0 {
		tanxImpression.Video = &tanx_proto.Request_Impression_Video{
			MinDuration: request.VideoMinDuration,
			MaxDuration: request.VideoMaxDuration,
		}
	}
	impressions = append(impressions, tanxImpression)

	tanxRequest.Imp = impressions

	buffer := buffer_pool.NewBuffer()
	defer buffer.Release()
	buffer.EnsureSize(tanxRequest.Size())

	_, err := tanxRequest.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		zap.L().Error("TanxDspBroker MarshalToSizedBuffer err", zap.Error(err))
		return nil, err
	}

	req, err := http.NewRequest("POST", b.GetBidUrl(), buffer.GetReadCloser())
	if err != nil {
		zap.L().Error("TanxDspBroker http NewRequest err", zap.Error(err))
		return nil, err
	}

	b.SampleDspBroadcastProtobufRequest(b.GetDspId(), dspSlot.Id, candidate, tanxRequest)

	if request.IsDebug == true {
		zap.L().Info("TanxDspBroker url:, send:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", b.GetBidUrl())))), tanxRequest.DumpJson())
	}
	return req, err
}
func (b *TanxDspBroker) BuildTanxSiteDetails(traffic ad_service_entity.TrafficData) *tanx_proto.Request_Site {
	return nil
}

func (b *TanxDspBroker) BuildTanxUserDetails(traffic ad_service_entity.TrafficData) *tanx_proto.Request_User {
	// cookie mapping 后实现
	return &tanx_proto.Request_User{}
}

func (b *TanxDspBroker) BuildTanxAppDetails(traffic ad_service_entity.TrafficData) *tanx_proto.Request_App {
	return &tanx_proto.Request_App{
		PackageName: traffic.GetAppBundle(),
		AppName:     traffic.GetAppName(),
		Category:    nil,
		Version:     traffic.GetAppVersion(),
	}

	//TODO: 通过MappingData 对接AppCategory信息
}

func (b *TanxDspBroker) ToTanxDeviceType(t entity.DeviceType) int32 {
	switch t {
	case entity.DeviceTypePad:
		return 1
	case entity.DeviceTypeMobile:
		return 0
	case entity.DeviceTypePc:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	}
	return 0
}

func (b *TanxDspBroker) BuildTanxDeviceDetails(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) *tanx_proto.Request_Device {
	tanxDevice := &tanx_proto.Request_Device{
		Ip:         trafficData.GetRequestIp(),
		UserAgent:  trafficData.GetUserAgent(),
		Idfa:       trafficData.GetIdfa(),
		Imei:       trafficData.GetImei(),
		ImeiMd5:    trafficData.GetMd5Imei(),
		Mac:        trafficData.GetMac(),
		MacMd5:     trafficData.GetMd5Mac(),
		AndroidId:  trafficData.GetAndroidId(),
		DeviceType: b.ToTanxDeviceType(trafficData.GetDeviceType()),
		Brand:      trafficData.GetBrand(),
		Model:      trafficData.GetModel(),
		Os:         ToTanxOsType(trafficData.GetOsType()),
		Osv:        trafficData.GetOsVersion(),
		Network:    proto.Int32(ToTanxNetType(trafficData.GetConnectionType())),
		Operator:   ToTanxISPOperator(trafficData.GetOperatorType()),
		Width:      trafficData.GetScreenWidth(),
		Height:     trafficData.GetScreenHeight(),
		Oaid:       trafficData.GetOaid(),

		AliAaid: trafficData.GetAaid(),
		Caids: []*tanx_proto.Request_Device_CAID{
			{
				Ver:  device_utils.GetCaidVersion(trafficData.GetCaid()),
				Caid: device_utils.GetCaidReverse(trafficData.GetCaid()),
			},
		},
		BootMark:   trafficData.GetBootMark(),
		UpdateMark: trafficData.GetUpdateMark(),
		IdfaMd5:    trafficData.GetMd5Idfa(),
		OaidMd5:    trafficData.GetMd5Oaid(),
	}

	if trafficData.GetScreenWidth() > trafficData.GetScreenHeight() {
		tanxDevice.Orientation = 1
	} else {
		tanxDevice.Orientation = 2
	}

	if request.Device.Lat > float64(0) && request.Device.Lon > float64(0) {
		tanxDevice.Geo = &tanx_proto.Request_Device_Geo{
			Lat: request.Device.Lat,
			Lon: request.Device.Lon,
		}
	}

	if len(request.App.InstalledApp) > 0 {
		tanxDevice.InstalledApp = request.App.InstalledApp
	}

	return tanxDevice
}

func ToTanxOsType(t entity.OsType) string {
	switch t {
	case entity.OsTypeAndroid:
		return "Android"
	case entity.OsTypeIOS:
		return "iOS"
	}
	return "unknown"
}

func ToTanxISPOperator(t entity.OperatorType) int32 {
	switch t {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 2
	case entity.OperatorTypeChinaTelecom:
		return 3
	}
	return 0
}

func ToTanxNetType(t entity.ConnectionType) int32 {
	switch t {
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	}
	return 0
}

func (b *TanxDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if resp.StatusCode != 200 {
		b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.StatusCode), type_convert.GetAssertString(resp.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	buffer, err := io.ReadAll(resp.Body)
	if err != nil {
		b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")

		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	tanxResponse := &tanx_proto.Response{}
	if err := proto.Unmarshal(buffer, tanxResponse); err != nil {
		b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	if request.IsDebug {
		zap.L().Info("tanx.ParseResponse response, status:, msg:%s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(tanxResponse.GetStatus())))), tanxResponse.GetMsg())
	}

	b.SampleDspBroadcastProtobufResponse(b.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, tanxResponse)

	b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(tanxResponse.GetStatus()), tanxResponse.GetMsg())

	if tanxResponse.GetStatus() != 0 {
		b.markTanxStatus(request, broadcastCandidate, tanxResponse.GetStatus())
		return nil, err_code.ErrBroadcastNoBidding
	}

	if len(tanxResponse.Seat) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	if request.IsDebug == true {
		zap.L().Info("tanx.ParseResponse response, status:, msg:%s, body:%s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(tanxResponse.GetStatus())))), tanxResponse.GetMsg(), tanxResponse.DumpJson())
	}

	result := make(ad_service.DspAdCandidateList, 0, len(tanxResponse.Seat[0].GetAd()))
	for _, tanxBid := range tanxResponse.Seat[0].GetAd() {
		candidateAd := &entity.Ad{
			DspId:         b.GetDspId(),
			DspSlotId:     broadcastCandidate.GetDspSlotId(),
			DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
			AdMonitorInfo: b.ParseTrackingData(request, tanxBid),
		}
		candidateAd.AppInfo = &entity.AppInfo{}
		if request.Device.GetOsType() == entity.OsTypeIOS {
			candidateAd.AppInfo.PackageName = taobaoAppPkgNameIos
		} else {
			candidateAd.AppInfo.PackageName = taobaoAppPkgNameAndroid
		}

		candidateCreative := b.ParseCreativeData(tanxBid)

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(b.chargePriceEncoder)
		candidate.SetBidPrice(uint32(tanxBid.GetBidPrice()))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspProtocol(b.GetDspProtocol())
		result = append(result, candidate)
	}

	return result, nil
}

func (b *TanxDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := b.PriceManager.GetDspCoder(b.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), b.GetIKey(), b.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (b *TanxDspBroker) ParseCreativeData(bid *tanx_proto.Response_Seat_Ad) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.CreativeId,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	//switch bid.CreativeType {
	//case TanxRespCreativeType_txt:
	//	material.MimeType = dict.MimeText
	//case TanxRespCreativeType_img:
	//	material.MimeType = dict.MimeJpg
	////case TanxRespCreativeType_flash:
	////	material.MimeType = dict.MimeJpg
	//case TanxRespCreativeType_video:
	//	material.MimeType = dict.MimeMp4
	//}

	img := &entity.Material{
		MaterialType: entity.MaterialTypeImage,
	}

	video := &entity.Material{
		MaterialType: entity.MaterialTypeVideo,
	}

	if bid.NativeAd != nil {
		for _, v := range bid.NativeAd.Attr {
			switch v.Name {
			case "img_url":
				img.Url = v.GetValue()
			case "img_size":
			case "img_width":
				w, _ := strconv.Atoi(v.GetValue())
				img.Width = int32(w)
			case "img_height":
				h, _ := strconv.Atoi(v.GetValue())
				img.Height = int32(h)
			case "title":
				material := &entity.Material{
					MaterialType: entity.MaterialTypeTitle,
					Data:         v.GetValue(),
				}

				creative.MaterialList = append(creative.MaterialList, material)
			case "description":
				material := &entity.Material{
					MaterialType: entity.MaterialTypeDesc,
					Data:         v.GetValue(),
				}
				creative.MaterialList = append(creative.MaterialList, material)
			case "img_url2":
				creative.MaterialList = append(creative.MaterialList, &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          v.GetValue(),
				})
			case "img_url3":
				creative.MaterialList = append(creative.MaterialList, &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          v.GetValue(),
				})
			case "img_url4":
				creative.MaterialList = append(creative.MaterialList, &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          v.GetValue(),
				})
			case "img_sm":
				creative.MaterialList = append(creative.MaterialList, &entity.Material{
					MaterialType: entity.MaterialTypeIcon,
					Url:          v.GetValue(),
					Width:        200,
					Height:       200,
				})
			case "ad_words":
			case "adv_name":
				//advertiser name
			case "video":
				video.Url = v.GetValue()
			case "video_width":
				w, _ := strconv.Atoi(v.GetValue())
				video.Width = int32(w)
			case "video_height":
				h, _ := strconv.Atoi(v.GetValue())
				video.Height = int32(h)
			case "video_duration":
				duration, _ := strconv.Atoi(v.GetValue())
				video.Duration = float64(duration)
			case "video_md5":
			case "sku_id":
			case "item_id":
			case "action_type":
			}
			//img_url	URL字符串	广告展示素材对应的URL
			//img_size	整型数值	图片素材尺寸
			//img_width	整型数值	图片素材宽度
			//img_height	整型数值	图片素材高度
			//title	字符串	广告标题
			//description	字符串	广告描述
			//img_url2	URL字符串	第二个图片，多图素材样式使用
			//img_url3	URL字符串	第三个图片，多图素材样式使用
			//img_url4	URL字符串	第四个图片，多图素材样式使用
			//img_sm	字符串	缩略图
			//ad_words	字符串	广告主
			//adv_name	字符串	广告主名称
			//video	URL字符串	视频素材地址
			//video_width	整型数值	视频尺寸宽度
			//video_height	整型数值	视频尺寸高度
			//video_duration	整型数值	视频时长，单位毫秒ms
			//video_md5	字符串	视频素材的md5值
			//sku_id	字符串	商品属性id，用于互动场景
			//item_id	字符串	商品id，用于互动场景
			//action_type	字符串	互动方式标识，1-加购,2-收藏，多种交互方式以逗号隔开
		}
	}

	if len(img.Url) > 0 {
		for index, item := range creative.MaterialList {
			switch item.MaterialType {
			case entity.MaterialTypeImage:
				if item.Width == 0 {
					creative.MaterialList[index].Width = img.Width
					creative.MaterialList[index].Width = img.Height
				}
			}
		}
		creative.MaterialList = append(creative.MaterialList, img)
	}

	if len(video.Url) > 0 {
		for index, item := range creative.MaterialList {
			switch item.MaterialType {
			case entity.MaterialTypeVideo:
				if item.Width == 0 {
					creative.MaterialList[index].Width = video.Width
					creative.MaterialList[index].Width = video.Height
				}
			}
		}
		creative.MaterialList = append(creative.MaterialList, video)
	}

	return creative
}

func (b *TanxDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *tanx_proto.Response_Seat_Ad) *entity.AdMonitorInfo {
	trackings := entity.NewAdMonitorInfo()
	trackings.LandingUrl = bid.GetClickThroughUrl()
	trackings.LandingAction = entity.LandingTypeInWebView
	trackings.ClickMonitorList = bid.ClickTrackingUrl

	if len(bid.GetWinnoticeUrl()) > 0 {
		if strings.Contains(bid.GetWinnoticeUrl(), b.MacroWinPrice) {
			newNurl := strings.ReplaceAll(bid.GetWinnoticeUrl(), b.MacroWinPrice, "__DSPWPRICE__")
			trackings.AddWinNoticeUrl(newNurl)
		} else {
			trackings.AddWinNoticeUrl(bid.GetWinnoticeUrl())
		}
	}

	for _, impTrack := range bid.GetImpressionTrackingUrl() {
		if strings.Contains(impTrack, b.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impTrack, b.MacroWinPrice, "__DSPWPRICE__")
			trackings.AddImpressionMonitor(newImpTrack)
		} else {
			trackings.AddImpressionMonitor(impTrack)
		}
	}

	if len(bid.GetDownloadUrl()) > 0 {
		trackings.LandingUrl = bid.GetDownloadUrl()
		trackings.LandingAction = entity.LandingTypeDownload
	}

	trackings.DeepLinkUrl = bid.GetDeeplinkUrl()

	if bid.OpenType == 4 && request.Device.GetOsType() == entity.OsTypeIOS {
		trackings.DeepLinkUrl = bid.GetClickThroughUrl()
	}

	if len(bid.EventTrack) > 0 {
		trackings.DelayMonitorUrlList = make([]entity.AdDelayMonitor, 0)
		for _, v := range bid.EventTrack {
			for _, url := range v.Url {
				trackings.DelayMonitorUrlList = append(trackings.DelayMonitorUrlList, entity.AdDelayMonitor{
					Delay: int(v.GetTime()),
					Url:   url,
				})
			}

		}
	}
	return trackings
}

func (b *TanxDspBroker) CheckBroadcastContext(ctx *ad_service.AdRequest, candidateList ad_service.AdCandidateList) error {
	if ctx.UserSegment.ContainsTag(TanxLowQualityTag) || ctx.UserSegment.ContainsTag(entity.UserTagTanxLowQualityTag) {
		if time_utils.GetTimeUnixSecond()%2 == 0 {
			//zap.L().Info("TanxDspBroker CheckBroadcastContext user segment contains 1000204, tag:%s, ad", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(ctx.UserSegment.DumpJson())))), candidateList[0].GetAd().GetAdId())
			return ErrTanxLowQualityTag
		}
	}

	return nil
}

func (b *TanxDspBroker) markTanxStatus(request *ad_service.AdRequest, candidate *ad_service.AdCandidate, status int32) {
	if status != TanxLowQualityTag {
		return
	}

	if b.GetUserSegmentClient() == nil {
		return
	}

	trafficData := candidate.GetModifiedTrafficData()
	deviceId, _ := trafficData.GetDeviceIdWithType()

	// seconds to the end of the day
	now := time.Now()
	expireTime := (24-now.Hour())*3600 - now.Minute()*60 - now.Second()
	if expireTime <= 0 {
		return
	}

	if err := b.GetUserSegmentClient().AddUserSegmentAsync(deviceId, entity.UserTagTanxLowQualityTag, 1, uint32(expireTime), 0); err != nil {
		zap.L().Error("tanx markTanxStatus AddUserSegmentAsync err", zap.Error(err))
		return
	}
}
