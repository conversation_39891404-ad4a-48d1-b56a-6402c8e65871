package bayes_dsp_broker

import (
	"io"
	"net/http"
	"strconv"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	dsp_entity "gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/bayes_dsp_broker/bayes_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type BayesDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *BayesDspSlotRegister
	log          *zap.Logger
	macroInfo    macro_builder.MonitorMacroInfo
}

func NewBayesDspBroker(dspId utils.ID) *BayesDspBroker {
	return &BayesDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{DspId: dspId},
		slotRegister:  NewBayesDspSlotRegister(dspId),
		log:           zap.L().With(zap.String("broker", "BayesDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__WIN_PRICE__",
			MacroClickDownX: "__DOWN_X__",
			MacroClickDownY: "__DOWN_Y__",
			MacroClickUpX:   "__UP_X__",
			MacroClickUpY:   "__UP_Y__",
		},
	}
}

// var _ DspBrokerInterface = (*BayesDspBroker)(nil)

func (b *BayesDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := b.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	// Slot id in DSP side
	slotKey := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotKey) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}
	candidate.SetDspSlotId(dspSlot.Id)
	candidate.SetDspSlotKey(slotKey)

	// slot size
	var width, height int32
	if dspSlot.Width > 0 && dspSlot.Height > 0 {
		width = int32(dspSlot.Width)
		height = int32(dspSlot.Height)
	}
	if width == 0 || height == 0 {
		width = int32(trafficData.GetSlotWidth())
		height = int32(trafficData.GetSlotHeight())
		if (width == 0 || height == 0) && len(request.SlotSize) > 0 {
			width = int32(request.SlotSize[0].Width)
			height = int32(request.SlotSize[0].Height)
		}
	}

	bidFloor := candidate.GetBidFloor()

	// Request info
	req := new(dsp_entity.BidRequest)
	req.Version = "3.1"
	req.Time = strconv.FormatInt(time_utils.GetTimeUnixSecond()*1000, 10)
	req.Token = ""
	req.ReqId = trafficData.GetRequestId()
	req.AppId = dspSlot.AppId
	req.AppVer = dspSlot.AppVersion
	req.AdSpotId = slotKey
	req.BidFloor = float64(bidFloor.Price)
	req.Bundle = dspSlot.AppBundle

	req.Ip = trafficData.GetRequestIp()
	req.Ua = trafficData.GetWebviewUA()
	if len(req.Ua) < 1 {
		req.Ua = trafficData.GetUserAgent()
	}
	req.Make = trafficData.GetBrand()
	req.Model = trafficData.GetModel()
	req.Os = b.mappingOsType(trafficData.GetOsType())
	req.Osv = trafficData.GetOsVersion()
	req.Carrier = b.mappingOperatorType(trafficData.GetOperatorType())
	req.Network = b.mappingConnectionType(trafficData.GetConnectionType())
	req.ScreenWidth = int(trafficData.GetScreenWidth())
	req.ScreenHeight = int(trafficData.GetScreenHeight())
	req.Ppi = int(request.Device.PPI)
	req.Dpi = int(request.Device.DPI)
	req.Density = request.Device.ScreenDensity
	req.Oaid = trafficData.GetOaid()
	req.Imei = trafficData.GetImei()
	req.ImeiMd5 = trafficData.GetMd5Imei()
	req.Mac = trafficData.GetMac()
	req.AndroidId = trafficData.GetAndroidId()
	req.AndroididMd5 = trafficData.GetMd5AndroidId()
	req.Imsi = request.Device.Imsi
	req.ImsiMd5 = request.Device.ImsiMd5
	req.Idfa = trafficData.GetIdfa()
	req.IdfaMd5 = trafficData.GetMd5Idfa()
	req.Idfv = trafficData.GetIdfv()
	req.IdfvMd5 = trafficData.GetMd5Idfv()
	req.ImpSize = 1
	req.Lat = trafficData.GetGeoLatitude()
	req.Lon = trafficData.GetGeoLongitude()
	req.Devicetype = b.mappingDeviceType(trafficData.GetDeviceType())
	req.Orientation = b.mappingOrientation(trafficData.GetScreenOrientation())
	req.Donottrack = 0
	req.UniqueId = trafficData.GetMac()
	req.AppStoreVersion = request.Device.AppStoreVersion

	req.Device = new(dsp_entity.Device)
	req.Device.Aaid = request.Device.Aaid
	if len(req.Device.Aaid) == 0 {
		req.Device.Aaid = request.Device.AliAaid
	}
	req.Device.BootMark = trafficData.GetBootMark()
	req.Device.UpdateMark = trafficData.GetUpdateMark()
	req.Device.BirthTime = trafficData.GetDeviceInitTime()
	req.Device.BootTime = trafficData.GetDeviceStartupTime()
	req.Device.UpdateTime = trafficData.GetDeviceUpgradeTime()
	req.Device.Paid = request.Device.Paid
	if len(trafficData.GetCaid()) > 0 {
		caidVersion := device_utils.GetCaidVersion(trafficData.GetCaid())
		caid := device_utils.GetCaidRaw(trafficData.GetCaid())
		req.Device.CaidVersion = caidVersion
		req.Device.Caid = caid
		req.Device.CaidList = append(req.Device.CaidList, &dsp_entity.Caid{
			Caid:    caid,
			Version: caidVersion,
		})
	}
	if len(request.Device.Caids) > 0 {
		for _, item := range request.Device.Caids {
			if item == trafficData.GetCaid() {
				continue
			}
			req.Device.CaidList = append(req.Device.CaidList, &dsp_entity.Caid{
				Caid:    device_utils.GetCaidRaw(item),
				Version: device_utils.GetCaidVersion(item),
			})
		}
	}

	req.Ext = new(dsp_entity.RequesetExt)
	req.Ext.VerCodeOfHms = request.Device.VercodeHms
	req.Ext.VerCodeOfAG = request.Device.VercodeAg
	req.Ext.AgCountryCode = request.Device.CountryCode

	// Token
	req.Token = md5_utils.GetMd5String(dspSlot.AppId + dspSlot.AppKey + req.Time)

	// Build
	httpReq, _, err := b.BuildSonicJsonHttpRequest(req)
	if err != nil {
		b.log.WithError(err).Error("BuildSonicJsonHttpRequest error")
		return nil, err_code.ErrBroadcastRequestBuildFail.Wrap(err)
	}
	b.SampleDspBroadcastSonicJsonRequest(b.GetDspId(), trafficData.GetDspSlotId(), candidate, req)
	if request.IsDebug {
		payload, _ := sonic.Marshal(req)
		b.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}
	return httpReq, nil
}

func (b *BayesDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if response.StatusCode != 200 {
		b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			type_convert.GetAssertString(response.StatusCode),
			type_convert.GetAssertString(response.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	// Body will be closed in `BroadcastDspClient`
	data, err := io.ReadAll(response.Body)
	if err != nil {
		b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			"1", "读取body失败")
		return nil, err_code.ErrBrokerResponse
	}

	resp := new(dsp_entity.BidResponse)
	payload, err := b.ParseSonicJsonHttpResponse(response, data, resp)
	if err != nil {
		b.log.WithError(err).Warn("ParseResponse error")
		b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
			broadcastCandidate.GetDspSlotId().String(),
			"2", "body反序列化失败")
		return nil, err_code.ErrBrokerParseError.Wrap(err)
	}

	b.SampleDspBroadcastResponse(b.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, payload)
	if request.IsDebug {
		payload, _ := sonic.Marshal(resp)
		b.log.WithField("response", string(payload)).Info("ParseResponse debug")
	}
	b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(),
		broadcastCandidate.GetDspSlotId().String(),
		type_convert.GetAssertString(resp.Code),
		resp.Msg)

	if resp.Code != 200 || len(resp.Imp) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0, 1)
	for _, imp := range resp.Imp {
		// skip GDT ad
		if imp.IsGdt == 1 {
			return nil, err_code.ErrNotSupportGdt
		}

		ad := &entity.Ad{
			DspId:         b.GetDspId(),
			DspSlotId:     broadcastCandidate.GetDspSlotId(),
			DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
			AdMonitorInfo: b.parseCallbacks(imp),
			AppInfo:       b.parseApp(imp),
		}

		creative := b.parseCreative(imp)

		b.replaceMacro(request, ad.AdMonitorInfo, broadcastCandidate.GetModifiedTrafficData())

		candidate := ad_service.NewDspAdCandidateWithPool(ad)
		candidate.SetAdCandidateChargePriceEncoder(b.chargePriceEncoder)
		candidate.SetBidPrice(uint32(imp.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(creative)
		candidate.SetDspAdID(imp.MaterialId)
		candidate.SetDspProtocol(b.GetDspProtocol())
		result = append(result, candidate)
		break
	}

	return result, nil
}

func (b *BayesDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.slotRegister
}

func (b *BayesDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := b.PriceManager.GetDspCoder(b.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), b.GetIKey(), b.GetEKey())
	if err != nil {
		b.log.WithError(err).WithField("price", chargePrice).Error("chargePriceEncoder error")
		return ""
	}

	return result
}

func (b *BayesDspBroker) mappingOrientation(oType entity.ScreenOrientationType) int {
	switch oType {
	case entity.ScreenOrientationTypeLandscape:
		return 2
	case entity.ScreenOrientationTypePortrait:
		return 1
	default:
		return 0
	}
}

func (b *BayesDspBroker) mappingDeviceType(deviceType entity.DeviceType) int {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	default:
		return 0
	}
}

func (b *BayesDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	case entity.ConnectionTypeNetEthernet:
		return 6
	case entity.ConnectionTypeCellular:
		return 7
	default:
		return 0
	}
}

func (b *BayesDspBroker) mappingOsType(osType entity.OsType) int {
	switch osType {
	case entity.OsTypeIOS:
		return 1
	case entity.OsTypeAndroid:
		return 2
	case entity.OsTypeWindows, entity.OsTypeWindowsPhone:
		return 3
	default:
		return 0
	}
}

func (b *BayesDspBroker) mappingOperatorType(operatorType entity.OperatorType) string {
	switch operatorType {
	case entity.OperatorTypeChinaTelecom:
		return "46003"
	case entity.OperatorTypeChinaUnicom:
		return "46001"
	case entity.OperatorTypeChinaMobile:
		return "46000"
	case entity.OperatorTypeTietong:
		return "46020"
	default:
		return ""
	}
}

// Replace macros in monitor urls
func (b *BayesDspBroker) replaceMacro(request *ad_service.AdRequest, monitor *entity.AdMonitorInfo, trafficData ad_service_entity.TrafficData) {
	monitor.ImpressionMonitorList = macro_builder.MacroReplaceList(monitor.ImpressionMonitorList, b.macroInfo)
	monitor.ClickMonitorList = macro_builder.MacroReplaceList(monitor.ClickMonitorList, b.macroInfo)
	monitor.DeepLinkMonitorList = macro_builder.MacroReplaceList(monitor.DeepLinkMonitorList, b.macroInfo)
	monitor.LandingUrl = macro_builder.MacroReplace(monitor.LandingUrl, b.macroInfo)
	monitor.DeepLinkUrl = macro_builder.MacroReplace(monitor.DeepLinkUrl, b.macroInfo)
	monitor.DownloadUrl = macro_builder.MacroReplace(monitor.DownloadUrl, b.macroInfo)
}

func (b *BayesDspBroker) parseCallbacks(data *dsp_entity.Imp) *entity.AdMonitorInfo {
	info := new(entity.AdMonitorInfo)
	info.LandingUrl = data.Link
	info.DeepLinkUrl = data.Deeplink
	if data.Action == 2 {
		info.DownloadUrl = data.Link
	}

	info.ImpressionMonitorList = append(info.ImpressionMonitorList, data.ImpTk...)
	info.ClickMonitorList = append(info.ClickMonitorList, data.ClickTk...)
	info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, data.DeeplinkTk...)
	info.VideoStartUrlList = append(info.VideoStartUrlList, data.StartTk...)
	info.VideoCloseUrlList = append(info.VideoCloseUrlList, data.CloseTk...)
	info.AppDownloadStartedMonitorList = append(info.AppDownloadStartedMonitorList, data.DownloadTk...)
	info.AppDownloadFinishedMonitorList = append(info.AppDownloadFinishedMonitorList, data.DownloadedTk...)
	info.AppInstallStartMonitorList = append(info.AppInstallStartMonitorList, data.InstallTk...)
	info.AppInstalledFinishMonitorList = append(info.AppInstalledFinishMonitorList, data.InstalledTk...)

	if len(info.DownloadUrl) > 0 {
		info.LandingAction = entity.LandingTypeDownload
	} else if len(info.DeepLinkUrl) > 0 {
		info.LandingAction = entity.LandingTypeDeepLink
	} else if data.Ext != nil && len(data.Ext.UserName) > 0 {
		info.LandingAction = entity.LandingTypeWeChatProgram
	} else {
		info.LandingAction = entity.LandingTypeInWebView
	}

	return info
}

func (b *BayesDspBroker) parseCreative(data *dsp_entity.Imp) *entity.Creative {
	creative := &entity.Creative{
		CreativeKey:  data.MaterialId,
		MaterialList: make(entity.MaterialList, 0),
	}

	if len(data.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         data.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(data.Desc) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         data.Desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(data.Logo) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          data.Logo,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(data.Vurl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          data.Vurl,
			Height:       int32(data.Height),
			Width:        int32(data.Width),
			Duration:     float64(data.Duration),
		}
		creative.MaterialList = append(creative.MaterialList, material)

		if len(data.VideoImage) > 0 {
			cover := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          data.VideoImage,
				Height:       int32(data.Height),
				Width:        int32(data.Width),
			}
			creative.MaterialList = append(creative.MaterialList, cover)
		}
	}

	for _, url := range data.Image {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          url,
			Height:       int32(data.Height),
			Width:        int32(data.Width),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}

func (b *BayesDspBroker) parseApp(data *dsp_entity.Imp) *entity.AppInfo {
	appInfo := new(entity.AppInfo)
	app := data.App
	if app != nil {
		appInfo.AppName = app.Name
		appInfo.PackageName = app.Bundle
		appInfo.Develop = app.Developer
		appInfo.AppVersion = app.AppVer
		appInfo.Privacy = app.PrivacyUrl
		appInfo.Permission = app.PermissionUrl
		appInfo.AppDescURL = app.DescUrl
		appInfo.AppDesc = app.Desc
		appInfo.PackageSize = app.Size
		appInfo.Icon = app.Icon

		if len(app.PermissionsDesc) > 0 {
			appInfo.PermissionDesc = make([]entity.PermissionDesc, 0, len(app.PermissionsDesc))
			for k, v := range app.PermissionsDesc {
				appInfo.PermissionDesc = append(appInfo.PermissionDesc, entity.PermissionDesc{
					PermissionLab:  k,
					PermissionDesc: v,
				})
			}
		}
	}

	if data.Ext != nil && len(data.Ext.UserName) > 0 {
		wechat := new(entity.WechatExt)
		wechat.ProgramId = data.Ext.UserName
		wechat.ProgramPath = data.Ext.Path
		appInfo.WechatExt = wechat
	}

	return appInfo
}
