package ximalaya_dsp_broker

import (
	"encoding/json"
	"io"
	"net/http"
	"strconv"
	"strings"

	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/ximalaya_dsp_broker/ximalaya_proto"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/price_coder"
	"gitlab.com/dev/heidegger/library/utils/type_convert"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"fmt"
)

type XimalayaDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *XimalayaSlotRegister
	dspId        utils.ID

	monitorMacro      macro_builder.MonitorMacroInfo
	callbackUrlEncode *price_coder.XmlyDspRtbCoder
}

func NewXimalayaDspBroker(dspId utils.ID) *XimalayaDspBroker {
	return &XimalayaDspBroker{
		slotRegister: NewXimalayaSlotRegister(dspId),
		dspId:        dspId,
		monitorMacro: macro_builder.MonitorMacroInfo{
			MacroWinPrice: "__SETTLE_PRICE__",
			MacroCBURL:    "__CALLBACK_URL__",
		},
		callbackUrlEncode: price_coder.NewXmlyDspRtbCoder("xmly_dsp", "", "qjd4t3eahpsxwr8u"),
	}
}

func (b *XimalayaDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.slotRegister
}

func (b *XimalayaDspBroker) GetDspId() utils.ID {
	return b.dspId
}

func (b *XimalayaDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := b.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) == 0 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	brokerRequest := &ximalaya_proto.BidRequest{
		Id:         trafficData.GetRequestId(),
		ApiVersion: "v1.3",
		Imps:       make([]*ximalaya_proto.BidRequest_Imp, 0),
		App: &ximalaya_proto.BidRequest_App{
			Name:    trafficData.GetAppName(),
			Bundle:  trafficData.GetAppBundle(),
			Version: trafficData.GetOsVersion(),
		},
		Device: &ximalaya_proto.BidRequest_Device{
			Ua:            trafficData.GetUserAgent(),
			Carrier:       b.CarrierType(trafficData.GetOperatorType()),
			NetworkType:   b.ConnectionType(trafficData.GetConnectionType()),
			Make:          trafficData.GetBrand(),
			Model:         trafficData.GetModel(),
			Os:            b.OsType(trafficData.GetOsType()),
			Osv:           trafficData.GetOsVersion(),
			DeviceType:    b.DeviceType(trafficData.GetDeviceType()),
			ScreenHeight:  trafficData.GetScreenHeight(),
			ScreenWidth:   trafficData.GetScreenWidth(),
			Imei:          trafficData.GetImei(),
			ImeiMd5:       trafficData.GetMd5Imei(),
			AndroidId:     trafficData.GetAndroidId(),
			AndroidIdMd5:  trafficData.GetMd5AndroidId(),
			Oaid:          trafficData.GetOaid(),
			OaidMd5:       trafficData.GetMd5Oaid(),
			HmsVersion:    request.Device.VercodeHms,
			AgVersion:     request.Device.VercodeAg,
			AgCountryCode: request.Device.CountryCode,
			BootTs:        request.Device.DeviceStartupTime,
			UpdateTs:      request.Device.DeviceUpgradeTime,
			Idfa:          trafficData.GetIdfa(),
			IdfaMd5:       trafficData.GetMd5Idfa(),
			BirthTs:       request.Device.DeviceInitTime,
		},
		User: &ximalaya_proto.BidRequest_User{
			AppList: strings.Join(request.App.InstalledApp, ","),
		},
		Geo: &ximalaya_proto.BidRequest_Geo{
			Lat:  float32(trafficData.GetGeoLatitude()),
			Lon:  float32(trafficData.GetGeoLongitude()),
			Type: 1,
		},
	}

	if request.Device.IsIp6 {
		brokerRequest.Device.Ipv6 = trafficData.GetRequestIp()
	} else {
		brokerRequest.Device.Ipv4 = trafficData.GetRequestIp()
	}

	brokerRequest.Device.CaidCriteria = &ximalaya_proto.BidRequest_Device_CAIDCriteria{
		CaidBootSec:         trafficData.GetDeviceStartupTime(),
		CaidCountryCode:     request.Device.CountryCode,
		CaidLanguage:        trafficData.GetLanguage(),
		CaidDeviceNameMd5:   request.Device.DeviceName,
		CaidSystemVersion:   trafficData.GetOsVersion(),
		CaidHardwareMachine: request.Device.HardwareMachineCode,
		CaidCarrierInfo: func() string {
			switch trafficData.GetOperatorType() {
			case entity.OperatorTypeChinaMobile:
				return "中国移动"
			case entity.OperatorTypeChinaUnicom:
				return "中国联通"
			case entity.OperatorTypeChinaTelecom:
				return "中国电信"
			default:
				return ""
			}
		}(),
		CaidPhysicalMemoryByte: strconv.FormatInt(request.Device.SystemTotalMem, 10),
		CaidHarddiskSizeByte:   strconv.FormatInt(request.Device.SystemTotalDisk, 10),
		CaidSystemUpdateSec:    request.Device.DeviceUpgradeTime,
		CaidHardwareModel:      trafficData.GetModel(),
		CaidTimeZone:           strconv.FormatInt(int64(request.Device.TimeZone), 10),
		//CaidMntId:              mobile.BootTime,
		CaidFileInitTime: request.Device.DeviceInitTime,
	}

	if len(request.Device.Caids) > 0 {
		brokerRequest.Device.Caids = make([]*ximalaya_proto.BidRequest_Caid, 0)
		for _, caid := range request.Device.Caids {
			if len(caid) > 0 {
				brokerRequest.Device.Caids = append(brokerRequest.Device.Caids, &ximalaya_proto.BidRequest_Caid{
					Version: device_utils.GetCaidVersion(caid),
					Id:      device_utils.GetCaidRaw(caid),
				})
			}
		}
	}

	if len(dspSlot.AppVersion) > 0 {
		brokerRequest.App.Version = dspSlot.AppVersion
	}

	if len(dspSlot.PkgName) > 0 {
		brokerRequest.App.Bundle = dspSlot.PkgName
	}

	if len(dspSlot.AppName) > 0 {
		brokerRequest.App.Name = dspSlot.AppName
	}

	imp := &ximalaya_proto.BidRequest_Imp{}

	imp.Id = candidate.GetAdRequest().ImpressionId
	imp.TagId = slotId
	imp.AdType = int32(dspSlot.AdType)
	imp.BidType = 0

	if len(dspSlot.SlotTemplate) > 0 {
		imp.Assets = dspSlot.SlotTemplate
	}
	bidFloor := candidate.GetBidFloor()
	imp.BidFloor = int64(bidFloor.Price)

	brokerRequest.Imps = append(brokerRequest.Imps, imp)

	req, _, err := b.BuildPbHttpHttpRequest(brokerRequest)
	if err != nil {
		zap.L().Error("XimalayaDspBroker MarshalToSizedBuffer err", zap.Error(err))
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	b.SampleDspBroadcastProtobufRequest(b.dspId, dspSlot.Id, candidate, brokerRequest)
	if request.IsDebug {
		reqbody, _ := json.Marshal(brokerRequest)
		zap.L().Info("XimalayaDspBroker url:, send:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", b.GetBidUrl())))), reqbody)
	}
	return req, err
}

func (b *XimalayaDspBroker) ParseCreativeData(bid *ximalaya_proto.BidResponse_Bid) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.Adm.CreativeId,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}
	//material.MaterialId = util.GetAssertInt64(bid.Adm.GetTemplateId())
	//material.AdxTemplateId = bid.Adm.GetTemplateId()
	if bid.Adm.GetTitle() != "" {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         bid.Adm.GetTitle(),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if bid.Adm.GetDesc() != "" {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         bid.Adm.GetDesc(),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Adm.Images) > 0 {
		for _, admImage := range bid.Adm.Images {
			if admImage == nil {
				continue
			}
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          admImage.GetUrl(),
				Height:       admImage.GetHeight(),
				Width:        admImage.GetWidth(),
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}

	}

	if bid.Adm.Video != nil && bid.Adm.Video.GetVideoUrl() != "" {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          bid.Adm.Video.GetVideoUrl(),
			Height:       bid.Adm.Video.GetHeight(),
			Width:        bid.Adm.Video.GetWidth(),
			Duration:     float64(bid.Adm.Video.GetDuration()),
		}

		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}

func (b *XimalayaDspBroker) ParseTrackingData(bid *ximalaya_proto.BidResponse_Bid) *entity.AdMonitorInfo {
	trackings := &entity.AdMonitorInfo{
		LandingUrl:            bid.Adm.GetLandingPage(),
		H5LandingUrl:          bid.Adm.GetLandingPage(),
		DeepLinkUrl:           bid.Adm.GetDeepLink(),
		ClickMonitorList:      macro_builder.MacroReplaceList(bid.GetClkTrackers(), b.monitorMacro),
		DeepLinkMonitorList:   macro_builder.MacroReplaceList(bid.GetEvokeMonitorUrls(), b.monitorMacro),
		ImpressionMonitorList: macro_builder.MacroReplaceList(append(bid.GetImpTrackers(), bid.GetWinNoticeUrls()...), b.monitorMacro),
	}

	if len(bid.EvokeMonitorUrls) > 0 {
		trackings.DeepLinkMonitorList = bid.EvokeMonitorUrls
	}
	switch bid.Adm.ActionType {
	case ximalaya_proto.BidResponse_Bid_Adm_ACTION_TYPE_LANDING_PAGE:
		trackings.LandingAction = entity.LandingTypeInWebView
	case ximalaya_proto.BidResponse_Bid_Adm_ACTION_TYPE_DEEP_LINK:
		trackings.LandingAction = entity.LandingTypeDeepLink
	case ximalaya_proto.BidResponse_Bid_Adm_ACTION_TYPE_IOS_INSTALL:
		trackings.LandingAction = entity.LandingTypeDeepLink
	case ximalaya_proto.BidResponse_Bid_Adm_ACTION_TYPE_ANDROID_INSTALL:
		trackings.LandingAction = entity.LandingTypeDownload
		if bid.Adm.DownloadMonitor != nil {
			trackings.AppDownloadStartedMonitorList = bid.Adm.DownloadMonitor.DmDownStartMonitorUrls
			trackings.AppDownloadFinishedMonitorList = bid.Adm.DownloadMonitor.DmDownEndMonitorUrls
			trackings.AppInstallStartMonitorList = bid.Adm.DownloadMonitor.DmInstallStartMonitorUrls
			trackings.AppInstalledFinishMonitorList = bid.Adm.DownloadMonitor.DmInstallEndMonitorUrls
		}
	case ximalaya_proto.BidResponse_Bid_Adm_ACTION_TYPE_MINI_PROGRAM:
		trackings.LandingAction = entity.LandingTypeWeChatProgram
	}

	return trackings
}

func (b *XimalayaDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]
	if resp.StatusCode != 200 {
		b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.StatusCode), type_convert.GetAssertString(resp.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	buffer, err := io.ReadAll(resp.Body)
	if err != nil {
		b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	brokerResponse := &ximalaya_proto.BidResponse{}
	if err := b.ParsePbHttpHttpResponse(resp, buffer, brokerResponse); err != nil {
		b.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		zap.L().Debug("XimalayaDspBroker.DecodeResponse json.Unmarshal, err", zap.Error(err))
		return nil, err_code.ErrBrokerParseError
	}

	b.SampleDspBroadcastProtobufResponse(b.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, brokerResponse)
	if request.IsDebug {
		resBody, _ := json.Marshal(brokerResponse)
		zap.L().Info("XimalayaDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}

	// =================================================================
	if len(brokerResponse.SeatBids) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}
	if len(brokerResponse.SeatBids[0].Bids) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0, len(brokerResponse.SeatBids[0].Bids))

	for _, bid := range brokerResponse.SeatBids[0].Bids {
		if bid.Adm == nil {
			continue
		}

		candidateAd := &entity.Ad{
			DspId:         b.GetDspId(),
			AdMonitorInfo: b.ParseTrackingData(bid),
			DspSlotId:     broadcastCandidate.GetDspSlotId(),
			DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
		}

		if bid.Adm.AppInfo != nil {
			candidateAd.AppInfo = &entity.AppInfo{
				PackageName:    bid.Adm.AppInfo.PackageName,
				AppName:        bid.Adm.AppInfo.Name,
				Icon:           bid.Adm.AppInfo.IconImageUrl,
				AppVersion:     bid.Adm.AppInfo.Version,
				PermissionDesc: nil,
				AppDesc:        bid.Adm.AppInfo.Desc,
			}

			appSize, _ := strconv.Atoi(bid.Adm.AppInfo.PackageSize)
			candidateAd.AppInfo.PackageSize = appSize

			if bid.Adm.AppInfo.ComplianceInfo != nil {
				candidateAd.AppInfo.Develop = bid.Adm.AppInfo.ComplianceInfo.Developer
				candidateAd.AppInfo.Privacy = bid.Adm.AppInfo.ComplianceInfo.PrivacyUrl
				candidateAd.AppInfo.Permission = bid.Adm.AppInfo.ComplianceInfo.PermissionsUrl
				candidateAd.AppInfo.AppDescURL = bid.Adm.AppInfo.ComplianceInfo.FunctionDescUrl
				candidateAd.AppInfo.PermissionDesc = make([]entity.PermissionDesc, 0)
				for permissionDescKey, permissionDesc := range bid.Adm.AppInfo.ComplianceInfo.Permission {
					candidateAd.AppInfo.PermissionDesc = append(candidateAd.AppInfo.PermissionDesc, entity.PermissionDesc{
						PermissionLab:  permissionDescKey,
						PermissionDesc: permissionDesc,
					})
				}
			}
			if bid.Adm.MiniProgramInfo != nil {
				candidateAd.AppInfo.WechatExt = &entity.WechatExt{
					ProgramId:   bid.Adm.MiniProgramInfo.ProgramId,
					ProgramPath: bid.Adm.MiniProgramInfo.ProgramPath,
				}
			}
		}

		if len(candidateAd.AdMonitorInfo.DeepLinkUrl) > 0 && candidateAd.AdMonitorInfo.LandingAction == entity.LandingTypeInWebView {
			candidateAd.AdMonitorInfo.LandingAction = entity.LandingTypeDeepLink
		}

		candidateCreative := b.ParseCreativeData(bid)
		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(b.chargePriceEncoder)
		candidate.SetAdCandidateEncodeCBURL(b.encodeURL)
		candidate.SetBidPrice(uint32(bid.GetPrice()))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(bid.Adm.CreativeId)
		candidate.SetDspProtocol(b.GetDspProtocol())
		result = append(result, candidate)
		break
	}

	return result, nil
}

func (b *XimalayaDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := b.PriceManager.GetDspCoder(b.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), b.GetIKey(), b.GetEKey())
	if err != nil {
		zap.L().Error("XimalayaDspBroker encode price error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return ""
	}

	return result
}
func (b *XimalayaDspBroker) encodeURL(url string) string {
	result, err := b.callbackUrlEncode.EncodeURL(url)
	if err != nil {
		zap.L().Error("XimalayaDspBroker encodeURL ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return ""
	}
	return result
}

func (b *XimalayaDspBroker) DeviceType(deviceType entity.DeviceType) int32 {
	switch deviceType {
	case entity.DeviceTypePc:
		return 2
	case entity.DeviceTypeMobile:
		return 0
	case entity.DeviceTypePad:
		return 1
	default:
		return 3
	}
}

func (b *XimalayaDspBroker) OsType(osType entity.OsType) string {
	switch osType {
	case entity.OsTypeAndroid:
		return "android"
	case entity.OsTypeIOS:
		return "ios"
	default:
		return "android"
	}
}

func (b *XimalayaDspBroker) ConnectionType(connectionType entity.ConnectionType) int32 {
	switch connectionType {
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (b *XimalayaDspBroker) CarrierType(operatorType entity.OperatorType) int32 {
	switch operatorType {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 2
	case entity.OperatorTypeChinaTelecom, entity.OperatorTypeTietong:
		return 3
	default:
		return 0
	}
}
