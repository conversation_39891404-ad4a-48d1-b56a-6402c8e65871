package ximalaya_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/ximalaya_dsp_broker/ximalaya_proto"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"strings"
)

type SlotInfo struct {
	*entity.DspSlotInfo
	TemplateIds  string                                 `json:"template_id"`
	Height       int                                    `json:"height"`
	Width        int                                    `json:"width"`
	AdType       int                                    `json:"ad_type"`
	AppVersion   string                                 `json:"app_version"`
	PkgName      string                                 `json:"pkg_name"`
	AppName      string                                 `json:"app_name"`
	SlotTemplate []*ximalaya_proto.BidRequest_Imp_Asset `json:"-"`
}

func (info *SlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")

	info.AdType, err = dspSlotInfo.ExtraData.GetInt("ad_type")

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.TemplateIds, err = dspSlotInfo.ExtraData.GetString("template_id")
	if err != nil {
		return fmt.Errorf("get template_id from extra_data failed, err: %v", err)
	}
	if len(info.TemplateIds) >= 0 {
		slotTemplate := []*ximalaya_proto.BidRequest_Imp_Asset{}
		for _, template := range strings.Split(info.TemplateIds, ",") {
			slotTemplate = append(slotTemplate, &ximalaya_proto.BidRequest_Imp_Asset{
				TemplateId: template,
				Width:      int32(info.Width),
				Height:     int32(info.Height),
			})
			fmt.Println(slotTemplate)
		}
		info.SlotTemplate = slotTemplate
	}
	return nil
}

type XimalayaSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*SlotInfo
}

func NewXimalayaSlotRegister(dspId utils.ID) *XimalayaSlotRegister {
	return &XimalayaSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*SlotInfo),
	}
}

func (r *XimalayaSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *XimalayaSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*SlotInfo)
	for _, slot := range list {
		pddSlot := &SlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[XimalayaSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *XimalayaSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *XimalayaSlotRegister) GetSlotInfo(slotId utils.ID) *SlotInfo {
	return r.dspSlotMap[slotId]
}
