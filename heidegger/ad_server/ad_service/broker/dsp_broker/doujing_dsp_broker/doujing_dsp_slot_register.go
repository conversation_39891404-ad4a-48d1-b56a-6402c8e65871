package doujing_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type DouJingSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppId      int    `json:"app_id"`
	PkgName    string `json:"pkg_name"`
	AppName    string `json:"app_name"`
	AppVersion string `json:"app_version"`
	AdType     int    `json:"ad_type"`
}

func (info *DouJingSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error
	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	appId, err := dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get app_id from extra_data failed, err: %v", err)
	}
	info.AppId = type_convert.GetAssertInt(appId)

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	adType, err := dspSlotInfo.ExtraData.GetString("ad_type")
	if err != nil {
		return fmt.Errorf("get ad_type from extra_data failed, err: %v", err)
	}

	switch adType {
	case "banner":
		info.AdType = 1
	case "native":
		info.AdType = 4
	case "splash":
		info.AdType = 2
	case "rvideo":
		info.AdType = 6
	case "insert":
		info.AdType = 3
	default:
		info.AdType = 4
	}

	return nil
}

type DouJingDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*DouJingSlotSlotInfo
}

func NewDouJingDspSlotRegister(dspId utils.ID) *DouJingDspSlotRegister {
	return &DouJingDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*DouJingSlotSlotInfo),
	}
}

func (r *DouJingDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *DouJingDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*DouJingSlotSlotInfo)
	for _, slot := range list {
		pddSlot := &DouJingSlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[DouJingDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *DouJingDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *DouJingDspSlotRegister) GetSlotInfo(slotId utils.ID) *DouJingSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
