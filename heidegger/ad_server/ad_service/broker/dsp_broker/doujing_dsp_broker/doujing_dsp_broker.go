package doujing_dsp_broker

import (
	"bytes"
	"encoding/json"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/doujing_dsp_broker/doujing_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"strings"
	"time"
	"fmt"
)

type DouJingDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *DouJingDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string
}

func NewDouJingDspBroker(dspId utils.ID) *DouJingDspBroker {
	return &DouJingDspBroker{
		slotRegister:  NewDouJingDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "__WIN_PRICE__",
	}
}

func (impl *DouJingDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *DouJingDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("DouJingDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("DouJingDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))

		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("DouJingDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	if len(trafficData.GetUserAgent()) == 0 {
		return nil, err_code.ErrUAEmpty
	}

	bidFloor := candidate.GetBidFloor()

	deviceId, _ := trafficData.GetDeviceIdWithType()

	adSpace := doujing_dsp_entity.Adspace{
		AdspaceID:   type_convert.GetAssertInt(slotId),
		AdspaceType: dspSlot.AdType,
		Width:       int32(trafficData.GetSlotWidth()),
		Height:      int32(trafficData.GetSlotHeight()),
		Keywords:    "",
		BidFloor:    int(bidFloor.Price),
	}

	if dspSlot.Width != 0 {
		adSpace.Width = int32(dspSlot.Width)
	}

	if dspSlot.Height != 0 {
		adSpace.Height = int32(dspSlot.Height)
	}

	if adSpace.Width == 0 || adSpace.Height == 0 {
		if len(request.SlotSize) > 0 {
			adSpace.Width = int32(request.SlotSize[0].Width)
			adSpace.Height = int32(request.SlotSize[0].Height)
		}
	}

	djRequest := &doujing_dsp_entity.BidRequest{
		Bid:        trafficData.GetRequestId(),
		ApiVersion: "V2.1.9",
		Ua:         trafficData.GetUserAgent(),
		App: doujing_dsp_entity.App{
			AppID:       dspSlot.AppId,
			AppName:     trafficData.GetAppName(),
			PackageName: trafficData.GetAppBundle(),
			AppVersion:  trafficData.GetAppVersion(),
		},
		Device: impl.encodeDevice(request, trafficData),
		Network: doujing_dsp_entity.Network{
			NetworkType: impl.mappingConnectionType(trafficData.GetConnectionType()),
			CarrierID:   impl.mappingOperatorType(trafficData.GetOperatorType()),
			Reffer:      request.Referer,
		},
		Gps: &doujing_dsp_entity.GPS{
			Type:      1,
			Longitude: trafficData.GetGeoLongitude(),
			Latitude:  trafficData.GetGeoLatitude(),
		},
		User:     &doujing_dsp_entity.User{UserId: deviceId},
		Adspaces: []doujing_dsp_entity.Adspace{adSpace},
		AppList:  nil,
		IsDebug:  false,
		WebvUa:   trafficData.GetUserAgent(),
	}

	if len(dspSlot.PkgName) != 0 {
		djRequest.App.PackageName = dspSlot.PkgName
	}

	if len(dspSlot.AppName) != 0 {
		djRequest.App.AppName = dspSlot.AppName
	}

	if len(dspSlot.AppVersion) != 0 {
		djRequest.App.AppVersion = dspSlot.AppVersion
	}

	if request.Device.IsIp6 {
		djRequest.Network.Ipv6 = trafficData.GetRequestIp()
	} else {
		djRequest.Network.IP = trafficData.GetRequestIp()
	}

	//req, _, err := impl.BuildEasyJsonHttpRequest(djRequest)
	//if err != nil {
	//	zap.L().Error("DouJingDspBroker http BuildJsonHttpRequest err", zap.Error(err))
	//	return nil, err
	//}

	requestBody, err := easyjson.Marshal(djRequest)
	if err != nil {
		zap.L().Error("DouJingDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err_code.ErrBroadcastRequestBuildFail
	}

	req, err := http.NewRequest(http.MethodPost, impl.GetBidUrl(), bytes.NewBuffer(requestBody))
	if err != nil {
		zap.L().Error("DouJingDspBroker http NewRequest err", zap.Error(err))
		return nil, err
	}

	if request.IsDebug {
		//reqbody, _ := json.Marshal(djRequest)
		zap.L().Info("DouJingDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", requestBody)))))
	}

	req.Header["Content-Type"] = []string{"application/json;charset=UTF-8"}
	req.Header["Accept-Encoding"] = []string{"deflate,br"}

	impl.SampleDspBroadcastRequest(impl.dspId, dspSlot.Id, candidate, requestBody)

	//impl.SampleDspBroadcastEasyjsonRequest(impl.dspId, dspSlot.Id, candidate, djRequest)

	return req, nil
}

func (impl *DouJingDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) doujing_dsp_entity.Device {
	deviceInfo := doujing_dsp_entity.Device{
		DeviceID:          make([]doujing_dsp_entity.DeviceID, 0),
		OsType:            impl.mappingOsType(trafficData.GetOsType()),
		OsVersion:         trafficData.GetOsVersion(),
		OsApiLevel:        "",
		Vendor:            trafficData.GetBrand(),
		Brand:             trafficData.GetBrand(),
		Model:             trafficData.GetModel(),
		HardwareMachine:   request.Device.HardwareMachineCode,
		FingerPrint:       "",
		DeviceType:        impl.mappingDeviceType(trafficData.GetDeviceType()),
		Language:          "zh_cn",
		ScreenWidth:       trafficData.GetScreenWidth(),
		ScreenHeight:      trafficData.GetScreenHeight(),
		ScreenDensity:     float64(trafficData.GetScreenDensity()),
		Ppi:               request.Device.PPI,
		ScreenSize:        "",
		OsUpdateTime:      0,
		OsStartupTime:     0,
		HwModel:           request.Device.DeviceName,
		HwName:            request.Device.RomName,
		SysMemory:         request.Device.SystemTotalMem,
		SysDiskSize:       request.Device.SystemTotalDisk,
		JailBreak:         false,
		RomVersion:        trafficData.GetRomVersion(),
		SysCompilingTime:  0,
		CpuNum:            0,
		AuthStatus:        0,
		BootTime:          0,
		ScreenOrientation: impl.mappingOrientation(trafficData.GetScreenOrientation()),
		Sensors:           nil,
		Bootmark:          trafficData.GetBootMark(),
		Updatemark:        trafficData.GetUpdateMark(),
		VerCodeOfHms:      request.Device.VercodeHms,
		ClientTime:        time.Now().Format("2006-01-02 15:04:05.000Z0700"),
		VerCodeOfAG:       request.Device.VercodeAg,
		AppStoreVersion:   request.Device.AppStoreVersion,
	}

	if len(trafficData.GetUpdateMark()) > 0 {
		deviceInfo.OsUpdateTime = type_convert.GetAssertInt64(trafficData.GetUpdateMark())
	}

	if trafficData.GetOsType() == entity.OsTypeIOS {

		if len(trafficData.GetIdfa()) > 0 {
			deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
				DeviceID:     trafficData.GetIdfa(),
				DeviceIDType: 2,
				HashType:     0,
			})
		} else if len(trafficData.GetMd5Idfa()) > 0 {
			deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
				DeviceID:     trafficData.GetMd5Idfa(),
				DeviceIDType: 2,
				HashType:     1,
			})
		}

		if len(trafficData.GetCaid()) > 0 {
			deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
				DeviceID:     trafficData.GetCaid(),
				DeviceIDType: 9,
				HashType:     0,
			})
		} else if len(trafficData.GetMd5Caid()) > 0 {
			deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
				DeviceID:     trafficData.GetMd5Caid(),
				DeviceIDType: 9,
				HashType:     1,
			})
		}

		if len(trafficData.GetAaid()) > 0 {
			deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
				DeviceID:     trafficData.GetAaid(),
				DeviceIDType: 5,
				HashType:     0,
			})
		}
	} else {
		if len(trafficData.GetImei()) > 0 {
			deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
				DeviceID:     trafficData.GetImei(),
				DeviceIDType: 1,
				HashType:     0,
			})
		} else if len(trafficData.GetMd5Imei()) > 0 {
			deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
				DeviceID:     trafficData.GetMd5Imei(),
				DeviceIDType: 1,
				HashType:     1,
			})
		}

		if len(trafficData.GetOaid()) > 0 {
			deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
				DeviceID:     trafficData.GetOaid(),
				DeviceIDType: 7,
				HashType:     0,
			})
		} else if len(trafficData.GetMd5Oaid()) > 0 {
			deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
				DeviceID:     trafficData.GetMd5Oaid(),
				DeviceIDType: 7,
				HashType:     1,
			})
		}

		if len(trafficData.GetAndroidId()) > 0 {
			deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
				DeviceID:     trafficData.GetAndroidId(),
				DeviceIDType: 3,
				HashType:     0,
			})
		} else if len(trafficData.GetMd5AndroidId()) > 0 {
			deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
				DeviceID:     trafficData.GetMd5AndroidId(),
				DeviceIDType: 3,
				HashType:     1,
			})
		}

		if len(trafficData.GetAaid()) > 0 {
			deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
				DeviceID:     trafficData.GetAaid(),
				DeviceIDType: 6,
				HashType:     0,
			})
		}

		if len(trafficData.GetOpenUdid()) > 0 {
			deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
				DeviceID:     trafficData.GetOpenUdid(),
				DeviceIDType: 10,
				HashType:     0,
			})
		}
	}

	if len(trafficData.GetMac()) > 0 {
		deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
			DeviceID:     trafficData.GetMac(),
			DeviceIDType: 4,
			HashType:     0,
		})
	} else if len(trafficData.GetMd5Mac()) > 0 {
		deviceInfo.DeviceID = append(deviceInfo.DeviceID, doujing_dsp_entity.DeviceID{
			DeviceID:     trafficData.GetMd5Mac(),
			DeviceIDType: 4,
			HashType:     1,
		})
	}

	return deviceInfo
}

func (impl *DouJingDspBroker) mappingOperatorType(carrier entity.OperatorType) string {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return "70120"
	case entity.OperatorTypeChinaTelecom:
		return "70121"
	case entity.OperatorTypeTietong:
		return "70121"
	case entity.OperatorTypeChinaUnicom:
		return "70123"
	default:
		return "0"
	}
}

func (impl *DouJingDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int32 {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionTypeCellular:
		return 5
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (impl *DouJingDspBroker) mappingSlotType(s entity.SlotType) int {
	switch s {
	case entity.SlotTypeBanner:
		return 1
	case entity.SlotTypeOpening:
		return 2
	case entity.SlotTypePopup:
		return 3
	case entity.SlotTypeFeeds:
		return 4
	case entity.SlotTypeRewardVideo:
		return 6
	default:
		return 1
	}
}

func (impl *DouJingDspBroker) mappingOsType(os entity.OsType) int32 {
	switch os {
	case entity.OsTypeIOS:
		return 1
	case entity.OsTypeAndroid:
		return 2
	case entity.OsTypeWindowsPhone:
		return 3
	default:
		return 0
	}
}

func (impl *DouJingDspBroker) mappingDeviceType(deviceType entity.DeviceType) int32 {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return 2
	case entity.DeviceTypePad:
		return 1
	default:
		return 0
	}
}

func (impl *DouJingDspBroker) mappingOrientation(s entity.ScreenOrientationType) int32 {
	switch s {
	case entity.ScreenOrientationTypeLandscape:
		return 2
	case entity.ScreenOrientationTypePortrait:
		return 1
	}

	return 0
}
func (impl *DouJingDspBroker) chargePriceEncoder(chargePrice uint32) string {
	//明文替换
	result, err := impl.PriceManager.PlaintextEncode(uint64(chargePrice))
	//result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *DouJingDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("DouJingDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &doujing_dsp_entity.BidResponse{}
	//resBody, err := impl.ParseJsonHttpResponse(resp, data, response)
	err = easyjson.Unmarshal(data, response)
	if err != nil {
		zap.L().Error("DouJingDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, data)

	if request.IsDebug {
		resbody, _ := json.Marshal(response)
		zap.L().Info("DouJingDspBroker raw reponse1:  , raw data", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))), zap.String("param2", fmt.Sprintf("%v", data)))
	}

	if response.ErrorCode != 0 || len(response.Ads) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)
	for _, resBid := range response.Ads[0].Creative {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = &entity.AppInfo{
			PackageName: resBid.InteractionObject.PackageName,
			AppName:     resBid.InteractionObject.AppName,
		}

		candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

		candidateCreative := impl.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)

		candidate.SetBidPrice(uint32(resBid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(resBid.AdID)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break

	}
	return result, nil
}

func (impl *DouJingDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid doujing_dsp_entity.Creative) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      make([]string, 0),
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkUrl:           bid.InteractionObject.DeepLink,
		LandingUrl:            bid.InteractionObject.URL,
		H5LandingUrl:          bid.InteractionObject.URL,
	}

	for _, event := range bid.EventTrack {
		switch event.EventType {
		case 1, 99:
			if strings.Contains(event.NotifyURL, impl.MacroWinPrice) {
				newImpTrack := strings.ReplaceAll(event.NotifyURL, impl.MacroWinPrice, "__DSPWPRICE__")
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
			} else {
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, event.NotifyURL)
			}

		case 2, 19:
			tracking.ClickMonitorList = append(tracking.ClickMonitorList, event.NotifyURL)
		case 3, 6, 22, 27:
			tracking.DeepLinkMonitorList = append(tracking.DeepLinkMonitorList, event.NotifyURL)
		case 4, 12:
			tracking.AppDownloadFinishedMonitorList = append(tracking.AppDownloadStartedMonitorList, event.NotifyURL)
		case 5, 13:
			tracking.AppInstalledFinishMonitorList = append(tracking.AppInstalledFinishMonitorList, event.NotifyURL)
		case 8:
			tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, event.NotifyURL)
		case 10, 21:
			tracking.VideoCloseUrlList = append(tracking.VideoCloseUrlList, event.NotifyURL)
		case 11:
			tracking.AppDownloadStartedMonitorList = append(tracking.AppDownloadStartedMonitorList, event.NotifyURL)
		}

	}

	return tracking

}

func (impl *DouJingDspBroker) ParseCreativeData(bid doujing_dsp_entity.Creative) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.AdID,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	item := bid.Adm.AdmNative

	if len(item.AdTitle) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         item.AdTitle,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Description) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         item.Description,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Logo.URL) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeLogo,
			Url:          item.Logo.URL,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Imgs) > 0 {
		for _, img := range item.Imgs {
			if len(img.URL) > 0 {
				material := &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          img.URL,
					Width:        img.Width,
					Height:       img.Height,
				}
				creative.MaterialList = append(creative.MaterialList, material)
			}
		}
	}

	if item.Video != nil && len(item.Video.VideoURL) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          item.Video.VideoURL,
			Width:        int32(item.Video.VideoWidth),
			Height:       int32(item.Video.VideoHeight),
			Duration:     float64(item.Video.VideoDuration),
		}
		creative.MaterialList = append(creative.MaterialList, material)

		if len(item.Video.CoverImgURL) > 0 {
			material1 := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          item.Video.CoverImgURL,
				Width:        int32(item.Video.CoverWidth),
				Height:       int32(item.Video.CoverHeight),
			}
			creative.MaterialList = append(creative.MaterialList, material1)
		}

		if item.Video.EndFrame != nil {
			if len(item.Video.CoverImgURL) == 0 && len(item.Video.EndFrame.Img) > 0 {
				material2 := &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          item.Video.EndFrame.Img,
					Width:        int32(item.Video.VideoWidth),
					Height:       int32(item.Video.VideoHeight),
				}
				creative.MaterialList = append(creative.MaterialList, material2)
			}

			if len(item.Video.EndFrame.Icon) > 0 {
				material2 := &entity.Material{
					MaterialType: entity.MaterialTypeIcon,
					Url:          item.Video.EndFrame.Icon,
					Width:        100,
					Height:       100,
				}
				creative.MaterialList = append(creative.MaterialList, material2)
			}

			if len(item.Video.EndFrame.Title) > 0 {
				material2 := &entity.Material{
					MaterialType: entity.MaterialTypeTitle,
					Data:         item.Video.EndFrame.Title,
				}
				creative.MaterialList = append(creative.MaterialList, material2)
			}

			if len(item.Video.EndFrame.Desc) > 0 {
				material2 := &entity.Material{
					MaterialType: entity.MaterialTypeDesc,
					Data:         item.Video.EndFrame.Desc,
				}
				creative.MaterialList = append(creative.MaterialList, material2)
			}

		}

	}

	return creative
}
