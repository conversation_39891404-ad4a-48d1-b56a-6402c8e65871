package kaweiapu_dsp_broker

import (
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/kaweiapu_dsp_broker/kaweiapu_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"io"
	"net/http"
	"strconv"
	"strings"
)

type KaweiapuDspBroker struct {
	dsp_broker.DspBrokerBase
	dspSlotRegister *KaweiapuDspSlotRegister
	log             *zap.Logger
	macroInfo       macro_builder.MonitorMacroInfo
}

func NewKaweiapuDspBroker(dspId utils.ID) *KaweiapuDspBroker {
	return &KaweiapuDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		dspSlotRegister: NewKaweiapuDspSlotRegister(dspId),
		log:             zap.L().With(zap.String("broker", "KaweiapuDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroClickDownX: "__DOWN_X__",
			MacroClickDownY: "__DOWN_Y__",
			MacroClickUpX:   "__UP_X__",
			MacroClickUpY:   "__UP_Y__",
		},
	}
}

func (k *KaweiapuDspBroker) BuildRequest(request *ad_service.AdRequest,
	candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := k.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	slotKey := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotKey) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(dspSlot.Id)
	candidate.SetDspSlotKey(slotKey)

	bidRequest := &kaweiapu_dsp_entity.BidRequest{
		RequestId: trafficData.GetRequestId(),
		Tmax:      300,
		Imp: kaweiapu_dsp_entity.RequestImp{
			FloorPrice: float64(candidate.GetBidFloor().Price),
		},
		Device: kaweiapu_dsp_entity.RequestDevice{
			Ua:              trafficData.GetUserAgent(),
			Ipv4:            trafficData.GetRequestIp(),
			Lmt:             1,
			Idfa:            trafficData.GetIdfa(),
			IdfaMd5:         trafficData.GetMd5Idfa(),
			Idfv:            trafficData.GetIdfv(),
			IdfvMd5:         trafficData.GetMd5Idfv(),
			Imei:            trafficData.GetImei(),
			ImeiMd5:         trafficData.GetMd5Imei(),
			Oaid:            trafficData.GetOaid(),
			OaidMd5:         trafficData.GetMd5Oaid(),
			AndroidId:       trafficData.GetAndroidId(),
			AndroidIdMd5:    trafficData.GetMd5AndroidId(),
			DeviceType:      mappingDeviceType(trafficData.GetDeviceType()),
			H:               int(trafficData.GetScreenHeight()),
			W:               int(trafficData.GetScreenWidth()),
			Os:              mappingOs(trafficData.GetOsType()),
			Osv:             trafficData.GetOsVersion(),
			Make:            trafficData.GetBrand(),
			Model:           trafficData.GetModel(),
			Carrier:         mappingCarrier(trafficData.GetOperatorType()),
			ConnType:        mappingConnType(trafficData.GetConnectionType()),
			BootSecond:      trafficData.GetDeviceStartupTime(),
			UpdateSecond:    trafficData.GetDeviceUpgradeTime(),
			BatteryStatus:   "Unplugged",
			Caid:            device_utils.GetCaidRaw(trafficData.GetCaid()),
			CaidVersion:     device_utils.GetCaidVersion(trafficData.GetCaid()),
			CountryCode:     request.Device.CountryCode,
			Language:        trafficData.GetLanguage(),
			PhoneNameMd5:    md5_utils.GetMd5String(request.Device.DeviceName),
			MemorySize:      float64(request.Device.SystemTotalMem),
			DiskSize:        float64(request.Device.SystemTotalDisk),
			TimeZone:        strconv.FormatInt(int64(request.Device.TimeZone), 10),
			Geo:             kaweiapu_dsp_entity.Geo{},
			BirthTime:       trafficData.GetDeviceInitTime(),
			ComplingTime:    trafficData.GetDeviceUpgradeTime(),
			BootMark:        trafficData.GetBootMark(),
			UpdateMark:      trafficData.GetUpdateMark(),
			PxRatio:         float64(trafficData.GetScreenDensity()),
			Dpi:             int(request.Device.DPI),
			Ppi:             int(request.Device.PPI),
			Brand:           trafficData.GetBrand(),
			Orientation:     int(trafficData.GetScreenOrientation()),
			DeviceStartSec:  trafficData.GetDeviceStartupTime(),
			SystemUpdateSec: trafficData.GetDeviceUpgradeTime(),
			HardwareMachine: request.Device.HardwareMachineCode,
			Paid:            request.Device.Paid,
			Aaid:            trafficData.GetAaid(),
			Mac:             trafficData.GetMac(),
			MacMd5:          trafficData.GetMd5Mac(),
			HmsVer:          request.Device.VercodeHms,
			AG:              request.Device.VercodeAg,
			Maker:           trafficData.GetBrand(),
			OpenUdid:        trafficData.GetOpenUdid(),
		},
		App: kaweiapu_dsp_entity.RequestApp{
			BundleId: trafficData.GetAppBundle(),
			AppName:  trafficData.GetAppName(),
			Version:  trafficData.GetAppVersion(),
		},
		Site: k.buildSite(request, candidate, trafficData, dspSlot),
		User: kaweiapu_dsp_entity.RequestUser{
			Gender: int(request.UserGender),
			Age:    int(request.UserAge),
			AppList: &kaweiapu_dsp_entity.AppList{
				Content: strings.Join(request.App.InstalledApp, ","),
			},
		},
	}

	if len(dspSlot.PkgName) > 0 {
		bidRequest.App.BundleId = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidRequest.App.AppName = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidRequest.App.Version = dspSlot.AppVersion
	}

	if request.UseHttps {
		bidRequest.Imp.Secure = 1
	}

	if request.Device.IsIp6 {
		bidRequest.Device.Ipv6 = trafficData.GetRequestIp()
	}
	if len(trafficData.GetIdfa()) > 0 {
		bidRequest.Device.Lmt = 3
	}
	if len(bidRequest.Device.Caid) == 0 {
		for _, item := range trafficData.GetCaids() {
			if item == trafficData.GetCaid() {
				continue
			}
			bidRequest.Device.Caid = device_utils.GetCaidRaw(item)
			bidRequest.Device.CaidVersion = device_utils.GetCaidVersion(item)
			break
		}
	}

	if request.IsDebug {
		marshal, _ := sonic.Marshal(bidRequest)
		k.log.WithField("request", string(marshal)).Info("debug request")
	}

	httpRequest, _, err := k.BuildSonicJsonHttpRequest(bidRequest)
	if err != nil {
		k.log.WithError(err).Error("BuildSonicJsonHttpRequest error")
		return nil, err
	}
	httpRequest.Header.Set("x-ld-adserving-enable-trace", "true")

	k.SampleDspBroadcastSonicJsonRequest(k.GetDspId(), dspSlot.Id, candidate, bidRequest)
	return httpRequest, nil
}

func mappingConnType(connectionType entity.ConnectionType) kaweiapu_dsp_entity.ConnType {
	switch connectionType {
	case entity.ConnectionType3G:
		return kaweiapu_dsp_entity.ConnType3G
	case entity.ConnectionType4G:
		return kaweiapu_dsp_entity.ConnType4G
	case entity.ConnectionType5G:
		return kaweiapu_dsp_entity.ConnType5G
	case entity.ConnectionTypeWifi:
		return kaweiapu_dsp_entity.ConnTypeWIFI
	case entity.ConnectionType2G, entity.ConnectionTypeNetEthernet, entity.ConnectionTypeCellular:
		return kaweiapu_dsp_entity.ConnTypeOTHER
	default:
		return kaweiapu_dsp_entity.ConnTypeUNKNOWN
	}
}

func (k *KaweiapuDspBroker) buildSite(request *ad_service.AdRequest, candidate *ad_service.AdCandidate,
	trafficData ad_service_entity.TrafficData, dspSlot *KaweiapuDspSlotInfo) kaweiapu_dsp_entity.RequestSite {
	site := kaweiapu_dsp_entity.RequestSite{
		Id:            candidate.GetDspSlotKey(),
		AdType:        mappingAdType(trafficData.GetSlotType()),
		CreativeSpecs: nil,
		SupportInteractionType: []kaweiapu_dsp_entity.InteractionType{
			kaweiapu_dsp_entity.InteractionTypeH5,
			kaweiapu_dsp_entity.InteractionTypeDOWNLOAD,
			kaweiapu_dsp_entity.InteractionTypeDEEPLINK,
			kaweiapu_dsp_entity.InteractionTypeWECHAT_MINI_PROG,
		},
		Support302: true,
	}

	width, height := dspSlot.Width, dspSlot.Height
	if width == 0 || height == 0 {
		width, height = int(trafficData.GetSlotWidth()), int(trafficData.GetSlotHeight())
	}
	if (width == 0 || height == 0) && len(request.SlotSize) > 0 {
		width, height = int(request.SlotSize[0].Width), int(request.SlotSize[0].Height)
	}

	for _, key := range request.GetCreativeTemplateKeyList() {
		templateKey := creative_entity.CreativeTemplateKey(key)

		for range templateKey.Image().GetRequiredCount() {
			site.CreativeSpecs = append(site.CreativeSpecs, kaweiapu_dsp_entity.CreativeSpec{
				SupportCreativeType: kaweiapu_dsp_entity.RequestCreativeTypeIMAGE,
				SupportSizes: kaweiapu_dsp_entity.Size{
					W: width,
					H: height,
				},
			})
		}

		if templateKey.Video().GetRequiredCount() > 0 {
			site.CreativeSpecs = append(site.CreativeSpecs, kaweiapu_dsp_entity.CreativeSpec{
				SupportCreativeType: kaweiapu_dsp_entity.RequestCreativeTypeVIDEO,
				SupportSizes: kaweiapu_dsp_entity.Size{
					W: width,
					H: height,
				},
			})
		}

		break
	}

	return site
}

func (k *KaweiapuDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList,
	response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if response.StatusCode != 200 {
		k.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			strconv.Itoa(response.StatusCode), "")
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		k.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			err_code.ErrBrokerResponse.CodeStr(), "read body err")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	bidResponse := &kaweiapu_dsp_entity.BidResponse{}
	payload, err := k.ParseSonicJsonHttpResponse(response, data, bidResponse)
	if err != nil {
		k.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			err_code.ErrBrokerResponse.CodeStr(), "ParseSonicJsonHttpResponse error")
		k.log.WithError(err).Error("ParseSonicJsonHttpResponse error")
		return nil, err
	}

	if request.IsDebug {
		k.log.Infof("ParseResponse body: %s", string(payload))
	}

	k.SampleDspBroadcastResponse(k.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, payload)

	if bidResponse.Imp == nil || bidResponse.Ad == nil {
		k.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			err_code.ErrBroadcastNoBidding.CodeStr(), "imp or ad is empty")
		return nil, err_code.ErrBroadcastNoBidding
	}

	k.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
		"0", "")

	dspAdCandidate, err := k.buildDspAdCandidate(bidResponse, broadcastCandidate)
	if err != nil {
		k.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(),
			err_code.ErrBroadcastResponseCandidateError.CodeStr(), "buildDspAdCandidate error")
		k.log.WithError(err).Error("buildDspAdCandidate error")
		return nil, err_code.ErrBroadcastResponseCandidateError.Wrap(err)
	}

	return ad_service.DspAdCandidateList{dspAdCandidate}, nil
}

func (k *KaweiapuDspBroker) buildDspAdCandidate(bid *kaweiapu_dsp_entity.BidResponse,
	broadcastCandidate *ad_service.AdCandidate) (*ad_service.DspAdCandidate, error) {
	ad := &entity.Ad{
		DspId:         k.GetDspId(),
		DspSlotId:     broadcastCandidate.GetDspSlotId(),
		DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
		AdMonitorInfo: k.buildMonitor(bid.Ad),
	}
	if app := bid.Ad.Interaction.App; app != nil {
		ad.AppInfo = &entity.AppInfo{
			PackageName: app.BundleId,
			AppName:     app.AppName,
			AppVersion:  app.Version,
			Privacy:     app.PrivacyLink,
			Permission:  app.PermissionLink,
			Develop:     app.PublisherName,
		}
	}
	if len(bid.Ad.Interaction.OriginId) > 0 {
		if ad.AppInfo == nil {
			ad.AppInfo = &entity.AppInfo{}
		}
		ad.AppInfo.WechatExt = &entity.WechatExt{
			ProgramId:   bid.Ad.Interaction.OriginId,
			ProgramPath: bid.Ad.Interaction.DeeplinkUrl,
		}
	}

	creative := k.buildCreative(bid.Ad)
	if creative == nil {
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	adCandidate := ad_service.NewDspAdCandidateWithPool(ad)
	adCandidate.SetCreative(creative)
	adCandidate.SetBidPrice(uint32(bid.Imp.Price))
	adCandidate.SetBidType(entity.BidTypeCpm)
	adCandidate.SetDspProtocol(k.GetDspProtocol())
	adCandidate.SetDspAdID(bid.Ad.AdId)

	return adCandidate, nil
}

func (k *KaweiapuDspBroker) buildMonitor(ad *kaweiapu_dsp_entity.Ad) *entity.AdMonitorInfo {
	monitor := &entity.AdMonitorInfo{}

	monitor.LandingUrl = ad.Interaction.TargetUrl

	if ad.Interaction.Type == kaweiapu_dsp_entity.InteractionTypeDOWNLOAD {
		monitor.DownloadUrl = ad.Interaction.TargetUrl
		monitor.LandingAction = entity.LandingTypeDownload
	} else if ad.Interaction.Type == kaweiapu_dsp_entity.InteractionTypeDEEPLINK {
		monitor.DeepLinkUrl = ad.Interaction.DeeplinkUrl
		monitor.LandingAction = entity.LandingTypeDeepLink
	} else if ad.Interaction.Type == kaweiapu_dsp_entity.InteractionTypeWECHAT_MINI_PROG {
		monitor.DeepLinkUrl = ad.Interaction.DeeplinkUrl
		monitor.LandingAction = entity.LandingTypeWeChatProgram
	} else if ad.Interaction.Type == kaweiapu_dsp_entity.InteractionTypeH5 {
		monitor.LandingAction = entity.LandingTypeInWebView
	}

	if len(ad.Native.WinnoticeTracker) > 0 {
		monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, ad.Native.WinnoticeTracker)
	}
	for _, tracker := range ad.Native.ImpressionTrackers {
		if len(tracker) > 0 {
			monitor.ImpressionMonitorList = append(monitor.ImpressionMonitorList, tracker)
		}
	}
	for _, tracker := range ad.Native.ClickTrackers {
		if len(tracker) > 0 {
			monitor.ClickMonitorList = append(monitor.ClickMonitorList, tracker)
		}
	}
	for _, tracker := range ad.Native.ConversionTrackers {
		for _, url := range tracker.TrackerUrls {
			if len(url) > 0 {
				switch tracker.EventType {
				case kaweiapu_dsp_entity.TrackerTypeVIDEO_START:
					monitor.VideoStartUrlList = append(monitor.VideoStartUrlList, url)
				case kaweiapu_dsp_entity.TrackerTypeVIDEO_COMPLETE:
					monitor.VideoCloseUrlList = append(monitor.VideoCloseUrlList, url)
				case kaweiapu_dsp_entity.TrackerTypeDOWNLOAD_START:
					monitor.AppDownloadStartedMonitorList = append(monitor.AppDownloadStartedMonitorList, url)
				case kaweiapu_dsp_entity.TrackerTypeDOWNLOAD_FINISH:
					monitor.AppDownloadFinishedMonitorList = append(monitor.AppDownloadFinishedMonitorList, url)
				case kaweiapu_dsp_entity.TrackerTypeAPP_INSTALLED:
					monitor.AppInstalledFinishMonitorList = append(monitor.AppInstalledFinishMonitorList, url)
				case kaweiapu_dsp_entity.TrackerTypeDEEPLINK_SUCCESS:
					monitor.DeepLinkMonitorList = append(monitor.DeepLinkMonitorList, url)
				default:
					continue
				}
			}
		}
	}

	// 宏替换
	if len(monitor.ImpressionMonitorList) > 0 {
		monitor.ImpressionMonitorList = k.macroInfo.MacroReplaceList(monitor.ImpressionMonitorList)
	}
	if len(monitor.ClickMonitorList) > 0 {
		monitor.ClickMonitorList = k.macroInfo.MacroReplaceList(monitor.ClickMonitorList)
	}
	if len(monitor.DeepLinkMonitorList) > 0 {
		monitor.DeepLinkMonitorList = k.macroInfo.MacroReplaceList(monitor.DeepLinkMonitorList)
	}

	return monitor
}

func (k *KaweiapuDspBroker) buildCreative(ad *kaweiapu_dsp_entity.Ad) *entity.Creative {
	creative := &entity.Creative{
		CreativeKey: ad.AdId,
	}

	title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: "点击查看详情"}
	creative.MaterialList = append(creative.MaterialList, title)
	desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: "点击查看详情"}
	creative.MaterialList = append(creative.MaterialList, desc)
	if ad.Native.Text != nil {
		if len(ad.Native.Text.Title) > 0 {
			title.Data = ad.Native.Text.Title
		}
		if len(ad.Native.Text.Desc) > 0 {
			desc.Data = ad.Native.Text.Desc
		}
	}

	if len(ad.Native.Icon.Url) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          ad.Native.Icon.Url,
			Height:       int32(ad.Native.Icon.Height),
			Width:        int32(ad.Native.Icon.Width),
		})
	}

	if ad.Native.Image != nil && len(ad.Native.Image.Url) > 0 {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          ad.Native.Image.Url,
			Height:       int32(ad.Native.Image.H),
			Width:        int32(ad.Native.Image.W),
		})
	}

	if ad.Native.Video != nil {
		if len(ad.Native.Video.Url) > 0 {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeVideo,
				Url:          ad.Native.Video.Url,
				Height:       int32(ad.Native.Video.H),
				Width:        int32(ad.Native.Video.W),
				Duration:     float64(ad.Native.Video.Duration),
			})
		}
		if len(ad.Native.Video.CoverUrl) > 0 {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeCoverImage,
				Url:          ad.Native.Video.CoverUrl,
				Height:       int32(ad.Native.Video.H),
				Width:        int32(ad.Native.Video.W),
			})
		}
	}

	return creative
}

func (k *KaweiapuDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return k.dspSlotRegister
}

func mappingDeviceType(deviceType entity.DeviceType) kaweiapu_dsp_entity.DeviceType {
	switch deviceType {
	case entity.DeviceTypePad:
		return kaweiapu_dsp_entity.DeviceTypePAD
	default:
		return kaweiapu_dsp_entity.DeviceTypePHONE
	}
}

func mappingOs(osType entity.OsType) kaweiapu_dsp_entity.OsType {
	switch osType {
	case entity.OsTypeIOS:
		return kaweiapu_dsp_entity.OsTypeIOS
	default:
		return kaweiapu_dsp_entity.OsTypeANDROID
	}
}

func mappingCarrier(operatorType entity.OperatorType) kaweiapu_dsp_entity.Carrier {
	switch operatorType {
	case entity.OperatorTypeChinaMobile:
		return kaweiapu_dsp_entity.CarrierMOBILE
	case entity.OperatorTypeChinaUnicom:
		return kaweiapu_dsp_entity.CarrierUNICOM
	case entity.OperatorTypeChinaTelecom:
		return kaweiapu_dsp_entity.CarrierTELECOM
	default:
		return kaweiapu_dsp_entity.CarrierUNKNOWN
	}
}

func mappingAdType(slotType entity.SlotType) int {
	switch slotType {
	case entity.SlotTypeOpening, entity.SlotTypeVideoOpening:
		return 1
	case entity.SlotTypeFeeds:
		return 2
	case entity.SlotTypeBanner:
		return 3
	case entity.SlotTypeRewardVideo:
		return 4
	case entity.SlotTypePopup:
		return 5
	default:
		return 0
	}
}
