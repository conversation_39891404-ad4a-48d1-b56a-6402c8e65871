package b612_dsp_broker

import (
	"bytes"
	"github.com/bytedance/sonic"
	"github.com/google/go-querystring/query"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/b612_dsp_broker/b612_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"strings"
	"fmt"
)

type B612DspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *B612DspSlotRegister

	MacroWinPrice string
}

func NewB612DspBroker(dspId utils.ID) *B612DspBroker {
	return &B612DspBroker{
		slotRegister:  NewB612DspSlotRegister(dspId),
		MacroWinPrice: "__PRICE__",
	}
}

func (impl *B612DspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *B612DspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("B612DspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		zap.L().Error("B612DspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))

		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("B612DspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidFloor := candidate.GetBidFloor()
	bRequest := &b612_broker_entity.BidRequest{
		AdID:         slotId,
		BidFloor:     bidFloor.Price,
		Version:      trafficData.GetAppVersion(),
		Client:       impl.mappingOsType(trafficData.GetOsType()),
		Channel:      impl.mappingChannelName(trafficData.GetOsType()),
		IMEI:         trafficData.GetImei(),
		ImeiMD5:      trafficData.GetMd5Imei(),
		ClientPostIP: trafficData.GetRequestIp(),
		IFA:          trafficData.GetIdfa(),
		UUID:         "",
		OAID:         trafficData.GetOaid(),
		AndroidID:    trafficData.GetAndroidId(),
		UpdateMark:   trafficData.GetUpdateMark(),
		BootMark:     trafficData.GetBootMark(),
		UserAgent:    trafficData.GetUserAgent(),
		Brand:        trafficData.GetBrand(),
		Vendor:       trafficData.GetBrand(),
		Device:       trafficData.GetModel(),
		Lon:          trafficData.GetGeoLatitude(),
		Lat:          trafficData.GetGeoLongitude(),
		Reach:        impl.mappingConnectionType(trafficData.GetConnectionType()),
		SDK:          trafficData.GetOsVersion(),
		Mac:          trafficData.GetMac(),
		Resolution:   "",
		CNS:          "",
		OsTimeZone:   "",
		SysCmpTime:   trafficData.GetDeviceStartupTime(),
		SysDiskSize:  request.Device.SystemTotalDisk,
		SysMemory:    request.Device.SystemTotalMem,
		OsUpdateTime: trafficData.GetDeviceUpgradeTime(),
		BirthTime:    trafficData.GetDeviceInitTime(),
		DPI:          trafficData.GetScreenDensity(),
		PPI:          request.Device.PPI,
		Pyysimembyte: request.Device.SystemTotalMem,
		Imsi:         "",
		Bundle:       trafficData.GetAppBundle(),
		Appname:      trafficData.GetAppName(),
	}

	if trafficData.GetOsType() == entity.OsTypeIOS {
		bRequest.Resolution = type_convert.GetAssertString(trafficData.GetScreenWidth()) + "*" + type_convert.GetAssertString(trafficData.GetScreenHeight())
	} else {
		bRequest.Resolution = type_convert.GetAssertString(trafficData.GetScreenHeight()) + "*" + type_convert.GetAssertString(trafficData.GetScreenWidth())
	}

	if len(dspSlot.AppName) > 0 {
		bRequest.Appname = dspSlot.AppName
	}

	if len(dspSlot.PkgName) > 0 {
		bRequest.Bundle = dspSlot.PkgName
	}

	if len(dspSlot.AppVersion) > 0 {
		bRequest.Version = dspSlot.AppVersion
	}

	querys, _ := query.Values(bRequest)
	rawQuery := querys.Encode()

	if request.IsDebug {
		reqbody, _ := sonic.Marshal(bRequest)
		zap.L().Info("B612DspBroker.EncodeRequest end, B612DspBroker json Body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(reqbody))))))
		zap.L().Info("B612DspBroker.EncodeRequest end, B612DspBroker rawQuery", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", rawQuery)))))
	}

	req, err := http.NewRequest(http.MethodPost, impl.GetBidUrl(), bytes.NewBuffer([]byte(rawQuery)))
	if err != nil {
		zap.L().Error("B612DspBroker http NewRequest err", zap.Error(err))
		return nil, err
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	impl.SampleDspBroadcastRequest(impl.GetDspId(), dspSlot.Id, candidate, []byte(rawQuery))

	return req, nil

}

func (impl *B612DspBroker) mappingOsType(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "3"
	case entity.OsTypeAndroid:
		return "4"
	default:
		return "4"
	}
}

func (impl *B612DspBroker) mappingChannelName(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "App Store"
	case entity.OsTypeAndroid:
		return ""
	default:
		return ""
	}
}

func (impl *B612DspBroker) mappingConnectionType(connectionType entity.ConnectionType) string {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return "1"
	case entity.ConnectionTypeWifi:
		return "1"
	case entity.ConnectionTypeCellular:
		return "0"
	case entity.ConnectionType2G:
		return "0"
	case entity.ConnectionType3G:
		return "0"
	case entity.ConnectionType4G:
		return "0"
	case entity.ConnectionType5G:
		return "0"
	default:
		return "0"
	}
}

func (impl *B612DspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("B612DspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &b612_broker_entity.BidResponse{}
	resBody, err := impl.ParseSonicJsonHttpResponse(resp, data, response)
	if err != nil {
		zap.L().Error("B612DspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.GetDspId(), broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		zap.L().Info("B612DspBroker raw reponse1", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}

	if response.State != "success" || response.Result == nil {
		return nil, err_code.ErrBroadcastNoBidding
	}

	candidateAd := &entity.Ad{
		DspId:      impl.GetDspId(),
		DspSlotId:  broadcastCandidate.GetDspSlotId(),
		DspSlotKey: broadcastCandidate.GetDspSlotKey(),
	}

	candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, response.Result)

	candidateCreative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        response.Result.ID,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}
	if len(response.Result.Image) > 0 {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          response.Result.Image,
		}
		candidateCreative.MaterialList = append(candidateCreative.MaterialList, materialImg)
	}

	if len(response.Result.VideoURL) > 0 {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          response.Result.VideoURL,
		}
		candidateCreative.MaterialList = append(candidateCreative.MaterialList, materialImg)
	}

	if len(response.Result.Title) > 0 {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         response.Result.Title,
		}
		candidateCreative.MaterialList = append(candidateCreative.MaterialList, materialImg)
	}

	if len(response.Result.Describe) > 0 {
		materialImg := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         response.Result.Describe,
		}
		candidateCreative.MaterialList = append(candidateCreative.MaterialList, materialImg)
	}

	candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
	candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
	candidate.SetBidPrice(uint32(response.Result.Price))
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetCreative(candidateCreative)
	candidate.SetDspProtocol(impl.GetDspProtocol())

	result := make(ad_service.DspAdCandidateList, 0)
	result = append(result, candidate)
	return result, nil

}

func (impl *B612DspBroker) chargePriceEncoder(chargePrice uint32) string {
	//明文替换
	result, err := impl.PriceManager.PlaintextEncodeMulti(uint64(chargePrice), 100)
	//result, err := impl.PriceManager.GetDspCoder(impl.GetDspProtocol()).EncodeWithKey(uint64(chargePrice), impl.GetIKey(), impl.GetEKey())
	if err != nil {
		return ""
	}

	return result
}

func (impl *B612DspBroker) ParseTrackingData(request *ad_service.AdRequest, bid *b612_broker_entity.AdResult) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		LandingUrl:                     bid.ClickURL,
		H5LandingUrl:                   bid.ClickURL,
		DeepLinkUrl:                    bid.DeeplinkURL,
		ImpressionMonitorList:          make([]string, 0),
		ClickMonitorList:               bid.ClickTrackers,
		LandingAction:                  entity.LandingTypeInWebView,
		DeepLinkMonitorList:            bid.DeeplinkSuccTrackers,
		DeepLinkFailedMonitorList:      bid.DeeplinkFailTrackers,
		AppDownloadStartedMonitorList:  bid.DownloadStartTrackers,
		AppDownloadFinishedMonitorList: bid.DownloadEndTrackers,
		AppInstallStartMonitorList:     bid.InstallStartTrackers,
		AppInstalledFinishMonitorList:  bid.InstallCompleteTrackers,
		VideoStartUrlList:              bid.VideoStart,
		VideoCloseUrlList:              bid.VideoComplete,
	}

	if len(bid.WnURL) != 0 {
		if strings.Contains(bid.WnURL, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(bid.WnURL, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.WnURL)
		}
	}

	for _, impUrl := range bid.NURL {
		if strings.Contains(impUrl, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impUrl, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impUrl)
		}
	}

	for _, impUrl := range bid.ImpTrackers {
		if strings.Contains(impUrl, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(impUrl, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impUrl)
		}
	}

	if bid.IsDeeplink {
		tracking.LandingAction = entity.LandingTypeDeepLink
	}

	if bid.IsDownload {
		tracking.LandingAction = entity.LandingTypeDownload
	}

	return tracking
}
