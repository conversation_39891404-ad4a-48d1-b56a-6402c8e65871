package b612_dsp_broker

import (
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"go.uber.org/zap"
)

type B612SlotSlotInfo struct {
	*entity.DspSlotInfo
	AppVersion string `json:"app_version"`
	PkgName    string `json:"pkg_name"`
	AppName    string `json:"app_name"`
}

func (info *B612SlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	return nil
}

type B612DspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*B612SlotSlotInfo
}

func NewB612DspSlotRegister(dspId utils.ID) *B612DspSlotRegister {
	return &B612DspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*B612SlotSlotInfo),
	}
}

func (r *B612DspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *B612DspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*B612SlotSlotInfo)
	for _, slot := range list {
		qihangSlot := &B612SlotSlotInfo{}
		if err := qihangSlot.Init(slot); err != nil {
			zap.L().Error("[B612DspSlotRegister] init slot failed", zap.Error(err), zap.Int64("slotId", int64(slot.Id)))
			continue
		}

		slotMap[slot.Id] = qihangSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *B612DspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *B612DspSlotRegister) GetSlotInfo(slotId utils.ID) *B612SlotSlotInfo {
	return r.dspSlotMap[slotId]
}
