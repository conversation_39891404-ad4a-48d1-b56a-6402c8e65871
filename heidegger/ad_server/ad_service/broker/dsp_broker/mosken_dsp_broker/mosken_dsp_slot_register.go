package mosken_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"fmt"
)

type MoskenSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`
}

func (info *MoskenSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	info.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	info.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")

	return nil
}

type MoskenSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*MoskenSlotInfo
}

func NewMoskenSlotRegister(dspId utils.ID) *MoskenSlotRegister {
	return &MoskenSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*MoskenSlotInfo),
	}
}

func (r *MoskenSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *MoskenSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*MoskenSlotInfo)
	for _, slot := range list {
		dspSlot := &MoskenSlotInfo{}
		if err := dspSlot.Init(slot); err != nil {
			zap.L().Error("[MoskenSlotRegister] init slot failed", zap.Error(err), zap.String("slot", fmt.Sprintf("%v", slot.Id)))
			continue
		}

		slotMap[slot.Id] = dspSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *MoskenSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *MoskenSlotRegister) GetSlotInfo(slotId utils.ID) *MoskenSlotInfo {
	return r.dspSlotMap[slotId]
}
