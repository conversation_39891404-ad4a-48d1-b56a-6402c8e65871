package mosken_dsp_broker

import (
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/mosken_dsp_broker/mosken_dsp_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type MoskenDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *MoskenSlotRegister
	log          *zap.Logger
	macroInfo    macro_builder.MonitorMacroInfo
}

func NewMoskenDspBroker(dspId utils.ID) *MoskenDspBroker {
	return &MoskenDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		slotRegister: NewMoskenSlotRegister(dspId),
		log:          zap.L().With(zap.String("broker", "MoskenDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__WIN_PRICE__",
			MacroClickDownX: "__DOWN_X__",
			MacroClickDownY: "__DOWN_Y__",
			MacroClickUpX:   "__UP_X__",
			MacroClickUpY:   "__UP_Y__",
			MacroHWSld:      "__SLD__",
		},
	}
}

func (a *MoskenDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		zap.L().Error("[MoskenDspBroker] candidateList too many", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))
		return nil, err_code.ErrTooManyCandidateForDsp
	}
	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()
	dspSlot := a.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}
	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	// 广告位宽高
	slotWidth, slotHeight := uint32(dspSlot.Width), uint32(dspSlot.Height)
	if slotWidth == 0 || slotHeight == 0 {
		if len(request.SlotSize) > 0 {
			slotWidth, slotHeight = uint32(request.SlotSize[0].Width), uint32(request.SlotSize[0].Height)
		} else {
			slotWidth, slotHeight = trafficData.GetSlotWidth(), trafficData.GetSlotHeight()
		}
	}
	imp := &mosken_dsp_broker_entity.Imps{
		ID:       "1",
		Tagid:    slotId,
		W:        int32(slotWidth),
		H:        int32(slotHeight),
		Bidfloor: candidate.GetBidFloor().Price,
		PosType:  mappingSlotType(trafficData.GetSlotType()),
	}
	if dspSlot.Width != 0 {
		imp.W = int32(dspSlot.Width)
	}
	if dspSlot.Height != 0 {
		imp.H = int32(dspSlot.Height)
	}

	caid := trafficData.GetCaid()
	if len(request.Device.Caids) > 0 {
		for _, caidItem := range request.Device.Caids {
			if caidItem == trafficData.GetCaid() {
				continue
			}
			if len(caid) > 0 {
				caid = "," + caidItem
			} else {
				caid = caidItem
			}
		}
	}

	bidRequest := &mosken_dsp_broker_entity.BidRequest{
		ID:     request.GetRequestId(),
		APIVer: "2.0",
		Imps:   []*mosken_dsp_broker_entity.Imps{imp},
		Device: &mosken_dsp_broker_entity.Device{
			Ua:           trafficData.GetUserAgent(),
			Devicetype:   mappingDeviceType(trafficData.GetDeviceType()),
			Make:         request.Device.Brand,
			Model:        trafficData.GetModel(),
			Network:      mappingConnectionType(trafficData.GetConnectionType()),
			Carrier:      mappingCarrier(trafficData.GetOperatorType()),
			Os:           mappingDeviceOs(trafficData.GetOsType()),
			Osv:          trafficData.GetOsVersion(),
			H:            trafficData.GetScreenHeight(),
			W:            trafficData.GetScreenWidth(),
			Ppi:          request.Device.PPI,
			Dpi:          request.Device.DPI,
			Orientation:  mappingOrientation(trafficData.GetScreenOrientation()),
			Idfa:         trafficData.GetIdfa(),
			IdfaMd5:      trafficData.GetMd5Idfa(),
			Idfv:         trafficData.GetIdfv(),
			Imei:         trafficData.GetImei(),
			ImeiMd5:      trafficData.GetMd5Imei(),
			Oaid:         trafficData.GetOaid(),
			OaidMd5:      trafficData.GetMd5Oaid(),
			AndroidID:    trafficData.GetAndroidId(),
			AndroidIDMd5: trafficData.GetMd5AndroidId(),
			Mac:          trafficData.GetMac(),
			MacMd5:       trafficData.GetMd5Mac(),
			FormatMacMd5: mappingFormatMacMd5(trafficData.GetMac()),
			Aaid:         trafficData.GetAaid(),
			Paid:         request.Device.Paid,
			BootMark:     trafficData.GetBootMark(),
			UpdateMark:   trafficData.GetUpdateMark(),
			HmsVersion:   request.Device.VercodeHms,
			AgVersion:    request.Device.VercodeAg,
			AppList:      request.App.InstalledApp,
			Geo: &mosken_dsp_broker_entity.Geo{
				Lat:     trafficData.GetGeoLatitude(),
				Lon:     trafficData.GetGeoLongitude(),
				GpsType: mappingCoordinateType(request.Device.GpsType),
			},
			Caid: caid,
			CaidExt: &mosken_dsp_broker_entity.CaidExt{
				BootTime:   trafficData.GetDeviceStartupTime(),
				UpdateTime: trafficData.GetDeviceUpgradeTime(),
				BirthTime:  trafficData.GetDeviceInitTime(),
				Country:    "CN",
				Language:   "zh-CN",
				PhoneName:  request.Device.DeviceNameMd5,
				DiskSize:   strconv.FormatInt(request.Device.SystemTotalDisk, 10),
				MemorySize: strconv.FormatInt(request.Device.SystemTotalMem, 10),
				ModelCode:  request.Device.HardwareMachineCode,
				Timezone:   strconv.FormatInt(int64(request.Device.TimeZone), 10),
			},
		},
		App: &mosken_dsp_broker_entity.App{
			Name:    trafficData.GetAppName(),
			Bundle:  trafficData.GetAppBundle(),
			Version: trafficData.GetAppVersion(),
		},
		User: &mosken_dsp_broker_entity.User{
			Gender: mappingGender(request.UserGender),
		},
	}
	if len(trafficData.GetWebviewUA()) > 0 {
		bidRequest.Device.Ua = trafficData.GetWebviewUA()
	}
	if request.UseHttps {
		bidRequest.Secure = 1
	}
	if request.Device.IsIp6 {
		bidRequest.Device.Ipv6 = trafficData.GetRequestIp()
	} else {
		bidRequest.Device.IP = trafficData.GetRequestIp()
	}
	if len(dspSlot.PkgName) > 0 {
		bidRequest.App.Bundle = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidRequest.App.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidRequest.App.Version = dspSlot.AppVersion
	}
	// Mosken app name & version & bundle 不能为空
	if len(bidRequest.App.Name) == 0 {
		bidRequest.App.Name = bidRequest.App.Bundle
	}

	if request.IsDebug {
		payload, _ := sonic.Marshal(bidRequest)
		a.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}

	httpReq, _, err := a.BuildSonicJsonHttpRequest(bidRequest)
	if err != nil {
		a.log.WithError(err).Error("BuildSonicJsonHttpRequest error")
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	a.SampleDspBroadcastSonicJsonRequest(a.DspId, trafficData.GetDspSlotId(), candidate, bidRequest)
	return httpReq, nil
}

func (a *MoskenDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if response.StatusCode != 200 {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(response.StatusCode), type_convert.GetAssertString(response.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse
	}

	resp := new(mosken_dsp_broker_entity.BidResponse)
	payload, err := a.ParseSonicJsonHttpResponse(response, data, resp)
	if err != nil {
		a.log.WithError(err).WithField("response", response).Error("ParseSonicJsonHttpResponse error")
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		return nil, err_code.ErrBrokerParseError.Wrap(err)
	}

	a.SampleDspBroadcastResponse(a.DspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, payload)
	if request.IsDebug {
		a.log.WithField("resp", string(payload)).Info("ParseResponse debug")
	}

	a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.Code), getCodeMsg(resp.Code))
	if resp.Code != 0 || len(resp.Ads) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0, 1)
	ad := &entity.Ad{
		DspId:      a.DspId,
		DspSlotId:  broadcastCandidate.GetDspSlotId(),
		DspSlotKey: broadcastCandidate.GetDspSlotKey(),
	}

	adMonitorInfo, creative := a.parseCallbacksAndCreative(resp.Ads[0], request.Device.OsType)
	ad.AdMonitorInfo = adMonitorInfo
	if creative == nil {
		return nil, err_code.ErrCreativeNotFound
	}
	candidate := ad_service.NewDspAdCandidateWithPool(ad)
	candidate.SetAdCandidateChargePriceEncoder(a.chargePriceEncoder)
	candidate.SetBidPrice(uint32(resp.Ads[0].Price))
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetCreative(creative)
	candidate.SetDspProtocol(a.DspProtocol)
	result = append(result, candidate)

	return result, nil
}

func (a *MoskenDspBroker) chargePriceEncoder(chargePrice uint32) string {
	if chargePrice > 0 {
		result, err := a.PriceManager.GetDspCoder(a.DspProtocol).EncodeWithKey(uint64(chargePrice), a.GetIKey(), a.GetEKey())
		if err != nil {
			a.log.WithError(err).WithField("price", chargePrice).Errorf("chargePriceEncoder error")
			return ""
		}
		return result
	} else {
		return ""
	}
}

func (a *MoskenDspBroker) parseCallbacksAndCreative(data *mosken_dsp_broker_entity.Ads, ostype entity.OsType) (*entity.AdMonitorInfo, *entity.Creative) {
	appinfo := data.AppInfo
	image := data.Image
	logo := data.Logo
	images := data.Images
	video := data.Video
	events := data.Em

	video25 := 0
	video50 := 0
	video75 := 0
	video100 := 0

	info := &entity.AdMonitorInfo{
		LandingUrl:       data.Lp,
		DeepLinkUrl:      data.Deeplink,
		ClickMonitorList: data.Cm,
		//DownloadUrl:      data.PackageURL,
		LandingAction: mappingLandingType(data.InteractType),
	}

	if len(data.Nurl) > 0 {
		info.ImpressionMonitorList = append(info.ImpressionMonitorList, data.Nurl)
	}
	info.ImpressionMonitorList = append(info.ImpressionMonitorList, data.Pm...)

	if appinfo != nil {
		info.DownloadUrl = appinfo.URL
		info.AppInfo = entity.AppInfo{
			PackageName: appinfo.PackageName,
			AppName:     appinfo.AppName,
			AppVersion:  appinfo.AppVersion,
			Develop:     appinfo.DeveloperName,
			AppDescURL:  appinfo.AppIntroURL,
			AppDesc:     appinfo.AppIntro,
			PackageSize: int(appinfo.FileSize),
			Permission:  appinfo.PermissionHTML,
			Privacy:     appinfo.PrivacyPolicy,
			Icon:        appinfo.AppIcon,
		}

		if len(appinfo.Permissions) > 0 {
			for _, perm := range appinfo.Permissions {
				info.AppInfo.PermissionDesc = append(info.AppInfo.PermissionDesc, entity.PermissionDesc{
					PermissionLab:  perm.Name,
					PermissionDesc: perm.Desc,
				})
			}
		}

		if len(data.Wxoid) > 0 {
			info.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   data.Wxoid,
				ProgramPath: data.Wxp,
			}
		}

	} else if appinfo == nil && len(data.Wxoid) > 0 {
		info.AppInfo = entity.AppInfo{
			WechatExt: &entity.WechatExt{
				ProgramId:   data.Wxoid,
				ProgramPath: data.Wxp,
			},
		}
	}

	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        data.Crid,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if logo != nil {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeLogo,
			Url:          logo.URL,
			Height:       logo.H,
			Width:        logo.W,
		})
	}

	title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: data.Title}
	if len(data.Title) == 0 {
		title.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, title)
	desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: data.Desc}
	if len(data.Desc) == 0 {
		desc.Data = "点击查看详情"
	}
	creative.MaterialList = append(creative.MaterialList, desc)

	if len(images) > 0 {
		for _, imgitem := range images {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          imgitem.URL,
				Height:       imgitem.H,
				Width:        imgitem.W,
			})
		}
	}
	if image != nil {
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          image.URL,
			Height:       image.H,
			Width:        image.W,
		})
	}

	if video != nil {
		video100 = int(video.Duration)
		creative.MaterialList = append(creative.MaterialList, &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          video.URL,
			Height:       video.H,
			Width:        video.W,
			Duration:     float64(video.Duration),
			FileSize:     video.Size,
		})

		if len(video.Cover) > 0 {
			creative.MaterialList = append(creative.MaterialList, &entity.Material{
				MaterialType: entity.MaterialTypeCoverImage,
				Url:          video.Cover,
				Height:       video.H,
				Width:        video.W,
			})
		}
	}

	if events != nil {
		if len(events.DownloadStart) > 0 {
			info.AppDownloadStartedMonitorList = append(info.AppDownloadStartedMonitorList, events.DownloadStart...)
		}
		if len(events.DownloadFinish) > 0 {
			info.AppDownloadFinishedMonitorList = append(info.AppDownloadFinishedMonitorList, events.DownloadFinish...)
		}
		if len(events.InstallStart) > 0 {
			info.AppInstallStartMonitorList = append(info.AppInstallStartMonitorList, events.InstallStart...)
		}
		if len(events.InstallFinish) > 0 {
			info.AppInstalledFinishMonitorList = append(info.AppInstalledFinishMonitorList, events.InstallFinish...)
		}
		if len(events.DeeplinkAttempt) > 0 {
			info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, events.DeeplinkAttempt...)
		}
		if len(events.AppInvokeSuccess) > 0 {
			info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, events.AppInvokeSuccess...)
		}
		if len(events.AppInvokeFailed) > 0 {
			info.DeepLinkFailedMonitorList = append(info.DeepLinkFailedMonitorList, events.AppInvokeFailed...)
		}

		//video event
		video25 = video100 / 4
		video50 = video100 / 2
		video75 = video100 * 75 / 100
		if len(events.VideoPlay) > 0 {
			for _, item := range events.VideoPlay {
				info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   item,
					Delay: 0,
				})
			}
		}
		if len(events.VideoFirstQtr) > 0 {
			for _, item := range events.VideoFirstQtr {
				info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   item,
					Delay: video25,
				})
			}
		}
		if len(events.VideoMidpoint) > 0 {
			for _, item := range events.VideoMidpoint {
				info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   item,
					Delay: video50,
				})
			}
		}
		if len(events.VideoThirdQtr) > 0 {
			for _, item := range events.VideoThirdQtr {
				info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   item,
					Delay: video75,
				})
			}
		}
		if len(events.VideoOver) > 0 {
			for _, item := range events.VideoOver {
				info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   item,
					Delay: video100,
				})
			}
		}

	}

	//宏替换
	info.ImpressionMonitorList = macro_builder.MacroReplaceList(info.ImpressionMonitorList, a.macroInfo)
	info.ClickMonitorList = macro_builder.MacroReplaceList(info.ClickMonitorList, a.macroInfo)

	return info, creative
}

func mappingOrientation(orientation entity.ScreenOrientationType) int32 {
	switch orientation {
	case entity.ScreenOrientationTypePortrait:
		return 0
	case entity.ScreenOrientationTypeLandscape:
		return 1
	default:
		return 0
	}
}

func mappingFormatMacMd5(mac string) string {
	if len(mac) > 0 {
		upper := strings.ToUpper(mac)
		return strings.ReplaceAll(upper, ":", "")
	} else {
		return ""
	}
}
func mappingDeviceOs(osType entity.OsType) int32 {
	switch osType {
	case entity.OsTypeIOS:
		return 1
	case entity.OsTypeAndroid:
		return 2
	default:
		return 2
	}
}
func mappingSlotType(t entity.SlotType) int32 {
	switch t {
	case entity.SlotTypeOpening, entity.SlotTypeVideoOpening:
		return 1
	case entity.SlotTypeVideo:
		return 1
	case entity.SlotTypeBanner:
		return 2
	case entity.SlotTypePopup:
		return 3
	case entity.SlotTypeFeeds:
		return 5
	case entity.SlotTypeRewardVideo:
		return 8
	case entity.SlotTypeVideoPause:
		return 9
	default:
		return 8
	}
}
func mappingCarrier(carrier entity.OperatorType) int32 {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 3
	case entity.OperatorTypeChinaTelecom:
		return 2
	default:
		return 0
	}
}
func mappingConnectionType(connectionType entity.ConnectionType) int32 {
	switch connectionType {
	case entity.ConnectionTypeWifi:
		return 20
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	default:
		return 20
	}
}
func mappingCoordinateType(gpsType entity.GpsType) int32 {
	switch gpsType {
	case entity.GpsTypeWSG84:
		return 1
	case entity.GpsTypeGCJ02:
		return 2
	case entity.GpsTypeBd09:
		return 3
	default:
		return 0
	}
}
func mappingGender(gender entity.UserGenderType) string {
	switch gender {
	case entity.UserGenderMan:
		return "M"
	default:
		return "F"
	}
}

func mappingDeviceType(dy entity.DeviceType) int32 {
	switch dy {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypeOtt:
		return 4
	case entity.DeviceTypePc:
		return 5
	default:
		return 1
	}
}

// 映射落地页类型
func mappingLandingType(interactType int32) entity.LandingType {
	switch interactType {
	case 2:
		return entity.LandingTypeInWebView
	case 3:
		return entity.LandingTypeDownload
	case 4:
		return entity.LandingTypeDeepLink
	case 5:
		return entity.LandingTypeWeChatProgram
	default:
		return entity.LandingTypeInWebView
	}
}
func getCodeMsg(code int32) string {
	switch code {
	case 0:
		return "广告请求成功"
	case 204:
		return "请求校验通过，但⽆⼴告应答"
	case 1000:
		return "参数api_ver异常"
	case 1001:
		return "请求数据格式异常，⽆法解析成JSON"
	case 1002:
		return "请求ID为空"

	case 1101:
		return "参数imps异常"
	case 1102:
		return "参数imp.id异常"
	case 1103:
		return "参数imp.tagid异常"
	case 1104:
		return "参数imp.w 或 imp.h异常"

	case 1200:
		return "未解析出device对应json"
	case 1201:
		return "参数device.os异常"
	case 1202:
		return "参数device.osv异常"
	case 1203:
		return "参数device.carrier异常"
	case 1204:
		return "参数device.network异常"
	case 1205:
		return "参数device.make异常"
	case 1206:
		return "参数device.model异常"
	case 1207:
		return "参数device.devicetype异常"
	case 1208:
		return "参数device.ua异常"
	case 1209:
		return "参数device.ip异常"
	case 1210:
		return "参数device.w 或 device.h异常"
	case 1211:
		return "iOS设备号异常，idfa/idfa_md5参数不符合要求"
	case 1212:
		return "安卓设备号异常，imei/imei_md5/oaid/oaid_md5参数不符合要求"
	case 1214:
		return "OTT设备号异常，mac/mac_md5/format_mac_md5参数不符合要求"
	case 1300:
		return "未解析出app对应json"
	case 1301:
		return "参数app.version异常"
	case 1302:
		return "参数app.name异常"
	case 1303:
		return "参数app.bundle异常"
	case 2001:
		return "传递的imp.tagid未在DYNAMIC平台注册"
	case 2002:
		return "传递的device.os与⼴告位在DYNAMIC平台设置的系统类型不⼀致"
	default:
		return "其他错误"
	}
}

func (a *MoskenDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return a.slotRegister
}
