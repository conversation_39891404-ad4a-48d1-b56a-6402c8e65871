package youlianghui_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type YLHSlotSlotInfo struct {
	*entity.DspSlotInfo
	Height int    `json:"height"`
	Width  int    `json:"width"`
	AppId  string `json:"app_id"`
	AppPkg string `json:"app_pkg"`
}

func (info *YLHSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	info.AppId, err = dspSlotInfo.ExtraData.GetString("app_id")
	if err != nil {
		return fmt.Errorf("get app_id from extra_data failed, err: %v", err)
	}

	info.AppPkg, err = dspSlotInfo.ExtraData.GetString("app_pkg")
	if err != nil {

	}

	pkgName, err := dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {

	}

	if len(info.AppPkg) == 0 {
		info.AppPkg = pkgName
	}

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	if err != nil {
	}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	if err != nil {
	}

	return nil
}

type YLHDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*YLHSlotSlotInfo
}

func NewYLHDspSlotRegister(dspId utils.ID) *YLHDspSlotRegister {
	return &YLHDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*YLHSlotSlotInfo),
	}
}

func (r *YLHDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *YLHDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*YLHSlotSlotInfo)
	for _, slot := range list {
		sSlot := &YLHSlotSlotInfo{}
		if err := sSlot.Init(slot); err != nil {
			zap.L().Error("[YLHDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = sSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *YLHDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *YLHDspSlotRegister) GetSlotInfo(slotId utils.ID) *YLHSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
