package youlianghui_dsp_broker

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/youlianghui_dsp_broker/youlianghui_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

var YLHSecret = "lc1Qq<w5uTxyF070S,hN}6diA=2zoyiR"

type YLHDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *YLHDspSlotRegister
	bidUrl       string
	dspId        utils.ID

	dspProtocol   string
	iKey          string
	eKey          string
	MacroWinPrice string
}

func NewYLHDspBroker(dspId utils.ID) *YLHDspBroker {
	return &YLHDspBroker{
		slotRegister:  NewYLHDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "__AUCTION_PRICE__",
	}
}

func (impl *YLHDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *YLHDspBroker) GetDspId() utils.ID {
	return impl.dspId
}

func (impl *YLHDspBroker) UpdateDspInfo(dsp *entity.Dsp) error {
	impl.bidUrl = dsp.BidUrl
	impl.iKey = dsp.Ikey
	impl.eKey = dsp.Ekey
	impl.dspProtocol = dsp.Protocol
	return nil
}

func (impl *YLHDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("YLHDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	if request.IsDebug {
		reqBody, _ := json.Marshal(request)
		zap.L().Info("YLHDspBroker.EncodeRequest Ctx req", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))

	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	now := time.Now()
	timestamp := now.UnixNano() / int64(time.Millisecond)

	sRequest := youlianghui_dsp_entity.YLHAdRequest{
		ApiVersion:      "3.19",
		SupportHttps:    1,
		SupportAppStore: 0,
		Geo:             impl.encodeGeo(request),
		TimeStamp:       timestamp,
	}

	sRequest.Pos = impl.encodePos(request, trafficData)
	if sRequest.Pos == "" {
		return nil, err_code.ErrDspSlotNotFound
	}

	sRequest.Media = impl.encodeMedia(trafficData)
	if sRequest.Pos == "" {
		return nil, err_code.ErrDspSlotInfo
	}

	sRequest.Device = impl.encodeDevice(request, trafficData)
	if sRequest.Pos == "" {
		return nil, err_code.ErrInvalidDeviceType
	}

	sRequest.Network = impl.encodeNetwork(trafficData)
	if sRequest.Pos == "" {
		return nil, err_code.ErrInvalidConnectionType
	}

	sRequest.Sign = impl.generateSign(YLHSecret, sRequest.Device, sRequest.Media, sRequest.Network, sRequest.Pos, timestamp)

	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()
	_, err := easyjson.MarshalToWriter(sRequest, buffer)
	if err != nil {
		zap.L().Error("YLHDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err_code.ErrBroadcastRequestBuildFail
	}

	body := buffer.Get()

	if len(body) <= 0 {
		return nil, err_code.ErrBroadcastRequestBuildFail
	}

	requestParam := fmt.Sprintf(
		"?api_version=%s&support_https=1&support_app_store=0&device=%s&media=%s&network=%s&pos=%s&geo=%s&time_stamp=%d&sign=%s",
		sRequest.ApiVersion, url.QueryEscape(sRequest.Device), url.QueryEscape(sRequest.Media), url.QueryEscape(sRequest.Network),
		url.QueryEscape(sRequest.Pos), url.QueryEscape(sRequest.Geo), timestamp, sRequest.Sign)
	bidUrl := impl.bidUrl + requestParam

	if request.IsDebug {
		zap.L().Info("YLHDspBroker.EncodeRequest end, YLHRequest json Body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(body))))))
		zap.L().Info("YLHDspBroker.EncodeRequest end, YLHRequest url", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", bidUrl)))))
	}

	req, err := http.NewRequest(http.MethodGet, bidUrl, nil)
	if err != nil {
		zap.L().Error("YLHDspBroker http NewRequest err", zap.Error(err))
		return nil, err
	}
	req.Header["X-Forwarded-For"] = []string{trafficData.GetRequestIp()}

	impl.SampleDspBroadcastRequest(impl.dspId, dspSlot.Id, candidate, body)

	return req, nil
}

func (impl *YLHDspBroker) generateSign(secret, device, media, network, pos string, timestamp int64) string {
	requestParam := fmt.Sprintf(
		"device=%s&media=%s&network=%s&pos=%s&time_stamp=%d",
		device, media, network, pos, timestamp,
	)

	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(requestParam))
	sign := hex.EncodeToString(h.Sum(nil))

	return sign
}

func (impl *YLHDspBroker) encodePos(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) string {
	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		zap.L().Error("[YLHDspBroker] encode pos get slot id empty")
		return ""
	}

	pos := &youlianghui_dsp_entity.YLHPos{
		ID:                  type_convert.GetAssertInt64(slotId),
		AdCount:             1,
		LastAdIds:           "",
		DeepLinkVersion:     1,
		SupportQuickApp:     true,
		SupportWechatCanvas: false,
		MaxDuration:         request.VideoMaxDuration,
		MinDuration:         request.VideoMinDuration,
	}

	posBody, err := easyjson.Marshal(pos)
	if err != nil {
		zap.L().Error("[YLHDspBroker] encode pos Marshal err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return ""
	}

	return string(posBody)
}

func (impl *YLHDspBroker) encodeMedia(trafficData ad_service_entity.TrafficData) string {
	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())

	if len(dspSlot.AppId) == 0 || len(dspSlot.AppPkg) == 0 {
		zap.L().Error("[YLHDspBroker] encode media app_id or app_pkg is empty")

		return ""
	}

	media := &youlianghui_dsp_entity.YLHMedia{
		AppID:       dspSlot.AppId,
		AppBundleID: dspSlot.AppPkg,
	}

	posBody, err := easyjson.Marshal(media)
	if err != nil {
		zap.L().Error("[YLHDspBroker] encode media Marshal err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return ""
	}

	return string(posBody)
}

func (impl *YLHDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) string {
	device := &youlianghui_dsp_entity.YLHDevice{
		OS:                 impl.mappingOs(trafficData.GetOsType()),
		OSVersion:          "unknown",
		Model:              "unknown",
		Manufacturer:       trafficData.GetBrand(),
		DeviceType:         impl.mappingDeviceType(trafficData.GetDeviceType()),
		ScreenWidth:        trafficData.GetScreenWidth(),
		ScreenHeight:       trafficData.GetScreenHeight(),
		Orientation:        impl.mappingOrientation(trafficData.GetScreenOrientation()),
		IMEI:               trafficData.GetImei(),
		IMEIMD5:            trafficData.GetMd5Imei(),
		OAID:               trafficData.GetOaid(),
		AndroidID:          trafficData.GetAndroidId(),
		AndroidIDMD5:       trafficData.GetMd5AndroidId(),
		AndroidAdID:        "",
		IDFA:               trafficData.GetIdfa(),
		IDFAMD5:            trafficData.GetMd5Idfa(),
		DeviceStartSec:     trafficData.GetBootMark(),
		DeviceNameMD5:      md5_utils.GetMd5String(trafficData.GetBrand()),
		HardwareMachine:    trafficData.GetModel(),
		PhysicalMemoryByte: "",
		HardDiskSizeByte:   "",
		SystemUpdateSec:    trafficData.GetUpdateMark(),
		HardwareModel:      trafficData.GetModel(),
		Country:            "CN",
		Language:           "zh-Hans-CN",
		TimeZone:           "28800",
		WxAPIVer:           0,
		WxInstalled:        false,
		OpenSdkVer:         "",
		Mac:                trafficData.GetMac(),
		WifiName:           "",
		SDFreeSpace:        "",
	}

	if len(trafficData.GetOsVersion()) > 0 {
		device.OSVersion = trafficData.GetOsVersion()
	}

	if len(trafficData.GetModel()) > 0 {
		device.Model = trafficData.GetModel()
	}

	if trafficData.GetOsType() == entity.OsTypeAndroid && len(device.Manufacturer) == 0 {
		device.Manufacturer = "unknown"
	}

	posBody, err := easyjson.Marshal(device)
	if err != nil {
		zap.L().Error("[YLHDspBroker] encode device Marshal err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return ""
	}

	return string(posBody)
}

func (impl *YLHDspBroker) encodeNetwork(trafficData ad_service_entity.TrafficData) string {
	network := &youlianghui_dsp_entity.YLHNetwork{
		ConnectType: impl.mappingConnectionType(trafficData.GetConnectionType()),
		Carrier:     impl.mappingCarrier(trafficData.GetOperatorType()),
		IP:          trafficData.GetRequestIp(),
		UA:          trafficData.GetUserAgent(),
	}

	posBody, err := easyjson.Marshal(network)
	if err != nil {
		zap.L().Error("[YLHDspBroker] encodeNetwork Marshal err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return ""
	}

	return string(posBody)
}

func (impl *YLHDspBroker) encodeGeo(request *ad_service.AdRequest) string {
	geo := &youlianghui_dsp_entity.YLHGeo{
		Lat: int32(request.Device.Lat),
		Lng: int32(request.Device.Lon),
	}

	posBody, err := easyjson.Marshal(geo)
	if err != nil {
		zap.L().Error("[YLHDspBroker] encodeNetwork Marshal err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return ""
	}

	return string(posBody)
}

func (impl *YLHDspBroker) mappingCarrier(carrier entity.OperatorType) int32 {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaTelecom:
		return 3
	case entity.OperatorTypeTietong:
		return 3
	case entity.OperatorTypeChinaUnicom:
		return 2
	default:
		return 0
	}
}

func (impl *YLHDspBroker) mappingOs(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "ios"
	case entity.OsTypeAndroid:
		return "android"
	default:
		return ""
	}
}

func (impl *YLHDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int32 {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 8
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 7
	default:
		return 0
	}
}

func (impl *YLHDspBroker) mappingOrientation(s entity.ScreenOrientationType) int32 {
	switch s {
	case entity.ScreenOrientationTypeLandscape:
		return 90
	case entity.ScreenOrientationTypePortrait:
		return 0
	default:
		return 0
	}
}

func (impl *YLHDspBroker) mappingDeviceType(s entity.DeviceType) int32 {
	switch s {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	default:
		return 0
	}
}

func (impl *YLHDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := impl.PriceManager.GetDspCoder(impl.dspProtocol).EncodeWithKey(uint64(chargePrice), impl.iKey, impl.eKey)
	if err != nil {
		return ""
	}

	return result
}

func (impl *YLHDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("YLHDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &youlianghui_dsp_entity.YLHAdResponse{}

	err = easyjson.Unmarshal(data, response)
	if err != nil {
		zap.L().Error("YLHDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, data)

	if request.IsDebug {
		resbody, _ := json.Marshal(response)
		zap.L().Info("YLHDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	if response.Ret != 0 || len(response.Data) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)
	for _, resBidList := range response.Data {
		for _, resBid := range resBidList.List {
			candidateAd := &entity.Ad{
				DspId:      impl.GetDspId(),
				DspSlotId:  broadcastCandidate.GetDspSlotId(),
				DspSlotKey: broadcastCandidate.GetDspSlotKey(),
			}

			candidateAd.AppInfo = impl.ParseAppInfo(resBid)

			candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, resBid)

			candidateCreative := impl.ParseCreativeData(resBid)
			if candidateCreative == nil {
				return nil, err_code.ErrBrokerResponseInternalFail
			}

			candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
			candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
			candidate.SetBidPrice(uint32(resBid.Ecpm))
			candidate.SetBidType(entity.BidTypeCpm)
			candidate.SetCreative(candidateCreative)
			candidate.SetDspAdID(resBid.AdID)
			candidate.SetDspProtocol(impl.GetDspProtocol())
			result = append(result, candidate)
			break

		}
	}

	return result, nil
}

func (impl *YLHDspBroker) ParseAppInfo(bid youlianghui_dsp_entity.YLHAd) *entity.AppInfo {
	appInfo := &entity.AppInfo{
		PackageName:    bid.PackageName,
		AppName:        bid.AppName,
		Icon:           "",
		WechatExt:      nil,
		AppID:          "",
		AppVersion:     bid.AppVersion,
		PackageSize:    int(bid.PackageSizeBytes),
		Privacy:        bid.PrivacyAgreement,
		Permission:     bid.PermissionsURL,
		PermissionDesc: nil,
		AppDesc:        "",
		AppDescURL:     bid.AppInfoURL,
		Develop:        bid.AdvertiserName,
		AppBeian:       "",
		AppAge:         "",
	}

	return appInfo
}

func (impl *YLHDspBroker) ParseCreativeData(bid youlianghui_dsp_entity.YLHAd) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        type_convert.GetAssertString(bid.CreativeID),
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if len(bid.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         bid.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Description) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         bid.Description,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.ImgList) > 0 {
		for _, img := range bid.ImgList {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img,
				Width:        bid.ImgWidth,
				Height:       bid.ImgHeight,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if len(bid.ImgURL) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          bid.ImgURL,
			Width:        bid.ImgWidth,
			Height:       bid.ImgHeight,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Img2URL) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.Img2URL,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.VideoURL) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          bid.VideoURL,
			Duration:     float64(bid.VideoDuration),
			Width:        bid.VideoWidth,
			Height:       bid.VideoHeight,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}

func (impl *YLHDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid youlianghui_dsp_entity.YLHAd) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		LandingUrl:            bid.LandingPageURL,
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      make([]string, 0),
		VideoStartUrlList:     make([]string, 0),
		LandingAction:         entity.LandingTypeInWebView,
	}

	if len(bid.ImpressionLink) > 0 {
		if strings.Contains(bid.ImpressionLink, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(bid.ImpressionLink, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.ImpressionLink)
		}
	}

	if len(bid.WinNoticeURL) > 0 {
		if strings.Contains(bid.WinNoticeURL, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(bid.WinNoticeURL, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.WinNoticeURL)
		}
	}

	if len(bid.ClickLink) > 0 {
		tracking.ClickMonitorList = append(tracking.ClickMonitorList, bid.ClickLink)
	}

	if len(bid.VideoViewLink) > 0 {
		tracking.VideoStartUrlList = append(tracking.VideoStartUrlList, bid.VideoViewLink)
	}

	if bid.InteractType == 1 {
		tracking.LandingAction = entity.LandingTypeDownload
	}

	//bid.CrtType
	//11 - 图片
	//20 - 视频
	//24 - 三小图

	if len(bid.CustomizedInvokeURL) != 0 {
		tracking.DeepLinkUrl = bid.CustomizedInvokeURL
	}

	if len(bid.UniversalLink) != 0 && request.Device.OsType == entity.OsTypeIOS {
		tracking.DeepLinkUrl = bid.UniversalLink
		tracking.LandingUrl = bid.UniversalLink
	}

	if bid.ConversionEvent != nil {

		if bid.ConversionEvent.DeeplinkInvoke != "" {
			tracking.DeepLinkMonitorList = []string{bid.ConversionEvent.DeeplinkInvoke}
		}

		if bid.ConversionEvent.AppInstalled != "" {
			tracking.AppInstallStartMonitorList = []string{bid.ConversionEvent.AppInstalled}
		}

		if bid.ConversionEvent.InstallFinished != "" {
			tracking.AppInstalledFinishMonitorList = []string{bid.ConversionEvent.InstallFinished}
		}

		if bid.ConversionEvent.DownloadStarted != "" {
			tracking.AppDownloadStartedMonitorList = []string{bid.ConversionEvent.DownloadStarted}
		}

		if bid.ConversionEvent.DownloadFinished != "" {
			tracking.AppDownloadFinishedMonitorList = []string{bid.ConversionEvent.DownloadFinished}
		}
	}

	return tracking

}
