package guangyin_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"fmt"
)

type GuangYinSlotInfo struct {
	*entity.DspSlotInfo
	Height     int    `json:"height"`
	Width      int    `json:"width"`
	AppName    string `json:"app_name"`
	PkgName    string `json:"pkg_name"`
	AppVersion string `json:"app_version"`
	AppID      string `json:"appid"`
}

func (info *GuangYinSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	info.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	info.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")
	info.AppID, _ = dspSlotInfo.ExtraData.GetString("appid")

	return nil
}

type GuangYinSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*GuangYinSlotInfo
}

func NewGuangYinSlotRegister(dspId utils.ID) *GuangYinSlotRegister {
	return &GuangYinSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*GuangYinSlotInfo),
	}
}

func (r *GuangYinSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *GuangYinSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*GuangYinSlotInfo)
	for _, slot := range list {
		dspSlot := &GuangYinSlotInfo{}
		if err := dspSlot.Init(slot); err != nil {
			zap.L().Error("[GuangYinSlotRegister] init slot failed", zap.Error(err), zap.String("slot", fmt.Sprintf("%v", slot.Id)))
			continue
		}

		slotMap[slot.Id] = dspSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *GuangYinSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *GuangYinSlotRegister) GetSlotInfo(slotId utils.ID) *GuangYinSlotInfo {
	return r.dspSlotMap[slotId]
}
