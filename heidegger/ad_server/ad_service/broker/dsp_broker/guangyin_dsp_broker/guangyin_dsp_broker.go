package guangyin_dsp_broker

import (
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/guangyin_dsp_broker/guangyin_dsp_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type GuangYinDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *GuangYinSlotRegister
	log          *zap.Logger
	macroInfo    macro_builder.MonitorMacroInfo
}

func NewGuangYinDspBroker(dspId utils.ID) *GuangYinDspBroker {
	return &GuangYinDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		slotRegister: NewGuangYinSlotRegister(dspId),
		log:          zap.L().With(zap.String("broker", "GuangYinDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "__PRICE__",
			MacroClickDownX: "__DOWN_X__",
			MacroClickDownY: "__DOWN_Y__",
			MacroClickUpX:   "__UP_X__",
			MacroClickUpY:   "__UP_Y__",
		},
	}
}

func (a *GuangYinDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		zap.L().Error("[GuangYinDspBroker] candidateList too many", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := a.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	optype := []int{0}
	creativetype := []int{1, 2, 3}

	bidRequest := &guangyin_dsp_broker_entity.BidRequest{
		Version: "0.4.4",
		App: &guangyin_dsp_broker_entity.App{
			AppID:   dspSlot.AppID,
			Name:    trafficData.GetAppName(),
			Package: trafficData.GetAppBundle(),
			Version: trafficData.GetAppVersion(),
		},
		Device: &guangyin_dsp_broker_entity.Device{
			OsType:            mappingDeviceOs(trafficData.GetOsType()),
			Type:              mappingDeviceType(trafficData.GetDeviceType()),
			Adid:              trafficData.GetAndroidId(),
			Udid:              trafficData.GetOpenUdid(),
			OsVersion:         trafficData.GetOsVersion(),
			Language:          "zh-CN",
			Brand:             request.Device.Brand,
			Model:             trafficData.GetModel(),
			Vendor:            request.Device.Brand,
			Imei:              trafficData.GetImei(),
			Oaid:              trafficData.GetOaid(),
			Height:            int(trafficData.GetScreenHeight()),
			Width:             int(trafficData.GetScreenWidth()),
			Density:           float32(trafficData.GetScreenDensity()),
			Idfa:              trafficData.GetIdfa(),
			IdfaMd5:           trafficData.GetMd5Idfa(),
			Orientation:       mappingOrientation(trafficData.GetScreenOrientation()),
			ScreenDpi:         int(request.Device.DPI),
			Imsi:              request.Device.Imsi,
			Idfv:              trafficData.GetIdfv(),
			RomVersion:        trafficData.GetRomVersion(),
			SysComplingTime:   request.Device.SystemCompileTime,
			Bssid:             trafficData.GetMac(),
			Serialno:          trafficData.GetImei(),
			BootMark:          trafficData.GetBootMark(),
			UpdateMark:        trafficData.GetUpdateMark(),
			Hms:               request.Device.VercodeHms,
			Hag:               request.Device.VercodeAg,
			MiuiVersion:       request.Device.SystemOsUIVersion,
			IsSupportDp:       true,
			StartupTime:       trafficData.GetDeviceStartupTime(),
			MbTime:            trafficData.GetDeviceUpgradeTime(),
			InitTime:          trafficData.GetDeviceInitTime(),
			DiskTotalmemTotal: request.Device.SystemTotalDisk,
			MemTotal:          request.Device.SystemTotalMem,
			CPUNum:            request.Device.SystemTotalCpu,
			CPUFreq:           request.Device.SystemCPUFrequency,
			BatteryPower:      request.Device.SystemBatteryPower,
			ImeiMd5:           trafficData.GetMd5Imei(),
			AndroidIDMd5:      trafficData.GetMd5AndroidId(),
			Caid:              device_utils.GetCaidRaw(trafficData.GetCaid()),
			CaidVersion:       device_utils.GetCaidVersion(trafficData.GetCaid()),
			Timezone:          int(request.Device.TimeZone),
			ModelCode:         request.Device.HardwareMachineCode,
			DeviceName:        request.Device.DeviceName,
			ScreenPpi:         int(request.Device.PPI),
		},
		Network: &guangyin_dsp_broker_entity.Network{
			Mac:            trafficData.GetMac(),
			MacMd5:         trafficData.GetMd5Mac(),
			Net:            mappingConnectionType(trafficData.GetConnectionType()),
			Carrier:        mappingCarrier(trafficData.GetOperatorType()),
			Ua:             trafficData.GetUserAgent(),
			Reffer:         trafficData.GetReferer(),
			Country:        request.Device.CountryCode,
			CoordinateType: mappingCoordinateType(request.Device.GpsType),
		},
		Geo: &guangyin_dsp_broker_entity.Geo{
			Lat: trafficData.GetGeoLatitude(),
			Lng: trafficData.GetGeoLongitude(),
		},
		User: &guangyin_dsp_broker_entity.User{
			Gender: mappingGender(request.UserGender),
		},
		Imp: &guangyin_dsp_broker_entity.Imp{
			PosID:        slotId,
			Width:        int(trafficData.GetSlotWidth()),
			Height:       int(trafficData.GetSlotHeight()),
			OpType:       optype,
			CreativeType: creativetype,
		},
		Downbidfloor:  candidate.GetBidFloor().Price,
		InstalledApps: request.App.InstalledApp,
	}

	if len(request.Device.Paid) != 0 {
		bidRequest.Device.Paid = append(bidRequest.Device.Paid, &guangyin_dsp_broker_entity.Paid{
			Value: request.Device.Paid,
		})
	}
	if request.UseHttps {
		bidRequest.Secure = 1
	}
	if len(request.Device.AppStoreVersion) != 0 {
		if appver, err := strconv.Atoi(request.Device.AppStoreVersion); err == nil {
			bidRequest.AppStoreVersion = appver
		}
	}
	if request.Device.IsIp6 {
		bidRequest.Network.Ipv6 = trafficData.GetRequestIp()
	} else {
		bidRequest.Network.IP = trafficData.GetRequestIp()
	}

	if len(dspSlot.PkgName) > 0 {
		bidRequest.App.Package = dspSlot.PkgName
	}
	if len(dspSlot.AppName) > 0 {
		bidRequest.App.Name = dspSlot.AppName
	}
	if len(dspSlot.AppVersion) > 0 {
		bidRequest.App.Version = dspSlot.AppVersion
	}
	if dspSlot.Width > 0 {
		bidRequest.Imp.Width = dspSlot.Width
	}
	if dspSlot.Height > 0 {
		bidRequest.Imp.Height = dspSlot.Height
	}

	if request.IsDebug {
		payload, _ := sonic.Marshal(bidRequest)
		a.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}

	httpReq, _, err := a.BuildSonicJsonHttpRequest(bidRequest)
	if err != nil {
		a.log.WithError(err).Error("BuildSonicJsonHttpRequest error")
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	a.SampleDspBroadcastSonicJsonRequest(a.DspId, trafficData.GetDspSlotId(), candidate, bidRequest)
	return httpReq, nil
}

func (a *GuangYinDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]

	if response.StatusCode != 200 {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(response.StatusCode), type_convert.GetAssertString(response.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse
	}

	resp := new(guangyin_dsp_broker_entity.BidResponse)
	payload, err := a.ParseSonicJsonHttpResponse(response, data, resp)
	if err != nil {
		a.log.WithError(err).WithField("response", response).Error("ParseSonicJsonHttpResponse error")
		a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		return nil, err_code.ErrBrokerParseError.Wrap(err)
	}

	if request.IsDebug {
		a.log.WithField("resp", string(payload)).Info("ParseResponse debug")
	}
	a.SampleDspBroadcastResponse(a.DspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, payload)
	a.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.Error), resp.Errmsg)

	bid := resp.Data
	if bid == nil || bid.Status != 0 || bid.AdInfo == nil {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0, 1)
	ad := &entity.Ad{
		DspId:      a.DspId,
		DspSlotId:  broadcastCandidate.GetDspSlotId(),
		DspSlotKey: broadcastCandidate.GetDspSlotKey(),
	}

	adMonitorInfo, creative := a.parseCallbacksAndCreative(bid.AdInfo, request.Device.OsType)
	ad.AdMonitorInfo = adMonitorInfo
	if creative == nil {
		a.log.WithError(err_code.ErrBrokerResponseInternalFail).WithField("bidResponse", bid).Error("creative is nil")
		return nil, err_code.ErrBrokerResponseInternalFail
	}
	candidate := ad_service.NewDspAdCandidateWithPool(ad)
	candidate.SetAdCandidateChargePriceEncoder(a.chargePriceEncoder)
	candidate.SetBidPrice(uint32(bid.AdInfo.Ecpm))
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetCreative(creative)
	candidate.SetDspProtocol(a.DspProtocol)
	result = append(result, candidate)

	return result, nil
}

func (a *GuangYinDspBroker) chargePriceEncoder(chargePrice uint32) string {
	if chargePrice > 0 {
		result, err := a.PriceManager.GetDspCoder(a.DspProtocol).EncodeWithKey(uint64(chargePrice), a.GetIKey(), a.GetEKey())
		if err != nil {
			a.log.WithError(err).WithField("price", chargePrice).Errorf("chargePriceEncoder error")
			return ""
		}
		return result
	} else {
		return ""
	}
}

func (a *GuangYinDspBroker) parseCallbacksAndCreative(data *guangyin_dsp_broker_entity.AdInfo, ostype entity.OsType) (*entity.AdMonitorInfo, *entity.Creative) {
	appinfo := data.AppInfo
	material := data.Material
	convurls := data.ConvUrls
	conversion := data.Conversion

	info := &entity.AdMonitorInfo{
		LandingAction: mappingLandingType(data),
	}

	timenow := time.Now()
	for _, clkurl := range data.ClickUrls {
		clkurl = strings.Replace(clkurl, "__TS__", type_convert.GetAssertString(timenow.Unix()), -1)
		info.ClickMonitorList = append(info.ClickMonitorList, strings.Replace(clkurl, "__TS_MS__", type_convert.GetAssertString(timenow.UnixMilli()), -1))
	}

	if len(data.WinUrls) > 0 {
		info.ImpressionMonitorList = append(info.ImpressionMonitorList, data.WinUrls...)
	}
	info.ImpressionMonitorList = append(info.ImpressionMonitorList, data.ShowUrls...)

	if conversion != nil {
		conversion.H5URL = strings.Replace(conversion.H5URL, "__TS__", type_convert.GetAssertString(timenow.Unix()), -1)
		conversion.H5URL = strings.Replace(conversion.H5URL, "__TS_MS__", type_convert.GetAssertString(timenow.UnixMilli()), -1)
		info.LandingUrl = macro_builder.MacroReplace(conversion.H5URL, a.macroInfo)

		info.DeepLinkUrl = conversion.DeeplinkURL
		info.DownloadUrl = macro_builder.MacroReplace(conversion.AppURL, a.macroInfo)
		if ostype == entity.OsTypeIOS {
			info.DeepLinkUrl = conversion.UniversalLinkURL
		}

		if len(conversion.AppletID) > 0 && len(conversion.AppletPath) > 0 {
			info.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   conversion.AppletID,
				ProgramPath: conversion.AppletPath,
			}
		}
	}

	if appinfo != nil {
		info.AppInfo.PackageName = appinfo.Package
		info.AppInfo.AppName = appinfo.Name
		info.AppInfo.AppVersion = appinfo.Version
		info.AppInfo.Develop = appinfo.Corporate
		info.AppInfo.AppDescURL = appinfo.IntroductionInfoURL
		info.AppInfo.AppDesc = appinfo.IntroductionInfo
		info.AppInfo.PackageSize = appinfo.Size
		info.AppInfo.Permission = appinfo.Permissions
		info.AppInfo.Privacy = appinfo.Privacy
		info.AppInfo.Icon = appinfo.IconURL
	}

	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        "",
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}
	video25 := 0
	video50 := 0
	video75 := 0
	video100 := 0

	//material
	if material != nil {
		if len(data.Icons) > 0 {
			for _, icons := range data.Icons {
				creative.MaterialList = append(creative.MaterialList, &entity.Material{
					MaterialType: entity.MaterialTypeIcon,
					Url:          icons,
				})
			}
		}

		title := &entity.Material{MaterialType: entity.MaterialTypeTitle, Data: data.Name}
		if len(data.Name) == 0 {
			title.Data = "点击查看详情"
		}
		creative.MaterialList = append(creative.MaterialList, title)

		desc := &entity.Material{MaterialType: entity.MaterialTypeDesc, Data: data.Desc}
		if len(data.Desc) == 0 {
			desc.Data = "点击查看详情"
		}
		creative.MaterialList = append(creative.MaterialList, desc)

		for _, features := range material.Features {
			if features.Type == 2 || features.Type == 3 || features.Type == 4 {
				creative.MaterialList = append(creative.MaterialList, &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          features.MaterialURL,
					Height:       int32(features.Height),
					Width:        int32(features.Width),
				})
			} else if features.Type == 1 {
				video100 = int(features.VDuration / 1000)
				creative.MaterialList = append(creative.MaterialList, &entity.Material{
					MaterialType: entity.MaterialTypeVideo,
					Url:          features.MaterialURL,
					Height:       int32(features.VHeight),
					Width:        int32(features.VWidth),
					Duration:     float64(features.VDuration / 1000),
					FileSize:     int32(features.Size),
				})

				if len(features.CoverURL) > 0 {
					creative.MaterialList = append(creative.MaterialList, &entity.Material{
						MaterialType: entity.MaterialTypeCoverImage,
						Url:          features.CoverURL,
						Height:       int32(features.VHeight),
						Width:        int32(features.VWidth),
					})
				}

			}
		}
	}

	if convurls != nil {
		if len(convurls.DlBegin) > 0 {
			info.AppDownloadStartedMonitorList = append(info.AppDownloadStartedMonitorList, convurls.DlBegin...)
		}

		if len(convurls.DlEnd) > 0 {
			info.AppDownloadFinishedMonitorList = append(info.AppDownloadFinishedMonitorList, convurls.DlEnd...)
		}

		if len(convurls.IsBegin) > 0 {
			info.AppInstallStartMonitorList = append(info.AppInstallStartMonitorList, convurls.IsBegin...)
		}

		if len(convurls.IsEnd) > 0 {
			info.AppInstalledFinishMonitorList = append(info.AppInstalledFinishMonitorList, convurls.IsEnd...)
		}
		if len(convurls.Active) > 0 {
			info.AppOpenMonitorList = append(info.AppOpenMonitorList, convurls.Active...)
		}

		if len(convurls.DplinkTry) > 0 {
			info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, convurls.DplinkTry...)
		}
		if len(convurls.Dplink) > 0 {
			info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, convurls.Dplink...)
		}

		if len(convurls.InvokeAppFail) > 0 {
			info.DeepLinkFailedMonitorList = append(info.DeepLinkFailedMonitorList, convurls.InvokeAppFail...)
		}
		if len(convurls.DplinkFail) > 0 {
			info.DeepLinkFailedMonitorList = append(info.DeepLinkFailedMonitorList, convurls.DplinkFail...)
		}

		//video event
		video25 = video100 / 4
		video50 = video100 / 2
		video75 = video100 * 75 / 100

		if len(convurls.Quarter1) > 0 {
			for _, item := range convurls.Quarter1 {
				info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   item,
					Delay: video25,
				})
			}
		}

		if len(convurls.Quarter2) > 0 {
			for _, item := range convurls.Quarter2 {
				info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   item,
					Delay: video50,
				})
			}
		}

		if len(convurls.Quarter3) > 0 {
			for _, item := range convurls.Quarter3 {
				info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
					Url:   item,
					Delay: video75,
				})
			}
		}

		if len(convurls.ReportPoint) > 0 {
			for _, item := range convurls.ReportPoint {
				for _, items := range item.Urls {
					info.DelayMonitorUrlList = append(info.DelayMonitorUrlList, entity.AdDelayMonitor{
						Url:   items,
						Delay: int(item.Pt),
					})
				}
			}
		}

	}

	//宏替换
	info.ImpressionMonitorList = macro_builder.MacroReplaceList(info.ImpressionMonitorList, a.macroInfo)
	info.ClickMonitorList = macro_builder.MacroReplaceList(info.ClickMonitorList, a.macroInfo)

	return info, creative
}

func mappingOrientation(orientation entity.ScreenOrientationType) int {
	switch orientation {
	case entity.ScreenOrientationTypePortrait:
		return 1
	case entity.ScreenOrientationTypeLandscape:
		return 2
	default:
		return 0
	}
}
func mappingDeviceOs(osType entity.OsType) int {
	switch osType {
	case entity.OsTypeIOS:
		return 2
	case entity.OsTypeAndroid:
		return 1
	case entity.OsTypeWindowsPhone:
		return 5
	case entity.OsTypeMacOs:
		return 6
	default:
		return 0
	}
}
func mappingCarrier(carrier entity.OperatorType) int {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 3
	case entity.OperatorTypeChinaTelecom:
		return 2
	default:
		return 0
	}
}
func mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}
func mappingCoordinateType(gpsType entity.GpsType) int {
	switch gpsType {
	case entity.GpsTypeWSG84:
		return 1
	case entity.GpsTypeGCJ02:
		return 2
	case entity.GpsTypeBd09:
		return 3
	default:
		return 1
	}
}
func mappingGender(gender entity.UserGenderType) int {
	switch gender {
	case entity.UserGenderMan:
		return 1
	case entity.UserGenderWoman:
		return 2
	default:
		return 0
	}
}

func mappingDeviceType(dy entity.DeviceType) int {
	switch dy {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypeOtt:
		return 3
	case entity.DeviceTypePc:
		return 4
	default:
		return 9
	}
}

// 映射落地页类型
func mappingLandingType(bid *guangyin_dsp_broker_entity.AdInfo) entity.LandingType {
	switch bid.OpType {
	case 1:
		return entity.LandingTypeDownload
	case 3:
		return entity.LandingTypeDeepLink
	case 6:
		return entity.LandingTypeWeChatProgram
	default:
		return entity.LandingTypeInWebView
	}
}

func (a *GuangYinDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return a.slotRegister
}
