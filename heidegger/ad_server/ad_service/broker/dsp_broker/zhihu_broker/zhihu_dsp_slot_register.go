package zhihu_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type ZhihuSlotSlotInfo struct {
	*entity.DspSlotInfo
	DealId int64 `json:"deal_id"`
}

func (info *ZhihuSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	dealId, err := dspSlotInfo.ExtraData.GetString("deal_id")
	if err != nil {
		return fmt.Errorf("get deal_id from extra_data failed, err: %v", err)
	}

	info.DealId = type_convert.GetAssertInt64(dealId)

	return nil
}

type ZhihuDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*ZhihuSlotSlotInfo
}

func NewZhihuDspSlotRegister(dspId utils.ID) *ZhihuDspSlotRegister {
	return &ZhihuDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*ZhihuSlotSlotInfo),
	}
}

func (r *ZhihuDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *ZhihuDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*ZhihuSlotSlotInfo)
	for _, slot := range list {
		qihangSlot := &ZhihuSlotSlotInfo{}
		if err := qihangSlot.Init(slot); err != nil {
			zap.L().Error("[ZhihuDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = qihangSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *ZhihuDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *ZhihuDspSlotRegister) GetSlotInfo(slotId utils.ID) *ZhihuSlotSlotInfo {
	return r.dspSlotMap[slotId]
}
