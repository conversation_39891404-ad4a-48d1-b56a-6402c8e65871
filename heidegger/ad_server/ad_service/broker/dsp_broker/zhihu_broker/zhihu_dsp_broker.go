package zhihu_broker

import (
	"bytes"
	"fmt"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/zhihu_broker/zhihu_json_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
)

const zhihuSource = "yiyun"

type ZhihuDspBroker struct {
	dsp_broker.DspBrokerBase

	dspSlotRegister *ZhihuDspSlotRegister

	bidUrl      string
	dspId       utils.ID
	iKey        string
	eKey        string
	dspProtocol string
}

func NewZhihuDspBroker(dspId utils.ID) *ZhihuDspBroker {
	return &ZhihuDspBroker{
		dspId:           dspId,
		dspSlotRegister: NewZhihuDspSlotRegister(dspId),
	}
}

func (b *ZhihuDspBroker) GetDspId() utils.ID {
	return b.dspId
}

func (b *ZhihuDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.dspSlotRegister
}

func (b *ZhihuDspBroker) UpdateDspInfo(dsp *entity.Dsp) error {
	b.bidUrl = dsp.BidUrl
	b.iKey = dsp.Ikey
	b.eKey = dsp.Ekey
	b.dspProtocol = dsp.Protocol
	return nil
}

func (b *ZhihuDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("ZhihuDspBroker.EncodeRequest Enter")
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := b.dspSlotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound.Wrap(fmt.Errorf("slot:%s", trafficData.GetDspSlotId()))
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidRequest := &zhihu_json_entity.ZhihuBidRequest{
		RequestId:   trafficData.GetRequestId(),
		SlotId:      type_convert.GetAssertInt(slotId),
		Source:      zhihuSource,
		ApiVersion:  "1.0",
		ZhihuDealId: dspSlot.DealId,
		Device: &zhihu_json_entity.ZhihuDevice{
			Ua:           trafficData.GetUserAgent(),
			Ip:           trafficData.GetRequestIp(),
			Os:           b.mappingOsType(trafficData.GetOsType()),
			Osv:          trafficData.GetOsVersion(),
			Brand:        trafficData.GetBrand(),
			Model:        trafficData.GetModel(),
			Imei:         trafficData.GetImei(),
			ImeiMd5:      trafficData.GetMd5Imei(),
			Oaid:         trafficData.GetOaid(),
			OaidMd5:      trafficData.GetMd5Oaid(),
			Idfa:         trafficData.GetIdfa(),
			IdfaMd5:      trafficData.GetMd5Idfa(),
			AndroidId:    trafficData.GetAndroidId(),
			AndroidIdMd5: trafficData.GetMd5AndroidId(),
			Network:      b.mappingNetworkType(trafficData.GetConnectionType()),
			Mac:          trafficData.GetMac(),
			MacMd5:       trafficData.GetMd5Mac(),
			ScreenWidth:  trafficData.GetScreenWidth(),
			ScreenHeight: trafficData.GetScreenHeight(),
			Lat:          request.Device.Lat,
			Lon:          request.Device.Lon,
		},
	}

	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()
	_, err := easyjson.MarshalToWriter(bidRequest, buffer)
	if err != nil {
		zap.L().Error("ZhihuDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err_code.ErrBroadcastRequestBuildFail
	}

	body := buffer.Get()

	if request.IsDebug {
		zap.L().Info("ZhihuDspBroker.EncodeRequest end, ZhihuRequest", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(body))))))
	}

	req, err := http.NewRequest(http.MethodPost, b.bidUrl, bytes.NewBuffer(body))
	if err != nil {
		zap.L().Error("ZhihuDspBroker http NewRequest err", zap.Error(err))
		return nil, err
	}
	req.Header["Content-Type"] = []string{"application/json"}

	b.SampleDspBroadcastRequest(b.dspId, dspSlot.Id, candidate, body)

	return req, nil
}

func (b *ZhihuDspBroker) mappingOsType(s entity.OsType) int {
	switch s {
	case entity.OsTypeIOS:
		return 1
	case entity.OsTypeAndroid:
		return 2
	default:
		return 0
	}
}

func (b *ZhihuDspBroker) mappingNetworkType(s entity.ConnectionType) int32 {
	switch s {
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (b *ZhihuDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("ZhihuDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	if request.IsDebug {
		zap.L().Info("ZhihuDspBroker response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(data))))))
	}

	response := &zhihu_json_entity.ZhihuResponse{}
	err = easyjson.Unmarshal(data, response)
	if err != nil {
		zap.L().Error("ZhihuDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}
	broadcastCandidate := broadcastCandidateList[0]
	b.SampleDspBroadcastResponse(b.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, data)

	if response.Code != 0 || len(response.AdJSON) < 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	adjson := &zhihu_json_entity.ZhihuAdJSON{}
	err = easyjson.Unmarshal([]byte(response.AdJSON), adjson)
	if err != nil {
		zap.L().Error("ZhihuDspBroker.DecodeResponse json.AdJSON,AdJSON:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response.AdJSON)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	if len(adjson.Ads) < 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	candidateList := make(ad_service.DspAdCandidateList, 0)
	for _, ad := range adjson.Ads {
		if ad == nil {
			continue
		}

		tracking := &entity.AdMonitorInfo{
			ImpressionMonitorList: ad.ImpressionTracks,
			ClickMonitorList:      ad.ClickTracks,
			LandingAction:         entity.LandingTypeInWebView,
		}

		if len(ad.Creatives) > 0 && ad.Creatives[0].Asset != nil {
			tracking.LandingUrl = ad.Creatives[0].Asset.LandingURL
			tracking.DeepLinkUrl = ad.Creatives[0].Asset.DeepURL

		}

		candidateAd := &entity.Ad{
			DspId:         b.GetDspId(),
			DspSlotId:     broadcastCandidate.GetDspSlotId(),
			DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
			AdMonitorInfo: tracking,
		}

		candidateCreative := &entity.Creative{
			CreativeKey:  type_convert.GetAssertString(ad.ID),
			MaterialList: make(entity.MaterialList, 0),
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(b.chargePriceEncoder)
		candidate.SetBidPrice(uint32(100))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspProtocol(b.GetDspProtocol())
		candidateList = append(candidateList, candidate)
	}

	return candidateList, nil
}

func (b *ZhihuDspBroker) chargePriceEncoder(chargePrice uint32) string {
	return ""
	result, err := b.PriceManager.GetDspCoder(b.dspProtocol).EncodeWithKey(uint64(chargePrice), b.iKey, b.eKey)
	if err != nil {
		return ""
	}

	return result
}
