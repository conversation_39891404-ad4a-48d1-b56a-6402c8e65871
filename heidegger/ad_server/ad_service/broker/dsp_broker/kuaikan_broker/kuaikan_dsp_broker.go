package kuaikan_broker

import (
	"fmt"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/kuaikan_broker/converter"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/kuaikan_broker/kuaikan_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"net/http"
)

type KuaiKanDspBroker struct {
	dsp_broker.DspBrokerBase

	dspSlotRegister *dsp_slot_register.SimpleDspSlotRegister

	bidUrl string
	dspId  utils.ID
	tagId  string
}

func NewKuaiKanDspBroker(dspId utils.ID) *KuaiKanDspBroker {
	return &KuaiKanDspBroker{
		dspId:           dspId,
		tagId:           "39.1.a.1",
		dspSlotRegister: dsp_slot_register.NewSimpleDspSlotRegister(dspId),
	}
}

func (k *KuaiKanDspBroker) GetDspId() utils.ID {
	return k.dspId
}

func (k *KuaiKanDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return k.dspSlotRegister
}

func (k *KuaiKanDspBroker) UpdateDspInfo(dsp *entity.Dsp) error {
	k.bidUrl = dsp.BidUrl
	return nil
}

func (k *KuaiKanDspBroker) BuildRequest(adRequest *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	deviceId, _ := trafficData.GetDeviceIdWithType()
	brokerRequest := kuaikan_broker_entity.BidRequest{
		ID:   trafficData.GetRequestId(),
		Imps: make([]kuaikan_broker_entity.Imp, 0),
		App: kuaikan_broker_entity.App{
			Name:    trafficData.GetAppName(),
			Bundle:  trafficData.GetAppBundle(),
			Version: trafficData.GetOsVersion(),
		},
		Device: kuaikan_broker_entity.Device{
			UA:            trafficData.GetUserAgent(),
			IP:            trafficData.GetRequestIp(),
			DevType:       converter.DeviceType(trafficData.GetDeviceType()),
			Make:          trafficData.GetBrand(),
			Model:         trafficData.GetModel(),
			OS:            converter.OsType(trafficData.GetOsType()),
			OSV:           trafficData.GetOsVersion(),
			W:             int(trafficData.GetScreenWidth()),
			H:             int(trafficData.GetScreenHeight()),
			CT:            converter.ConnectionType(trafficData.GetConnectionType()),
			CA:            converter.CarrierType(trafficData.GetOperatorType()),
			IMEI:          trafficData.GetImei(),
			IMEIMD5:       trafficData.GetMd5Imei(),
			OAID:          trafficData.GetOaid(),
			IDFA:          trafficData.GetIdfa(),
			IDFAMD5:       trafficData.GetMd5Idfa(),
			MAC:           trafficData.GetMac(),
			MACMD5:        trafficData.GetMd5Mac(),
			BootMark:      trafficData.GetBootMark(),
			UpdateMark:    trafficData.GetUpdateMark(),
			DeviceInitSec: deviceId,
			Ext: kuaikan_broker_entity.DeviceExt{
				AndroidID:    trafficData.GetAndroidId(),
				AndroidIDMD5: trafficData.GetMd5AndroidId(),
			},
		},
		User: kuaikan_broker_entity.User{
			ID: deviceId,
		},
		SupportHttps: 1,
	}

	if brokerRequest.App.Name == "" {
		brokerRequest.App.Name = "快看漫画"
	}
	if brokerRequest.App.Bundle == "" {
		brokerRequest.App.Bundle = "com.kuaikan.comic"
	}

	imps := kuaikan_broker_entity.Imp{
		ID: trafficData.GetRequestIp(),
		Native: kuaikan_broker_entity.Native{
			Layout: 1,
			Width:  300,
			Height: 400,
		},
		TagID: k.tagId,
	}
	brokerRequest.Imps = append(brokerRequest.Imps, imps)

	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()

	_, err := easyjson.MarshalToWriter(brokerRequest, buffer)
	if err != nil {
		fmt.Println("Error in JSON marshalling:", err)
		return nil, err
	}

	zap.L().Info("KuaiKanDspBroker url:, send", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", k.bidUrl)))), zap.String("param2", fmt.Sprintf("%v", string(buffer.Get()))))

	req, err := http.NewRequest("POST", k.bidUrl, buffer.GetReadCloser())
	if err != nil {
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	return req, nil
}

func (k *KuaiKanDspBroker) ParseResponse(adRequest *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]
	body := k.SampleDspBroadcastResponseReader(k.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, response.Body)

	resp := kuaikan_broker_entity.Response{}
	if err := easyjson.UnmarshalFromReader(body, &resp); err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	if resp.Code != 200 {
		return nil, err_code.ErrBrokerResponse.Wrap(fmt.Errorf(resp.Message))
	}

	result := make(ad_service.DspAdCandidateList, 0, len(resp.Data.SeatBids))
	for _, item := range resp.Data.SeatBids {
		for _, bid := range item.Bids {
			adMonitorInfo := entity.AdMonitorInfo{
				LandingUrl:            bid.ClickURL,
				DeepLinkUrl:           bid.Deeplink,
				ClickMonitorList:      bid.Monitor.ClickURLs,
				ImpressionMonitorList: bid.Monitor.ExposalURLs,
			}

			candidateAd := &entity.Ad{
				AdMonitorInfo: &adMonitorInfo,
			}
			candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
			result = append(result, candidate)
		}

	}

	return result, nil
}
