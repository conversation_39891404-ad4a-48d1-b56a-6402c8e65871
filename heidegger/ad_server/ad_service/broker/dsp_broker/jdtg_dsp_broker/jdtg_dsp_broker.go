package jdtg_dsp_broker

import (
	"encoding/json"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jdtg_dsp_broker/jdtg_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"io"
	"net/http"
	"fmt"
)

type JdTgDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *JdtgDspSlotRegister
	dspId        utils.ID
}

func NewJdTgDspBroker(dspId utils.ID) *JdTgDspBroker {
	return &JdTgDspBroker{
		slotRegister: NewJdtgDspSlotRegister(dspId),
		dspId:        dspId,
	}
}

func (impl *JdTgDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *JdTgDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		zap.L().Error("JdTgDspBroker.candidateList len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(candidateList))))))
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		zap.L().Error("JdTgDspBroker.dspSlot not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(trafficData.GetDspSlotId())))))
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	jdtgRequest := &jdtg_dsp_entity.JdtgRequest{
		BidID:      trafficData.GetRequestId(),
		PosID:      dspSlot.PosID,
		MediaPosID: dspSlot.MediaPosID,
		StyleName:  dspSlot.StyleName,
		Channel:    dspSlot.Channel,
		ContractID: dspSlot.ContractID,
		IP:         trafficData.GetRequestIp(),
		Os:         impl.mappingOsType(trafficData.GetOsType()),
		Network:    impl.mappingConnectionType(trafficData.GetConnectionType()),
		Idfa:       trafficData.GetIdfa(),
		Oaid:       trafficData.GetOaid(),
		Ua:         trafficData.GetUserAgent(),
		Imei:       trafficData.GetImei(),
	}

	if len(trafficData.GetCaid()) > 0 {
		jdtgRequest.CaidInfo = append(jdtgRequest.CaidInfo, jdtg_dsp_entity.CaidInfo{
			Caid:    device_utils.GetCaidRaw(trafficData.GetCaid()),
			Version: device_utils.GetCaidVersion(trafficData.GetCaid()),
		})
	}

	if len(request.Device.Caids) > 0 {
		for _, caidItem := range request.Device.Caids {
			if caidItem == trafficData.GetCaid() {
				continue
			}
			deviceCaid1 := jdtg_dsp_entity.CaidInfo{
				Caid:    device_utils.GetCaidRaw(caidItem),
				Version: device_utils.GetCaidVersion(caidItem),
			}
			jdtgRequest.CaidInfo = append(jdtgRequest.CaidInfo, deviceCaid1)
		}
	}

	req, _, err := impl.BuildEasyJsonHttpRequest(jdtgRequest)
	if err != nil {
		zap.L().Error("JdTgDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}

	if request.IsDebug {
		zap.L().Info("JdTgDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", jdtgRequest.DumpJson())))))
	}

	impl.SampleDspBroadcastEasyjsonRequest(impl.dspId, dspSlot.Id, candidate, jdtgRequest)

	return req, nil
}

func (impl *JdTgDspBroker) mappingOsType(os entity.OsType) string {
	switch os {
	case entity.OsTypeIOS:
		return "iOS"
	case entity.OsTypeAndroid:
		return "Android"
	default:
		return "Android"
	}
}

func (impl *JdTgDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (impl *JdTgDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	broadcastCandidate := broadcastCandidateList[0]
	if resp.StatusCode != 200 {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(resp.StatusCode), type_convert.GetAssertString(resp.StatusCode))
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "1", "读取body失败")
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &jdtg_dsp_entity.JdtgResponse{}
	resBody, err := impl.ParseJsonHttpResponse(resp, data, response)
	//err = easyjson.Unmarshal(data, response)
	if err != nil {
		zap.L().Error("JdTgDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resp.Body)))), zap.Error(err))
		impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), "2", "body反序列化失败")
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := json.Marshal(response)
		zap.L().Info("JdTgDspBroker raw reponse1", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}
	impl.DspResponseAdMetrics(broadcastCandidate.GetAd().AdId.String(), broadcastCandidate.GetDspSlotId().String(), type_convert.GetAssertString(response.Status), response.Msg)

	if response.Status != 0 || response.Ad == nil {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)
	ad := response.Ad

	candidateAd := &entity.Ad{
		DspId:      impl.GetDspId(),
		DspSlotId:  broadcastCandidate.GetDspSlotId(),
		DspSlotKey: broadcastCandidate.GetDspSlotKey(),
	}

	candidateAd.AdMonitorInfo = impl.ParseTrackingData(request, ad)

	candidateCreative := impl.ParseCreativeData(ad)
	if candidateCreative == nil {
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
	candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
	//candidate.SetBidPrice()
	candidate.SetBidType(entity.BidTypeCpm)
	candidate.SetCreative(candidateCreative)
	candidate.SetDspAdID(ad.ID)
	candidate.SetDspProtocol(impl.GetDspProtocol())
	result = append(result, candidate)

	return result, nil
}

func (impl *JdTgDspBroker) chargePriceEncoder(chargePrice uint32) string {
	return ""
}

func (impl *JdTgDspBroker) ParseCreativeData(ad *jdtg_dsp_entity.Ad) *entity.Creative {

	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        ad.ID,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if len(ad.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         ad.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(ad.SubTitle) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         ad.SubTitle,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(ad.Creative) > 0 {
		for _, item := range ad.Creative {
			if len(item.CreativeURL) > 0 {
				material := &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          item.CreativeURL,
				}
				creative.MaterialList = append(creative.MaterialList, material)
			}
		}
	}

	return creative
}

func (impl *JdTgDspBroker) ParseTrackingData(request *ad_service.AdRequest, ad *jdtg_dsp_entity.Ad) *entity.AdMonitorInfo {

	tracking := &entity.AdMonitorInfo{
		ImpressionMonitorList: ad.ImpMonitorURL,
		ClickMonitorList:      ad.ClickMonitorURL,
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkUrl:           ad.OpenURL,
		LandingUrl:            ad.LinkURL,
	}

	if request.Device.OsType == entity.OsTypeIOS && ad.ULinkURL != "" {
		tracking.DeepLinkUrl = ad.ULinkURL
	}

	if len(tracking.DeepLinkUrl) > 0 {
		tracking.LandingAction = entity.LandingTypeDeepLink
	}

	return tracking

}
