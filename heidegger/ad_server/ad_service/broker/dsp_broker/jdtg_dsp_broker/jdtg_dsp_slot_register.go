package jdtg_dsp_broker

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

/*
配置信息初始化
insert into `ad_enums`(`title`, `value`, `parent`, `ext_data`, `create_time`, `update_time`) values
('京东天宫', 'jdtg', 594, '{"dsp_slot_ext_attrs":[{"key":"posId","label":"天宫id","type":"input","required":false,"is_number":false,"multiple":false,"comma_separated":false},{"key":"mediaPosId","label":"媒体资源位id","type":"input","required":false,"is_number":false,"multiple":false,"comma_separated":false},{"key":"styleName","label":"广告类型","type":"input","required":false,"is_number":true,"multiple":false,"comma_separated":false,"options":[{"label":"开屏图片","value":1},{"label":"信息流-大图","value":2},{"label":"信息流-三图","value":3},{"label":"信息流-图文","value":4},{"label":"信息流-图块","value":5},{"label":"信息流-视频","value":6}]},{"key":"channel","label":"渠道ID","type":"input","required":false,"is_number":false,"multiple":false,"comma_separated":false},{"key":"contractId","label":"合同ID","type":"input","required":false,"is_number":false,"multiple":false,"comma_separated":false}]}',
CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
*/

type JdtgSlotSlotInfo struct {
	*entity.DspSlotInfo
	//Height     int    `json:"height"`
	//Width      int    `json:"width"`
	//AppName    string `json:"app_name"`
	//PkgName    string `json:"pkg_name"`
	//AppVersion string `json:"app_version"`
	//天宫id
	PosID string `json:"posId"`
	//媒体资源位id
	MediaPosID string `json:"mediaPosId"`
	//启动页：1-全屏图片
	//信息流：2-大图 3-三图 4-图文 5-图块  6-视频
	StyleName int `json:"styleName"`
	//渠道ID
	Channel string `json:"channel"`
	//合同ID
	ContractID string `json:"contractId"`
}

func (info *JdtgSlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo
	var err error

	//info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	//info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	//info.AppName, _ = dspSlotInfo.ExtraData.GetString("app_name")
	//info.PkgName, _ = dspSlotInfo.ExtraData.GetString("pkg_name")
	//info.AppVersion, _ = dspSlotInfo.ExtraData.GetString("app_version")
	info.StyleName, err = dspSlotInfo.ExtraData.GetInt("styleName")
	if err != nil {
		return fmt.Errorf("get [styleName] from extra_data failed, err: %v", err)
	}
	info.PosID, _ = dspSlotInfo.ExtraData.GetString("posId")
	info.MediaPosID, _ = dspSlotInfo.ExtraData.GetString("mediaPosId")
	//info.StyleName, _ = dspSlotInfo.ExtraData.GetInt("styleName")
	info.Channel, _ = dspSlotInfo.ExtraData.GetString("channel")
	info.ContractID, _ = dspSlotInfo.ExtraData.GetString("contractId")

	return nil
}

type JdtgDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*JdtgSlotSlotInfo
}

func NewJdtgDspSlotRegister(dspId utils.ID) *JdtgDspSlotRegister {
	return &JdtgDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*JdtgSlotSlotInfo),
	}
}

func (y *JdtgDspSlotRegister) GetDspId() utils.ID {
	return y.dspId
}

func (y *JdtgDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	dspSlotMap := make(map[utils.ID]*JdtgSlotSlotInfo)
	for _, slotInfo := range list {
		slot := &JdtgSlotSlotInfo{}
		if err := slot.Init(slotInfo); err != nil {
			zap.L().Error("[JdtgDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		dspSlotMap[slot.Id] = slot
	}

	y.dspSlotList = list
	y.dspSlotMap = dspSlotMap
	return nil
}

func (y *JdtgDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return y.dspSlotList
}

func (y *JdtgDspSlotRegister) GetSlotInfo(slotId utils.ID) *JdtgSlotSlotInfo {
	return y.dspSlotMap[slotId]
}
