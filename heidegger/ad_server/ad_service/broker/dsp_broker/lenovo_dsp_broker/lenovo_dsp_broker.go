package lenovo_dsp_broker

import (
	"bytes"
	"fmt"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/lenovo_dsp_broker/lenovo_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"io"
	"net/http"
	"strings"
)

type LenovoDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *LenovoDspSlotRegister
	bidUrl       string
	dspId        utils.ID

	dspProtocol   string
	iKey          string
	eKey          string
	MacroWinPrice string
}

func NewLenovoDspBroker(dspId utils.ID) *LenovoDspBroker {
	return &LenovoDspBroker{
		slotRegister:  NewLenovoDspSlotRegister(dspId),
		dspId:         dspId,
		MacroWinPrice: "__AUCTION_PRICE__",
	}
}

func (impl *LenovoDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return impl.slotRegister
}

func (impl *LenovoDspBroker) GetDspId() utils.ID {
	return impl.dspId
}

func (impl *LenovoDspBroker) UpdateDspInfo(dsp *entity.Dsp) error {
	impl.bidUrl = dsp.BidUrl
	impl.iKey = dsp.Ikey
	impl.eKey = dsp.Ekey
	impl.dspProtocol = dsp.Protocol
	return nil
}

func (impl *LenovoDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	zap.L().Debug("LenovoDspBroker.EncodeRequest Enter")

	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := impl.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, fmt.Errorf("empty slot")
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	bidFloor := candidate.GetBidFloor()
	width := int32(0)
	height := int32(0)
	if len(request.SlotSize) > 0 {
		width = int32(request.SlotSize[0].Width)
		height = int32(request.SlotSize[0].Height)
	}

	if dspSlot.Width != 0 && dspSlot.Height != 0 {
		width = int32(dspSlot.Width)
		height = int32(dspSlot.Height)
	}

	lenovoRequest := &lenovo_broker_entity.LenovoRequest{
		RequestID:  trafficData.GetRequestId(),
		ApiVersion: "3.9",
		Pos: lenovo_broker_entity.Pos{
			ID:       slotId,
			Width:    width,
			Height:   height,
			AdNum:    1,
			BidFloor: int32(bidFloor.Price),
			BidType:  0,
		},
		App:    impl.encodeApp(trafficData, dspSlot),
		Device: impl.encodeDevice(request, trafficData),
		Network: lenovo_broker_entity.Network{
			IP:             trafficData.GetRequestIp(),
			ConnectionType: impl.mappingConnectionType(trafficData.GetConnectionType()),
			OperatorType:   impl.mappingCarrier(trafficData.GetOperatorType()),
		},
		User: impl.encodeUser(request),
	}

	if trafficData.GetGeoLatitude() != 0 || trafficData.GetGeoLongitude() != 0 {
		lenovoRequest.Geo = &lenovo_broker_entity.Geo{
			Lat: trafficData.GetGeoLatitude(),
			Lng: trafficData.GetGeoLongitude(),
		}
	}

	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()
	_, err := easyjson.MarshalToWriter(lenovoRequest, buffer)
	if err != nil {
		zap.L().Error("LenovoDspBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err_code.ErrBroadcastRequestBuildFail
	}

	body := buffer.Get()

	if request.IsDebug {
		zap.L().Info("LenovoDspBroker.EncodeRequest end, kejiRequest", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(body))))))
	}

	req, err := http.NewRequest(http.MethodPost, impl.bidUrl, bytes.NewBuffer(body))
	if err != nil {
		zap.L().Error("LenovoDspBroker http NewRequest err", zap.Error(err))
		return nil, err
	}
	req.Header["Content-Type"] = []string{"application/json"}
	req.Header["X-FORWARDED-FOR"] = []string{trafficData.GetRequestIp()}

	impl.SampleDspBroadcastRequest(impl.dspId, dspSlot.Id, candidate, body)

	return req, nil

}

func (impl *LenovoDspBroker) encodeUser(request *ad_service.AdRequest) *lenovo_broker_entity.User {
	user := &lenovo_broker_entity.User{}

	if request.UserGender == entity.UserGenderMan {
		user.Gender = 1
	} else if request.UserGender == entity.UserGenderWoman {
		user.Gender = 0
	}
	if request.UserAge > 0 {
		user.YOB = request.UserAge
	}

	user.UID = request.UserId
	return user
}

func (impl *LenovoDspBroker) encodeApp(trafficData ad_service_entity.TrafficData, dspSlot *LenovoSlotSlotInfo) lenovo_broker_entity.App {
	app := lenovo_broker_entity.App{
		AppID: dspSlot.AppId,
	}

	app.AppBundle = trafficData.GetAppBundle()
	app.AppVersion = trafficData.GetAppVersion()
	return app
}

func (impl *LenovoDspBroker) encodeDevice(request *ad_service.AdRequest, trafficData ad_service_entity.TrafficData) lenovo_broker_entity.Device {
	device := lenovo_broker_entity.Device{
		Imei:    trafficData.GetImei(),
		ImeiMd5: trafficData.GetMd5Imei(),
		Oaid:    trafficData.GetOaid(),
		OaidMd5: trafficData.GetMd5Oaid(),
		//Imsi:            trafficData,
		AndroidID:       trafficData.GetAndroidId(),
		AndroidIdMd5:    trafficData.GetMd5AndroidId(),
		Idfa:            trafficData.GetIdfa(),
		IdfaMd5:         trafficData.GetMd5Idfa(),
		Caid:            trafficData.GetCaid(),
		Mac:             trafficData.GetMac(),
		MacMd5:          trafficData.GetMd5Mac(),
		DeviceType:      impl.mappingDeviceType(trafficData.GetDeviceType()),
		UserAgent:       trafficData.GetUserAgent(),
		Vendor:          trafficData.GetBrand(),
		Model:           trafficData.GetModel(),
		OsType:          impl.mappingOs(trafficData.GetOsType()),
		OsVersion:       trafficData.GetOsVersion(),
		Orientation:     impl.mappingOrientation(trafficData.GetScreenOrientation()),
		ScreenWidth:     trafficData.GetScreenWidth(),
		ScreenHeight:    trafficData.GetScreenHeight(),
		Density:         float32(trafficData.GetScreenDensity()),
		Dpi:             0,
		BootMark:        trafficData.GetBootMark(),
		UpdateMark:      trafficData.GetUpdateMark(),
		HMSVersion:      "",
		AppStoreVersion: "",
		MiuiVersion:     "",
		CpuNum:          0,
		Memory:          "",
		BootTime:        trafficData.GetBootMark(),
		UpdateTime:      trafficData.GetUpdateMark(),
		TimeZone:        "28800",
	}

	return device
}

func (impl *LenovoDspBroker) mappingCarrier(carrier entity.OperatorType) int32 {
	switch carrier {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaTelecom:
		return 3
	case entity.OperatorTypeTietong:
		return 3
	case entity.OperatorTypeChinaUnicom:
		return 2
	default:
		return 0
	}
}

func (impl *LenovoDspBroker) mappingConnectionType(connectionType entity.ConnectionType) int32 {
	switch connectionType {
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionTypeUnknown:
		return 0
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (impl *LenovoDspBroker) mappingDeviceType(deviceType entity.DeviceType) int32 {
	switch deviceType {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	default:
		return 0
	}
}

func (impl *LenovoDspBroker) mappingOs(os entity.OsType) int32 {
	switch os {
	case entity.OsTypeIOS:
		return 2
	case entity.OsTypeAndroid:
		return 1
	default:
		return 0
	}
}

func (impl *LenovoDspBroker) mappingOrientation(s entity.ScreenOrientationType) int32 {
	switch s {
	case entity.ScreenOrientationTypeLandscape:
		return 2
	case entity.ScreenOrientationTypePortrait:
		return 1
	default:
		return 0
	}
}

func (impl *LenovoDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	zap.L().Debug("LenovoDspBroker.ParseResponse Enter")

	if resp.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &lenovo_broker_entity.LenovoResponse{}
	err = easyjson.Unmarshal(data, response)
	if err != nil {
		zap.L().Error("LenovoDspBroker.DecodeResponse json.Unmarshal,resp:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data)))), zap.Error(err))
		return nil, err_code.ErrBrokerResponseInternalFail
	}

	broadcastCandidate := broadcastCandidateList[0]
	impl.SampleDspBroadcastResponse(impl.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, data)

	if request.IsDebug {
		zap.L().Info("LenovoDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data)))))
	}

	if response.Code != 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	if len(response.Data.Ads) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)
	for _, resBid := range response.Data.Ads {
		candidateAd := &entity.Ad{
			DspId:      impl.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		candidateAd.AppInfo = impl.ParseAppInfo(resBid)

		monitor := impl.ParseTrackingData(request, resBid)
		if monitor == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidateCreative := impl.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(impl.chargePriceEncoder)
		candidate.SetBidPrice(uint32(resBid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(resBid.Material.AdID)
		candidate.SetDspProtocol(impl.GetDspProtocol())
		result = append(result, candidate)
		break

	}

	return result, nil

}

func (impl *LenovoDspBroker) chargePriceEncoder(chargePrice uint32) string {
	//明文替换
	result, err := impl.PriceManager.PlaintextEncode(uint64(chargePrice))
	//result, err := impl.PriceManager.GetDspCoder(impl.dspProtocol).EncodeWithKey(uint64(chargePrice), impl.iKey, impl.eKey)
	if err != nil {
		return ""
	}

	return result
}

func (impl *LenovoDspBroker) ParseAppInfo(bid lenovo_broker_entity.LenovoResponseAd) *entity.AppInfo {
	resMaterial := bid.Material

	appInfo := &entity.AppInfo{
		PackageName: resMaterial.AppPackage,
		AppName:     resMaterial.AppName,
		Icon:        resMaterial.IconURL,
		WechatExt:   nil,
		AppID:       "",
		AppVersion:  resMaterial.AppVersion,
		PackageSize: int(resMaterial.AppSize),
		Privacy:     resMaterial.PrivacyURL,
		Permission:  resMaterial.PermissionsURL,
		AppDesc:     "",
		AppDescURL:  resMaterial.AppInfoURL,
		Develop:     resMaterial.DevName,
		AppBeian:    "",
		AppAge:      "",
	}

	for _, perm := range resMaterial.AppPermissions {
		appInfo.PermissionDesc = append(appInfo.PermissionDesc, entity.PermissionDesc{
			PermissionLab:  perm.Title,
			PermissionDesc: perm.Description,
		})
	}

	return appInfo
}

func (impl *LenovoDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid lenovo_broker_entity.LenovoResponseAd) *entity.AdMonitorInfo {
	tracking := &entity.AdMonitorInfo{
		LandingUrl:                     bid.ClickURL,
		DeepLinkUrl:                    bid.DeepLinkURL,
		ImpressionMonitorList:          make([]string, 0),
		ClickMonitorList:               bid.ClickLinks,
		DeepLinkMonitorList:            bid.AwakenLinks,
		AppInstallStartMonitorList:     bid.InstallLinks,
		AppInstalledFinishMonitorList:  bid.InstalledLinks,
		AppDownloadStartedMonitorList:  bid.DownloadLinks,
		AppDownloadFinishedMonitorList: bid.DownloadedLinks,
		VideoStartUrlList:              bid.VideoPlayLinks,
		VideoCloseUrlList:              bid.VideoFinishLinks,
		DownloadUrl:                    bid.PackageURL,
		H5LandingUrl:                   bid.PackageURL,
		LandingAction:                  entity.LandingTypeInWebView,
	}

	if len(bid.NURL) > 0 {
		if strings.Contains(bid.NURL, impl.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(bid.NURL, impl.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.NURL)
		}
	}

	for _, impUrl := range bid.ImpressionLinks {

		if len(impUrl) > 0 {
			if strings.Contains(impUrl, impl.MacroWinPrice) {
				newImpTrack := strings.ReplaceAll(impUrl, impl.MacroWinPrice, "__DSPWPRICE__")
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
			} else {
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impUrl)
			}
		}
	}

	if bid.Material.InteractionType == 2 {
		tracking.LandingAction = entity.LandingTypeDownload
	} else if bid.Material.InteractionType == 3 {
		tracking.LandingAction = entity.LandingTypeDeepLink

	}

	return tracking
}

func (impl *LenovoDspBroker) ParseCreativeData(bid lenovo_broker_entity.LenovoResponseAd) *entity.Creative {
	bidMaterial := bid.Material

	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bidMaterial.AdID,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}

	if len(bidMaterial.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         bidMaterial.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bidMaterial.Description) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         bidMaterial.Description,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bidMaterial.ImgList) > 0 {
		for _, img := range bidMaterial.ImgList {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          img,
				Width:        bidMaterial.ImgWidth,
				Height:       bidMaterial.ImgHeight,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if len(bidMaterial.ImgURL) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          bidMaterial.ImgURL,
			Width:        bidMaterial.ImgWidth,
			Height:       bidMaterial.ImgHeight,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bidMaterial.IconURL) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bidMaterial.IconURL,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bidMaterial.VideoURL) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          bidMaterial.VideoURL,
			Duration:     float64(bidMaterial.VideoDuration),
			Width:        bidMaterial.ImgWidth,
			Height:       bidMaterial.ImgHeight,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}
