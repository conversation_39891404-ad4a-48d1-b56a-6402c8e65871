package common_json_v2_dsp_broker

import (
	"bytes"
	"fmt"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/common_v2_json_broker/common_v2_json_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"io"
	"net/http"
)

type CommonJsonV2nDspBroker struct {
	dsp_broker.DspBrokerBase

	dspSlotRegister *dsp_slot_register.SimpleDspSlotRegister
	bidUrl          string
	dspId           utils.ID

	dspProtocol   string
	iKey          string
	eKey          string
	MacroWinPrice string
}

func NewCommonJsonV2nDspBroker(dspId utils.ID) *CommonJsonV2nDspBroker {
	return &CommonJsonV2nDspBroker{
		dspId:           dspId,
		dspSlotRegister: dsp_slot_register.NewSimpleDspSlotRegister(dspId),
	}
}

func (b *CommonJsonV2nDspBroker) GetDspId() utils.ID {
	return b.dspId
}

func (b *CommonJsonV2nDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.dspSlotRegister
}

func (b *CommonJsonV2nDspBroker) UpdateDspInfo(dsp *entity.Dsp) error {
	b.bidUrl = dsp.BidUrl
	b.iKey = dsp.Ikey
	b.eKey = dsp.Ekey
	b.dspProtocol = dsp.Protocol
	return nil
}

func (b *CommonJsonV2nDspBroker) BuildRequest(adRequest *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := b.dspSlotRegister.GetDspSlotById(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound.Wrap(fmt.Errorf("slot:%s", trafficData.GetDspSlotId()))
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(dspSlot.GetDspSlotIdByTrafficContext(trafficData))

	brokerRequest := common_v2_json_broker_entity.CommonV2JsonRequest{
		Id:         adRequest.GetRequestId(),
		DeviceType: b.mappingDeviceType(trafficData.GetDeviceType()),
		Imp: common_v2_json_broker_entity.CommonV2JsonImp{
			Id:     adRequest.GetRequestId(),
			SlotId: dspSlot.GetDspSlotIdByTrafficContext(trafficData),
		},
		Ref: adRequest.Device.Referer,
		Device: common_v2_json_broker_entity.CommonV2JsonDevice{
			Ip:           trafficData.GetRequestIp(),
			Ua:           trafficData.GetUserAgent(),
			Os:           b.mappingOsType(trafficData.GetOsType()),
			Oaid:         trafficData.GetOaid(),
			OaidMd5:      trafficData.GetMd5Oaid(),
			Imei:         trafficData.GetImei(),
			ImeiMd5:      trafficData.GetMd5Imei(),
			AndroidId:    trafficData.GetAndroidId(),
			AndroidIdMd5: trafficData.GetMd5AndroidId(),
			OpenUDID:     trafficData.GetOpenUdid(),
			Idfa:         trafficData.GetIdfa(),
			IdfaMd5:      trafficData.GetMd5Idfa(),
			Mac:          trafficData.GetMac(),
			MacMd5:       trafficData.GetMd5Mac(),
			Vendor:       trafficData.GetBrand(),
			Model:        trafficData.GetModel(),
			Osv:          trafficData.GetOsVersion(),
			Net:          b.mappingConnectionType(trafficData.GetConnectionType()),
			Operator:     b.mappingOperator(trafficData.GetOperatorType()),
			Width:        int(trafficData.GetScreenWidth()),
			Height:       int(trafficData.GetScreenHeight()),
			Lat:          trafficData.GetGeoLatitude(),
			Lng:          trafficData.GetGeoLongitude(),
			GeoType:      0,
			Caid:         trafficData.GetCaid(),
			BootMark:     trafficData.GetBootMark(),
			UpdateMark:   trafficData.GetUpdateMark(),
		},
	}

	data, err := easyjson.Marshal(brokerRequest)
	if err != nil {
		return nil, err
	}

	url := b.bidUrl

	request, err := http.NewRequest(http.MethodPost, url, bytes.NewBuffer(data))
	if err != nil {
		return nil, err
	}
	request.Header["Content-Type"] = []string{"application/json"}

	if adRequest.IsDebug {
		zap.L().Info("CommonJsonV2nDspBroker url:, send", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", b.bidUrl)))), zap.String("param2", fmt.Sprintf("%v", string(data))))
	}

	b.SampleDspBroadcastRequest(b.dspId, dspSlot.Id, candidate, data)

	return request, nil
}

func (b *CommonJsonV2nDspBroker) ParseResponse(adRequest *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	if adRequest.IsDebug {
		zap.L().Info("CommonJsonV2nDspBroker response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(data))))))
	}

	response := common_v2_json_broker_entity.CommonV2JsonResponse{}
	if err := easyjson.Unmarshal(data, &response); err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	broadcastCandidate := broadcastCandidateList[0]
	b.SampleDspBroadcastResponse(b.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, data)

	candidateList := make(ad_service.DspAdCandidateList, 0, len(response.Bid))

	for _, ad := range response.Bid {
		adMonitorInfo := &entity.AdMonitorInfo{
			LandingUrl:            ad.Landing,
			DeepLinkUrl:           ad.DpUrl,
			ClickMonitorList:      ad.ClickTracking,
			ImpressionMonitorList: ad.ImpTracking,
			DeepLinkMonitorList:   ad.DpTracking,
		}

		for _, delay := range ad.DelayTracking {
			adMonitorInfo.AddDelayMonitorUrl(delay.DelayTime, delay.Url)
		}

		creative := &entity.Creative{}
		for _, assets := range ad.Native.Assets {
			switch assets.Type {
			case 1:
				material := &entity.Material{
					MaterialType: entity.MaterialTypeVideo,
					Url:          assets.Video.Url,
					Width:        int32(assets.Video.W),
					Height:       int32(assets.Video.H),
					Duration:     float64(assets.Video.Duration),
				}
				creative.AddMaterial(material)
			case 2:
				material := &entity.Material{
					MaterialType: entity.MaterialTypeImage,
					Url:          assets.Image.Url,
					Width:        int32(assets.Image.W),
					Height:       int32(assets.Image.H),
				}
				creative.AddMaterial(material)
			case 3:
				material := &entity.Material{
					MaterialType: entity.MaterialTypeTitle,
					Data:         assets.Text,
				}
				creative.AddMaterial(material)
			case 4:
				material := &entity.Material{
					MaterialType: entity.MaterialTypeDesc,
					Data:         assets.Text,
				}
				creative.AddMaterial(material)
			case 5:
				material := &entity.Material{
					MaterialType: entity.MaterialTypeIcon,
					Url:          assets.Image.Url,
					Width:        int32(assets.Image.W),
					Height:       int32(assets.Image.H),
				}
				creative.AddMaterial(material)
			}
		}

		candidateAd := &entity.Ad{
			AdMonitorInfo: adMonitorInfo,
			DspId:         b.GetDspId(),
			DspSlotId:     broadcastCandidate.GetDspSlotId(),
			DspSlotKey:    broadcastCandidate.GetDspSlotKey(),
		}
		candidateAd.SetCreative(creative)

		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetBidPrice(uint32(ad.Price))
		candidate.SetDspProtocol(b.GetDspProtocol())
		candidateList = append(candidateList, candidate)
	}

	return candidateList, nil
}

func (b *CommonJsonV2nDspBroker) mappingOsType(os entity.OsType) int {
	switch os {
	case entity.OsTypeAndroid:
		return 1
	case entity.OsTypeIOS:
		return 2
	default:
		return 0
	}
}

func (b *CommonJsonV2nDspBroker) mappingConnectionType(v entity.ConnectionType) int {
	switch v {
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (b *CommonJsonV2nDspBroker) mappingOperator(v entity.OperatorType) int {
	switch v {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 2
	case entity.OperatorTypeChinaTelecom:
		return 3
	default:
		return 0
	}
}

func (b *CommonJsonV2nDspBroker) mappingDeviceType(v entity.DeviceType) int {
	switch v {
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypePc:
		return 3
	case entity.DeviceTypeOtt:
		return 4
	default:
		return 1
	}
}
