package admate_dsp_broker

import (
	"errors"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/admate_traffic_broker/admate_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"io"
	"net/http"
	"strconv"
	"strings"
)

type AdMateDspBroker struct {
	dsp_broker.DspBrokerBase
	slotRegister *AdMateDspSlotRegister
	log          *zap.Logger
	macroInfo    macro_builder.MonitorMacroInfo
}

func NewAdMateDspBroker(dspId utils.ID) *AdMateDspBroker {
	return &AdMateDspBroker{
		DspBrokerBase: dsp_broker.DspBrokerBase{
			DspId: dspId,
		},
		slotRegister: NewAdMateDspSlotRegister(dspId),
		log:          zap.L().With(zap.String("broker", "AdMateDspBroker")),
		macroInfo: macro_builder.MonitorMacroInfo{
			MacroWinPrice:   "%%PRICE%%",  //价格宏（RTB加密后的竞价成功的成交价）。曝光监控中
			MacroClickDownX: "__DOWN_X__", //点击监测宏（用户手指按下时的横坐标）。落地页、点击监测
			MacroClickDownY: "__DOWN_Y__", //点击监测宏（用户手指按下时的纵坐标）。落地页、点击监测
			MacroClickUpX:   "__UP_X__",   //点击监测宏（用户手指离开设备屏幕时的横坐标）。落地页、点击监测
			MacroClickUpY:   "__UP_Y__",   //点击监测宏（用户手指离开设备屏幕时的纵坐标）。落地页、点击监测
		},
	}
}

func (a *AdMateDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := a.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}
	//对方dspSlotId
	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	// 广告位宽高
	slotWidth, slotHeight := uint32(dspSlot.Width), uint32(dspSlot.Height)
	if slotWidth == 0 || slotHeight == 0 {
		if len(request.SlotSize) > 0 {
			slotWidth, slotHeight = uint32(request.SlotSize[0].Width), uint32(request.SlotSize[0].Height)
		} else {
			slotWidth, slotHeight = trafficData.GetSlotWidth(), trafficData.GetSlotHeight()
		}
	}
	imp := &admate_broker_entity.Imp{
		Id:       request.ImpressionId,
		Type:     dspSlot.AdType,
		Pid:      slotId,
		BidType:  0, //暂时只支持cpm
		BidFloor: uint32(candidate.GetBidFloor().Price),
	}

	// 广告类型. 0:横幅,1:开屏,3:插屏,4:视频贴片,5:原生,7:激励视频
	switch toSlotType(dspSlot.AdType) {
	case entity.SlotTypeBanner: //横幅广告
		imp.Banner = &admate_broker_entity.Banner{
			Id:    1,
			W:     slotWidth,
			H:     slotHeight,
			Mimes: []string{"jpg", "jpeg", "png", "gif"},
		}
	case entity.SlotTypeVideo, entity.SlotTypeRewardVideo: //视频广告
		imp.Video = &admate_broker_entity.Video{
			Mimes:       []string{"mp4"},
			W:           slotWidth,
			H:           slotHeight,
			MinDuration: request.VideoMinDuration,
			MaxDuration: request.VideoMaxDuration,
		}
	case entity.SlotTypeFeeds: //信息流广告。当前映射为原生广告（native）
		for _, templateId := range dspSlot.innerTemplateIds {
			var asset *admate_broker_entity.Asset
			/*
				1-1（一图一文），1-8（两图一文），1-4（三图一文）
				1-2（图文摘要）
				2-2（原生信息流视频）
				2-3（音频贴片）
				2-4（激励视频）
			*/
			switch templateId {
			case "1-1", "1-2", "1-4", "1-8":
				asset = &admate_broker_entity.Asset{
					Id: templateId,
					W:  slotWidth,
					H:  slotHeight,
					Ext: &admate_broker_entity.AssetExt{
						Image: &admate_broker_entity.AssetImage{
							Mimes: []string{"jpg", "jpeg", "png", "gif"},
							W:     slotWidth,
							H:     slotHeight,
						},
					},
				}
			case "2-2", "2-3", "2-4":
				asset = &admate_broker_entity.Asset{
					Id: templateId,
					W:  slotWidth,
					H:  slotHeight,
					Ext: &admate_broker_entity.AssetExt{
						Video: &admate_broker_entity.Video{
							Mimes: []string{"mp4"},
							W:     slotWidth,
							H:     slotHeight,
						},
					},
				}
			}
			if asset != nil {
				imp.NativeAd.Assets = append(imp.NativeAd.Assets, asset)
				imp.NativeAd.TemplateIds = append(imp.NativeAd.TemplateIds, asset.Id)
			}
		}

		// 如果事先库中配置的template跟广告位类型不匹配；或者配置的templateId错误，这里会报错
		if imp.NativeAd == nil || len(imp.NativeAd.TemplateIds) < 1 {
			err := errors.New("invalid templateId or adType")
			a.log.WithField("slotRegister", a.slotRegister).WithField("req", request).Error(err.Error())
			return nil, err_code.ErrDspSlotNotFound.Wrap(err)
		}
	}

	deviceId, _ := trafficData.GetDeviceIdWithType()
	admateRequest := &admate_broker_entity.BidRequest{
		Id:  request.GetRequestId(),
		Imp: []*admate_broker_entity.Imp{imp},
		App: &admate_broker_entity.App{
			Id:     request.GetRequestId(),
			Name:   trafficData.GetAppName(),
			Bundle: trafficData.GetAppBundle(),
			Ver:    trafficData.GetAppVersion(),
		},
		Device: &admate_broker_entity.Device{
			Ua:             trafficData.GetUserAgent(),
			ConnectionType: mappingConnectionType(trafficData.GetConnectionType()),
			DeviceType:     mappingDeviceType(trafficData.GetDeviceType()),
			Imsi:           request.Device.Imsi,
			AndroidId:      trafficData.GetAndroidId(),
			Imei:           trafficData.GetImei(),
			Oaid:           trafficData.GetOaid(),
			OaidMd5:        trafficData.GetMd5Oaid(),
			Mac:            trafficData.GetMac(),
			Idfa:           trafficData.GetIdfa(),
			Caid:           trafficData.GetCaid(),
			Make:           request.Device.Brand,
			Model:          trafficData.GetModel(),
			AppstoreVer:    request.Device.AppStoreVersion,
			HmsCore:        request.Device.VercodeHms,
			BootMark:       trafficData.GetBootMark(),
			UpdateMark:     trafficData.GetUpdateMark(),
			BootTime:       trafficData.GetDeviceStartupTime(),
			UpdateTime:     trafficData.GetDeviceUpgradeTime(),
			BirthTime:      trafficData.GetDeviceInitTime(),
			Lan:            trafficData.GetLanguage(),
			Os:             mappingDeviceOs(trafficData.GetOsType()),
			Osv:            trafficData.GetOsVersion(),
			H:              strconv.FormatInt(int64(trafficData.GetScreenHeight()), 10),
			W:              strconv.FormatInt(int64(trafficData.GetScreenWidth()), 10),
			Ppi:            strconv.FormatInt(int64(request.Device.PPI), 10),
			Orientation:    mappingOrientation(trafficData.GetScreenOrientation()),
			Geo: &admate_broker_entity.Geo{
				Type: request.Device.GeoStandard,
				Lat:  trafficData.GetGeoLatitude(),
				Lon:  trafficData.GetGeoLongitude(),
			},
		},
		Site: &admate_broker_entity.Site{
			Page: request.Url,
			Ref:  trafficData.GetReferer(),
		},
		User: &admate_broker_entity.User{
			Id:     deviceId,
			Gender: mappingGender(request.UserGender),
		},
		Tmax:   request.TMax,
		Secure: 0,
	}
	if request.Device.IsIp6 {
		admateRequest.Device.Ipv6 = trafficData.GetRequestIp()
	} else {
		admateRequest.Device.Ip = trafficData.GetRequestIp()
	}

	httpRequest, _, err := a.BuildSonicJsonHttpRequest(admateRequest)
	if err != nil {
		a.log.WithError(err).Error("BuildSonicJsonHttpRequest error")
		return nil, err_code.ErrBrokerRequest.Wrap(err)
	}

	a.SampleDspBroadcastSonicJsonRequest(a.DspId, trafficData.GetDspSlotId(), candidate, admateRequest)
	if request.IsDebug {
		payload, _ := sonic.Marshal(admateRequest)
		a.log.WithField("request", string(payload)).Info("BuildRequest debug")
	}
	return httpRequest, nil
}

func mappingOrientation(orientation entity.ScreenOrientationType) string {
	switch orientation {
	case entity.ScreenOrientationTypePortrait:
		return "0"
	case entity.ScreenOrientationTypeLandscape:
		return "1"
	default:
		return "-1"
	}
}

func mappingDeviceOs(osType entity.OsType) string {
	switch osType {
	case entity.OsTypeIOS:
		return "iOS"
	case entity.OsTypeAndroid:
		return "Android"
	case entity.OsTypeWindowsPhone:
		return "WP"
	default:
		return "Others"
	}
}

func mappingConnectionType(connectionType entity.ConnectionType) int {
	switch connectionType {
	case entity.ConnectionTypeNetEthernet:
		return 1
	case entity.ConnectionTypeWifi:
		return 2
	case entity.ConnectionType2G:
		return 4
	case entity.ConnectionType3G:
		return 5
	case entity.ConnectionType4G:
		return 6
	case entity.ConnectionType5G:
		return 7
	default:
		return 0
	}
}

func mappingDeviceType(dy entity.DeviceType) int {
	switch dy {
	case entity.DeviceTypeMobile:
		return 2
	case entity.DeviceTypePad:
		return 3
	default:
		return 0
	}
}

func mappingGender(gender entity.UserGenderType) string {
	switch gender {
	case entity.UserGenderMan:
		return "F"
	case entity.UserGenderWoman:
		return "M"
	default:
		return "U"
	}
}

func toSlotType(t int) entity.SlotType {
	switch t {
	case 0: //横幅
		return entity.SlotTypeBanner
	case 1: //开屏
		return entity.SlotTypeOpening
	case 3: //插屏
		return entity.SlotTypePopup
	case 4: //视频贴片
		return entity.SlotTypeVideo
	case 5: //原生广告
		return entity.SlotTypeFeeds
	case 7: //激励视频
		return entity.SlotTypeRewardVideo
	default: //默认都视为信息流
		return entity.SlotTypeUnknown
	}
}

func (a *AdMateDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error) {
	if response.StatusCode != 200 {
		return nil, err_code.ErrBroadcastNoBidding
	}
	data, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse
	}
	resp := new(admate_broker_entity.BidResponse)
	payload, err := a.ParseSonicJsonHttpResponse(response, data, resp)
	if err != nil {
		a.log.WithError(err).WithField("response", response).Error("ParseSonicJsonHttpResponse error")
		return nil, err_code.ErrBrokerParseError.Wrap(err)
	}

	broadcastCandidate := broadcastCandidateList[0]
	a.SampleDspBroadcastResponse(a.DspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, response.StatusCode, payload)
	if request.IsDebug {
		a.log.WithField("resp", string(payload)).Info("ParseResponse debug")
	}

	if len(resp.Seatbid) < 1 || len(resp.Seatbid[0].Bid) < 1 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0, 1)
	for _, bid := range resp.Seatbid[0].Bid {
		ad := &entity.Ad{
			DspId:      a.DspId,
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
			AppInfo:    &entity.AppInfo{},
		}
		adMonitorInfo, creative := a.parseCallbacksAndCreative(bid)
		ad.AdMonitorInfo = adMonitorInfo
		if creative == nil {
			a.log.WithError(err_code.ErrBrokerResponseInternalFail).WithField("bidResponse", bid).Error("creative is nil")
			return nil, err_code.ErrBrokerResponseInternalFail
		}

		candidate := ad_service.NewDspAdCandidateWithPool(ad)
		candidate.SetAdCandidateChargePriceEncoder(a.chargePriceEncoder)
		candidate.SetBidPrice(uint32(bid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(creative)
		candidate.SetDspProtocol(a.DspProtocol)

		result = append(result, candidate)
	}

	return result, nil
}

func (a *AdMateDspBroker) chargePriceEncoder(chargePrice uint32) string {
	result, err := a.PriceManager.GetDspCoder(a.DspProtocol).EncodeWithKey(uint64(chargePrice), a.GetIKey(), a.GetEKey())
	if err != nil {
		a.log.WithError(err).WithField("price", chargePrice).Errorf("chargePriceEncoder error")
		return ""
	}

	return result
}

func (a *AdMateDspBroker) parseCallbacksAndCreative(data *admate_broker_entity.ResBid) (*entity.AdMonitorInfo, *entity.Creative) {
	info := &entity.AdMonitorInfo{
		LandingUrl:                    data.DURL,
		DeepLinkUrl:                   data.DeepLink,
		ImpressionMonitorList:         data.NURL,
		ClickMonitorList:              data.CURL,
		AppInstallStartMonitorList:    data.DNInstStart,
		AppInstalledFinishMonitorList: data.DNInstSucc,
		AppOpenMonitorList:            data.DNActive,
		LandingAction:                 mappingLandingType(data),
	}
	if len(data.DNActive) > 0 {
		info.ActionCallbackUrl = data.DNActive[0]
	}
	// 落地页是应用下载类型
	if data.ADCK == 2 {
		info.DownloadUrl = data.DURL
	} else { //如果是网页类型
		info.H5LandingUrl = data.DURL
	}
	if len(info.DeepLinkUrl) > 0 {
		if len(data.DNStart) > 0 {
			info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, data.DNStart...)
		}
		if len(data.DNSucc) > 0 {
			info.DeepLinkMonitorList = append(info.DeepLinkMonitorList, data.DNSucc...)
		}
	}
	// 如果是下载类广告，需要填写下载类型的回调参数
	if len(data.AppName) > 0 {
		info.AppInfo = entity.AppInfo{
			PackageName: data.PackageName,
			AppName:     data.AppName,
			Icon:        data.AppIcon,
			AppID:       data.AppStoreID,
			AppVersion:  data.AppVersion,
			Privacy:     data.PrivacyAgreement,
			Permission:  data.AppPermissionURL,
			AppDescURL:  data.AppIntroURL,
			Develop:     data.Developer,
		}
		if len(data.AppSize) > 0 {
			size, err := strconv.ParseUint(data.AppSize, 10, 64)
			if err != nil {
				a.log.WithError(err).WithField("app_size", data.AppSize).Error("parse app_size error")
			} else {
				// 单位KB
				info.AppInfo.PackageSize = int(size)
			}
		}
		if len(data.WxUsername) > 0 {
			info.AppInfo.WechatExt = &entity.WechatExt{
				ProgramId:   data.WxUsername,
				ProgramPath: data.WxPath,
			}
		}
	}

	// 解析广告主id
	if len(data.AderID) > 0 {
		parseUint, err := strconv.ParseUint(data.AderID, 10, 64)
		if err != nil {
			a.log.WithError(err).WithField("ader_id", data.AderID).Error("parse aderId error")
		} else {
			info.AdvertiserId = utils.ID(parseUint)
		}
	}

	//宏替换
	if len(info.H5LandingUrl) > 0 {
		info.H5LandingUrl = macro_builder.MacroReplace(info.H5LandingUrl, a.macroInfo)
	}
	if len(info.LandingUrl) > 0 {
		info.LandingUrl = macro_builder.MacroReplace(info.LandingUrl, a.macroInfo)
	}
	info.ImpressionMonitorList = macro_builder.MacroReplaceList(info.ImpressionMonitorList, a.macroInfo)
	info.ClickMonitorList = macro_builder.MacroReplaceList(info.ClickMonitorList, a.macroInfo)

	// 解析adm
	var creative *entity.Creative
	if len(data.ADM) > 0 {
		if len(data.TemplateID) > 0 {
			template := &admate_broker_entity.Template{}
			err := sonic.Unmarshal([]byte(data.ADM), template)
			if err != nil {
				a.log.WithError(err).WithField("adm", data.ADM).Error("parse adm error")
			} else {
				if template.VideoDuration > 0 {
					info.VideoStartUrlList = append(info.VideoStartUrlList, template.VideoUrl)
				}
				creative = parseCreative(data, template)
			}
		} else {
			creative = &entity.Creative{
				CreativeKey:  data.CID,
				MaterialList: make(entity.MaterialList, 0),
			}
			switch a.slotRegister.GetSlotInfo(a.DspId).AdType {
			case 4, 7: //视频
				material := &entity.Material{
					MaterialType: entity.MaterialTypeVideo,
					Url:          data.ADM,
					Duration:     float64(data.VideoDuration),
					Height:       data.H,
					Width:        data.W,
				}
				creative.MaterialList = append(creative.MaterialList, material)
			default:
				// 获取素材的后缀
				if strings.Contains(data.ADM, ".mp4") {
					creative.MaterialList = append(creative.MaterialList, &entity.Material{
						MaterialType: entity.MaterialTypeVideo,
						Duration:     float64(data.VideoDuration),
						Url:          data.ADM,
						Height:       data.H,
						Width:        data.W,
					})
				} else {
					creative.MaterialList = append(creative.MaterialList, &entity.Material{
						MaterialType: entity.MaterialTypeImage,
						Url:          data.ADM,
						Height:       data.H,
						Width:        data.W,
					})
				}
			}
		}
	}

	return info, creative
}

// 解析创意模板
func parseCreative(bid *admate_broker_entity.ResBid, template *admate_broker_entity.Template) *entity.Creative {
	creative := &entity.Creative{
		CreativeKey:  bid.CID,
		MaterialList: make(entity.MaterialList, 0),
	}

	if len(template.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         template.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}
	if len(template.Icon) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          template.Icon,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}
	if len(template.ActionText) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         template.ActionText,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}
	if len(template.ImgUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeImage,
			Url:          template.ImgUrl,
			Height:       bid.H,
			Width:        bid.W,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}
	if len(template.Cover) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeCoverImage,
			Url:          template.Cover,
			Height:       bid.H,
			Width:        bid.W,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}
	if len(template.EndCover) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeCoverImage,
			Url:          template.EndCover,
			Height:       bid.H,
			Width:        bid.W,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}
	if len(template.VideoUrl) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          template.VideoUrl,
			Duration:     float64(template.VideoDuration),
			Height:       bid.H,
			Width:        bid.W,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}

// 映射落地页类型
func mappingLandingType(bid *admate_broker_entity.ResBid) entity.LandingType {
	if len(bid.DeepLink) > 0 {
		return entity.LandingTypeDeepLink
	}
	if len(bid.WxUsername) > 0 {
		return entity.LandingTypeWeChatProgram
	}
	if bid.ADCK == 2 {
		return entity.LandingTypeDownload
	}
	return entity.LandingTypeInWebView
}

func (a *AdMateDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return a.slotRegister
}
