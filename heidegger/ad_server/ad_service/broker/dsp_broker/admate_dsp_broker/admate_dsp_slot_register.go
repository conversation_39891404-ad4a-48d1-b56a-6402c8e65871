package admate_dsp_broker

import (
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"strings"
)

type AdMateDspSlotInfo struct {
	*entity.DspSlotInfo
	// AdType 广告类型. 0:横幅,1:开屏,3:插屏,4:视频贴片,5:原生,7:激励视频
	AdType int `json:"ad_type"`
	/*
		TemplateIds 多个广告位模板。多个用英文逗号分隔
		1-1（一图一文），1-8（两图一文），1-4（三图一文）
		1-2（图文摘要）
		2-2（原生信息流视频）
		2-3（音频贴片）
		2-4（激励视频）
	*/
	TemplateIds      string `json:"template_ids"`
	innerTemplateIds []string
	Height           int `json:"height"`
	Width            int `json:"width"`
}

var templateMap = map[string]struct{}{"1-1": {}, "1-8": {}, "1-4": {}, "1-2": {}, "2-2": {}, "2-3": {}, "2-4": {}}

func (info *AdMateDspSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) (err error) {
	info.DspSlotInfo = dspSlotInfo
	info.AdType, err = dspSlotInfo.ExtraData.GetInt("ad_type")
	if err != nil {
		return errors.Wrapf(err, "[AdMateDspSlotInfo] get ad_type failed, slotId:%d", dspSlotInfo.Id)
	}
	info.TemplateIds, _ = dspSlotInfo.ExtraData.GetString("template_ids")
	idMap := map[string]struct{}{}
	for _, templateId := range strings.Split(info.TemplateIds, ",") {
		templateId = strings.TrimSpace(templateId)
		if len(templateId) < 1 {
			continue
		}
		if _, ok := idMap[templateId]; !ok {
			if _, ok := templateMap[templateId]; !ok {
				continue
			}
			idMap[templateId] = struct{}{}
			info.innerTemplateIds = append(info.innerTemplateIds, templateId)
		}
	}
	info.Height, _ = dspSlotInfo.ExtraData.GetInt("height")
	info.Width, _ = dspSlotInfo.ExtraData.GetInt("width")
	return nil
}

type AdMateDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*AdMateDspSlotInfo
}

func NewAdMateDspSlotRegister(dspId utils.ID) *AdMateDspSlotRegister {
	return &AdMateDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*AdMateDspSlotInfo),
	}
}

func (r *AdMateDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *AdMateDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	dspSlotMap := make(map[utils.ID]*AdMateDspSlotInfo)
	for _, slotInfo := range list {
		slot := &AdMateDspSlotInfo{}
		if err := slot.Init(slotInfo); err != nil {
			zap.L().Error("[AdMateDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		dspSlotMap[slot.Id] = slot
	}

	r.dspSlotList = list
	r.dspSlotMap = dspSlotMap
	return nil
}

func (r *AdMateDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *AdMateDspSlotRegister) GetSlotInfo(slotId utils.ID) *AdMateDspSlotInfo {
	return r.dspSlotMap[slotId]
}
