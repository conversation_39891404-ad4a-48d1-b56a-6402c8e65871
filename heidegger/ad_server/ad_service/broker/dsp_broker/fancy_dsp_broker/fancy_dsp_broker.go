package fancy_dsp_broker

import (
	"encoding/json"
	"io"
	"net/http"
	"strings"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/fancy_dsp_broker/fancy_dsp_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"fmt"
)

type FancyDspBroker struct {
	dsp_broker.DspBrokerBase

	slotRegister *FancyDspSlotRegister
	dspId        utils.ID

	MacroWinPrice string
}

func NewFancyDspBroker(dspId utils.ID) *FancyDspBroker {
	return &FancyDspBroker{
		dspId:         dspId,
		slotRegister:  NewFancyDspSlotRegister(dspId),
		MacroWinPrice: "${AUCTION_PRICE}",
	}
}

func (b *FancyDspBroker) GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface {
	return b.slotRegister
}

func (b *FancyDspBroker) BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error) {
	if len(candidateList) != 1 {
		return nil, err_code.ErrTooManyCandidateForDsp
	}

	candidate := candidateList[0]
	trafficData := candidate.GetModifiedTrafficData()

	dspSlot := b.slotRegister.GetSlotInfo(trafficData.GetDspSlotId())
	if dspSlot == nil {
		return nil, err_code.ErrDspSlotNotFound
	}

	slotId := dspSlot.GetDspSlotIdByTrafficContext(trafficData)
	if len(slotId) < 1 {
		return nil, err_code.ErrDspSlotNotFound
	}

	candidate.SetDspSlotId(trafficData.GetDspSlotId())
	candidate.SetDspSlotKey(slotId)

	deviceId, _ := trafficData.GetDeviceIdWithType()

	fancyRequest := &fancy_dsp_entity.FancyJsonBrokerRequest{
		Id:  request.GetRequestId(),
		Imp: nil,
		App: &fancy_dsp_entity.App{
			Name:       trafficData.GetAppName(),
			Ver:        trafficData.GetAppVersion(),
			SdkVersion: trafficData.GetSdkVersion(),
			Bundle:     trafficData.GetAppBundle(),
		},
		Device: &fancy_dsp_entity.Device{
			Imei:           trafficData.GetImei(),
			ImeiMd5:        trafficData.GetMd5Imei(),
			Caid:           device_utils.GetCaidRaw(trafficData.GetCaid()),
			CaidVersion:    device_utils.GetCaidVersion(trafficData.GetCaid()),
			AliAaid:        trafficData.GetAaid(),
			Oaid:           trafficData.GetOaid(),
			OaidMd5:        trafficData.GetMd5Oaid(),
			Aid:            trafficData.GetAndroidId(),
			AidMd5:         trafficData.GetMd5AndroidId(),
			Idfa:           trafficData.GetIdfa(),
			IdfaMd5:        trafficData.GetMd5Idfa(),
			Os:             int32(trafficData.GetOsType()),
			Osv:            trafficData.GetOsVersion(),
			Make:           trafficData.GetBrand(),
			Model:          trafficData.GetModel(),
			DeviceType:     b.mappingDeviceType(trafficData.GetDeviceType()),
			Ip:             trafficData.GetRequestIp(),
			Mac:            trafficData.GetMac(),
			MacMd5:         trafficData.GetMd5Mac(),
			Ua:             trafficData.GetUserAgent(),
			Carrier:        b.mappingOperator(trafficData.GetOperatorType()),
			ConnectionType: b.mappingConnectionType(trafficData.GetConnectionType()),
			Sw:             trafficData.GetScreenWidth(),
			Sh:             trafficData.GetScreenHeight(),
			Den:            float32(trafficData.GetScreenDensity()),
			Ori:            int(trafficData.GetScreenOrientation()),
			Geo: &fancy_dsp_entity.Geo{
				Lat: float32(trafficData.GetGeoLatitude()),
				Lon: float32(trafficData.GetGeoLongitude()),
			},
			BootMark:     trafficData.GetBootMark(),
			UpdateMark:   trafficData.GetUpdateMark(),
			VerCodeOfHms: request.Device.VercodeHms,
			VerCodeOfAG:  request.Device.VercodeAg,
			Paid:         request.Device.Paid,
			BootTime:     trafficData.GetDeviceInitTime(),
			UpdateTime:   trafficData.GetDeviceUpgradeTime(),
			BirthTime:    trafficData.GetDeviceStartupTime(),
			Caids:        request.Device.Caids,
		},
		User: &fancy_dsp_entity.User{
			Id:     deviceId,
			Gender: "",
			Age:    request.UserAge,
		},
	}

	if len(dspSlot.AppName) > 0 {
		fancyRequest.App.Name = dspSlot.AppName
	}

	if len(dspSlot.PkgName) > 0 {
		fancyRequest.App.Bundle = dspSlot.PkgName
	}

	if len(dspSlot.AppVersion) > 0 {
		fancyRequest.App.Ver = dspSlot.AppVersion
	}

	bidFloor := candidate.GetBidFloor()
	imp := &fancy_dsp_entity.Imp{
		Id:          request.ImpressionId,
		W:           int32(trafficData.GetSlotWidth()),
		H:           int32(trafficData.GetSlotHeight()),
		MinDuration: request.VideoMinDuration,
		MaxDuration: request.VideoMaxDuration,
		/*
			1	通用单图文落地
			5	通用三图文
			6	通用一图一视频
			7	通用一图一icon
			9	通用三图一icon
			11	通用一图一文
		*/
		Formids:         []int32{1, 5, 6, 11},
		SlotId:          slotId,
		AdType:          b.mappingSlotType(trafficData.GetSlotType()),
		CType:           []int32{1, 2, 3, 4, 5, 6, 8, 11},
		BidFloor:        float32(bidFloor.Price),
		BidType:         0,
		Secure:          0,
		SupportInteract: []int{0, 1, 2, 3},
	}

	if len(dspSlot.TemplateIds) > 0 {
		imp.Formids = dspSlot.TemplateIds
	}

	if len(request.SlotSize) > 0 && (imp.W == 0 || imp.H == 0) {
		for _, size := range request.SlotSize {
			imp.W = int32(size.Width)
			imp.H = int32(size.Height)
			break
		}
	}
	if dspSlot.Width > 0 && dspSlot.Height > 0 {
		imp.W = int32(dspSlot.Width)
		imp.H = int32(dspSlot.Height)
	}

	fancyRequest.Imp = append(fancyRequest.Imp, imp)

	req, _, err := b.BuildEasyJsonHttpRequest(fancyRequest)
	if err != nil {
		zap.L().Error("FancyDspBroker http BuildJsonHttpRequest err", zap.Error(err))
		return nil, err
	}

	if request.IsDebug {
		reqbody, _ := json.Marshal(fancyRequest)
		zap.L().Info("FancyDspBroker.EncodeRequest end, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(reqbody))))))
	}

	b.SampleDspBroadcastEasyjsonRequest(b.dspId, dspSlot.Id, candidate, fancyRequest)

	return req, nil
}

func (b *FancyDspBroker) mappingSlotType(t entity.SlotType) int32 {
	switch t {
	case entity.SlotTypeOpening:
		return 6
	case entity.SlotTypeVideo:
		return 1
	case entity.SlotTypeBanner:
		return 5
	case entity.SlotTypePopup:
		return 7
	case entity.SlotTypeFeeds:
		return 8
	case entity.SlotTypeRewardVideo:
		return 12
	default:
		return 8
	}
}

func (b *FancyDspBroker) mappingDeviceType(deviceType entity.DeviceType) int32 {
	switch deviceType {
	case entity.DeviceTypePad:
		return 2
	case entity.DeviceTypeMobile:
		return 1
	case entity.DeviceTypePc:
		return 3
	case entity.DeviceTypeOtt:
		return 5
	default:
		return 0
	}
}

func (b *FancyDspBroker) mappingOperator(v entity.OperatorType) int32 {
	switch v {
	case entity.OperatorTypeChinaMobile:
		return 1
	case entity.OperatorTypeChinaUnicom:
		return 2
	case entity.OperatorTypeChinaTelecom:
		return 3
	case entity.OperatorTypeTietong:
		return 3
	default:
		return 0
	}
}

func (b *FancyDspBroker) mappingConnectionType(v entity.ConnectionType) int32 {
	switch v {
	case entity.ConnectionTypeNetEthernet:
		return 6
	case entity.ConnectionTypeWifi:
		return 1
	case entity.ConnectionTypeCellular:
		return 7
	case entity.ConnectionType2G:
		return 2
	case entity.ConnectionType3G:
		return 3
	case entity.ConnectionType4G:
		return 4
	case entity.ConnectionType5G:
		return 5
	default:
		return 0
	}
}

func (b *FancyDspBroker) chargePriceEncoder(chargePrice uint32) string {
	//明文替换
	result, err := b.PriceManager.PlaintextEncode(uint64(chargePrice))
	//result, err := impl.PriceManager.GetDspCoder(impl.dspProtocol).EncodeWithKey(uint64(chargePrice), impl.iKey, impl.eKey)
	if err != nil {
		return ""
	}

	return result
}

func (b *FancyDspBroker) ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, resp *http.Response) (ad_service.DspAdCandidateList, error) {
	if resp.StatusCode != 200 {
		if request.IsDebug {
			zap.L().Error("[FancyDspBroker] StatusCode", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(resp.StatusCode)))))
		}
		return nil, err_code.ErrBroadcastNoBidding
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	response := &fancy_dsp_entity.FancyJsonBidResponse{}
	resBody, err := b.ParseJsonHttpResponse(resp, data, response)
	if err != nil {
		zap.L().Debug("FancyDspBroker.DecodeResponse json.Unmarshal, err", zap.Error(err))
		return nil, err_code.ErrBrokerResponse.Wrap(err)
	}

	broadcastCandidate := broadcastCandidateList[0]
	b.SampleDspBroadcastResponse(b.dspId, broadcastCandidate.GetDspSlotId(), broadcastCandidate, resp.StatusCode, resBody)

	if request.IsDebug {
		resbody, _ := json.Marshal(response)
		zap.L().Info("FancyDspBroker raw reponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	if len(response.SeatBid) == 0 || len(response.SeatBid[0].Bid) == 0 {
		return nil, err_code.ErrBroadcastNoBidding
	}

	result := make(ad_service.DspAdCandidateList, 0)
	for _, resBid := range response.SeatBid[0].Bid {

		candidateAd := &entity.Ad{
			DspId:      b.GetDspId(),
			DspSlotId:  broadcastCandidate.GetDspSlotId(),
			DspSlotKey: broadcastCandidate.GetDspSlotKey(),
		}

		if resBid.Adm.App != nil {
			candidateAd.AppInfo = &entity.AppInfo{
				PackageName: resBid.Adm.App.PackageName,
				AppName:     resBid.Adm.App.Name,
				Icon:        resBid.Adm.App.Icon,
				AppID:       resBid.Adm.App.AppId,
				AppVersion:  resBid.Adm.App.AppVersion,
				PackageSize: resBid.Adm.App.PackageSize,
				Privacy:     resBid.Adm.App.Privacy,
				Permission:  resBid.Adm.App.Permission,
				AppDesc:     resBid.Adm.App.AppDesc,
				AppDescURL:  resBid.Adm.App.AppDescURL,
				Develop:     resBid.Adm.App.AppDeveloper,
			}

			if len(resBid.Adm.App.PermissionDesc) > 0 {
				for _, per := range resBid.Adm.App.PermissionDesc {
					candidateAd.AppInfo.PermissionDesc = append(candidateAd.AppInfo.PermissionDesc, entity.PermissionDesc{
						PermissionLab:  per.PermissionLab,
						PermissionDesc: per.PermissionDesc,
					})
				}
			}
			if resBid.Adm.App.WeChatExt != nil {
				candidateAd.AppInfo.WechatExt = &entity.WechatExt{
					ProgramId:   resBid.Adm.App.WeChatExt.ProgramId,
					ProgramPath: resBid.Adm.App.WeChatExt.ProgramPath,
				}
			}
		}

		candidateAd.AdMonitorInfo = b.ParseTrackingData(request, resBid)

		candidateCreative := b.ParseCreativeData(resBid)
		if candidateCreative == nil {
			return nil, err_code.ErrBrokerResponseInternalFail
		}
		candidate := ad_service.NewDspAdCandidateWithPool(candidateAd)
		candidate.SetAdCandidateChargePriceEncoder(b.chargePriceEncoder)
		candidate.SetBidPrice(uint32(resBid.Price))
		candidate.SetBidType(entity.BidTypeCpm)
		candidate.SetCreative(candidateCreative)
		candidate.SetDspAdID(resBid.Crid)
		candidate.SetDspProtocol(b.GetDspProtocol())
		result = append(result, candidate)
		break
	}

	return result, nil
}

func (b *FancyDspBroker) ParseCreativeData(bid fancy_dsp_entity.FancyJsonBrokerResponseSeatBidBid) *entity.Creative {
	creative := &entity.Creative{
		Id:                 0,
		Name:               "",
		CreativeKey:        bid.Crid,
		CreativeTemplateId: 0,
		AdvertiserId:       0,
		ProductId:          0,
		MaterialIdList:     nil,
		CreativeTag:        nil,
		MaterialList:       make(entity.MaterialList, 0),
	}
	item := bid.Adm

	if len(item.Title) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         item.Title,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeTitle,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(item.Desc) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         item.Desc,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	} else {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeDesc,
			Data:         "点击查看详情",
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	if len(bid.Adm.Ext.Icon) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeIcon,
			Url:          bid.Adm.Ext.Icon,
			Width:        100,
			Height:       100,
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	for _, image := range item.Img {
		if len(image.Url) > 0 {
			material := &entity.Material{
				MaterialType: entity.MaterialTypeImage,
				Url:          image.Url,
				Width:        image.W,
				Height:       image.H,
			}
			creative.MaterialList = append(creative.MaterialList, material)
		}
	}

	if item.Video != nil && len(item.Video.Url) > 0 {
		material := &entity.Material{
			MaterialType: entity.MaterialTypeVideo,
			Url:          item.Video.Url,
			Width:        item.Video.W,
			Height:       item.Video.H,
			Duration:     float64(item.Video.Duration),
		}
		creative.MaterialList = append(creative.MaterialList, material)
	}

	return creative
}

func (b *FancyDspBroker) ParseTrackingData(request *ad_service.AdRequest, bid fancy_dsp_entity.FancyJsonBrokerResponseSeatBidBid) *entity.AdMonitorInfo {

	tracking := &entity.AdMonitorInfo{
		LandingUrl:            bid.Adm.Land,
		ImpressionMonitorList: make([]string, 0),
		ClickMonitorList:      bid.ClickTrackers,
		LandingAction:         entity.LandingTypeInWebView,
		DeepLinkMonitorList:   bid.DpTrackers,
		DeepLinkUrl:           bid.Adm.DpLink,
	}

	if bid.Adm.Interact == 1 {
		tracking.LandingAction = entity.LandingTypeDownload
	} else if bid.Adm.Interact == 3 {
		tracking.LandingAction = entity.LandingTypeDeepLink
	} else if bid.Adm.Interact == 2 {
		tracking.LandingAction = entity.LandingTypeWeChatProgram
	}

	if len(bid.ImpTrackers) > 0 {
		for _, impTrack := range bid.ImpTrackers {
			if strings.Contains(impTrack, b.MacroWinPrice) {
				newImpTrack := strings.ReplaceAll(impTrack, b.MacroWinPrice, "__DSPWPRICE__")
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
			} else {
				tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, impTrack)
			}
		}
	}

	if len(bid.NUrl) > 0 {
		if strings.Contains(bid.NUrl, b.MacroWinPrice) {
			newImpTrack := strings.ReplaceAll(bid.NUrl, b.MacroWinPrice, "__DSPWPRICE__")
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, newImpTrack)
		} else {
			tracking.ImpressionMonitorList = append(tracking.ImpressionMonitorList, bid.NUrl)
		}
	}

	if len(bid.DownStartTrackers) > 0 {
		tracking.AppDownloadStartedMonitorList = bid.DownStartTrackers
	}

	if len(bid.DownCompTrackers) > 0 {
		tracking.AppDownloadFinishedMonitorList = bid.DownCompTrackers
	}

	if len(bid.InstallStartTrackers) > 0 {
		tracking.AppInstallStartMonitorList = bid.InstallStartTrackers
	}

	if len(bid.InstallCompTrackers) > 0 {
		tracking.AppInstalledFinishMonitorList = bid.InstallCompTrackers
	}

	for _, v := range bid.VideoTrackers {
		tracking.DelayMonitorUrlList = append(tracking.DelayMonitorUrlList, entity.AdDelayMonitor{
			Url:   v.Url,
			Delay: v.Event,
		})
	}

	return tracking

}
