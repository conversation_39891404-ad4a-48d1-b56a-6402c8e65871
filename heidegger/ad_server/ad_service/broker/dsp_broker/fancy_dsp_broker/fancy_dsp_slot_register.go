package fancy_dsp_broker

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"strings"
)

type FancySlotSlotInfo struct {
	*entity.DspSlotInfo
	TemplateIds []int32 `json:"template_id"`
	Height      int     `json:"height"`
	Width       int     `json:"width"`
	AppVersion  string  `json:"app_version"`
	PkgName     string  `json:"pkg_name"`
	AppName     string  `json:"app_name"`
}

func (info *FancySlotSlotInfo) Init(dspSlotInfo *entity.DspSlotInfo) error {
	info.DspSlotInfo = dspSlotInfo

	var err error

	templateId, err := dspSlotInfo.ExtraData.GetString("template_id")
	if err != nil {
	}

	templateIds := strings.Split(templateId, ",")
	for _, item := range templateIds {
		if item != "" {
			info.TemplateIds = append(info.TemplateIds, type_convert.GetAssertInt32(item))
		}
	}

	info.Height, err = dspSlotInfo.ExtraData.GetInt("height")
	//if err != nil {
	//	return fmt.Errorf("get height from extra_data failed, err: %v", err)
	//}

	info.Width, err = dspSlotInfo.ExtraData.GetInt("width")
	//if err != nil {
	//	return fmt.Errorf("get width from extra_data failed, err: %v", err)
	//}

	info.AppVersion, err = dspSlotInfo.ExtraData.GetString("app_version")
	if err != nil {
	}

	info.PkgName, err = dspSlotInfo.ExtraData.GetString("pkg_name")
	if err != nil {
	}

	info.AppName, err = dspSlotInfo.ExtraData.GetString("app_name")
	if err != nil {
	}

	return nil
}

type FancyDspSlotRegister struct {
	dspId       utils.ID
	dspSlotList entity.DspSlotInfoList
	dspSlotMap  map[utils.ID]*FancySlotSlotInfo
}

func NewFancyDspSlotRegister(dspId utils.ID) *FancyDspSlotRegister {
	return &FancyDspSlotRegister{
		dspId:       dspId,
		dspSlotList: make(entity.DspSlotInfoList, 0),
		dspSlotMap:  make(map[utils.ID]*FancySlotSlotInfo),
	}
}

func (r *FancyDspSlotRegister) GetDspId() utils.ID {
	return r.dspId
}

func (r *FancyDspSlotRegister) UpdateDspSlotInfo(list entity.DspSlotInfoList) error {
	slotMap := make(map[utils.ID]*FancySlotSlotInfo)
	for _, slot := range list {
		pddSlot := &FancySlotSlotInfo{}
		if err := pddSlot.Init(slot); err != nil {
			zap.L().Error("[FancyDspSlotRegister] init slot failed, slot:, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slot.Id)))), zap.Error(err))
			continue
		}

		slotMap[slot.Id] = pddSlot
	}

	r.dspSlotMap = slotMap
	r.dspSlotList = list
	return nil
}

func (r *FancyDspSlotRegister) GetDspSlotInfoList() entity.DspSlotInfoList {
	return r.dspSlotList
}

func (r *FancyDspSlotRegister) GetSlotInfo(slotId utils.ID) *FancySlotSlotInfo {
	return r.dspSlotMap[slotId]
}
