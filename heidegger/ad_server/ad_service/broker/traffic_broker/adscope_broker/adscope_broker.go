package adscope_broker

import (
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/adscope_broker/adscope_broker_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"strings"
)

type (
	AdScopeBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId    utils.ID
		MediaMacro *macro_builder.MediaMacro
	}
)

func NewAdScopeBroker(mediaId utils.ID) *AdScopeBroker {
	return &AdScopeBroker{
		mediaId: mediaId,
		MediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "__PRICE__",
			MediaClickUpXMacro:   ".AD_CLK_PT_UP_X.",
			MediaClickUpYMacro:   ".AD_CLK_PT_UP_Y.",
			MediaClickDownXMacro: ".AD_CLK_PT_DOWN_X.",
			MediaClickDownYMacro: ".AD_CLK_PT_DOWN_Y.",
		},
	}
}

func (mb *AdScopeBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *AdScopeBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	request.AdRequestMedia.MediaMacro = mb.MediaMacro
	return mb.ParseAdRequest(request)
}

func (mb *AdScopeBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[AdScopeBroker]request body empty")
	}

	bidRequest := &adscope_broker_proto.BidRequest{}
	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[AdScopeBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if err = mb.buildRequest(request, bidRequest); err != nil {
		zap.L().Debug("[AdScopeBroker]BrokeRequest, parseUser failed")
		return err
	}
	mb.DoTrafficSamplePb(request, bidRequest)
	return nil
}
func (mb *AdScopeBroker) buildRequest(request *ad_service.AdRequest, bidRequest *adscope_broker_proto.BidRequest) error {
	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[AdScopeBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(utils.NewUUID())
	}
	request.SetMediaId(mb.mediaId)

	if err := mb.parseImp(bidRequest, request); err != nil {
		zap.L().Debug("[AdScopeBroker] BrokeRequest, parseImp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, request); err != nil {
		zap.L().Debug("[AdScopeBroker]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseUser(bidRequest, request); err != nil {
		zap.L().Debug("[AdScopeBroker]BrokeRequest, parseUser failed")
		return err
	}

	if err := mb.parseApp(bidRequest, request); err != nil {
		zap.L().Debug("[AdScopeBroker]BrokeRequest, parseApp failed")
		return err
	}
	return nil
}

func (mb *AdScopeBroker) parseImp(mediaBidRequest *adscope_broker_proto.BidRequest,
	bidReq *ad_service.AdRequest) error {

	if len(mediaBidRequest.Imp) == 0 {
		zap.L().Debug("[AdScopeBroker]parseImp, vendor: , imp nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return fmt.Errorf("parseImp, imp nil")
	}
	item := mediaBidRequest.Imp[0]

	bidReq.SetMediaSlotKey(item.PlaceID)
	bidReq.BidFloor = uint32(item.Bidfloor)
	bidReq.UseHttps = true
	bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
		Width:  int64(item.W),
		Height: int64(item.H),
	})

	switch item.AdType {
	case 1:
		bidReq.SlotType = entity.SlotTypeOpening
	case 2:
		bidReq.SlotType = entity.SlotTypePopup
	case 3:
		bidReq.SlotType = entity.SlotTypeBanner
	case 4:
		bidReq.SlotType = entity.SlotTypeFeeds
	default:
		bidReq.SlotType = entity.SlotTypeUnknown
	}
	if item.Pmp != nil && len(item.Pmp.Deals) > 0 {
		for _, d := range item.Pmp.Deals {
			sd := ad_service.SourceDeal{
				DealId:   d.Id,
				BidFloor: int64(d.Bidfloor),
			}
			bidReq.SourceDeal = append(bidReq.SourceDeal, sd)
		}
	}

	for _, templateID := range item.TemplateID {
		bidReq.AdxTemplateId = append(bidReq.AdxTemplateId, templateID)
		switch templateID {
		case templateImage21:
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(720),
				Height: int64(1280),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraString(key.String(), templateID)
		case templateImage24:
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(1280),
				Height: int64(720),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraString(key.String(), templateID)
		case templateNative255:
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(720),
				Height: int64(1280),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraString(key.String(), templateID)
		case templateNative256:
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(1280),
				Height: int64(720),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			key.Icon().AddRequiredCount(1)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraString(key.String(), templateID)
		}
	}

	return nil
}

func (mb *AdScopeBroker) parseDevice(mediaBidRequest *adscope_broker_proto.BidRequest, bidReq *ad_service.AdRequest) error {
	device := mediaBidRequest.Device
	bidReq.Device.UserAgent = device.Ua
	bidReq.Device.RequestIp = device.Ip
	if strings.Contains(device.Ip, ":") {
		bidReq.Device.IsIp6 = true
	}
	if len(device.Ip) == 0 {
		bidReq.Device.RequestIp = device.Ipv6
		bidReq.Device.IsIp6 = true
	}
	if device.Geo != nil {
		bidReq.Device.Lat = float64(device.Geo.Lat)
		bidReq.Device.Lon = float64(device.Geo.Lon)
	}

	bidReq.Device.DeviceType = func() entity.DeviceType {
		switch device.DeviceType {
		case 1:
			return entity.DeviceTypeMobile
		case 2:
			return entity.DeviceTypePc
		case 3:
			return entity.DeviceTypeOtt
		default:
			return entity.DeviceTypeUnknown
		}
	}()
	bidReq.Device.Model = device.Model
	bidReq.Device.Vendor = device.Make
	bidReq.Device.OsType = func() entity.OsType {
		switch device.Os {
		case "android":
			return entity.OsTypeAndroid
		case "ios":
			return entity.OsTypeIOS
		default:
			return entity.OsTypeUnknown
		}
	}()
	bidReq.Device.OsVersion = device.Osv
	bidReq.Device.ScreenWidth = device.W
	bidReq.Device.ScreenHeight = device.H
	bidReq.Device.PPI = device.Ppi
	bidReq.Device.ScreenDensity = device.Pxratio
	bidReq.Device.Language = device.Lang
	bidReq.Device.OperatorType = func() entity.OperatorType {
		switch device.Carrier {
		case 1:
			return entity.OperatorTypeChinaMobile
		case 2:
			return entity.OperatorTypeChinaUnicom
		case 3:
			return entity.OperatorTypeChinaTelecom
		default:
			return entity.OperatorTypeUnknown
		}
	}()

	bidReq.Device.ConnectionType = func() entity.ConnectionType {
		switch device.ConnectionType {
		case 1:
			return entity.ConnectionTypeNetEthernet
		case 2:
			return entity.ConnectionTypeWifi
		case 3:
			return entity.ConnectionTypeCellular
		case 4:
			return entity.ConnectionType2G
		case 5:
			return entity.ConnectionType3G
		case 6:
			return entity.ConnectionType4G
		case 7:
			return entity.ConnectionType5G
		default:
			return entity.ConnectionTypeUnknown
		}
	}()

	bidReq.Device.Mac = device.GetMac()
	bidReq.Device.MacMd5 = device.GetMacmd5()
	bidReq.Device.Imei = device.GetImei()
	bidReq.Device.ImeiMd5 = device.GetDidmd5()
	bidReq.Device.Oaid = device.GetOaid()
	bidReq.Device.Idfa = device.GetIdfa()

	bidReq.Device.AndroidId = device.GetAndroidID()
	for _, caid := range device.GetCaids() {
		bidReq.Device.Caid = caid.GetVersion() + "_" + caid.GetCaid()
		bidReq.Device.Caids = append(bidReq.Device.Caids, bidReq.Device.Caid)
	}

	bidReq.Device.BootMark = device.GetBootMark()
	bidReq.Device.UpdateMark = device.GetUpdateMark()
	bidReq.Device.DeviceUpgradeTime = device.GetSysUpdateMark()
	bidReq.Device.DeviceInitTime = device.GetFileMark()
	bidReq.Device.VercodeHms = device.GetHmsCoreVersion()
	bidReq.Device.VercodeAg = device.GetAgVercode()
	bidReq.Device.CountryCode = device.GetCountryCode()
	bidReq.Device.DeviceName = device.GetDeviceName()
	return nil
}

func (mb *AdScopeBroker) parseApp(mediaBidRequest *adscope_broker_proto.BidRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.App == nil {
		zap.L().Debug("parseApp, vendor: , app nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	app := mediaBidRequest.App
	bidReq.App.AppName = app.Name
	bidReq.App.AppBundle = app.Bundle
	bidReq.App.AppVersion = app.Version

	return nil
}

func (mb *AdScopeBroker) parseUser(mediaBidRequest *adscope_broker_proto.BidRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.User == nil {
		zap.L().Debug("parseUser, vendor: , user nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	user := mediaBidRequest.User

	if user.Gender == "M" {
		bidReq.UserGender = entity.UserGenderMan
	} else if user.Gender == "F" {
		bidReq.UserGender = entity.UserGenderWoman
	}

	if len(user.Tag) > 0 {
		bidReq.App.MediaInstalledAppIds = user.Tag
	}
	return nil
}

func (mb *AdScopeBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("AdScopeBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("AdScopeBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}
	bidResponse, err := mb.buildResponse(request)
	if err != nil {
		zap.L().Error("AdScopeBroker Error in buildResponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}
	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()

	buffer.EnsureSize(bidResponse.Size())
	_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		zap.L().Error("AdScopeBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/octet-stream")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	mb.DoTrafficResponseSamplePb(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[AdScopeBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", responseStr)))))
	}

	return nil
}

func (mb *AdScopeBroker) buildResponse(request *ad_service.AdRequest) (*adscope_broker_proto.BidResponse, error) {
	bidResponse := &adscope_broker_proto.BidResponse{
		Reqid:   request.GetRequestId(),
		Respid:  request.GetMediaSlotKey(),
		Seatbid: []*adscope_broker_proto.BidResponse_SeatBid{},
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		creative := candidate.GetCreative()
		if creative == nil {
			continue
		}
		bid := &adscope_broker_proto.BidResponse_Bid{
			Bidid: request.GetRequestId(),
			Impid: request.GetRequestId(),
			Price: candidate.GetBidPrice().Price,
			Crid:  candidate.GetCreative().GetCreativeId().String(),

			CreativeInfo: &adscope_broker_proto.BidResponse_Creative{
				Promotion:    &adscope_broker_proto.BidResponse_PromotionApp{},
				Cats:         "IAB24",
				AdvertiserID: "1",
			},
			Track: &adscope_broker_proto.BidResponse_Track{
				View:     candidate.GetMacroReplaceImpressionMonitorList(),
				Click:    candidate.GetMacroReplaceClickMonitorList(),
				Landing:  candidate.GetLandingUrl(),
				DeepLink: candidate.GetDeepLinkUrl(),
				FollowExt: &adscope_broker_proto.BidResponse_Track_FollowExt{
					DeepLinkSuccess: candidate.GetMacroReplaceDeepLinkMonitorList(),
					BeginDownload:   candidate.GetMacroReplaceAppDownloadStartedMonitorList(),
					Download:        candidate.GetMacroReplaceAppDownloadFinishedMonitorList(),
					BeginInstall:    candidate.GetMacroReplaceAppInstallStartMonitorList(),
					Install:         candidate.GetMacroReplaceAppInstalledMonitorList(),
				},
			},
		}

		if len(candidate.GetGenericAd().GetDelayMonitorUrlList()) > 0 {
			showTracks := []*adscope_broker_proto.BidResponse_Track_VideoTrack_TShowTrack{}
			for _, v := range candidate.GetGenericAd().GetDelayMonitorUrlList() {
				showTracks = append(showTracks, &adscope_broker_proto.BidResponse_Track_VideoTrack_TShowTrack{
					T:   int32(v.Delay),
					Url: []string{v.Url},
				})
			}
			bid.Track.VideoExt = &adscope_broker_proto.BidResponse_Track_VideoTrack{
				ShowTrack: showTracks,
			}
		}

		templateID := request.GetMediaExtraString(candidate.GetActiveCreativeTemplateKey().String(), "")
		bid.TemplateID = templateID
		bid.CreativeInfo.TemplateID = bid.TemplateID
		bid.CreativeInfo.TemplateType = getTemplateType(bid.TemplateID)
		bid.CreativeInfo.Materials = getBidResponseMaterials(candidate.GetSelectedMaterialList(), bid.TemplateID)
		switch candidate.GetLandingAction() {
		case entity.LandingTypeWeChatProgram:
		case entity.LandingTypeDownload:
			bid.CreativeInfo.InteractType = "2"
			bid.CreativeInfo.Promotion.AppDownloadURL = candidate.GetLandingUrl()
		case entity.LandingTypeDeepLink:
		default:
			bid.CreativeInfo.InteractType = "4"
		}

		if candidate.GetAppInfo() != nil {
			if len(candidate.GetAppInfo().Icon) > 0 {
				bid.AdLogo = &adscope_broker_proto.BidResponse_AdLogo{
					AdLogo: candidate.GetAppInfo().Icon,
				}
			}
			bid.CreativeInfo.Promotion.ApkName = candidate.GetAppInfo().AppName
			bid.CreativeInfo.Promotion.PackageName = candidate.GetAppInfo().PackageName
			//bid.CreativeInfo.Promotion.AppStoreID = candidate.GetAppInfo().AppID
			bid.CreativeInfo.Promotion.AppVersion = candidate.GetAppInfo().AppVersion
			bid.CreativeInfo.Promotion.PackageSizeBytes = int64(candidate.GetAppInfo().PackageSize)
			bid.CreativeInfo.Promotion.PrivacyUrl = candidate.GetAppInfo().Privacy
			bid.CreativeInfo.Promotion.PermissionsUrl = candidate.GetAppInfo().Permission
			bid.CreativeInfo.Promotion.AppDesc = candidate.GetAppInfo().AppDesc
			bid.CreativeInfo.Promotion.AppDeveloper = candidate.GetAppInfo().Develop
		}
		bidResponse.Seatbid = append(bidResponse.Seatbid, &adscope_broker_proto.BidResponse_SeatBid{
			Bid: []*adscope_broker_proto.BidResponse_Bid{
				bid,
			},
		})
	}
	return bidResponse, nil
}

func (mb *AdScopeBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[AdScopeBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/octet-stream")
	writer.WriteWithStatus(204, nil)
	return nil
}
