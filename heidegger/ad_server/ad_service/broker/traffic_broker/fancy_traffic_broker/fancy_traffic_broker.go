package fancy_traffic_broker

import (
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/fancy_traffic_broker/fancy_broker_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

const (
	AdxTemplateKey = "adxTemplate"
)

var (
	emptyAdxTemplateMap map[uint64]int = make(map[uint64]int)
)

type (
	FancyTrafficBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId       utils.ID
		host          string
		useRawMonitor bool

		WinPriceMacro string
	}
)

func NewFancyTrafficBroker(mediaId utils.ID) *FancyTrafficBroker {
	return &FancyTrafficBroker{
		mediaId:       mediaId,
		WinPriceMacro: "__PRICE__",
	}
}

func (mb *FancyTrafficBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *FancyTrafficBroker) Do(request *ad_service.AdRequest) error {
	return mb.ParseAdRequest(request)
}

func (mb *FancyTrafficBroker) ParseAdRequest(mRequest *ad_service.AdRequest) error {
	mRequest.Response.SetResponseBuilder(mb.SendResponse)
	mRequest.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	mRequest.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro

	body := mRequest.RawHttpRequest.GetBodyContent()

	if len(body) == 0 {
		return fmt.Errorf("[FancyTrafficBroker]request body empty")
	}

	bidRequest := &fancy_broker_entity.FancyJsonRequest{}
	//err := easyjson.Unmarshal(body, bidRequest)
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[FancyTrafficBroker]BrokeRequest, Request body unmarshal failed, err:, body", zap.Error(err), zap.String("param2", fmt.Sprintf("%v", string(body))))
		return fmt.Errorf("request body invalid")
	}

	if mRequest.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[FancyTrafficBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	mRequest.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		mRequest.SetRequestId(utils.NewUUID())
	}
	mRequest.SetMediaId(mb.mediaId)

	if err := mb.parseUser(bidRequest, mRequest); err != nil {
		zap.L().Debug("[FancyTrafficBroker]BrokeRequest, parseUser failed")
		return err
	}

	if err := mb.parseApp(bidRequest, mRequest); err != nil {
		zap.L().Debug("[FancyTrafficBroker]BrokeRequest, parseApp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, mRequest); err != nil {
		zap.L().Debug("[FancyTrafficBroker]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseImp(bidRequest, mRequest); err != nil {
		zap.L().Debug("[FancyTrafficBroker] BrokeRequest, parseImp failed")
		return err
	}

	mRequest.Referer = bidRequest.Ref
	mRequest.Url = bidRequest.Url

	mb.DoTrafficSample(mRequest, body)

	return nil

}

func (mb *FancyTrafficBroker) parseUser(mediaBidRequest *fancy_broker_entity.FancyJsonRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.User == nil {
		zap.L().Debug("[FancyTrafficBroker]parseUser, vendor: , user nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	user := mediaBidRequest.User

	bidReq.UserId = user.Uid
	bidReq.App.MediaInstalledAppIds = user.AList

	return nil
}

func (mb *FancyTrafficBroker) parseImp(mediaBidRequest *fancy_broker_entity.FancyJsonRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Imp == nil {
		zap.L().Debug("[FancyTrafficBroker]parseImp, vendor: , imp nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return fmt.Errorf("[FancyTrafficBroker]parseImp, imp nil")
	}

	item := mediaBidRequest.Imp

	if item == nil {
		zap.L().Error("[FancyTrafficBroker]parseImp, imp is nil id: , imp nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return fmt.Errorf("[FancyTrafficBroker]parseImp, imp nil")
	}

	bidReq.ImpressionId = type_convert.GetAssertString(item.Id)
	slotId := item.SlotId
	slotArr := strings.Split(slotId, "_")
	if len(slotArr) > 1 {
		slotId = slotArr[1]
	}

	bidReq.SetMediaSlotKey(slotId)
	bidReq.BidFloor = uint32(item.BidFloor)

	bidReq.SlotType = mb.mappingSlotType(item.SlotType)

	if len(item.MrSize) > 0 {
		for _, sizeI := range item.MrSize {
			arr := strings.Split(sizeI, "*")
			if len(arr) >= 2 {
				bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
					Width:  type_convert.GetAssertInt64(arr[0]),
					Height: type_convert.GetAssertInt64(arr[1]),
				})
			}
		}
	}

	//for _, ct := range item.CType {
	//	bidReq.Mimes = append(bidReq.Mimes, entity.MimeType(ct))
	//}

	for _, d := range item.Deal {
		sd := ad_service.SourceDeal{
			DealId:   d.DealId,
			BidFloor: int64(d.DealFloor),
		}

		bidReq.SourceDeal = append(bidReq.SourceDeal, sd)
	}

	bidReq.VideoMaxDuration = item.VideoMax
	bidReq.VideoMinDuration = item.VideoMin
	if bidReq.VideoMaxDuration < bidReq.VideoMinDuration {
		bidReq.VideoMaxDuration = bidReq.VideoMinDuration
	}

	adxTemplateMap := make(map[uint64]int)

	if item.Native != nil {
		for _, template := range item.Native.Templates {
			bidReq.AdxTemplateId = append(bidReq.AdxTemplateId, type_convert.GetAssertString(template.Id))
			switch template.Id {
			case 1, 2:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1)
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = template.Id
			case 3:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1)
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = template.Id
			case 5:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1)
				key.Image().AddRequiredCount(3).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = template.Id
			case 6:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = template.Id
			case 7:
				key := creative_entity.NewCreativeTemplateKey()
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				key.Icon().AddRequiredCount(1)
				key.Title().AddRequiredCount(1)
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = template.Id
			case 8:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Icon().AddRequiredCount(1)
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = template.Id
			case 9:
				key := creative_entity.NewCreativeTemplateKey()
				key.Image().AddRequiredCount(3).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				key.Icon().AddRequiredCount(1)
				key.Title().AddRequiredCount(1)
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = template.Id
			}
		}
	}

	if len(bidReq.GetCreativeTemplateKeyList()) == 0 {
		for _, mrType := range item.MrType {
			switch mrType {
			case 1:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				bidReq.AppendCreativeTemplateKey(key)
			case 2:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				bidReq.AppendCreativeTemplateKey(key)
			case 3:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				bidReq.AppendCreativeTemplateKey(key)
			}
		}
	}

	//for _, l := range item.SupportInteract {
	//	bidReq.ClickAction = append(bidReq.ClickAction, dict.LandingType(l))
	//}
	//if len(impInfo.ClickAction) < 1 {
	//	impInfo.ClickAction = append(impInfo.ClickAction, dict.LandingAll)
	//}
	bidReq.UseHttps = mb.mappingSecure(mediaBidRequest.Https)
	bidReq.AddMediaExtraData(AdxTemplateKey, adxTemplateMap)

	return nil
}

func (mb *FancyTrafficBroker) parseDevice(mediaBidRequest *fancy_broker_entity.FancyJsonRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Device == nil {
		zap.L().Error("parseDevice, device nil", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", mediaBidRequest)))))
		return fmt.Errorf("parseDevice failed")
	}
	//bidReq.IsMobile = true
	device := mediaBidRequest.Device

	bidReq.Device.RequestIp = mediaBidRequest.Ip

	bidReq.Device.UserAgent = mediaBidRequest.Ua

	bidReq.Device.OsType = mb.mappingOsType(device.Os)
	bidReq.Device.OsVersion = device.Osv
	bidReq.Device.Model = device.Model
	bidReq.Device.Brand = device.Vendor

	bidReq.Device.DeviceType = mb.mappingDeviceType(mediaBidRequest.DeviceType)

	if bidReq.Device.OsType == entity.OsTypeIOS {
		bidReq.Device.Idfa = device.Idfa
		bidReq.Device.IdfaMd5 = device.IdfaMd5
	} else {
		bidReq.Device.Imei = device.Imei
		bidReq.Device.ImeiMd5 = device.ImeiMd5

		bidReq.Device.Oaid = device.Oaid
		bidReq.Device.OaidMd5 = device.OaidMd5

		bidReq.Device.AndroidId = device.AndroidId
		bidReq.Device.AndroidIdMd5 = device.AndroidIdMd5
	}
	bidReq.Device.Caid = string_utils.ConcatString(device.CaidVersion, "_", device.Caid)
	bidReq.Device.CaidRaw = device.Caid
	bidReq.Device.CaidVersion = device.CaidVersion
	bidReq.Device.Caids = device.Caids

	if len(bidReq.Device.Caid) == 0 && len(bidReq.Device.Caids) > 0 {
		bidReq.Device.Caid = bidReq.Device.Caids[0]
	}

	bidReq.Device.Mac = device.Mac
	bidReq.Device.MacMd5 = device.MacMd5

	bidReq.Device.ScreenHeight = device.Height
	bidReq.Device.ScreenWidth = device.Width
	//bidReq.Device.ScreenDensity = device.Den

	bidReq.Device.Lat = float64(device.Lat)
	bidReq.Device.Lon = float64(device.Lng)

	bidReq.Device.OperatorType = mb.mappingOperatorType(device.Operator)
	bidReq.Device.ConnectionType = entity.ConnectionType(device.Net)

	bidReq.Device.Paid = device.Paid
	bidReq.Device.BootMark = device.BootMark
	bidReq.Device.UpdateMark = device.UpdateMark
	bidReq.Device.DeviceStartupTime = device.BootTime
	bidReq.Device.DeviceUpgradeTime = device.UpdateTime
	bidReq.Device.VercodeAg = device.VercodeAg
	bidReq.Device.VercodeHms = device.VercodeHms

	return nil
}

func (mb *FancyTrafficBroker) parseApp(mediaBidRequest *fancy_broker_entity.FancyJsonRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.App == nil {
		zap.L().Debug("[FancyTrafficBroker]parseApp, vendor: , app nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	app := mediaBidRequest.App

	bidReq.App.AppName = app.Name
	bidReq.App.AppBundle = app.Package
	bidReq.App.AppVersion = app.Version

	return nil
}

func (mb *FancyTrafficBroker) mappingSecure(s int32) bool {

	switch s {
	case 0:
		return false
	case 1:
		return true
	case 2:
		return false
	default:
		return false
	}
}

func (mb *FancyTrafficBroker) mappingSlotType(s int32) entity.SlotType {
	switch s {
	case 1:
		return entity.SlotTypeVideo
	case 2:
		return entity.SlotTypeVideoPause
	case 3:
		return entity.SlotTypeBanner
	case 4:
		return entity.SlotTypePopup
	case 5:
		return entity.SlotTypeBanner
	case 6:
		return entity.SlotTypeOpening
	case 7:
		return entity.SlotTypePopup
	case 8:
		return entity.SlotTypeFeeds
	case 12:
		return entity.SlotTypeRewardVideo
	default:
		return entity.SlotTypeUnknown
	}
}

func (mb *FancyTrafficBroker) mappingOperatorType(s int32) entity.OperatorType {
	switch s {
	case 0:
		return entity.OperatorTypeUnknown
	case 1:
		return entity.OperatorTypeChinaMobile
	case 2:
		return entity.OperatorTypeChinaUnicom
	case 3:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func (mb *FancyTrafficBroker) mappingOsType(s int32) entity.OsType {
	switch s {
	case 0:
		return entity.OsTypeUnknown
	case 2:
		return entity.OsTypeIOS
	case 1:
		return entity.OsTypeAndroid
	case 3:
		return entity.OsTypeWindowsPhone
	case 4:
		return entity.OsTypeWindows
	default:
		return entity.OsTypeUnknown
	}
}

func (mb *FancyTrafficBroker) mappingDeviceType(s int32) entity.DeviceType {
	switch s {
	case 1:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePad
	case 3:
		return entity.DeviceTypePc
	case 4:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeUnknown
	}
}

func (mb *FancyTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("FancyTrafficBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("FancyTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}

	bidResponse := &fancy_broker_entity.FancyJsonResponse{}
	bidResponse.Rid = request.GetRequestId()
	bidResponse.BidId = request.GetRequestId()

	bidResponse.Bid = make([]*fancy_broker_entity.FancyJsonResponseBid, 0)

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()

		if genericAd == nil || creative == nil {
			continue
		}

		if len(candidate.GetSelectedMaterialList()) == 0 {
			zap.L().Error("[FancyTrafficBroker]no matierial")
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		bid := &fancy_broker_entity.FancyJsonResponseBid{
			Action:  0,
			Deal:    "",
			Native:  &fancy_broker_entity.FancyJsonResponseNative{},
			Landing: candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
			DpUrl:   candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen),
		}

		if creative.GetCreativeId() > 0 {
			bid.AdId = strconv.Itoa(int(creative.GetCreativeId()))
		} else {
			bid.AdId = creative.GetCreativeKey()
		}

		bid.ImpTracking = candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
		bid.ClickTracking = candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen)
		bid.DpTracking = candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen)
		if len(genericAd.GetDeepLinkUrl()) > 0 {
			bid.DpTracking = append(bid.DpTracking, candidate.ReplaceUrlMacro(genericAd.GetDpSuccess(), traffic, trackingGen))
		}
		if len(genericAd.GetDelayMonitorUrlList()) > 0 {
			bid.DelayTracking = make([]*fancy_broker_entity.FancyJsonResponseVideoTracker, 0)
			for _, ev := range genericAd.GetDelayMonitorUrlList() {
				bid.DelayTracking = append(bid.DelayTracking, &fancy_broker_entity.FancyJsonResponseVideoTracker{
					DelayTime: int32(ev.Delay),
					Url:       ev.Url,
				})
			}
		}

		bidPrice := candidate.GetBidPrice()
		bid.Price = int(bidPrice.Price)

		adm := bid.Native
		bid.MrType = 3 // 1.图片 2.视频 3.naitve
		reqAdxTemplateMap := request.GetMediaExtraDataWithDefault(AdxTemplateKey, emptyAdxTemplateMap).(map[uint64]int)
		key1 := candidate.GetActiveCreativeTemplateKey()
		keyInt := key1.Uint64()
		adm.TemplateId = reqAdxTemplateMap[keyInt]
		hasImage := false
		hasVideo := false
		hasText := false
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				adm.Assets = append(adm.Assets, &fancy_broker_entity.FancyJsonResponseAsset{
					Type: fancy_broker_entity.TemplateAssetTypeImage,
					Image: &fancy_broker_entity.FancyJsonResponseImg{
						Url: rsc.Url,
						W:   rsc.Width,
						H:   rsc.Height,
					},
				})

				if len(bid.MrSrc) == 0 {
					bid.MrSrc = rsc.Url
				}

				if bid.Width == 0 || bid.Height == 0 {
					bid.Width = rsc.Width
					bid.Height = rsc.Height
				}

				hasImage = true

			case entity.MaterialTypeIcon:
				adm.Assets = append(adm.Assets, &fancy_broker_entity.FancyJsonResponseAsset{
					Type: fancy_broker_entity.TemplateAssetTypeIcon,
					Icon: &fancy_broker_entity.FancyJsonResponseImg{
						Url: rsc.Url,
						W:   rsc.Width,
						H:   rsc.Height,
					},
				})
			case entity.MaterialTypeLogo:

			case entity.MaterialTypeTitle:
				adm.Assets = append(adm.Assets, &fancy_broker_entity.FancyJsonResponseAsset{
					Title: rsc.Data,
					Type:  fancy_broker_entity.TemplateAssetTypeTitle,
				})
				hasText = true
			case entity.MaterialTypeDesc:
				adm.Assets = append(adm.Assets, &fancy_broker_entity.FancyJsonResponseAsset{
					Type: fancy_broker_entity.TemplateAssetTypeDesc,
					Desc: rsc.Data,
				})
				hasText = true
			//case dict.MaterialResourceLayoutAdvertiser:
			//	adm.Ext.AdvertiserName = rsc.Text.Text

			case entity.MaterialTypeVideo:
				adm.Assets = append(adm.Assets, &fancy_broker_entity.FancyJsonResponseAsset{
					Type: fancy_broker_entity.TemplateAssetTypeVideo,
					Video: &fancy_broker_entity.FancyJsonResponseVideo{
						Url:      rsc.Url,
						W:        rsc.Width,
						H:        rsc.Height,
						Duration: int32(rsc.Duration),
					},
				})
				bid.Duration = int(rsc.Duration)
				bid.MrSrc = rsc.Url
				hasVideo = true
				if bid.Width == 0 || bid.Height == 0 {
					bid.Width = rsc.Width
					bid.Height = rsc.Height
				}
			//case dict.MaterialResourceLayoutButtonText:
			//	adm.Ext.Btn = rsc.Text.Text
			default:
				continue
			}
		}

		bid.Action = 0
		if genericAd.GetLandingAction() == entity.LandingTypeDeepLink {
			bid.Action = 2
		} else if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			bid.Action = 1
		}

		if hasVideo {
			bid.MrType = 2
			if hasImage || hasText {
				bid.MrType = 3
			}
		} else if hasImage {
			bid.MrType = 1
			if hasText || hasVideo {
				bid.MrType = 3
			}
		}

		if genericAd.GetAppInfo() != nil {
			bid.AppDesc = &fancy_broker_entity.FancyJsonResponseApp{
				Name:         genericAd.GetAppInfo().AppName,
				Package:      genericAd.GetAppInfo().PackageName,
				AppId:        genericAd.GetAppInfo().AppID,
				Version:      genericAd.GetAppInfo().AppVersion,
				PackageSize:  int64(genericAd.GetAppInfo().PackageSize),
				Privacy:      genericAd.GetAppInfo().Privacy,
				Permission:   genericAd.GetAppInfo().Permission,
				AppDesc:      genericAd.GetAppInfo().AppDesc,
				AppDescUrl:   genericAd.GetAppInfo().AppDescURL,
				AppDeveloper: genericAd.GetAppInfo().Develop,
			}

			if genericAd.GetAppInfo().WechatExt != nil {
				bid.AppDesc.WxProgramId = genericAd.GetAppInfo().WechatExt.ProgramId
				bid.AppDesc.WxTargetPath = genericAd.GetAppInfo().WechatExt.ProgramPath
			}

			if len(genericAd.GetAppInfo().PermissionDesc) > 0 {
				bid.AppDesc.PermissionDesc = make([]*fancy_broker_entity.FancyJsonResponsePermissionDesc, 0)
				for _, item := range genericAd.GetAppInfo().PermissionDesc {
					bid.AppDesc.PermissionDesc = append(bid.AppDesc.PermissionDesc, &fancy_broker_entity.FancyJsonResponsePermissionDesc{
						PermissionLab:  item.PermissionLab,
						PermissionDesc: item.PermissionDesc,
					})
				}
			}
		}

		bidResponse.Bid = append(bidResponse.Bid, bid)
		break

	}

	//if err := mb.BuildHttpEasyJsonResponse(request, writer, bidResponse); err != nil {
	//	zap.L().Error("CommonJsonTrafficBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
	//	return err
	//}
	//
	//mb.DoTrafficResponseSampleEasyJson(request, bidResponse)

	if err := mb.BuildHttpSonicJsonResponse(request, writer, &bidResponse); err != nil {
		//zap.L().Error("FancyTrafficBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	mb.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		zap.L().Info("[FancyTrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", bidResponse.DumpJson())))))
	}

	return nil
}

func (mb *FancyTrafficBroker) RoundSeconds(duration int) int32 {
	const VideoDurationUnit = 1
	return int32(math.Round(float64(duration) / VideoDurationUnit))
}

func (mb *FancyTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[FancyTrafficBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		zap.L().Info("[FancyTrafficBroker] adCode:%+v").GetErrCodeMap())
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	writer.WriteWithStatus(204, nil)
	return nil
}
