package rta_traffic_broker

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/rta_traffic_broker/rta_traffic_entity"

	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type HuaWeiRtaTrafficBroker struct {
	traffic_broker.TrafficBrokerBase
	mediaId   utils.ID
	secretKey string
	logger    *zap.Logger
}

func NewHuaWeiRtaTrafficBroker(mediaId utils.ID, secretKey string) *HuaWeiRtaTrafficBroker {
	broker := &HuaWeiRtaTrafficBroker{
		mediaId:   mediaId,
		secretKey: secretKey,
	}
	// 配置日志格式为JSON
	logger := zap.L()
	// logger.SetFormatter converted - configure zap logger instead

	// 设置日志级别
	// logger.SetLevel converted - configure zap logger instead

	// 初始化logger
	// broker.logger converted - use zap.L().With(zap.String("module", "huawei_rta"), zap.String("mediaId", fmt.Sprintf("%v", mediaId))) instead

	return broker
}

func (hw *HuaWeiRtaTrafficBroker) GetMediaId() utils.ID {
	return hw.mediaId
}

// verifySign 验证签名
// HmacSha256(requestId + requestTime + oaid)，其中oaid需要转为大写再进行加密
func (hw *HuaWeiRtaTrafficBroker) verifySign(requestId string, requestTime int64, oaid string, sign string) bool {
	if len(requestId) == 0 || requestTime == 0 || len(oaid) == 0 || len(sign) == 0 {
		return false
	}

	// 构建签名字符串
	signStr := fmt.Sprintf("%s%d%s", requestId, requestTime, strings.ToUpper(oaid))

	// 创建 HMAC-SHA256 哈希
	h := hmac.New(sha256.New, []byte(hw.secretKey))
	h.Write([]byte(signStr))

	// 计算签名
	calculatedSign := hex.EncodeToString(h.Sum(nil))

	// 比较签名
	return calculatedSign == sign
}

func (hw *HuaWeiRtaTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(hw.SendResponse)
	request.Response.SetFallbackResponseBuilder(hw.SendFallbackResponse)
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[HuaWeiRtaTrafficBroker]request body empty")
	}

	if request.IsDebug {
		hw.logger.// Logger.SetLevel converted - configure zap logger instead
	} else {
		hw.logger.// Logger.SetLevel converted - configure zap logger instead
	}

	rtaReq := &rta_traffic_entity.HuaWeiRTARequest{}
	err := sonic.Unmarshal(body, rtaReq)
	if err != nil {
		hw.logger.WithError(err).Debug("request body is empty")
		return fmt.Errorf("request body is empty")
	}

	// 参数验证
	if len(rtaReq.RtaIdList) == 0 {
		hw.logger.Debug("rtaIdList are both empty")
		return fmt.Errorf("rtaIdList are both empty")
	}

	if len(rtaReq.Oaid) == 0 && len(rtaReq.Ifa) == 0 {
		hw.logger.Debug("Oaid and Ifa are both empty")
		return fmt.Errorf("Oaid and Ifa are both empty")
	}

	if len(rtaReq.RequestId) == 0 || rtaReq.RequestTime == 0 {
		hw.logger.Debug("RequestId or RequestTime are empty")
		return fmt.Errorf("RequestId or RequestTime are empty")
	}

	// 验证sign
	if !hw.verifySign(rtaReq.RequestId, rtaReq.RequestTime, rtaReq.Oaid, rtaReq.Sign) {
		hw.logger.Debug("sign verification failed")
		return fmt.Errorf("sign verification failed")
	}

	// 处理reqid
	reqId := rtaReq.RequestId
	hw.logger = hw.logger.WithField("reqId", reqId)
	hw.logger.WithField("request_body", rtaReq).Debug("request body")
	request.SetRequestId(reqId)

	// 设置mediaId
	request.SetMediaId(hw.mediaId)
	// 设置slotKey
	query := request.GetQuery()
	slotId := query.Get("slot")
	if slotId == "" {
		hw.logger.WithField("query", query).Error("slot id is empty")
		return fmt.Errorf("slot id is empty")
	}
	request.SetMediaSlotKey(slotId)

	// 设置设备信息
	request.Device.OsType = hw.toOsType(rtaReq.ChannelName)
	request.Device.Oaid = rtaReq.Oaid
	request.Device.Idfa = rtaReq.Ifa
	request.Device.OsVersion = rtaReq.AndroidVersion
	request.Device.SystemOsUIVersion = rtaReq.EmuiVersion
	request.Device.DeviceName = rtaReq.PropagationName
	request.Device.RequestIp = rtaReq.Ip
	request.Device.UserAgent = rtaReq.Ua

	// 处理RTA ID列表
	for _, rtaId := range rtaReq.RtaIdList {
		request.RtaIds = append(request.RtaIds, utils.ID(type_convert.GetAssertInt(rtaId)))
	}

	//hack template
	key := creative_entity.NewCreativeTemplateKey()
	key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	request.AppendCreativeTemplateKey(key)

	key1 := creative_entity.NewCreativeTemplateKey()
	key1.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	request.AppendCreativeTemplateKey(key1)

	hw.DoTrafficSample(request, body)
	return nil
}

func (hw *HuaWeiRtaTrafficBroker) toOsType(channelName string) entity.OsType {
	if strings.ToLower(channelName) == "huaweiads" {
		return entity.OsTypeAndroid
	}
	return entity.OsTypeAndroid
}

func (hw *HuaWeiRtaTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		request.Response.Dump("HuaWeiRtaTrafficBroker")
		hw.logger.Infof("[HuaWeiRtaTrafficBroker] adCode:%+v", request.Response.GetTotalAdCandidateList().GetErrCodeMap())
	}

	if request.Response.NoCandidate() || len(request.RtaIds) == 0 {
		hw.zap.L().Debug("NoCandidate or NoRtaids", zap.String("GetAdCandidateList", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response.GetAdCandidateList())))), zap.String("rtaIds", fmt.Sprintf("%v", request.RtaIds)))
		return hw.SendFallbackResponse(request, writer)
	}

	response := &rta_traffic_entity.HuaWeiRTAResponse{
		RetCode: 0,
		Result:  "no",
	}

	allAccept := true
	for _, candidate := range request.Response.GetAdCandidateList() {
		ad := candidate.GetAd()
		dmpResult := ad.GetDmpTagResult()
		if dmpResult == nil {
			continue
		}

		for _, rtaId := range request.RtaIds {
			if dmpResult.IsSatisfiedById(rtaId) {
				response.RtaIdList = append(response.RtaIdList, rtaId.String())
			} else {
				allAccept = false
			}
		}
	}

	if allAccept {
		response.Result = "yes"
	}

	if err := hw.BuildHttpSonicJsonResponse(request, writer, &response); err != nil {
		return err
	}

	hw.DoTrafficResponseSampleSonicJson(request, response)

	hw.zap.L().Debug("HuaWeiRtaTrafficBroker success response", zap.String("response", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response)))), zap.String("request", fmt.Sprintf("%v", request.RawHttpRequest.GetBodyContent())))

	return nil
}

func (hw *HuaWeiRtaTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		hw.logger.Infof("[HuaWeiRtaTrafficBroker] SendFallbackResponse start")
	}

	errCode := int(request.Response.GetAdErrorCode().Code)
	if errCode == 0 {
		errCode = 1
	}
	response := &rta_traffic_entity.HuaWeiRTAResponse{
		RetCode:  errCode,
		ErrorMsg: request.Response.GetAdErrorCode().Message(),
		Result:   "no",
	}

	if err := hw.BuildHttpSonicJsonResponse(request, writer, &response); err != nil {
		writer.SetHeader("Content-Length", "0")
		writer.SetHeader("Content-Type", "application/json;charset=utf-8")
		writer.WriteWithStatus(200, nil)
		hw.logger.WithError(err).WithField("response", response).Info("BuildHttpSonicJsonResponse error")
		return nil
	}
	hw.zap.L().Debug("HuaWeiRtaTrafficBroker fail response", zap.String("response", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response)))))
	return nil
}
