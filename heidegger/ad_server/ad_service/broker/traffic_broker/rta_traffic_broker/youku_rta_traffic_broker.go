package rta_traffic_broker

import (
	"fmt"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/rta_traffic_broker/rta_traffic_entity"

	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

const (
	templateId = "templateId"
)

type YoukuRtaTrafficBroker struct {
	traffic_broker.TrafficBrokerBase
	mediaId utils.ID
	logger  *zap.Logger
}

func NewYoukuRtaTrafficBroker(mediaId utils.ID) *YoukuRtaTrafficBroker {
	broker := &YoukuRtaTrafficBroker{
		mediaId: mediaId,
	}
	// 配置日志格式为JSON
	logger := zap.L()
	// logger.SetFormatter converted - configure zap logger instead

	// 设置日志级别
	// logger.SetLevel converted - configure zap logger instead

	// 初始化logger
	// broker.logger converted - use zap.L().With(zap.String("module", "youku_rta"), zap.String("mediaId", fmt.Sprintf("%v", mediaId))) instead

	return broker
}

func (yk *YoukuRtaTrafficBroker) GetMediaId() utils.ID {
	return yk.mediaId
}

func (yk *YoukuRtaTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(yk.SendResponse)
	request.Response.SetFallbackResponseBuilder(yk.SendFallbackResponse)
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[YoukuRtaTrafficBroker]request body empty")
	}

	if request.IsDebug {
		yk.logger.// Logger.SetLevel converted - configure zap logger instead
	} else {
		yk.logger.// Logger.SetLevel converted - configure zap logger instead
	}

	rtaReq := &rta_traffic_entity.YoukuRtaRequest{}
	err := sonic.Unmarshal(body, rtaReq)
	if err != nil {
		yk.logger.WithError(err).Debug("request body is empty")
		return fmt.Errorf("request body is empty")
	}

	// 参数验证
	if len(rtaReq.RtaIdList) == 0 {
		yk.logger.Info("rtaIdList is empty")
		return fmt.Errorf("rtaIdList is empty")
	}

	if rtaReq.DeviceInfo == nil {
		yk.logger.Debug("deviceInfo is empty")
		return fmt.Errorf("deviceInfo is empty")
	}

	// 处理reqid
	reqId := rtaReq.ReqId
	if len(reqId) == 0 {
		reqId = utils.NewUUID()
	}
	yk.logger = yk.logger.WithField("reqId", reqId)
	yk.logger.WithField("request_body", rtaReq).Debug("request body")

	request.SetRequestId(reqId)

	// 设置mediaId
	request.SetMediaId(yk.mediaId)

	// 设置slotKey
	query := request.GetQuery()
	slotId := query.Get("slot")
	if slotId == "" {
		yk.logger.WithField("query", query).Error("slot id is empty")
		return fmt.Errorf("slot id is empty")
	}
	request.SetMediaSlotKey(slotId)
	request.ImpressionId = request.GetRequestId()

	request.Device.OsType = yk.toOsType(rtaReq.DeviceInfo.Os)
	request.Device.AndroidIdMd5 = rtaReq.DeviceInfo.AndroidIdMd5
	request.Device.ImeiMd5 = rtaReq.DeviceInfo.ImeiMd5
	request.Device.Oaid = rtaReq.DeviceInfo.Oaid
	request.Device.IdfaMd5 = rtaReq.DeviceInfo.IdfaMd5
	request.Device.CaidRaw = rtaReq.DeviceInfo.Caid1
	request.Device.CaidVersion = rtaReq.DeviceInfo.Caid1Version

	if len(rtaReq.DeviceInfo.Caid1) != 0 && len(rtaReq.DeviceInfo.Caid1Version) != 0 {
		request.Device.Caid = rtaReq.DeviceInfo.Caid1 + "_" + rtaReq.DeviceInfo.Caid1Version
	}

	advertiserIdMap := make(map[utils.ID]int64)
	for _, rtaInfo := range rtaReq.RtaIdList {
		rtaId := utils.ID(type_convert.GetAssertInt(rtaInfo.RtaId))
		if rtaId == 0 {
			yk.logger.Debugf("rtaid is empty,ratInfo:%v", rtaInfo)
			continue
		}
		advertiserIdMap[rtaId] = rtaInfo.AdvertiserId
		request.RtaIds = append(request.RtaIds, rtaId)
	}
	request.AddMediaExtraData(templateId, advertiserIdMap)

	//hack template
	key := creative_entity.NewCreativeTemplateKey()
	key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	request.AppendCreativeTemplateKey(key)

	key1 := creative_entity.NewCreativeTemplateKey()
	key1.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	request.AppendCreativeTemplateKey(key1)

	yk.DoTrafficSample(request, body)
	return nil
}

func (yk *YoukuRtaTrafficBroker) toOsType(osStr string) entity.OsType {
	switch strings.ToLower(osStr) {
	case "ios":
		return entity.OsTypeIOS
	case "android":
		return entity.OsTypeAndroid
	default:
		return entity.OsTypeOther
	}
}

func (yk *YoukuRtaTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		request.Response.Dump("YoukuRtaTrafficBroker")
		yk.logger.Infof("[YoukuRtaTrafficBroker] adCode:%+v", request.Response.GetTotalAdCandidateList().GetErrCodeMap())
	}

	if request.Response.NoCandidate() || len(request.RtaIds) == 0 {
		yk.zap.L().Debug("NoCandidate or NoRtaids", zap.String("GetAdCandidateList", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response.GetAdCandidateList())))), zap.String("rtaIds", fmt.Sprintf("%v", request.RtaIds)))
		return yk.SendFallbackResponse(request, writer)
	}

	reqId := request.GetRequestId()
	response := &rta_traffic_entity.YoukuRtaResponse{
		Code:       0,
		RtaRetList: make([]*rta_traffic_entity.RtaRetInfo, 0),
		ReqId:      reqId,
		Accept:     "N",
	}

	advertiserIdMapI, ok := request.GetMediaExtraData(templateId)
	if !ok {
		yk.logger.Debug("advertiserIdMapI is empty")
		return yk.SendFallbackResponse(request, writer)
	}
	advertiserIdMap := advertiserIdMapI.(map[utils.ID]int64)

	allAccept := true
	for _, candidate := range request.Response.GetAdCandidateList() {
		ad := candidate.GetAd()
		dmpResult := ad.GetDmpTagResult()
		if dmpResult == nil {
			continue
		}

		for _, rtaId := range request.RtaIds {
			rtaRetInfo := &rta_traffic_entity.RtaRetInfo{
				AdvertiserId: 0,
				RtaId:        rtaId.String(),
				Accept:       "N",
			}
			advId, ok := advertiserIdMap[rtaId]
			if ok {
				rtaRetInfo.AdvertiserId = advId
			}
			if dmpResult.IsSatisfiedById(rtaId) {
				rtaRetInfo.Accept = "Y"
				response.RtaRetList = append(response.RtaRetList, rtaRetInfo)
			} else {
				allAccept = false
			}
		}
	}

	if allAccept {
		response.Accept = "Y"
	} else if len(response.RtaRetList) != 0 {
		response.Accept = "PART"
	}

	if err := yk.BuildHttpSonicJsonResponse(request, writer, &response); err != nil {
		return err
	}

	yk.DoTrafficResponseSampleSonicJson(request, response)

	yk.zap.L().Debug("YoukuRtaTrafficBroker success response", zap.String("response", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response)))), zap.String("request", fmt.Sprintf("%v", request.RawHttpRequest.GetBodyContent())))

	return nil
}

func (yk *YoukuRtaTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		yk.logger.Infof("[YoukuRtaTrafficBroker] SendFallbackResponse start")
	}

	errCode := int(request.Response.GetAdErrorCode().Code)
	if errCode == 0 {
		errCode = 1
	}
	response := &rta_traffic_entity.RTAResponse{
		Code: errCode,
	}

	if err := yk.BuildHttpSonicJsonResponse(request, writer, &response); err != nil {
		writer.SetHeader("Content-Length", "0")
		writer.SetHeader("Content-Type", "application/json;charset=utf-8")
		writer.WriteWithStatus(200, nil)
		yk.logger.WithError(err).WithField("response", response).Info("BuildHttpSonicJsonResponse error")
		return nil
	}
	yk.zap.L().Debug("YoukuRtaTrafficBroker fail response", zap.String("response", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response)))))
	return nil
}
