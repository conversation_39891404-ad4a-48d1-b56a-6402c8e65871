package rta_traffic_broker

import (
	"fmt"

	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/rta_traffic_broker/rta_traffic_entity/qtt_traffic_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type QttRtaTrafficBroker struct {
	traffic_broker.TrafficBrokerBase
	mediaId utils.ID
	logger  *zap.Logger
}

func NewQttRtaTrafficBroker(mediaId utils.ID) *QttRtaTrafficBroker {
	broker := &QttRtaTrafficBroker{
		mediaId: mediaId,
	}
	// 配置日志格式为JSON
	logger := zap.L()
	// logger.SetFormatter converted - configure zap logger instead

	// 设置日志级别
	// logger.SetLevel converted - configure zap logger instead

	// 初始化logger
	// broker.logger converted - use zap.L().With(zap.String("module", "qtt_rta"), zap.String("mediaId", fmt.Sprintf("%v", mediaId))) instead

	return broker
}

func (qtt *QttRtaTrafficBroker) GetMediaId() utils.ID {
	return qtt.mediaId
}

func (qtt *QttRtaTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(qtt.SendResponse)
	request.Response.SetFallbackResponseBuilder(qtt.SendFallbackResponse)
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[QttRtaTrafficBroker]request body empty")
	}

	if request.IsDebug {
		qtt.logger.// Logger.SetLevel converted - configure zap logger instead
	} else {
		qtt.logger.// Logger.SetLevel converted - configure zap logger instead
	}

	rtaReq := &qtt_traffic_entity.RTARequest{}
	err := proto.Unmarshal(body, rtaReq)
	if err != nil {
		qtt.logger.WithError(err).Debug("request body unmarshal failed")
		return fmt.Errorf("request body unmarshal failed: %v", err)
	}

	// 参数验证
	if rtaReq.GetRtaId() == 0 {
		qtt.logger.Debug("rtaId is empty")
		return fmt.Errorf("rtaId is empty")
	}

	if rtaReq.GetDevice() == nil {
		qtt.logger.Debug("device is empty")
		return fmt.Errorf("device is empty")
	}

	// 处理reqid
	reqId := rtaReq.GetReqId()
	if len(reqId) == 0 {
		reqId = utils.NewUUID()
	}
	qtt.logger = qtt.logger.WithField("reqId", reqId)
	qtt.logger.WithField("request_body", rtaReq).Debug("request body")

	request.SetRequestId(reqId)

	// 设置mediaId
	request.SetMediaId(qtt.mediaId)

	// 设置slotKey
	query := request.GetQuery()
	slotId := query.Get("slot")
	rtaId := query.Get("tag")

	if slotId == "" || rtaId == "" {
		qtt.logger.WithField("query", query).Error("slot id || rtaId is empty")
		return fmt.Errorf("slot id || rtaId is empty")
	}
	request.SetMediaSlotKey(slotId)
	// 设置RTA ID
	request.RtaIds = append(request.RtaIds, utils.ID(type_convert.GetAssertInt(rtaId)))
	request.ImpressionId = request.GetRequestId()

	// 判断req_type是否为ADLIST_REQUEST
	if rtaReq.GetReqType() == "ADLIST_REQUEST" {
		qtt.logger.Debug("req_type is ADLIST_REQUEST")
		return fmt.Errorf("req_type is ADLIST_REQUEST")
	}

	// 设置设备信息
	request.Device.OsType = qtt.toOsType(rtaReq.Device.GetOs())
	request.Device.RequestIp = rtaReq.Device.GetIp()

	for _, devId := range rtaReq.Device.GetDevId() {
		switch devId.Type {
		case qtt_traffic_entity.DeviceIdType_IMEI:
			if devId.GetIsMd5() {
				request.Device.ImeiMd5 = devId.Id
			} else {
				request.Device.Imei = devId.Id
			}
		case qtt_traffic_entity.DeviceIdType_IDFA:
			if devId.GetIsMd5() {
				request.Device.IdfaMd5 = devId.Id
			} else {
				request.Device.Idfa = devId.Id
			}
		case qtt_traffic_entity.DeviceIdType_ANDROIDID:
			if devId.GetIsMd5() {
				request.Device.AndroidIdMd5 = devId.Id
			} else {
				request.Device.AndroidId = devId.Id
			}
		case qtt_traffic_entity.DeviceIdType_OAID:
			if devId.GetIsMd5() {
				request.Device.OaidMd5 = devId.Id
			} else {
				request.Device.Oaid = devId.Id
			}
		case qtt_traffic_entity.DeviceIdType_CAID:
			if devId.GetIsMd5() {
				request.Device.CaidMd5Raw = devId.Id
			} else {
				request.Device.CaidRaw = devId.Id
			}
			if len(devId.Id) != 0 && len(devId.Ver) != 0 {
				request.Device.Caid = devId.Ver + "_" + devId.Id
			}
		default:
			qtt.logger.WithField("devId", devId).Error("unknown device id type")
		}
	}

	// 设置创意模板
	key := creative_entity.NewCreativeTemplateKey()
	key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	request.AppendCreativeTemplateKey(key)

	key1 := creative_entity.NewCreativeTemplateKey()
	key1.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	request.AppendCreativeTemplateKey(key1)

	qtt.DoTrafficSample(request, body)
	return nil
}

func (qtt *QttRtaTrafficBroker) toOsType(osType qtt_traffic_entity.OSType) entity.OsType {
	switch osType {
	case qtt_traffic_entity.OSType_IOS:
		return entity.OsTypeIOS
	case qtt_traffic_entity.OSType_ANDROID:
		return entity.OsTypeAndroid
	default:
		return entity.OsTypeOther
	}
}

func (qtt *QttRtaTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		request.Response.Dump("QttRtaTrafficBroker")
		qtt.logger.Infof("[QttRtaTrafficBroker] adCode:%+v", request.Response.GetTotalAdCandidateList().GetErrCodeMap())
	}

	if request.Response.NoCandidate() || len(request.RtaIds) == 0 {
		qtt.zap.L().Debug("NoCandidate or NoRtaids", zap.String("GetAdCandidateList", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response.GetAdCandidateList())))), zap.String("rtaIds", fmt.Sprintf("%v", request.RtaIds)))
		return qtt.SendFallbackResponse(request, writer)
	}

	reqId := request.GetRequestId()
	response := &qtt_traffic_entity.RTAResponse{
		StatusCode: qtt_traffic_entity.StatusCode_BID_ABANDON,
		Success:    true,
		ReqId:      reqId,
	}

	allAccept := true
	for _, candidate := range request.Response.GetAdCandidateList() {
		ad := candidate.GetAd()
		dmpResult := ad.GetDmpTagResult()
		if dmpResult == nil {
			qtt.zap.L().Debug("dmpResult is nil", zap.String("ad", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", ad)))))
			continue
		}

		for _, rtaId := range request.RtaIds {
			if !dmpResult.IsSatisfiedById(rtaId) {
				allAccept = false
			}
		}
	}

	if allAccept {
		response.StatusCode = qtt_traffic_entity.StatusCode_BID_ALL
	}

	// 内部耗时
	response.CostTime = request.GetProcessTimeMilliseconds()

	// 设置响应头
	writer.SetHeader("Content-Type", "application/x-protobuf")

	// 序列化响应
	responseBytes, err := proto.Marshal(response)
	if err != nil {
		qtt.logger.WithError(err).Error("marshal response failed")
		return err
	}

	// 写入响应
	if _, err := writer.WriteWithStatus(200, responseBytes); err != nil {
		qtt.logger.WithError(err).Error("write response failed")
		return err
	}

	qtt.DoTrafficResponseSamplePb(request, response)

	qtt.zap.L().Debug("QttRtaTrafficBroker success response", zap.String("response", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response)))))

	return nil
}

func (qtt *QttRtaTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		qtt.logger.Infof("[QttRtaTrafficBroker] SendFallbackResponse start")
	}

	response := &qtt_traffic_entity.RTAResponse{
		StatusCode: qtt_traffic_entity.StatusCode_BID_ABANDON,
		Success:    false,
		ReqId:      request.GetRequestId(),
	}

	// 设置响应头
	writer.SetHeader("Content-Type", "application/x-protobuf")

	// 序列化响应
	responseBytes, err := proto.Marshal(response)
	if err != nil {
		qtt.logger.WithError(err).Error("marshal fallback response failed")
		writer.SetHeader("Content-Length", "0")
		writer.SetHeader("Content-Type", "application/x-protobuf")
		writer.WriteWithStatus(200, nil)
		return nil
	}

	// 写入响应
	if _, err := writer.WriteWithStatus(200, responseBytes); err != nil {
		qtt.logger.WithError(err).Error("write fallback response failed")
		return err
	}

	qtt.zap.L().Debug("QttRtaTrafficBroker fail response", zap.String("response", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response)))))
	return nil
}
