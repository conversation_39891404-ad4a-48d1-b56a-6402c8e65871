package rta_traffic_broker

import (
	"fmt"
	"strings"

	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/rta_traffic_broker/rta_traffic_entity/ximalaya_traffic_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type XimalayaRtaTrafficBroker struct {
	traffic_broker.TrafficBrokerBase
	mediaId utils.ID
	logger  *zap.Logger
}

func NewXimalayaRtaTrafficBroker(mediaId utils.ID) *XimalayaRtaTrafficBroker {
	broker := &XimalayaRtaTrafficBroker{
		mediaId: mediaId,
	}
	// 配置日志格式为JSON
	logger := zap.L()
	// logger.SetFormatter converted - configure zap logger instead

	// 设置日志级别
	// logger.SetLevel converted - configure zap logger instead

	// 初始化logger
	// broker.logger converted - use zap.L().With(zap.String("module", "ximalaya_rta"), zap.String("mediaId", fmt.Sprintf("%v", mediaId))) instead

	return broker
}

func (ximalayaRta *XimalayaRtaTrafficBroker) GetMediaId() utils.ID {
	return ximalayaRta.mediaId
}

func (ximalayaRta *XimalayaRtaTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(ximalayaRta.SendResponse)
	request.Response.SetFallbackResponseBuilder(ximalayaRta.SendFallbackResponse)
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[XimalayaRtaTrafficBroker]request body empty")
	}

	if request.IsDebug {
		ximalayaRta.logger.// Logger.SetLevel converted - configure zap logger instead
	} else {
		ximalayaRta.logger.// Logger.SetLevel converted - configure zap logger instead
	}

	rtaReq := &ximalaya_traffic_entity.Request{}
	err := proto.Unmarshal(body, rtaReq)
	if err != nil {
		ximalayaRta.logger.WithError(err).Debug("request body unmarshal failed")
		return fmt.Errorf("request body unmarshal failed: %v", err)
	}

	// 参数验证
	if len(rtaReq.GetRtaIds()) == 0 {
		ximalayaRta.logger.Debug("rtaId is empty")
		return fmt.Errorf("rtaId is empty")
	}

	if rtaReq.GetDevice() == nil {
		ximalayaRta.logger.Debug("device is empty")
		return fmt.Errorf("device is empty")
	}

	// 处理reqid
	reqId := rtaReq.GetReqId()
	if len(reqId) == 0 {
		reqId = utils.NewUUID()
	}
	ximalayaRta.logger = ximalayaRta.logger.WithField("reqId", reqId)
	ximalayaRta.logger.WithField("request_body", rtaReq).Debug("request body")

	request.SetRequestId(reqId)

	// 设置mediaId
	request.SetMediaId(ximalayaRta.mediaId)

	// 设置slotKey
	query := request.GetQuery()
	slotId := query.Get("slot")
	if slotId == "" {
		ximalayaRta.logger.WithField("slotId", slotId).Error("slot id is empty")
		return fmt.Errorf("slot id is empty")
	}
	request.SetMediaSlotKey(slotId)
	request.ImpressionId = request.GetRequestId()

	// 设置设备信息
	request.Device.RequestIp = rtaReq.Device.GetIp()
	request.Device.OsType = ximalayaRta.toOsType(rtaReq.Device.GetOs())
	request.Device.ImeiMd5 = strings.ToLower(rtaReq.Device.GetImeiMd5())
	request.Device.Oaid = rtaReq.Device.GetOaid()
	request.Device.AndroidId = rtaReq.Device.GetAndroidId()
	request.Device.Idfa = rtaReq.Device.GetIdfa()
	request.Device.UserAgent = rtaReq.Device.GetUserAgent()

	// 设置RTA ID
	for _, rtaId := range rtaReq.GetRtaIds() {
		request.RtaIds = append(request.RtaIds, utils.ID(rtaId))
	}

	// 设置创意模板
	key := creative_entity.NewCreativeTemplateKey()
	key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	request.AppendCreativeTemplateKey(key)

	key1 := creative_entity.NewCreativeTemplateKey()
	key1.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	request.AppendCreativeTemplateKey(key1)

	ximalayaRta.DoTrafficSample(request, body)
	return nil
}

func (ximalayaRta *XimalayaRtaTrafficBroker) toOsType(osType int64) entity.OsType {
	switch osType {
	case 2:
		return entity.OsTypeIOS
	case 1:
		return entity.OsTypeAndroid
	default:
		return entity.OsTypeOther
	}
}

func (ximalayaRta *XimalayaRtaTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		request.Response.Dump("XimalayaRtaTrafficBroker")
		ximalayaRta.logger.Infof("[XimalayaRtaTrafficBroker] adCode:%+v", request.Response.GetTotalAdCandidateList().GetErrCodeMap())
	}

	if request.Response.NoCandidate() || len(request.RtaIds) == 0 {
		ximalayaRta.zap.L().Debug("NoCandidate or NoRtaids", zap.String("GetAdCandidateList", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response.GetAdCandidateList())))), zap.String("rtaIds", fmt.Sprintf("%v", request.RtaIds)))
		return ximalayaRta.SendFallbackResponse(request, writer)
	}

	reqId := request.GetRequestId()
	response := &ximalaya_traffic_entity.Response{
		StatusCode: 0,
		ReqId:      reqId,
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		ad := candidate.GetAd()
		dmpResult := ad.GetDmpTagResult()
		if dmpResult == nil {
			ximalayaRta.zap.L().Debug("dmpResult is nil", zap.String("ad", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", ad)))))
			continue
		}

		for _, rtaId := range request.RtaIds {
			if !dmpResult.IsSatisfiedById(rtaId) {
				response.RtaIds = append(response.RtaIds, int64(rtaId))
			}
		}
	}

	if len(response.RtaIds) == 0 {
		ximalayaRta.zap.L().Debug("XimalayaRtaTrafficBroker no rta ids", zap.String("response", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response)))))
		return ximalayaRta.SendFallbackResponse(request, writer)
	}

	// 设置响应头
	writer.SetHeader("Content-Type", "application/octet-stream")

	// 序列化响应
	responseBytes, err := proto.Marshal(response)
	if err != nil {
		ximalayaRta.logger.WithError(err).Error("marshal response failed")
		return err
	}

	// 写入响应
	if _, err := writer.WriteWithStatus(200, responseBytes); err != nil {
		ximalayaRta.logger.WithError(err).Error("write response failed")
		return err
	}

	ximalayaRta.DoTrafficResponseSamplePb(request, response)

	ximalayaRta.zap.L().Debug("XimalayaRtaTrafficBroker success response", zap.String("response", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response)))))

	return nil
}

func (ximalayaRta *XimalayaRtaTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		ximalayaRta.logger.Infof("[XimalayaRtaTrafficBroker] SendFallbackResponse start")
	}

	response := &ximalaya_traffic_entity.Response{
		StatusCode: 1,
		ReqId:      request.GetRequestId(),
	}

	// 设置响应头
	writer.SetHeader("Content-Type", "application/octet-stream")

	// 序列化响应
	responseBytes, err := proto.Marshal(response)
	if err != nil {
		ximalayaRta.logger.WithError(err).Error("marshal fallback response failed")
		writer.SetHeader("Content-Length", "0")
		writer.WriteWithStatus(200, nil)
		return nil
	}

	// 写入响应
	if _, err := writer.WriteWithStatus(200, responseBytes); err != nil {
		ximalayaRta.logger.WithError(err).Error("write fallback response failed")
		return err
	}

	ximalayaRta.zap.L().Debug("XimalayaRtaTrafficBroker fail response", zap.String("response", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response)))))
	return nil
}
