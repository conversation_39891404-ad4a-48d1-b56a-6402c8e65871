package rta_traffic_broker

import (
	"fmt"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/rta_traffic_broker/rta_traffic_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

const (
	RTAReqId = "reqId"
)

type RtaTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	mediaId      utils.ID
	host         string
	clientSecret string
}

func NewRtaTrafficBroker(mediaId utils.ID, clientSecret string) *RtaTrafficBroker {
	return &RtaTrafficBroker{
		mediaId:      mediaId,
		clientSecret: clientSecret,
	}
}

func (mb *RtaTrafficBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *RtaTrafficBroker) CheckSecret(request *ad_service.AdRequest) error {
	if mb.clientSecret == "" {
		return err_code.ErrEmptySecret
	}

	reqId := request.GetRawHttpHeader("X-RTA-ID")

	if len(reqId) == 0 {
		return err_code.ErrEmptyHeaderXADSTime

	}

	reqAdsKey := request.GetRawHttpHeader("X-RTA-SECRET")
	if len(reqAdsKey) == 0 {
		return err_code.ErrEmptyHeaderXADSKey
	}

	adsKey := md5_utils.GetMd5String(reqId + mb.clientSecret)

	if adsKey != reqAdsKey {
		return err_code.ErrSecretCheck
	}

	return nil
}

func (mb *RtaTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)

	err := mb.CheckSecret(request)
	if err != nil {
		//zap.L().Error("[RtaTrafficBroker]Check secret error, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[RtaTrafficBroker]request body empty")
	}

	bidRequest := &rta_traffic_entity.RTARequest{}
	err = sonic.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[RtaTrafficBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if request.IsDebug {
		zap.L().Info("[RtaTrafficBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", body)))))
	}

	if len(bidRequest.RtaIds) == 0 {
		return fmt.Errorf("request rta ids empty")
	}

	request.SetRequestId(bidRequest.ReqId)
	if len(bidRequest.ReqId) < 1 {
		request.SetRequestId(utils.NewUUID())
	}

	request.AddMediaExtraString(RTAReqId, request.GetRequestId())

	request.SetMediaId(mb.mediaId)

	request.ImpressionId = request.GetRequestId()
	request.SetMediaSlotKey(bidRequest.ChannelId)
	for _, rtaId := range bidRequest.RtaIds {
		rtaId1 := utils.ID(type_convert.GetAssertInt(rtaId))
		if rtaId1 != 0 {
			request.RtaIds = append(request.RtaIds, rtaId1)
		}
	}

	request.Device.OsType = entity.OsType(bidRequest.Os)
	request.Device.OsVersion = bidRequest.Osv
	request.Device.Imei = bidRequest.Imei
	request.Device.ImeiMd5 = bidRequest.ImeiMd5
	request.Device.AndroidId = bidRequest.AndroidId
	request.Device.AndroidIdMd5 = bidRequest.AndroidIdMd5
	request.Device.Oaid = bidRequest.Oaid
	request.Device.OaidMd5 = bidRequest.OaidMd5
	request.Device.Idfa = bidRequest.Idfa
	request.Device.IdfaMd5 = bidRequest.IdfaMd5
	request.Device.Caid = bidRequest.Caid
	request.Device.CaidMd5 = bidRequest.CaidMd5
	request.Device.Caids = bidRequest.Caids
	request.Device.RequestIp = bidRequest.Ip
	request.Device.UserAgent = bidRequest.Ua
	request.Device.Model = bidRequest.Model
	request.Device.Brand = bidRequest.Brand

	//hack template
	key := creative_entity.NewCreativeTemplateKey()
	key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	request.AppendCreativeTemplateKey(key)

	key1 := creative_entity.NewCreativeTemplateKey()
	key1.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	request.AppendCreativeTemplateKey(key1)

	mb.DoTrafficSample(request, body)

	return nil

}

func (mb *RtaTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		request.Response.Dump("RtaTrafficBroker")
		zap.L().Info("[RtaTrafficBroker] adCode:%+v").GetErrCodeMap())
	}

	if request.Response.NoCandidate() || len(request.RtaIds) == 0 {
		return mb.SendFallbackResponse(request, writer)
	}
	reqId := request.GetMediaExtraString(RTAReqId, "")
	if len(reqId) == 0 {
		reqId = request.GetRequestId()
	}

	response := &rta_traffic_entity.RTAResponse{
		Code:   0,
		RtaIds: make([]string, 0),
		ReqId:  reqId,
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		ad := candidate.GetAd()
		dmpResult := ad.GetDmpTagResult()
		if dmpResult == nil {
			continue
		}

		for _, rtaId := range request.RtaIds {
			if dmpResult.IsSatisfiedById(rtaId) {
				response.RtaIds = append(response.RtaIds, type_convert.GetAssertString(int(rtaId)))
			}
		}
	}

	if len(response.RtaIds) == 0 {
		return mb.SendFallbackResponse(request, writer)
	}

	if err := mb.BuildHttpSonicJsonResponse(request, writer, &response); err != nil {
		return err
	}

	mb.DoTrafficResponseSampleSonicJson(request, response)

	if request.IsDebug {
		resBody, _ := sonic.Marshal(response)
		zap.L().Info("[RtaTrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}

	return nil
}

func (mb *RtaTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[RtaTrafficBroker] SendFallbackResponse start")
	}

	errCode := int(request.Response.GetAdErrorCode().Code)
	if errCode == 0 {
		errCode = 1
	}
	response := &rta_traffic_entity.RTAResponse{
		Code: errCode,
	}

	if err := mb.BuildHttpSonicJsonResponse(request, writer, &response); err != nil {
		writer.SetHeader("Content-Length", "0")
		writer.SetHeader("Content-Type", "application/json;charset=utf-8")
		writer.WriteWithStatus(200, nil)
		return nil
	}
	return nil
}
