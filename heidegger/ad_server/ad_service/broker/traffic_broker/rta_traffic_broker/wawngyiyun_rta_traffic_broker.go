package rta_traffic_broker

import (
	"fmt"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/rta_traffic_broker/rta_traffic_entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

const wangyiyunTmpId = "wangyiyun_tmp_id"

type WangYiYunRtaTrafficBroker struct {
	traffic_broker.TrafficBrokerBase
	mediaId utils.ID
	logger  *zap.Logger
}

func NewWangYiYunRtaTrafficBroker(mediaId utils.ID) *WangYiYunRtaTrafficBroker {
	broker := &WangYiYunRtaTrafficBroker{
		mediaId: mediaId,
	}

	// 配置日志格式为JSON
	logger := zap.L()
	// logger.SetFormatter converted - configure zap logger instead

	// 设置日志级别
	// logger.SetLevel converted - configure zap logger instead

	// 初始化logger
	// broker.logger converted - use zap.L().With(zap.String("module", "wangyiyun_rta"), zap.String("mediaId", fmt.Sprintf("%v", mediaId))) instead

	return broker
}

func (wyy *WangYiYunRtaTrafficBroker) GetMediaId() utils.ID {
	return wyy.mediaId
}

func (wyy *WangYiYunRtaTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(wyy.SendResponse)
	request.Response.SetFallbackResponseBuilder(wyy.SendFallbackResponse)
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[WangYiYunRtaTrafficBroker]request body empty")
	}

	if request.IsDebug {
		wyy.logger.// Logger.SetLevel converted - configure zap logger instead
	} else {
		wyy.logger.// Logger.SetLevel converted - configure zap logger instead
	}

	rtaReq := &rta_traffic_entity.WangYiYunRtaRequest{}
	err := sonic.Unmarshal(body, rtaReq)
	if err != nil {
		wyy.logger.WithError(err).Debug("request body is empty")
		return fmt.Errorf("request body is empty")
	}

	// 参数验证
	if len(rtaReq.RtaPlanIDs) == 0 {
		wyy.logger.Debug("RtaPlanIDs are both empty")
		return fmt.Errorf("RtaPlanIDs are both empty")
	}

	if rtaReq.Device == nil {
		wyy.logger.Debug("Device are both empty")
		return fmt.Errorf("Device are both empty")
	}

	if len(rtaReq.ReqID) == 0 {
		wyy.logger.Debug("RequestId is empty")
		return fmt.Errorf("RequestId is empty")
	}

	request.SetRequestId(rtaReq.ReqID)
	request.SetMediaId(wyy.mediaId)
	query := request.GetQuery()
	slotId := query.Get("slot")
	rtaId := query.Get("tag")

	if slotId == "" || rtaId == "" {
		wyy.logger.WithField("query", query).Error("slot id || rtaId is empty")
		return fmt.Errorf("slot id || rtaId is empty")
	}
	request.SetMediaSlotKey(slotId)
	request.RtaIds = append(request.RtaIds, utils.ID(type_convert.GetAssertInt(rtaId)))

	// 处理RTA ID列表
	request.AddMediaExtraData(wangyiyunTmpId, rtaReq.RtaPlanIDs)

	// 设置设备信息
	rtaDevice := rtaReq.Device
	request.Device.Oaid = rtaDevice.Oaid
	request.Device.OaidMd5 = rtaDevice.OaidMd5
	request.Device.Idfa = rtaDevice.Idfa
	request.Device.IdfaMd5 = rtaDevice.IdfaMd5
	request.Device.ImeiMd5 = rtaDevice.ImeiMd5
	if len(rtaDevice.Caid) != 0 && len(rtaDevice.CaidVersion) != 0 {
		request.Device.CaidRaw = rtaDevice.Caid
		request.Device.CaidVersion = rtaDevice.CaidVersion
		request.Device.Caid = rtaDevice.CaidVersion + "_" + rtaDevice.Caid
		request.Device.Caids = append(request.Device.Caids, request.Device.Caid)
	}

	if len(rtaDevice.PreCaid) != 0 && len(rtaDevice.PreCaidVersion) != 0 {
		preCaid := rtaDevice.PreCaidVersion + "_" + rtaDevice.PreCaid
		request.Device.Caids = append(request.Device.Caids, preCaid)
	}

	// hack template
	key := creative_entity.NewCreativeTemplateKey()
	key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	request.AppendCreativeTemplateKey(key)

	key1 := creative_entity.NewCreativeTemplateKey()
	key1.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	request.AppendCreativeTemplateKey(key1)

	wyy.DoTrafficSample(request, body)
	return nil
}

func (wyy *WangYiYunRtaTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		request.Response.Dump("WangYiYunRtaTrafficBroker")
		wyy.logger.Infof("[WangYiYunRtaTrafficBroker] adCode:%+v", request.Response.GetTotalAdCandidateList().GetErrCodeMap())
	}

	if request.Response.NoCandidate() || len(request.RtaIds) == 0 {
		wyy.zap.L().Debug("NoCandidate or NoRtaids", zap.String("GetAdCandidateList", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response.GetAdCandidateList())))), zap.String("rtaIds", fmt.Sprintf("%v", request.RtaIds)))
		return wyy.SendFallbackResponse(request, writer)
	}

	response := &rta_traffic_entity.WangYiYunRTAResponse{
		ReqID: request.GetRequestId(),
		Code:  0,
	}

	// 获取rtaPalnIds
	rtaPalnIds := []string{}
	rtaPlanIdInf, ok := request.GetMediaExtraData(wangyiyunTmpId)
	if !ok {
		wyy.logger.WithField("rtaPlanIds", rtaPlanIdInf).Error("获取rtaPlanIds 失败")
	} else {
		rtaPalnIds = rtaPlanIdInf.([]string)
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		ad := candidate.GetAd()
		dmpResult := ad.GetDmpTagResult()
		if dmpResult == nil {
			continue
		}

		for _, rtaId := range request.RtaIds {
			var planRes rta_traffic_entity.WangYiYunPlanResult
			// 是否投放，0表示否，1表示是
			isBidding := 1
			if !dmpResult.IsSatisfiedById(rtaId) {
				isBidding = 0
			}
			for _, planId := range rtaPalnIds {
				planRes = rta_traffic_entity.WangYiYunPlanResult{RtaPlanID: planId, IsBidding: isBidding}
				response.PlanResults = append(response.PlanResults, planRes)
			}
		}
	}

	if err := wyy.BuildHttpSonicJsonResponse(request, writer, &response); err != nil {
		return err
	}

	wyy.DoTrafficResponseSampleSonicJson(request, response)

	wyy.zap.L().Debug("WangYiYunRtaTrafficBroker success response", zap.String("response", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response)))), zap.String("request", fmt.Sprintf("%v", request.RawHttpRequest.GetBodyContent())))

	return nil
}

func (wyy *WangYiYunRtaTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		wyy.logger.Infof("[WangYiYunRtaTrafficBroker] SendFallbackResponse start")
	}

	response := &rta_traffic_entity.WangYiYunRTAResponse{
		Code:  -1,
		ReqID: request.GetRequestId(),
	}

	if err := wyy.BuildHttpSonicJsonResponse(request, writer, &response); err != nil {
		writer.SetHeader("Content-Length", "0")
		writer.SetHeader("Content-Type", "application/json;charset=utf-8")
		writer.WriteWithStatus(400, nil)
		wyy.logger.WithError(err).WithField("response", response).Info("BuildHttpSonicJsonResponse error")
		return nil
	}
	wyy.zap.L().Debug("WangYiYunRtaTrafficBroker fail response", zap.String("response", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response)))))
	return nil
}
