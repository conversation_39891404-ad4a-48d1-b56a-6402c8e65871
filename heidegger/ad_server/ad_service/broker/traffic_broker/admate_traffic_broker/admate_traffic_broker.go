package admate_traffic_broker

import (
	"errors"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/admate_traffic_broker/admate_broker_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
)

const (
	adxTemplateKey = "adxTemplate"
	templateId     = "templateId"
)

type AdMateTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        *zap.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewAdMateTrafficBroker(mediaId utils.ID) *AdMateTrafficBroker {
	return &AdMateTrafficBroker{
		log:     zap.L().With(zap.String("broker", "AdMateTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "%%PRICE%%",
			MediaClickDownXMacro: "__DOWN_X__",
			MediaClickDownYMacro: "__DOWN_Y__",
			MediaClickUpXMacro:   "__UP_X__",
			MediaClickUpYMacro:   "__UP_Y__",
		},
	}
}

func (a *AdMateTrafficBroker) GetMediaId() utils.ID {
	return a.mediaId
}

func (a *AdMateTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(a.SendResponse)
	request.Response.SetFallbackResponseBuilder(a.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = a.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = a.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &admate_broker_entity.BidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		a.log.WithError(err).Error("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		a.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	request.SetMediaId(a.GetMediaId())
	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}
	if bidRequest.Tmax > 0 {
		request.TMax = bidRequest.Tmax
	}

	a.parseApp(bidRequest, request)
	a.parseUser(bidRequest, request)
	a.parseDevice(bidRequest, request)
	if err = a.parseImp(bidRequest, request); err != nil {
		a.log.WithError(err).Error("BrokeRequest, parseImp failed")
		return err
	}

	a.DoTrafficSample(request, body)

	return nil
}

func (a *AdMateTrafficBroker) parseApp(request *admate_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.App != nil {
		adRequest.App.AppName = request.App.Name
		adRequest.App.AppVersion = request.App.Ver
		adRequest.App.AppBundle = request.App.Bundle
	}
}

func (a *AdMateTrafficBroker) parseUser(request *admate_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.User != nil {
		switch request.User.Gender {
		case "F":
			adRequest.UserGender = entity.UserGenderWoman
		case "M":
			adRequest.UserGender = entity.UserGenderMan
		}
	}
}

func (a *AdMateTrafficBroker) parseDevice(request *admate_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.Device == nil {
		return
	}

	adRequest.Device = ad_service.AdRequestDevice{
		OsType:            mappingOsType(request.Device.Os),
		OsVersion:         request.Device.Osv,
		DeviceType:        mappingDeviceType(request.Device.DeviceType),
		IsMobile:          request.Device.DeviceType == 2,
		DeviceInitTime:    request.Device.BirthTime,
		DeviceStartupTime: request.Device.BootTime,
		DeviceUpgradeTime: request.Device.UpdateTime,
		BootMark:          request.Device.BootMark,
		UpdateMark:        request.Device.UpdateMark,
		VercodeAg:         request.Device.AppstoreVer,
		VercodeHms:        request.Device.HmsCore,
		Idfa:              request.Device.Idfa,
		Imei:              request.Device.Imei,
		Imsi:              request.Device.Imsi,
		ImsiMd5:           md5_utils.GetMd5String(request.Device.Imsi),
		Mac:               request.Device.Mac,
		Oaid:              request.Device.Oaid,
		OaidMd5:           request.Device.OaidMd5,
		AndroidId:         request.Device.AndroidId,
		Model:             request.Device.Model,
		Brand:             request.Device.Make,
		Vendor:            request.Device.Make,
		Language:          request.Device.Lan,
		CountryCode:       "CN",
		ScreenOrientation: mappingScreenOrientation(request.Device.Orientation),
		UserAgent:         request.Device.Ua,
		WebviewUA:         request.Device.Ua,
		RequestIp:         request.Device.Ip,
		ConnectionType:    mappingConnectionType(request.Device.ConnectionType),
		OperatorType:      mappingOperatorType(request.Device.Carrier),
		OperatorName:      mappingOperatorType(request.Device.Carrier).String(),
	}

	if request.Site != nil {
		adRequest.Device.Referer = request.Site.Ref
	}

	if len(request.Device.Ipv6) > 0 {
		adRequest.Device.IsIp6 = true
		adRequest.Device.RequestIp = request.Device.Ipv6
	}
	if len(request.Device.Ppi) > 0 {
		ppi, _ := strconv.ParseInt(request.Device.Ppi, 10, 64)
		adRequest.Device.PPI = int32(ppi)
	}

	if request.Device.Geo != nil {
		adRequest.Device.Lat = request.Device.Geo.Lat
		adRequest.Device.Lon = request.Device.Geo.Lon
	}

	if len(request.Device.AndroidId) == 32 {
		adRequest.Device.AndroidIdMd5 = request.Device.AndroidId
	} else {
		adRequest.Device.AndroidIdMd5 = md5_utils.GetMd5String(request.Device.AndroidId)
	}
	if len(request.Device.Imei) == 32 {
		adRequest.Device.ImeiMd5 = request.Device.Imei
	} else {
		adRequest.Device.ImeiMd5 = md5_utils.GetMd5String(request.Device.Imei)
	}
	if len(request.Device.Mac) == 32 {
		adRequest.Device.MacMD5 = request.Device.Mac
	} else {
		adRequest.Device.MacMD5 = md5_utils.GetMd5String(request.Device.Mac)
	}
	if len(request.Device.Idfa) == 32 {
		adRequest.Device.IdfaMd5 = request.Device.Idfa
	} else {
		adRequest.Device.IdfaMd5 = md5_utils.GetMd5String(request.Device.Idfa)
	}

	if len(request.Device.Caid) > 0 {
		split := strings.Split(request.Device.Caid, ",")
		for _, caid := range split {
			if len(caid) == 0 {
				continue
			}
			idx := strings.IndexByte(caid, '_')
			if idx > -1 {
				adRequest.Device.CaidRaw = caid[:idx]
				adRequest.Device.CaidVersion = caid[idx+1:]
			}
			if len(adRequest.Device.Caid) <= 0 {
				adRequest.Device.Caid = caid
			} else {
				adRequest.Device.Caids = append(adRequest.Device.Caids, caid)
			}
		}
	}

	h, _ := strconv.ParseInt(request.Device.H, 10, 64)
	adRequest.Device.ScreenHeight = int32(h)
	w, _ := strconv.ParseInt(request.Device.W, 10, 64)
	adRequest.Device.ScreenWidth = int32(w)

	if len(request.Device.Ext) > 0 {
		adRequest.App.MediaInstalledAppIds = strings.Split(request.Device.Ext, ".")
	}
}

func (a *AdMateTrafficBroker) parseImp(request *admate_broker_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	if len(request.Imp) == 0 {
		return errors.New("impressions empty")
	}

	for _, imp := range request.Imp {
		if imp == nil {
			continue
		}

		adRequest.ImpressionId = imp.Id
		adRequest.SetMediaSlotKey(imp.Pid)
		adRequest.BidFloor = imp.BidFloor
		adRequest.UseHttps = request.Secure == 1
		adRequest.BidType = entity.BidTypeCpm
		if imp.BidType == 1 { // 暂不支持，但也加上逻辑
			adRequest.BidType = entity.BidTypeCpc
		}

		if imp.Pmp != nil {
			for _, deal := range imp.Pmp.Deals {
				if deal != nil {
					adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
						DealId:   deal.Id,
						BidFloor: int64(deal.Price),
					})
				}
			}
		}

		adxTemplateMap := make(map[uint64]int)

		if imp.Banner != nil {
			adRequest.SlotWidth = imp.Banner.W
			adRequest.SlotHeight = imp.Banner.H

			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = 0
		} else if imp.Video != nil {
			adRequest.SlotWidth = imp.Video.W
			adRequest.SlotHeight = imp.Video.H
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = 0
		} else if imp.NativeAd != nil {
			for _, asset := range imp.NativeAd.Assets {
				if asset == nil {
					continue
				}
				adRequest.AddMediaExtraString(templateId, asset.Id)
				/*
					模板列表：
					1-1（一图一文），1-8（两图一文），1-4（三图一文）
					1-2（图文摘要）
					2-2（原生信息流视频）
					2-3（音频贴片）
					2-4（激励视频）
				*/
				switch asset.Id {
				case "1-1", "1-2", "1-4", "1-8":
					adRequest.SlotWidth = asset.W
					adRequest.SlotHeight = asset.H

					imgCount := 1
					if asset.Id == "1-8" {
						imgCount = 3
					} else if asset.Id == "1-4" {
						imgCount = 2
					}

					key := creative_entity.NewCreativeTemplateKey()
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1).SetOptional(true)
					key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
					key.Image().AddRequiredCount(imgCount).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
					adRequest.AppendCreativeTemplateKey(key)
					adxTemplateMap[key.Uint64()] = 0
				case "2-2", "2-4":
					adRequest.SlotWidth = asset.W
					adRequest.SlotHeight = asset.H

					key := creative_entity.NewCreativeTemplateKey()
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1).SetOptional(true)
					key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
					key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
					key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
					adRequest.AppendCreativeTemplateKey(key)
					adxTemplateMap[key.Uint64()] = 0
				}

				if len(adxTemplateMap) > 0 {
					break
				}
			}
		}

		if len(adxTemplateMap) == 0 {
			return errors.New("impressions empty")
		}

		adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)
		break
	}

	return nil
}

func (a *AdMateTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("AdMate")
	}

	if request.Response.NoCandidate() {
		return a.SendFallbackResponse(request, writer)
	}

	bidResponse, err := a.buildResponse(request)
	if err != nil {
		a.log.WithError(err).Error("buildResponse err")
		return err
	}

	err = a.BuildHttpSonicJsonResponse(request, writer, bidResponse)
	if err != nil {
		return err
	}

	a.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		a.log.Infof("SendResponse success, response: %s", responseStr)
	}

	return nil
}

func (a *AdMateTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Infof("Build Fallback Response start. bid response: [%v]", request.Response)
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	_, _ = writer.WriteWithStatus(204, nil)
	return nil
}

func (a *AdMateTrafficBroker) buildResponse(request *ad_service.AdRequest) (*admate_broker_entity.BidResponse, error) {
	bidResponse := &admate_broker_entity.BidResponse{
		Id: request.GetRequestId(),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		var iKey string
		if request.GetMediaInfo() != nil {
			iKey = request.GetMediaInfo().Ikey
		}

		resBid := &admate_broker_entity.ResBid{
			AderID:      iKey, //广告主Id用iKey配置
			CID:         "",   //白名单广告主不要填写，填了后必审核素材
			Price:       int(candidate.GetBidPrice().Price),
			DURL:        candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
			ADCK:        1,
			AdType:      1,
			DeepLink:    genericAd.GetDeepLinkUrl(),
			NURL:        candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			CURL:        candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
			DPSucc:      genericAd.GetDeepLinkMonitorList(),
			DPFail:      genericAd.GetDeepLinkFailedMonitorList(),
			DNStart:     genericAd.GetAppDownloadStartedMonitorList(),
			DNSucc:      genericAd.GetAppDownloadFinishedMonitorList(),
			DNInstStart: genericAd.GetAppInstallStartMonitorList(),
			DNInstSucc:  genericAd.GetAppInstalledMonitorList(),
			DNActive:    genericAd.GetAppOpenMonitorList(),
			TemplateID:  request.GetMediaExtraString(templateId, utils.EmptyString),
		}

		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			resBid.ADCK = 2
		}

		if candidate.GetIndexDeal() != nil {
			resBid.DealID = candidate.GetIndexDeal().DealId
		}

		var hasVideo bool
		adm := &admate_broker_entity.Template{}
		var imgUrl, videoUrl string
		for _, material := range candidate.GetSelectedMaterialList() {
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				if len(resBid.TemplateID) > 0 {
					adm.Title = material.Data
				}
			case entity.MaterialTypeDesc:
				if len(resBid.TemplateID) > 0 {
					adm.ActionText = material.Data
				}
			case entity.MaterialTypeCoverImage:
				if len(resBid.TemplateID) > 0 {
					adm.Cover = material.Url
				} else {
					resBid.VideoCover = material.Url
				}
			case entity.MaterialTypeIcon:
				if len(resBid.TemplateID) > 0 {
					adm.Icon = material.Url
				}
			case entity.MaterialTypeImage:
				if len(resBid.TemplateID) > 0 {
					if resBid.TemplateID == "2-4" {
						adm.Cover = material.Url
					} else {
						adm.ImgUrl = candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen)
					}
				} else {
					imgUrl = candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen)
					resBid.ADM = imgUrl
				}
				resBid.W = material.Width
				resBid.H = material.Height
			case entity.MaterialTypeVideo:
				if len(resBid.TemplateID) > 0 {
					adm.VideoUrl = candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen)
					adm.VideoDuration = int(material.Duration)
				} else {
					videoUrl = candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen)
					resBid.ADM = videoUrl
					resBid.VideoDuration = int(material.Duration)
				}
				resBid.W = material.Width
				resBid.H = material.Height
				hasVideo = true
			default:
			}
		}
		if hasVideo {
			resBid.AdType = 2
			if len(resBid.TemplateID) <= 0 {
				if len(resBid.VideoCover) == 0 {
					resBid.VideoCover = imgUrl
				}
				resBid.ADM = videoUrl
			}
		}
		if len(resBid.TemplateID) > 0 {
			marshal, _ := sonic.Marshal(adm)
			resBid.ADM = string(marshal)
		}

		if genericAd.GetAppInfo() != nil {
			resBid.PackageName = genericAd.GetAppInfo().PackageName
			resBid.AppName = genericAd.GetAppInfo().AppName
			resBid.AppVersion = genericAd.GetAppInfo().AppVersion
			resBid.AppIcon = genericAd.GetAppInfo().Icon
			resBid.AppSize = strconv.Itoa(genericAd.GetAppInfo().PackageSize)
			resBid.AppPermissionURL = genericAd.GetAppInfo().Permission
			resBid.PrivacyAgreement = genericAd.GetAppInfo().Privacy
			resBid.Developer = genericAd.GetAppInfo().Develop
			resBid.AppIntroURL = genericAd.GetAppInfo().AppDescURL

			if genericAd.GetAppInfo().WechatExt != nil {
				resBid.WxUsername = genericAd.GetAppInfo().WechatExt.ProgramId
				resBid.WxPath = genericAd.GetAppInfo().WechatExt.ProgramPath
			}
		}

		bidResponse.Seatbid = []*admate_broker_entity.ResSeatBid{{Bid: []*admate_broker_entity.ResBid{resBid}}}
		break
	}

	return bidResponse, nil
}

func mappingOsType(os string) entity.OsType {
	switch strings.ToLower(os) {
	case "android":
		return entity.OsTypeAndroid
	case "ios":
		return entity.OsTypeIOS
	case "wp":
		return entity.OsTypeWindowsPhone
	default:
		return entity.OsTypeUnknown
	}
}

func mappingScreenOrientation(orientation string) entity.ScreenOrientationType {
	switch orientation {
	case "0":
		return entity.ScreenOrientationTypePortrait
	case "1":
		return entity.ScreenOrientationTypeLandscape
	default:
		return entity.ScreenOrientationTypeUnknown
	}
}

func mappingOperatorType(carrier string) entity.OperatorType {
	switch carrier {
	case "46000", "46002", "46007", "46008", "46020":
		return entity.OperatorTypeChinaMobile
	case "46001", "46006", "46009":
		return entity.OperatorTypeChinaUnicom
	case "46003", "46005", "46011":
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType int) entity.ConnectionType {
	switch connectionType {
	case 1:
		return entity.ConnectionTypeNetEthernet
	case 2:
		return entity.ConnectionTypeWifi
	case 3:
		return entity.ConnectionType2G
	case 4:
		return entity.ConnectionType3G
	case 5:
		return entity.ConnectionType4G
	case 6:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingDeviceType(deviceType int) entity.DeviceType {
	switch deviceType {
	case 2:
		return entity.DeviceTypeMobile
	case 3:
		return entity.DeviceTypePad
	case 6:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeUnknown
	}
}
