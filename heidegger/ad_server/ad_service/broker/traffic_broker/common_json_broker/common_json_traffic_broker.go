package common_json_broker

import (
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/common_json_broker/common_json_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
)

const requestSourceKey = "requestSource"

type (
	CommonJsonTrafficBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId    utils.ID
		MediaMacro *macro_builder.MediaMacro
	}
)

func NewCommonJsonTrafficBroker(mediaId utils.ID) *CommonJsonTrafficBroker {
	return &CommonJsonTrafficBroker{
		mediaId: mediaId,
		MediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "__PRICE__",
			MediaClickDownXMacro: "__DOWN_X__",
			MediaClickDownYMacro: "__DOWN_Y__",
			MediaClickUpXMacro:   "__UP_X__",
			MediaClickUpYMacro:   "__UP_Y__",
			MediaHWSldMacro:      "__HW_SLD__",
		},
	}
}

func (mb *CommonJsonTrafficBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *CommonJsonTrafficBroker) Do(request *ad_service.AdRequest) error {
	return mb.ParseAdRequest(request)
}

func (mb *CommonJsonTrafficBroker) ParseAdRequest(mRequest *ad_service.AdRequest) error {
	mRequest.Response.SetResponseBuilder(mb.SendResponse)
	mRequest.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	mRequest.AdRequestMedia.WinPriceMacro = mb.MediaMacro.MediaPriceMacro
	mRequest.AdRequestMedia.MediaMacro = mb.MediaMacro

	body := mRequest.RawHttpRequest.GetBodyContent()

	if len(body) == 0 {
		return fmt.Errorf("[CommonJsonTrafficBroker]request body empty")
	}

	bidRequest := &common_json_entity.CommonJsonBrokerRequest{}
	//err := easyjson.Unmarshal(body, bidRequest)
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[CommonJsonTrafficBroker]BrokeRequest, Request body unmarshal failed, err:, body", zap.Error(err), zap.String("param2", fmt.Sprintf("%v", string(body))))
		return fmt.Errorf("request body invalid")
	}

	if mb.mediaId == 1015 {
		mRequest.IsDebug = false
	}

	if mRequest.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[CommonJsonTrafficBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	mRequest.IsTest = bidRequest.Test
	mRequest.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		mRequest.SetRequestId(utils.NewUUID())
	}
	mRequest.SetMediaId(mb.mediaId)

	if err := mb.parseApp(bidRequest, mRequest); err != nil {
		zap.L().Debug("[CommonJsonTrafficBroker]BrokeRequest, parseApp failed")
		return err
	}

	if err := mb.parseUser(bidRequest, mRequest); err != nil {
		zap.L().Debug("[CommonJsonTrafficBroker]BrokeRequest, parseUser failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, mRequest); err != nil {
		zap.L().Debug("[CommonJsonTrafficBroker]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseImp(bidRequest, mRequest); err != nil {
		zap.L().Debug("[CommonJsonTrafficBroker] BrokeRequest, parseImp failed")
		return err
	}

	if err := mb.parseSite(bidRequest, mRequest); err != nil {
		zap.L().Debug("[CommonJsonTrafficBroker] BrokeRequest, parseSite failed")
		return err
	}

	if mRequest.Device.RequestIp == "client" || len(mRequest.Device.RequestIp) < 1 {
		clientIp, _ := net_utils.ParseIpPort(mRequest.RawHttpRequest.GetRemoteAddress())
		if clientIp != nil {
			mRequest.Device.RequestIp = clientIp.String()
		}
	}

	// json proto v1 compatible
	if len(bidRequest.Ext) > 0 {
		ext := new(common_json_entity.RequestExt)
		if err := sonic.Unmarshal(bidRequest.Ext, ext); err == nil {
			if len(ext.Source) > 0 {
				mRequest.AddMediaExtraString(requestSourceKey, ext.Source)
			}
		}
	}

	mb.DoTrafficSample(mRequest, body)

	return nil
}

func (mb *CommonJsonTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("CommonJsonTrafficBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("CommonJsonTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}

	bidResponse := &common_json_entity.CommonJsonBidResponse{}

	bidResponse.Id = request.GetRequestId()
	bidResponse.BidId = request.GetRequestId()

	bidResponse.SeatBid = make([]common_json_entity.CommonJsonBrokerResponseSeatBid, 1)
	bidResponse.SeatBid[0].Bid = make([]common_json_entity.CommonJsonBrokerResponseSeatBidBid, 0)

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()

		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		bid := common_json_entity.CommonJsonBrokerResponseSeatBidBid{
			Adm: &common_json_entity.Adm{},
		}

		bid.Id = request.GetRequestId()
		bid.ImpId = request.ImpressionId
		bidPrice := candidate.GetBidPrice()
		bid.Price = float32(bidPrice.Price)
		//bid.DealId = seatBid.DealId
		bid.Crid = strconv.Itoa(int(creative.GetCreativeId()))
		if creative.GetCreativeId() == 0 {
			bid.Crid = creative.GetCreativeKey()
		}

		bid.Ext = &common_json_entity.BidExt{}
		if genericAd.GetAdExtInfo() != nil {
			bid.Ext.XRequestWithPackagename = genericAd.GetAdExtInfo().XRequestWithPackageName
			bid.Ext.ReplacedHost = genericAd.GetAdExtInfo().ReplacedHost
			bid.Ext.XUserAgent = genericAd.GetAdExtInfo().XUserAgent
		}
		if request.GetMediaExtraString(requestSourceKey, utils.EmptyString) == "huawei" {
			bid.Ext.Language = "zh"
			// 广告主的品牌，使用落地页域名替换，后续可自行修改
			bid.Ext.Advertiser = net_utils.GetDomainFromUrl(genericAd.GetLandingUrl())
			bid.Ext.HuaweiCat = "3201" // 综合电商
		}

		//bid.NUrl =
		bid.ImpTrackers = candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
		bid.ClickTrackers = candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen)
		if len(genericAd.GetAppDownloadStartedMonitorList()) > 0 {
			bid.DownStartTrackers = candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadStartedMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetAppDownloadFinishedMonitorList()) > 0 {
			bid.DownCompTrackers = candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadFinishedMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetAppInstalledMonitorList()) > 0 {
			bid.InstallCompTrackers = candidate.ReplaceUrlMacroList(genericAd.GetAppInstalledMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetAppInstallStartMonitorList()) > 0 {
			bid.InstallStartTrackers = candidate.ReplaceUrlMacroList(genericAd.GetAppInstallStartMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			bid.DpTrackers = candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetVideoStartUrlList()) > 0 {
			for _, videoStart := range genericAd.GetVideoStartUrlList() {
				bid.VideoTrackers = append(bid.VideoTrackers, common_json_entity.VideoTracker{
					Event: 0,
					Url:   candidate.ReplaceUrlMacro(videoStart, traffic, trackingGen),
				})
			}
		}

		if len(genericAd.GetVideoCloseUrlList()) > 0 {
			bid.VideoCloseTrackers = candidate.ReplaceUrlMacroList(genericAd.GetVideoCloseUrlList(), traffic, trackingGen)
		}

		if len(genericAd.GetAppOpenMonitorList()) > 0 {
			bid.AppOpenTrackers = candidate.ReplaceUrlMacroList(genericAd.GetAppOpenMonitorList(), traffic, trackingGen)
		}

		//bid.SdkSwitchExt = seatBid.BidExtData.SdkSwitchExt

		for _, ev := range genericAd.GetDelayMonitorUrlList() {
			bid.VideoTrackers = append(bid.VideoTrackers, common_json_entity.VideoTracker{
				Event: int(mb.RoundSeconds(ev.Delay)),
				Url:   ev.Url,
			})
		}

		hasImage := false
		hasVideo := false
		hasText := false
		adm := bid.Adm
		if adm.Ext == nil {
			adm.Ext = &common_json_entity.Ext{}
		}
		//for _, rsc := range creative.GetMaterialList() {
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				img := &common_json_entity.Img{
					Url:  candidate.ReplaceUrlMacro(rsc.Url, traffic, trackingGen),
					W:    rsc.Width,
					H:    rsc.Height,
					Mime: rsc.MimeType.String(),
				}
				adm.Img = append(adm.Img, img)
				if strings.Contains(rsc.Url, ".png") {
					img.Mime = "image/png"
				} else if strings.Contains(rsc.Url, ".gif") {
					img.Mime = "image/gif"
				} else {
					img.Mime = "image/jpg"
				}
				hasImage = true
			case entity.MaterialTypeIcon:
				adm.Ext.Icon = rsc.Url
			case entity.MaterialTypeLogo:
				adm.Ext.Logo = rsc.Url
			case entity.MaterialTypeTitle:
				adm.Title = rsc.Data
				hasText = true
			case entity.MaterialTypeDesc:
				adm.Desc = rsc.Data
				hasText = true
			//case dict.MaterialResourceLayoutAdvertiser:
			//	adm.Ext.AdvertiserName = rsc.Text.Text

			case entity.MaterialTypeVideo:
				adm.Video = &common_json_entity.Video{
					Url:      candidate.ReplaceUrlMacro(rsc.Url, traffic, trackingGen),
					H:        rsc.Height,
					W:        rsc.Width,
					Duration: mb.RoundSeconds(int(rsc.Duration)),
					Mime:     rsc.MimeType.String(),
					FileSize: rsc.FileSize,
				}
				if strings.Contains(rsc.Url, ".flv") {
					adm.Video.Mime = "video/flv"
				} else if strings.Contains(rsc.Url, ".webm") {
					adm.Video.Mime = "video/webm"
				} else {
					adm.Video.Mime = "video/mp4"
				}
				hasVideo = true
			//case dict.MaterialResourceLayoutButtonText:
			//	adm.Ext.Btn = rsc.Text.Text
			default:
				continue
			}
		}

		//switch seatBid.MaterialData.MimeType {
		//case dict.MimeJpg, dict.MimeJpeg, dict.MimePng, dict.MimeGif:
		//	genImage(seatBid, adm)
		//case dict.MimeFlv, dict.MimeMp4:
		//	genVideo(seatBid, adm)
		//case dict.MimeText:
		//	genText(seatBid, adm)
		//case dict.MimeFeed, dict.MimeVideoFeed:
		//	genFeed(seatBid, adm)
		//default:
		//}

		//adm.Ext.Style = seatBid.MaterialData.AdxTemplateId
		//bid.TemplateSpec = seatBid.MaterialData.HtmlTemplate

		adm.Land = candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen)
		adm.Interact = int(entity.LandingTypeInWebView)
		if hasImage {
			adm.Formid = 3
			adm.CType = int(entity.MimeTypeIdPng)
			if hasText || hasVideo {
				adm.Formid = 1
				adm.CType = int(entity.MimeTypeIdFeed)
			}
		}

		if hasVideo {
			if hasImage || hasText {
				adm.Formid = 6
				adm.CType = int(entity.MimeTypeIdVideoFeed)
			} else {
				adm.Formid = 4
				adm.CType = int(entity.MimeTypeIdMp4)
			}
		}
		//adm.Formid, _ = strconv.Atoi(seatBid.MaterialData.AdxTemplateId)

		adm.DpLink = candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen)
		if len(adm.DpLink) > 0 {
			adm.Interact = int(entity.LandingTypeDeepLink)
			bid.DpTrackers = append(bid.DpTrackers, candidate.ReplaceUrlMacro(genericAd.GetDpSuccess(), traffic, trackingGen))
		}
		if adm.LandDesc == nil {
			adm.LandDesc = &common_json_entity.LandDesc{}
		}
		//adm.LandDesc.ClickAreaClick = seatBid.BidExtData.ClickAreaClick
		//adm.LandDesc.ShakeModel = seatBid.BidExtData.ShakeModel

		//adm.Formid, _ = strconv.Atoi(seatBid.MaterialData.AdxTemplateId)
		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			adm.Interact = int(entity.LandingTypeDownload)
		} else if genericAd.GetLandingAction() == entity.LandingTypeDeepLink {
			adm.Interact = int(entity.LandingTypeDeepLink)
		}

		if adm.Interact == int(entity.LandingTypeDownload) && len(genericAd.GetDownloadUrl()) > 0 {
			adm.Land = candidate.ReplaceUrlMacro(genericAd.GetDownloadUrl(), traffic, trackingGen)
		}

		if genericAd.GetAppInfo() != nil {
			adm.App = &common_json_entity.DownloadApp{
				Name:        genericAd.GetAppInfo().AppName,
				Icon:        genericAd.GetAppInfo().Icon,
				PackageName: genericAd.GetAppInfo().PackageName,
				AppId:       genericAd.GetAppInfo().AppID,
				AppVersion:  genericAd.GetAppInfo().AppVersion,
				PackageSize: genericAd.GetAppInfo().PackageSize,
				Privacy:     genericAd.GetAppInfo().Privacy,
				Permission:  genericAd.GetAppInfo().Permission,
				AppDesc:     genericAd.GetAppInfo().AppDesc,
				AppDescURL:  genericAd.GetAppInfo().AppDescURL,
				Develop:     genericAd.GetAppInfo().Develop,
				AppBeian:    genericAd.GetAppInfo().AppBeian,
				AppAge:      genericAd.GetAppInfo().AppAge,
			}

			if len(adm.Ext.Icon) == 0 {
				adm.Ext.Icon = genericAd.GetAppInfo().Icon
			}

			if genericAd.GetAppInfo().WechatExt != nil {
				adm.App.WeChatExt = &common_json_entity.WeChatExt{
					ProgramId:   genericAd.GetAppInfo().WechatExt.ProgramId,
					ProgramPath: genericAd.GetAppInfo().WechatExt.ProgramPath,
				}
			}

			if len(genericAd.GetAppInfo().PermissionDesc) > 0 {
				adm.App.PermissionDesc = make([]common_json_entity.PermissionDesc, 0)
				for _, item := range genericAd.GetAppInfo().PermissionDesc {
					adm.App.PermissionDesc = append(adm.App.PermissionDesc, common_json_entity.PermissionDesc{
						PermissionLab:  item.PermissionLab,
						PermissionDesc: item.PermissionDesc,
					})
				}
			}

		}

		//if seatBid.MonitorData.LandingDesc.LandingAction == dict.LandingTypeWeChatProgram {
		//	if adm.App == nil {
		//		adm.App = &json_broker.DownloadApp{}
		//	}
		//	if adm.App.WeChatExt == nil {
		//		adm.App.WeChatExt = &json_broker.WeChatExt{}
		//	}
		//	adm.App.WeChatExt.BindApp = seatBid.MonitorData.LandingDesc.WechatBindedApp
		//	adm.App.WeChatExt.ProgramId = seatBid.MonitorData.LandingDesc.WechatProgramId
		//	adm.App.WeChatExt.ProgramPath = seatBid.MonitorData.LandingDesc.WechatProgramPath
		//}
		//mb.genSharedData(seatBid, adm)

		bidResponse.SeatBid[0].Bid = append(bidResponse.SeatBid[0].Bid, bid)
		if candidate.GetDspResponseAd() != nil {
			bidResponse.SeatBid[0].Seat = strconv.Itoa(int(candidate.GetDspResponseAd().DspId))
		}
		break
	}

	//if err := mb.BuildHttpEasyJsonResponse(request, writer, bidResponse); err != nil {
	//	return err
	//}
	//
	//mb.DoTrafficResponseSampleEasyJson(request, bidResponse)

	if err := mb.BuildHttpSonicJsonResponse(request, writer, &bidResponse); err != nil {
		return err
	}

	mb.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		zap.L().Info("[CommonJsonTrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", bidResponse.DumpJson())))))
	}

	return nil
}

func (mb *CommonJsonTrafficBroker) RoundSeconds(duration int) int32 {
	const VideoDurationUnit = 1
	return int32(math.Round(float64(duration) / VideoDurationUnit))
}

func (mb *CommonJsonTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[CommonJsonTrafficBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		zap.L().Info("[CommonJsonTrafficBroker] adCode:%+v").GetErrCodeMap())
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	_, err := writer.WriteWithStatus(204, nil)
	return err
}

func (mb *CommonJsonTrafficBroker) parseDevice(mediaBidRequest *common_json_entity.CommonJsonBrokerRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Device == nil {
		return fmt.Errorf("parseDevice failed")
	}
	//bidReq.IsMobile = true
	device := mediaBidRequest.Device

	bidReq.Device.RequestIp = device.Ip

	bidReq.Device.UserAgent = device.Ua
	bidReq.Device.WebviewUA = device.Ua

	bidReq.Device.OsType = entity.OsType(device.Os)
	bidReq.Device.OsVersion = device.Osv
	bidReq.Device.Model = device.Model
	bidReq.Device.Brand = device.Make

	bidReq.Device.DeviceType = mb.mappingDeviceType(device.DeviceType)

	if bidReq.Device.OsType == entity.OsTypeIOS {
		bidReq.Device.Idfa = device.Idfa
		bidReq.Device.IdfaMd5 = device.IdfaMd5
	} else {
		bidReq.Device.Imei = device.Imei
		bidReq.Device.ImeiMd5 = device.ImeiMd5

		bidReq.Device.Oaid = device.Oaid
		bidReq.Device.OaidMd5 = device.OaidMd5

		bidReq.Device.AndroidId = device.Aid
		bidReq.Device.AndroidIdMd5 = device.AidMd5
	}
	bidReq.Device.Caid = string_utils.ConcatString(device.CaidVersion, "_", device.Caid)
	bidReq.Device.CaidRaw = device.Caid
	bidReq.Device.CaidVersion = device.CaidVersion

	bidReq.Device.AliAaid = device.AliAaid

	bidReq.Device.Mac = device.Mac
	bidReq.Device.MacMd5 = device.MacMd5

	bidReq.Device.ScreenHeight = device.Sh
	bidReq.Device.ScreenWidth = device.Sw
	bidReq.Device.ScreenDensity = device.Den
	bidReq.Device.Paid = device.Paid
	bidReq.Device.Caids = device.Caids

	if device.Geo != nil {
		bidReq.Device.Lat = device.Geo.Lat
		bidReq.Device.Lon = device.Geo.Lon
	}

	bidReq.Device.OperatorType = entity.OperatorType(device.Carrier)
	bidReq.Device.ConnectionType = entity.ConnectionType(device.ConnectionType)

	bidReq.Device.BootMark = device.BootMark
	bidReq.Device.UpdateMark = device.UpdateMark
	bidReq.Device.VercodeAg = device.VerCodeOfAG
	bidReq.Device.VercodeHms = device.VerCodeOfHms
	bidReq.Device.CountryCode = device.AgCountryCode

	bidReq.Device.PPI = int32(device.PPI)
	bidReq.Device.DeviceInitTime = device.DeviceInitTime
	bidReq.Device.DeviceStartupTime = device.DeviceStartupTime
	bidReq.Device.DeviceUpgradeTime = device.DeviceUpgradeTime
	bidReq.Device.SystemTotalMem = device.SystemTotalMem
	bidReq.Device.SystemFreeMem = device.SystemFreeMem
	bidReq.Device.SystemTotalDisk = device.SystemTotalDisk
	bidReq.Device.SystemFreeDisk = device.SystemFreeDisk
	bidReq.Device.DeviceName = device.DeviceName
	bidReq.Device.HardwareMachineCode = device.DeviceModel

	return nil
}

func (mb *CommonJsonTrafficBroker) parseSite(mediaBidRequest *common_json_entity.CommonJsonBrokerRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Site == nil {
		zap.L().Debug("parseSite, vendor: , site nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	site := mediaBidRequest.Site

	bidReq.Referer = site.Ref
	bidReq.Url = site.Page

	return nil
}

func (mb *CommonJsonTrafficBroker) parseUser(mediaBidRequest *common_json_entity.CommonJsonBrokerRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.User == nil {
		zap.L().Debug("parseUser, vendor: , user nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	user := mediaBidRequest.User

	bidReq.UserId = user.Id

	bidReq.UserGender = mb.mappingGenderType(user.Gender)
	bidReq.UserAge = user.Age
	//bidReq.User.AdxDmpRules = make([]string, len(user.AudienceTag))
	//copy(bidReq.User.AdxDmpRules, user.AudienceTag)
	if len(bidReq.App.InstalledApp) == 0 && len(user.AppList) != 0 {
		bidReq.App.InstalledApp = user.AppList
	}

	return nil
}

func (mb *CommonJsonTrafficBroker) parseApp(mediaBidRequest *common_json_entity.CommonJsonBrokerRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.App == nil {
		zap.L().Debug("parseApp, vendor: , app nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	app := mediaBidRequest.App

	bidReq.App.AppName = app.Name
	bidReq.App.AppBundle = app.Bundle
	bidReq.App.SdkVersion = app.SdkVersion
	bidReq.App.AppVersion = app.Ver
	if len(app.InstalledApp) > 0 {
		bidReq.App.InstalledApp = app.InstalledApp
	}

	return nil
}

func (mb *CommonJsonTrafficBroker) parseImp(mediaBidRequest *common_json_entity.CommonJsonBrokerRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Imp == nil {
		zap.L().Debug("[CommonJsonTrafficBroker]parseImp, vendor: , imp nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return fmt.Errorf("parseImp, imp nil")
	}

	for _, item := range mediaBidRequest.Imp {
		if item == nil {
			continue
		}

		bidReq.ImpressionId = item.Id
		if len(bidReq.ImpressionId) < 1 {
			bidReq.ImpressionId = utils.NewUUID()
		}

		bidReq.SetMediaSlotKey(strings.TrimSpace(item.TagId))
		bidReq.BidFloor = uint32(item.BidFloor)

		mySlotId := strings.TrimSpace(item.SlotId)

		bidReq.SlotType = mb.mappingSlotType(item.AdType)
		if len(mySlotId) > 0 && mySlotId != "0" {
			bidReq.SetMediaSlotKey(mySlotId)

		}
		//TODO  slot pos新的协议中暂无

		if item.H > 0 && item.W > 0 {
			bidReq.SlotWidth = uint32(item.W)
			bidReq.SlotHeight = uint32(item.H)
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  item.W,
				Height: item.H,
			})
		}
		// 多尺寸支持
		if len(item.Sizes) > 0 {
			for _, size := range item.Sizes {
				if size.W > 0 && size.H > 0 {
					if bidReq.SlotWidth < 1 {
						bidReq.SlotWidth = uint32(size.W)
						bidReq.SlotHeight = uint32(size.H)
					}
					bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
						Width:  size.W,
						Height: size.H,
					})
				}
			}
		}

		//for _, ct := range item.CType {
		//	bidReq.Mimes = append(bidReq.Mimes, entity.MimeType(ct))
		//}

		for _, d := range item.Pmp {
			sd := ad_service.SourceDeal{
				DealId:   d.Deal,
				BidFloor: d.Price,
			}

			bidReq.SourceDeal = append(bidReq.SourceDeal, sd)
		}

		bidReq.VideoMaxDuration = item.MaxDuration
		bidReq.VideoMinDuration = item.MinDuration
		if bidReq.VideoMaxDuration < bidReq.VideoMinDuration {
			bidReq.VideoMaxDuration = bidReq.VideoMinDuration
		}

		formids := item.Formids

		if len(formids) == 0 {
			switch bidReq.SlotType {
			case entity.SlotTypeRewardVideo:
				formids = []int64{6}
			case entity.SlotTypeVideo:
				formids = []int64{4, 6}
			default:
				formids = []int64{1, 2, 3, 4, 6}
			}
		}

		sizeType := mb.getSizeType(item.SupportSizeType)

		for _, id := range formids {
			bidReq.AdxTemplateId = append(bidReq.AdxTemplateId, strconv.FormatInt(id, 10))
			switch id {
			case 1:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1)
				//key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(item.W), int(item.H))
				key.Image().AddRequiredCount(1).SetRequiredSizeType(sizeType)
				key.Icon().AddRequiredCount(1).SetOptional(true)
				bidReq.AppendCreativeTemplateKey(key)
			case 2:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(sizeType)
				key.Icon().AddRequiredCount(1).SetOptional(true)
				bidReq.AppendCreativeTemplateKey(key)
			case 3:
				key := creative_entity.NewCreativeTemplateKey()
				key.Image().AddRequiredCount(1).SetRequiredSizeType(sizeType)
				key.Title().AddRequiredCount(1).SetOptional(true)
				bidReq.AppendCreativeTemplateKey(key)
			case 4:
				key := creative_entity.NewCreativeTemplateKey()
				key.Video().AddRequiredCount(1).SetRequiredSizeType(sizeType)
				key.Title().AddRequiredCount(1).SetOptional(true)
				bidReq.AppendCreativeTemplateKey(key)
			case 5:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Image().AddRequiredCount(3).SetRequiredSizeType(sizeType)
				key.Icon().AddRequiredCount(1).SetOptional(true)
				bidReq.AppendCreativeTemplateKey(key)
			case 6:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(sizeType)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(sizeType)
				bidReq.AppendCreativeTemplateKey(key)
			case 7:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(sizeType)
				key.Icon().AddRequiredCount(1)
				bidReq.AppendCreativeTemplateKey(key)
			case 8:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				bidReq.AppendCreativeTemplateKey(key)
			case 9:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(3).SetRequiredSizeType(sizeType)
				key.Icon().AddRequiredCount(1)
				bidReq.AppendCreativeTemplateKey(key)
			}
		}

		//for _, l := range item.SupportInteract {
		//	bidReq.ClickAction = append(bidReq.ClickAction, dict.LandingType(l))
		//}
		//if len(impInfo.ClickAction) < 1 {
		//	impInfo.ClickAction = append(impInfo.ClickAction, dict.LandingAll)
		//}
		bidReq.UseHttps = mb.mappingSecure(item.Secure)
	}

	return nil
}

func (mb *CommonJsonTrafficBroker) getSizeType(sizeType []int) creative_entity.CreativeTemplateKeyItemRequirementType {
	if len(sizeType) == 0 {
		return creative_entity.RT_SIZE_NULL
	}

	hasVertical := false
	hasHorizontal := false

	for _, st := range sizeType {
		switch st {
		case 1:
			hasHorizontal = true
		case 2:
			hasVertical = true
		}
	}

	if hasVertical && hasHorizontal {
		return creative_entity.RT_SIZE_NULL
	} else if hasVertical {
		return creative_entity.RT_SIZE_VERTICAL
	} else if hasHorizontal {
		return creative_entity.RT_SIZE_HORIZONTAL
	} else {
		return creative_entity.RT_SIZE_NULL
	}
}

func (mb *CommonJsonTrafficBroker) mappingSecure(s int) bool {

	switch s {
	case 0:
		return false
	case 1:
		return true
	case 2:
		return false
	default:
		return false
	}
}

func (mb *CommonJsonTrafficBroker) mappingGenderType(s string) entity.UserGenderType {
	switch s {
	case "M":
		return entity.UserGenderMan
	case "F":
		return entity.UserGenderWoman
	default:
		return entity.UserGenderUnknown
	}
}

func (mb *CommonJsonTrafficBroker) mappingDeviceType(s int32) entity.DeviceType {
	switch s {
	case 1, 3:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePad
	case 4:
		return entity.DeviceTypePc
	case 5:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeUnknown
	}
}

/*
SlotTypeVideo

	SlotTypeVideoPause
	SlotTypeVideoOverlay
	SlotTypeBanner
	SlotTypeOpening
	SlotTypeInterstitial
	SlotTypeFeed
	SlotTypeText
	SlotTypeKC
	SlotIncentiveVideo
	slotTypeEnd
*/
func (mb *CommonJsonTrafficBroker) mappingSlotType(s int32) entity.SlotType {
	switch s {
	case 1:
		return entity.SlotTypeVideo
	case 2:
		return entity.SlotTypeVideoPause
	case 3:
		return entity.SlotTypeVideoOpening
	case 4:
		return entity.SlotTypeBanner
	case 5:
		return entity.SlotTypeOpening
	case 6:
		return entity.SlotTypePopup
	case 7:
		return entity.SlotTypeFeeds
	case 10:
		return entity.SlotTypeRewardVideo
	default:
		return entity.SlotTypeUnknown
	}
}
