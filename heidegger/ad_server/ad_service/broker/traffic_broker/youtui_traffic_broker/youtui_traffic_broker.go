package youtui_traffic_broker

import (
	"errors"
	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"github.com/spf13/cast"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	youtuiproto "gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/youtui_traffic_broker/youitui_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"net/http"
	"strconv"
	"strings"
	"fmt"
)

const (
	adxTemplateKey = "adxTemplate"
)

type YouTuiTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        *zap.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewYouTuiTrafficBroker(mediaId utils.ID) *YouTuiTrafficBroker {
	return &YouTuiTrafficBroker{
		log:     zap.L().With(zap.String("broker", "YouTuiTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "${AUCTION_PRICE}",
			MediaClickDownXMacro: "__LEMON__DP_DOWN_X__",
			MediaClickDownYMacro: "__LEMON__DP_DOWN_Y__",
			MediaClickUpXMacro:   "__LEMON__DP_UP_X__",
			MediaClickUpYMacro:   "__LEMON__DP_UP_Y__",
			MediaHWSldMacro:      "__LEMON__SLD__",
		},
	}
}

func (y *YouTuiTrafficBroker) GetMediaId() utils.ID {
	return y.mediaId
}

func (y *YouTuiTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(y.SendResponse)
	request.Response.SetFallbackResponseBuilder(y.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = y.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = y.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &youtuiproto.BidRequest{}
	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		y.log.WithError(err).Error("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		y.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	request.SetMediaId(y.GetMediaId())
	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}

	y.parseApp(bidRequest, request)
	y.parseUser(bidRequest, request)
	y.parseDevice(bidRequest, request)
	if err = y.parseImp(bidRequest, request); err != nil {
		y.log.WithError(err).Debugf("BrokeRequest, parseImp failed")
		return err
	}

	y.DoTrafficSamplePb(request, bidRequest)

	return nil
}

func (y *YouTuiTrafficBroker) parseApp(request *youtuiproto.BidRequest, adRequest *ad_service.AdRequest) {
	if request.App != nil {
		adRequest.App.AppName = request.App.Name
		adRequest.App.AppVersion = request.App.Ver
		adRequest.App.AppBundle = request.App.Bundle
	}
}

func (y *YouTuiTrafficBroker) parseUser(request *youtuiproto.BidRequest, adRequest *ad_service.AdRequest) {
	if request.User != nil {
		adRequest.UserId = request.User.Id
	}
}

func (y *YouTuiTrafficBroker) parseDevice(request *youtuiproto.BidRequest, adRequest *ad_service.AdRequest) {
	if request.Device == nil {
		return
	}

	adRequest.Device = ad_service.AdRequestDevice{
		OsType:            mappingOsType(request.Device.Os),
		DeviceType:        mappingDeviceType(request.Device.Devicetype),
		IsMobile:          request.Device.Devicetype == "phone",
		DeviceInitTime:    request.Device.Boottime,
		DeviceStartupTime: request.Device.StartupTime,
		DeviceUpgradeTime: request.Device.Osupdatetime,
		SystemTotalMem:    cast.ToInt64(request.Device.Memory),
		SystemTotalDisk:   cast.ToInt64(request.Device.HardDisk),
		DeviceName:        request.Device.DeviceName,
		DeviceNameMd5:     request.Device.DeviceNameMd5,
		BootMark:          request.Device.Bootmark,
		UpdateMark:        request.Device.Updatemark,
		Idfa:              request.Device.Ifa,
		Imei:              request.Device.Did,
		Mac:               request.Device.Mac,
		AndroidId:         request.Device.Dpid,
		Oaid:              request.Device.Oaid,
		Aaid:              request.Device.Aaid,
		Idfv:              request.Device.Idfv,
		MacMd5:            request.Device.Macmd5,
		ImeiMd5:           request.Device.Didmd5,
		AndroidIdMd5:      request.Device.Dpidmd5,
		OaidMd5:           request.Device.OaidMd5,
		Model:             request.Device.Model,
		Brand:             request.Device.Brand,
		Vendor:            request.Device.Make,
		Language:          "zh",
		CountryCode:       "CN",
		ScreenHeight:      cast.ToInt32(request.Device.H),
		ScreenWidth:       cast.ToInt32(request.Device.W),
		ScreenOrientation: mappingOrientation(request.Device.Orientation),
		OsVersion:         request.Device.Osv,
		UserAgent:         request.Device.Ua,
		WebviewUA:         request.Device.Ua,
		DPI:               cast.ToInt32(request.Device.Dpi),
		RequestIp:         request.Device.Ip,
		ConnectionType:    mappingConnectionType(request.Device.Connectiontype),
		OperatorType:      mappingOperatorType(request.Device.Carrier),
		OperatorName:      mappingOperatorType(request.Device.Carrier).String(),
	}

	if len(request.Device.Caid) > 0 {
		adRequest.Device.Caid = request.Device.Caid
		adRequest.Device.CaidRaw = request.Device.Caid
	}

	if request.Device.Ext != nil {
		if len(request.Device.Ext.Oaid) > 0 {
			adRequest.Device.Oaid = request.Device.Ext.Oaid
		}
		if len(request.Device.Ext.Brand) > 0 {
			adRequest.Device.Brand = request.Device.Ext.Brand
		}
		if len(request.Device.Ext.Mac3Md5) > 0 {
			adRequest.Device.MacMD5 = request.Device.Ext.Mac3Md5
		}
		if len(request.Device.Ext.Ipv6) > 0 {
			adRequest.Device.RequestIp = request.Device.Ext.Ipv6
			adRequest.Device.IsIp6 = true
		}
	}

	if request.Device.Geo != nil {
		adRequest.Device.Lat = request.Device.Geo.Lat
		adRequest.Device.Lon = request.Device.Geo.Lon
	}
}

func (y *YouTuiTrafficBroker) parseImp(request *youtuiproto.BidRequest, adRequest *ad_service.AdRequest) error {
	if request.Imp == nil {
		return errors.New("impressions is empty")
	}

	imp := request.Imp
	adRequest.ImpressionId = imp.Id
	adRequest.SetMediaSlotKey(imp.Tagid)
	adRequest.BidFloor = uint32(cast.ToFloat64(imp.Bidfloor) * 100)
	adRequest.BidType = entity.BidTypeCpm

	if imp.Pmp != nil {
		for _, deal := range imp.Pmp.Deals {
			adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
				DealId:   deal.Id,
				BidFloor: cast.ToInt64(deal.Bidfloor),
			})
		}
	}

	if imp.Ext != nil {
		appList := strings.Split(imp.Ext.AppList, ",")
		if len(appList) > 0 {
			adRequest.App.InstalledApp = appList
		}
		appIdList := strings.Split(imp.Ext.Tag, ",")
		if len(appIdList) > 0 {
			adRequest.App.MediaInstalledAppIds = appIdList
		}
	}

	adxTemplateMap := make(map[uint64]string)

	if imp.Banner != nil && imp.Banner.Ext != nil {
		for _, tempId := range imp.Banner.Ext.Tempid {
			key := creative_entity.NewCreativeTemplateKey()
			switch tempId {
			case "1", "2", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17": //图文
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			case "3", "4", "5": // 图文(可选)+视频
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			case "6", "7": // 图文+视频(可选)
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			case "18", "19": // 激励视频
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			default: // 默认图片
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			}
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = tempId
		}
		for _, tempInfo := range imp.Banner.Ext.Tempinfo {
			for _, size := range tempInfo.Sizes {
				sizes := strings.Split(size, "x")
				if len(sizes) > 1 {
					width, height := cast.ToInt64(sizes[0]), cast.ToInt64(sizes[1])
					if width > 0 && height > 0 {
						if adRequest.SlotHeight > 0 || adRequest.SlotWidth > 0 {
							adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
								Width:  width,
								Height: height,
							})
						} else {
							adRequest.SlotHeight, adRequest.SlotWidth = uint32(width), uint32(height)
						}
					}
				}
			}
		}
	}

	if imp.Video != nil && imp.Video.Ext != nil {
		for _, tempId := range imp.Video.Ext.Tempid {
			key := creative_entity.NewCreativeTemplateKey()
			switch tempId {
			case "1", "2", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17": // 图文
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			case "3", "4", "5": // 图文(可选)+视频
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			case "6", "7": //图文+视频(可选)
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			case "18", "19": // 激励视频
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			default: // 默认视频
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			}
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = tempId
		}
		for _, tempInfo := range imp.Video.Ext.Tempinfo {
			for _, size := range tempInfo.Sizes {
				sizes := strings.Split(size, "x")
				if len(sizes) > 1 {
					width, height := cast.ToInt64(sizes[0]), cast.ToInt64(sizes[1])
					if width > 0 && height > 0 {
						if adRequest.SlotHeight > 0 || adRequest.SlotWidth > 0 {
							adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
								Width:  width,
								Height: height,
							})
						} else {
							adRequest.SlotHeight, adRequest.SlotWidth = uint32(width), uint32(height)
						}
					}
				}
			}
		}
	}

	if imp.Native != nil {
		for _, tempId := range imp.Native.Tempid {
			key := creative_entity.NewCreativeTemplateKey()
			switch tempId {
			case "1", "2", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17": //图文
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			case "3", "4", "5": // 图文(可选)+视频
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			case "6", "7": // 图文+视频(可选)
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			case "18", "19": // 激励视频
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			default: // 默认图片+视频(可选)
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			}
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = tempId
		}
		if imp.Native.Ext != nil {
			for _, tempInfo := range imp.Native.Ext.Tempinfo {
				for _, size := range tempInfo.Sizes {
					sizes := strings.Split(size, "x")
					if len(sizes) > 1 {
						width, height := cast.ToInt64(sizes[0]), cast.ToInt64(sizes[1])
						if width > 0 && height > 0 {
							if adRequest.SlotHeight > 0 || adRequest.SlotWidth > 0 {
								adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
									Width:  width,
									Height: height,
								})
							} else {
								adRequest.SlotHeight, adRequest.SlotWidth = uint32(width), uint32(height)
							}
						}
					}
				}
			}
		}
	}

	if len(adxTemplateMap) < 1 { //默认图片+视频(可选)
		key := creative_entity.NewCreativeTemplateKey()
		key.Title().AddRequiredCount(1).SetOptional(true)
		key.Desc().AddRequiredCount(1).SetOptional(true)
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
		key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
		adRequest.AppendCreativeTemplateKey(key)
		adxTemplateMap[key.Uint64()] = ""
	}
	adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)

	return nil
}

func (y *YouTuiTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		y.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("YouTui")
	}

	if request.Response.NoCandidate() {
		return y.SendFallbackResponse(request, writer)
	}

	bidResponse, err := y.buildResponse(request)
	if err != nil {
		y.log.WithError(err).Error("buildResponse err")
		return err
	}
	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()

	buffer.EnsureSize(bidResponse.Size())
	_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		zap.L().Error("Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/x-protobuf")
	if _, err = writer.WriteWithStatus(http.StatusOK, data); err != nil {
		return err
	}

	y.DoTrafficResponseSamplePb(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		y.log.Infof("SendResponse success, response: %s", responseStr)
	}

	return nil
}

func (y *YouTuiTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		y.log.Infof("Build Fallback Response start. bid response: [%v]", request.Response)
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/x-protobuf")
	_, err := writer.WriteWithStatus(http.StatusNoContent, nil)
	if err != nil {
		return err
	}

	return nil
}

func (y *YouTuiTrafficBroker) buildResponse(request *ad_service.AdRequest) (*youtuiproto.BidResponse, error) {
	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		resBid := &youtuiproto.BidResponse_Bid{
			Id:    request.GetRequestId(),
			Impid: request.ImpressionId,
			Price: strconv.FormatFloat(float64(candidate.GetBidPrice().Price)/100, 'f', 2, 64),
			Ext: &youtuiproto.BidResponse_BidExt{
				Ldp:      genericAd.GetLandingUrl(),
				Ldptype:  0,
				Videotrs: &youtuiproto.BidResponse_Videotrs{},
			},
		}

		if candidate.GetIndexDeal() != nil {
			resBid.Dealid = candidate.GetIndexDeal().DealId
		}

		if creative.GetCreativeId() != 0 {
			resBid.Crid = strconv.FormatInt(int64(creative.GetCreativeId()), 10)
		} else {
			resBid.Crid = creative.GetCreativeKey()
		}

		for _, s := range candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen) {
			resBid.Ext.Pa = append(resBid.Ext.Pa, &youtuiproto.BidResponse_Pa{Url: s})
		}
		for _, s := range candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen) {
			resBid.Ext.Ca = append(resBid.Ext.Ca, &youtuiproto.BidResponse_Ca{Url: s})
		}
		for _, s := range genericAd.GetVideoStartUrlList() {
			resBid.Ext.Videotrs.Videostart = append(resBid.Ext.Videotrs.Videostart, &youtuiproto.BidResponse_Videostart{Url: s})
		}
		for _, s := range genericAd.GetVideoCloseUrlList() {
			resBid.Ext.Videotrs.Videoend = append(resBid.Ext.Videotrs.Videoend, &youtuiproto.BidResponse_Videoend{Url: s})
		}
		for _, s := range genericAd.GetAppDownloadStartedMonitorList() {
			resBid.Ext.Downstarttrs = append(resBid.Ext.Downstarttrs, &youtuiproto.BidResponse_Downstarttrs{Url: s})
		}
		for _, s := range genericAd.GetAppDownloadFinishedMonitorList() {
			resBid.Ext.Downcomptrs = append(resBid.Ext.Downcomptrs, &youtuiproto.BidResponse_Downcomptrs{Url: s})
		}
		for _, s := range genericAd.GetAppInstallStartMonitorList() {
			resBid.Ext.Installstarttrs = append(resBid.Ext.Installstarttrs, &youtuiproto.BidResponse_Installstarttrs{Url: s})
		}
		for _, s := range genericAd.GetAppInstalledMonitorList() {
			resBid.Ext.Installcomptrs = append(resBid.Ext.Installcomptrs, &youtuiproto.BidResponse_Installcomptrs{Url: s})
		}
		for _, s := range genericAd.GetDeepLinkMonitorList() {
			resBid.Ext.Dpsuccesstrs = append(resBid.Ext.Dpsuccesstrs, &youtuiproto.BidResponse_Dpsuccesstrs{Url: s})
		}
		for _, s := range genericAd.GetDeepLinkFailedMonitorList() {
			resBid.Ext.Dpfailtrs = append(resBid.Ext.Dpfailtrs, &youtuiproto.BidResponse_Dpfailtrs{Url: s})
		}

		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			resBid.Ext.Ldptype = 1
		} else if genericAd.GetLandingAction() == entity.LandingTypeDeepLink {
			resBid.Ext.Ldptype = 2
		}

		if traffic.GetOsType() == entity.OsTypeIOS {
			resBid.Ext.Universallink = genericAd.GetDeepLinkUrl()
		} else {
			resBid.Ext.Deeplink = genericAd.GetDeepLinkUrl()
		}
		if genericAd.GetAppInfo() != nil {
			resBid.Ext.Apk = &youtuiproto.BidResponse_Apk{
				Apkname: genericAd.GetAppInfo().AppName,
				Package: genericAd.GetAppInfo().PackageName,
			}
			if traffic.GetOsType() == entity.OsTypeIOS {
				resBid.Ext.Apk.Iurl = genericAd.GetDownloadUrl()
			}
		}

		adxTemplateMap := request.GetMediaExtraDataWithDefault(adxTemplateKey, make(map[uint64]string)).(map[uint64]string)
		resBid.Ext.Tempid = adxTemplateMap[uint64(candidate.GetActiveCreativeTemplateKey())]
		switch resBid.Ext.Tempid {
		case "6", "7", "8", "9": // 信息流
			resBid.Ext.Native = &youtuiproto.BidResponse_RespNative{}
			for _, material := range candidate.GetSelectedMaterialList() {
				switch material.MaterialType {
				case entity.MaterialTypeTitle:
					resBid.Ext.Native.Title = material.Data
				case entity.MaterialTypeDesc:
					resBid.Ext.Native.Desc = material.Data
				case entity.MaterialTypeIcon:
					resBid.Ext.Icon = append(resBid.Ext.Icon, &youtuiproto.BidResponse_Icon{
						Url: material.Url,
						W:   strconv.FormatInt(int64(material.Width), 10),
						H:   strconv.FormatInt(int64(material.Height), 10),
					})
				case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
					resBid.Ext.Native.Imgurl = append(resBid.Ext.Native.Imgurl, material.Url)
					resBid.Adm = material.Url
				case entity.MaterialTypeVideo:
					resBid.Ext.Native.Videourl = material.Url
					resBid.Ext.Native.Duration = uint32(material.Duration)
					resBid.Ext.Native.H = strconv.FormatInt(int64(material.Height), 10)
					resBid.Ext.Native.W = strconv.FormatInt(int64(material.Width), 10)
				default:
				}
			}
		default: // 图片+视频
			for _, material := range candidate.GetSelectedMaterialList() {
				switch material.MaterialType {
				case entity.MaterialTypeTitle:
					resBid.Ext.Title = material.Data
				case entity.MaterialTypeDesc:
					resBid.Ext.Desc = material.Data
				case entity.MaterialTypeIcon:
					resBid.Ext.Icon = append(resBid.Ext.Icon, &youtuiproto.BidResponse_Icon{
						Url: material.Url,
						W:   strconv.FormatInt(int64(material.Width), 10),
						H:   strconv.FormatInt(int64(material.Height), 10),
					})
				case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
					resBid.Adm = material.Url
				case entity.MaterialTypeVideo:
					if resBid.Ext.Video == nil {
						resBid.Ext.Video = &youtuiproto.BidResponse_RespVideo{}
					}
					resBid.Ext.Video.Videourl = material.Url
					resBid.Ext.Video.Duration = uint32(material.Duration)
					resBid.Ext.Video.H = strconv.FormatInt(int64(material.Height), 10)
					resBid.Ext.Video.W = strconv.FormatInt(int64(material.Width), 10)
				default:
				}
			}
			if len(resBid.Adm) > 0 && resBid.Ext.Video != nil {
				resBid.Ext.Video.Cover = resBid.Adm
			}
		}

		return &youtuiproto.BidResponse{
			Id: request.GetRequestId(),
			Seatbid: []*youtuiproto.BidResponse_Seatbid{{
				Bid:  []*youtuiproto.BidResponse_Bid{resBid},
				Seat: "", // ADX为广告主分配的广告主id，如果是先审和后审，此参数必返回，免审可以忽略
			}}}, nil
	}

	return nil, err_code.ErrNoCandidate
}

func mappingOperatorType(carrier string) entity.OperatorType {
	switch carrier {
	case "46000":
		return entity.OperatorTypeChinaMobile
	case "46001":
		return entity.OperatorTypeChinaUnicom
	case "46003":
		return entity.OperatorTypeChinaTelecom
	case "46020":
		return entity.OperatorTypeTietong
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType string) entity.ConnectionType {
	switch connectionType {
	case "1":
		return entity.ConnectionTypeNetEthernet
	case "2":
		return entity.ConnectionTypeWifi
	case "3":
		return entity.ConnectionTypeCellular
	case "4":
		return entity.ConnectionType2G
	case "5":
		return entity.ConnectionType3G
	case "6":
		return entity.ConnectionType4G
	case "7":
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingOrientation(orientation string) entity.ScreenOrientationType {
	switch orientation {
	case "1":
		return entity.ScreenOrientationTypePortrait
	case "2":
		return entity.ScreenOrientationTypeLandscape
	default:
		return entity.ScreenOrientationTypeUnknown
	}
}

func mappingDeviceType(deviceType string) entity.DeviceType {
	switch deviceType {
	case "phone":
		return entity.DeviceTypeMobile
	case "pad":
		return entity.DeviceTypePad
	case "pc":
		return entity.DeviceTypePc
	case "ott":
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeUnknown
	}
}

func mappingOsType(os string) entity.OsType {
	switch os {
	case "android":
		return entity.OsTypeAndroid
	case "ios":
		return entity.OsTypeIOS
	case "windows":
		return entity.OsTypeWindows
	default:
		return entity.OsTypeUnknown
	}
}
