package meiyou_traffic_broker

import (
	"fmt"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"strconv"
	"strings"
)

var (
	materialType2  = 2  //原生大图640*264,640*360宽高比【16:11～16:4】一张图 + 标题
	materialType3  = 3  //原生三图300*200宽高比【10:14～10:4】三张图 + 标题
	materialType4  = 4  //原生单图300*200宽高比【1:1～2:1】一张图 + 标题
	materialType6  = 6  //开屏图片1242*18801242*2208   1242*2340--一张图
	materialType7  = 7  //原生视频640*360宽高比【9:20 ~ 16:5】一个视频 + 一张图(封面)  + 标题 + 副标题
	materialType9  = 9  //开屏视频1080*1632,1080*1920,1080*2032--一个视频（不需要封面）
	materialType14 = 14 //原生竖图480*640宽高比【4:9 ~ 1:1】一张图 + 标题
	materialType18 = 18 //插屏图片960*1280宽高比【9:17 ~ 1:1】一张图
)

type (
	MeiyouBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId       utils.ID
		WinPriceMacro string
	}
)

func NewMeiyouBroker(mediaId utils.ID) *MeiyouBroker {
	return &MeiyouBroker{
		mediaId:       mediaId,
		WinPriceMacro: "__WIN_SIGN_PRICE__",
	}
}

func (mb *MeiyouBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *MeiyouBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro
	return mb.ParseAdRequest(request)
}

func (mb *MeiyouBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[MeiyouBroker]request body empty")
	}

	bidRequest := &MeiYouBidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[MeiyouBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if err = mb.buildRequest(request, bidRequest); err != nil {
		zap.L().Debug("[MeiyouBroker]BrokeRequest, parseUser failed")
		return err
	}
	mb.DoTrafficSample(request, body)
	return nil
}

func (mb *MeiyouBroker) buildRequest(request *ad_service.AdRequest, bidRequest *MeiYouBidRequest) error {
	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[MeiyouBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	request.SetRequestId(bidRequest.ID)
	if len(bidRequest.ID) < 1 {
		request.SetRequestId(utils.NewUUID())
	}
	request.SetMediaId(mb.mediaId)

	if err := mb.parseImp(bidRequest, request); err != nil {
		zap.L().Debug("[MeiyouBroker] BrokeRequest, parseImp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, request); err != nil {
		zap.L().Debug("[MeiyouBroker]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseApp(bidRequest, request); err != nil {
		zap.L().Debug("[MeiyouBroker]BrokeRequest, parseApp failed")
		return err
	}
	return nil
}

func (mb *MeiyouBroker) parseImp(mediaRequest *MeiYouBidRequest,
	bidReq *ad_service.AdRequest) error {

	imp := mediaRequest.Pos
	bidReq.ImpressionId = mediaRequest.ID
	bidReq.BidFloor = uint32(imp.LimitPrice) * 100
	bidReq.UseHttps = true
	bidReq.SetMediaSlotKey(imp.AdId)

	for k, imageSizeList := range imp.ImageSize {
		bidReq.AdxTemplateId = append(bidReq.AdxTemplateId, strconv.FormatInt(int64(k), 10))

		switch k {
		case materialType2: // 原生大图
			bidReq.SlotType = entity.SlotTypeFeeds
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraInt64(key.String()+"_"+bidReq.SlotType.String(), int64(k))

			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(640),
				Height: int64(360),
			})
		case materialType3: //原生三图
			bidReq.SlotType = entity.SlotTypeFeeds
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1)
			key.Image().AddRequiredCount(3).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraInt64(key.String()+"_"+bidReq.SlotType.String(), int64(k))
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(300),
				Height: int64(200),
			})
		case materialType4: // 原生单图
			bidReq.SlotType = entity.SlotTypeFeeds
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraInt64(key.String()+"_"+bidReq.SlotType.String(), int64(k))
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(300),
				Height: int64(200),
			})
		case materialType6: // 开屏图片
			bidReq.SlotType = entity.SlotTypeOpening
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraInt64(key.String()+"_"+bidReq.SlotType.String(), int64(k))
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(720),
				Height: int64(1280),
			})
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(1080),
				Height: int64(1920),
			})
		case materialType7: // 原生视频
			bidReq.SlotType = entity.SlotTypeFeeds
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraInt64(key.String()+"_"+bidReq.SlotType.String(), int64(k))
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(640),
				Height: int64(360),
			})
		case materialType9: // 开屏视频
			bidReq.SlotType = entity.SlotTypeOpening
			key := creative_entity.NewCreativeTemplateKey()
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraInt64(key.String()+"_"+bidReq.SlotType.String(), int64(k))
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(720),
				Height: int64(1280),
			})
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(1080),
				Height: int64(1920),
			})
		case materialType14: // 原生竖图
			bidReq.SlotType = entity.SlotTypeFeeds
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraInt64(key.String()+"_"+bidReq.SlotType.String(), int64(k))
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(480),
				Height: int64(640),
			})
		case materialType18: // 插屏图片
			bidReq.SlotType = entity.SlotTypePopup
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraInt64(key.String()+"_"+bidReq.SlotType.String(), int64(k))
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(960),
				Height: int64(1280),
			})
		}

		for _, imageSize := range imageSizeList {
			image := strings.Split(imageSize, "*")
			if len(image) >= 2 {
				w, _ := strconv.Atoi(image[0])
				h, _ := strconv.Atoi(image[1])
				bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
					Width:  int64(w),
					Height: int64(h),
				})
			}
		}
	}

	return nil
}

func (mb *MeiyouBroker) parseDevice(mediaRequest *MeiYouBidRequest,
	bidReq *ad_service.AdRequest) error {

	device := mediaRequest.Device
	bidReq.Device.OsType = func() entity.OsType {
		if device.Platform == 3 {
			return entity.OsTypeAndroid
		} else if device.Platform == 2 {
			return entity.OsTypeIOS
		} else {
			return entity.OsTypeUnknown
		}
	}()
	bidReq.Device.OsVersion = device.OsVersion
	bidReq.Device.Model = device.Osmodel
	bidReq.Device.Brand = device.Osbrand
	bidReq.Device.UserAgent = device.Ua
	bidReq.Device.AndroidIdMd5 = device.AndroidIdMd5
	bidReq.Device.AndroidIdMd5 = device.AndroidId
	bidReq.Device.Idfa = device.Idfa
	bidReq.Device.IdfaMd5 = device.IdfaMd5
	bidReq.Device.ImeiMd5 = device.ImeiMd5
	bidReq.Device.Imei = device.Imei
	bidReq.Device.Oaid = device.Oaid
	bidReq.Device.MacMd5 = device.MacMd5
	bidReq.Device.Mac = device.Mac
	bidReq.Device.RequestIp = device.Ip
	if strings.Contains(device.Ip, ":") {
		bidReq.Device.IsIp6 = true
	}
	bidReq.Device.BootMark = device.BootMark
	bidReq.Device.UpdateMark = device.UpdateMark
	bidReq.Device.DeviceStartupTime = device.DeviceStartSec
	bidReq.Device.DeviceInitTime = device.BirthTime
	bidReq.Device.ScreenWidth = device.DeviceWidth
	bidReq.Device.ScreenHeight = device.DeviceHeight
	bidReq.Device.CountryCode = device.Country
	bidReq.Device.Language = device.Language
	bidReq.Device.DeviceName = device.DeviceNameMd5
	bidReq.Device.HardwareMachineCode = device.HardwareMachine
	bidReq.Device.DeviceUpgradeTime = device.SystemUpdateSec
	if len(device.TimeZone) > 0 {
		tz, _ := strconv.ParseInt(device.TimeZone, 10, 32)
		bidReq.Device.TimeZone = int32(tz)
	}
	bidReq.Device.VercodeHms = device.HmsVersion
	bidReq.Device.VercodeAg = strconv.Itoa(device.AppStoreVersion)
	if len(device.PreCaidVersion) > 0 {
		bidReq.Device.Caid = string_utils.ConcatString(device.PreCaidVersion, "_", device.PreCaid)
		bidReq.Device.Caids = append(bidReq.Device.Caids, bidReq.Device.Caid)
	}
	if len(device.CaidVersion) > 0 {
		bidReq.Device.Caid = string_utils.ConcatString(device.CaidVersion, "_", device.Caid)
		bidReq.Device.Caids = append(bidReq.Device.Caids, bidReq.Device.Caid)
	}

	network := mediaRequest.Network
	bidReq.Device.ConnectionType = func() entity.ConnectionType {
		switch network.ConnectType {
		case 1:
			return entity.ConnectionTypeWifi
		case 2:
			return entity.ConnectionType2G
		case 3:
			return entity.ConnectionType3G
		case 4:
			return entity.ConnectionType4G
		case 5:
			return entity.ConnectionType5G
		default:
			return entity.ConnectionTypeUnknown
		}
	}()
	bidReq.Device.OperatorType = func() entity.OperatorType {
		switch network.Carrier {
		case 0:
			return entity.OperatorTypeUnknown
		case 1:
			return entity.OperatorTypeChinaMobile
		case 2:
			return entity.OperatorTypeChinaUnicom
		case 3:
			return entity.OperatorTypeChinaTelecom
		default:
			return entity.OperatorTypeUnknown
		}
	}()
	bidReq.Device.Lat = mediaRequest.Geo.Lat
	bidReq.Device.Lon = mediaRequest.Geo.Lon
	return nil

}

func (mb *MeiyouBroker) parseApp(mediaRequest *MeiYouBidRequest,
	bidReq *ad_service.AdRequest) error {

	bidReq.App.AppName = mediaRequest.Media.AppName
	bidReq.App.AppBundle = mediaRequest.Media.AppBundleId
	bidReq.App.AppVersion = mediaRequest.Media.AppVersion
	return nil
}

func (mb *MeiyouBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("MeiyouBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("MeiyouBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}
	bidResponse, err := mb.buildResponse(request)
	if err != nil {
		zap.L().Error("MeiyouBroker Error in buildResponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := []byte(bidResponse.String())
	writer.SetHeader("Content-Type", "application/json")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	mb.DoTrafficResponseSample(request, data)
	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[MeiyouBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", responseStr)))))
	}

	return nil
}

func (mb *MeiyouBroker) buildResponse(request *ad_service.AdRequest) (*MeiYouBidResponse, error) {
	response := &MeiYouBidResponse{
		Ret: 0,
		ID:  request.GetRequestId(),
		Data: MeiYouData{
			AdId: request.GetMediaSlotKey(),
		},
	}
	for _, candidate := range request.Response.GetAdCandidateList() {
		creative := candidate.GetCreative()
		if creative == nil {
			continue
		}
		bid := MeiYouItem{
			BidPrice: float64(candidate.GetBidPrice().Price) / 100,
			DeepLink: candidate.GetDeepLinkUrl(),
			ClickUrl: candidate.GetLandingUrl(),
		}

		if candidate.GetAppInfo() != nil {
			bid.Logo = candidate.GetAppInfo().Icon
			bid.Icon = candidate.GetAppInfo().Icon
			bid.Advertiser = candidate.GetAppInfo().AppName
			bid.ApkInfo = ApkInfo{
				PackageName:    candidate.GetAppInfo().PackageName,
				AppName:        candidate.GetAppInfo().AppName,
				VersionName:    candidate.GetAppInfo().AppVersion,
				AppDesc:        candidate.GetAppInfo().AppDesc,
				AppIcon:        candidate.GetAppInfo().Icon,
				DevelopName:    candidate.GetAppInfo().Develop,
				PrivatePolicy:  candidate.GetAppInfo().Privacy,
				PermissionsUrl: candidate.GetAppInfo().Permission,
			}
			for _, desc := range candidate.GetAppInfo().PermissionDesc {
				bid.ApkInfo.AppPermissions = append(bid.ApkInfo.AppPermissions, MeiYouPermissions{
					Title:   desc.PermissionLab,
					Content: desc.PermissionDesc,
				})
			}
			if candidate.GetAppInfo().WechatExt != nil {
				bid.WxMiniPath = candidate.GetAppInfo().WechatExt.ProgramPath
				bid.WxMiniId = candidate.GetAppInfo().WechatExt.ProgramId
			}
		}

		switch candidate.GetLandingAction() {
		case entity.LandingTypeDownload:
			bid.InteractType = 3
			bid.ApkInfo.Url = candidate.GetMacroReplaceLandingUrl()
		case entity.LandingTypeDeepLink:
			bid.InteractType = 2
		case entity.LandingTypeWeChatProgram:
			bid.InteractType = 4
		default:
			bid.InteractType = 1
		}

		VideoDuration := int(0)
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				bid.ImgUrl = rsc.Url
				bid.ImgWidth = rsc.Width
				bid.ImgHeight = rsc.Height
			case entity.MaterialTypeIcon, entity.MaterialTypeLogo:
				bid.Logo = rsc.Url
			case entity.MaterialTypeTitle:
				bid.Title = rsc.Data
			case entity.MaterialTypeDesc:
				bid.Description = rsc.Data
			case entity.MaterialTypeVideo:
				bid.Video = MeiYouVideo{
					VideoURL:      rsc.Url,
					VideoWidth:    rsc.Width,
					VideoHeight:   rsc.Height,
					VideoDuration: int32(rsc.Duration),
				}
				VideoDuration = int(rsc.Duration)
			}
		}

		bid.Tracking = MeiYouTracking{
			ShowPing:          candidate.GetMacroReplaceImpressionMonitorList(),
			ClickPing:         candidate.GetMacroReplaceClickMonitorList(),
			AppInstalledPing:  candidate.GetMacroReplaceAppInstallStartMonitorList(),
			OverDeepLinkPing:  candidate.GetMacroReplaceDeepLinkMonitorList(),
			StartDownloadPing: candidate.GetMacroReplaceAppDownloadStartedMonitorList(),
			OverDownloadPing:  candidate.GetMacroReplaceAppDownloadFinishedMonitorList(),
			StartInstallPing:  candidate.GetMacroReplaceAppInstallStartMonitorList(),
			OverInstallPing:   candidate.GetMacroReplaceAppInstalledMonitorList(),
		}

		if VideoDuration > 0 {
			for _, delayMonitor := range candidate.GetGenericAd().GetDelayMonitorUrlList() {
				event := entity.GetVideoTrackingEvent(delayMonitor.Delay, VideoDuration)
				switch event {
				case entity.KVideoTrackingEventStart:
					bid.Tracking.VideoStartPing = append(bid.Tracking.VideoStartPing, delayMonitor.Url)
				case entity.KVideoTrackingEventFirst:
					bid.Tracking.VideoFirstQuartilePing = append(bid.Tracking.VideoFirstQuartilePing, delayMonitor.Url)
				case entity.KVideoTrackingEventMid:
					bid.Tracking.VideoMidpointPing = append(bid.Tracking.VideoMidpointPing, delayMonitor.Url)
				case entity.KVideoTrackingEventThird:
					bid.Tracking.VideoThirdQuartilePing = append(bid.Tracking.VideoThirdQuartilePing, delayMonitor.Url)
				case entity.KVideoTrackingEventComplete:
					bid.Tracking.VideoCompletePing = append(bid.Tracking.VideoCompletePing, delayMonitor.Url)
				}
			}
		}

		if extraInt64, ok := request.GetMediaExtraInt64(candidate.GetActiveCreativeTemplateKey().String() + "_" + request.SlotType.String()); ok {
			bid.MaterialType = int(extraInt64)
		}
		response.Data.Items = []MeiYouItem{
			bid,
		}
	}

	return response, nil
}

func (mb *MeiyouBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[MeiyouBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json")
	writer.WriteWithStatus(204, nil)
	return nil
}
