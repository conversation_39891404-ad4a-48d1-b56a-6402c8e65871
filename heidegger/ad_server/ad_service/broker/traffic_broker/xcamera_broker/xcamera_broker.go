package xcamera_broker

import (
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/slice_utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"math"
	"math/big"
	"strconv"
	"strings"
)

type (
	XCamera struct {
		traffic_broker.TrafficBrokerBase

		mediaId    utils.ID
		MediaMacro *macro_builder.MediaMacro
	}
)

func NewXCamera(mediaId utils.ID) *XCamera {
	return &XCamera{
		mediaId: mediaId,
		MediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "__AUCTION_PRICE__",
			MediaClickUpXMacro:   "__UP_X__",
			MediaClickUpYMacro:   "__UP_Y__",
			MediaClickDownXMacro: "__DOWN_X__",
			MediaClickDownYMacro: "__DOWN_Y__",
		},
	}
}

func (mb *XCamera) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *XCamera) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	request.AdRequestMedia.MediaMacro = mb.MediaMacro
	return mb.ParseAdRequest(request)
}

func (mb *XCamera) ParseAdRequest(request *ad_service.AdRequest) error {
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[XCamera]request body empty")
	}

	bidRequest := &XCameraRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[XCamera]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if err = mb.buildRequest(request, bidRequest); err != nil {
		zap.L().Debug("[XCamera]BrokeRequest, parseUser failed")
		return err
	}
	mb.DoTrafficSample(request, body)
	return nil
}

func (mb *XCamera) buildRequest(request *ad_service.AdRequest, bidRequest *XCameraRequest) error {
	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[XCamera] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(utils.NewUUID())
	}
	request.SetMediaId(mb.mediaId)

	if err := mb.parseImp(bidRequest, request); err != nil {
		zap.L().Debug("[XCamera] BrokeRequest, parseImp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, request); err != nil {
		zap.L().Debug("[XCamera]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseApp(bidRequest, request); err != nil {
		zap.L().Debug("[XCamera]BrokeRequest, parseApp failed")
		return err
	}

	if err := mb.parseUser(bidRequest, request); err != nil {
		zap.L().Debug("[XCamera]BrokeRequest, parseUser failed")
		return err
	}
	if len(bidRequest.Appendix01) > 0 {
		request.App.MediaInstalledAppIds = slice_utils.Int64ToStringSlice(bidRequest.Appendix01)
	}
	return nil
}

func (mb *XCamera) parseImp(mediaRequest *XCameraRequest, bidReq *ad_service.AdRequest) error {
	if len(mediaRequest.Imp) == 0 {
		return errors.New("no imp")
	}
	imp := mediaRequest.Imp[0]
	bidReq.ImpressionId = mediaRequest.Id
	bidReq.SetMediaSlotKey(imp.TagId)
	bidReq.BidFloor = uint32(imp.Bidfloor)
	bidReq.UseHttps = true
	if imp.AdslotSize != nil {
		bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
			Width:  int64(imp.AdslotSize.Width),
			Height: int64(imp.AdslotSize.Height),
		})
		key := creative_entity.NewCreativeTemplateKey()

		key.Title().AddRequiredCount(1)
		key.Desc().AddRequiredCount(1)
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		bidReq.AppendCreativeTemplateKey(key)
	}
	if len(imp.Deals) > 0 {
		for _, d := range imp.Deals {
			sd := ad_service.SourceDeal{
				DealId:   d.Id,
				BidFloor: int64(d.Bidfloor),
			}
			bidReq.SourceDeal = append(bidReq.SourceDeal, sd)
		}
	}

	key := creative_entity.NewCreativeTemplateKey()
	key.Desc().AddRequiredCount(1).SetOptional(true)
	key.Title().AddRequiredCount(1).SetOptional(true)
	key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
	key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
	key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
	bidReq.AppendCreativeTemplateKey(key)

	return nil
}

func (mb *XCamera) parseDevice(mediaRequest *XCameraRequest, bidReq *ad_service.AdRequest) error {

	device := mediaRequest.Device
	bidReq.Device.OsType = func() entity.OsType {
		switch device.Os {
		case "iOS":
			return entity.OsTypeIOS
		case "3", "android":
			return entity.OsTypeAndroid
		default:
			return entity.OsTypeUnknown
		}
	}()
	bidReq.Device.OsVersion = device.Osv
	bidReq.Device.RequestIp = device.Ip
	if len(device.Ip) == 0 && len(device.IpV6) > 0 {
		bidReq.Device.IsIp6 = true
		bidReq.Device.RequestIp = device.IpV6
	}
	bidReq.Device.Imei = device.Imei
	bidReq.Device.ImeiMd5 = device.ImeiMd5
	bidReq.Device.Oaid = device.Oaid
	bidReq.Device.OaidMd5 = device.OaidMd5
	bidReq.Device.AndroidId = device.AndroidId
	bidReq.Device.Idfa = device.Idfa
	bidReq.Device.IdfaMd5 = device.IdfaMd5
	bidReq.Device.Mac = device.Mac
	bidReq.Device.MacMd5 = device.MacMd5
	bidReq.Device.UserAgent = device.Ua
	bidReq.Device.Model = device.Model
	bidReq.Device.Vendor = device.Make
	bidReq.Device.Brand = device.Brand
	bidReq.Device.OperatorType = func() entity.OperatorType {
		switch device.Carrier {
		case 1:
			return entity.OperatorTypeChinaMobile
		case 3:
			return entity.OperatorTypeChinaUnicom
		case 2:
			return entity.OperatorTypeChinaTelecom
		default:
			return entity.OperatorTypeUnknown
		}
	}()
	bidReq.Device.ScreenWidth = device.ScreenWidth
	bidReq.Device.ScreenHeight = device.ScreenWidth
	bidReq.Device.PPI = device.Ppi
	if device.Geo != nil {
		bidReq.Device.Lon = device.Geo.Lon
		bidReq.Device.Lat = device.Geo.Lat
	}
	bidReq.Device.BootMark = device.BootMark
	bidReq.Device.UpdateMark = device.UpdateMark
	bidReq.Device.VercodeHms = device.VerCodeOfHms
	bidReq.Device.VercodeAg = device.VerCodeOfAG
	bidReq.Device.ScreenOrientation = func() entity.ScreenOrientationType {
		switch device.Orientation {
		case 1:
			return entity.ScreenOrientationTypePortrait
		case 2:
			return entity.ScreenOrientationTypeLandscape
		default:
			return entity.ScreenOrientationTypeUnknown
		}
	}()
	bidReq.Device.DeviceStartupTime = device.BootTimeSec
	bidReq.Device.DeviceName = device.PhoneName
	bidReq.Device.SystemTotalMem = device.MemorySize
	bidReq.Device.SystemTotalDisk = device.DiskSize
	bidReq.Device.DeviceUpgradeTime = device.OsUpdateTimeSec
	bidReq.Device.RomVersion = device.RomVersion
	bidReq.Device.HardwareMachineCode = device.ModelCode
	if len(device.TimeZone) > 0 {
		tz, _ := strconv.ParseInt(device.TimeZone, 10, 32)
		bidReq.Device.TimeZone = int32(tz)
	}
	if device.CaidWithFactors != nil {
		bidReq.Device.Caid = string_utils.ConcatString(device.CaidWithFactors.CaidVersion, "_", device.CaidWithFactors.Caid)
		bidReq.Device.Caids = append(bidReq.Device.Caids, bidReq.Device.Caid)

		if device.CaidWithFactors.Factors != nil {
			bidReq.Device.DeviceStartupTime = device.CaidWithFactors.Factors.BootTimeInSec
			bidReq.Device.CountryCode = device.CaidWithFactors.Factors.CountryCode
			bidReq.Device.Language = device.CaidWithFactors.Factors.Language
			bidReq.Device.HardwareMachineCode = device.CaidWithFactors.Factors.Machine
		}
	}

	return nil

}

func (mb *XCamera) parseApp(mediaRequest *XCameraRequest, bidReq *ad_service.AdRequest) error {

	if mediaRequest.App == nil {
		return nil
	}
	bidReq.App.AppName = mediaRequest.App.AppName
	bidReq.App.AppBundle = mediaRequest.App.Bundle
	bidReq.App.AppVersion = mediaRequest.App.AppVersion
	return nil
}

func (mb *XCamera) parseUser(mediaRequest *XCameraRequest, bidReq *ad_service.AdRequest) error {

	if mediaRequest.User == nil {
		return nil
	}
	bidReq.UserAge = mediaRequest.User.Age
	bidReq.UserGender = entity.UserGenderType(mediaRequest.User.Gender)
	return nil
}

func (mb *XCamera) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("XCamera Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("XCamera")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}
	bidResponse, err := mb.buildResponse(request)
	if err != nil {
		zap.L().Error("XCamera Error in buildResponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := []byte(bidResponse.String())
	writer.SetHeader("Content-Type", "application/json")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	mb.DoTrafficResponseSample(request, data)
	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[XCamera] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", responseStr)))))
	}

	return nil
}

func (mb *XCamera) md5ToInt(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	hashStr := strings.ToUpper(hex.EncodeToString(h.Sum(nil)))

	// 将 16 进制字符串转换为大整数
	bigIntValue := new(big.Int)
	bigIntValue.SetString(hashStr, 16)

	// 对大整数取模
	result := new(big.Int)
	result.Mod(bigIntValue, big.NewInt(math.MaxInt32))

	return strconv.FormatInt(result.Int64(), 10)
}

func (mb *XCamera) buildResponse(request *ad_service.AdRequest) (*XCameraResponse, error) {
	response := &XCameraResponse{
		Id:      request.GetRequestId(),
		Bidid:   request.GetRequestId(),
		Seatbid: []*XCameraResponse_SeatBid{},
	}
	for _, candidate := range request.Response.GetAdCandidateList() {
		creative := candidate.GetCreative()
		if creative == nil {
			continue
		}
		bid := &XCameraResponse_Bid{
			Impid:   request.ImpressionId,
			AdType:  3, //目前支持3 开屏
			AdStyle: 2, //目前支持2 图片
			Price:   float64(candidate.GetBidPrice().Price),
			Crid:    "",
		}
		items := &XCameraResponse_Item{
			MediaStyle:          1,
			Imgs:                []string{},
			ClickUrl:            candidate.GetLandingUrl(),
			DplUrl:              candidate.GetDeepLinkUrl(),
			ExposalUrls:         candidate.GetMacroReplaceClickMonitorList(),
			ClickMonitorUrls:    candidate.GetMacroReplaceClickMonitorList(),
			DownloadTrackUrls:   candidate.GetMacroReplaceAppDownloadStartedMonitorList(),
			DownloadedTrackUrls: candidate.GetMacroReplaceAppDownloadFinishedMonitorList(),
			InstalledTrackUrls:  candidate.GetMacroReplaceAppInstalledMonitorList(),
			DpSuccessTrackUrls:  candidate.GetMacroReplaceDeepLinkMonitorList(),
		}

		switch candidate.GetLandingAction() {
		case entity.LandingTypeDownload:
			items.MediaStyle = 2
			items.DownloadUrl = candidate.GetLandingUrl()
		case entity.LandingTypeDeepLink:
		case entity.LandingTypeWeChatProgram:
			items.MediaStyle = 4
		default:
		}

		if candidate.GetAppInfo() != nil {
			items.Icon = candidate.GetAppInfo().Icon
			items.PackageName = candidate.GetAppInfo().PackageName
			items.DownloadAppInfo = &XCameraResponse_DownloadAppInfo{}
			items.DownloadAppInfo.AppName = candidate.GetAppInfo().AppName
			items.DownloadAppInfo.Developer = candidate.GetAppInfo().Develop
			items.DownloadAppInfo.Version = candidate.GetAppInfo().AppVersion
			items.DownloadAppInfo.PacketSize = strconv.Itoa(candidate.GetAppInfo().PackageSize)
			items.DownloadAppInfo.Privacy = candidate.GetAppInfo().Privacy
			items.DownloadAppInfo.Permission = candidate.GetAppInfo().Permission
			items.DownloadAppInfo.Desc = candidate.GetAppInfo().AppDesc
			items.DownloadAppInfo.DescURL = candidate.GetAppInfo().AppDescURL
			if candidate.GetAppInfo().WechatExt != nil {
				items.MiniProgramPath = candidate.GetAppInfo().WechatExt.ProgramPath
				items.MiniProgramId = candidate.GetAppInfo().WechatExt.ProgramId
				items.MiniProgramType = 1
			}
		}
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				items.Imgs = append(items.Imgs, rsc.Url)
			case entity.MaterialTypeTitle:
				items.Title = rsc.Data
			case entity.MaterialTypeDesc:
				items.Desc = rsc.Data
			case entity.MaterialTypeVideo:
				items.Video = &XCameraResponse_Video{
					VideoUrl:      rsc.Url,
					VideoDuration: int32(rsc.Duration),
				}
			default:

			}
		}
		bid.Items = items
		response.Seatbid = []*XCameraResponse_SeatBid{
			{
				Bid: []*XCameraResponse_Bid{
					bid,
				},
			},
		}
	}

	return response, nil
}

func (mb *XCamera) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[XCamera] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json")
	writer.WriteWithStatus(204, nil)
	return nil
}
