package tongcheng_traffic_broker

import (
	"errors"
	"strconv"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/tongcheng_traffic_broker/tongcheng_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
)

const (
	adxTemplateKey = "adxTemplate"
)

type TongChengTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        *zap.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewTongChengTrafficBroker(mediaId utils.ID) *TongChengTrafficBroker {
	return &TongChengTrafficBroker{
		log:     zap.L().With(zap.String("broker", "TongChengTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "__BID_PRICE__",
			MediaClickDownXMacro: "__DOWN_X__",
			MediaClickDownYMacro: "__DOWN_Y__",
			MediaClickUpXMacro:   "__UP_X__",
			MediaClickUpYMacro:   "__UP_Y__",
		},
	}
}

func (t *TongChengTrafficBroker) GetMediaId() utils.ID {
	return t.mediaId
}

func (t *TongChengTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(t.SendResponse)
	request.Response.SetFallbackResponseBuilder(t.SendResponse)
	request.AdRequestMedia.WinPriceMacro = t.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = t.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &tongcheng_broker_entity.BidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		t.log.WithError(err).Error("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		t.log.Infof("Parse Request start. broker request: [%v]", body)
	}

	request.SetMediaId(t.GetMediaId())
	request.SetRequestId(request.GenerateRequestId())

	t.parseApp(bidRequest, request)
	t.parseDevice(bidRequest, request)
	if err = t.parseImp(bidRequest, request); err != nil {
		t.log.WithError(err).Debugf("BrokeRequest, parseImp failed")
		return err
	}

	t.DoTrafficSample(request, body)
	return nil
}

func (t *TongChengTrafficBroker) parseApp(bidRequest *tongcheng_broker_entity.BidRequest,
	adRequest *ad_service.AdRequest) {
	if bidRequest.Media == nil {
		return
	}

	adRequest.App.AppBundle = bidRequest.Media.AppPackage
	adRequest.App.AppVersion = bidRequest.Media.AppVersion
	adRequest.App.AppName = bidRequest.Media.Name
	if len(bidRequest.Media.AppBundleID) > 0 {
		adRequest.App.AppBundle = bidRequest.Media.AppBundleID
	}
}

func (t *TongChengTrafficBroker) parseDevice(bidRequest *tongcheng_broker_entity.BidRequest,
	adRequest *ad_service.AdRequest) {
	adRequest.Device = ad_service.AdRequestDevice{}
	if bidRequest.Device != nil {
		adRequest.Device = ad_service.AdRequestDevice{
			OsType:              mappingOsType(bidRequest.Device.OS),
			OsVersion:           bidRequest.Device.OSVersion,
			DeviceType:          mappingDeviceType(bidRequest.Device.DeviceType),
			IsMobile:            bidRequest.Device.DeviceType == 1,
			DeviceStartupTime:   bidRequest.Device.DeviceStartSec,
			DeviceUpgradeTime:   bidRequest.Device.SystemUpdateSec,
			DeviceName:          bidRequest.Device.DeviceNameMd5,
			DeviceNameMd5:       bidRequest.Device.DeviceNameMd5,
			HardwareMachineCode: bidRequest.Device.HardwareMachine,
			Idfa:                bidRequest.Device.IDFA,
			IdfaMd5:             bidRequest.Device.IDFAMD5,
			Imei:                bidRequest.Device.Imei,
			ImeiMd5:             bidRequest.Device.ImeiMd5,
			Mac:                 bidRequest.Device.Mac,
			MacMd5:              md5_utils.GetMd5String(bidRequest.Device.Mac),
			AndroidId:           bidRequest.Device.AndroidID,
			AndroidIdMd5:        bidRequest.Device.AndroidIDMd5,
			Oaid:                bidRequest.Device.Oaid,
			OaidMd5:             bidRequest.Device.OaidMd5,
			Model:               bidRequest.Device.Model,
			Brand:               bidRequest.Device.Make,
			Language:            bidRequest.Device.Language,
			CountryCode:         bidRequest.Device.Country,
			ScreenHeight:        bidRequest.Device.ScreenHeight,
			ScreenWidth:         bidRequest.Device.ScreenWidth,
		}
		if len(bidRequest.Device.TimeZone) > 0 {
			intTimeZone, _ := strconv.ParseInt(bidRequest.Device.TimeZone, 10, 64)
			adRequest.Device.TimeZone = int32(intTimeZone)
		}
		if len(bidRequest.Device.Caid) > 0 {
			adRequest.Device.Caid = bidRequest.Device.Caid
			if len(bidRequest.Device.CaidVersion) > 0 {
				adRequest.Device.Caid = bidRequest.Device.CaidVersion + "_" + bidRequest.Device.Caid
			}
			adRequest.Device.CaidVersion = bidRequest.Device.CaidVersion
			adRequest.Device.CaidRaw = bidRequest.Device.Caid
			adRequest.Device.CaidMd5Raw = md5_utils.GetMd5String(bidRequest.Device.Caid)
			adRequest.Device.CaidMd5 = adRequest.Device.CaidVersion + "_" + adRequest.Device.CaidMd5Raw
		}
		if len(bidRequest.Device.PhysicalMemoryByte) > 0 {
			adRequest.Device.SystemTotalMem, _ = strconv.ParseInt(bidRequest.Device.PhysicalMemoryByte, 10, 64)
			adRequest.Device.SystemTotalMem /= 1024
		}
		if len(bidRequest.Device.HarddiskSizeByte) > 0 {
			adRequest.Device.SystemTotalDisk, _ = strconv.ParseInt(bidRequest.Device.HarddiskSizeByte, 10, 64)
			adRequest.Device.SystemTotalDisk /= 1024
		}
	}

	if bidRequest.Network != nil {
		adRequest.Device.RequestIp = bidRequest.Network.IP
		adRequest.Device.UserAgent = bidRequest.Network.UA
		adRequest.Device.WebviewUA = bidRequest.Network.UA
		adRequest.Device.OperatorType = mappingOperatorType(bidRequest.Network.Carrier)
		adRequest.Device.ConnectionType = mappingConnectionType(bidRequest.Network.ConnectType)
		adRequest.Device.OperatorName = adRequest.Device.ConnectionType.String()
		if len(bidRequest.Network.IPV6) > 0 {
			adRequest.Device.RequestIp = bidRequest.Network.IPV6
			adRequest.Device.IsIp6 = true
		}
	}

	if bidRequest.Geo != nil {
		adRequest.Device.Lat = float64(bidRequest.Geo.Lat)
		adRequest.Device.Lon = float64(bidRequest.Geo.Lng)
	}
}

func (t *TongChengTrafficBroker) parseImp(bidRequest *tongcheng_broker_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	if bidRequest.Pos == nil {
		return errors.New("impressions empty")
	}

	adRequest.SetMediaSlotKey(bidRequest.Pos.PositionID)
	adRequest.BidFloor = uint32(bidRequest.Pos.BidFloor)
	adRequest.BidType = entity.BidTypeCpm
	adRequest.SlotHeight = uint32(bidRequest.Pos.Height)
	adRequest.SlotWidth = uint32(bidRequest.Pos.Width)

	adxTemplateMap := make(map[uint64]int)

	// 图文
	imgTemplate := creative_entity.NewCreativeTemplateKey()
	imgTemplate.Title().AddRequiredCount(1).SetOptional(true)
	imgTemplate.Desc().AddRequiredCount(1).SetOptional(true)
	imgTemplate.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	imgTemplate.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
	adRequest.AppendCreativeTemplateKey(imgTemplate)
	adxTemplateMap[imgTemplate.Uint64()] = 0

	// 视频
	videoTemplate := creative_entity.NewCreativeTemplateKey()
	videoTemplate.Title().AddRequiredCount(1).SetOptional(true)
	videoTemplate.Desc().AddRequiredCount(1).SetOptional(true)
	videoTemplate.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
	videoTemplate.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	videoTemplate.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
	adRequest.AppendCreativeTemplateKey(videoTemplate)
	adxTemplateMap[videoTemplate.Uint64()] = 1

	adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)
	return nil
}

func (t *TongChengTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		t.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("TongChengTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return t.SendFallbackResponse(request, writer)
	}

	bidResponse, err := t.buildResponse(request)
	if err != nil {
		t.log.WithError(err).Error("buildResponse err")
		return err
	}

	if err = t.BuildHttpSonicJsonResponse(request, writer, bidResponse); err != nil {
		return err
	}

	t.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		t.log.Infof("SendResponse success, response: %s", responseStr)
	}

	return nil
}

func (t *TongChengTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		t.log.Infof("Build Fallback Response start. bid response: [%v]", request.Response)
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json")
	_, err := writer.WriteWithStatus(204, nil)
	if err != nil {
		return err
	}
	return nil
}

func (t *TongChengTrafficBroker) buildResponse(adRequest *ad_service.AdRequest) (*tongcheng_broker_entity.BidResponse, error) {
	for _, candidate := range adRequest.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		resBid := &tongcheng_broker_entity.Bid{
			AdID:           strconv.FormatInt(int64(genericAd.GetAdId()), 10),
			ECPM:           candidate.GetBidPrice().Price,
			ImpressionLink: candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			ClickLink:      candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
			LandingPageURL: genericAd.GetLandingUrl(),
			PackageURL:     genericAd.GetDownloadUrl(),
			ConversionEvent: &tongcheng_broker_entity.ConversionEvent{
				DownloadStarted:  genericAd.GetAppDownloadStartedMonitorList(),
				DownloadFinished: genericAd.GetAppDownloadFinishedMonitorList(),
				InstallStarted:   genericAd.GetAppInstallStartMonitorList(),
				InstallFinished:  genericAd.GetAppInstalledMonitorList(),
				DeeplinkFail:     genericAd.GetDeepLinkFailedMonitorList(),
				DeeplinkSuccess:  genericAd.GetDeepLinkMonitorList(),
			},
		}

		if adRequest.Device.GetOsType() == entity.OsTypeIOS {
			resBid.UniversalLink = genericAd.GetDeepLinkUrl()
		} else {
			resBid.DeepLinkURL = genericAd.GetDeepLinkUrl()
		}

		for _, material := range candidate.GetSelectedMaterialList() {
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				resBid.Title = material.Data
			case entity.MaterialTypeDesc:
				resBid.Description = material.Data
			case entity.MaterialTypeImage:
				image := &tongcheng_broker_entity.Image{
					URL:    material.Url,
					Width:  material.Width,
					Height: material.Height,
				}
				if resBid.Image == nil {
					resBid.Image = image
				} else {
					resBid.ImageList = append(resBid.ImageList, image)
				}
			case entity.MaterialTypeCoverImage:
				if resBid.Video == nil {
					resBid.Video = &tongcheng_broker_entity.Video{}
				}
				resBid.Video.PreviewImgURL = material.Url
			case entity.MaterialTypeVideo:
				if resBid.Video == nil {
					resBid.Video = &tongcheng_broker_entity.Video{}
				}
				resBid.Video.URL = material.Url
				resBid.Video.Width = material.Width
				resBid.Video.Height = material.Height
				resBid.Video.Size = material.FileSize
				resBid.Video.DurationSize = int32(material.Duration)
			default:
			}
		}
		if resBid.Video != nil && resBid.Video.PreviewImgURL == "" && resBid.Image != nil {
			resBid.Video.PreviewImgURL = resBid.Image.URL
		}

		if genericAd.GetAppInfo() != nil {
			resBid.AppID = genericAd.GetAppInfo().AppID
			resBid.AppName = genericAd.GetAppInfo().AppName
			resBid.AppPackage = genericAd.GetAppInfo().PackageName
			resBid.AppIconURL = genericAd.GetAppInfo().Icon
			resBid.AppSize = strconv.Itoa(genericAd.GetAppInfo().PackageSize)
			resBid.AppVersion = genericAd.GetAppInfo().AppVersion
			resBid.AppDeveloper = genericAd.GetAppInfo().Develop
			resBid.PrivacyAgreement = genericAd.GetAppInfo().Privacy
			resBid.PermissionsURL = genericAd.GetAppInfo().Permission

			if genericAd.GetAppInfo().WechatExt != nil {
				resBid.WechatAppUserName = genericAd.GetAppInfo().WechatExt.ProgramId
				resBid.WechatAppPath = genericAd.GetAppInfo().WechatExt.ProgramPath
			}
		}

		return &tongcheng_broker_entity.BidResponse{
			Data: map[string]*tongcheng_broker_entity.BidData{
				adRequest.GetMediaSlotKey(): {List: []*tongcheng_broker_entity.Bid{resBid}},
			},
		}, nil
	}

	return nil, err_code.ErrNoCandidate
}

func mappingDeviceType(deviceType int32) entity.DeviceType {
	switch deviceType {
	case 1:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePad
	case 3:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeUnknown
	}
}

func mappingOsType(os string) entity.OsType {
	switch os {
	case "ios":
		return entity.OsTypeIOS
	case "android":
		return entity.OsTypeAndroid
	default:
		return entity.OsTypeUnknown
	}
}

func mappingConnectionType(connectType int32) entity.ConnectionType {
	switch connectType {
	case 1:
		return entity.ConnectionTypeWifi
	case 2:
		return entity.ConnectionType2G
	case 3:
		return entity.ConnectionType3G
	case 4, 5, 6:
		return entity.ConnectionType4G
	case 7:
		return entity.ConnectionType5G
	case 8:
		return entity.ConnectionTypeNetEthernet
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingOperatorType(carrier int32) entity.OperatorType {
	switch carrier {
	case 1:
		return entity.OperatorTypeChinaMobile
	case 2:
		return entity.OperatorTypeChinaUnicom
	case 3:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}
