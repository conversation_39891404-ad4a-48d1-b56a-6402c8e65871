package zuiyou_broker

import (
	"errors"
	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/zuiyou_broker/zuiyou_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"strconv"
	"time"
)

const (
	adxTemplateKey   = "adxTemplate"
	requestStartTime = "start_time"
)

type ZuiYouTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        *zap.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewZuiYouTrafficBroker(mediaId utils.ID) *ZuiYouTrafficBroker {
	return &ZuiYouTrafficBroker{
		log:     zap.L().With(zap.String("broker", "ZuiYouTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro: "__WIN_PRICE_SECURE__",
		},
	}
}

func (z *ZuiYouTrafficBroker) GetMediaId() utils.ID {
	return z.mediaId
}

func (z *ZuiYouTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(z.SendResponse)
	request.Response.SetFallbackResponseBuilder(z.SendResponse)
	request.AdRequestMedia.WinPriceMacro = z.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = z.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &zuiyou_proto.RtbRequest{}
	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		z.log.WithError(err).Error("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		z.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	request.SetMediaId(z.GetMediaId())
	request.SetRequestId(bidRequest.RequestId)
	if len(bidRequest.RequestId) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}
	if bidRequest.Timeout > 0 {
		request.TMax = int(bidRequest.Timeout)
	}
	request.AddMediaExtraInt64(requestStartTime, time.Now().UnixMilli())

	z.parseApp(bidRequest, request)
	z.parseUser(bidRequest, request)
	z.parseDevice(bidRequest, request)
	if err = z.parseImp(bidRequest, request); err != nil {
		z.log.WithError(err).Debugf("BrokeRequest, parseImp failed")
		return err
	}

	z.DoTrafficSamplePb(request, bidRequest)
	return nil
}

func (z *ZuiYouTrafficBroker) parseApp(bidRequest *zuiyou_proto.RtbRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.App != nil {
		adRequest.App.AppName = bidRequest.App.Name
		adRequest.App.AppBundle = bidRequest.App.PackageName
		adRequest.App.AppVersion = bidRequest.App.Version
	}
	if len(bidRequest.PkgList) > 0 {
		adRequest.App.MediaInstalledAppIds = append(adRequest.App.MediaInstalledAppIds, bidRequest.PkgList...)
	}
}

func (z *ZuiYouTrafficBroker) parseUser(bidRequest *zuiyou_proto.RtbRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.User != nil {
		adRequest.UserAge = int32(bidRequest.User.Age)
		adRequest.UserGender = mappingGender(bidRequest.User.Gender)
		adRequest.UserId = bidRequest.User.Uid
	}
}

func (z *ZuiYouTrafficBroker) parseDevice(bidRequest *zuiyou_proto.RtbRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.Device == nil {
		return
	}

	adRequest.Device = ad_service.AdRequestDevice{
		OsType:            mappingOsType(bidRequest.Device.OsType),
		DeviceType:        mappingDeviceType(bidRequest.Device.DeviceType),
		IsMobile:          bidRequest.Device.DeviceType == zuiyou_proto.RtbRequest_Device_PHONE,
		DeviceInitTime:    bidRequest.Device.BirthTime,
		DeviceStartupTime: bidRequest.Device.StartupTime,
		DeviceUpgradeTime: bidRequest.Device.MbTime,
		SystemTotalMem:    int64(bidRequest.Device.MemTotal),
		SystemTotalDisk:   int64(bidRequest.Device.DiskTotal),
		DeviceName:        bidRequest.Device.PhoneName,
		DeviceNameMd5:     bidRequest.Device.PhoneName,
		BootMark:          bidRequest.Device.BootMark,
		UpdateMark:        bidRequest.Device.UpdateMark,
		Idfa:              bidRequest.Device.Idfa,
		Imei:              bidRequest.Device.Imei,
		Mac:               bidRequest.Device.Mac,
		AndroidId:         bidRequest.Device.AndroidId,
		Oaid:              bidRequest.Device.Oaid,
		Idfv:              bidRequest.Device.Idfv,
		IdfaMd5:           bidRequest.Device.IdfaMd5,
		ImeiMd5:           bidRequest.Device.ImeiMd5,
		Model:             bidRequest.Device.Model,
		Brand:             bidRequest.Device.Make,
		Vendor:            bidRequest.Device.Make,
		Language:          bidRequest.Device.Language,
		CountryCode:       bidRequest.Device.CountryCode,
		ScreenHeight:      int32(bidRequest.Device.ScreenHeight),
		ScreenWidth:       int32(bidRequest.Device.ScreenWidth),
		OsVersion:         bidRequest.Device.OsVersion,
		UserAgent:         bidRequest.Device.Ua,
		WebviewUA:         bidRequest.Device.Ua,
		RequestIp:         bidRequest.Device.Ip,
		ConnectionType:    mappingConnectionType(bidRequest.Device.ConnType),
		OperatorType:      mappingOperatorType(bidRequest.Device.Carrier),
		OperatorName:      mappingOperatorType(bidRequest.Device.Carrier).String(),
	}

	if len(bidRequest.Device.Ipv6) > 0 {
		adRequest.Device.IsIp6 = true
		adRequest.Device.RequestIp = bidRequest.Device.Ipv6
	}
	if bidRequest.Device.Geo != nil {
		adRequest.Device.Lat = float64(bidRequest.Device.Geo.Latitude)
		adRequest.Device.Lon = float64(bidRequest.Device.Geo.Longitude)
	}
	if len(bidRequest.Device.DeviceModel) > 0 {
		adRequest.Device.Model = bidRequest.Device.DeviceModel
	}

	for _, caid := range bidRequest.Device.CaidList {
		if caid == nil {
			continue
		}
		adRequest.Device.CaidRaw = caid.Caid
		adRequest.Device.CaidVersion = caid.Version
		adRequest.Device.Caid = caid.Version + "_" + caid.Caid
		adRequest.Device.Caids = append(adRequest.Device.Caids, caid.Version+"_"+caid.Caid)
	}
}

func (z *ZuiYouTrafficBroker) parseImp(bidRequest *zuiyou_proto.RtbRequest, adRequest *ad_service.AdRequest) error {
	for _, adSlot := range bidRequest.Adslots {
		for _, size := range adSlot.Size_ {
			if size.Width > 0 && size.Height > 0 {
				if adRequest.SlotWidth < 1 {
					adRequest.SlotWidth = size.Width
					adRequest.SlotHeight = size.Height
				} else {
					adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
						Width:  int64(size.Width),
						Height: int64(size.Height),
					})
				}
			}
		}

		adRequest.ImpressionId = adSlot.Impid
		adRequest.SetMediaSlotKey(adSlot.TagId)
		adRequest.BidFloor = adSlot.Price
		adRequest.BidType = entity.BidTypeCpm

		adxTemplateMap := make(map[uint64]int)
		for _, template := range adSlot.Templates {
			key := creative_entity.NewCreativeTemplateKey()
			var hasVideo bool
			//var hasImg bool
			switch template {
			case zuiyou_proto.Template_SPLASH_DEFAULT,
				zuiyou_proto.Template_SPLASH_BROWSER,
				zuiyou_proto.Template_SPLASH_OPEN_APP,
				zuiyou_proto.Template_SPLASH_DOWNLOAD:
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				//hasImg = true
			case zuiyou_proto.Template_FEED_DEFAULT,
				zuiyou_proto.Template_FEED_BROWSER,
				zuiyou_proto.Template_FEED_DOWNLOAD,
				zuiyou_proto.Template_FEED_OPEN_APP,
				zuiyou_proto.Template_REVIEW_DEFAULT,
				zuiyou_proto.Template_REVIEW_BROWSER,
				zuiyou_proto.Template_REVIEW_DOWNLOAD,
				zuiyou_proto.Template_REVIEW_OPEN_APP,
				zuiyou_proto.Template_POPUP_DEFAULT,
				zuiyou_proto.Template_POPUP_BROWSER,
				zuiyou_proto.Template_POPUP_DOWNLOAD,
				zuiyou_proto.Template_POPUP_OPEN_APP:
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				//hasImg = true
				hasVideo = true
			case zuiyou_proto.Template_REWARD_DEFAULT,
				zuiyou_proto.Template_REWARD_BROWSER,
				zuiyou_proto.Template_REWARD_DOWNLOAD,
				zuiyou_proto.Template_REWARD_OPEN_APP:
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				//hasImg = true
				hasVideo = true
			default:
				continue
			}
			for _, materialType := range adSlot.MaterialTypes {
				switch materialType {
				//case zuiyou_proto.MType_IMG:
				//if !hasImg {
				//	key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				//	hasImg = true
				//}
				case zuiyou_proto.MType_VIDEO:
					if !hasVideo {
						key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
						hasVideo = true
					}
				}
			}

			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = int(template)
		}

		adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)
		break
	}

	if bidRequest.Pmp != nil {
		adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
			DealId:   strconv.FormatUint(uint64(bidRequest.Pmp.DealId), 10),
			BidFloor: int64(adRequest.BidFloor),
		})
	}

	return nil
}

func (z *ZuiYouTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		z.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("ZuiYouTrafficBroker")
	}

	bidResponse, err := z.buildResponse(request)
	if err != nil {
		z.log.WithError(err).Error("buildResponse err")
		return err
	}
	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()

	buffer.EnsureSize(bidResponse.Size())
	_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		z.log.Errorf("Error in JSON marshalling:%s", err.Error())
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/octet-stream")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	z.DoTrafficResponseSamplePb(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		z.log.Infof("SendResponse success, response: %s", responseStr)
	}

	return nil
}

func (z *ZuiYouTrafficBroker) buildResponse(request *ad_service.AdRequest) (*zuiyou_proto.RtbResponse, error) {
	bidResponse := &zuiyou_proto.RtbResponse{
		ResponseId: request.GetRequestId(),
		StatusCode: zuiyou_proto.RtbResponse_REQ_OK,
	}
	if v, ok := request.GetMediaExtraInt64(requestStartTime); ok {
		bidResponse.ProcessingTimeMs = uint32(time.Now().UnixMilli() - v)
	}

	if request.Response.GetError() != nil {
		bidResponse.StatusCode = zuiyou_proto.RtbResponse_REQ_FAIL
		return bidResponse, nil
	}
	if request.Response.NoCandidate() {
		bidResponse.StatusCode = zuiyou_proto.RtbResponse_DROP_OTHER
		return bidResponse, nil
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		resBid := &zuiyou_proto.RtbResponse_Ad{
			Impid:     request.ImpressionId,
			PriceType: zuiyou_proto.RtbResponse_Ad_CPM,
			Price:     uint64(candidate.GetBidPrice().Price),
			Material: &zuiyou_proto.RtbResponse_Ad_Material{
				Name:    "",
				IconUrl: "",
				Video:   nil,
				Img:     nil,
				Desc:    "",
			},
			Template: 0,
			Interaction: &zuiyou_proto.RtbResponse_Ad_Interaction{
				LandingUrl:  genericAd.GetLandingUrl(),
				BrowserUrl:  genericAd.GetH5LandingUrl(),
				DownloadUrl: genericAd.GetDownloadUrl(),
				InvokeUrl:   genericAd.GetDeepLinkUrl(),
			},
			Tracking: &zuiyou_proto.RtbResponse_Ad_Tracking{
				ImpTracking:            candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
				ClickTracking:          candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
				VideoStartTracking:     genericAd.GetVideoStartUrlList(),
				InvokeFailedTracking:   genericAd.GetDeepLinkFailedMonitorList(),
				InvokeWaitSuccTracking: genericAd.GetDeepLinkMonitorList(),
				VideoCloseTracking:     genericAd.GetVideoCloseUrlList(),
				StartDownloadTracking:  genericAd.GetAppDownloadStartedMonitorList(),
				FinishDownloadTracking: genericAd.GetAppDownloadFinishedMonitorList(),
				FinishInstallTracking:  genericAd.GetAppInstalledMonitorList(),
			},
		}

		if creative.GetCreativeId() != 0 {
			resBid.CreativeId = strconv.FormatInt(int64(creative.GetCreativeId()), 10)
		} else {
			resBid.CreativeId = creative.GetCreativeKey()
		}

		adxTemplateMap := request.GetMediaExtraDataWithDefault(adxTemplateKey, make(map[uint64]int)).(map[uint64]int)
		template := zuiyou_proto.Template(adxTemplateMap[uint64(candidate.GetActiveCreativeTemplateKey())])
		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			switch template {
			case zuiyou_proto.Template_SPLASH_DEFAULT,
				zuiyou_proto.Template_SPLASH_BROWSER,
				zuiyou_proto.Template_SPLASH_OPEN_APP,
				zuiyou_proto.Template_SPLASH_DOWNLOAD:
				template = zuiyou_proto.Template_SPLASH_DOWNLOAD
			case zuiyou_proto.Template_FEED_DEFAULT,
				zuiyou_proto.Template_FEED_BROWSER,
				zuiyou_proto.Template_FEED_DOWNLOAD,
				zuiyou_proto.Template_FEED_OPEN_APP:
				template = zuiyou_proto.Template_FEED_DOWNLOAD
			case zuiyou_proto.Template_REVIEW_DEFAULT,
				zuiyou_proto.Template_REVIEW_BROWSER,
				zuiyou_proto.Template_REVIEW_DOWNLOAD,
				zuiyou_proto.Template_REVIEW_OPEN_APP:
				template = zuiyou_proto.Template_REVIEW_DOWNLOAD
			case zuiyou_proto.Template_POPUP_DEFAULT,
				zuiyou_proto.Template_POPUP_BROWSER,
				zuiyou_proto.Template_POPUP_DOWNLOAD,
				zuiyou_proto.Template_POPUP_OPEN_APP:
				template = zuiyou_proto.Template_POPUP_DOWNLOAD
			case zuiyou_proto.Template_REWARD_DEFAULT,
				zuiyou_proto.Template_REWARD_BROWSER,
				zuiyou_proto.Template_REWARD_DOWNLOAD,
				zuiyou_proto.Template_REWARD_OPEN_APP:
				template = zuiyou_proto.Template_REWARD_DOWNLOAD
			default:
				continue
			}
		} else if genericAd.GetLandingAction() == entity.LandingTypeDeepLink {
			switch template {
			case zuiyou_proto.Template_SPLASH_DEFAULT,
				zuiyou_proto.Template_SPLASH_BROWSER,
				zuiyou_proto.Template_SPLASH_OPEN_APP,
				zuiyou_proto.Template_SPLASH_DOWNLOAD:
				template = zuiyou_proto.Template_SPLASH_OPEN_APP
			case zuiyou_proto.Template_FEED_DEFAULT,
				zuiyou_proto.Template_FEED_BROWSER,
				zuiyou_proto.Template_FEED_DOWNLOAD,
				zuiyou_proto.Template_FEED_OPEN_APP:
				template = zuiyou_proto.Template_FEED_OPEN_APP
			case zuiyou_proto.Template_REVIEW_DEFAULT,
				zuiyou_proto.Template_REVIEW_BROWSER,
				zuiyou_proto.Template_REVIEW_DOWNLOAD,
				zuiyou_proto.Template_REVIEW_OPEN_APP:
				template = zuiyou_proto.Template_REVIEW_OPEN_APP
			case zuiyou_proto.Template_POPUP_DEFAULT,
				zuiyou_proto.Template_POPUP_BROWSER,
				zuiyou_proto.Template_POPUP_DOWNLOAD,
				zuiyou_proto.Template_POPUP_OPEN_APP:
				template = zuiyou_proto.Template_POPUP_OPEN_APP
			case zuiyou_proto.Template_REWARD_DEFAULT,
				zuiyou_proto.Template_REWARD_BROWSER,
				zuiyou_proto.Template_REWARD_DOWNLOAD,
				zuiyou_proto.Template_REWARD_OPEN_APP:
				template = zuiyou_proto.Template_REWARD_OPEN_APP
			default:
				continue
			}
		} else {
			switch template {
			case zuiyou_proto.Template_SPLASH_DEFAULT,
				zuiyou_proto.Template_SPLASH_BROWSER,
				zuiyou_proto.Template_SPLASH_OPEN_APP,
				zuiyou_proto.Template_SPLASH_DOWNLOAD:
				template = zuiyou_proto.Template_SPLASH_DEFAULT
			case zuiyou_proto.Template_FEED_DEFAULT,
				zuiyou_proto.Template_FEED_BROWSER,
				zuiyou_proto.Template_FEED_DOWNLOAD,
				zuiyou_proto.Template_FEED_OPEN_APP:
				template = zuiyou_proto.Template_FEED_DEFAULT
			case zuiyou_proto.Template_REVIEW_DEFAULT,
				zuiyou_proto.Template_REVIEW_BROWSER,
				zuiyou_proto.Template_REVIEW_DOWNLOAD,
				zuiyou_proto.Template_REVIEW_OPEN_APP:
				template = zuiyou_proto.Template_REVIEW_DEFAULT
			case zuiyou_proto.Template_POPUP_DEFAULT,
				zuiyou_proto.Template_POPUP_BROWSER,
				zuiyou_proto.Template_POPUP_DOWNLOAD,
				zuiyou_proto.Template_POPUP_OPEN_APP:
				template = zuiyou_proto.Template_POPUP_DEFAULT
			case zuiyou_proto.Template_REWARD_DEFAULT,
				zuiyou_proto.Template_REWARD_BROWSER,
				zuiyou_proto.Template_REWARD_DOWNLOAD,
				zuiyou_proto.Template_REWARD_OPEN_APP:
				template = zuiyou_proto.Template_REWARD_DEFAULT
			default:
				continue
			}
		}
		resBid.Template = template

		var imgUrls []string
		var imgH, imgW int32
		for _, material := range candidate.GetSelectedMaterialList() {
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				resBid.Material.Name = material.Data
			case entity.MaterialTypeDesc:
				resBid.Material.Desc = material.Data
			case entity.MaterialTypeIcon:
				resBid.Material.IconUrl = material.Url
			case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
				imgUrls = append(imgUrls, material.Url)
				if material.Width > 0 && material.Height > 0 {
					if imgW == 0 && imgH == 0 {
						imgW = material.Width
						imgH = material.Height
					}
				}
			case entity.MaterialTypeVideo:
				resBid.Material.Video = &zuiyou_proto.RtbResponse_Ad_Material_Video{
					VideoUrl: material.Url,
					Height:   uint32(material.Width),
					Width:    uint32(material.Height),
					Dur:      uint32(material.Duration),
				}
				if material.Width < 1 || material.Height < 1 {
					resBid.Material.Video.Height = request.SlotHeight
					resBid.Material.Video.Width = request.SlotWidth
				}
			default:
			}
		}
		if len(resBid.Material.Name) < 1 {
			resBid.Material.Name = "点击查看详情"
		}
		if len(resBid.Material.Desc) < 1 {
			resBid.Material.Desc = "点击查看详情"
		}
		if resBid.Material.Video != nil {
			if len(imgUrls) > 0 {
				resBid.Material.Video.CoverUrl = imgUrls[0]
			}
		} else {
			resBid.Material.Img = &zuiyou_proto.RtbResponse_Ad_Material_Image{
				ImgUrls: imgUrls,
				Height:  uint32(imgH),
				Width:   uint32(imgW),
			}
			if imgW < 1 || imgH < 1 {
				resBid.Material.Img.Height = request.SlotHeight
				resBid.Material.Img.Width = request.SlotWidth
			}
		}

		if genericAd.GetAppInfo() != nil {
			resBid.Interaction.AppVersion = genericAd.GetAppInfo().AppVersion
			resBid.Interaction.DeveloperName = genericAd.GetAppInfo().Develop
			resBid.Interaction.PkgName = genericAd.GetAppInfo().PackageName
			resBid.Interaction.PermissionUrl = genericAd.GetAppInfo().Permission
			resBid.Interaction.PrivacyUrl = genericAd.GetAppInfo().Privacy
			resBid.Interaction.AppIntroUrl = genericAd.GetAppInfo().AppDescURL
			if len(genericAd.GetAppInfo().AppName) > 0 {
				resBid.Material.Name = genericAd.GetAppInfo().AppName
			}
			if len(genericAd.GetAppInfo().Icon) > 0 {
				resBid.Material.IconUrl = genericAd.GetAppInfo().Icon
			}
			if genericAd.GetAppInfo().WechatExt != nil {
				resBid.Interaction.WxMiniappPath = genericAd.GetAppInfo().WechatExt.ProgramPath
				resBid.Interaction.WxMiniappId = genericAd.GetAppInfo().WechatExt.ProgramId
			}
		}

		bidResponse.SiteName = net_utils.GetDomainFromUrl(genericAd.GetLandingUrl())
		bidResponse.Ads = append(bidResponse.Ads, resBid)
		return bidResponse, nil
	}

	bidResponse.StatusCode = zuiyou_proto.RtbResponse_DROP_OTHER
	return bidResponse, nil
}

func mappingGender(gender zuiyou_proto.RtbRequest_User_Gender) entity.UserGenderType {
	switch gender {
	case zuiyou_proto.RtbRequest_User_FEMALE:
		return entity.UserGenderWoman
	case zuiyou_proto.RtbRequest_User_MALE:
		return entity.UserGenderMan
	default:
		return entity.UserGenderUnknown
	}
}

func mappingOperatorType(carrier zuiyou_proto.RtbRequest_Device_CarrierType) entity.OperatorType {
	switch carrier {
	case zuiyou_proto.RtbRequest_Device_MOBILE:
		return entity.OperatorTypeChinaMobile
	case zuiyou_proto.RtbRequest_Device_UNICOM:
		return entity.OperatorTypeChinaUnicom
	case zuiyou_proto.RtbRequest_Device_TELECOM:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connType zuiyou_proto.RtbRequest_Device_ConnectionType) entity.ConnectionType {
	switch connType {
	case zuiyou_proto.RtbRequest_Device_WIFI:
		return entity.ConnectionTypeWifi
	case zuiyou_proto.RtbRequest_Device_MOBILE_2G:
		return entity.ConnectionType2G
	case zuiyou_proto.RtbRequest_Device_MOBILE_3G:
		return entity.ConnectionType3G
	case zuiyou_proto.RtbRequest_Device_MOBILE_4G:
		return entity.ConnectionType4G
	case zuiyou_proto.RtbRequest_Device_MOBILE_5G:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingOsType(osType zuiyou_proto.RtbRequest_Device_OsType) entity.OsType {
	switch osType {
	case zuiyou_proto.RtbRequest_Device_ANDROID:
		return entity.OsTypeAndroid
	case zuiyou_proto.RtbRequest_Device_IOS:
		return entity.OsTypeIOS
	default:
		return entity.OsTypeUnknown
	}
}

func mappingDeviceType(deviceType zuiyou_proto.RtbRequest_Device_DeviceType) entity.DeviceType {
	switch deviceType {
	case zuiyou_proto.RtbRequest_Device_PHONE:
		return entity.DeviceTypeMobile
	case zuiyou_proto.RtbRequest_Device_TABLET:
		return entity.DeviceTypePad
	default:
		return entity.DeviceTypeUnknown
	}
}
