package yezichuanmei_traffic_broker

import (
	"errors"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/yezichuanmei_traffic_broker/yezichuanmei_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/geo_parser"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
)

const (
	adxTemplateKey = "adxTemplate"
	creativeType   = "creativeType"
)

type YeZiChuanMeiTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        *zap.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewYeZiChuanMeiTrafficBroker(mediaId utils.ID) *YeZiChuanMeiTrafficBroker {
	return &YeZiChuanMeiTrafficBroker{
		log:     zap.L().With(zap.String("broker", "YeZiChuanMeiTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro: "{WIN_PRICE}",
		},
	}
}

func (y *YeZiChuanMeiTrafficBroker) GetMediaId() utils.ID {
	return y.mediaId
}

func (y *YeZiChuanMeiTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(y.SendResponse)
	request.Response.SetFallbackResponseBuilder(y.SendResponse)
	request.AdRequestMedia.WinPriceMacro = y.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = y.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &yezichuanmei_proto.BidRequest{}
	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		y.log.WithError(err).Error("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		y.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	request.SetMediaId(y.GetMediaId())
	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}

	y.parseApp(bidRequest, request)
	y.parseUser(bidRequest, request)
	y.parseDevice(bidRequest, request)
	if err = y.parseImp(bidRequest, request); err != nil {
		y.log.WithError(err).Debugf("BrokeRequest, parseImp failed")
		return err
	}

	y.DoTrafficSamplePb(request, bidRequest)
	return nil
}

func (y *YeZiChuanMeiTrafficBroker) parseApp(bidRequest *yezichuanmei_proto.BidRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.App == nil {
		return
	}

	adRequest.App.AppName = bidRequest.App.Name
	adRequest.App.AppVersion = bidRequest.App.Ver
	adRequest.App.AppBundle = bidRequest.App.Bundle
}

func (y *YeZiChuanMeiTrafficBroker) parseUser(bidRequest *yezichuanmei_proto.BidRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.User == nil {
		return
	}

	adRequest.UserId = bidRequest.User.Id
	adRequest.App.InstalledApp = bidRequest.User.InstalledApps
}

func (y *YeZiChuanMeiTrafficBroker) parseDevice(bidRequest *yezichuanmei_proto.BidRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.Device == nil {
		return
	}

	adRequest.Device = ad_service.AdRequestDevice{
		OsType:            mappingOsType(bidRequest.Device.OsType),
		OsVersion:         bidRequest.Device.OsVersion,
		DeviceType:        mappingDeviceType(bidRequest.Device.DeviceType),
		IsMobile:          bidRequest.Device.DeviceType == 1,
		DeviceUpgradeTime: bidRequest.Device.OsUpdateTime,
		Idfa:              bidRequest.Device.Idfa,
		IdfvMd5:           bidRequest.Device.IdfaMd5,
		Imei:              bidRequest.Device.Imei,
		ImeiMd5:           bidRequest.Device.ImeiMd5,
		Mac:               bidRequest.Device.Mac,
		MacMD5:            bidRequest.Device.MacMd5,
		AndroidId:         bidRequest.Device.AndroidId,
		AndroidIdMd5:      bidRequest.Device.AndroidIdMd5,
		Oaid:              bidRequest.Device.Oaid,
		OaidMd5:           bidRequest.Device.OaidMd5,
		Model:             bidRequest.Device.Model,
		Brand:             bidRequest.Device.Make,
		ScreenHeight:      bidRequest.Device.H,
		ScreenWidth:       bidRequest.Device.W,
		UserAgent:         bidRequest.Device.Ua,
		WebviewUA:         bidRequest.Device.Ua,
		PPI:               bidRequest.Device.Ppi,
		RequestIp:         bidRequest.Device.Ip,
		IsIp6:             geo_parser.IsIPv6(bidRequest.Device.Ip),
		ConnectionType:    mappingConnectionType(bidRequest.Device.NetworkType),
		OperatorType:      mappingOperatorType(bidRequest.Device.Carrier),
	}

	if len(bidRequest.Device.SystemMemory) > 0 {
		adRequest.Device.SystemTotalMem, _ = strconv.ParseInt(bidRequest.Device.SystemMemory, 10, 64)
		adRequest.Device.SystemTotalMem *= 1024
	}
	if len(bidRequest.Device.SysDiskSize) > 0 {
		adRequest.Device.SystemTotalDisk, _ = strconv.ParseInt(bidRequest.Device.SysDiskSize, 10, 64)
		adRequest.Device.SystemTotalDisk *= 1024
	}

	if bidRequest.Device.Geo != nil {
		adRequest.Device.Lat = bidRequest.Device.Geo.Lat
		adRequest.Device.Lon = bidRequest.Device.Geo.Lon
	}
}

func (y *YeZiChuanMeiTrafficBroker) parseImp(bidRequest *yezichuanmei_proto.BidRequest, adRequest *ad_service.AdRequest) error {
	if len(bidRequest.Imp) <= 0 {
		return err_code.ErrInvalidImpression
	}

	for _, imp := range bidRequest.Imp {
		if imp == nil || imp.AdsCount < 1 {
			continue
		}

		adRequest.ImpressionId = imp.Id
		adRequest.SetMediaSlotKey(imp.TagId)
		adRequest.SetMediaSlotKeyMapping(strconv.FormatInt(int64(adRequest.Device.OsType), 10) + "_" + strconv.FormatInt(int64(imp.SspImpType), 10))
		if imp.SupportCpc {
			adRequest.BidType = entity.BidTypeCpc
			adRequest.BidFloor = uint32(imp.CpcBidFloor)
		} else {
			adRequest.BidType = entity.BidTypeCpm
			adRequest.BidFloor = uint32(imp.CpmBidFloor)
		}
		adRequest.BlockQuickapp = !imp.QuickApp
		adRequest.BlockWechatMiniProgram = !imp.WechatApplet

		adxTemplateMap := make(map[uint64]int)
		key := creative_entity.NewCreativeTemplateKey()
		switch imp.CreativeType {
		case 42:
			key.Title().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(240, 180)
			adRequest.SlotWidth = 240
			adRequest.SlotHeight = 180
		case 45:
			key.Title().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
			adRequest.SlotWidth = 1280
			adRequest.SlotHeight = 720
		case 46:
			key.Title().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280)
			adRequest.SlotWidth = 720
			adRequest.SlotHeight = 1280
		case 96:
			key.Title().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1080, 1920)
			adRequest.SlotWidth = 1080
			adRequest.SlotHeight = 1920
		case 21:
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(640, 320)
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			adRequest.SlotWidth = 640
			adRequest.SlotHeight = 320
		case 36, 121:
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			adRequest.SlotWidth = 1280
			adRequest.SlotHeight = 720
		case 47:
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1080)
			key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1080)
			adRequest.SlotWidth = 720
			adRequest.SlotHeight = 1080
		case 100:
			key.Title().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
			key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			adRequest.SlotWidth = 1280
			adRequest.SlotHeight = 720
		case 114:
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280)
			key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280)
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			adRequest.SlotWidth = 720
			adRequest.SlotHeight = 1280
		case 136:
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1)
			key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
			key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			adRequest.SlotWidth = 1280
			adRequest.SlotHeight = 720
		case 33: // 激励视频
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(1280, 720)
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			adRequest.SlotWidth = 1280
			adRequest.SlotHeight = 720
		case 104: // 激励视频
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(720, 1280)
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			adRequest.SlotWidth = 720
			adRequest.SlotHeight = 1280
		default:
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			switch imp.SspImpType {
			case 1: // banner
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			case 2: // 插屏
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			case 4: // 开屏
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			case 8: // native
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			case 10: // 激励视频
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			case 40: // 信息流
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			default:
				continue
			}
		}

		adRequest.AppendCreativeTemplateKey(key)
		adxTemplateMap[key.Uint64()] = 0
		adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)
		adRequest.AddMediaExtraInt64(creativeType, int64(imp.CreativeType))
		return nil
	}

	return err_code.ErrInvalidImpression
}

func (y *YeZiChuanMeiTrafficBroker) SendResponse(request *ad_service.AdRequest,
	writer ad_service.HttpResponse) error {
	if request.IsDebug {
		y.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("YeZiChuanMei")
	}

	bidResponse, err := y.buildResponse(request)
	if err != nil {
		y.log.WithError(err).Debug("buildResponse err")
		return err
	}
	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()

	buffer.EnsureSize(bidResponse.Size())
	_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		y.log.WithError(err).Errorf("MarshalToSizedBuffer err")
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/octet-stream")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	y.DoTrafficResponseSamplePb(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		y.log.Infof("SendResponse success, response: %s", responseStr)
	}

	return nil
}

func (y *YeZiChuanMeiTrafficBroker) buildResponse(request *ad_service.AdRequest) (*yezichuanmei_proto.BidResponse, error) {
	bidResponse := &yezichuanmei_proto.BidResponse{
		Id:      request.GetRequestId(),
		SeatBid: make([]*yezichuanmei_proto.SeatBid, 0),
	}
	if request.Response.NoCandidate() {
		return bidResponse, nil
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		resBid := &yezichuanmei_proto.Bid{
			Id:           request.GetRequestId(),
			TagId:        request.GetMediaSlotKey(),
			BidPrice:     candidate.GetBidPrice().Price,
			ImpId:        request.ImpressionId,
			BidType:      1,
			ImpTracks:    candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			ClickTracks:  candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
			PlaySTracks:  genericAd.GetVideoStartUrlList(),
			PlayClTracks: genericAd.GetVideoCloseUrlList(),
			CreativeType: int32(request.GetMediaExtraInt64WithDefault(creativeType, 0)),
			LandingUrl:   genericAd.GetLandingUrl(),
			DeeplinkUrl:  genericAd.GetDeepLinkUrl(),
			AdvertiserId: string(creative.GetAdvertiserId()),
		}
		if request.BidType == entity.BidTypeCpc {
			resBid.BidType = 0
		}
		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			resBid.DownloadUrl = genericAd.GetLandingUrl()
			resBid.FileUrl = genericAd.GetLandingUrl()
		}

		if len(genericAd.GetDeepLinkUrl()) > 0 {
			resBid.DeepType = 1
			if request.Device.OsType == entity.OsTypeIOS && strings.HasPrefix(genericAd.GetDeepLinkUrl(), "https://") {
				resBid.DeepType = 2
			}
		}

		if request.GetMediaSlotInfo().SlotType == entity.SlotTypeRewardVideo {
			resBid.RenderUrl = genericAd.GetLandingUrl()
		}

		if creative.GetCreativeId() != 0 {
			resBid.Crid = strconv.FormatInt(int64(creative.GetCreativeId()), 10)
		} else {
			resBid.Crid = creative.GetCreativeKey()
		}

		if genericAd.GetAppInfo() != nil {
			resBid.Bundle = genericAd.GetAppInfo().PackageName
			resBid.AppId = genericAd.GetAppInfo().AppID

			if genericAd.GetAppInfo().WechatExt != nil {
				resBid.AppletId = genericAd.GetAppInfo().WechatExt.ProgramId
				resBid.AppletPath = genericAd.GetAppInfo().WechatExt.ProgramPath
			}
		}

		for _, material := range candidate.GetSelectedMaterialList() {
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				resBid.Title = material.Data
			case entity.MaterialTypeDesc:
				resBid.Description = material.Data
			case entity.MaterialTypeIcon:
				resBid.Logo = &yezichuanmei_proto.Logo{
					Url:  material.Url,
					W:    100,
					H:    100,
					Type: "image/jpg",
					Kb:   material.FileSize,
				}
			case entity.MaterialTypeImage:
				image := &yezichuanmei_proto.Image{
					Url:  material.Url,
					W:    material.Width,
					H:    material.Height,
					Type: "image/jpg",
					Kb:   material.FileSize,
				}
				if resBid.Image == nil {
					resBid.Image = image
				}
				resBid.Images = append(resBid.Images, image)
			case entity.MaterialTypeCoverImage:
				image := &yezichuanmei_proto.Image{
					Url:  material.Url,
					W:    material.Width,
					H:    material.Height,
					Type: "image/jpg",
					Kb:   material.FileSize,
				}
				if resBid.Image == nil {
					resBid.Image = image
				}
				resBid.Images = append(resBid.Images, image)
			case entity.MaterialTypeVideo:
				resBid.Video = &yezichuanmei_proto.Video{
					Url:      material.Url,
					W:        material.Width,
					H:        material.Height,
					Type:     "video/mp4",
					Kb:       material.FileSize,
					Duration: int32(material.Duration),
				}
			default:
			}
		}

		// 视频宽高没有的时候，将封面的尺寸写入视频中
		if resBid.Video != nil && (resBid.Video.H < 1 || resBid.Video.W < 1) && resBid.Image != nil {
			resBid.Video.W, resBid.Video.H = resBid.Image.W, resBid.Image.H
		}

		// 严格尺寸匹配
		if request.SlotHeight > 0 && request.SlotWidth > 0 {
			if resBid.Image != nil &&
				(request.SlotHeight != uint32(resBid.Image.W) || request.SlotHeight != uint32(resBid.Image.H)) {
				return nil, err_code.ErrCreativeNotMatch
			}
			if resBid.Video != nil &&
				(request.SlotHeight != uint32(resBid.Video.W) || request.SlotHeight != uint32(resBid.Video.H)) {
				return nil, err_code.ErrCreativeNotMatch
			}
		}

		bidResponse.SeatBid = append(bidResponse.SeatBid, &yezichuanmei_proto.SeatBid{
			Bid: []*yezichuanmei_proto.Bid{resBid},
		})
		break
	}
	return bidResponse, nil
}

func mappingConnectionType(networkType int32) entity.ConnectionType {
	switch networkType {
	case 1:
		return entity.ConnectionTypeWifi
	case 2:
		return entity.ConnectionType2G
	case 3:
		return entity.ConnectionType3G
	case 4:
		return entity.ConnectionType4G
	case 5:
		return entity.ConnectionType5G
	case 6, 100:
		return entity.ConnectionTypeCellular
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingOperatorType(carrier string) entity.OperatorType {
	switch carrier {
	case "carrier_mobile":
		return entity.OperatorTypeChinaMobile
	case "carrier_unicom":
		return entity.OperatorTypeChinaUnicom
	case "carrier_telecom":
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingDeviceType(deviceType int32) entity.DeviceType {
	switch deviceType {
	case 1:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePad
	default:
		return entity.DeviceTypeUnknown
	}
}

func mappingOsType(osType int32) entity.OsType {
	switch osType {
	case 1:
		return entity.OsTypeIOS
	case 2:
		return entity.OsTypeAndroid
	default:
		return entity.OsTypeUnknown
	}
}
