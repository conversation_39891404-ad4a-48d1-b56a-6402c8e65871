package yuedong_traffic_broker

import (
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"strconv"
)

type (
	YueDongBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId       utils.ID
		WinPriceMacro string
	}
)

func NewYueDongBroker(mediaId utils.ID) *YueDongBroker {
	return &YueDongBroker{
		mediaId:       mediaId,
		WinPriceMacro: "_PRICE_",
	}
}

func (mb *YueDongBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *YueDongBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro
	return mb.ParseAdRequest(request)
}

func (mb *YueDongBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[YueDongBroker]request body empty")
	}

	bidRequest := &BidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[YueDongBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if err = mb.buildRequest(request, bidRequest); err != nil {
		zap.L().Debug("[YueDongBroker]BrokeRequest, parseUser failed")
		return err
	}
	mb.DoTrafficSample(request, body)
	return nil
}
func (mb *YueDongBroker) buildRequest(request *ad_service.AdRequest, bidRequest *BidRequest) error {
	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[YueDongBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	request.SetRequestId(bidRequest.ID)
	if len(bidRequest.ID) < 1 {
		request.SetRequestId(utils.NewUUID())
	}
	request.SetMediaId(mb.mediaId)

	if err := mb.parseImp(bidRequest, request); err != nil {
		zap.L().Debug("[YueDongBroker] BrokeRequest, parseImp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, request); err != nil {
		zap.L().Debug("[YueDongBroker]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseUser(bidRequest, request); err != nil {
		zap.L().Debug("[YueDongBroker]BrokeRequest, parseUser failed")
		return err
	}

	if err := mb.parseApp(bidRequest, request); err != nil {
		zap.L().Debug("[YueDongBroker]BrokeRequest, parseApp failed")
		return err
	}
	return nil
}

func (mb *YueDongBroker) parseImp(mediaBidRequest *BidRequest,
	bidReq *ad_service.AdRequest) error {

	if len(mediaBidRequest.Imp) == 0 {
		zap.L().Debug("[YueDongBroker]parseImp, vendor: , imp nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return errors.New("parseImp, imp nil")
	}
	imp := mediaBidRequest.Imp[0]
	bidReq.ImpressionId = imp.ID
	bidReq.BidFloor = uint32(imp.BidFloor)
	bidReq.UseHttps = true
	if imp.Deeplink == 1 {
		bidReq.SupportDeeplink = true
	}
	bidReq.SetMediaSlotKey(imp.TagID)

	bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
		Width:  int64(imp.Width),
		Height: int64(imp.Height),
	})

	switch imp.Style {
	case 1:
		bidReq.SlotType = entity.SlotTypeOpening
		key := creative_entity.NewCreativeTemplateKey()
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		bidReq.AppendCreativeTemplateKey(key)

		bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
			Width:  int64(1080),
			Height: int64(1920),
		})
	case 2:
		bidReq.SlotType = entity.SlotTypePopup
		key := creative_entity.NewCreativeTemplateKey()
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		bidReq.AppendCreativeTemplateKey(key)
	case 3:
		bidReq.SlotType = entity.SlotTypeBanner
		key := creative_entity.NewCreativeTemplateKey()
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		bidReq.AppendCreativeTemplateKey(key)
	case 4:
		bidReq.SlotType = entity.SlotTypeFeeds
		key := creative_entity.NewCreativeTemplateKey()
		key.Title().AddRequiredCount(1).SetOptional(true)
		key.Desc().AddRequiredCount(1).SetOptional(true)
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		bidReq.AppendCreativeTemplateKey(key)
		bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
			Width:  int64(1280),
			Height: int64(720),
		})
	case 5:
		bidReq.SlotType = entity.SlotTypeRewardVideo
		key := creative_entity.NewCreativeTemplateKey()
		key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		bidReq.AppendCreativeTemplateKey(key)
	case 6:
		bidReq.SlotType = entity.SlotTypeVideoOpening
		key := creative_entity.NewCreativeTemplateKey()
		key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		bidReq.AppendCreativeTemplateKey(key)
		bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
			Width:  int64(1080),
			Height: int64(1920),
		})
	case 7:
		bidReq.SlotType = entity.SlotTypeVideo
		key := creative_entity.NewCreativeTemplateKey()
		key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		bidReq.AppendCreativeTemplateKey(key)
	case 8:

	}
	return nil
}

func (mb *YueDongBroker) parseDevice(mediaBidRequest *BidRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Device == nil {
		zap.L().Error("parseDevice, device nil", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", mediaBidRequest)))))
		return fmt.Errorf("parseDevice failed")
	}

	device := mediaBidRequest.Device
	bidReq.Device.OsType = func() entity.OsType {
		if device.OS == 1 {
			return entity.OsTypeAndroid
		} else {
			return entity.OsTypeIOS
		}
	}()
	bidReq.Device.OsVersion = device.OSV
	bidReq.Device.AndroidIdMd5 = device.Dpidmd5
	bidReq.Device.AndroidIdMd5 = device.Dpid
	bidReq.Device.ImeiMd5 = device.Didmd5
	bidReq.Device.Imei = device.Did
	bidReq.Device.Oaid = device.Oid
	bidReq.Device.OaidMd5 = device.Oidmd5
	bidReq.Device.Idfa = device.Ifa
	bidReq.Device.IdfaMd5 = device.Ifamd5
	bidReq.Device.Idfv = device.Idfv
	bidReq.Device.IdfvMd5 = device.Idfvmd5

	bidReq.Device.UserAgent = device.UA
	if len(device.IP) > 0 {
		bidReq.Device.RequestIp = device.IP
	} else if len(device.IPv6) > 0 {
		bidReq.Device.RequestIp = device.IPv6
		bidReq.Device.IsIp6 = true
	}
	bidReq.Device.ConnectionType = func() entity.ConnectionType {
		switch device.ConnectionType {
		case 1:
			return entity.ConnectionTypeNetEthernet
		case 2:
			return entity.ConnectionTypeWifi
		case 4:
			return entity.ConnectionType2G
		case 5:
			return entity.ConnectionType3G
		case 6:
			return entity.ConnectionType4G
		case 7:
			return entity.ConnectionType5G
		default:
			return entity.ConnectionTypeUnknown
		}
	}()

	bidReq.Device.OperatorType = func() entity.OperatorType {
		switch device.Carrier {
		case 1:
			return entity.OperatorTypeChinaMobile
		case 2:
			return entity.OperatorTypeChinaUnicom
		case 3:
			return entity.OperatorTypeChinaTelecom
		default:
			return entity.OperatorTypeUnknown
		}
	}()
	bidReq.Device.DeviceType = func() entity.DeviceType {
		switch device.DeviceType {
		case 1:
			return entity.DeviceTypeMobile
		case 2:
			return entity.DeviceTypePad
		case 3:
			return entity.DeviceTypePc
		case 4:
			return entity.DeviceTypeOtt
		default:
			return entity.DeviceTypeUnknown
		}
	}()
	bidReq.Device.Brand = device.Make
	bidReq.Device.Model = device.Model
	bidReq.Device.MacMd5 = device.MACMd5
	bidReq.Device.Mac = device.MAC
	bidReq.Device.ScreenWidth = device.W
	bidReq.Device.ScreenHeight = device.H
	bidReq.Device.PPI = device.PPI
	if device.Geo != nil {
		bidReq.Device.Lat = device.Geo.Lat
		bidReq.Device.Lon = device.Geo.Lon
	}
	bidReq.Device.RomVersion = device.ROMVersion
	bidReq.Device.BootMark = device.BootMark
	bidReq.Device.UpdateMark = device.UpdateMark
	bidReq.Device.DeviceStartupTime = device.BootTime
	bidReq.Device.DeviceInitTime = device.BirthTime
	bidReq.Device.Paid = device.Paid14
	bidReq.Device.VercodeHms = device.HMSVer
	bidReq.Device.VercodeAg = device.HWAGVer
	if len(device.CAIDVersion) > 0 {
		bidReq.Device.Caid = string_utils.ConcatString(device.CAIDVersion, "_", device.CAID)
	}
	bidReq.Device.DeviceName = device.DeviceNameMd5
	bidReq.Device.HardwareMachineCode = device.HardwareMachine
	if len(device.PhysicalMemory) > 0 {
		bidReq.Device.SystemTotalMem, _ = strconv.ParseInt(device.PhysicalMemory, 10, 64)
	}
	if len(device.HardDiskSize) > 0 {
		bidReq.Device.SystemTotalDisk, _ = strconv.ParseInt(device.HardDiskSize, 10, 64)
	}
	bidReq.Device.CountryCode = device.Country
	bidReq.Device.Language = device.Language

	if len(device.TimeZone) > 0 {
		tz, _ := strconv.ParseInt(device.TimeZone, 10, 32)
		bidReq.Device.TimeZone = int32(tz)
	}

	if len(device.InstalledApps) > 0 {
		bidReq.App.MediaInstalledAppIds = device.InstalledApps
	}

	return nil

}

func (mb *YueDongBroker) parseApp(mediaBidRequest *BidRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.App == nil {
		zap.L().Debug("parseApp, vendor: , app nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	app := mediaBidRequest.App
	bidReq.App.AppName = app.Name
	bidReq.App.AppBundle = app.Bundle
	bidReq.App.AppVersion = app.Ver
	return nil
}

func (mb *YueDongBroker) parseUser(mediaBidRequest *BidRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.User == nil {
		zap.L().Debug("parseUser, vendor: , user nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	//adxRequest.User.FtxUserId
	switch mediaBidRequest.User.Gender {
	case "M":
		bidReq.UserGender = entity.UserGenderMan
	case "F":
		bidReq.UserGender = entity.UserGenderWoman
	default:
		bidReq.UserGender = entity.UserGenderUnknown
	}
	return nil
}

func (mb *YueDongBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("YueDongBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("YueDongBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}
	bidResponse, err := mb.buildResponse(request)
	if err != nil {
		zap.L().Error("YueDongBroker Error in buildResponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := []byte(bidResponse.String())
	writer.SetHeader("Content-Type", "application/json")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	mb.DoTrafficResponseSample(request, data)
	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[YueDongBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", responseStr)))))
	}

	return nil
}

func (mb *YueDongBroker) buildResponse(request *ad_service.AdRequest) (*BidResponse, error) {
	response := &BidResponse{
		ID:    request.GetRequestId(),
		BidID: request.GetRequestId(),
	}
	for _, candidate := range request.Response.GetAdCandidateList() {
		creative := candidate.GetCreative()
		if creative == nil {
			continue
		}
		bid := &Bid{
			ID:         request.GetRequestId(),
			ImpID:      request.ImpressionId,
			Price:      candidate.GetBidPrice().Price,
			TargetURL:  candidate.GetMacroReplaceDeepLinkUrl(),
			LandingURL: candidate.GetMacroReplaceLandingUrl(),
			Adm:        &Adm{},
			Tracking: &Tracking{
				Nurl:               "",
				ClickTrackers:      candidate.GetMacroReplaceClickMonitorList(),
				ImpTrackers:        candidate.GetMacroReplaceImpressionMonitorList(),
				DplkTrackers:       candidate.GetMacroReplaceDeepLinkMonitorList(),
				DownloadTrackers:   candidate.GetMacroReplaceAppDownloadStartedMonitorList(),
				DownloadedTrackers: candidate.GetMacroReplaceAppDownloadFinishedMonitorList(),
				InstalledTrackers:  candidate.GetMacroReplaceAppInstallStartMonitorList(),
				InstallTrackers:    candidate.GetMacroReplaceAppInstalledMonitorList(),
			},
		}
		bid.AdID = strconv.Itoa(int(creative.GetCreativeId()))
		if creative.GetCreativeId() == 0 {
			bid.AdID = creative.GetCreativeKey()
		}

		switch candidate.GetLandingAction() {
		case entity.LandingTypeDownload:
			bid.Action = 3
			bid.TargetURL = candidate.GetMacroReplaceLandingUrl()
		case entity.LandingTypeDeepLink:
			bid.Action = 2
		default:
			bid.Action = 1
		}

		if candidate.GetAppInfo() != nil {
			bid.AppName = candidate.GetAppInfo().AppName
			bid.BrandName = candidate.GetAppInfo().AppName
			bid.PackageName = candidate.GetAppInfo().PackageName
			bid.AppSize = candidate.GetAppInfo().PackageSize
			bid.AppVersion = candidate.GetAppInfo().AppVersion
			bid.Developer = candidate.GetAppInfo().Develop
			bid.PrivacyURL = candidate.GetAppInfo().Privacy
			bid.PermissionURL = candidate.GetAppInfo().Permission
		}
		VideoDuration := int(0)
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				bid.Adm.Img = []*Image{
					{
						URL:    rsc.Url,
						Width:  rsc.Width,
						Height: rsc.Height,
					},
				}
			case entity.MaterialTypeIcon, entity.MaterialTypeLogo:
				bid.Adm.Icon = rsc.Url
			case entity.MaterialTypeTitle:
				bid.Adm.Title = rsc.Data
			case entity.MaterialTypeDesc:
				bid.Adm.Desc = rsc.Data
			case entity.MaterialTypeVideo:
				bid.Adm.Video = &Video{
					URL:      rsc.Url,
					Width:    rsc.Width,
					Height:   rsc.Height,
					Duration: int32(rsc.Duration),
					Cover:    "",
				}
				VideoDuration = int(rsc.Duration)
			}
		}

		if VideoDuration > 0 {
			if len(bid.Adm.Img) > 0 {
				bid.Adm.Video.Cover = bid.Adm.Img[0].URL
			}
			for _, delayMonitor := range candidate.GetGenericAd().GetDelayMonitorUrlList() {
				event := entity.GetVideoTrackingEvent(delayMonitor.Delay, VideoDuration)
				switch event {
				case entity.KVideoTrackingEventStart:
					bid.Tracking.PlayerStartTrackers = append(bid.Tracking.PlayerStartTrackers, delayMonitor.Url)
				case entity.KVideoTrackingEventFirst:
					bid.Tracking.PlayerFirstQuartileTrackers = append(bid.Tracking.PlayerFirstQuartileTrackers, delayMonitor.Url)
				case entity.KVideoTrackingEventMid:
					bid.Tracking.PlayerMidpointTrackers = append(bid.Tracking.PlayerMidpointTrackers, delayMonitor.Url)
				case entity.KVideoTrackingEventThird:
					bid.Tracking.PlayerThirdQuartileTrackers = append(bid.Tracking.PlayerThirdQuartileTrackers, delayMonitor.Url)
				case entity.KVideoTrackingEventComplete:
					bid.Tracking.PlayerEndTrackers = append(bid.Tracking.PlayerEndTrackers, delayMonitor.Url)
				}
			}
		}
		response.SeatBid = &SeatBid{
			Bid: []*Bid{
				bid,
			},
		}
	}

	return response, nil
}

func (mb *YueDongBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[YueDongBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json")
	writer.WriteWithStatus(204, nil)
	return nil
}
