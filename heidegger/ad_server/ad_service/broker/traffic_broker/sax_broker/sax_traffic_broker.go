package sax_broker

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/sax_broker/sax_entity"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
)

const (
	AdxTemplateKey = "adxTemplate"
)

var (
	emptyAdxTemplateMap map[uint64]string = make(map[uint64]string)
)

type (
	SaxTrafficBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId       utils.ID
		host          string
		useRawMonitor bool

		WinPriceMacro string
	}
)

func NewSaxTrafficBroker(mediaId utils.ID) *SaxTrafficBroker {
	return &SaxTrafficBroker{
		mediaId:       mediaId,
		WinPriceMacro: "${AUCTION_PRICE:CUSTOM}",
	}
}

func (mb *SaxTrafficBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *SaxTrafficBroker) Do(request *ad_service.AdRequest) error {
	return mb.ParseAdRequest(request)
}

func (mb *SaxTrafficBroker) ParseAdRequest(mRequest *ad_service.AdRequest) error {
	zap.L().Debug("[SaxTrafficBroker] ParseAdRequest")

	mRequest.Response.SetResponseBuilder(mb.SendResponse)
	mRequest.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	mRequest.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro

	body := mRequest.RawHttpRequest.GetBodyContent()

	if len(body) == 0 {
		return fmt.Errorf("[SaxTrafficBroker]request body empty")
	}

	bidRequest := &sax_entity.SaxRequest{}
	err := easyjson.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[SaxTrafficBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	zap.L().Debug("SaxTrafficBroker Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", *bidRequest)))))

	if bidRequest.Test == 1 {
		mRequest.IsDebug = true
	}
	mRequest.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 && bidRequest.Ext != nil {
		mRequest.SetRequestId(bidRequest.Ext.Reqid)
	}
	if len(bidRequest.Id) < 1 {
		mRequest.SetRequestId(utils.NewUUID())
	}
	mRequest.SetMediaId(mb.mediaId)

	if bidRequest.User != nil {
		mRequest.UserId = bidRequest.User.Id
	}

	if err := mb.parseApp(bidRequest, mRequest); err != nil {
		zap.L().Debug("[SaxTrafficBroker]BrokeRequest, parseApp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, mRequest); err != nil {
		zap.L().Debug("[SaxTrafficBroker]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseImp(bidRequest, mRequest); err != nil {
		zap.L().Debug("[SaxTrafficBroker] BrokeRequest, parseImp failed")
		return err
	}

	if err := mb.parseSite(bidRequest, mRequest); err != nil {
		zap.L().Debug("[SaxTrafficBroker] BrokeRequest, parseSite failed")
		return err
	}

	mb.DoTrafficSample(mRequest, body)

	return nil

}

func (mb *SaxTrafficBroker) parseSite(mediaBidRequest *sax_entity.SaxRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Site == nil {
		zap.L().Debug("parseSite, vendor: , site nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	site := mediaBidRequest.Site

	bidReq.Url = site.Page

	return nil
}

func (mb *SaxTrafficBroker) parseImp(mediaBidRequest *sax_entity.SaxRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Imp == nil {
		zap.L().Debug("[SaxTrafficBroker]parseImp, vendor: , imp nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return fmt.Errorf("parseImp, imp nil")
	}
	for _, item := range mediaBidRequest.Imp {
		if item == nil {
			continue
		}

		bidReq.ImpressionId = "1"
		bidReq.SetMediaSlotKey(item.Id)
		bidReq.BidFloor = uint32(item.BidFloor)

		// NOTE: 新浪除了Banner位置不支持竖版,其他位置横竖版都支持

		if item.Banner != nil {
			bidReq.SlotType = entity.SlotTypeBanner
			if item.Banner.H > 0 && item.Banner.W > 0 {

				bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
					Width:  item.Banner.W,
					Height: item.Banner.H,
				})
			}
		}

		if item.Video != nil {
			bidReq.SlotType = entity.SlotTypeVideo
			if item.Video.H > 0 && item.Video.W > 0 {

				bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
					Width:  item.Video.W,
					Height: item.Video.H,
				})
				bidReq.VideoMaxDuration = item.Video.MaxDuration
				bidReq.VideoMinDuration = item.Video.MinDuration
			}
			key := creative_entity.NewCreativeTemplateKey()
			key.Video().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			bidReq.AppendCreativeTemplateKey(key)
		}

		if item.Native != nil {
			bidReq.SlotType = entity.SlotTypeFeeds
			adxTemplateMap := make(map[uint64]string)
			if len(item.Native.Request) > 0 {
				nativeRequest := &sax_entity.SaxNativeRequestData{}
				err := easyjson.Unmarshal([]byte(item.Native.Request), nativeRequest)
				if err == nil {
					has66 := false
					for _, templateId := range nativeRequest.Template {
						if templateId == "66" || templateId == "108" {
							has66 = true
							break
						}
					}

					for _, templateId := range nativeRequest.Template {
						key := creative_entity.NewCreativeTemplateKey()
						switch templateId {
						case "64":
							key.Image().SetRequiredCount(3).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
							key.Title().SetRequiredCount(1)
							bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
								Width:  230,
								Height: 154,
							})
						case "68":
							if has66 {
								//使用66模板点击率更高
								continue
							}
							key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
							key.Title().SetRequiredCount(1)
							bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
								Width:  230,
								Height: 154,
							})
						case "66", "108":
							key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
							key.Title().SetRequiredCount(1)
							bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
								Width:  710,
								Height: 354,
							})
						case "107":
							key.Video().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
							key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
							key.Title().SetRequiredCount(1)
							bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
								Width:  710,
								Height: 400,
							})
						default:
							continue

						}
						bidReq.AppendCreativeTemplateKey(key)
						keyId := key.Uint64()
						adxTemplateMap[keyId] = templateId
					}
				}
			}

			bidReq.AddMediaExtraData(AdxTemplateKey, adxTemplateMap)
		}

		bidReq.UseHttps = mb.mappingSecure(item.Secure)
		break

	}

	return nil
}

func (mb *SaxTrafficBroker) parseDevice(mediaBidRequest *sax_entity.SaxRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Device == nil {
		zap.L().Error("parseDevice, device nil", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", mediaBidRequest)))))
		return nil
	}
	device := mediaBidRequest.Device

	bidReq.Device.RequestIp = device.Ip
	bidReq.Device.UserAgent = device.Ua

	if device.Geo != nil {
		bidReq.Device.Lat = float64(device.Geo.Lat)
		bidReq.Device.Lon = float64(device.Geo.Lon)
	}

	bidReq.Device.OsType = mb.mappingOsType(device.Os)
	bidReq.Device.OsVersion = device.Osv
	bidReq.Device.Model = device.Model
	bidReq.Device.Brand = device.Make

	bidReq.Device.DeviceType = mb.mappingDeviceType(device.DeviceType)

	bidReq.Device.OperatorType = mb.mappingCarrier(device.Carrier)
	bidReq.Device.ConnectionType = mb.mappingConnectionType(device.ConnectionType)

	if mediaBidRequest.User == nil || mediaBidRequest.User.Ext == nil {
		return nil
	}

	userExt := mediaBidRequest.User.Ext

	if len(userExt.Oaid) > 0 {
		bidReq.Device.Oaid = userExt.Oaid
	}
	if len(userExt.Did) > 0 {
		bidReq.Device.Imei = userExt.Did
	}

	bidReq.Device.Idfa = userExt.Idfa
	bidReq.Device.Idfv = userExt.Idfv

	if len(userExt.Imei) > 0 {
		bidReq.Device.Imei = userExt.Imei
	}

	bidReq.Device.Caid = userExt.Caid
	bidReq.Device.CaidRaw = userExt.Caid

	return nil
}

func (mb *SaxTrafficBroker) parseApp(mediaBidRequest *sax_entity.SaxRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.App == nil {
		zap.L().Debug("parseApp, vendor: , app nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	app := mediaBidRequest.App

	bidReq.App.AppName = app.Name
	if strings.Contains(app.Name, "sina") {
		bidReq.App.AppName = "新浪新闻"
	}
	bidReq.App.AppBundle = app.Name
	bidReq.App.AppVersion = app.Ver

	return nil
}

func (mb *SaxTrafficBroker) mappingSecure(s int) bool {

	switch s {
	case 0:
		return false
	case 1:
		return true
	case 2:
		return false
	default:
		return false
	}
}

func (mb *SaxTrafficBroker) mappingCarrier(carrier string) entity.OperatorType {
	switch carrier {
	case "46000", "46002", "46007":
		return entity.OperatorTypeChinaMobile
	case "46001", "46006":
		return entity.OperatorTypeChinaUnicom
	case "46003", "46005":
		return entity.OperatorTypeChinaTelecom
	case "46020":
		return entity.OperatorTypeTietong
	}
	return entity.OperatorTypeUnknown
}

func (mb *SaxTrafficBroker) mappingOsType(s string) entity.OsType {
	switch s {
	case "iOS":
		return entity.OsTypeIOS
	case "Android":
		return entity.OsTypeAndroid
	default:
		return entity.OsTypeUnknown
	}
}

func (mb *SaxTrafficBroker) mappingDeviceType(s int32) entity.DeviceType {
	switch s {
	case 4:
		return entity.DeviceTypeMobile
	case 5:
		return entity.DeviceTypePad
	default:
		return entity.DeviceTypeUnknown
	}
}

func (mb *SaxTrafficBroker) mappingConnectionType(s int32) entity.ConnectionType {
	switch s {
	case 2:
		return entity.ConnectionTypeWifi
	case 4:
		return entity.ConnectionType2G
	case 5:
		return entity.ConnectionType3G
	case 6:
		return entity.ConnectionType4G
	case 7:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func (mb *SaxTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("SaxTrafficBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("SaxTrafficBroker")
		zap.L().Info("[SaxTrafficBroker] adCode:%+v").GetErrCodeMap())
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}

	bidResponse := &sax_entity.SaxBidResponse{}

	bidResponse.Id = request.GetRequestId()
	bidResponse.SeatBid = make([]*sax_entity.SaxBidResponseSeatBid, 0)
	bidResponse.SeatBid = append(bidResponse.SeatBid, &sax_entity.SaxBidResponseSeatBid{})
	bidResponse.SeatBid[0].Bid = make([]*sax_entity.SaxResponseSeatBidBid, 0)

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil || len(candidate.GetSelectedMaterialList()) == 0 {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		bid := &sax_entity.SaxResponseSeatBidBid{}
		bid.ImpId = request.GetMediaSlotKey()
		bidPrice := candidate.GetBidPrice()
		bid.Price = float32(bidPrice.Price)
		bid.Adid = strconv.Itoa(int(genericAd.GetAdId()))

		nativeAdm := &sax_entity.SaxNativeAdm{
			Adtype: "02", //写死信息流
		}

		reqAdxTemplateMap := request.GetMediaExtraDataWithDefault(AdxTemplateKey, emptyAdxTemplateMap).(map[uint64]string)
		key1 := candidate.GetActiveCreativeTemplateKey()
		keyInt := key1.Uint64()
		nativeAdm.Templateid = reqAdxTemplateMap[keyInt]

		nativeImages := make([]*sax_entity.Img, 0)

		for _, material := range candidate.GetSelectedMaterialList() {
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				nativeAdm.Title = material.Data
			case entity.MaterialTypeDesc:
				nativeAdm.Summary = material.Data
			case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
				nativeImages = append(nativeImages, &sax_entity.Img{
					U: material.Url,
					W: material.Width,
					H: material.Height,
				})
			case entity.MaterialTypeVideo:
				nativeAdm.VideoInfo = &sax_entity.Video{
					VideoUrl:      material.Url,
					VideoDuration: int32(material.Duration),
				}
			}

		}

		if len(nativeImages) > 1 {
			nativeAdm.Images = nativeImages
		} else if len(nativeImages) > 0 {
			nativeAdm.Img = nativeImages[0]
		}

		nativeAdm.Url = candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen)
		nativeAdm.Pvmonitor = candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
		nativeAdm.Deeplink = candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen)
		nativeAdm.Click = candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen)
		if genericAd.GetAppInfo() != nil {
			nativeAdm.PackageName = genericAd.GetAppInfo().PackageName
			if genericAd.GetLandingAction() == entity.LandingTypeDownload {
				nativeAdm.InteractType = "download"
				nativeAdm.AppDownloadInfo = &sax_entity.AppDownloadInfo{
					DownloadUrl: genericAd.GetLandingUrl(),
					AppName:     genericAd.GetAppInfo().AppName,
					Developer:   genericAd.GetAppInfo().Develop,
					Version:     genericAd.GetAppInfo().AppVersion,
					AppDesc:     genericAd.GetAppInfo().AppDesc,
					AppDescLink: genericAd.GetAppInfo().AppDescURL,
					//AppPermission:     genericAd.GetAppInfo().Permission,
					AppPermissionLink: genericAd.GetAppInfo().Permission,
					AppPrivacy:        genericAd.GetAppInfo().Privacy,
				}
			}
		}

		dpMonitor := candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen)
		if len(genericAd.GetDeepLinkUrl()) > 0 {
			dpMonitor = append(dpMonitor, candidate.ReplaceUrlMacro(genericAd.GetDpSuccess(), traffic, trackingGen))
		}
		if len(dpMonitor) > 0 {
			nativeAdm.Namonitor = &sax_entity.Namonitor{
				Scheme: candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen),
			}
		}

		nativeJsonStr, _ := easyjson.Marshal(nativeAdm)
		bid.Adm = string(nativeJsonStr)

		bidResponse.SeatBid[0].Bid = append(bidResponse.SeatBid[0].Bid, bid)
		break
	}
	zap.L().Debug("SaxTrafficBroker Build Response end. broker response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", bidResponse)))))

	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()

	_, err := easyjson.MarshalToWriter(bidResponse, buffer)
	if err != nil {
		zap.L().Error("SaxTrafficBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	if request.RawHttpRequest.AcceptGzip() && buffer_pool.ShouldGzip(data) {
		gzipBuffer := buffer_pool.GzipEncodeBuffer(data)
		defer gzipBuffer.Release()

		writer.SetHeader("Content-Encoding", "gzip")
		if _, err := writer.WriteWithStatus(200, gzipBuffer.Get()); err != nil {
			return err
		}

		zap.L().Info("[SaxTrafficBroker] SendResponse success, original response:, gzip response:%d", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(len(data))))), len(gzipBuffer.Get()))
	} else {
		if _, err := writer.WriteWithStatus(200, data); err != nil {
			return err
		}
	}

	mb.DoTrafficResponseSample(request, data)
	if request.IsDebug {
		zap.L().Info("[SaxTrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data)))))
	}

	return nil

}

func (mb *SaxTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	writer.WriteWithStatus(204, nil)
	return nil
}
