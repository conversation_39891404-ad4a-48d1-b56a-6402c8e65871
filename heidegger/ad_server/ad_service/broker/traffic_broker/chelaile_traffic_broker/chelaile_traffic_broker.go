package chelaile_traffic_broker

import (
	"errors"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/chelaile_traffic_broker/chelaile_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
)

const (
	adxTemplateKey = "adxTemplate"
)

type CheLaiLeTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        *zap.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewCheLaiLeTrafficBroker(mediaId utils.ID) *CheLaiLeTrafficBroker {
	return &CheLaiLeTrafficBroker{
		log:     zap.L().With(zap.String("broker", "CheLaiLeTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "__AUCTION_PRICE__",
			MediaClickDownXMacro: "__DOWN_X__",
			MediaClickDownYMacro: "__DOWN_Y__",
			MediaClickUpXMacro:   "__UP_X__",
			MediaClickUpYMacro:   "__UP_Y__",
		},
	}
}

func (c *CheLaiLeTrafficBroker) GetMediaId() utils.ID {
	return c.mediaId
}

func (c *CheLaiLeTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(c.SendResponse)
	request.Response.SetFallbackResponseBuilder(c.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = c.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = c.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &chelaile_proto.BidRequest{}
	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		c.log.WithError(err).Error("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		c.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	request.SetMediaId(c.GetMediaId())
	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}

	c.parseApp(bidRequest, request)
	c.parseUser(bidRequest, request)
	c.parseDevice(bidRequest, request)
	if err = c.parseImp(bidRequest, request); err != nil {
		c.log.WithError(err).Debugf("BrokeRequest, parseImp failed")
		return err
	}

	c.DoTrafficSamplePb(request, bidRequest)

	return nil
}

func (c *CheLaiLeTrafficBroker) parseApp(request *chelaile_proto.BidRequest, adRequest *ad_service.AdRequest) {
	adRequest.App = ad_service.AdRequestApp{}
	if len(request.InstalledApp) > 0 {
		adRequest.App.InstalledApp = request.InstalledApp
	}

	if request.App != nil {
		adRequest.App.AppName = request.App.AppName
		adRequest.App.AppVersion = request.App.AppVersion
		adRequest.App.AppBundle = request.App.Bundle
	}
}

func (c *CheLaiLeTrafficBroker) parseUser(request *chelaile_proto.BidRequest, adRequest *ad_service.AdRequest) {
	if request.User != nil {
		adRequest.UserAge = request.User.Age
		adRequest.UserGender = entity.UserGenderType(request.User.Gender)
	}
}

func (c *CheLaiLeTrafficBroker) parseDevice(request *chelaile_proto.BidRequest, adRequest *ad_service.AdRequest) {
	adRequest.Device = ad_service.AdRequestDevice{}
	if request.Device == nil {
		return
	}

	adRequest.Device = ad_service.AdRequestDevice{
		OsType:              mappingOs(request.Device.Os),
		OsVersion:           request.Device.Osv,
		DeviceType:          entity.DeviceTypeMobile,
		IsMobile:            true,
		CaidVersion:         request.Device.CaidVersion,
		Paid:                request.Device.Paid,
		DeviceInitTime:      request.Device.DeviceBirthTime,
		DeviceStartupTime:   request.Device.BootTimeSec,
		DeviceUpgradeTime:   request.Device.OsUpdateTimeSec,
		SystemTotalMem:      request.Device.MemorySize,
		SystemTotalDisk:     request.Device.DiskSize,
		DeviceName:          request.Device.PhoneName,
		HardwareMachineCode: request.Device.Hwv,
		RomVersion:          request.Device.RomVersion,
		BootMark:            request.Device.BootMark,
		UpdateMark:          request.Device.UpdateMark,
		VercodeAg:           request.Device.VerCodeOfAG,
		VercodeHms:          request.Device.VerCodeOfHms,
		Idfa:                request.Device.Idfa,
		IdfaMd5:             request.Device.IdfaMd5,
		Imei:                request.Device.Imei,
		ImeiMd5:             request.Device.ImeiMd5,
		Mac:                 request.Device.Mac,
		MacMd5:              request.Device.MacMd5,
		AndroidId:           request.Device.AndroidId,
		AndroidIdMd5:        md5_utils.GetMd5String(request.Device.AndroidId),
		Oaid:                request.Device.Oaid,
		OaidMd5:             request.Device.OaidMd5,
		Model:               request.Device.Model,
		Brand:               request.Device.Brand,
		Vendor:              request.Device.Make,
		ScreenHeight:        request.Device.ScreenHeight,
		ScreenWidth:         request.Device.ScreenWidth,
		ScreenOrientation:   mappingOrientation(request.Device.Orientation),
		UserAgent:           request.Device.Ua,
		WebviewUA:           request.Device.Ua,
		PPI:                 request.Device.Ppi,
		RequestIp:           request.Device.Ip,
		ConnectionType:      mappingConnectionType(request.Device.ConnectionType),
		OperatorType:        mappingOperatorType(request.Device.Carrier),
		OperatorName:        mappingOperatorType(request.Device.Carrier).String(),
	}

	if len(request.Device.IpV6) > 0 {
		adRequest.Device.IsIp6 = true
		adRequest.Device.RequestIp = request.Device.IpV6
	}

	if len(request.Device.Caid) > 0 {
		adRequest.Device.CaidRaw = request.Device.Caid
		adRequest.Device.CaidVersion = md5_utils.GetMd5String(request.Device.Caid)
		adRequest.Device.Caid = request.Device.Caid
		if len(request.Device.CaidVersion) > 0 {
			adRequest.Device.Caid = request.Device.CaidVersion + "_" + request.Device.Caid
		}
		adRequest.Device.CaidMd5 = md5_utils.GetMd5String(request.Device.Caid)
		adRequest.Device.Caids = []string{adRequest.Device.Caid}
	}

	if len(request.Device.TimeZone) > 0 {
		timeZone, _ := strconv.ParseInt(request.Device.TimeZone, 10, 64)
		if timeZone > 0 {
			adRequest.Device.TimeZone = int32(timeZone)
		}
	}

	if request.Device.Geo != nil {
		adRequest.Device.GeoStandard = mappingGeoType(request.Device.Geo.Type)
		adRequest.Device.Lat = request.Device.Geo.Lat
		adRequest.Device.Lon = request.Device.Geo.Lon
		adRequest.Device.CountryCode = request.Device.Geo.Country
		adRequest.Device.GpsType = mappingGpsType(request.Device.Geo.Type)
	}
}

func (c *CheLaiLeTrafficBroker) parseImp(request *chelaile_proto.BidRequest, adRequest *ad_service.AdRequest) error {
	if len(request.Imp) == 0 {
		return errors.New("impressions empty")
	}

	for _, imp := range request.Imp {
		if imp == nil {
			continue
		}

		adRequest.ImpressionId = imp.Id
		adRequest.SetMediaSlotKey(imp.TagId)
		adRequest.BidFloor = uint32(imp.Bidfloor)
		adRequest.BidType = entity.BidTypeCpm
		adRequest.UseHttps = imp.Secure == 1

		for _, deal := range imp.Deals {
			adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
				DealId:   deal.Id,
				BidFloor: int64(deal.Bidfloor),
			})
		}

		adxTemplateMap := make(map[uint64]int)
		if imp.AdslotSize != nil {
			adRequest.SlotWidth = uint32(imp.AdslotSize.Width)
			adRequest.SlotHeight = uint32(imp.AdslotSize.Height)

			key := creative_entity.NewCreativeTemplateKey()
			if imp.AdslotSize.TitleLength > 0 {
				key.Title().AddRequiredCount(1).SetRequiredCount(1)
			} else {
				key.Title().AddRequiredCount(1).SetOptional(true)
			}
			if imp.AdslotSize.DescLength > 0 {
				key.Desc().AddRequiredCount(1).SetRequiredCount(1)
			} else {
				key.Desc().AddRequiredCount(1).SetOptional(true)
			}
			var hasImg, hasVideo bool
			for _, mime := range imp.AdslotSize.Mimes {
				switch strings.ToLower(mime) {
				case "mp4", "flv", "avi", "wmv":
					if !hasVideo {
						key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
						adRequest.VideoMinDuration = imp.AdslotSize.MinDuration
						adRequest.VideoMaxDuration = imp.AdslotSize.MaxDuration
						hasVideo = true
					}
				case "jpg", "png", "gif":
					if !hasImg {
						key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
						hasImg = true
					}
				}
			}
			if !hasImg && !hasVideo {
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			}
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = 0
		} else {
			// 默认
			defaultTemplate := creative_entity.NewCreativeTemplateKey()
			defaultTemplate.Title().AddRequiredCount(1).SetOptional(true)
			defaultTemplate.Desc().AddRequiredCount(1).SetOptional(true)
			defaultTemplate.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			defaultTemplate.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			defaultTemplate.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
			adRequest.AppendCreativeTemplateKey(defaultTemplate)
			adxTemplateMap[defaultTemplate.Uint64()] = 0
		}

		adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)

		break
	}

	return nil
}

func (c *CheLaiLeTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		c.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("CheLaiLe")
	}

	if request.Response.NoCandidate() {
		return c.SendFallbackResponse(request, writer)
	}

	bidResponse, err := c.buildResponse(request)
	if err != nil {
		c.log.WithError(err).Error("buildResponse err")
		return err
	}
	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()

	buffer.EnsureSize(bidResponse.Size())
	_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		c.log.Errorf("Error in JSON marshalling:%s", err.Error())
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/x-protobuf")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	c.DoTrafficResponseSamplePb(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		c.log.Infof("SendResponse success, response: %s", responseStr)
	}

	return nil
}

func (c *CheLaiLeTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		c.log.Infof("Build Fallback Response start. bid response: [%v]", request.Response)
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/x-protobuf")
	_, err := writer.WriteWithStatus(204, nil)
	if err != nil {
		return err
	}
	return nil
}

func (c *CheLaiLeTrafficBroker) buildResponse(request *ad_service.AdRequest) (*chelaile_proto.BidResponse, error) {
	bidResponse := &chelaile_proto.BidResponse{
		Id:      request.GetRequestId(),
		Bidid:   request.GetRequestId(),
		Seatbid: make([]*chelaile_proto.BidResponse_SeatBid, 0),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)
		resBid := &chelaile_proto.BidResponse_Bid{
			Impid:   request.ImpressionId,
			AdType:  mappingAdType(request.GetMediaSlotInfo().SlotType),
			AdStyle: 2,
			Price:   float64(candidate.GetBidPrice().Price),
			Items: &chelaile_proto.BidResponse_Item{
				DownloadUrl:         genericAd.GetDownloadUrl(),
				ClickUrl:            genericAd.GetLandingUrl(),
				DplUrl:              genericAd.GetDeepLinkUrl(),
				ExposalUrls:         candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
				ClickMonitorUrls:    candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
				DownloadTrackUrls:   genericAd.GetAppDownloadStartedMonitorList(),
				DownloadedTrackUrls: genericAd.GetAppDownloadFinishedMonitorList(),
				InstalledTrackUrls:  genericAd.GetAppInstalledMonitorList(),
				DpSuccessTrackUrls:  genericAd.GetDeepLinkMonitorList(),
				ActionTrackUrls:     genericAd.GetAppOpenMonitorList(),
			},
		}

		if creative.GetCreativeId() != 0 {
			resBid.Crid = strconv.FormatInt(int64(creative.GetCreativeId()), 10)
		} else {
			resBid.Crid = creative.GetCreativeKey()
		}

		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			resBid.Items.DownloadUrl = genericAd.GetDownloadUrl()
			resBid.Items.MediaStyle = 2
		} else if genericAd.GetLandingAction() == entity.LandingTypeDeepLink {
			resBid.Items.MediaStyle = 1
		} else if genericAd.GetLandingAction() == entity.LandingTypeWeChatProgram {
			resBid.Items.MediaStyle = 4
		}

		if genericAd.GetAppInfo() != nil {
			resBid.Items.DownloadAppInfo = &chelaile_proto.BidResponse_DownloadAppInfo{
				AppName:    genericAd.GetAppInfo().AppName,
				Developer:  genericAd.GetAppInfo().Develop,
				Version:    genericAd.GetAppInfo().AppVersion,
				PacketSize: strconv.Itoa(genericAd.GetAppInfo().PackageSize),
				Privacy:    genericAd.GetAppInfo().Privacy,
				Permission: genericAd.GetAppInfo().Permission,
			}
			resBid.Items.PackageName = genericAd.GetAppInfo().PackageName
			if genericAd.GetAppInfo().WechatExt != nil {
				resBid.Items.MiniProgramId = genericAd.GetAppInfo().WechatExt.ProgramId
				resBid.Items.MiniProgramPath = genericAd.GetAppInfo().WechatExt.ProgramPath
				resBid.Items.MiniProgramType = 1
			}
		}

		var textCount, imgCount, videoCount int64
		for _, material := range candidate.GetSelectedMaterialList() {
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				resBid.Items.Title = material.Data
				textCount++
			case entity.MaterialTypeDesc:
				resBid.Items.Desc = material.Data
			case entity.MaterialTypeIcon:
				resBid.Items.Icon = material.Url
			case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
				if len(material.Url) > 0 {
					imgCount++
					resBid.Items.Imgs = append(resBid.Items.Imgs, material.Url)
				}
			case entity.MaterialTypeVideo:
				if len(material.Url) > 0 {
					videoCount++
					resBid.Items.Video = &chelaile_proto.BidResponse_Video{
						VideoUrl:      material.Url,
						VideoDuration: int32(material.Duration),
					}
					for _, url := range genericAd.GetVideoStartUrlList() {
						if len(url) > 0 {
							resBid.Items.Video.VideoStartUrl = candidate.ReplaceUrlMacro(url, traffic, trackingGen)
							break
						}
					}
					for _, url := range genericAd.GetVideoCloseUrlList() {
						if len(url) > 0 {
							resBid.Items.Video.VideoFinishUrl = candidate.ReplaceUrlMacro(url, traffic, trackingGen)
							break
						}
					}
				}
			default:
			}
		}

		if imgCount > 0 {
			if videoCount > 0 {
				resBid.AdStyle = 5
			} else if imgCount > 2 {
				resBid.AdStyle = 7
			} else if textCount > 0 {
				resBid.AdStyle = 6
			} else {
				resBid.AdStyle = 2
			}
		} else if videoCount > 0 {
			resBid.AdStyle = 3
		} else if textCount > 0 {
			resBid.AdStyle = 1
		}

		bidResponse.Seatbid = append(bidResponse.Seatbid, &chelaile_proto.BidResponse_SeatBid{
			Bid: []*chelaile_proto.BidResponse_Bid{resBid},
		})
		break
	}

	return bidResponse, nil
}

func mappingAdType(slotType entity.SlotType) int32 {
	switch slotType {
	case entity.SlotTypeBanner:
		return 1
	case entity.SlotTypePopup:
		return 2
	case entity.SlotTypeOpening, entity.SlotTypeVideoOpening:
		return 3
	case entity.SlotTypeFeeds:
		return 4
	case entity.SlotTypeVideo:
		return 5
	case entity.SlotTypeRewardVideo:
		return 6
	default:
		return 0
	}
}

func mappingOs(os string) entity.OsType {
	switch strings.ToLower(os) {
	case "ios":
		return entity.OsTypeIOS
	case "android":
		return entity.OsTypeAndroid
	default:
		return entity.OsTypeUnknown
	}
}

func mappingOrientation(orientation int32) entity.ScreenOrientationType {
	switch orientation {
	case 1:
		return entity.ScreenOrientationTypeLandscape
	case 2:
		return entity.ScreenOrientationTypePortrait
	default:
		return entity.ScreenOrientationTypeUnknown
	}
}

func mappingConnectionType(connectionType int32) entity.ConnectionType {
	switch connectionType {
	case 2:
		return entity.ConnectionType2G
	case 3:
		return entity.ConnectionType3G
	case 4:
		return entity.ConnectionType4G
	case 5:
		return entity.ConnectionType5G
	case 100:
		return entity.ConnectionTypeWifi
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingOperatorType(carrier int32) entity.OperatorType {
	switch carrier {
	case 1:
		return entity.OperatorTypeChinaMobile
	case 2:
		return entity.OperatorTypeChinaUnicom
	case 3:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingGeoType(t int32) int {
	switch t {
	case 1:
		return 2
	case 2, 4:
		return 1
	case 3:
		return 0
	default:
		return 2
	}
}

func mappingGpsType(t int32) entity.GpsType {
	switch t {
	case 1:
		return entity.GpsTypeWSG84
	case 2, 4:
		return entity.GpsTypeGCJ02
	case 3:
		return entity.GpsTypeBd09
	default:
		return entity.GpsTypeUnknown
	}
}
