package oppo_traffic_broker

import (
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/oppo_traffic_broker/oppo_traffic_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"strconv"
	"strings"
)

const oppoImp = "oppo_imp"

var (
	oppoTemplate1001 = 1001 // 横板大图
	oppoTemplate1050 = 1050 // 竖版大图
	oppoTemplate1020 = 1020 // 横板小图
	oppoTemplate1030 = 1030 // 组图三张
	oppoTemplate1060 = 1060 // 横板视频
	oppoTemplate1063 = 1063 // 竖版视频
	oppoTemplate1005 = 1005 // 横板开屏
	oppoTemplate1034 = 1034 // 竖版开屏
	oppoTemplate1071 = 1071 // 图标
	oppoTemplate1004 = 1004 // 通知栏
	oppoTemplate635  = 635  // 应用规格
)

type (
	OPPOBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId       utils.ID
		WinPriceMacro string
	}
)

func NewOPPOBroker(mediaId utils.ID) *OPPOBroker {
	return &OPPOBroker{
		mediaId:       mediaId,
		WinPriceMacro: "${AUCTION_PRICE}",
	}
}

func (mb *OPPOBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *OPPOBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro
	return mb.ParseAdRequest(request)
}

func (mb *OPPOBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[OPPOBroker]request body empty")
	}

	bidRequest := &oppo_traffic_entity.OPPOJsonRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[OPPOBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if err = mb.buildRequest(request, bidRequest); err != nil {
		zap.L().Debug("[OPPOBroker]BrokeRequest, parseUser failed")
		return err
	}
	mb.DoTrafficSample(request, body)
	return nil
}
func (mb *OPPOBroker) buildRequest(request *ad_service.AdRequest, bidRequest *oppo_traffic_entity.OPPOJsonRequest) error {
	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[OPPOBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(utils.NewUUID())
	}
	request.SetMediaId(mb.mediaId)

	if err := mb.parseImp(bidRequest, request); err != nil {
		zap.L().Debug("[OPPOBroker] BrokeRequest, parseImp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, request); err != nil {
		zap.L().Debug("[OPPOBroker]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseUser(bidRequest, request); err != nil {
		zap.L().Debug("[OPPOBroker]BrokeRequest, parseUser failed")
		return err
	}

	if err := mb.parseApp(bidRequest, request); err != nil {
		zap.L().Debug("[OPPOBroker]BrokeRequest, parseApp failed")
		return err
	}
	return nil
}

func (mb *OPPOBroker) parseImp(mediaBidRequest *oppo_traffic_entity.OPPOJsonRequest,
	bidReq *ad_service.AdRequest) error {

	if len(mediaBidRequest.Imp) == 0 {
		zap.L().Debug("[OPPOBroker]parseImp, vendor: , imp nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return errors.New("parseImp, imp nil")
	}
	imp := mediaBidRequest.Imp[0]
	bidReq.ImpressionId = imp.Id
	bidReq.BidFloor = uint32(imp.BidFloor)
	bidReq.SetMediaSlotKey(strconv.Itoa(imp.ImpType))
	bidReq.AddMediaExtraData(oppoImp, imp)

	switch imp.ImpType {
	case 1: // Banner，横幅
		bidReq.SlotType = entity.SlotTypeBanner
		if imp.Banner != nil {
			mb.parseImpFormatType(imp.Banner.FormatTypes, bidReq)
		}
	case 8: // Native，原生
		bidReq.SlotType = entity.SlotTypeFeeds
		if imp.Native != nil {
			nativeReq := &oppo_traffic_entity.OPPOJsonRequestNativeRequest{}
			if err := sonic.UnmarshalString(imp.Native.Request, nativeReq); err != nil {
				return errors.New("nativeReq Unmarshal error")
			}
			for _, asset := range nativeReq.Native.Assets {
				if asset.SpecificFeeds != nil {
					mb.parseImpFormatType(asset.SpecificFeeds.FormatTypes, bidReq)
				}
				if asset.CommonNt != nil {
					mb.parseImpFormatType(asset.CommonNt.FormatTypes, bidReq)
				}
			}
		}
	case 16: // Native SpecificFeeds，OPPO 信息流广告
		bidReq.SlotType = entity.SlotTypeFeeds
		bidReq.SetMediaSlotKey(imp.TagId) //站内直接使用广告位id
		if imp.Native != nil {
			nativeReq := &oppo_traffic_entity.OPPOJsonRequestNativeRequest{}
			if err := sonic.UnmarshalString(imp.Native.Request, nativeReq); err != nil {
				return errors.New("nativeReq Unmarshal error")
			}
			for _, asset := range nativeReq.Native.Assets {
				if asset.SpecificFeeds != nil {
					mb.parseImpFormatType(asset.SpecificFeeds.FormatTypes, bidReq)
				}
				if asset.CommonNt != nil {
					mb.parseImpFormatType(asset.CommonNt.FormatTypes, bidReq)
				}
			}
		}

	case 32: // PMP
	case 64: // interstitial，插屏（弹窗）
		bidReq.SlotType = entity.SlotTypePopup
		if imp.Interstitial != nil {
			mb.parseImpFormatType(imp.Interstitial.FormatTypes, bidReq)
		}
	case 128: // Search，搜索直达

	case 256: // Video, 激励视频
		bidReq.SlotType = entity.SlotTypeRewardVideo
		if imp.Video != nil {
			mb.parseImpFormatType(imp.Video.FormatTypes, bidReq)
		}
	case 512: // SplashScreen，开屏
		bidReq.SlotType = entity.SlotTypeOpening
		if imp.Splashscreen != nil {
			mb.parseImpFormatType(imp.Splashscreen.FormatTypes, bidReq)
		}
	}
	//bidReq.SetMediaSlotKey(imp.TagId)

	return nil
}

func (mb *OPPOBroker) parseImpFormatType(formatTypes []int, bidReq *ad_service.AdRequest) {
	for _, v := range formatTypes {
		bidReq.AdxTemplateId = append(bidReq.AdxTemplateId, strconv.FormatInt(int64(v), 10))
		bidReq.AddMediaExtraInt64(strconv.Itoa(v), int64(v))
		switch v {
		case oppoTemplate1001: // 横板大图
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(1280),
				Height: int64(720),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
		case oppoTemplate1050: // 竖版大图
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(1080),
				Height: int64(1920),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			bidReq.AppendCreativeTemplateKey(key)
		case oppoTemplate1020: // 横板小图
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(320),
				Height: int64(120),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
		case oppoTemplate1030: // 组图三张
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(320),
				Height: int64(120),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(3).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)

		case oppoTemplate1060: // 横板视频
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(1280),
				Height: int64(720),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
		case oppoTemplate1063: // 竖版视频
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(1080),
				Height: int64(1920),
			})
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(720),
				Height: int64(1080),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			bidReq.AppendCreativeTemplateKey(key)
		case oppoTemplate1005: // 横板开屏
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(1920),
				Height: int64(1080),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
		case oppoTemplate1034: // 竖版开屏
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(1080),
				Height: int64(1920),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			bidReq.AppendCreativeTemplateKey(key)
		case oppoTemplate1071: // 图标
			//bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
			//	Width:  int64(512),
			//	Height: int64(512),
			//})
			//key := creative_entity.NewCreativeTemplateKey()
			//key.Title().AddRequiredCount(1).SetOptional(true)
			//key.Desc().AddRequiredCount(1).SetOptional(true)
			//key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			//bidReq.AppendCreativeTemplateKey(key)
		case oppoTemplate1004: // 通知栏
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(1080),
				Height: int64(171),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
		case oppoTemplate635: // 应用规格
		}
	}
}

func (mb *OPPOBroker) parseDevice(mediaBidRequest *oppo_traffic_entity.OPPOJsonRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Device == nil {
		zap.L().Error("parseDevice, device nil", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", mediaBidRequest)))))
		return fmt.Errorf("parseDevice failed")
	}

	device := mediaBidRequest.Device
	bidReq.Device.UserAgent = device.Ua
	bidReq.Device.RequestIp = device.Ip
	bidReq.Device.DeviceType = entity.DeviceTypeMobile // 只有mob
	bidReq.Device.Brand = device.Make
	bidReq.Device.Model = device.Model
	bidReq.Device.OsType = entity.OsTypeAndroid // 只有安卓
	bidReq.Device.OsVersion = device.Osv
	bidReq.Device.ScreenWidth = device.W
	bidReq.Device.ScreenHeight = device.H
	bidReq.Device.PPI = device.Ppi
	bidReq.Device.ScreenDensity = device.Pxratio
	bidReq.Device.ConnectionType = func() entity.ConnectionType {
		switch device.Connectiontype {
		case 1:
			return entity.ConnectionTypeNetEthernet
		case 2:
			return entity.ConnectionTypeWifi
		case 3:
			return entity.ConnectionTypeCellular
		case 4:
			return entity.ConnectionType2G
		case 5:
			return entity.ConnectionType3G
		case 6:
			return entity.ConnectionType4G
		case 7:
			return entity.ConnectionType5G
		default:
			return entity.ConnectionTypeUnknown
		}
	}()
	bidReq.Device.ImeiMd5 = device.DidMd5
	bidReq.Device.AndroidIdMd5 = device.AndroidIdMd5
	bidReq.Device.CountryCode = device.Region
	bidReq.Device.Language = device.Lang
	bidReq.Device.VercodeAg = device.AppVersion
	bidReq.Device.OaidMd5 = device.OaIdMd5
	bidReq.Device.Oaid = device.OaId
	bidReq.Device.BootMark = device.BootMark
	bidReq.Device.UpdateMark = device.UpdateMark

	return nil

}

func (mb *OPPOBroker) parseApp(mediaBidRequest *oppo_traffic_entity.OPPOJsonRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.App == nil {
		zap.L().Debug("parseApp, vendor: , app nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	app := mediaBidRequest.App
	bidReq.App.AppName = app.Name
	bidReq.App.AppBundle = app.Bundle
	bidReq.App.AppVersion = app.Ver
	if len(mediaBidRequest.HitStrategys) > 0 {
		adxRule := []string{}
		for _, v := range mediaBidRequest.HitStrategys {
			adxRule = append(adxRule, strconv.FormatInt(int64(v), 10))
		}
		bidReq.App.MediaInstalledAppIds = adxRule
	}
	return nil
}

func (mb *OPPOBroker) parseUser(mediaBidRequest *oppo_traffic_entity.OPPOJsonRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.User == nil {
		zap.L().Debug("parseUser, vendor: , user nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	//adxRequest.User.FtxUserId
	switch mediaBidRequest.User.Gender {
	case "M":
		bidReq.UserGender = entity.UserGenderMan
	case "F":
		bidReq.UserGender = entity.UserGenderWoman
	default:
		bidReq.UserGender = entity.UserGenderUnknown
	}
	return nil
}

func (mb *OPPOBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("OPPOBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("OPPOBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}
	bidResponse, err := mb.buildResponse(request)
	if err != nil {
		zap.L().Error("OPPOBroker Error in buildResponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := []byte(bidResponse.String())
	writer.SetHeader("Content-Type", "application/json")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	mb.DoTrafficResponseSample(request, data)
	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[OPPOBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", responseStr)))))
	}

	return nil
}

func (mb *OPPOBroker) buildResponse(request *ad_service.AdRequest) (*oppo_traffic_entity.OPPOJsonResponse, error) {

	impData, ok := request.GetMediaExtraData(oppoImp)
	if !ok {
		return nil, errors.New("impData nil")
	}
	imp, ok := impData.(*oppo_traffic_entity.OPPOJsonRequestImp)
	if !ok || imp == nil {
		return nil, errors.New("OPPOJsonRequestImp err")
	}
	bid := &oppo_traffic_entity.OPPOJsonResponseBid{}
	for _, candidate := range request.Response.GetAdCandidateList() {
		creative := candidate.GetCreative()
		if creative == nil {
			continue
		}
		bid = &oppo_traffic_entity.OPPOJsonResponseBid{
			Id:    request.GetRequestId(),
			Impid: request.ImpressionId,
			Price: candidate.GetBidPrice().Price,
			//Nurl: candidate.
			Exp:           0,
			ImpTrackers:   candidate.GetMacroReplaceImpressionMonitorList(),
			ClickTrackers: candidate.GetMacroReplaceClickMonitorList(),
			TrackingList: []*oppo_traffic_entity.OPPOTracking{
				{
					TrackingEvent: 10001,
					TrackUrls:     candidate.GetMacroReplaceImpressionMonitorList(),
				}, {
					TrackingEvent: 10002,
					TrackUrls:     candidate.GetMacroReplaceClickMonitorList(),
				},
			},
		}
		bid.Lurl = strings.ReplaceAll(candidate.GetMacroReplaceBidFailed(), "__BIDERRCODE__", "${ERROR_CODE}")

		bid.Adid = strconv.Itoa(int(creative.GetCreativeId()))
		if creative.GetCreativeId() == 0 {
			bid.Adid = creative.GetCreativeKey()
		}
		bid.Crid = bid.Adid

		// ===============
		title := ""
		brand := ""
		desc := ""
		video := &entity.Material{}
		img := &entity.Material{}
		logoUrl := ""
		fileUrls := []string{}
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				fileUrls = append(fileUrls, rsc.Url)
				img = rsc
			case entity.MaterialTypeIcon, entity.MaterialTypeLogo:
				logoUrl = rsc.Url
			case entity.MaterialTypeTitle:
				title = rsc.Data
			case entity.MaterialTypeDesc:
				desc = rsc.Data
			case entity.MaterialTypeVideo:
				fileUrls = append(fileUrls, rsc.Url)
				video = rsc
			default:
				continue
			}
		}
		appPkg := "0"
		WechatAppletPath := ""
		WechatAppletId := ""
		if candidate.GetAppInfo() != nil {
			brand = candidate.GetAppInfo().AppName
			if len(logoUrl) == 0 {
				logoUrl = candidate.GetAppInfo().Icon
			}
			appPkg = candidate.GetAppInfo().PackageName
			if candidate.GetAppInfo().WechatExt != nil {
				WechatAppletPath = candidate.GetAppInfo().WechatExt.ProgramPath
				WechatAppletId = candidate.GetAppInfo().WechatExt.ProgramId
			}
		}

		var formatType, contentType int = 1001, 1
		formatType = mb.getFormatType(request, img, video, logoUrl)
		switch candidate.GetLandingAction() {
		case entity.LandingTypeDownload:
			contentType = 2
			if len(candidate.GetMacroReplaceDeepLinkUrl()) > 0 {
				contentType = 4
			}
		case entity.LandingTypeWeChatProgram:
			contentType = 7
		case entity.LandingTypeDeepLink:
			contentType = 1
		default:
			contentType = 1
		}

		if mb.getUtf8Len(title) > 20 {
			title = mb.substringUtf8(title, 20)
		}

		if mb.getUtf8Len(desc) > 8 {
			title = mb.substringUtf8(title, 8)
		}

		if imp.Native != nil {
			reqNative := &oppo_traffic_entity.OPPOJsonRequestNativeRequest{}
			_ = sonic.UnmarshalString(imp.Native.Request, reqNative)

			OPPONativeMarkupResponseAssets := []*oppo_traffic_entity.OPPONativeMarkupResponseAsset{}
			for _, asset := range reqNative.Native.Assets {
				rowAsset := &oppo_traffic_entity.OPPONativeMarkupResponseAsset{
					Id:       asset.Id,
					Required: asset.Required,
				}
				if asset.Title != nil {
					rowAsset.Title = &oppo_traffic_entity.OPPONativeMarkupResponseAssetTitle{
						Text: title,
					}
				}
				if asset.Data != nil && asset.Data.Type == 2 {
					rowAsset.Data = &oppo_traffic_entity.OPPONativeMarkupResponseAssetData{
						Value: desc,
						Logo:  logoUrl,
					}
				} else if asset.Data != nil && asset.Data.Type == 1 {
					rowAsset.Data = &oppo_traffic_entity.OPPONativeMarkupResponseAssetData{
						Value: brand,
						Logo:  logoUrl,
					}
				}
				if asset.SpecificFeeds != nil && len(fileUrls) > 0 {
					rowAsset.SpecificFeeds = &oppo_traffic_entity.OPPONativeMarkupResponseAssetSpecificFeeds{
						FormatType: formatType,
						ImageUrls:  []string{fileUrls[0]},
					}
					if len(video.Url) > 0 {
						rowAsset.SpecificFeeds.VideoUrl = video.Url
						rowAsset.SpecificFeeds.Duration = int32(video.Duration) * 1000
					}
				}
				if asset.CommonNt != nil && len(fileUrls) > 0 {
					rowAsset.CommonNt = &oppo_traffic_entity.OPPONativeMarkupResponseAssetCommonNt{
						FormatType: formatType,
						FileUrls:   []string{fileUrls[0]},
					}
					if len(video.Url) > 0 {
						rowAsset.CommonNt.VideoUrl = video.Url
						rowAsset.CommonNt.Duration = int32(video.Duration) * 1000
					}
				}
				OPPONativeMarkupResponseAssets = append(OPPONativeMarkupResponseAssets, rowAsset)
			}

			OPPONativeMarkupResponse := &oppo_traffic_entity.OPPONativeMarkupResponse{
				Assets: OPPONativeMarkupResponseAssets,
				Link: &oppo_traffic_entity.OPPONativeMarkupResponseLink{
					ContentType:      contentType,
					Url:              candidate.GetMacroReplaceLandingUrl(),
					DeeplinkUrl:      candidate.GetMacroReplaceDeepLinkUrl(),
					AppPkg:           appPkg,
					DownloadUrl:      candidate.GetMacroReplaceLandingUrl(),
					ClickTrackers:    candidate.GetMacroReplaceClickMonitorList(),
					WechatAppletPath: WechatAppletPath,
					WechatAppletId:   WechatAppletId,
				},
				ImpTrackers: candidate.GetMacroReplaceImpressionMonitorList(),
			}
			adm := &oppo_traffic_entity.OPPONativeMarkupResponseAdm{Native: OPPONativeMarkupResponse}
			bid.ClickTrackers = nil
			bid.ImpTrackers = nil
			bid.TrackingList = nil
			bid.Adm = adm.String()
		} else if imp.Banner != nil {
			OPPOBannerResponse := &oppo_traffic_entity.OPPOBannerResponse{
				FormatType:       formatType,
				ContentType:      contentType,
				Title:            title,
				Desc:             desc,
				Url:              candidate.GetMacroReplaceLandingUrl(),
				DeeplinkUrl:      candidate.GetMacroReplaceDeepLinkUrl(),
				AppPkg:           appPkg,
				DownloadUrl:      candidate.GetMacroReplaceLandingUrl(),
				FileUrls:         fileUrls,
				BdLogo:           logoUrl,
				BdName:           brand,
				BtnName:          "查看详情",
				WechatAppletPath: WechatAppletPath,
				WechatAppletId:   WechatAppletId,
			}
			adm := &oppo_traffic_entity.OPPOBannerResponseAdm{Banner: OPPOBannerResponse}
			bid.Adm = adm.String()
		} else if imp.Interstitial != nil {
			OPPOInterstitialResponse := &oppo_traffic_entity.OPPOInterstitialResponse{
				FormatType:  formatType,
				ContentType: contentType,
				//Title:            title,
				//Desc:             desc,
				Url:              candidate.GetMacroReplaceLandingUrl(),
				DeeplinkUrl:      candidate.GetMacroReplaceDeepLinkUrl(),
				AppPkg:           appPkg,
				FileUrls:         fileUrls,
				BdLogo:           logoUrl,
				BdName:           brand,
				BtnName:          "查看详情",
				WechatAppletPath: WechatAppletPath,
				WechatAppletId:   WechatAppletId,
			}
			if len(video.Url) > 0 {
				OPPOInterstitialResponse.Duration = int32(video.Duration) * 1000
			}
			adm := &oppo_traffic_entity.OPPOInterstitialResponseAdm{Interstitial: OPPOInterstitialResponse}
			bid.Adm = adm.String()
		} else if imp.Video != nil {
			videosUrls := []string{video.Url}
			var duration = int32(video.Duration)
			OPPOVideoResponse := &oppo_traffic_entity.OPPOVideoResponse{
				FormatType:       formatType,
				ContentType:      contentType,
				Title:            title,
				Desc:             desc,
				Url:              candidate.GetMacroReplaceLandingUrl(),
				Duration:         int64(duration * 1000),
				DeeplinkUrl:      candidate.GetMacroReplaceDeepLinkUrl(),
				AppPkg:           appPkg,
				DownloadUrl:      candidate.GetMacroReplaceLandingUrl(),
				FileUrls:         videosUrls,
				BtnName:          "查看详情",
				BdName:           brand,
				BdLogo:           logoUrl,
				WechatAppletPath: WechatAppletPath,
				WechatAppletId:   WechatAppletId,
			}
			if len(img.Url) > 0 {
				OPPOVideoResponse.VideoCoverImg = img.Url
			}
			adm := &oppo_traffic_entity.OPPOVideoResponseAdm{Video: OPPOVideoResponse}
			bid.Adm = adm.String()
		} else if imp.Splashscreen != nil {
			OPPOSplashScreenResponse := &oppo_traffic_entity.OPPOSplashScreenResponse{
				FormatType:  formatType,
				ContentType: contentType,
				//Title:            title,
				//Desc:             desc,
				Url:              candidate.GetMacroReplaceLandingUrl(),
				DeeplinkUrl:      candidate.GetMacroReplaceDeepLinkUrl(),
				AppPkg:           appPkg,
				DownloadUrl:      candidate.GetMacroReplaceLandingUrl(),
				FileUrls:         fileUrls,
				BdLogo:           logoUrl,
				BtnName:          "查看详情",
				BdName:           brand,
				WechatAppletPath: WechatAppletPath,
				WechatAppletId:   WechatAppletId,
			}
			adm := &oppo_traffic_entity.OPPOSplashScreenResponseAdm{Splashscreen: OPPOSplashScreenResponse}
			bid.Adm = adm.String()
		}
	}
	bidResponse := &oppo_traffic_entity.OPPOJsonResponse{
		Id:    request.GetRequestId(),
		BidId: request.GetRequestId(),
		SeatBid: []*oppo_traffic_entity.OPPOJsonResponseSeatBid{
			{
				Seat: "",
				Bid: []*oppo_traffic_entity.OPPOJsonResponseBid{
					bid,
				},
			},
		},
	}

	return bidResponse, nil
}

func (mb *OPPOBroker) getFormatType(request *ad_service.AdRequest, img, video *entity.Material, logoUrl string) int {
	if _, ok := request.GetMediaExtraInt64(strconv.Itoa(oppoTemplate1071)); ok &&
		len(img.Url) == 0 && len(video.Url) == 0 && len(logoUrl) > 0 {
		return oppoTemplate1071
	}
	if request.SlotType == entity.SlotTypeOpening && len(img.Url) > 0 {
		if _, ok := request.GetMediaExtraInt64(strconv.Itoa(oppoTemplate1005)); ok && img.Width > img.Height {
			return oppoTemplate1005
		} else {
			return oppoTemplate1034
		}
	}

	if len(video.Url) > 0 {
		if _, ok := request.GetMediaExtraInt64(strconv.Itoa(oppoTemplate1060)); ok && video.Width > video.Height {
			return oppoTemplate1060
		} else {
			return oppoTemplate1063
		}
	}
	if len(img.Url) > 0 {
		if _, ok := request.GetMediaExtraInt64(strconv.Itoa(oppoTemplate1020)); ok && img.Width == 320 && img.Height == 120 {
			return oppoTemplate1020
		} else if _, ok := request.GetMediaExtraInt64(strconv.Itoa(oppoTemplate1001)); ok && img.Width > img.Height {
			return oppoTemplate1001
		} else {
			return oppoTemplate1050
		}
	}
	return oppoTemplate1001
}
func (mb *OPPOBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[OPPOBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/octet-stream")
	writer.WriteWithStatus(204, nil)
	return nil
}

func (mb *OPPOBroker) getUtf8Len(str string) int {
	return len([]rune(str))
}

func (mb *OPPOBroker) substringUtf8(text string, n int) string {
	runes := []rune(text) // 转换为 Unicode 字符数组
	if n > len(runes) {
		n = len(runes) // 防止超出字符长度
	}
	return string(runes[:n]) // 截取前 n 个字符并转换为字符串
}
