package wenlong_broker

import (
	"bytes"
	"encoding/json"
	"github.com/gogo/protobuf/jsonpb"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/wenlong_broker/wenlong_broker_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"math/rand"
	"strconv"
	"strings"
	"fmt"
)

type WenLongTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	mediaId       utils.ID
	host          string
	useRawMonitor bool
}

func NewWenLongTrafficBroker(mediaId utils.ID) *WenLongTrafficBroker {
	return &WenLongTrafficBroker{
		mediaId: mediaId,
	}
}

func (b *WenLongTrafficBroker) GetMediaId() utils.ID {
	return b.mediaId
}

func (b *WenLongTrafficBroker) Do(request *ad_service.AdRequest) error {
	return b.ParseAdRequest(request)
}

func (b *WenLongTrafficBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	//zap.L().Info("[WenLongTrafficBroker] ParseAdRequest")

	request.Response.SetResponseBuilder(b.SendResponse)
	request.Response.SetFallbackResponseBuilder(b.SendFallbackResponse)

	body := request.RawHttpRequest.GetBodyContent()
	br := &wenlong_broker_proto.Request{}
	if request.GetRawHttpHeader("Content-Type") == "application/json" {
		if err := jsonpb.Unmarshal(bytes.NewBuffer(body), br); err != nil {
			zap.L().Error("[WenLongTrafficBroker] request fail, err", zap.Error(err))
			return err
		}
	} else {
		if err := br.Unmarshal(body); err != nil {
			zap.L().Error("[WenLongTrafficBroker] request fail, err", zap.Error(err))
			return err
		}
	}

	if len(br.Imp) == 0 {
		return err_code.ErrInvalidImpression
	}

	currentImp := br.Imp[0]

	request.SetRequestId(br.Id)
	request.ImpressionId = currentImp.Id
	request.SetMediaId(b.mediaId)
	request.SetMediaSlotKey(currentImp.Tagid)
	request.SlotType = b.getSlotType(currentImp)
	request.UseHttps = currentImp.Secure == 1

	//if rand.Intn(10000) == 0 {
	//	zap.L().Info("[WenLongTrafficBroker] WenLongTrafficBroker slot", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", currentImp.Tagid)))))
	//}

	request.AppendSlotCreativeTemplate(b.buildCreativeTemplate(request.SlotType, currentImp))

	request.Device.UserAgent = br.Device.Ua
	request.Device.OsType = b.getOsType(br.Device.Os)
	request.Device.OsVersion = br.Device.Osv
	request.Device.DeviceType = b.getDeviceType(int32(br.Device.Devicetype))
	request.Device.ConnectionType = b.getConnectionType(br.Device.Connectiontype)
	request.Device.OperatorType = b.getOperatorType(br.Device.Carrier)
	request.Device.PPI = int32(br.Device.Ppi)
	request.Device.ScreenDensity = float32(br.Device.Density)

	requestIp := net_utils.ParseIp(br.Device.Ip)
	request.Device.RequestIp = requestIp.String()
	//request.Device.UserAgent = br.Device.Ua

	request.Device.Idfa = br.Device.Ifa
	request.Device.IdfaMd5 = br.Device.Ifamd5

	request.Device.Imei = br.Device.Did
	request.Device.ImeiMd5 = br.Device.Didmd5

	request.Device.Oaid = br.Device.Oid
	request.Device.OaidMd5 = br.Device.Oidmd5

	request.Device.AndroidId = br.Device.Androidid
	request.Device.AndroidIdMd5 = br.Device.Androididmd5

	request.Device.Mac = br.Device.Mac
	request.Device.MacMd5 = br.Device.Macidmd5

	request.Device.Idfv = br.Device.Idfv

	request.Device.Caid = br.Device.Caid
	request.Device.Aaid = br.Device.Aaid

	request.Device.Brand = br.Device.Brand
	request.Device.Model = br.Device.Model
	request.Device.ScreenWidth = int32(br.Device.Screenwidth)
	request.Device.ScreenHeight = int32(br.Device.Screenheight)
	request.Device.ScreenDensity = float32(br.Device.Density)

	request.Device.BootMark = br.Device.BootMark
	request.Device.UpdateMark = br.Device.UpdateMark

	request.Device.SystemTotalMem = br.Device.MemTotal
	request.Device.SystemTotalDisk = br.Device.DiskTotal
	request.Device.DeviceInitTime = br.Device.BirthTime
	request.Device.DeviceStartupTime = br.Device.StartTimeMsec
	request.Device.DeviceUpgradeTime = br.Device.UpdateTimeNsec
	request.Device.Paid = br.Device.Paid
	request.Device.HardwareMachineCode = br.Device.HardwareModel
	request.Device.DeviceName = br.Device.DeviceNameMd5
	request.Device.VercodeHms = br.Device.Hms
	request.Device.VercodeAg = br.Device.Ag
	request.Device.RomVersion = br.Device.Romversion
	request.Device.SystemCompileTime = br.Device.Syscompilingtime

	b.DoTrafficSamplePb(request, br)

	return nil
}

func (b *WenLongTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		request.Response.Dump("WenLongTrafficBroker")
	}

	response := &wenlong_broker_proto.Response{
		Id:    request.GetRequestId(),
		Bidid: request.ImpressionId,
	}

	var data []byte
	var err error
	if request.GetRawHttpHeader("Content-Type") == "application/json" {
		data, err = json.Marshal(response)
		if err != nil {
			zap.L().Error("[WenLongTrafficBroker] MarshalToSizedBuffer err", zap.Error(err))
			return err
		}
	} else {
		buffer := buffer_pool.NewBuffer()
		defer buffer.Release()

		buffer.EnsureSize(response.Size())
		_, err := response.MarshalToSizedBuffer(buffer.Get())
		if err != nil {
			zap.L().Error("[WenLongTrafficBroker] MarshalToSizedBuffer err", zap.Error(err))
			return err
		}

		data = buffer.Get()
	}

	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	b.DoTrafficResponseSamplePb(request, response)

	return nil
}

func (b *WenLongTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		request.Response.Dump("WenLongTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return b.SendFallbackResponse(request, writer)
	}

	response := &wenlong_broker_proto.Response{
		Id:      request.GetRequestId(),
		Bidid:   request.GetRequestId(),
		Seatbid: &wenlong_broker_proto.Response_SeatBid{},
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		adResponse, err := b.buildAdResponse(request, candidate)
		if err != nil {
			return err_code.ErrBrokerResponseMediaData.Wrap(err)
		}

		response.Seatbid.Bids = append(response.Seatbid.Bids, adResponse)
	}

	var data []byte
	var err error
	if request.GetRawHttpHeader("Content-Type") == "application/json" {
		data, err = json.Marshal(response)
		if err != nil {
			zap.L().Error("[WenLongTrafficBroker] MarshalToSizedBuffer err", zap.Error(err))
			return err
		}

		writer.SetHeader("Content-Type", "application/json")
	} else {
		buffer := buffer_pool.NewBuffer()
		defer buffer.Release()

		buffer.EnsureSize(response.Size())
		_, err := response.MarshalToSizedBuffer(buffer.Get())
		if err != nil {
			zap.L().Error("[WenLongTrafficBroker] MarshalToSizedBuffer err", zap.Error(err))
			return err
		}

		data = buffer.Get()
	}

	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	b.DoTrafficResponseSample(request, data)

	if rand.Intn(10000) == 0 {
		zap.L().Info("[WenLongTrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response.DumpJson())))))
	}

	return nil
}

func (b *WenLongTrafficBroker) buildAdResponse(request *ad_service.AdRequest, candidate *ad_service.AdCandidate) (*wenlong_broker_proto.Response_SeatBid_Bid, error) {
	var err error

	genericAd := candidate.GetGenericAd()
	creative := candidate.GetCreative()

	//zap.L().Info("[WenLongTrafficBroker] ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", candidate.GetDspResponseAd())))))

	traffic := candidate.GetModifiedTrafficData()
	trackingGen := candidate.GetTrackingGen(traffic)

	bidPrice := candidate.GetBidPrice()

	bid := &wenlong_broker_proto.Response_SeatBid_Bid{
		Id:       request.GetRequestId(),
		Impid:    request.ImpressionId,
		Adid:     strconv.Itoa(int(genericAd.GetAdId())),
		Price:    float64(bidPrice.Price),
		Target:   candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
		Deeplink: candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen),
		Events: &wenlong_broker_proto.Response_SeatBid_Bid_Event{
			ImpUrls:       candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			ClickUrls:     candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
			StartPlayUrls: candidate.ReplaceUrlMacroList(genericAd.GetVideoStartUrlList(), traffic, trackingGen),
			ClosePlayUrls: candidate.ReplaceUrlMacroList(genericAd.GetVideoCloseUrlList(), traffic, trackingGen),
		},
		Ext: &wenlong_broker_proto.Response_SeatBid_Bid_Ext{
			DoLand:       1,
			LandStayTime: int32(candidate.GetAd().GetAdMonitorInfo().LandingStayTime),
		},
	}

	if !candidate.GetPacingDecision().ShouldClick() {
		bid.Events.ClickUrls = nil
		bid.Target = "http://www.baidu.com"
	}

	if creative != nil {
		bid.Crid = strconv.Itoa(int(creative.GetCreativeId()))
		bid.Admobject = &wenlong_broker_proto.Response_SeatBid_Bid_AdmObject{}
		bid.Admobject.Native, err = b.buildAdResponseNative(request, candidate, genericAd, creative)
		if err != nil {
			zap.L().Error("[WenLongTrafficBroker] buildAdResponseNative err", zap.Error(err))
			return nil, err
		}
	}
	return bid, nil
}

func (b *WenLongTrafficBroker) buildAdResponseNative(
	request *ad_service.AdRequest,
	candidate *ad_service.AdCandidate,
	genericAd entity.GenericAd,
	creative entity.GenericCreative) (*wenlong_broker_proto.Response_SeatBid_Bid_AdmObject_Native, error) {

	//template := request.GetSlotCreativeTemplate()
	//if template == nil {
	//	return nil, err_code.ErrInvalidCreativeTemplate
	//}

	native := &wenlong_broker_proto.Response_SeatBid_Bid_AdmObject_Native{}

	//creativeMapper := entity.NewCreativeTemplateMapper()
	//defer creativeMapper.Release()
	//
	//creativeMapper.SetCreative(creative)

	//for _, asset := range template.Assets {
	//	material := creativeMapper.MapMaterial(asset)
	//	if material == nil {
	//		if asset.IsRequired {
	//			return nil, err_code.ErrCreativeTemplateNotMatch
	//		} else {
	//			continue
	//		}
	//	}
	//
	//	isRequired := uint32(0)
	//	if asset.IsRequired {
	//		isRequired = 1
	//	}
	//
	//	responseAsset := &wenlong_broker_proto.Response_SeatBid_Bid_AdmObject_Native_Asset{
	//		Id:         uint32(asset.Id),
	//		Isrequired: isRequired,
	//	}
	//
	//	if material.IsImage() {
	//		responseAsset.OneAsset = &wenlong_broker_proto.Response_SeatBid_Bid_AdmObject_Native_Asset_Img_{
	//			Img: &wenlong_broker_proto.Response_SeatBid_Bid_AdmObject_Native_Asset_Img{
	//				Url: material.Url,
	//				W:   uint32(material.Width),
	//				H:   uint32(material.Height),
	//			},
	//		}
	//	}
	//
	//	native.Assets = append(native.Assets, responseAsset)
	//}

	responseAsset := &wenlong_broker_proto.Response_SeatBid_Bid_AdmObject_Native_Asset{
		Id:         uint32(1),
		Isrequired: 1,
	}

	responseAsset.OneAsset = &wenlong_broker_proto.Response_SeatBid_Bid_AdmObject_Native_Asset_Img_{
		Img: &wenlong_broker_proto.Response_SeatBid_Bid_AdmObject_Native_Asset_Img{
			Url: "https://creative-1321228588.cos.ap-beijing.myqcloud.com/d3197cc7-63ca-4ea6-8a48-37487ae88c83.png",
			W:   uint32(1080),
			H:   uint32(1920),
		},
	}
	native.Assets = append(native.Assets, responseAsset)

	return native, nil
}

func (b *WenLongTrafficBroker) getSlotType(imp *wenlong_broker_proto.Request_Imp) entity.SlotType {
	if imp.GetNative() != nil {
		if imp.GetNative().Layout == 501 {
			return entity.SlotTypeOpening
		} else if imp.GetNative().Layout == 502 {
			return entity.SlotTypeBanner
		} else if imp.GetNative().Layout == 3 {
			return entity.SlotTypeFeeds
		}
	}

	return entity.SlotTypeUnknown
}

func (b *WenLongTrafficBroker) buildCreativeTemplate(
	slotType entity.SlotType,
	imp *wenlong_broker_proto.Request_Imp) *creative_entity.CreativeTemplate {
	template := creative_entity.AcquireCreativeTemplateWithPool()
	template.SetId(0)
	template.SetName("wenlong_slot")
	template.SetSlotType(slotType)
	template.SetIsMediaSpecific(true)

	//zap.L().Info("imp", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", imp)))))
	if imp.GetNative() != nil {
		//zap.L().Info("native", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", imp.GetNative())))).String())
		for _, asset := range imp.GetNative().Assets {
			templateAsset := creative_entity.AcquireCreativeTemplateAssetWithPool()
			image := asset.GetImg()
			video := asset.GetVideo()
			title := asset.GetTitle()
			if image != nil {
				if image.Type == 1 {
					templateAsset.SetMaterialType(entity.MaterialTypeIcon)
				} else if image.Type == 2 {
					templateAsset.SetMaterialType(entity.MaterialTypeLogo)
				} else if image.Type == 3 {
					templateAsset.SetMaterialType(entity.MaterialTypeImage)
				}

				templateAsset.AddSizeRequirement(creative_entity.RequirementSizeWithMinSizeRatio(int32(image.Wmin), int32(image.Hmin)))
			}

			if title != nil {
				templateAsset.SetMaterialType(entity.MaterialTypeTitle)
				templateAsset.AddTextRequirement(creative_entity.RequirementTextWithMinMaxLen(0, int32(title.Len)))
			}

			if video != nil {
				templateAsset.SetMaterialType(entity.MaterialTypeVideo)
				templateAsset.AddSizeRequirement(creative_entity.RequirementSizeWithMinSizeRatio(int32(image.Wmin), int32(image.Hmin)))
			}

			template.AddAsset(templateAsset)
		}
	}

	return template
}

func (b *WenLongTrafficBroker) getOsType(os string) entity.OsType {
	switch strings.ToLower(os) {
	case "android":
		return entity.OsTypeAndroid
	case "ios":
		return entity.OsTypeIOS
	default:
		return entity.OsTypeUnknown
	}
}

func (b *WenLongTrafficBroker) getDeviceType(deviceType int32) entity.DeviceType {
	switch deviceType {
	case 1:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePc
	case 3:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeMobile
	}
}

func (b *WenLongTrafficBroker) getConnectionType(connectionType wenlong_broker_proto.Request_Device_ConnectionType) entity.ConnectionType {
	switch connectionType {
	case wenlong_broker_proto.Request_Device_Ethernet:
		return entity.ConnectionTypeNetEthernet
	case wenlong_broker_proto.Request_Device_WiFi:
		return entity.ConnectionTypeWifi
	case wenlong_broker_proto.Request_Device_CellularNetworkUnknown:
		return entity.ConnectionTypeCellular
	case wenlong_broker_proto.Request_Device_G2:
		return entity.ConnectionType2G
	case wenlong_broker_proto.Request_Device_G3:
		return entity.ConnectionType3G
	case wenlong_broker_proto.Request_Device_G4:
		return entity.ConnectionType4G
	case wenlong_broker_proto.Request_Device_G5:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func (b *WenLongTrafficBroker) getOperatorType(operatorType string) entity.OperatorType {
	switch operatorType {
	case "mobile":
		return entity.OperatorTypeChinaMobile
	case "telecom":
		return entity.OperatorTypeChinaTelecom
	case "unicom":
		return entity.OperatorTypeChinaUnicom
	case "tietong":
		return entity.OperatorTypeTietong
	default:
		return entity.OperatorTypeUnknown
	}
}
