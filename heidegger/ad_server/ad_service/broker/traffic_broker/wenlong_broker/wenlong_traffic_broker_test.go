package wenlong_broker

import (
	"bytes"
	"github.com/gogo/protobuf/jsonpb"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/wenlong_broker/wenlong_broker_proto"
	"testing"
	"fmt"
)

func TestWenLongTrafficBroker_GenerateRequest(t *testing.T) {
	jsonData := `
{
	"app": {
		"appstoreversion": "",
		"bundle": "",
		"name": "",
		"version": ""
	},
	"device": {
		"androidid": "524af2b84b9ffc48",
		"boot_mark": "ac7b01ec-e297-434d-bb8f-54dbd59d784b",
		"brand": "vivo",
		"carrier": "mobile",
		"connectiontype": 2,
		"devicetype": 4,
		"did": "",
		"didmd5": "",
		"geo": {
			"lat": 0,
			"lon": 0
		},
		"hms": "",
		"ip": "**************",
		"macidmd5": "03B97AB098BA51E5405ED9A31959C6A0",
		"make": "vivo",
		"model": "V1831A",
		"oid": "1e563dda9394d6e6f06942a3634b8112f29545977f803de36469e20b424e0ded",
		"oidmd5": "D372EAC56A74F949ECB515E84EDCE737",
		"orientation": 2,
		"os": "android",
		"osv": "10",
		"screenheight": 2214,
		"screenwidth": 1080,
		"start_time_msec": "",
		"ua": "Mozilla/5.0 (Linux; Android 10; V1831A Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36",
		"update_mark": "1561910598.718968818",
		"update_time_nsec": ""
	},
	"id": "a943eabf295f4995a56deeca3f3dcd3b",
	"imp": [
		{
			"bidfloor": 200,
			"id": "835ad68a73b746a28a5c6a6725fc5a64",
			"isdeeplink": true,
			"isdownload": true,
			"native": {
				"assets": [
					{
						"id": 1,
						"img": {
							"hmin": 720,
							"type": 3,
							"wmin": 1280
						},
						"isrequired": 1
					},
					{
						"id": 2,
						"isrequired": 1,
						"title": {
							"len": 50
						}
					},
					{
						"data": {
							"len": 100
						},
						"id": 3,
						"isrequired": 1
					}
				],
				"layout": 3
			},
			"tagid": "TagID"
		}
	],
	"version": "1.2"
}
`
	request := wenlong_broker_proto.Request{}
	if err := jsonpb.Unmarshal(bytes.NewBuffer([]byte(jsonData)), &request); err != nil {
		t.Fatal(err)
	}

	zap.L().Info(fmt.Sprintf(request.String()))
}
