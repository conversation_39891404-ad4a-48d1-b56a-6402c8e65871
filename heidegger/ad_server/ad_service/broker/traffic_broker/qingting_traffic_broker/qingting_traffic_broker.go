package qingting_traffic_broker

import (
	"errors"
	"fmt"
	"strconv"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	qingting_broker_entity "gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/qingting_traffic_broker/qingting_traffic_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
)

const (
	adxTemplateKey = "adxTemplate"
)

type QingTingTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        *zap.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewQingTingTrafficBroker(mediaId utils.ID) *QingTingTrafficBroker {
	return &QingTingTrafficBroker{
		log:     zap.L().With(zap.String("broker", "QingTingTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro: "__WIN_PRICE__",
		},
	}
}

func (a *QingTingTrafficBroker) GetMediaId() utils.ID {
	return a.mediaId
}

func (a *QingTingTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(a.SendResponse)
	request.Response.SetFallbackResponseBuilder(a.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = a.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = a.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &qingting_broker_entity.BidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		a.log.WithError(err).Error("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		a.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	request.SetMediaId(a.GetMediaId())
	request.SetRequestId(bidRequest.RequestID)
	if len(bidRequest.RequestID) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}
	a.parseDevice(bidRequest, request)
	if err = a.parseImp(bidRequest, request); err != nil {
		a.log.WithError(err).Error("BrokeRequest, parseImp failed")
		return err
	}

	a.DoTrafficSample(request, body)

	return nil
}

func (a *QingTingTrafficBroker) parseDevice(request *qingting_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.Device == nil {
		return
	}

	if request.User != nil {
		adRequest.UserId = request.User.ID
	}

	adRequest.Device = ad_service.AdRequestDevice{
		RequestIp:         request.IP,
		UserAgent:         request.Ua,
		WebviewUA:         request.Ua,
		OperatorType:      mappingOperatorType(request.Device.Carrier),
		Language:          request.Device.Language,
		Model:             request.Device.Model,
		Brand:             request.Device.Make,
		Vendor:            request.Device.Make,
		OsType:            mappingOsType(request.Device.Os),
		OsVersion:         request.Device.OsVersion,
		ScreenWidth:       int32(request.Device.ScreenWidth),
		ScreenHeight:      int32(request.Device.ScreenHeight),
		DeviceType:        mappingDeviceType(request.Device.DeviceType),
		ConnectionType:    mappingConnectionType(request.Device.ConnectionType),
		Idfa:              request.Device.Idfa,
		IdfaMd5:           request.Device.IdfaMd5,
		Imei:              request.Device.Imei,
		ImeiMd5:           request.Device.ImeiMd5,
		Oaid:              request.Device.Oaid,
		AndroidId:         request.Device.AndroidID,
		Mac:               request.Device.Mac,
		MacMd5:            request.Device.MacMd5,
		OpenUdid:          request.Device.Openudid,
		ScreenOrientation: mappingScreenOrientation(request.Device.Orientation),
		BootMark:          request.Device.BootMark,
		UpdateMark:        request.Device.UpdateMark,
		DeviceInitTime:    request.Device.BirthTime,
		DeviceUpgradeTime: request.Device.UpdateTime,
		Paid:              request.Device.Paid,
	}
	if request.App != nil {
		adRequest.App.AppName = request.App.Name
		adRequest.App.AppVersion = request.App.Version
		adRequest.App.AppBundle = request.App.PackageName
		if request.App.Geo != nil {
			adRequest.Device.Lat = request.App.Geo.Latitude
			adRequest.Device.Lon = request.App.Geo.Longitude
		}
	}
	if adRequest.Device.DeviceType == entity.DeviceTypeMobile || adRequest.Device.DeviceType == entity.DeviceTypePad {
		adRequest.Device.IsMobile = true
	}

	if len(request.Device.MemoryTotal) > 0 {
		adRequest.Device.SystemTotalMem, _ = strconv.ParseInt(request.Device.MemoryTotal, 10, 64)
	}
	if len(request.Device.DiskSpace) > 0 {
		adRequest.Device.SystemTotalDisk, _ = strconv.ParseInt(request.Device.DiskSpace, 10, 64)
	}

}

func (a *QingTingTrafficBroker) parseImp(request *qingting_broker_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	if len(request.Impressions) == 0 {
		return errors.New("impressions empty")
	}

	for _, imp := range request.Impressions {
		if imp == nil {
			continue
		}
		for _, size := range imp.AcceptedSize {
			if size.Width > 0 && size.Height > 0 {
				adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
					Width:  int64(size.Width),
					Height: int64(size.Height),
				})
			}
		}

		adRequest.ImpressionId = request.RequestID
		adRequest.SetMediaSlotKey(imp.Adpid)
		// format: os_tagId
		adRequest.SetMediaSlotKeyMapping(fmt.Sprintf("%d_%s", adRequest.Device.OsType, imp.Adpid))
		adRequest.BidFloor = uint32(imp.MinCpm)
		adRequest.BidType = entity.BidTypeCpm

		if imp.DealIds != nil && len(imp.DealIds.DealID) > 0 {
			adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
				DealId: imp.DealIds.DealID,
			})
		}
		adxTemplateMap := make(map[uint64]int)
		for _, adpstyle := range imp.AdpStyle {
			switch adpstyle {
			case 1:
				key := creative_entity.NewCreativeTemplateKey()
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				adRequest.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = adpstyle
			case 2, 3, 4:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				adRequest.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = adpstyle
			case 5:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				adRequest.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = adpstyle
			case 7:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				adRequest.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = adpstyle
			}
		}
		adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)
		break
	}

	return nil
}

func (a *QingTingTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("QingTingTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return a.SendFallbackResponse(request, writer)
	}

	bidResponse, err := a.buildResponse(request)
	if err != nil {
		a.log.WithError(err).Error("buildResponse err")
		return err
	}

	err = a.BuildHttpSonicJsonResponse(request, writer, bidResponse)
	if err != nil {
		return err
	}
	writer.SetHeader("QTRTB-version", "1.0")
	a.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		a.log.Infof("SendResponse success, response: %s", responseStr)
	}

	return nil
}

func (a *QingTingTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Infof("Build Fallback Response start. bid response: [%v]", request.Response)
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	_, _ = writer.WriteWithStatus(204, nil)
	return nil
}

func (a *QingTingTrafficBroker) buildResponse(request *ad_service.AdRequest) (*qingting_broker_entity.BidResponse, error) {
	bidResponse := &qingting_broker_entity.BidResponse{
		RequestID:  request.GetRequestId(),
		StatusCode: 200,
		SeatBids:   make([]*qingting_broker_entity.SeatBids, 0),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		interactiontype := 0
		switch genericAd.GetLandingAction() {
		case entity.LandingTypeInWebView:
			interactiontype = 1
		case entity.LandingTypeDownload:
			interactiontype = 2
		case entity.LandingTypeDeepLink:
			interactiontype = 3
		case entity.LandingTypeWeChatProgram:
			interactiontype = 5
		}
		if len(genericAd.GetDeepLinkUrl()) > 0 && interactiontype == 2 {
			interactiontype = 4
		}
		if len(genericAd.GetDeepLinkUrl()) > 0 && interactiontype == 5 {
			interactiontype = 6
		}

		resBid := &qingting_broker_entity.SeatBids{
			Adpid:        request.GetMediaSlotKey(),
			Price:        uint64(candidate.GetBidPrice().Price),
			CreativeID:   creative.GetCreativeKey(),
			Chargingtype: 1,
			Creative: qingting_broker_entity.Creative{
				InteractionType: interactiontype,
				Image:           qingting_broker_entity.Image{},
				Video:           qingting_broker_entity.Video{},
				TargetURL:       genericAd.GetLandingUrl(),
				DownloadURL:     genericAd.GetDownloadUrl(),
				DeepLink:        genericAd.GetDeepLinkUrl(),
				ShowURL:         candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
				ClickURL:        candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
				AdpStyle:        0,
			},
		}

		reqAdxTemplateMap := request.GetMediaExtraDataWithDefault(adxTemplateKey, make(map[uint64]int)).(map[uint64]int)
		key1 := candidate.GetActiveCreativeTemplateKey()
		keyInt := key1.Uint64()
		resBid.Creative.AdpStyle = reqAdxTemplateMap[keyInt]

		if len(genericAd.GetDeepLinkFailedMonitorList()) > 0 {
			resBid.Creative.DeeplinkAppInvokeFailedTrackers = genericAd.GetDeepLinkFailedMonitorList()
		}
		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			resBid.Creative.DeeplinkAppInvokeSuccessTracker = genericAd.GetDeepLinkMonitorList()
		}
		if len(genericAd.GetDpSuccess()) > 0 {
			resBid.Creative.DeeplinkAppInvokeSuccessTracker = append(resBid.Creative.DeeplinkAppInvokeSuccessTracker, genericAd.GetDpSuccess())
		}

		if candidate.GetIndexDeal() != nil {
			resBid.DealID = candidate.GetIndexDeal().DealId
		}

		isvideo := false
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				resBid.Creative.Image.URL = rsc.Url
				resBid.Creative.Image.Width = uint32(rsc.Width)
				resBid.Creative.Image.Height = uint32(rsc.Height)
			case entity.MaterialTypeIcon, entity.MaterialTypeLogo:
				resBid.Creative.Icon = rsc.Url
			case entity.MaterialTypeTitle:
				resBid.Creative.Title = rsc.Data
			case entity.MaterialTypeDesc:
				resBid.Creative.Description = rsc.Data
			case entity.MaterialTypeVideo:
				isvideo = true
				resBid.Creative.Video.VideoURL = rsc.Url
				resBid.Creative.Video.VideoDuration = int(rsc.Duration)
				resBid.Creative.Video.Size = int(rsc.FileSize)
			case entity.MaterialTypeCoverImage:
				resBid.Creative.Video.VideoURL = rsc.Url
				resBid.Creative.Video.CoverWidth = int(rsc.Width)
				resBid.Creative.Video.CoverHeight = int(rsc.Height)
			}
		}
		if isvideo {
			resBid.Creative.MaterialType = 2
		} else {
			resBid.Creative.MaterialType = 1
		}

		if genericAd.GetAppInfo() != nil {
			resBid.Creative.AppName = genericAd.GetAppInfo().AppName
			resBid.Creative.PackageName = genericAd.GetAppInfo().PackageName
			if genericAd.GetAppInfo().WechatExt != nil {
				resBid.Creative.WxUsername = genericAd.GetAppInfo().WechatExt.ProgramId
				resBid.Creative.WxPathURL = genericAd.GetAppInfo().WechatExt.ProgramPath
			}
		}
		bidResponse.SeatBids = []*qingting_broker_entity.SeatBids{resBid}
		break
	}

	return bidResponse, nil
}
func mappingSlotType(s entity.SlotType) int {
	switch s {
	//case entity.SlotTypeBanner:
	//	return 1
	case entity.SlotTypeFeeds:
		return 3
	case entity.SlotTypeVideoOpening:
		return 1
	case entity.SlotTypeVideo, entity.SlotTypeVideoPause:
		return 7
	case entity.SlotTypePopup:
		return 5
	default:
		return 2
	}
}
func (mb *QingTingTrafficBroker) mappingSlotType(s int32) entity.SlotType {
	switch s {
	case 9:
		return entity.SlotTypeVideoPause
	case 2:
		return entity.SlotTypeBanner
	case 1:
		return entity.SlotTypeOpening
	case 8:
		return entity.SlotTypeRewardVideo
	case 5:
		return entity.SlotTypeFeeds
	case 3:
		return entity.SlotTypePopup

	default:
		return entity.SlotTypeUnknown
	}
}

func mappingOsType(os int) entity.OsType {
	switch os {
	case 1:
		return entity.OsTypeAndroid
	case 2:
		return entity.OsTypeIOS
	case 3:
		return entity.OsTypeWindows
	default:
		return entity.OsTypeAndroid
	}
}

func mappingScreenOrientation(orientation int) entity.ScreenOrientationType {
	switch orientation {
	case 1:
		return entity.ScreenOrientationTypePortrait
	case 2:
		return entity.ScreenOrientationTypeLandscape
	default:
		return entity.ScreenOrientationTypePortrait
	}
}

func mappingOperatorType(carrier string) entity.OperatorType {
	switch carrier {
	case "1":
		return entity.OperatorTypeChinaMobile
	case "2":
		return entity.OperatorTypeChinaUnicom
	case "3":
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType string) entity.ConnectionType {
	switch connectionType {
	case "1":
		return entity.ConnectionTypeWifi
	case "2":
		return entity.ConnectionType2G
	case "3":
		return entity.ConnectionType3G
	case "4":
		return entity.ConnectionType4G
	case "5":
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingDeviceType(deviceType string) entity.DeviceType {
	switch deviceType {
	case "1":
		return entity.DeviceTypeMobile
	case "2":
		return entity.DeviceTypePad
	case "3":
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeMobile
	}
}
