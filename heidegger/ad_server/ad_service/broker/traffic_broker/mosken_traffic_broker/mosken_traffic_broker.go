package mosken_traffic_broker

import (
	"errors"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	mosken_broker_entity "gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/mosken_traffic_broker/mosken_traffic_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
)

type MoskenTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        *zap.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewMoskenTrafficBroker(mediaId utils.ID) *MoskenTrafficBroker {
	return &MoskenTrafficBroker{
		log:     zap.L().With(zap.String("broker", "MoskenTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "__WIN_PRICE__",
			MediaClickDownXMacro: "__DOWN_X__",
			MediaClickDownYMacro: "__DOWN_Y__",
			MediaClickUpXMacro:   "__UP_X__",
			MediaClickUpYMacro:   "__UP_Y__",
		},
	}
}

func (a *MoskenTrafficBroker) GetMediaId() utils.ID {
	return a.mediaId
}

func (a *MoskenTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(a.SendResponse)
	request.Response.SetFallbackResponseBuilder(a.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = a.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = a.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &mosken_broker_entity.BidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		a.log.WithError(err).Error("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		a.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	request.SetMediaId(a.GetMediaId())
	request.SetRequestId(bidRequest.ID)
	if len(bidRequest.ID) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}
	a.parseDevice(bidRequest, request)
	if err = a.parseImp(bidRequest, request); err != nil {
		a.log.WithError(err).Error("BrokeRequest, parseImp failed")
		return err
	}

	a.DoTrafficSample(request, body)

	return nil
}

func (a *MoskenTrafficBroker) parseDevice(request *mosken_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.Device == nil {
		return
	}

	if request.User != nil {
		switch request.User.Gender {
		case "F":
			adRequest.UserGender = entity.UserGenderWoman
		case "M":
			adRequest.UserGender = entity.UserGenderMan
		}
	}

	if request.App != nil {
		adRequest.App.AppName = request.App.Name
		adRequest.App.AppVersion = request.App.Version
		adRequest.App.AppBundle = request.App.Bundle
	}

	adRequest.Device = ad_service.AdRequestDevice{
		RequestIp:           request.Device.IP,
		UserAgent:           request.Device.Ua,
		OsType:              mappingOsType(request.Device.Os),
		DeviceType:          mappingDeviceType(request.Device.Devicetype),
		OsVersion:           request.Device.Osv,
		Model:               request.Device.Model,
		Brand:               request.Device.Make,
		Vendor:              request.Device.Make,
		ConnectionType:      mappingConnectionType(request.Device.Connectiontype),
		OperatorType:        mappingOperatorType(request.Device.Carrier),
		Idfa:                request.Device.Idfa,
		IdfaMd5:             request.Device.Idfamd5,
		Imei:                request.Device.Imeiplain,
		ImeiMd5:             request.Device.Imeimd5,
		Oaid:                request.Device.Oaid,
		OaidMd5:             request.Device.Oaidmd5,
		AndroidId:           request.Device.Aidplain,
		AndroidIdMd5:        request.Device.Aidmd5,
		Mac:                 request.Device.Macplain,
		MacMd5:              request.Device.Macmd5,
		ScreenWidth:         request.Device.ScreenW,
		ScreenHeight:        request.Device.ScreenH,
		PPI:                 request.Device.Ppi,
		VercodeAg:           request.Device.AgVersion,
		VercodeHms:          request.Device.HmsVersion,
		BootMark:            request.Device.BootMark,
		UpdateMark:          request.Device.UpdateMark,
		DeviceInitTime:      request.Device.BirthTime,
		DeviceUpgradeTime:   request.Device.UpdateTime,
		DeviceStartupTime:   request.Device.BootTime,
		CountryCode:         request.Device.Country,
		Language:            request.Device.Language,
		DeviceName:          request.Device.PhoneName,
		HardwareMachineCode: request.Device.ModelCode,
	}
	if request.Geo != nil {
		adRequest.Device.Lat = request.Geo.Lat
		adRequest.Device.Lon = request.Geo.Lon
	}

	if adRequest.Device.DeviceType == entity.DeviceTypeMobile || adRequest.Device.DeviceType == entity.DeviceTypePad {
		adRequest.Device.IsMobile = true
	}

	if len(request.Device.MemorySize) > 0 {
		adRequest.Device.SystemTotalMem, _ = strconv.ParseInt(request.Device.MemorySize, 10, 64)
	}
	if len(request.Device.DiskSize) > 0 {
		adRequest.Device.SystemTotalDisk, _ = strconv.ParseInt(request.Device.DiskSize, 10, 64)
	}
	if len(request.Device.Timezone) > 0 {
		tz, _ := strconv.ParseInt(request.Device.Timezone, 10, 32)
		adRequest.Device.TimeZone = int32(tz)
	}

	if len(request.Device.Ipv6) > 0 {
		adRequest.Device.IsIp6 = true
		adRequest.Device.RequestIp = request.Device.Ipv6
	}

	if len(request.Device.Caid) > 0 {
		split := strings.Split(request.Device.Caid, ",")
		if len(split) > 0 {
			adRequest.Device.Caid = split[0]
			adRequest.Device.Caids = split
			if strings.Contains(split[0], "_") {
				adRequest.Device.CaidVersion = strings.Split(split[0], "_")[0]
				adRequest.Device.CaidRaw = strings.Split(split[0], "_")[1]
			}
		}
	}

}

func (a *MoskenTrafficBroker) parseImp(request *mosken_broker_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	if len(request.Imp) == 0 {
		return errors.New("impressions empty")
	}

	for _, imp := range request.Imp {
		if imp == nil {
			continue
		}
		adRequest.ImpressionId = imp.ID
		adRequest.SetMediaSlotKey(imp.Tagid)
		adRequest.BidFloor = uint32(imp.Bidfloor)
		adRequest.UseHttps = request.Secure == 1
		adRequest.BidType = entity.BidTypeCpm
		if imp.Pmp != nil && len(imp.Pmp.Deals) > 0 {
			for _, deal := range imp.Pmp.Deals {
				if deal != nil {
					adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
						DealId:   deal.ID,
						BidFloor: int64(deal.Bidfloor),
					})
				}
			}
		}

		switch imp.PosType {
		case 1:
			adRequest.SlotType = entity.SlotTypeOpening
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			key.Video().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL).SetOptional(true)
			adRequest.AppendCreativeTemplateKey(key)
		case 2:
			adRequest.SlotType = entity.SlotTypeBanner
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			adRequest.AppendCreativeTemplateKey(key)
		case 3:
			adRequest.SlotType = entity.SlotTypePopup
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			adRequest.AppendCreativeTemplateKey(key)
		case 5:
			adRequest.SlotType = entity.SlotTypeFeeds
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			adRequest.AppendCreativeTemplateKey(key)
		case 8:
			adRequest.SlotType = entity.SlotTypeRewardVideo
			key := creative_entity.NewCreativeTemplateKey()
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			adRequest.AppendCreativeTemplateKey(key)
		case 9:
			adRequest.SlotType = entity.SlotTypeVideoPause
			key := creative_entity.NewCreativeTemplateKey()
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			adRequest.AppendCreativeTemplateKey(key)
		}

		break
	}

	return nil
}

func (a *MoskenTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("MoskenTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return a.SendFallbackResponse(request, writer)
	}

	bidResponse, err := a.buildResponse(request)
	if err != nil {
		a.log.WithError(err).Error("buildResponse err")
		return err
	}

	err = a.BuildHttpSonicJsonResponse(request, writer, bidResponse)
	if err != nil {
		return err
	}

	a.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		a.log.Infof("SendResponse success, response: %s", responseStr)
	}

	return nil
}

func (a *MoskenTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Infof("Build Fallback Response start. bid response: [%v]", request.Response)
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	_, _ = writer.WriteWithStatus(204, nil)
	return nil
}

func (a *MoskenTrafficBroker) buildResponse(request *ad_service.AdRequest) (*mosken_broker_entity.BidResponse, error) {
	bidResponse := &mosken_broker_entity.BidResponse{
		ID: request.GetRequestId(),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		resBid := &mosken_broker_entity.Bids{
			Impid: request.ImpressionId,
			Price: int(candidate.GetBidPrice().Price),
			Crid:  creative.GetCreativeKey(),
			//Advid:     ,
			AuditType: 2,
			Lp:        genericAd.GetLandingUrl(),
			Deeplink:  genericAd.GetDeepLinkUrl(),
			Pm:        candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			Cm:        candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
		}

		if len(genericAd.GetDeepLinkFailedMonitorList()) > 0 {
			resBid.Em.AppInvokeFailed = genericAd.GetDeepLinkFailedMonitorList()
		}
		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			resBid.Em.AppInvokeSuccess = genericAd.GetDeepLinkMonitorList()
		}
		if len(genericAd.GetDeepLinkUrl()) > 0 {
			resBid.Em.AppInvokeSuccess = append(resBid.Em.AppInvokeSuccess, genericAd.GetDpSuccess())
		}

		if candidate.GetIndexDeal() != nil {
			resBid.Dealid = candidate.GetIndexDeal().DealId
		}

		nativeImages := make([]string, 0)
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				nativeImages = append(nativeImages, rsc.Url)
				resBid.Materials.ImgW = int(rsc.Width)
				resBid.Materials.ImgH = int(rsc.Height)
			case entity.MaterialTypeIcon, entity.MaterialTypeLogo:
				resBid.Materials.Icon = rsc.Url
			case entity.MaterialTypeTitle:
				resBid.Materials.Title = rsc.Data
			case entity.MaterialTypeDesc:
				resBid.Materials.Desc = rsc.Data
			case entity.MaterialTypeVideo:
				resBid.Materials.Video = rsc.Url
				resBid.Materials.VideoW = int(rsc.Width)
				resBid.Materials.VideoH = int(rsc.Height)
				resBid.Materials.VideoDuration = int(rsc.Duration)
				resBid.Materials.VideoSize = int(rsc.FileSize)
			}
		}

		if len(nativeImages) > 1 {
			resBid.Materials.Imgs = nativeImages
		} else if len(nativeImages) > 0 {
			resBid.Materials.Image = nativeImages[0]
		}

		if genericAd.GetAppInfo() != nil {
			if genericAd.GetAppInfo().WechatExt != nil {
				resBid.Materials.Wxoid = genericAd.GetAppInfo().WechatExt.ProgramId
				resBid.Materials.Wxp = genericAd.GetAppInfo().WechatExt.ProgramPath
			}
		}

		bidResponse.Seatbid = []*mosken_broker_entity.Seatbid{{Bids: []*mosken_broker_entity.Bids{resBid}}}
		break
	}

	return bidResponse, nil
}
func (mb *MoskenTrafficBroker) mappingSlotType(s int32) entity.SlotType {
	switch s {
	case 9:
		return entity.SlotTypeVideoPause
	case 2:
		return entity.SlotTypeBanner
	case 1:
		return entity.SlotTypeOpening
	case 8:
		return entity.SlotTypeRewardVideo
	case 5:
		return entity.SlotTypeFeeds
	case 3:
		return entity.SlotTypePopup

	default:
		return entity.SlotTypeUnknown
	}
}

func mappingOsType(os string) entity.OsType {
	switch strings.ToLower(os) {
	case "android":
		return entity.OsTypeAndroid
	case "ios":
		return entity.OsTypeIOS
	case "ott":
		return entity.OsTypeOtt
	default:
		return entity.OsTypeAndroid
	}
}

func mappingScreenOrientation(orientation int32) entity.ScreenOrientationType {
	switch orientation {
	case 0:
		return entity.ScreenOrientationTypePortrait
	case 1:
		return entity.ScreenOrientationTypeLandscape
	default:
		return entity.ScreenOrientationTypePortrait
	}
}

func mappingOperatorType(carrier int) entity.OperatorType {
	switch carrier {
	case 1:
		return entity.OperatorTypeChinaMobile
	case 3:
		return entity.OperatorTypeChinaUnicom
	case 2:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType int) entity.ConnectionType {
	switch connectionType {
	case 20:
		return entity.ConnectionTypeWifi
	case 2:
		return entity.ConnectionType2G
	case 3:
		return entity.ConnectionType3G
	case 4:
		return entity.ConnectionType4G
	case 5:
		return entity.ConnectionType5G
	case 21:
		return entity.ConnectionTypeNetEthernet
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingDeviceType(deviceType int) entity.DeviceType {
	switch deviceType {
	case 1:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePad
	case 4:
		return entity.DeviceTypeOtt
	case 5:
		return entity.DeviceTypePc
	default:
		return entity.DeviceTypeMobile
	}
}
