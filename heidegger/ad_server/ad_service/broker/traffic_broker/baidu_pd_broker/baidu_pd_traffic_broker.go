package baidu_pd_broker

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/baidu_pd_broker/protos"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	objectpool "gitlab.com/dev/heidegger/library/object_pool"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
)

const (
	AdxTemplateKey = "adxTemplate"
)

var (
	bidRequestPool = objectpool.NewObjectPool(func() *protos.BidRequest {
		return new(protos.BidRequest)
	})
	bidResponsePool = objectpool.NewObjectPool(func() *protos.BidResponse {
		return new(protos.BidResponse)
	})
	bidResponseSeatPool = objectpool.NewObjectPool(func() *protos.SeatBid {
		return new(protos.SeatBid)
	})
	bidResponseBidPool = objectpool.NewObjectPool(func() *protos.Bid {
		return new(protos.Bid)
	})
)

type BaiduPDTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	mediaId    utils.ID
	MediaMacro *macro_builder.MediaMacro
	log        *zap.Logger
}

// var _ TrafficBrokerInterface = (*BaiduPDTrafficBroker)(nil)

func NewBaiduPDTrafficBroker(mediaId utils.ID) *BaiduPDTrafficBroker {
	return &BaiduPDTrafficBroker{
		mediaId: mediaId,
		MediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro: "%%PRICE%%", // 仅RTB支持
		},
		log: zap.L().With(zap.String("broker", "BaiduPDTrafficBroker")),
	}
}

func (b *BaiduPDTrafficBroker) Do(request *ad_service.AdRequest) error {
	return b.ParseBidRequest(request)
}

func (b *BaiduPDTrafficBroker) GetMediaId() utils.ID {
	return b.mediaId
}

func (b *BaiduPDTrafficBroker) ParseBidRequest(adRequest *ad_service.AdRequest) error {
	adRequest.Response.SetResponseBuilder(b.BuildResponse)
	adRequest.Response.SetFallbackResponseBuilder(b.BuildFallbackResponse)
	adRequest.AdRequestMedia.WinPriceMacro = b.MediaMacro.MediaPriceMacro
	adRequest.AdRequestMedia.MediaMacro = b.MediaMacro

	body := adRequest.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("[BaiduPDTrafficBroker] request body empty")
	}

	bidRequest := bidRequestPool.Get()
	defer bidRequestPool.Put(bidRequest)

	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		b.log.WithError(err).WithField("body", string(body)).Error("ParseBidRequest error")
		return errors.New("[BaiduPDTrafficBroker] request body invalid")
	}

	if adRequest.IsDebug {
		reqBody, _ := json.Marshal(bidRequest)
		b.log.WithField("request", string(reqBody)).Info("parse request start")
	}

	adRequest.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		adRequest.SetRequestId(utils.NewUUID())
	}
	adRequest.SetMediaId(b.mediaId)

	if err := b.parseImp(bidRequest, adRequest); err != nil {
		b.log.Debug("parseImp fail")
		return err
	}

	if err := b.parseSite(bidRequest, adRequest); err != nil {
		b.log.Debug("parseSite fail")
		return err
	}

	if err := b.parseApp(bidRequest, adRequest); err != nil {
		b.log.Debug("parseApp fail")
		return err
	}

	if err := b.parseUser(bidRequest, adRequest); err != nil {
		b.log.Debug("parseUser fail")
		return err
	}

	if err := b.parseDevice(bidRequest, adRequest); err != nil {
		b.log.Debug("parseDevice fail")
		return err
	}

	if err := b.parseInfo(bidRequest, adRequest); err != nil {
		b.log.Debug("parseInfo fail")
		return err
	}

	// 手机百度-APP-开屏页 Android和iOS广告位ID相同
	if adRequest.GetMediaSlotKey() == "735" {
		// format: os_tagId
		adRequest.SetMediaSlotKeyMapping(fmt.Sprintf("%d_%s", adRequest.Device.OsType, adRequest.GetMediaSlotKey()))
	}

	b.DoTrafficSamplePb(adRequest, bidRequest)

	return nil
}

func (b *BaiduPDTrafficBroker) BuildResponse(adRequest *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if adRequest.IsDebug {
		b.log.Info("BuildResponse start")
		adRequest.Response.Dump("BaiduPDTrafficBroker")
	}

	if adRequest.Response.NoCandidate() {
		return b.BuildFallbackResponse(adRequest, writer)
	}

	bidResponse := bidResponsePool.Get()
	defer bidResponsePool.Put(bidResponse)

	bidResponse.Id = adRequest.GetRequestId()
	bidResponse.Bidid = adRequest.GetRequestId()
	bidResponse.Resmicrotime = strconv.FormatInt(time_utils.GetTimeUnixMilli(), 10)

	for _, candidate := range adRequest.Response.GetAdCandidateList() {
		seatBid := bidResponseSeatPool.Get()
		defer bidResponseSeatPool.Put(seatBid)
		bid := bidResponseBidPool.Get()
		defer bidResponseBidPool.Put(bid)

		seatBid.Bid = append(seatBid.Bid, bid)
		bidResponse.Seatbid = append(bidResponse.Seatbid, seatBid)

		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		trafficData := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(trafficData)
		bidPrice := candidate.GetBidPrice()

		bid.Id = adRequest.GetRequestId()
		bid.Impid = adRequest.ImpressionId
		// PD will ignore price
		bid.Price = float64(bidPrice.Price)

		// bid.Nurl =
		bid.Impurl = candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), trafficData, trackingGen)
		bid.Expurl = candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), trafficData, trackingGen)

		// 广告素材,json序列化的字符串（投放信息流PDB/PD/RTB，如果合作方以先投后审方式接入，则需填充该字段）
		// bid.Adm = ""
		// bid.Ext = ""
		bid.ClickThroughUrl = genericAd.GetLandingUrl()
		bid.DeeplinkUrl = b.updateDeeplink(genericAd.GetDeepLinkUrl(), adRequest.App.AppBundle)
		if adRequest.Device.GetOsType() == entity.OsTypeIOS {
			bid.UlkSchema = b.updateDeeplink(genericAd.GetDeepLinkUrl(), adRequest.App.AppBundle)
		}

		switch genericAd.GetAdType() {
		case entity.AdTypeDsp: // NOTE: 先投后审
			{
				var imageUrl, videoUrl, materialUrl string
				for _, material := range candidate.GetSelectedMaterialList() {
					switch material.MaterialType {
					case entity.MaterialTypeImage:
						imageUrl = material.Url
					case entity.MaterialTypeVideo:
						videoUrl = material.Url
					}
				}
				if len(videoUrl) > 0 {
					materialUrl = videoUrl
				} else {
					materialUrl = imageUrl
				}
				bid.SourceUrl = materialUrl
				bid.MaterialMd5 = md5_utils.GetMd5String(materialUrl)
			}
		default:
			// NOTE: Creative id, 广告id是需求方上传物料时,由百度生成并返回的adid
			bid.Adid = creative.GetCreativeKey()
		}

		adxTemplateMapI, ok := adRequest.GetMediaExtraData(AdxTemplateKey)
		if ok {
			adxTemplateMap := adxTemplateMapI.(map[uint64]string)
			key := candidate.GetActiveCreativeTemplateKey()
			keyInt := key.Uint64()
			bid.Templeid = adxTemplateMap[keyInt]
		}
		if candidate.GetIndexDeal() != nil {
			bid.Dealid = candidate.GetIndexDeal().DealId
		}
		bid.Tagid = adRequest.GetMediaSlotKey()

		break
	}

	buffer := buffer_pool.NewBuffer()
	defer buffer.Release()

	buffer.EnsureSize(bidResponse.Size())
	_, err := bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		b.log.WithError(err).Error("bidResponse marshal error")
		return err
	}
	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/octet-stream")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	b.DoTrafficResponseSamplePb(adRequest, bidResponse)
	if adRequest.IsDebug {
		data, _ := json.Marshal(bidResponse)
		b.log.WithField("response", string(data)).Info("BuildResponse success")
	}
	return nil
}

func (b *BaiduPDTrafficBroker) BuildFallbackResponse(adRequest *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/octet-stream")
	writer.WriteWithStatus(204, nil)

	if adRequest.IsDebug {
		for adId, errCode := range adRequest.Response.GetTotalAdCandidateList().GetErrCodeMap() {
			b.zap.L().Info("BuildFallbackResponse", zap.String("ad", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", adId)))), zap.String("code", fmt.Sprintf("%v", errCode)))
		}
	}
	return nil
}

func (b *BaiduPDTrafficBroker) parseImp(bidRequest *protos.BidRequest, adRequest *ad_service.AdRequest) error {
	if len(bidRequest.GetImp()) < 1 {
		b.log.Error("parseImp no imp")
		return errors.New("[BaiduPDTrafficBroker] request no imp")
	}

	for _, imp := range bidRequest.GetImp() {
		adRequest.ImpressionId = imp.GetId()
		adRequest.SetMediaSlotKey(imp.GetTagid())
		adRequest.SetMediaSlotKeyMapping(imp.GetTagid())
		// PD has no bidfloor, only available in RTB
		adRequest.SetBidFloor(uint32(imp.GetBidfloor()))
		adRequest.UseHttps = imp.GetSecure()

		var width, height = 0, 0
		if imp.GetBanner() != nil && imp.GetBanner().GetH() > 0 && imp.GetBanner().GetW() > 0 {
			width, height = int(imp.GetBanner().GetW()), int(imp.GetBanner().GetH())
			adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
				Width:  int64(width),
				Height: int64(height),
			})
			adRequest.SlotWidth = uint32(width)
			adRequest.SlotHeight = uint32(height)
		}

		// if imp.GetNative() != nil && imp.GetNative().GetRequestNative() != nil {
		//     native := imp.GetNative().GetRequestNative()
		//     native.GetPlcmtcnt()
		// }

		// get SlotType by media tag id
		switch imp.GetTagid() {
		case "1462853203791", "1462853496485", "1494245979274", "1494246364384", "1498728906803", "1498729062574", "1477279708464", "1477279831437", "1510566610070", "1510566516546", "1453093728320", "1453093904902":
			adRequest.SlotType = entity.SlotTypeFeeds
		case "1462854074707", "1462854136999", "1504243449802", "1504243400035":
			adRequest.SlotType = entity.SlotTypeBanner
		case "1503477681890", "1503477540931":
			adRequest.SlotType = entity.SlotTypeVideoOpening
		case "1599545865854", "1599545866013", "1598240310851", "1598240310729":
			adRequest.SlotType = entity.SlotTypePopup
		default:
			adRequest.SlotType = entity.SlotTypeOpening
		}

		adxTemplateMap := make(map[uint64]string)
		adRequest.AdxTemplateId = imp.GetTemplelist()
		hasOpenTemplate := false
		for _, templateId := range imp.GetTemplelist() {
			switch templateId {
			case "1", "2", "4": // 1 image
				key := creative_entity.NewCreativeTemplateKey()
				key.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = templateId
				hasOpenTemplate = true
			case "BC0054", "BC0055", "BC0082", "BC0085", "BC0086", "BC0087", "BC0119", "BC0289", "BC0295": // 1 image with title
				key := creative_entity.NewCreativeTemplateKey()
				key.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
				key.Title().SetRequiredCount(1)
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = templateId
			case "BC0092", "2001", "BC0075": // 1 image with title, icon
				key := creative_entity.NewCreativeTemplateKey()
				key.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
				key.Title().SetRequiredCount(1)
				key.Icon().SetRequiredCount(1)
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = templateId
			case "BC0056", "BC0290", "BC0073", "BC0084": // 3 image with title
				key := creative_entity.NewCreativeTemplateKey()
				key.Image().SetRequiredCount(3).SetRequiredSizeTypeWithAuto(width, height)
				key.Title().SetRequiredCount(1)
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = templateId
			case "GEN124", "BC0077": // 3 image with title, icon
				key := creative_entity.NewCreativeTemplateKey()
				key.Image().SetRequiredCount(3).SetRequiredSizeTypeWithAuto(width, height)
				key.Title().SetRequiredCount(1)
				key.Icon().SetRequiredCount(1)
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = templateId
			case "3", "5": // 1 video
				key := creative_entity.NewCreativeTemplateKey()
				key.Video().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = templateId
			case "BC0068": // 1 video with title, image
				key := creative_entity.NewCreativeTemplateKey()
				key.Video().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
				key.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
				key.Title().SetRequiredCount(1)
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = templateId
			case "BC0098", "BC0094", "BC0285", "BC0286", "BC0072", "BC0129": // 1 video with title, icon, image
				key := creative_entity.NewCreativeTemplateKey()
				key.Video().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
				key.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
				key.Title().SetRequiredCount(1)
				key.Icon().SetRequiredCount(1)
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = templateId
			}
		}
		// 添加默认开屏模板
		if !hasOpenTemplate {
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
			adRequest.AppendCreativeTemplateKey(key)
		}

		adRequest.AddMediaExtraData(AdxTemplateKey, adxTemplateMap)

		for _, dealId := range imp.GetDealidlist() {
			adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
				DealId:   dealId,
				BidFloor: int64(imp.GetBidfloor()),
			})
		}

		// imp.GetAdidlist()
		break
	}

	return nil
}

func (b *BaiduPDTrafficBroker) parseSite(bidRequest *protos.BidRequest, adRequest *ad_service.AdRequest) error {
	site := bidRequest.GetSite()
	if site != nil {
		adRequest.Url = site.GetPage()
		adRequest.Referer = site.GetRef()
	}

	return nil
}

func (b *BaiduPDTrafficBroker) parseApp(bidRequest *protos.BidRequest, adRequest *ad_service.AdRequest) error {
	// Empty app info in bid request for these apps
	switch adRequest.GetMediaSlotKey() {
	case "1974":
		adRequest.App.AppName = "一刻相册"
		adRequest.App.AppBundle = "com.baidu.youavideo"
	case "7303":
		adRequest.App.AppName = "黄油相机"
		adRequest.App.AppBundle = "com.by.butter.camera"
	}

	app := bidRequest.GetApp()
	if app != nil {
		adRequest.App.AppName = app.GetAppName()
		adRequest.App.AppBundle = app.GetBundle()
		adRequest.App.AppVersion = app.GetVer()
		adRequest.App.AdxAppCategory = app.GetCategory()
		if len(app.GetPackageName()) > 0 {
			adRequest.App.AppBundle = app.GetPackageName()
		}
	}

	return nil
}

func (b *BaiduPDTrafficBroker) parseDevice(bidRequest *protos.BidRequest, adRequest *ad_service.AdRequest) error {
	device := bidRequest.GetDevice()
	if device != nil {
		if strings.Contains(device.GetUa(), "%2F") {
			adRequest.Device.UserAgent, _ = url.QueryUnescape(device.GetUa())
		} else {
			adRequest.Device.UserAgent = device.GetUa()
		}
		adRequest.Device.RequestIp = device.GetIp()
		if len(device.GetIpv6()) > 0 && len(device.GetIp()) < 1 {
			adRequest.Device.RequestIp = device.GetIpv6()
			adRequest.Device.IsIp6 = true
		}
		geo := device.GetGeo()
		if geo != nil {
			adRequest.Device.Lat = geo.GetLat()
			adRequest.Device.Lon = geo.GetLon()
		}

		adRequest.Device.Model = device.GetModel()
		adRequest.Device.OsVersion = device.GetOsv()
		adRequest.Device.ScreenWidth = device.GetW()
		adRequest.Device.ScreenHeight = device.GetH()

		switch strings.ToLower(device.GetOs()) {
		case "ios":
			adRequest.Device.OsType = entity.OsTypeIOS
			adRequest.Device.IsMobile = true
		case "android":
			adRequest.Device.OsType = entity.OsTypeAndroid
			adRequest.Device.IsMobile = true
		default:
			adRequest.Device.OsType = entity.OsTypeUnknown
		}

		switch device.GetConnectiontype() {
		case protos.ConnectionType_CELL_2G:
			adRequest.Device.ConnectionType = entity.ConnectionType2G
		case protos.ConnectionType_CELL_3G:
			adRequest.Device.ConnectionType = entity.ConnectionType3G
		case protos.ConnectionType_CELL_4G:
			adRequest.Device.ConnectionType = entity.ConnectionType4G
		case protos.ConnectionType_CELL_5G:
			adRequest.Device.ConnectionType = entity.ConnectionType5G
		case protos.ConnectionType_WIFI:
			adRequest.Device.ConnectionType = entity.ConnectionTypeWifi
		case protos.ConnectionType_ETHERNET:
			adRequest.Device.ConnectionType = entity.ConnectionTypeNetEthernet
		case protos.ConnectionType_CELL_UNKNOWN:
			adRequest.Device.ConnectionType = entity.ConnectionTypeCellular
		default:
			adRequest.Device.ConnectionType = entity.ConnectionTypeUnknown
		}

		switch device.GetDevicetype() {
		case protos.DeviceType_PC:
			adRequest.Device.DeviceType = entity.DeviceTypePc
		case protos.DeviceType_APP, protos.DeviceType_WAP:
			// tablet or mobile?
			adRequest.Device.DeviceType = entity.DeviceTypeMobile
		default:
			adRequest.Device.DeviceType = entity.DeviceTypeUnknown
		}

		switch device.GetOperator() {
		case 1:
			adRequest.Device.OperatorType = entity.OperatorTypeChinaMobile
		case 2:
			adRequest.Device.OperatorType = entity.OperatorTypeChinaUnicom
		case 3:
			adRequest.Device.OperatorType = entity.OperatorTypeChinaTelecom
		default:
			adRequest.Device.OperatorType = entity.OperatorTypeUnknown
		}

		adRequest.Device.Idfa = device.GetIdfa()
		adRequest.Device.IdfaMd5 = device.GetIdfaMd5()
		adRequest.Device.MacMd5 = device.GetMacmd5()
		adRequest.Device.Oaid = device.GetOaid()
		adRequest.Device.AndroidId = device.GetAndroidId()
		adRequest.Device.ImeiMd5 = device.GetImeiMd5()
		adRequest.Device.BootMark = device.GetBootMark()
		adRequest.Device.UpdateMark = device.GetUpdateMark()
		adRequest.Device.Caid = device.GetCaid()
		adRequest.Device.Aaid = device.GetAliAaid()
		adRequest.Device.AliAaid = device.GetAliAaid()
		adRequest.Device.Paid = device.GetPaid()
		if len(adRequest.Device.Paid) < 1 {
			adRequest.Device.Paid = device.GetPddPaid()
		}
		if len(adRequest.Device.Caid) < 1 {
			adRequest.Device.Caid = device.GetXinCaid()
		}
		if len(adRequest.Device.Caid) < 1 {
			adRequest.Device.Caid = device.GetYunCaid()
		}

		// what is cuid?
	}

	return nil
}

func (b *BaiduPDTrafficBroker) parseUser(bidRequest *protos.BidRequest, adRequest *ad_service.AdRequest) error {
	user := bidRequest.GetUser()
	if user != nil {
		adRequest.UserId = user.GetBaiduid()
		if len(adRequest.UserId) < 1 {
			adRequest.UserId = user.GetId()
		}
		if user.GetYob() > 1900 {
			adRequest.UserAge = int32(time_utils.GetTimeNow20060102Int()/10000) - user.GetYob()
		}
		// TODO: adRequest.UserGender = user.GetGender()
		adRequest.Device.UserId = user.GetBuyeruid()

		// device also has geo info
		// geo := user.GetGeo()
		// if geo != nil {
		// 	adRequest.Device.Lat = geo.GetLat()
		// 	adRequest.Device.Lon = geo.GetLon()
		// }
	}

	return nil
}

func (b *BaiduPDTrafficBroker) parseInfo(bidRequest *protos.BidRequest, adRequest *ad_service.AdRequest) error {
	// if len(bidRequest.GetTransInfoList()) > 0 { }

	return nil
}

// 在Deeplink上拼接百度返回按钮参数
func (b *BaiduPDTrafficBroker) updateDeeplink(u string, baiduPkg string) string {
	if len(u) == 0 || len(baiduPkg) == 0 {
		return u
	}

	switch baiduPkg {
	case "com.baidu.searchbox", "com.baidu.BaiduMobile":
		{
			if strings.HasPrefix(u, "openapp.jdmobile://virtual") {
				u = b.updateJdDp(u, map[string]string{
					"keplerID": "kpl_jdjdtg00001228",
				})
			} else if strings.HasPrefix(u, "tbopen://m.taobao.com") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"backURL":     "baiduboxapp://donothing",
					"packageName": "com.baidu.searchbox",
					"appkay":      "24585299",
				})
			} else if strings.HasPrefix(u, "https://ulk.alimama.com") {
				u = b.updateTbUlk(u, map[string]string{
					"backURL":     "baiduboxapp://donothing",
					"packageName": "com.baidu.BaiduMobile",
				})
			} else if strings.HasPrefix(u, "alipays:") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"sourceId":   "baidu",
					"backScheme": "baiduboxapp://donothing",
				})
			} else if strings.HasPrefix(u, "pddopen:") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"packageId": "com.baidu.BaiduMobile",
					"backUrl":   "baiduboxapp://donothing",
				})
			} else if strings.HasPrefix(u, "https://app.yangkeduo.com/universal-link") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"packageId": "com.baidu.BaiduMobile",
					"backUrl":   "baiduboxapp://donothing",
				})
			}
		}
	case "com.baidu.BaiduMap", "com.baidu.map":
		{
			if strings.HasPrefix(u, "openapp.jdmobile://virtual") {
				u = b.updateJdDp(u, map[string]string{
					"keplerID": "kpl_jdjdtg00001166",
				})
			} else if strings.HasPrefix(u, "tbopen://m.taobao.com") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"backURL":     "baidumap://map",
					"packageName": "com.baidu.BaiduMap",
					"appkay":      "27577605",
				})
			} else if strings.HasPrefix(u, "https://ulk.alimama.com") {
				u = b.updateTbUlk(u, map[string]string{
					"backURL":     "baidumap://map",
					"packageName": "com.baidu.map",
				})
			} else if strings.HasPrefix(u, "alipays:") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"backScheme": "baidumap://map",
				})
			} else if strings.HasPrefix(u, "pddopen:") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"packageId": "com.baidu.BaiduMap",
					"backUrl":   "baidumap://map",
				})
			} else if strings.HasPrefix(u, "https://app.yangkeduo.com/universal-link") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"packageId": "baiduditu",
					"backUrl":   "baidumap://map",
				})
			}
		}
	case "com.baidu.haokan":
		{
			if strings.HasPrefix(u, "openapp.jdmobile://virtual") {
				u = b.updateJdDp(u, map[string]string{
					"keplerID": "kpl_jdjdtg00001380",
				})
			} else if strings.HasPrefix(u, "tbopen://m.taobao.com") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"backURL":     "baiduhaokan://donothing?tab=",
					"packageName": "com.baidu.haokan",
					"appkay":      "28104246",
				})
			} else if strings.HasPrefix(u, "https://ulk.alimama.com") {
				u = b.updateTbUlk(u, map[string]string{
					"backURL":     "baiduhaokan://donothing?tab=",
					"packageName": "com.baidu.haokan",
				})
			} else if strings.HasPrefix(u, "alipays:") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"backScheme": "baiduhaokan://donothing?tab=",
				})
			} else if strings.HasPrefix(u, "pddopen:") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"packageId": "com.baidu.haokan",
					"backUrl":   "baiduhaokan://donothing?tab=",
				})
			} else if strings.HasPrefix(u, "https://app.yangkeduo.com/universal-link") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"packageId": "baiduhaokan",
					"backUrl":   "baiduhaokan://donothing?tab=",
				})
			}
		}
	case "com.baidu.tieba":
		{
			if strings.HasPrefix(u, "openapp.jdmobile://virtual") {
				u = b.updateJdDp(u, map[string]string{
					"keplerID": "kpl_jdjdtg00001542",
				})
			} else if strings.HasPrefix(u, "tbopen://m.taobao.com") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"backURL":     "com.baidu.tieba://unidispatch/homepage",
					"packageName": "com.baidu.tieba",
					"appkay":      "28088656",
				})
			} else if strings.HasPrefix(u, "https://ulk.alimama.com") {
				u = b.updateTbUlk(u, map[string]string{
					"backURL":     "com.baidu.tieba://unidispatch/homepage",
					"packageName": "com.baidu.tieba",
				})
			} else if strings.HasPrefix(u, "alipays:") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"backScheme": "com.baidu.tieba://unidispatch/homepage",
				})
			} else if strings.HasPrefix(u, "pddopen:") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"packageId": "com.baidu.tieba",
					"backUrl":   "com.baidu.tieba://unidispatch/homepage",
				})
			} else if strings.HasPrefix(u, "https://app.yangkeduo.com/universal-link") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"packageId": "com.baidu.tieba",
					"backUrl":   "com.baidu.tieba://unidispatch/homepage",
				})
			}
		}
	case "com.baidu.netdisk":
		{
			if strings.HasPrefix(u, "openapp.jdmobile://virtual") {
				u = b.updateJdDp(u, map[string]string{
					"keplerID": "kpl_jdjdtg00001345",
				})
			} else if strings.HasPrefix(u, "tbopen://m.taobao.com") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"backURL":     "bdnetdisk://n/action.EXTERNAL_OPEN",
					"packageName": "com.baidu.netdisk",
					"appkay":      "28088072",
				})
			} else if strings.HasPrefix(u, "https://ulk.alimama.com") {
				u = b.updateTbUlk(u, map[string]string{
					"backURL":     "bdnetdisk://",
					"packageName": "com.baidu.netdisk",
				})
			} else if strings.HasPrefix(u, "alipays:") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"backScheme": "bdnetdisk://n/action.EXTERNAL_OPEN",
				})
			} else if strings.HasPrefix(u, "pddopen:") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"packageId": "com.baidu.netdisk",
					"backUrl":   "bdnetdisk://n/action.EXTERNAL_OPEN",
				})
			} else if strings.HasPrefix(u, "https://app.yangkeduo.com/universal-link") {
				u = net_utils.UpdateUrlQuery(u, map[string]string{
					"packageId": "com.baidu.netdisk",
					"backUrl":   "bdnetdisk://n/action.EXTERNAL_OPEN",
				})
			}
		}
	}

	return u
}

func (b *BaiduPDTrafficBroker) updateJdDp(link string, params map[string]string) string {
	if len(link) == 0 {
		return link
	}

	parsedURL, err := url.Parse(link)
	if err != nil {
		return link
	}

	queries := parsedURL.Query()
	jdParams := queries.Get("params")
	// no JD dp params, return
	if len(jdParams) == 0 {
		return link
	}

	var jdParamsMap map[string]any
	err = sonic.Unmarshal([]byte(jdParams), &jdParamsMap)
	if err != nil {
		return link
	}

	for k, v := range params {
		jdParamsMap[k] = v
	}
	jdParams, err = sonic.MarshalString(jdParamsMap)
	if err != nil {
		return link
	}

	queries.Set("params", jdParams)

	parsedURL.RawQuery = queries.Encode()
	return parsedURL.String()
}

func (b *BaiduPDTrafficBroker) updateTbUlk(link string, params map[string]string) string {
	if len(link) == 0 {
		return link
	}

	parsedURL, err := url.Parse(link)
	if err != nil {
		return link
	}

	queries := parsedURL.Query()
	tbDpLink := queries.Get("smburl")
	// no Taobao deeplink
	if len(tbDpLink) == 0 {
		return link
	}

	// parse nested deeplink
	newTbDpLink := net_utils.UpdateUrlQuery(tbDpLink, params)
	queries.Set("smburl", newTbDpLink)

	parsedURL.RawQuery = queries.Encode()
	return parsedURL.String()
}

// TODO: PDD DP&ULK
