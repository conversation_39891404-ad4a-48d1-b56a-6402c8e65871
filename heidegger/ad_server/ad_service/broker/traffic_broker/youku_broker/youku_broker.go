package youku_broker

import (
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"math"
	"math/big"
	"strconv"
	"strings"
)

type (
	YouKuBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId       utils.ID
		WinPriceMacro string
	}
)

func NewYouKuBroker(mediaId utils.ID) *YouKuBroker {
	return &YouKuBroker{
		mediaId:       mediaId,
		WinPriceMacro: "${AUCTION_PRICE}",
	}
}

func (mb *YouKuBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *YouKuBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro
	return mb.ParseAdRequest(request)
}

func (mb *YouKuBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[YouKuBroker]request body empty")
	}

	bidRequest := &YouKuJsonRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[YouKuBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if err = mb.buildRequest(request, bidRequest); err != nil {
		zap.L().Debug("[YouKuBroker]BrokeRequest, parseUser failed")
		return err
	}
	mb.DoTrafficSample(request, body)
	return nil
}

func (mb *YouKuBroker) buildRequest(request *ad_service.AdRequest, bidRequest *YouKuJsonRequest) error {
	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[YouKuBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(utils.NewUUID())
	}
	request.SetMediaId(mb.mediaId)

	if err := mb.parseImp(bidRequest, request); err != nil {
		zap.L().Debug("[YouKuBroker] BrokeRequest, parseImp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, request); err != nil {
		zap.L().Debug("[YouKuBroker]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseApp(bidRequest, request); err != nil {
		zap.L().Debug("[YouKuBroker]BrokeRequest, parseApp failed")
		return err
	}

	if len(request.App.AppBundle) == 0 {
		if request.Device.OsType == entity.OsTypeIOS {
			request.App.AppName = "Youku"
			request.App.AppBundle = "com.youku.phone"
		} else {
			request.App.AppName = "Youku"
			request.App.AppBundle = "com.youku.YouKu"
		}
	}
	return nil
}

func (mb *YouKuBroker) parseImp(mediaRequest *YouKuJsonRequest, bidReq *ad_service.AdRequest) error {
	if len(mediaRequest.Imp) == 0 {
		return errors.New("no imp")
	}
	imp := mediaRequest.Imp[0]
	bidReq.ImpressionId = mediaRequest.Id
	bidReq.SetMediaSlotKey(imp.Tagid)
	bidReq.BidFloor = uint32(imp.Bidfloor)
	bidReq.UseHttps = true
	switch imp.Tagid {
	case "614", "615", "616", "617":
		bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
			Width:  int64(720),
			Height: int64(1280),
		})
	}
	if imp.Native != nil {
		for _, v := range imp.Native.NativeTemplateIds {
			if template, ok := youkuTemplateMap[v]; ok {
				bidReq.AppendCreativeTemplateKey(template)
				bidReq.AddMediaExtraString(template.String(), v)
			}
		}
		//for _, asset := range imp.Native.Assets {
		//
		//}
	}
	if len(imp.Dealids) > 0 {
		for _, d := range imp.Dealids {
			sd := ad_service.SourceDeal{
				DealId: d,
			}
			bidReq.SourceDeal = append(bidReq.SourceDeal, sd)
		}
	}

	return nil
}

func (mb *YouKuBroker) parseDevice(mediaRequest *YouKuJsonRequest,
	bidReq *ad_service.AdRequest) error {

	device := mediaRequest.Device
	bidReq.Device.OsType = func() entity.OsType {
		switch device.Os {
		case YouKuOsTypeWindows:
			return entity.OsTypeWindows
		case YouKuOsTypeAndroid:
			return entity.OsTypeAndroid
		case YouKuOsTypeIos:
			return entity.OsTypeIOS
		default:
			return entity.OsTypeUnknown
		}
	}()
	bidReq.Device.RequestIp = device.Ip
	if strings.Contains(device.Ip, ":") {
		bidReq.Device.IsIp6 = true
	}

	bidReq.Device.DeviceType = func() entity.DeviceType {
		switch device.Devicetype {
		case YouKuDeviceTypePhone:
			return entity.DeviceTypeMobile
		case YouKuDeviceTypePad:
			return entity.DeviceTypePad
		case YouKuDeviceTypePC:
			return entity.DeviceTypePc
		case YouKuDeviceTypeOtt:
			return entity.DeviceTypeOtt
		default:
			return entity.DeviceTypeUnknown
		}
	}()
	bidReq.Device.OsVersion = device.Osv
	bidReq.Device.UserAgent = device.Ua
	bidReq.Device.Idfa = device.Idfa
	bidReq.Device.Oaid = device.Oaid
	bidReq.Device.ImeiMd5 = device.ImeiMd5
	bidReq.Device.MacMd5 = device.MacMd5
	bidReq.Device.Model = device.Model
	bidReq.Device.Vendor = device.Make

	screenWidth, _ := strconv.Atoi(device.Screenwidth)
	bidReq.Device.ScreenWidth = int32(screenWidth)
	screenHeight, _ := strconv.Atoi(device.Screenhight)
	bidReq.Device.ScreenHeight = int32(screenHeight)

	bidReq.Device.ConnectionType = func() entity.ConnectionType {
		switch device.Connectiontype {
		case 2:
			return entity.ConnectionTypeWifi
		case 4:
			return entity.ConnectionType2G
		case 5:
			return entity.ConnectionType3G
		case 6:
			return entity.ConnectionType4G
		case 1:
			return entity.ConnectionTypeNetEthernet
		default:
			return entity.ConnectionTypeUnknown
		}
	}()
	bidReq.Device.OperatorType = func() entity.OperatorType {
		switch device.Carrier {
		case "1":
			return entity.OperatorTypeChinaMobile
		case "2":
			return entity.OperatorTypeChinaUnicom
		case "3":
			return entity.OperatorTypeChinaTelecom
		default:
			return entity.OperatorTypeUnknown
		}
	}()

	if len(device.Caid) > 0 {
		caids := []*YouKuJsonRequestDeviceCaid{}
		err := sonic.UnmarshalString(device.Caid, &caids)
		if err == nil {
			for _, caid := range caids {
				bidReq.Device.Caid = caid.Version + "_" + caid.Caid
				bidReq.Device.Caids = append(bidReq.Device.Caids, bidReq.Device.Caid)
			}
		}
	}
	return nil

}

func (mb *YouKuBroker) parseApp(mediaRequest *YouKuJsonRequest,
	bidReq *ad_service.AdRequest) error {

	bidReq.App.AppName = mediaRequest.App.Name
	bidReq.App.AppBundle = mediaRequest.App.Pck
	bidReq.App.AppVersion = mediaRequest.App.PckV
	return nil
}

func (mb *YouKuBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("YouKuBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("YouKuBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}
	bidResponse, err := mb.buildResponse(request)
	if err != nil {
		zap.L().Error("YouKuBroker Error in buildResponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := []byte(bidResponse.String())
	writer.SetHeader("Content-Type", "application/json")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	mb.DoTrafficResponseSample(request, data)
	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[YouKuBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", responseStr)))))
	}

	return nil
}

func (mb *YouKuBroker) md5ToInt(str string) string {
	hashStr := md5_utils.GetMd5StringUpper(str)
	// 将 16 进制字符串转换为大整数
	bigIntValue := new(big.Int)
	bigIntValue.SetString(hashStr, 16)

	// 对大整数取模
	result := new(big.Int)
	result.Mod(bigIntValue, big.NewInt(math.MaxInt32))

	return strconv.FormatInt(result.Int64(), 10)
}

func (mb *YouKuBroker) buildResponse(request *ad_service.AdRequest) (*YouKuJsonResponse, error) {
	response := &YouKuJsonResponse{
		Id:      request.GetRequestId(),
		Bidid:   request.GetRequestId(),
		Seatbid: []*YouKuJsonResponseSeat{},
	}
	for _, candidate := range request.Response.GetAdCandidateList() {
		creative := candidate.GetCreative()
		if creative == nil {
			continue
		}
		bid := &YouKuJsonResponseSeatBid{
			Id:         request.GetRequestId(),
			Impid:      request.ImpressionId,
			Price:      int64(candidate.GetBidPrice().Price),
			Crid:       "",
			Native:     nil,
			Advertiser: "",
			Industry:   "",
			Ext: YouKuJsonResponseSeatBidExt{
				Ldp: candidate.GetLandingUrl(),
				Pm:  candidate.GetMacroReplaceImpressionMonitorList(),
				Cm:  candidate.GetMacroReplaceClickMonitorList(),
				Apk: &YouKuJsonResponseSeatBidExtApk{},
			},
		}

		tmList := make([]YouKuJsonResponseSeatBidExtTm, 0)
		for _, delayImp := range candidate.GetGenericAd().GetDelayMonitorUrlList() {
			tm := YouKuJsonResponseSeatBidExtTm{
				T:   delayImp.Delay,
				Url: delayImp.Url,
			}
			tmList = append(tmList, tm)
		}
		if len(tmList) > 0 {
			bid.Ext.Tm = tmList
		}

		if request.Device.OsType == entity.OsTypeIOS {
			bid.Ext.Ulink = candidate.GetDeepLinkUrl()
		} else {
			bid.Ext.Dp = candidate.GetDeepLinkUrl()
		}

		logurl := ""
		if candidate.GetAppInfo() != nil {
			logurl = candidate.GetAppInfo().Icon
			bid.Advertiser = candidate.GetAppInfo().AppName

			bid.Ext.Apk.AppName = candidate.GetAppInfo().AppName
			bid.Ext.Apk.AppVersion = candidate.GetAppInfo().AppVersion
			bid.Ext.Apk.AppDeveloperName = candidate.GetAppInfo().Develop
			bid.Ext.Apk.AppPermissionInfo = candidate.GetAppInfo().Permission
			//bid.Ext.Apk.AppUpdateTime = candidate.GetAppInfo().Permission
			bid.Ext.Apk.AppPrivacyUrl = candidate.GetAppInfo().Privacy
			bid.Ext.Apk.AppIntroduction = candidate.GetAppInfo().AppDescURL
			if len(bid.Ext.Apk.AppIntroduction) == 0 {
				bid.Ext.Apk.AppIntroduction = candidate.GetAppInfo().AppDesc
			}
			if candidate.GetAppInfo().WechatExt != nil {
				bid.Ext.Wdp = &YouKuJsonResponseSeatBidWdp{
					MiniAppId:   candidate.GetAppInfo().WechatExt.ProgramId,
					MiniAppPath: candidate.GetAppInfo().WechatExt.ProgramPath,
				}
			}
		}

		switch candidate.GetLandingAction() {
		case entity.LandingTypeDownload:
			bid.Ext.Apk.DownloadUrl = candidate.GetLandingUrl()
		case entity.LandingTypeDeepLink:
		case entity.LandingTypeWeChatProgram:
		default:
		}

		tempalteKey := candidate.GetActiveCreativeTemplateKey()
		native := &YouKuJsonResponseNative{
			NativeTemplateId: request.GetMediaExtraString(tempalteKey.String(), ""),
		}
		if len(logurl) > 0 {
			native.Logo = &YouKuJsonResponseLogo{
				Url:  logurl,
				Type: "jpg",
			}
		}
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				native.Image = &YouKuJsonResponseImage{
					Url:    rsc.Url,
					Type:   "jpg",
					Width:  rsc.Width,
					Height: rsc.Height,
				}
				bid.Crid = mb.md5ToInt(rsc.Url)
			case entity.MaterialTypeTitle:
				native.Title = rsc.Data
			case entity.MaterialTypeDesc:
				native.SubTitle = rsc.Data
			case entity.MaterialTypeVideo:
				native.Video = &YouKuJsonResponseVideo{
					Url:  rsc.Url,
					W:    rsc.Width,
					H:    rsc.Height,
					Vl:   int32(rsc.Duration),
					Size: rsc.FileSize,
				}
				bid.Crid = mb.md5ToInt(rsc.Url)
			default:

			}
		}
		bid.Native = native
		response.Seatbid = []*YouKuJsonResponseSeat{
			{
				Bid: []*YouKuJsonResponseSeatBid{
					bid,
				},
			},
		}
	}

	return response, nil
}

func (mb *YouKuBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[YouKuBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json")
	writer.WriteWithStatus(204, nil)
	return nil
}
