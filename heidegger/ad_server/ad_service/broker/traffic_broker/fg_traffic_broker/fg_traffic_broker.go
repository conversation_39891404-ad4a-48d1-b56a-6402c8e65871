package fg_traffic_broker

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/fg_traffic_broker/fg_broker_pb"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

const (
	AdxTemplateKey = "adxTemplate"
)

var (
	emptyAdxTemplateMap map[uint64]int32 = make(map[uint64]int32)
)

type (
	FGTrafficBroker struct {
		traffic_broker.TrafficBrokerBase
		mediaId utils.ID

		isJson        bool
		WinPriceMacro string
		MediaMacro    *macro_builder.MediaMacro
	}
)

func NewFGTrafficBroker(mediaId utils.ID) *FGTrafficBroker {
	return &FGTrafficBroker{
		mediaId:       mediaId,
		WinPriceMacro: "__PRICE__",

		MediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "__PRICE__",
			MediaClickUpXMacro:   "__AUCTION_UX__",
			MediaClickUpYMacro:   "__AUCTION_UY__",
			MediaClickDownXMacro: "__AUCTION_DX__",
			MediaClickDownYMacro: "__AUCTION_DY__",
		},
	}
}

func (mb *FGTrafficBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *FGTrafficBroker) Do(request *ad_service.AdRequest) error {
	return mb.ParseAdRequest(request)
}

func (mb *FGTrafficBroker) ParseAdRequest(mRequest *ad_service.AdRequest) error {
	if strings.Contains(mRequest.GetRawHttpHeader("Content-Type"), "json") {
		mb.isJson = true
	} else {
		mb.isJson = false
	}
	mRequest.Response.SetResponseBuilder(mb.SendResponse)
	mRequest.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	mRequest.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro
	mRequest.AdRequestMedia.MediaMacro = mb.MediaMacro

	body := mRequest.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[FGTrafficBroker]request body empty")
	}

	bidRequest := &fg_broker_pb.Request{}
	var err error
	if mb.isJson {
		err = sonic.Unmarshal(body, bidRequest)
	} else {
		err = proto.Unmarshal(body, bidRequest)
	}
	if err != nil {
		zap.L().Error("[InnerTrafficBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if err = mb.buildRequest(mRequest, bidRequest); err != nil {
		zap.L().Debug("[FGTrafficBroker]BrokeRequest, parseUser failed")
		return err
	}
	if mb.isJson {
		mb.DoTrafficSample(mRequest, body)
	} else {
		mb.DoTrafficSamplePb(mRequest, bidRequest)
	}

	return nil
}
func (mb *FGTrafficBroker) buildRequest(mRequest *ad_service.AdRequest, bidRequest *fg_broker_pb.Request) error {
	if mRequest.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[FGTrafficBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	mRequest.IsDebug = bidRequest.Debug || mRequest.IsDebug
	mRequest.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		mRequest.SetRequestId(utils.NewUUID())
	}
	mRequest.SetMediaId(mb.mediaId)

	if err := mb.parseUser(bidRequest, mRequest); err != nil {
		zap.L().Debug("[FGTrafficBroker]BrokeRequest, parseUser failed")
		return err
	}

	if err := mb.parseApp(bidRequest, mRequest); err != nil {
		zap.L().Debug("[FGTrafficBroker]BrokeRequest, parseApp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, mRequest); err != nil {
		zap.L().Debug("[FGTrafficBroker]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseSite(bidRequest, mRequest); err != nil {
		zap.L().Debug("[FGTrafficBroker] BrokeRequest, parseSite failed")
		return err
	}

	if err := mb.parseImp(bidRequest, mRequest); err != nil {
		zap.L().Debug("[FGTrafficBroker] BrokeRequest, parseImp failed")
		return err
	}
	return nil
}

func (mb *FGTrafficBroker) parseImp(mediaBidRequest *fg_broker_pb.Request,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Imp == nil {
		zap.L().Debug("[FGTrafficBroker]parseImp, vendor: , imp nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return fmt.Errorf("parseImp, imp nil")
	}

	for _, item := range mediaBidRequest.Imp {
		if item == nil {
			continue
		}

		bidReq.ImpressionId = item.Id
		if len(bidReq.ImpressionId) < 1 {
			bidReq.ImpressionId = utils.NewUUID()
		}

		bidReq.SetMediaSlotKey(item.SlotId)
		bidReq.BidFloor = uint32(item.BidFloor)

		bidReq.SlotType = mb.mappingSlotType(item.SlotType)

		for _, sizeItem := range item.Size_ {
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(sizeItem.W),
				Height: int64(sizeItem.H),
			})
		}

		for _, d := range item.Deals {
			sd := ad_service.SourceDeal{
				DealId:   d.Id,
				BidFloor: int64(d.BidFloor),
			}

			bidReq.SourceDeal = append(bidReq.SourceDeal, sd)
		}

		bidReq.VideoMaxDuration = item.Maxduration
		bidReq.VideoMinDuration = item.Minduration
		adxTemplateMap := make(map[uint64]int32)

		templateId := item.TemplateId

		if len(templateId) == 0 {
			if bidReq.SlotType == entity.SlotTypeRewardVideo || bidReq.SlotType == entity.SlotTypeVideo {
				templateId = []int32{1, 6, 7, 11}
			} else {
				templateId = []int32{1, 6, 7, 11}
			}
		}

		for _, id := range templateId {
			bidReq.AdxTemplateId = append(bidReq.AdxTemplateId, strconv.FormatInt(int64(id), 10))
			switch id {
			case 1:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = id
			case 2:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = id
			case 5:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1)
				key.Desc().AddRequiredCount(1)
				key.Image().AddRequiredCount(3).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = id
			case 6:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = id
			case 7:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Icon().AddRequiredCount(1).SetOptional(true)
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = id
			case 9:
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(3).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Icon().AddRequiredCount(1).SetOptional(true)
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = id
			case 11:
				key := creative_entity.NewCreativeTemplateKey()
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Title().AddRequiredCount(1)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				bidReq.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = id
			}
		}

		// default template
		key := creative_entity.NewCreativeTemplateKey()
		key.Desc().AddRequiredCount(1).SetOptional(true)
		key.Title().AddRequiredCount(1).SetOptional(true)
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
		key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
		key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
		bidReq.AppendCreativeTemplateKey(key)
		keyId := key.Uint64()
		adxTemplateMap[keyId] = 0

		bidReq.AddMediaExtraData(AdxTemplateKey, adxTemplateMap)

		break
	}

	return nil
}

func (mb *FGTrafficBroker) mappingSlotType(s int32) entity.SlotType {
	switch s {
	case 1:
		return entity.SlotTypeVideo
	case 5:
		return entity.SlotTypeBanner
	case 6:
		return entity.SlotTypeOpening
	case 7:
		return entity.SlotTypePopup
	case 8:
		return entity.SlotTypeFeeds
	case 12:
		return entity.SlotTypeRewardVideo
	default:
		return entity.SlotTypeUnknown
	}
}

func (mb *FGTrafficBroker) parseSite(mediaBidRequest *fg_broker_pb.Request,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Site == nil {
		zap.L().Debug("parseSite, vendor: , site nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	site := mediaBidRequest.Site

	bidReq.Referer = site.Ref
	bidReq.Url = site.Page

	return nil
}

func (mb *FGTrafficBroker) parseDevice(mediaBidRequest *fg_broker_pb.Request,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Device == nil {
		zap.L().Error("parseDevice, device nil", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", mediaBidRequest)))))
		return fmt.Errorf("parseDevice failed")
	}

	device := mediaBidRequest.Device

	bidReq.Device.RequestIp = device.Ip

	bidReq.Device.UserAgent = device.Ua

	if device.Orientation == 1 {
		bidReq.Device.ScreenOrientation = entity.ScreenOrientationTypePortrait
	} else if device.Orientation == 2 {
		bidReq.Device.ScreenOrientation = entity.ScreenOrientationTypeLandscape

	}

	bidReq.Device.OsType = entity.OsType(device.Os)
	bidReq.Device.OsVersion = device.Osv
	bidReq.Device.Model = device.Model
	bidReq.Device.Brand = device.Make

	bidReq.Device.DeviceType = mb.mappingDeviceType(device.Devicetype)

	bidReq.Device.Imei = device.Imei
	bidReq.Device.ImeiMd5 = device.ImeiMd5
	bidReq.Device.Idfa = device.Idfa
	bidReq.Device.IdfaMd5 = device.IdfaMd5
	bidReq.Device.Oaid = device.Oaid
	bidReq.Device.OaidMd5 = device.OaidMd5
	bidReq.Device.AndroidId = device.AndroidId
	bidReq.Device.AndroidIdMd5 = device.AndroidIdMd5
	bidReq.Device.Paid = device.Paid

	if len(device.Caid) > 0 {
		bidReq.Device.Caid = string_utils.ConcatString(device.CaidVersion, "_", device.Caid)
		bidReq.Device.Caids = append(bidReq.Device.Caids, bidReq.Device.Caid)
	}
	bidReq.Device.CaidRaw = device.Caid
	bidReq.Device.CaidVersion = device.CaidVersion
	if len(device.CaidMd5) > 0 {
		bidReq.Device.CaidMd5 = string_utils.ConcatString(device.CaidVersion, "_", device.CaidMd5)
	}

	if len(device.Caids) > 0 {
		for _, caidItem := range device.Caids {
			if device.Caid == caidItem.Caid {
				continue
			}
			caid_ := caidItem.Version + "_" + caidItem.Caid
			bidReq.Device.Caids = append(bidReq.Device.Caids, caid_)
		}
	}

	//bidReq.Device.AliAaid = device.AliAaid

	bidReq.Device.Mac = device.Mac
	bidReq.Device.MacMd5 = device.MacMd5

	bidReq.Device.ScreenHeight = device.H
	bidReq.Device.ScreenWidth = device.W
	bidReq.Device.ScreenDensity = float32(device.Density)

	if device.Geo != nil {
		bidReq.Device.Lat = float64(device.Geo.Lat)
		bidReq.Device.Lon = float64(device.Geo.Lon)
	}

	bidReq.Device.OperatorType = entity.OperatorType(device.Carrier)
	bidReq.Device.ConnectionType = mb.mappingConnectionType(device.ConnectionType)

	bidReq.Device.BootMark = device.BootMark
	bidReq.Device.UpdateMark = device.UpdateMark
	bidReq.Device.DeviceStartupTime = device.BootTime
	bidReq.Device.DeviceUpgradeTime = device.UpdateTime
	bidReq.Device.DeviceInitTime = device.BirthTime
	bidReq.Device.VercodeAg = device.VercodeAg
	bidReq.Device.VercodeHms = device.VercodeHms
	bidReq.Device.CountryCode = device.CountryCode
	bidReq.Device.SystemTotalDisk = device.DiskTotal
	bidReq.Device.SystemTotalMem = device.MemTotal
	bidReq.Device.DeviceName = device.DeviceName
	bidReq.Device.HardwareMachineCode = device.HardwareMachine
	bidReq.Device.PPI = device.Ppi

	return nil

}

func (mb *FGTrafficBroker) mappingConnectionType(connectionType uint32) entity.ConnectionType {
	switch connectionType {
	case 1:
		return entity.ConnectionTypeNetEthernet
	case 2:
		return entity.ConnectionTypeWifi
	case 3:
		return entity.ConnectionTypeCellular
	case 4:
		return entity.ConnectionType2G
	case 5:
		return entity.ConnectionType3G
	case 6:
		return entity.ConnectionType4G
	case 7:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func (mb *FGTrafficBroker) mappingDeviceType(s int32) entity.DeviceType {
	switch s {
	case 1, 3:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePad
	case 4:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeUnknown
	}
}

func (mb *FGTrafficBroker) parseApp(mediaBidRequest *fg_broker_pb.Request,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.App == nil {
		zap.L().Debug("parseApp, vendor: , app nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	app := mediaBidRequest.App

	bidReq.App.AppName = app.Name
	bidReq.App.AppBundle = app.Bundle
	if len(bidReq.App.AppBundle) == 0 && len(app.Appid) != 0 {
		bidReq.App.AppBundle = app.Appid
	}
	bidReq.App.AppVersion = app.Version

	return nil
}

func (mb *FGTrafficBroker) parseUser(mediaBidRequest *fg_broker_pb.Request,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.User == nil {
		zap.L().Debug("parseUser, vendor: , user nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	user := mediaBidRequest.User

	if user.Gender == "M" {
		bidReq.UserGender = entity.UserGenderMan
	} else if user.Gender == "F" {
		bidReq.UserGender = entity.UserGenderWoman
	}

	bidReq.UserAge = type_convert.GetAssertInt32(user.Age)
	if len(user.InstallApp) > 0 {
		bidReq.App.InstalledApp = user.InstallApp
	}
	//bidReq.User.AdxDmpRules = make([]string, len(user.AudienceTag))
	//copy(bidReq.User.AdxDmpRules, user.AudienceTag)
	return nil
}

func (mb *FGTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("FGTrafficBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("FGTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}
	bidResponse, err := mb.buildResponse(request, writer)
	if err != nil {
		zap.L().Error("FGTrafficBroker Error in buildResponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}
	if mb.isJson {
		if err = mb.BuildHttpSonicJsonResponse(request, writer, bidResponse); err != nil {
			return err
		}

		mb.DoTrafficResponseSampleSonicJson(request, bidResponse)
	} else {
		buffer := buffer_pool.NewBufferWriter()
		defer buffer.Release()

		buffer.EnsureSize(bidResponse.Size())
		_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
		if err != nil {
			zap.L().Error("FGTrafficBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
			return err
		}

		data := buffer.Get()
		writer.SetHeader("Content-Type", "application/x-protobuf")
		if _, err := writer.WriteWithStatus(200, data); err != nil {
			return err
		}

		mb.DoTrafficResponseSamplePb(request, bidResponse)
	}

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[FGTrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", responseStr)))))
	}

	return nil
}

func (mb *FGTrafficBroker) buildResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) (*fg_broker_pb.Response, error) {
	bidResponse := &fg_broker_pb.Response{}
	bidResponse.Code = 0
	bidResponse.Id = request.GetRequestId()
	bidResponse.Bidid = request.GetRequestId()

	bidResponse.SeatBid = make([]*fg_broker_pb.ResponseSeatBid, 0)
	seatBid := &fg_broker_pb.ResponseSeatBid{}
	bidResponse.SeatBid = append(bidResponse.SeatBid, seatBid)
	bidResponse.SeatBid[0].Bid = make([]*fg_broker_pb.ResponseBid, 0)

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()

		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		bid := &fg_broker_pb.ResponseBid{
			SupportAction: 0,
			Native: &fg_broker_pb.ResponseBidNative{
				TemplateId: 0,
				Assets:     make([]*fg_broker_pb.ResponseBidNativeAsset, 0),
			},
		}

		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			bid.SupportAction = 1
		} else if genericAd.GetLandingAction() == entity.LandingTypeDeepLink {
			bid.SupportAction = 2
		}

		if genericAd.GetAppInfo() != nil {
			bid.App = &fg_broker_pb.ResponseBidApp{
				Name:          genericAd.GetAppInfo().AppName,
				AppIcon:       genericAd.GetAppInfo().Icon,
				PackageName:   genericAd.GetAppInfo().PackageName,
				AppId:         genericAd.GetAppInfo().AppID,
				AppVersion:    genericAd.GetAppInfo().AppVersion,
				AppSize:       int64(genericAd.GetAppInfo().PackageSize),
				AppPrivacy:    genericAd.GetAppInfo().Privacy,
				AppPermission: genericAd.GetAppInfo().Permission,
				AppDesc:       genericAd.GetAppInfo().AppDesc,
				AppDescUrl:    genericAd.GetAppInfo().AppDescURL,
				Developer:     genericAd.GetAppInfo().Develop,
			}
		}

		bid.Id = request.GetRequestId()
		bid.ImpId = request.ImpressionId

		bidPrice := candidate.GetBidPrice()
		bid.Price = bidPrice.Price

		bid.CreativeId = strconv.Itoa(int(creative.GetCreativeId()))
		if creative.GetCreativeId() == 0 {
			bid.CreativeId = creative.GetCreativeKey()
		}

		bid.Tracking = &fg_broker_pb.ResponseBidTrack{
			LandingPage: candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
			DeepLink:    candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen),
		}

		if genericAd.GetAppInfo() != nil && genericAd.GetAppInfo().WechatExt != nil {
			bid.Tracking.WxProgramId = genericAd.GetAppInfo().WechatExt.ProgramId
			bid.Tracking.WxProgramPath = genericAd.GetAppInfo().WechatExt.ProgramPath
			bid.SupportAction = 2
		}

		bid.Tracking.ImpTrackers = candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
		bid.Tracking.ClkTrackers = candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen)
		if len(genericAd.GetAppDownloadStartedMonitorList()) > 0 {
			bid.Tracking.DownloadStart = candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadStartedMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetAppDownloadFinishedMonitorList()) > 0 {
			bid.Tracking.DownloadEnd = candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadFinishedMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetAppInstalledMonitorList()) > 0 {
			bid.Tracking.InstallEnd = candidate.ReplaceUrlMacroList(genericAd.GetAppInstalledMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetAppInstallStartMonitorList()) > 0 {
			bid.Tracking.InstallStart = candidate.ReplaceUrlMacroList(genericAd.GetAppInstallStartMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			bid.Tracking.DeeplinkTrackers = candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetDeepLinkUrl()) > 0 {
			bid.Tracking.DeeplinkTrackers = append(bid.Tracking.DeeplinkTrackers, candidate.ReplaceUrlMacro(genericAd.GetDpSuccess(), traffic, trackingGen))
		}

		if len(genericAd.GetVideoStartUrlList()) > 0 {
			bid.Tracking.VideoStartPlay = candidate.ReplaceUrlMacroList(genericAd.GetVideoCloseUrlList(), traffic, trackingGen)
		}

		if len(genericAd.GetVideoCloseUrlList()) > 0 {
			bid.Tracking.VideoEndPlay = candidate.ReplaceUrlMacroList(genericAd.GetVideoCloseUrlList(), traffic, trackingGen)
		}

		reqAdxTemplateMap := request.GetMediaExtraDataWithDefault(AdxTemplateKey, emptyAdxTemplateMap).(map[uint64]int32)
		key1 := candidate.GetActiveCreativeTemplateKey()
		keyInt := key1.Uint64()
		bid.Native.TemplateId = reqAdxTemplateMap[keyInt]

		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				if len(rsc.Url) > 0 {
					bid.Native.Assets = append(bid.Native.Assets, &fg_broker_pb.ResponseBidNativeAsset{
						Image: &fg_broker_pb.ResponseBidNativeImg{
							Type: 1,
							Url:  candidate.ReplaceUrlMacro(rsc.Url, traffic, trackingGen),
							W:    rsc.Width,
							H:    rsc.Height,
						},
					})
				}
			case entity.MaterialTypeIcon:
				if len(rsc.Url) > 0 {
					bid.Native.Assets = append(bid.Native.Assets, &fg_broker_pb.ResponseBidNativeAsset{
						Image: &fg_broker_pb.ResponseBidNativeImg{
							Type: 3,
							Url:  rsc.Url,
							W:    rsc.Width,
							H:    rsc.Height,
						},
					})
				}
			case entity.MaterialTypeLogo:
				if len(rsc.Url) > 0 {
					bid.Native.Assets = append(bid.Native.Assets, &fg_broker_pb.ResponseBidNativeAsset{
						Image: &fg_broker_pb.ResponseBidNativeImg{
							Type: 2,
							Url:  rsc.Url,
							W:    rsc.Width,
							H:    rsc.Height,
						},
					})
				}
			case entity.MaterialTypeTitle:
				if len(rsc.Data) > 0 {
					bid.Native.Assets = append(bid.Native.Assets, &fg_broker_pb.ResponseBidNativeAsset{
						Data: &fg_broker_pb.ResponseBidNativeData{
							Type:  1,
							Value: rsc.Data,
						},
					})
				}
			case entity.MaterialTypeDesc:
				if len(rsc.Data) > 0 {
					bid.Native.Assets = append(bid.Native.Assets, &fg_broker_pb.ResponseBidNativeAsset{
						Data: &fg_broker_pb.ResponseBidNativeData{
							Type:  2,
							Value: rsc.Data,
						},
					})
				}
			//case dict.MaterialResourceLayoutAdvertiser:
			//	adm.Ext.AdvertiserName = rsc.Text.Text

			case entity.MaterialTypeVideo:
				if len(rsc.Url) > 0 {
					bid.Native.Assets = append(bid.Native.Assets, &fg_broker_pb.ResponseBidNativeAsset{
						Video: &fg_broker_pb.ResponseBidNativeVideo{
							Url:      candidate.ReplaceUrlMacro(rsc.Url, traffic, trackingGen),
							W:        rsc.Width,
							H:        rsc.Height,
							Duration: int32(rsc.Duration),
							CoverUrl: "",
						},
					})
				}
			//case dict.MaterialResourceLayoutButtonText:
			//	adm.Ext.Btn = rsc.Text.Text
			default:
				continue
			}
		}
		bidResponse.SeatBid[0].Bid = append(bidResponse.SeatBid[0].Bid, bid)
		break

	}
	return bidResponse, nil
}

func (mb *FGTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[FGTrafficBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
	}

	if mb.isJson {
		writer.SetHeader("Content-Length", "0")
		writer.SetHeader("Content-Type", "application/json;charset=utf-8")
		writer.WriteWithStatus(204, nil)
	} else {
		writer.SetHeader("Content-Length", "0")
		writer.SetHeader("Content-Type", "application/x-protobuf")
		writer.WriteWithStatus(204, nil)
	}
	return nil
}
