package bes_traffic_broker

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/gogo/protobuf/proto"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/bes_traffic_broker/bes_broker_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

const nativeParam = "native_param"
const styleInfo = "style_info"
const AdxTemplateKey = "adxTemplate"

var (
	emptyAdxTemplateMap = make(map[uint64]int32)
)

type (
	BesTrafficBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId utils.ID

		WinPriceMacro string
	}
)

func NewBesTrafficBroker(mediaId utils.ID) *BesTrafficBroker {
	return &BesTrafficBroker{
		mediaId:       mediaId,
		WinPriceMacro: "%%PRICE%%",
	}
}

func (mb *BesTrafficBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *BesTrafficBroker) Do(request *ad_service.AdRequest) error {
	return mb.ParseAdRequest(request)
}

func (mb *BesTrafficBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro

	body := request.RawHttpRequest.GetBodyContent()

	if len(body) == 0 {
		return fmt.Errorf("[BesTrafficBroker] request body empty")
	}

	besRequest := &bes_broker_proto.BidRequest{}
	err := proto.Unmarshal(body, besRequest)
	if err != nil {
		zap.L().Error("[BesTrafficBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return errors.Wrapf(err, "[BesTrafficBroker]request body invalid")
	}

	request.SetRequestId(besRequest.GetId())
	request.SetMediaId(mb.mediaId)

	if len(besRequest.GetBaiduIdList()) > 0 {
		request.UserId = besRequest.GetBaiduIdList()[0].GetBaiduUserId()
	}

	//todo usercategory

	for _, tagId := range besRequest.TagId {
		request.App.MediaInstalledAppIds = append(request.App.MediaInstalledAppIds, type_convert.GetAssertString(tagId))
	}

	//if besRequest.GetCustomizedUserTag() != nil {
	//	if len(besRequest.GetCustomizedUserTag().InstalledAppList) > 0 {
	//		for _, intallapp := range besRequest.GetCustomizedUserTag().InstalledAppList {
	//			if intallapp != nil {
	//				request.App.InstalledApp = append(request.App.InstalledApp, type_convert.GetAssertString(intallapp.Id))
	//			}
	//		}
	//	}
	//}

	if besRequest.GetUserGeoInfo() != nil {
		if len(besRequest.GetUserGeoInfo().GetUserCoordinate()) > 0 {
			request.Device.GeoStandard = int(besRequest.GetUserGeoInfo().GetUserCoordinate()[0].Standard)
			request.Device.Lat = float64(besRequest.GetUserGeoInfo().GetUserCoordinate()[0].Latitude)
			request.Device.Lon = float64(besRequest.GetUserGeoInfo().GetUserCoordinate()[0].Longitude)
		}
	}

	request.Url = besRequest.Url
	request.Referer = besRequest.Referer

	if err := mb.parseImp(besRequest, request); err != nil {
		zap.L().Debug("[BesTrafficBroker:BrokeRequest}, parseImp failed")
		return err
	}

	if err := mb.parseDevice(besRequest, request); err != nil {
		zap.L().Debug("[BesTrafficBroker:BrokeRequest}, parseDevice failed")
		return err
	}

	mb.DoTrafficSamplePb(request, besRequest)

	return nil
}

func (mb *BesTrafficBroker) parseImp(besRequest *bes_broker_proto.BidRequest,
	request *ad_service.AdRequest) error {
	if len(besRequest.GetAdslot()) == 0 {
		return fmt.Errorf("[BesTrafficBroker] bes slot empty")
	}

	osType := "0"
	mobile := besRequest.Mobile
	if mobile != nil {
		osType = type_convert.GetAssertString(int(mobile.GetPlatform()))
	}

	adSlot := besRequest.GetAdslot()[0]
	request.ImpressionId = adSlot.Id
	request.ImpSequenceId = adSlot.SequenceId
	request.SetMediaSlotKey(adSlot.AdBlockId)
	request.SlotWidth = uint32(adSlot.Width)
	request.SlotHeight = uint32(adSlot.Height)
	request.SetMediaSlotKeyMapping(type_convert.GetAssertString(adSlot.AdslotType) + "_" + osType)
	if besRequest.ExcludedQuickapp != nil {
		request.BlockQuickapp = *besRequest.ExcludedQuickapp
	}

	request.SlotSize = append(request.SlotSize, ad_service.Size{
		Width:  int64(adSlot.Width),
		Height: int64(adSlot.Height),
	})

	request.SlotType = mb.mappingSlotType(adSlot.AdslotType)
	request.SlotVisibility = adSlot.SlotVisibility
	request.BidFloor = uint32(adSlot.MinimumCpm)

	if adSlot.GetSecure() {
		request.UseHttps = true
	}

	if adSlot.GetPreferredOrderInfo() != nil {
		for _, deal := range adSlot.GetPreferredOrderInfo().PreferredOrders {
			request.SourceDeal = append(request.SourceDeal, ad_service.SourceDeal{
				DealId:   deal.OrderId,
				BidFloor: deal.FixedCpm,
			})
		}
	}

	if adSlot.NativeadParam != nil {
		request.AddMediaExtraInt64(nativeParam, 1)
		for _, requiredFiled := range adSlot.NativeadParam.RequiredFields {
			key := creative_entity.NewCreativeTemplateKey()
			if requiredFiled&int64(bes_broker_proto.BidRequest_AdSlot_NativeAdParam_TITLE) != 0 {
				key.Title().AddRequiredCount(1)
			}

			if requiredFiled&int64(bes_broker_proto.BidRequest_AdSlot_NativeAdParam_DESC) != 0 {
				key.Desc().AddRequiredCount(1)
			}

			if requiredFiled&int64(bes_broker_proto.BidRequest_AdSlot_NativeAdParam_IMAGE) != 0 {
				if adSlot.NativeadParam.Image != nil {
					imageNum := int(adSlot.NativeadParam.ImageNum)
					if imageNum == 0 {
						imageNum = 1
					}

					key.Image().AddRequiredCount(imageNum).SetRequiredSizeTypeWithAuto(int(adSlot.NativeadParam.Image.Width), int(adSlot.NativeadParam.Image.Height))

					//key.Image().AddRequiredCount(imageNum).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
				}
			}

			if requiredFiled&int64(bes_broker_proto.BidRequest_AdSlot_NativeAdParam_LOGOICON) != 0 {
				key.Icon().AddRequiredCount(1)

			}

			if requiredFiled&int64(bes_broker_proto.BidRequest_AdSlot_NativeAdParam_VIDEO) != 0 {
				key.Video().AddRequiredCount(1)
			}
			request.AppendCreativeTemplateKey(key)
		}

	}
	adxTemplateMap := make(map[uint64]int32)
	for _, style := range adSlot.GetStyleInfo() {
		request.AddMediaExtraInt64(styleInfo, 1)
		if style.GetAdStyle() != nil {
			for _, metaStyleGroup := range style.GetAdStyle().GetMetaStyleGroup() {
				if metaStyleGroup.GetMetaStyle() == nil {
					continue
				}

				if request.GetSlotType() == entity.SlotTypeVideo || request.GetSlotType() == entity.SlotTypeRewardVideo {
					if len(metaStyleGroup.GetMetaStyle().GetVideoEle()) == 0 {
						continue
					}
				}

				key := creative_entity.NewCreativeTemplateKey()
				if metaStyleGroup.GetMetaStyle().RequiredElements&int64(bes_broker_proto.BidRequest_AdSlot_StyleInfo_AdStyle_META_ELE_TITLE) > 0 {
					key.Title().AddRequiredCount(1)
				}
				if metaStyleGroup.GetMetaStyle().RequiredElements&int64(bes_broker_proto.BidRequest_AdSlot_StyleInfo_AdStyle_META_ELE_DESC) > 0 {
					key.Desc().AddRequiredCount(1)
				}

				if metaStyleGroup.GetMetaStyle().RequiredElements&int64(bes_broker_proto.BidRequest_AdSlot_StyleInfo_AdStyle_META_ELE_ICON) > 0 {
					key.Icon().AddRequiredCount(1)
				}

				if metaStyleGroup.GetMetaStyle().RequiredElements&int64(bes_broker_proto.BidRequest_AdSlot_StyleInfo_AdStyle_META_ELE_IMAGE) > 0 {
					if len(metaStyleGroup.GetMetaStyle().GetImageEle()) != 0 {
						for _, imageEle := range metaStyleGroup.GetMetaStyle().GetImageEle() {
							key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(imageEle.Width), int(imageEle.Height))
						}
					} else {
						key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(adSlot.Width), int(adSlot.Height))
					}

				}

				if metaStyleGroup.GetMetaStyle().RequiredElements&int64(bes_broker_proto.BidRequest_AdSlot_StyleInfo_AdStyle_META_ELE_VIDEO) > 0 {
					if len(metaStyleGroup.GetMetaStyle().GetVideoEle()) != 0 {
						for _, videoEle := range metaStyleGroup.GetMetaStyle().GetVideoEle() {
							key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(videoEle.Width), int(videoEle.Height))
						}
					} else {
						key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(adSlot.Width), int(adSlot.Height))
					}

				}

				request.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = style.StyleType
			}
		}

		//switch style.StyleType {
		//case 28, 29:
		//	key := creative_entity.NewCreativeTemplateKey()
		//	key.Title().AddRequiredCount(1)
		//	key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(adSlot.Width), int(adSlot.Height))
		//	key.Icon().AddRequiredCount(1)
		//	request.AppendCreativeTemplateKey(key)
		//	keyId := key.Uint64()
		//	adxTemplateMap[keyId] = style.StyleType
		//case 33, 34:
		//	key := creative_entity.NewCreativeTemplateKey()
		//	key.Title().AddRequiredCount(1)
		//	key.Desc().AddRequiredCount(1)
		//	key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(adSlot.Width), int(adSlot.Height))
		//	key.Icon().AddRequiredCount(1)
		//	request.AppendCreativeTemplateKey(key)
		//	keyId := key.Uint64()
		//	adxTemplateMap[keyId] = style.StyleType
		//case 30:
		//	key := creative_entity.NewCreativeTemplateKey()
		//	key.Title().AddRequiredCount(1)
		//	key.Desc().AddRequiredCount(1)
		//	key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(adSlot.Width), int(adSlot.Height))
		//	key.Icon().AddRequiredCount(1)
		//	request.AppendCreativeTemplateKey(key)
		//	keyId := key.Uint64()
		//	adxTemplateMap[keyId] = style.StyleType
		//case 31:
		//	key := creative_entity.NewCreativeTemplateKey()
		//	key.Title().AddRequiredCount(1)
		//	key.Desc().AddRequiredCount(1)
		//	key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(adSlot.Width), int(adSlot.Height))
		//	key.Icon().AddRequiredCount(1)
		//	request.AppendCreativeTemplateKey(key)
		//	keyId := key.Uint64()
		//	adxTemplateMap[keyId] = style.StyleType
		//case 32:
		//	key := creative_entity.NewCreativeTemplateKey()
		//	key.Title().AddRequiredCount(1).SetOptional(true)
		//	key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(adSlot.Width), int(adSlot.Height))
		//	key.Icon().AddRequiredCount(1)
		//	request.AppendCreativeTemplateKey(key)
		//	keyId := key.Uint64()
		//	adxTemplateMap[keyId] = style.StyleType
		//case 35:
		//	key := creative_entity.NewCreativeTemplateKey()
		//	key.Title().AddRequiredCount(1).SetOptional(true)
		//	key.Image().AddRequiredCount(3).SetRequiredSizeTypeWithAuto(int(adSlot.Width), int(adSlot.Height))
		//	key.Icon().AddRequiredCount(1)
		//	request.AppendCreativeTemplateKey(key)
		//	keyId := key.Uint64()
		//	adxTemplateMap[keyId] = style.StyleType
		//case 42:
		//	key := creative_entity.NewCreativeTemplateKey()
		//	key.Title().AddRequiredCount(1).SetOptional(true)
		//	key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
		//	key.Icon().AddRequiredCount(1)
		//	request.AppendCreativeTemplateKey(key)
		//	keyId := key.Uint64()
		//	adxTemplateMap[keyId] = style.StyleType
		//case 36:
		//	key := creative_entity.NewCreativeTemplateKey()
		//	key.Title().AddRequiredCount(1).SetOptional(true)
		//	key.Image().AddRequiredCount(3).SetRequiredSizeTypeWithAuto(int(adSlot.Width), int(adSlot.Height))
		//	key.Icon().AddRequiredCount(1)
		//	request.AppendCreativeTemplateKey(key)
		//	keyId := key.Uint64()
		//	adxTemplateMap[keyId] = style.StyleType
		//case 37, 100:
		//	key := creative_entity.NewCreativeTemplateKey()
		//	key.Title().AddRequiredCount(1).SetOptional(true)
		//	key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
		//	key.Icon().AddRequiredCount(1)
		//	request.AppendCreativeTemplateKey(key)
		//	keyId := key.Uint64()
		//	adxTemplateMap[keyId] = style.StyleType
		//case 41, 102:
		//	key := creative_entity.NewCreativeTemplateKey()
		//	key.Title().AddRequiredCount(1).SetOptional(true)
		//	key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
		//	key.Icon().AddRequiredCount(1)
		//	request.AppendCreativeTemplateKey(key)
		//	keyId := key.Uint64()
		//	adxTemplateMap[keyId] = style.StyleType
		//
		//}
		request.AddMediaExtraData(AdxTemplateKey, adxTemplateMap)
	}

	if len(request.GetCreativeTemplateKeyList()) == 0 {
		switch request.GetSlotType() {
		case entity.SlotTypeVideo, entity.SlotTypeRewardVideo:
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(adSlot.Width), int(adSlot.Height))
			key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(adSlot.Width), int(adSlot.Height))
			request.AppendCreativeTemplateKey(key)
		default:
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(adSlot.Width), int(adSlot.Height))
			request.AppendCreativeTemplateKey(key)
		}
	}

	return nil

}

func (mb *BesTrafficBroker) parseDevice(besRequest *bes_broker_proto.BidRequest,
	request *ad_service.AdRequest) error {
	request.Device.RequestIp = besRequest.Ip
	if len(besRequest.Ip) == 0 && len(besRequest.Ip6) > 0 {
		request.Device.RequestIp = besRequest.Ip6
		request.Device.IsIp6 = true
	}

	request.Device.UserAgent = besRequest.UserAgent

	if besRequest.Mobile == nil {
		return nil
	}

	mobile := besRequest.Mobile

	request.Device.DeviceType = mb.mappingDeviceType(mobile.GetDeviceType())
	request.Device.OsType = mb.mappingOsType(mobile.GetPlatform())
	if mobile.GetOsVersion() != nil {
		request.Device.OsVersion = type_convert.GetAssertString(mobile.GetOsVersion().OsVersionMajor) + "." +
			type_convert.GetAssertString(mobile.GetOsVersion().OsVersionMinor) + "." +
			type_convert.GetAssertString(mobile.GetOsVersion().OsVersionMicro)
	}

	// Bes will send Android device ids in iOS requests too
	for _, mobileId := range mobile.Id {
		switch mobileId.Type {
		case bes_broker_proto.BidRequest_Mobile_MobileID_IMEI:
			if request.Device.OsType != entity.OsTypeIOS {
				request.Device.ImeiMd5 = mobileId.Id
			}
		case bes_broker_proto.BidRequest_Mobile_MobileID_MAC:
			if request.Device.OsType != entity.OsTypeIOS {
				request.Device.MacMd5 = mobileId.Id
			}
		case bes_broker_proto.BidRequest_Mobile_MobileID_OAID:
			if request.Device.OsType != entity.OsTypeIOS {
				request.Device.Oaid = mobileId.Id
			}
		case bes_broker_proto.BidRequest_Mobile_MobileID_CAID:
			if request.Device.OsType == entity.OsTypeIOS {
				caid := string_utils.ConcatString(mobileId.Version, "_", mobileId.Id)
				// set vendor=1(信通院) as default Caid
				if len(request.Device.CaidRaw) == 0 || mobileId.Vendor == 1 {
					request.Device.CaidRaw = mobileId.Id
					request.Device.CaidVersion = mobileId.Version
					request.Device.Caid = caid
				}
				request.Device.Caids = append(request.Device.Caids, caid)
			}
		}
	}

	request.Device.Brand = mobile.Brand
	request.Device.Model = mobile.Model
	request.Device.HardwareMachineCode = mobile.Model

	request.App.SdkVersion = mobile.SdkVersion

	request.Device.ScreenWidth = mobile.ScreenWidth
	request.Device.ScreenHeight = mobile.ScreenHeight
	request.Device.ScreenDensity = mobile.ScreenDensity
	request.Device.OperatorType = mb.mappingOperatorType(mobile.CarrierId)
	request.Device.ConnectionType = mb.mappingNetworkType(mobile.GetWirelessNetworkType())
	request.Device.BootMark = mobile.BootMark
	request.Device.DeviceInitTime = string(mobile.InitMark)
	request.Device.UpdateMark = mobile.UpdateMark
	if len(mobile.UpdateMark) == 0 {
		request.Device.UpdateMark = mobile.AliUpdateMark
	}

	for _, advertiserId := range mobile.ForAdvertisingId {
		switch advertiserId.Type {
		case bes_broker_proto.BidRequest_Mobile_ForAdvertisingID_ANDROID_ID:
			if request.Device.OsType != entity.OsTypeIOS {
				request.Device.AndroidIdMd5 = advertiserId.Id
			}
		case bes_broker_proto.BidRequest_Mobile_ForAdvertisingID_IDFA:
			// Bes will send wrong IDFA data in Android request too
			if request.Device.OsType == entity.OsTypeIOS {
				request.Device.IdfaMd5 = advertiserId.Id
			}
		}
	}

	request.Device.RomName = mobile.RomName
	request.Device.RomVersion = mobile.RomVersion
	request.Device.Paid = mobile.Paid
	if len(mobile.PaidNew) > 0 {
		request.Device.Paid = string(mobile.PaidNew)
	}
	request.Device.VercodeHms = string(besRequest.HmsVer)
	request.Device.VercodeAg = string(besRequest.AgVer)

	if mobile.MobileApp != nil {
		request.App.AppBundle = mobile.MobileApp.AppBundleId
		request.App.AppVersion = mobile.MobileApp.AppVersion
	}

	return nil

}

func (mb *BesTrafficBroker) mappingSlotType(s int32) entity.SlotType {
	switch s {
	case 11:
		return entity.SlotTypePopup
	case 0, 1:
		return entity.SlotTypeBanner
	case 46:
		return entity.SlotTypeOpening
	case 13:
		return entity.SlotTypeFeeds
	case 21, 22, 23, 29:
		return entity.SlotTypeVideo
	case 26:
		return entity.SlotTypeVideoPause
	case 28:
		return entity.SlotTypeRewardVideo
	case 30:
		return entity.SlotTypeVideoOpening
	default:
		return entity.SlotTypeUnknown
	}
}

func (mb *BesTrafficBroker) mappingNetworkType(s bes_broker_proto.BidRequest_Mobile_WirelessNetworkType) entity.ConnectionType {
	switch s {
	case bes_broker_proto.BidRequest_Mobile_UNKNOWN_NETWORK:
		return entity.ConnectionTypeUnknown
	case bes_broker_proto.BidRequest_Mobile_WIFI:
		return entity.ConnectionTypeWifi
	case bes_broker_proto.BidRequest_Mobile_MOBILE_2G:
		return entity.ConnectionType2G
	case bes_broker_proto.BidRequest_Mobile_MOBILE_3G:
		return entity.ConnectionType3G
	case bes_broker_proto.BidRequest_Mobile_MOBILE_4G:
		return entity.ConnectionType4G
	case bes_broker_proto.BidRequest_Mobile_MOBILE_5G:
		return entity.ConnectionType5G
	case bes_broker_proto.BidRequest_Mobile_MOBILE_Ethernet:
		return entity.ConnectionTypeNetEthernet
	default:
		return entity.ConnectionTypeUnknown
	}
}

func (mb *BesTrafficBroker) mappingDeviceType(s bes_broker_proto.BidRequest_Mobile_MobileDeviceType) entity.DeviceType {
	switch s {
	case bes_broker_proto.BidRequest_Mobile_HIGHEND_PHONE:
		return entity.DeviceTypeMobile
	case bes_broker_proto.BidRequest_Mobile_TABLET:
		return entity.DeviceTypePad
	case bes_broker_proto.BidRequest_Mobile_SMART_TV:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeUnknown
	}
}

func (mb *BesTrafficBroker) mappingOsType(s bes_broker_proto.BidRequest_Mobile_OS) entity.OsType {
	switch s {
	case bes_broker_proto.BidRequest_Mobile_UNKNOWN_OS:
		return entity.OsTypeUnknown
	case bes_broker_proto.BidRequest_Mobile_IOS:
		return entity.OsTypeIOS
	case bes_broker_proto.BidRequest_Mobile_ANDROID:
		return entity.OsTypeAndroid
	case bes_broker_proto.BidRequest_Mobile_WINDOWS_PHONE:
		return entity.OsTypeWindowsPhone
	default:
		return entity.OsTypeUnknown
	}
}

func (mb *BesTrafficBroker) mappingOperatorType(s int64) entity.OperatorType {
	switch s {
	case 46000, 46002, 46004, 46007, 46008:
		return entity.OperatorTypeChinaMobile
	case 46001, 46006, 46009:
		return entity.OperatorTypeChinaUnicom
	case 46003, 46005, 46011:
		return entity.OperatorTypeChinaTelecom
	case 46020:
		return entity.OperatorTypeTietong
	default:
		return entity.OperatorTypeUnknown
	}
}

func (mb *BesTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("BesTrafficBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("BesTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}

	bidResponse := &bes_broker_proto.BidResponse{
		Id:          request.GetRequestId(),
		Ad:          nil,
		AdsNumLimit: 1,
	}
	bidResponse.Ad = make([]*bes_broker_proto.BidResponse_Ad, 0)
	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()

		if genericAd == nil || creative == nil {
			continue
		}

		if len(candidate.GetSelectedMaterialList()) == 0 {
			zap.L().Error("[BesTrafficBroker]no matierial")
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		bidPrice := candidate.GetBidPrice()
		landingPage := candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen)

		bidAd := &bes_broker_proto.BidResponse_Ad{
			SequenceId:        request.ImpSequenceId,
			Impid:             request.ImpressionId,
			CreativeId:        int64(candidate.GetAd().AdId),
			AdvertiserId:      mb.GetBesAdvId(), //todo 写死一个
			Width:             int32(request.SlotWidth),
			Height:            int32(request.SlotHeight),
			Category:          203221,
			Type:              4, //todo
			TargetUrl:         candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
			MonitorUrls:       candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			MaxCpm:            bidPrice.Price,
			IsCookieMatching:  false,
			GuaranteedOrderId: 0,
			BudgetType:        proto.Int32(0), //todo cpc
			StyleType:         0,
			ChargeType:        proto.Int32(0),
			ActionType:        0,
		}

		bidAd.LandingPage = net_utils.GetDomainFromUrl(landingPage)

		bidAd.TargetUrl = append(bidAd.TargetUrl, landingPage)

		switch genericAd.GetLandingAction() {
		case entity.LandingTypeDeepLink:
			bidAd.ActionType = 1024
		case entity.LandingTypeDownload:
			bidAd.ActionType = 16
		}

		if len(genericAd.GetDeepLinkUrl()) > 0 {
			deeplinkUrl := candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen)
			bidAd.DeeplinkInfo = &bes_broker_proto.BidResponse_Ad_DeeplinkInfo{
				DeeplinkUrl:  deeplinkUrl,
				AppVersion:   0,
				FallbackUrl:  landingPage,
				FallbackType: 1,
			}

			if strings.HasPrefix(genericAd.GetDeepLinkUrl(), "http") || strings.HasPrefix(genericAd.GetDeepLinkUrl(), "https") {
				bidAd.DeeplinkInfo.UlkUrl = deeplinkUrl
				bidAd.DeeplinkInfo.UlkScheme = deeplinkUrl
			}

			if genericAd.GetLandingAction() == entity.LandingTypeDownload {
				bidAd.DeeplinkInfo.FallbackType = 2
			}

			bidAd.DeeplinkInfo.DeeplinkType = bes_broker_proto.BidResponse_Ad_DEFAULT_DEEPLINK
			if net_utils.IsQuickApp(bidAd.DeeplinkInfo.DeeplinkUrl) {
				bidAd.DeeplinkInfo.DeeplinkType = bes_broker_proto.BidResponse_Ad_QUICK_APP_DEEPLINK
			}
			if genericAd.GetAppInfo() != nil {
				bidAd.DeeplinkInfo.AppBundleId = genericAd.GetAppInfo().PackageName
				bidAd.DeeplinkInfo.Publisher = genericAd.GetAppInfo().Develop
				bidAd.DeeplinkInfo.DownloadAppVersion = genericAd.GetAppInfo().AppVersion
				bidAd.DeeplinkInfo.PrivacyLink = genericAd.GetAppInfo().Privacy
				bidAd.DeeplinkInfo.PermissionLink = genericAd.GetAppInfo().Permission
				bidAd.DeeplinkInfo.AppName = genericAd.GetAppInfo().AppName
				bidAd.DeeplinkInfo.AppIntroductionLink = []byte(genericAd.GetAppInfo().AppDesc)
				if len(genericAd.GetAppInfo().AppDescURL) > 0 {
					bidAd.DeeplinkInfo.AppIntroductionLink = []byte(genericAd.GetAppInfo().AppDescURL)
				}

				if genericAd.GetAppInfo().WechatExt != nil {
					bidAd.DeeplinkInfo.MiniProgrameId = genericAd.GetAppInfo().WechatExt.ProgramId
					bidAd.DeeplinkInfo.MiniProgrameLink = genericAd.GetAppInfo().WechatExt.ProgramPath

					bidAd.DeeplinkInfo.DeeplinkType = bes_broker_proto.BidResponse_Ad_WECHAT_OPENSDK_DEEPLINK
					if len(genericAd.GetAppInfo().PackageName) > 0 {
						bidAd.DeeplinkInfo.DeeplinkType = bes_broker_proto.BidResponse_Ad_DEFAULT_DEEPLINK_WITH_WECHAT
					}
				}
			}
		}

		//1:图片 2:flash 4:图文 5：链接单元  7：video
		switch request.SlotType {
		case entity.SlotTypeRewardVideo, entity.SlotTypeVideo, entity.SlotTypeVideoOpening:
			bidAd.Type = 7
		case entity.SlotTypeOpening:
			bidAd.Type = 1
		}
		// 开屏创意尺寸需要填写真实素材尺寸
		isOpening := false
		creativeWidth, creativeHeight := bidAd.Width, bidAd.Height
		if request.SlotType == entity.SlotTypeOpening || request.SlotType == entity.SlotTypeVideoOpening {
			isOpening = true
		}

		if request.GetMediaExtraInt64WithDefault(nativeParam, 0) == 1 {
			bidAd.NativeAd = &bes_broker_proto.BidResponse_Ad_NativeAd{
				LogoIcon:      nil,
				AppSize:       0,
				BrandName:     "",
				Keyword:       nil,
				VideoUrl:      "",
				VideoWidth:    0,
				VideoHeight:   0,
				VideoDuration: 0,
				VideoSize:     0,
			}
			for _, rsc := range candidate.GetSelectedMaterialList() {
				switch rsc.MaterialType {
				case entity.MaterialTypeImage:
					image := &bes_broker_proto.BidResponse_Ad_NativeAd_Image{
						Url:    candidate.ReplaceUrlMacro(rsc.Url, traffic, trackingGen),
						Width:  rsc.Width,
						Height: rsc.Height,
					}
					creativeWidth, creativeHeight = rsc.Width, rsc.Height

					if !isOpening && request.SlotWidth != 0 {
						image.Width = int32(request.SlotWidth)
					}

					if !isOpening && request.SlotHeight != 0 {
						image.Height = int32(request.SlotHeight)
					}

					bidAd.NativeAd.Image = append(bidAd.NativeAd.Image, image)

				case entity.MaterialTypeTitle:
					bidAd.NativeAd.Title = rsc.Data
				case entity.MaterialTypeDesc:
					bidAd.NativeAd.Desc = rsc.Data
				case entity.MaterialTypeIcon:
					bidAd.NativeAd.LogoIcon = &bes_broker_proto.BidResponse_Ad_NativeAd_Image{
						Url:    rsc.Url,
						Width:  100,
						Height: 100,
					}
				case entity.MaterialTypeVideo:
					bidAd.NativeAd.VideoUrl = candidate.ReplaceUrlMacro(rsc.Url, traffic, trackingGen)
					bidAd.NativeAd.VideoWidth = rsc.Width
					bidAd.NativeAd.VideoHeight = rsc.Height
					bidAd.NativeAd.VideoDuration = int32(rsc.Duration)
				}
			}

		} else if request.GetMediaExtraInt64WithDefault(styleInfo, 0) == 1 {
			bidAd.MetaInfoGroup = make([]*bes_broker_proto.BidResponse_Ad_MetaInfoGroup, 0)
			metaInfoGroup := &bes_broker_proto.BidResponse_Ad_MetaInfoGroup{
				MetaInfo: make([]*bes_broker_proto.BidResponse_Ad_MetaInfo, 0),
			}
			deeplinkUrl := candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen)
			metaInfo := &bes_broker_proto.BidResponse_Ad_MetaInfo{
				ActionInfo: &bes_broker_proto.BidResponse_Ad_MetaInfo_ActionInfo{
					ActionUrl:       landingPage,
					LandingPage:     net_utils.GetDomainFromUrl(landingPage),
					ActionType:      0,
					ClickMonitorUrl: nil,
				},
				Title:        nil,
				Desc:         nil,
				Icon:         nil,
				Image:        nil,
				BrandInfo:    nil,
				Category:     []int32{203221},
				Video:        nil,
				AppInfo:      nil,
				DeeplinkInfo: nil,
				Button:       nil,
				CreativeType: 4,
				Keyword:      nil,
			}

			if request.SlotType == entity.SlotTypeRewardVideo || request.SlotType == entity.SlotTypeVideo || request.SlotType == entity.SlotTypeVideoOpening {
				metaInfo.CreativeType = 7
			}

			if len(deeplinkUrl) > 0 {
				if strings.HasPrefix(deeplinkUrl, "http://") || strings.HasPrefix(deeplinkUrl, "https://") {
					metaInfo.ActionInfo.ActionUrl = deeplinkUrl
				}
				metaInfo.ActionInfo.ActionType = 1024
				metaInfo.DeeplinkInfo = bidAd.DeeplinkInfo
				bidAd.DeeplinkInfo = nil
			}
			if genericAd.GetLandingAction() == entity.LandingTypeDownload {
				metaInfo.ActionInfo.ActionType = 16
				metaInfo.ActionInfo.AppDownloadUrl = landingPage
				metaInfo.ActionInfo.AppStoreLink = deeplinkUrl
			}

			for _, rsc := range candidate.GetSelectedMaterialList() {
				switch rsc.MaterialType {
				case entity.MaterialTypeImage:
					metaInfo.Image = append(metaInfo.Image, &bes_broker_proto.BidResponse_Ad_MetaInfo_SrcMaterial{
						Type:   101,
						Url:    candidate.ReplaceUrlMacro(rsc.Url, traffic, trackingGen),
						Width:  rsc.Width,
						Height: rsc.Height,
					})
					creativeWidth, creativeHeight = rsc.Width, rsc.Height
				case entity.MaterialTypeTitle:
					metaInfo.Title = append(metaInfo.Title, &bes_broker_proto.BidResponse_Ad_MetaInfo_TxtMaterial{Txt: rsc.Data})
				case entity.MaterialTypeDesc:
					metaInfo.Desc = append(metaInfo.Desc, &bes_broker_proto.BidResponse_Ad_MetaInfo_TxtMaterial{Txt: rsc.Data})
				case entity.MaterialTypeIcon:
					metaInfo.Icon = append(metaInfo.Icon, &bes_broker_proto.BidResponse_Ad_MetaInfo_SrcMaterial{
						Type:   101,
						Url:    rsc.Url,
						Width:  120,
						Height: 120,
					})
				case entity.MaterialTypeVideo:
					metaInfo.Video = append(metaInfo.Video, &bes_broker_proto.BidResponse_Ad_MetaInfo_SrcMaterial{
						Type:          1,
						Url:           candidate.ReplaceUrlMacro(rsc.Url, traffic, trackingGen),
						VideoDuration: int32(rsc.Duration),
						Width:         rsc.Width,
						Height:        rsc.Height,
						Size_:         10000,
					})
					creativeWidth, creativeHeight = rsc.Width, rsc.Height
				}
			}

			metaInfoGroup.MetaInfo = append(metaInfoGroup.MetaInfo, metaInfo)
			bidAd.MetaInfoGroup = append(bidAd.MetaInfoGroup, metaInfoGroup)
			reqAdxTemplateMap := request.GetMediaExtraDataWithDefault(AdxTemplateKey, emptyAdxTemplateMap).(map[uint64]int32)
			key1 := candidate.GetActiveCreativeTemplateKey()
			keyInt := key1.Uint64()
			bidAd.StyleType = reqAdxTemplateMap[keyInt]

		} else { //非BES托管的普通广告创意物料信息 material_info todo
			bidAd.MaterialInfo = make([]*bes_broker_proto.BidResponse_Ad_MaterialInfo, 0)
			materialInfo := &bes_broker_proto.BidResponse_Ad_MaterialInfo{}

			for _, rsc := range candidate.GetSelectedMaterialList() {
				switch rsc.MaterialType {
				case entity.MaterialTypeImage:
					materialInfo.MaterialUrl = candidate.ReplaceUrlMacro(rsc.Url, traffic, trackingGen)
					creativeWidth, creativeHeight = rsc.Width, rsc.Height
				case entity.MaterialTypeTitle:
					materialInfo.Title = rsc.Data
				case entity.MaterialTypeDesc:
					materialInfo.Desc = rsc.Data
				case entity.MaterialTypeVideo:
					materialInfo.MaterialUrl = candidate.ReplaceUrlMacro(rsc.Url, traffic, trackingGen)
					materialInfo.VideoDuration = int32(rsc.Duration)
				}
			}
			bidAd.MaterialInfo = append(bidAd.MaterialInfo, materialInfo)
		}

		if isOpening && creativeWidth > 0 && creativeHeight > 0 {
			bidAd.Width, bidAd.Height = creativeWidth, creativeHeight
		}

		if genericAd.GetLandingAction() == entity.LandingTypeDownload && genericAd.GetAppInfo() != nil {
			bidAd.DownloadInfo = &bes_broker_proto.BidResponse_Ad_DownloadInfo{
				AppSize:             int32(genericAd.GetAppInfo().PackageSize),
				AppPackageName:      genericAd.GetAppInfo().PackageName,
				IosId:               genericAd.GetAppInfo().PackageName,
				DocId:               "",
				DownloadDesc:        genericAd.GetAppInfo().AppDesc,
				Publisher:           genericAd.GetAppInfo().Develop,
				AppVersion:          genericAd.GetAppInfo().AppVersion,
				PrivacyLink:         genericAd.GetAppInfo().Privacy,
				PermissionLink:      genericAd.GetAppInfo().Permission,
				AppName:             genericAd.GetAppInfo().AppName,
				AppIntroductionLink: []byte(genericAd.GetAppInfo().AppDesc),
			}
			if len(genericAd.GetAppInfo().AppDescURL) > 0 {
				bidAd.DownloadInfo.AppIntroductionLink = []byte(genericAd.GetAppInfo().AppDescURL)
			}
		}

		eventTrack := &bes_broker_proto.BidResponse_Ad_EventTrackingInfo{}
		if len(genericAd.GetAppDownloadStartedMonitorList()) > 0 {
			eventTrack.DspTrackings = append(eventTrack.DspTrackings, &bes_broker_proto.BidResponse_Ad_Tracking{
				TrackingEvent: bes_broker_proto.BidResponse_Ad_APP_AD_DOWNLOAD_BEGIN,
				TrackingUrl:   genericAd.GetAppDownloadStartedMonitorList(),
			})
		}
		if len(genericAd.GetAppDownloadFinishedMonitorList()) > 0 {
			eventTrack.DspTrackings = append(eventTrack.DspTrackings, &bes_broker_proto.BidResponse_Ad_Tracking{
				TrackingEvent: bes_broker_proto.BidResponse_Ad_APP_AD_DOWNLOAD_FINISH,
				TrackingUrl:   genericAd.GetAppDownloadFinishedMonitorList(),
			})
		}
		if len(genericAd.GetAppInstalledMonitorList()) > 0 {
			eventTrack.DspTrackings = append(eventTrack.DspTrackings, &bes_broker_proto.BidResponse_Ad_Tracking{
				TrackingEvent: bes_broker_proto.BidResponse_Ad_APP_AD_INSTALL_FINISH,
				TrackingUrl:   genericAd.GetAppInstalledMonitorList(),
			})
		}
		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			eventTrack.DspTrackings = append(eventTrack.DspTrackings, &bes_broker_proto.BidResponse_Ad_Tracking{
				TrackingEvent: bes_broker_proto.BidResponse_Ad_DEEPLINK_TRIGGERED_SUCCESS,
				TrackingUrl:   genericAd.GetDeepLinkMonitorList(),
			})
		}

		if candidate.GetIndexDeal() != nil {
			bidAd.PreferredOrderId = candidate.GetIndexDeal().DealId
		}

		bidResponse.Ad = append(bidResponse.Ad, bidAd)

		break
	}

	buffer := buffer_pool.NewBuffer()
	defer buffer.Release()

	buffer.EnsureSize(bidResponse.Size())
	_, err := bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		zap.L().Error("[BesTrafficBroker] MarshalToSizedBuffer err", zap.Error(err))
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/octet-stream")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	mb.DoTrafficResponseSamplePb(request, bidResponse)
	if request.IsDebug {
		responseStr, _ := json.Marshal(bidResponse)
		zap.L().Info("[BesTrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", responseStr)))))
	}

	return nil
}

func (mb *BesTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[BesTrafficBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		zap.L().Info("[BesTrafficBroker] adCode:%+v").GetErrCodeMap())
	}

	response := &bes_broker_proto.BidResponse{
		Id: request.GetRequestId(),
	}

	buffer := buffer_pool.NewBuffer()
	defer buffer.Release()

	buffer.EnsureSize(response.Size())
	_, err := response.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		zap.L().Error("[BesTrafficBroker] MarshalToSizedBuffer err", zap.Error(err))
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/octet-stream")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}
	return nil
}
