package traffic_broker

import (
	"encoding/json"
	"fmt"
	"github.com/labstack/echo/v4"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_sampler"
	"gitlab.com/dev/heidegger/library/buffer_pool"

	"gitlab.com/dev/heidegger/library/utils/slice_utils"
)

type TrafficBrokerBase struct {
	trafficSampler  traffic_sampler.TrafficSampler
	besAdvId        uint64
	attributionType string
}

func (b *TrafficBrokerBase) GetAd(c echo.Context) error {
	return nil
}

func (b *TrafficBrokerBase) SetTrafficSampler(sampler traffic_sampler.TrafficSampler) {
	b.trafficSampler = sampler
}

func (b *TrafficBrokerBase) SetBesAdvId(advId uint64) {
	b.besAdvId = advId
}

func (b *TrafficBrokerBase) GetBesAdvId() uint64 {
	return b.besAdvId
}

func (b *TrafficBrokerBase) SetAttributionType(attributionType string) {
	b.attributionType = attributionType
}

func (b *TrafficBrokerBase) GetAttributionType() string {
	return b.attributionType
}

func (b *TrafficBrokerBase) DoTrafficSample(request *ad_service.AdRequest, data []byte) {
	//if b.trafficSampler == nil || !b.trafficSampler.ShouldSampleTrafficRequest(request.GetMediaId(), request.GetMediaSlotKey()) {
	//	return
	//}

	if b.trafficSampler == nil || !request.GetIsSampled() {
		return
	}

	request.SetSampleRequest(slice_utils.CopyBuffer(data))
	//request.SetIsSampled(true)
}

func (b *TrafficBrokerBase) DoTrafficSamplePb(request *ad_service.AdRequest, message proto.Message) {
	//if b.trafficSampler == nil || !b.trafficSampler.ShouldSampleTrafficRequest(request.GetMediaId(), request.GetMediaSlotKey()) {
	//	return
	//}

	if b.trafficSampler == nil || !request.GetIsSampled() {
		return
	}

	jsonData, err := json.Marshal(message)
	if err != nil {
		jsonData = []byte("proto marshal fail")
	}

	request.SetSampleRequest(jsonData)
	//request.SetIsSampled(true)
}

func (b *TrafficBrokerBase) DoTrafficResponseSample(request *ad_service.AdRequest, data []byte) {
	if b.trafficSampler == nil || !request.GetIsSampled() {
		return
	}

	request.SetSampleResponse(slice_utils.CopyBuffer(data))
}

func (b *TrafficBrokerBase) DoTrafficResponseSamplePb(request *ad_service.AdRequest, message proto.Message) {
	if b.trafficSampler == nil || !request.GetIsSampled() {
		return
	}

	jsonData, err := json.Marshal(message)
	if err != nil {
		jsonData = []byte("proto marshal fail")
	}

	request.SetSampleResponse(jsonData)
}

func (b *TrafficBrokerBase) DoTrafficResponseSampleJson(request *ad_service.AdRequest, data interface{}) {
	if b.trafficSampler == nil || !request.GetIsSampled() {
		return
	}

	jsonData, _ := json.Marshal(data)
	request.SetSampleResponse(jsonData)
}

func (b *TrafficBrokerBase) DoTrafficResponseSampleEasyJson(request *ad_service.AdRequest, marshaler easyjson.Marshaler) {
	if b.trafficSampler == nil || !request.GetIsSampled() {
		return
	}

	jsonData, _ := easyjson.Marshal(marshaler)
	request.SetSampleResponse(jsonData)
}

func (b *TrafficBrokerBase) BuildHttpEasyJsonResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse, marshaler easyjson.Marshaler) error {
	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()

	_, err := easyjson.MarshalToWriter(marshaler, buffer)
	if err != nil {
		zap.L().Error("[TrafficBrokerBase][BuildHttpEasyJsonResponse] Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := buffer.Get()

	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	if request.RawHttpRequest.AcceptGzip() && buffer_pool.ShouldGzip(data) {
		writer.SetHeader("Content-Encoding", "gzip")
		gzipBuffer := buffer_pool.GzipEncodeBuffer(data)
		defer gzipBuffer.Release()

		if _, err := writer.WriteWithStatus(200, gzipBuffer.Get()); err != nil {
			return err
		}
	} else {
		if _, err := writer.WriteWithStatus(200, data); err != nil {
			return err
		}
	}

	return nil
}

func (b *TrafficBrokerBase) BuildHttpSonicJsonResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse, response interface{}) error {
	//buffer := buffer_pool.NewBufferWriter()
	//defer buffer.Release()
	//
	//_, err := easyjson.MarshalToWriter(marshaler, buffer)
	//if err != nil {
	//	zap.L().Error("[TrafficBrokerBase][BuildHttpEasyJsonResponse] Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
	//	return err
	//}
	//
	//data := buffer.Get()

	data, err := sonic.Marshal(response)
	if err != nil {
		zap.L().Error("[TrafficBrokerBase][BuildHttpSonicJsonResponse] Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	if request.RawHttpRequest.AcceptGzip() && buffer_pool.ShouldGzip(data) {
		writer.SetHeader("Content-Encoding", "gzip")
		gzipBuffer := buffer_pool.GzipEncodeBuffer(data)
		defer gzipBuffer.Release()

		if _, err := writer.WriteWithStatus(200, gzipBuffer.Get()); err != nil {
			return err
		}
	} else {
		if _, err := writer.WriteWithStatus(200, data); err != nil {
			return err
		}
	}

	return nil
}

func (b *TrafficBrokerBase) ParseHttpPbRequest(request *ad_service.AdRequest, message proto.Message) error {
	if request.RawHttpRequest.GetHeader("Content-Encoding") == "gzip" {
		body := request.RawHttpRequest.GetBodyContent()
		gzipBuffer, _, err := buffer_pool.GzipDecodeBuffer(body)
		if err != nil {
			return err
		}
		defer gzipBuffer.Release()

		err = proto.Unmarshal(gzipBuffer.Get(), message)
		if err != nil {
			return fmt.Errorf("request body invalid after gzip, err:%s", err)
		}

		return nil
	} else {
		body := request.RawHttpRequest.GetBodyContent()
		err := proto.Unmarshal(body, message)
		if err != nil {
			return fmt.Errorf("request body invalid")
		}
	}

	return nil
}

func (b *TrafficBrokerBase) DoTrafficResponseSampleSonicJson(request *ad_service.AdRequest, response interface{}) {
	if b.trafficSampler == nil || !request.GetIsSampled() {
		return
	}

	jsonData, _ := sonic.Marshal(response)
	request.SetSampleResponse(jsonData)
}
