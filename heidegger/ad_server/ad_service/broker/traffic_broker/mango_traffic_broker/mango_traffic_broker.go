package mango_traffic_broker

import (
	"errors"
	"fmt"
	"net/url"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/mango_traffic_broker/mango_broker_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

const (
	AdxTemplateKey   = "adxTemplate"
	mediaTemplateKey = "mediaTemplate"
	reqVersion       = "version"
)

var (
	emptyAdxTemplateMap   map[uint64]*reqTemplate = make(map[uint64]*reqTemplate)
	emptyMediaTemplateMap map[string]*reqTemplate = make(map[string]*reqTemplate)
)

type (
	reqTemplate struct {
		Style       int64 `json:"style,omitempty"`
		AdSpaceSize int   `json:"ad_space_size,omitempty"`
		Width       int32 `json:"width,omitempty"`
		Height      int32 `json:"height,omitempty"`
		TmpId       int   `json:"tmp_id"`
	}

	MangoTvTrafficBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId    utils.ID
		log        *zap.Logger
		mediaMacro *macro_builder.MediaMacro
	}
)

func NewMangoTvTrafficBroker(mediaId utils.ID) *MangoTvTrafficBroker {
	return &MangoTvTrafficBroker{
		mediaId: mediaId,
		log:     zap.L().With(zap.String("broker", "MangoTvTrafficBroker")),
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro: "%%SETTLE_PRICE%%",
		},
	}
}

func (mb *MangoTvTrafficBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *MangoTvTrafficBroker) Do(request *ad_service.AdRequest) error {
	return mb.ParseAdRequest(request)
}

func (mb *MangoTvTrafficBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = mb.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = mb.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()

	if len(body) == 0 {
		return errors.New("[MangoTvTrafficBroker]request body empty")
	}

	bidRequest := &mango_broker_entity.MangoTVRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		mb.log.WithError(err).Errorf("Request body unmarshal failed, body:%s", string(body))
		return errors.New("[MangoTvTrafficBroker]request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		mb.log.Infof("Parse Request start, request:%s", reqBody)
	}

	request.SetRequestId(bidRequest.Bid)
	if len(bidRequest.Bid) < 1 {
		request.SetRequestId(utils.NewUUID())
	}
	request.SetMediaId(mb.mediaId)

	request.AddMediaExtraInt64(reqVersion, int64(bidRequest.Version))

	if err := mb.parseDevice(request, bidRequest); err != nil {
		return err
	}

	if err := mb.parseImp(request, bidRequest); err != nil {
		return err
	}

	request.App.AppName = "芒果TV"
	switch request.Device.OsType {
	case entity.OsTypeIOS:
		request.App.AppBundle = "com.hunantv.imgotv"
	default:
		request.App.AppBundle = "com.hunantv.imgo.activity"
	}

	mb.DoTrafficSample(request, body)

	return nil
}

func (mb *MangoTvTrafficBroker) parseImp(request *ad_service.AdRequest, bidRequest *mango_broker_entity.MangoTVRequest) error {
	if len(bidRequest.Imp) == 0 {
		return errors.New("[MangoTvTrafficBroker]empty imp")
	}
	item := bidRequest.Imp[0]

	request.ImpressionId = "1"
	request.SetMediaSlotKey(item.SpaceId)
	// format: os_tagId
	request.SetMediaSlotKeyMapping(fmt.Sprintf("%d_%s", request.Device.OsType, item.SpaceId))
	request.BidFloor = uint32(item.MinCpmPrice)
	request.SlotWidth = uint32(item.Width)
	request.SlotHeight = uint32(item.Height)
	request.SlotSize = append(request.SlotSize, ad_service.Size{
		Width:  int64(item.Width),
		Height: int64(item.Height),
	})
	request.VideoMinDuration = item.MinPlaytime
	request.VideoMaxDuration = item.MaxPlaytime

	request.SlotType = mb.mappingSlotType(item.Location)

	for _, pmp := range item.Pmp {
		if pmp == nil {
			continue
		}
		request.SourceDeal = append(request.SourceDeal, ad_service.SourceDeal{
			DealId:   pmp.Id,
			BidFloor: int64(pmp.Price),
		})
	}

	adxTemplateMap := make(map[uint64]*reqTemplate)
	// key: mediaId_style_interactTmplId
	mediaTemplateMap := make(map[string]*reqTemplate)

	for _, template := range item.Template {
		// 互动样式广告
		if template.Interact != nil {
			for _, tmpid := range template.Interact.TmplId {
				adxTmpId := fmt.Sprintf("%d_%d", template.Style, tmpid)
				request.AdxTemplateId = append(request.AdxTemplateId, adxTmpId)
			}
		} else {
			adxTmpId := fmt.Sprintf("%d_%d", template.Style, 0)
			request.AdxTemplateId = append(request.AdxTemplateId, adxTmpId)
		}
		request.SlotSize = append(request.SlotSize, ad_service.Size{
			Width:  int64(template.Width),
			Height: int64(template.Height),
		})
		// TODO: template.MinCpmPrice

		key := creative_entity.NewCreativeTemplateKey()
		if template.TitleLen > 0 {
			key.Title().AddRequiredCount(1)
		}
		if template.DescLen > 0 {
			key.Desc().AddRequiredCount(1)
		}
		adxTemplateId := request.GetMediaId().String() + "_" + type_convert.GetAssertString(template.Style) + "_"
		newTemplateItem := func() *reqTemplate {
			return &reqTemplate{
				Style:       template.Style,
				AdSpaceSize: template.AdSpaceSize,
				Width:       template.Width,
				Height:      template.Height,
				TmpId:       0,
			}
		}
		templateItem := newTemplateItem()
		switch item.Location {
		case 1, 2, 3, 15:
			switch template.Style {
			case 0, 1:
				key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				request.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = templateItem
			case 2, 4:
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				request.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = templateItem
			}
			adxTemplateId = adxTemplateId + "0"
			mediaTemplateMap[adxTemplateId] = templateItem
		case 4:
			switch template.Style {
			case 0, 3:
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				request.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = templateItem
				adxTemplateId = adxTemplateId + "0"
				mediaTemplateMap[adxTemplateId] = templateItem
			case 2, 22:
				key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				request.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = templateItem
				adxTemplateId = adxTemplateId + "0"
				mediaTemplateMap[adxTemplateId] = templateItem
			case 4:
				if template.Interact != nil {
					for _, tmpId := range template.Interact.TmplId {
						tmpKey := mb.getTemplateKey(tmpId, int(template.Width), int(template.Height), key)
						request.AppendCreativeTemplateKey(tmpKey)
						keyId := tmpKey.Uint64()
						item := newTemplateItem()
						item.TmpId = tmpId
						adxTemplateMap[keyId] = item
						adxTemplateId = adxTemplateId + type_convert.GetAssertString(tmpId)
						mediaTemplateMap[adxTemplateId] = item
					}
				}
			}
		case 5:
			if template.Style == 2 {
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				request.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = templateItem
				adxTemplateId = adxTemplateId + "0"
				mediaTemplateMap[adxTemplateId] = templateItem
			}
		case 9:
			switch template.Style {
			case 1:
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				request.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = templateItem
				adxTemplateId = adxTemplateId + "0"
				mediaTemplateMap[adxTemplateId] = templateItem
			case 0:
				key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				request.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = templateItem
				adxTemplateId = adxTemplateId + "0"
				mediaTemplateMap[adxTemplateId] = templateItem
			case 4:
				if template.Interact != nil {
					for _, tmpId := range template.Interact.TmplId {
						tmpKey := mb.getTemplateKey(tmpId, int(template.Width), int(template.Height), key)
						request.AppendCreativeTemplateKey(tmpKey)
						keyId := tmpKey.Uint64()
						item := newTemplateItem()
						item.TmpId = tmpId
						adxTemplateMap[keyId] = item
						adxTemplateId = adxTemplateId + type_convert.GetAssertString(tmpId)
						mediaTemplateMap[adxTemplateId] = item
					}
				}
			}
		case 7:
			switch template.Style {
			case 0, 1, 5, 8, 11, 14:
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				request.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = templateItem
				adxTemplateId = adxTemplateId + "0"
				mediaTemplateMap[adxTemplateId] = templateItem
			case 7:
				key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				request.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = templateItem
				adxTemplateId = adxTemplateId + "0"
				mediaTemplateMap[adxTemplateId] = templateItem
			case 10:
				key.Image().AddRequiredCount(2).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				request.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = templateItem
				adxTemplateId = adxTemplateId + "0"
				mediaTemplateMap[adxTemplateId] = templateItem
			case 12:
				if template.Interact != nil {
					for _, tmpId := range template.Interact.TmplId {
						tmpKey := mb.getTemplateKey(tmpId, int(template.Width), int(template.Height), key)
						request.AppendCreativeTemplateKey(tmpKey)
						keyId := tmpKey.Uint64()
						item := newTemplateItem()
						item.TmpId = tmpId
						adxTemplateMap[keyId] = item
						adxTemplateId = adxTemplateId + type_convert.GetAssertString(tmpId)
						mediaTemplateMap[adxTemplateId] = item
					}
				}
			}
		case 17:
			if template.Style == 0 {
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				request.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = templateItem
				adxTemplateId = adxTemplateId + "0"
				mediaTemplateMap[adxTemplateId] = templateItem
			}
		case 18:
			if template.Style == 0 {
				key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(int(template.Width), int(template.Height))
				request.AppendCreativeTemplateKey(key)
				keyId := key.Uint64()
				adxTemplateMap[keyId] = templateItem
				adxTemplateId = adxTemplateId + "0"
				mediaTemplateMap[adxTemplateId] = templateItem
			}
		}
	}
	request.AddMediaExtraData(AdxTemplateKey, adxTemplateMap)
	request.AddMediaExtraData(mediaTemplateKey, mediaTemplateMap)

	return nil
}

func (mb *MangoTvTrafficBroker) getTemplateKey(tmplId, width, height int, key creative_entity.CreativeTemplateKey) creative_entity.CreativeTemplateKey {
	switch tmplId {
	case 1, 2, 3, 5, 6, 7:
		key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
		key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
		key.Icon().AddRequiredCount(1).SetOptional(true)
	case 4, 39, 8, 41:
		key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
		key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
	case 9, 10, 11, 13, 14, 15:
		key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
		key.Icon().AddRequiredCount(1).SetOptional(true)
	case 38, 40, 12, 23, 18, 24, 16, 17, 20, 21, 34, 36, 28, 29, 45, 47:
		key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
	case 19, 35, 46, 48:
		key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
	case 22:
		key.Image().AddRequiredCount(2).SetRequiredSizeTypeWithAuto(width, height)
	}

	return key
}

func (mb *MangoTvTrafficBroker) parseDevice(request *ad_service.AdRequest, bidRequest *mango_broker_entity.MangoTVRequest) error {

	if bidRequest.Device == nil {
		mb.log.Debugf("parseDevice, device nil, %v", bidRequest)
		return errors.New("[MangoTvTrafficBroker]no device info")
	}

	device := bidRequest.Device

	request.Device.RequestIp = device.Ip
	request.Device.UserAgent = device.Ua
	if strings.Contains(device.Ua, "%2") {
		ua, err := url.QueryUnescape(device.Ua)
		if err == nil {
			request.Device.UserAgent = ua
		}
	}

	request.Device.Lat = type_convert.GetAssertFloat64(device.Len)
	request.Device.Lon = type_convert.GetAssertFloat64(device.Lon)
	request.Device.ImeiMd5 = device.Imei
	request.Device.Idfa = device.Idfa
	request.Device.AndroidId = device.Anid
	request.Device.Mac = device.Mac
	request.Device.MacMd5 = device.MacMd5
	request.Device.OsType, request.Device.OsVersion = mb.getOsInfo(device.Os)
	request.Device.Brand = device.Brand
	request.Device.Model = device.Model
	request.Device.ScreenWidth = device.Sw
	request.Device.ScreenHeight = device.Sh
	request.Device.ConnectionType = mb.getConnectionType(device.ConnectionType)
	request.Device.OperatorType = mb.getOperatorType(device.Carrier)
	request.Device.DeviceType = mb.getDeviceType(device.Type)

	switch device.ScreenOrientation {
	case 1:
		request.Device.ScreenOrientation = entity.ScreenOrientationTypePortrait
	case 2:
		request.Device.ScreenOrientation = entity.ScreenOrientationTypeLandscape
	}
	request.Device.OpenUdid = device.Openudid
	request.Device.Oaid = device.Oaid
	if len(device.Caid) > 0 {
		request.Device.Caid = device.CaidVersion + "_" + device.Caid
		request.Device.Caids = append(request.Device.Caids, request.Device.Caid)
	}
	if len(device.PreCaid) > 0 {
		request.Device.Caids = append(request.Device.Caids, device.PreCaidVersion+"_"+device.PreCaid)
	}
	request.Device.Paid = device.Paid

	return nil
}

func (mb *MangoTvTrafficBroker) mappingSlotType(s int) entity.SlotType {
	switch s {
	case 1, 2, 3, 15:
		return entity.SlotTypeVideo
	case 4:
		return entity.SlotTypeVideoPause
	case 5, 8:
		return entity.SlotTypeBanner
	case 9:
		return entity.SlotTypeOpening
	case 18:
		return entity.SlotTypeRewardVideo
	case 6, 7, 10:
		return entity.SlotTypeFeeds
	default:
		return entity.SlotTypeFeeds
	}
}

func (mb *MangoTvTrafficBroker) getOsInfo(os string) (entity.OsType, string) {
	osArr := strings.Split(os, "_")

	if len(osArr) < 2 {
		return entity.OsTypeUnknown, ""
	}

	switch strings.ToLower(osArr[0]) {
	case "android":
		return entity.OsTypeAndroid, osArr[1]
	case "ios":
		return entity.OsTypeIOS, osArr[1]
	default:
		return entity.OsTypeUnknown, osArr[1]
	}
}

func (mb *MangoTvTrafficBroker) getDeviceType(deviceType int) entity.DeviceType {
	switch deviceType {
	case 1, 101, 102:
		return entity.DeviceTypePc
	case 22, 24, 32, 34, 41, 42:
		return entity.DeviceTypeMobile
	case 21, 23, 31, 33:
		return entity.DeviceTypePad
	case 100:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeMobile
	}
}

func (mb *MangoTvTrafficBroker) getConnectionType(connectionType int) entity.ConnectionType {
	switch connectionType {
	case 1:
		return entity.ConnectionTypeWifi
	case 2:
		return entity.ConnectionType2G
	case 3:
		return entity.ConnectionType3G
	case 4:
		return entity.ConnectionType4G
	case 5:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func (mb *MangoTvTrafficBroker) getOperatorType(operatorType int) entity.OperatorType {
	switch operatorType {
	case 0, 2, 7:
		return entity.OperatorTypeChinaMobile
	case 3, 5, 11:
		return entity.OperatorTypeChinaTelecom
	case 1, 6:
		return entity.OperatorTypeChinaUnicom
	default:
		return entity.OperatorTypeUnknown
	}
}

func (mb *MangoTvTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		mb.log.Infof("Build Response start. bid response:[%v]", request.Response)
		request.Response.Dump("MangoTvTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}

	bidResponse := &mango_broker_entity.MangoTVResponse{
		Version: int(request.GetMediaExtraInt64WithDefault(reqVersion, 3)),
		Bid:     request.GetRequestId(),
		ErrCode: 200,
		Ads:     nil,
	}

	bidResponse.Ads = make([]*mango_broker_entity.MangoTVResponseAds, 0)
	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()

		if genericAd == nil || creative == nil {
			continue
		}

		if len(candidate.GetSelectedMaterialList()) == 0 {
			mb.log.Debug("no matierial")
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		bidPrice := candidate.GetBidPrice()

		bid := &mango_broker_entity.MangoTVResponseAds{
			SpaceId:                 request.GetMediaSlotKey(),
			Price:                   int32(bidPrice.Price),
			ClickThroughUrl:         candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
			DownloadClickThroughUrl: candidate.ReplaceUrlMacro(genericAd.GetH5LandingUrl(), traffic, trackingGen),
			SchemaUrl:               candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen),
			WakeType:                0,
			LandOpenMode:            0,
			ClickStyle:              0,
			TmplId:                  0,
		}

		mappingTemplateIndex := false
		if len(genericAd.GetMediaTemplateIndex()) > 0 {
			reqMediaTemplateMap := request.GetMediaExtraDataWithDefault(mediaTemplateKey, emptyMediaTemplateMap).(map[string]*reqTemplate)
			for _, mediaTemplate := range genericAd.GetMediaTemplateIndex() {
				if _, ok := reqMediaTemplateMap[mediaTemplate]; ok {
					if reqMediaTemplateMap[mediaTemplate] != nil {
						bid.AdSpaceSize = reqMediaTemplateMap[mediaTemplate].AdSpaceSize
						bid.Width = reqMediaTemplateMap[mediaTemplate].Width
						bid.Height = reqMediaTemplateMap[mediaTemplate].Height
						bid.Style = reqMediaTemplateMap[mediaTemplate].Style
						bid.TmplId = reqMediaTemplateMap[mediaTemplate].TmpId
						mappingTemplateIndex = true
					}
				}

			}
		}
		//没有命中定向模板
		if !mappingTemplateIndex {
			key1 := candidate.GetActiveCreativeTemplateKey()
			keyInt := key1.Uint64()
			reqAdxTemplateMap := request.GetMediaExtraDataWithDefault(AdxTemplateKey, emptyAdxTemplateMap).(map[uint64]*reqTemplate)
			if _, ok := reqAdxTemplateMap[keyInt]; !ok {
				return errors.New("[MangoTvTrafficBroker]unknown req adxtemplate")
			} else if reqAdxTemplateMap[keyInt] == nil {
				return errors.New("[MangoTvTrafficBroker]unknown req adxtemplate")
			} else {
				bid.AdSpaceSize = reqAdxTemplateMap[keyInt].AdSpaceSize
				bid.Width = reqAdxTemplateMap[keyInt].Width
				bid.Height = reqAdxTemplateMap[keyInt].Height
				bid.Style = reqAdxTemplateMap[keyInt].Style
				bid.TmplId = reqAdxTemplateMap[keyInt].TmpId
			}
		}

		if len(genericAd.GetDeepLinkUrl()) > 0 && traffic.GetOsType() == entity.OsTypeIOS {
			bid.LandOpenMode = 1
		}

		// NOTE: 如开通了素材域名白名单，则只需要实时返回的 ad_url & click_through_url 的首域名在白名单中即可通过，不需要返回 creative_id 字段，返回了 creative_id 则视为选择预审素材。
		/*
			bid.CreativeId = strconv.Itoa(int(creative.GetCreativeId()))
			if creative.GetCreativeId() == 0 {
				bid.CreativeId = creative.GetCreativeKey()
			}
		*/

		imgUrl := ""
		videoUrl := ""
		videoDuration := int32(0)

		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeTitle:
				bid.Title = rsc.Data
			case entity.MaterialTypeDesc:
				bid.AdDesc = rsc.Data
			case entity.MaterialTypeIcon:
				bid.IconUrl = rsc.Url
				bid.IconWidth = 120
				bid.IconHeight = 120
			case entity.MaterialTypeImage:
				imgUrl = rsc.Url
			case entity.MaterialTypeVideo:
				videoUrl = rsc.Url
				videoDuration = int32(rsc.Duration)
			}
		}

		bid.Ctype = 1
		bid.AdUrl = imgUrl
		if len(videoUrl) > 0 {
			bid.Ctype = 2
			bid.AdUrl = videoUrl
			bid.Duration = videoDuration
			bid.BackgroundAdUrl = imgUrl
		}

		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			bid.ClickAction = 1
		}
		if genericAd.GetLandingAction() == entity.LandingTypeWeChatProgram &&
			genericAd.GetAppInfo() != nil &&
			genericAd.GetAppInfo().WechatExt != nil &&
			len(genericAd.GetAppInfo().WechatExt.ProgramId) > 0 {
			bid.WakeType = 1
			bid.WakeId = genericAd.GetAppInfo().WechatExt.ProgramId
			bid.WakePath = genericAd.GetAppInfo().WechatExt.ProgramPath
		}

		impList := candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
		for _, imp := range impList {
			iurl := mango_broker_entity.MangoTVIurl{
				Event: 0,
				Url:   imp,
			}

			bid.Iurl = append(bid.Iurl, iurl)
		}

		if len(genericAd.GetAppDownloadStartedMonitorList()) > 0 {
			for _, item := range genericAd.GetAppDownloadStartedMonitorList() {
				turl := mango_broker_entity.MangoTVTrackingUrl{
					Event: "dlSt",
					Url:   candidate.ReplaceUrlMacro(item, traffic, trackingGen),
				}
				bid.TrackingUrl = append(bid.TrackingUrl, turl)
			}
		}

		if len(genericAd.GetAppDownloadFinishedMonitorList()) > 0 {
			for _, item := range genericAd.GetAppDownloadFinishedMonitorList() {
				turl := mango_broker_entity.MangoTVTrackingUrl{
					Event: "dlFin",
					Url:   candidate.ReplaceUrlMacro(item, traffic, trackingGen),
				}
				bid.TrackingUrl = append(bid.TrackingUrl, turl)
			}
		}

		if len(genericAd.GetAppInstalledMonitorList()) > 0 {
			for _, item := range genericAd.GetAppInstalledMonitorList() {
				turl := mango_broker_entity.MangoTVTrackingUrl{
					Event: "insFin",
					Url:   candidate.ReplaceUrlMacro(item, traffic, trackingGen),
				}
				bid.TrackingUrl = append(bid.TrackingUrl, turl)
			}
		}

		if len(genericAd.GetAppInstallStartMonitorList()) > 0 {
			for _, item := range genericAd.GetAppInstallStartMonitorList() {
				turl := mango_broker_entity.MangoTVTrackingUrl{
					Event: "insSt",
					Url:   candidate.ReplaceUrlMacro(item, traffic, trackingGen),
				}
				bid.TrackingUrl = append(bid.TrackingUrl, turl)
			}
		}
		// TODO: genericAd.GetDelayMonitorUrlList()

		bid.Curl = candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen)

		bidResponse.Ads = append(bidResponse.Ads, bid)
		break
	}

	if err := mb.BuildHttpSonicJsonResponse(request, writer, &bidResponse); err != nil {
		return err
	}

	mb.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		resBody, _ := sonic.Marshal(bidResponse)
		mb.log.Infof("SendResponse success, response:%s", resBody)
	}

	return nil
}

func (mb *MangoTvTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		mb.log.Infof("Build Response start. bid response:[%v]", request.Response)
		mb.log.Infof("adCode:%+v", request.Response.GetTotalAdCandidateList().GetErrCodeMap())
	}

	response := &mango_broker_entity.MangoTVResponse{
		Version: int(request.GetMediaExtraInt64WithDefault(reqVersion, 3)),
		Bid:     request.GetRequestId(),
		ErrCode: 204,
	}

	mb.BuildHttpSonicJsonResponse(request, writer, response)
	return nil
}
