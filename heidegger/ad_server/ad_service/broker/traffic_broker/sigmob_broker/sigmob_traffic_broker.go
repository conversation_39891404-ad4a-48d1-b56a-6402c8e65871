package sigmob_broker

import (
	"errors"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	sigmob "gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/sigmob_broker/sigmob_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"strconv"
	"strings"
	"time"
	"fmt"
)

const imageAssetId = "image_asset_id"
const iconAssetId = "icon_asset_id"
const videoAssetId = "video_asset_id"
const titleAssetId = "title_asset_id"
const descAssetId = "desc_asset_id"
const brandAssetId = "brand_asset_id"
const sigInstl = "instl"
const AdxTemplateKey = "adxTemplate"

var (
	emptyAdxTemplateMap map[uint64]int = make(map[uint64]int)
)

type (
	SigmobTrafficBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId utils.ID

		MediaMacro *macro_builder.MediaMacro
	}
)

func NewSigmobTrafficBroker(mediaId utils.ID) *SigmobTrafficBroker {
	return &SigmobTrafficBroker{
		mediaId: mediaId,
		MediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "${AUCTION_PRICE}",
			MediaClickUpXMacro:   "${UP_X}",
			MediaClickUpYMacro:   "${UP_Y}",
			MediaClickDownXMacro: "${DOWN_X}",
			MediaClickDownYMacro: "${DOWN_Y}",
		},
	}
}

func (s *SigmobTrafficBroker) GetMediaId() utils.ID {
	return s.mediaId
}

func (s *SigmobTrafficBroker) Do(request *ad_service.AdRequest) error {
	return s.ParseAdRequest(request)
}

func (s *SigmobTrafficBroker) ParseAdRequest(mRequest *ad_service.AdRequest) error {
	//zap.L().Info("[SigmobTrafficBroker] ParseAdRequest")

	mRequest.Response.SetResponseBuilder(s.SendResponse)
	mRequest.Response.SetFallbackResponseBuilder(s.SendFallbackResponse)
	mRequest.AdRequestMedia.MediaMacro = s.MediaMacro

	body := mRequest.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("[SigmobTrafficBroker]request body empty")
	}

	request := &sigmob.SigmobBidRequest{}
	err := sonic.Unmarshal(body, request)
	if err != nil {
		zap.L().Error("[SigmobTrafficBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return errors.New("request body invalid")
	}
	zap.L().Debug("SigmobTrafficBroker Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", *request)))))

	if mRequest.IsDebug {
		reqBody, _ := sonic.Marshal(request)
		zap.L().Info("[SigmobTrafficBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	if len(request.Imp) <= 0 {
		zap.L().Debug("request invalid", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request)))))
		return errors.New("request invalid")
	}

	mRequest.SetRequestId(request.Id)
	if len(request.Id) < 1 {
		mRequest.SetRequestId(utils.NewUUID())
	}
	mRequest.SetMediaId(s.mediaId)
	mRequest.TMax = request.Tmax

	if err := s.parseImp(request, mRequest); err != nil {
		zap.L().Debug("BrokeRequest, parseImp failed")
		return err
	}
	if err := s.parseApp(request, mRequest); err != nil {
		zap.L().Debug("BrokeRequest, parseApp failed")
		return err
	}
	if err := s.parseDevice(request, mRequest); err != nil {
		zap.L().Debug("BrokeRequest, parseDevice failed")
		return err
	}
	if err := s.parseUser(request, mRequest); err != nil {
		zap.L().Debug("BrokeRequest, parseUser failed")
		return err
	}

	s.DoTrafficSample(mRequest, body)

	return nil
}

func (s *SigmobTrafficBroker) parseImp(request *sigmob.SigmobBidRequest, bidRequest *ad_service.AdRequest) error {
	for _, imp := range request.Imp {
		if imp == nil {
			continue
		}
		bidRequest.ImpressionId = imp.Id
		bidRequest.SetMediaSlotKey(imp.Tagid)
		bidRequest.BidFloor = uint32(imp.Bidfloor)

		switch imp.Instl {
		case 1:
			bidRequest.SlotType = entity.SlotTypeRewardVideo
		case 2:
			bidRequest.SlotType = entity.SlotTypeOpening
		case 3:
			bidRequest.SlotType = entity.SlotTypeFeeds
		case 4:
			bidRequest.SlotType = entity.SlotTypeBanner
		case 6:
			bidRequest.SlotType = entity.SlotTypePopup
		}

		bidRequest.AddMediaExtraInt64(sigInstl, int64(imp.Instl))

		if imp.Banner != nil {
			bidRequest.SlotType = entity.SlotTypeBanner
			bidRequest.SlotSize = append(bidRequest.SlotSize, ad_service.Size{
				Width:  int64(imp.Banner.W),
				Height: int64(imp.Banner.H),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(imp.Banner.W, imp.Banner.H)
			bidRequest.AppendCreativeTemplateKey(key)
			//imp.Banner.Mimes 写死
			//bidImp.AllowMimes = append(bidImp.AllowMimes, dict.MimeJpg, dict.MimePng, dict.MimeJpeg)
		}

		natives := imp.Natives
		if len(natives) == 0 && imp.Native != nil {
			natives = make([]*sigmob.SigmobImpNative, 0)
			natives = append(natives, imp.Native)
		}

		for _, impNative := range natives {
			key := creative_entity.NewCreativeTemplateKey()
			for _, asset := range impNative.Assets {
				if asset.Image != nil {
					bidRequest.SlotSize = append(bidRequest.SlotSize, ad_service.Size{
						Width:  int64(asset.Image.W),
						Height: int64(asset.Image.H),
					})

					if asset.Image.Type == 1 {
						if bidRequest.GetMediaExtraInt64WithDefault(imageAssetId, 0) == 0 || asset.Required == 1 {
							bidRequest.AddMediaExtraInt64(imageAssetId, int64(asset.Id))
							key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(asset.Image.W, asset.Image.H)
						}
					} else if asset.Image.Type == 2 {
						bidRequest.AddMediaExtraInt64(iconAssetId, int64(asset.Id))
						if asset.Required == 1 {
							key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
						}
					}
				}

				if asset.Video != nil && asset.Video.W > 0 {
					bidRequest.SlotSize = append(bidRequest.SlotSize, ad_service.Size{
						Width:  int64(asset.Video.W),
						Height: int64(asset.Video.H),
					})
					bidRequest.VideoMinDuration = int32(asset.Video.MinDuration)
					bidRequest.VideoMaxDuration = int32(asset.Video.MaxDuration)

					if bidRequest.GetMediaExtraInt64WithDefault(videoAssetId, 0) == 0 || asset.Required == 1 {
						bidRequest.AddMediaExtraInt64(videoAssetId, int64(asset.Id))
						key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(asset.Video.W, asset.Video.H)
					}
				}

				if asset.Text != nil {
					if asset.Text.Type == 1 {
						bidRequest.AddMediaExtraInt64(titleAssetId, int64(asset.Id))
						if asset.Required == 1 {
							key.Title().AddRequiredCount(1)
						}
					} else if asset.Text.Type == 2 {
						bidRequest.AddMediaExtraInt64(descAssetId, int64(asset.Id))
						if asset.Required == 1 {
							key.Desc().AddRequiredCount(1)
						}
					} else if asset.Text.Type == 8 {
						bidRequest.AddMediaExtraInt64(brandAssetId, int64(asset.Id))
					}
				}
			}
			bidRequest.AppendCreativeTemplateKey(key)
			//bidImp.AllowMimes = append(bidImp.AllowMimes, dict.MimeJpg, dict.MimePng, dict.MimeJpeg, dict.MimeMp4)
		}

		if imp.Instl == 6 && len(imp.Formattype) > 0 {
			adxTemplateMap := make(map[uint64]int)

			has06 := false
			for _, formattype := range imp.Formattype {
				switch formattype {
				case 600006:
					has06 = true
				}
			}

			for _, formattype := range imp.Formattype {
				key := creative_entity.NewCreativeTemplateKey()
				switch formattype {
				case 600001:
					key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
					key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
					bidRequest.VideoMinDuration = 5
					bidRequest.VideoMaxDuration = 30
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1)
					key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
					bidRequest.AppendCreativeTemplateKey(key)
					keyId := key.Uint64()
					adxTemplateMap[keyId] = formattype
				case 600002:
					if has06 {
						continue
					}
					key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1)
					key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
					bidRequest.AppendCreativeTemplateKey(key)
					keyId := key.Uint64()
					adxTemplateMap[keyId] = formattype
				case 600003:
					if has06 {
						continue
					}
					key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
					bidRequest.VideoMinDuration = 5
					bidRequest.VideoMaxDuration = 30
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1)
					key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
					bidRequest.AppendCreativeTemplateKey(key)
					keyId := key.Uint64()
					adxTemplateMap[keyId] = formattype
				case 600004:
					if has06 {
						continue
					}
					key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1)
					key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
					bidRequest.AppendCreativeTemplateKey(key)
					keyId := key.Uint64()
					adxTemplateMap[keyId] = formattype
				case 600005:
					key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
					bidRequest.VideoMinDuration = 5
					bidRequest.VideoMaxDuration = 30
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1)
					key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
					bidRequest.AppendCreativeTemplateKey(key)
					keyId := key.Uint64()
					adxTemplateMap[keyId] = formattype
				case 600006:
					key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
					key.Title().AddRequiredCount(1)
					key.Desc().AddRequiredCount(1)
					key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
					bidRequest.AppendCreativeTemplateKey(key)
					keyId := key.Uint64()
					adxTemplateMap[keyId] = formattype
				}
			}
			bidRequest.AddMediaExtraData(AdxTemplateKey, adxTemplateMap)

		}

		if imp.Video != nil {
			bidRequest.VideoMinDuration = int32(imp.Video.Minduration)
			bidRequest.VideoMaxDuration = int32(imp.Video.Maxduration)

			bidRequest.SlotSize = append(bidRequest.SlotSize, ad_service.Size{
				Width:  int64(imp.Video.W),
				Height: int64(imp.Video.H),
			})
			key := creative_entity.NewCreativeTemplateKey()
			key.Video().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(imp.Video.W, imp.Video.H)
			if imp.Instl == 1 {
				key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(imp.Video.W, imp.Video.H)
				key.Title().AddRequiredCount(1)
			}
			bidRequest.AppendCreativeTemplateKey(key)
			//bidImp.AllowMimes = append(bidImp.AllowMimes, dict.MimeMp4)
		}

		if imp.Pmp != nil && len(imp.Pmp.Deals) > 0 {
			for _, deal := range imp.Pmp.Deals {
				bidRequest.SourceDeal = append(bidRequest.SourceDeal, ad_service.SourceDeal{
					DealId:   deal.Id,
					BidFloor: int64(deal.Bidfloor),
				})
			}
		}

		bidRequest.UseHttps = false
		if imp.Secure == 1 {
			bidRequest.UseHttps = true
		}
		break
	}
	return nil
}

func (s *SigmobTrafficBroker) parseApp(request *sigmob.SigmobBidRequest, bidRequest *ad_service.AdRequest) error {
	if request.App == nil {
		return nil
	}
	app := request.App
	bidRequest.App.AppBundle = app.Bundle
	bidRequest.App.AppName = app.Name
	bidRequest.App.AppVersion = app.Ver
	return nil
}

func (s *SigmobTrafficBroker) parseUser(request *sigmob.SigmobBidRequest, bidRequest *ad_service.AdRequest) error {
	if request.User == nil {
		return nil
	}
	switch request.User.Gender {
	case "M":
		bidRequest.UserGender = entity.UserGenderMan
	case "F":
		bidRequest.UserGender = entity.UserGenderWoman
	default:
		bidRequest.UserGender = entity.UserGenderUnknown
	}
	bidRequest.UserAge = request.User.Yob
	bidRequest.App.MediaInstalledAppIds = request.User.UserStrategy
	return nil
}

func (s *SigmobTrafficBroker) parseDevice(request *sigmob.SigmobBidRequest, bidRequest *ad_service.AdRequest) error {
	device := request.Device

	bidRequest.Device.UserAgent = device.Ua
	bidRequest.Device.RequestIp = device.Ip
	if bidRequest.Device.RequestIp == "" {
		bidRequest.Device.RequestIp = device.Ipv6
		bidRequest.Device.IsIp6 = true
	}
	bidRequest.Device.DeviceType = s.mappingDeviceType(device.DeviceType)
	bidRequest.Device.ConnectionType = s.mappingNetworkType(device.ConnectionType)
	bidRequest.Device.OperatorType = s.mappingCarrier(device.Carrier)
	if device.OS == 2 {
		bidRequest.Device.OsType = entity.OsTypeAndroid
	} else if device.OS == 1 {
		bidRequest.Device.OsType = entity.OsTypeIOS
	}
	//idfv
	bidRequest.Device.OsVersion = device.OSV
	bidRequest.Device.ImeiMd5 = device.Orimeimd5
	bidRequest.Device.Oaid = device.Oaid
	bidRequest.Device.IdfaMd5 = device.Ifamd5
	if len(device.Caid) > 0 {
		if len(device.Caid[0].Version) > 0 {
			bidRequest.Device.CaidRaw = device.Caid[0].Id
			bidRequest.Device.CaidVersion = device.Caid[0].Version
			bidRequest.Device.Caid = string_utils.ConcatString(device.Caid[0].Version, "_", device.Caid[0].Id)
		} else {
			bidRequest.Device.Caid = device.Caid[0].Id
			bidRequest.Device.CaidRaw = device.Caid[0].Id
		}
	}

	bidRequest.Device.Mac = device.Mac
	bidRequest.Device.MacMd5 = device.Macmd5
	bidRequest.Device.AndroidIdMd5 = device.Androididmd5
	bidRequest.Device.Brand = device.Make
	bidRequest.Device.Model = device.Model
	bidRequest.Device.BootMark = device.BootMark
	bidRequest.Device.UpdateMark = device.UpdateMark
	bidRequest.Device.DeviceInitTime = device.FileInitTime
	bidRequest.Device.DeviceStartupTime = device.DeviceStartSec
	bidRequest.Device.DeviceUpgradeTime = device.SystemUpdateSec
	bidRequest.Device.Paid = device.Paid
	if device.Geo != nil {
		bidRequest.Device.Lon = float64(device.Geo.Lon)
		bidRequest.Device.Lat = float64(device.Geo.Lat)
	}

	return nil
}

func (s *SigmobTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	zap.L().Debug("SigmobTrafficBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
	if request.IsDebug {
		zap.L().Info("[SigmobTrafficBroker] adCode:%+v").GetErrCodeMap())
	}

	if request.Response.NoCandidate() {
		return s.SendFallbackResponse(request, writer)
	}

	response := &sigmob.SigmobBidResponse{
		Id:      request.GetRequestId(),
		Seatbid: make([]*sigmob.SigmobSeatbid, 0),
		Nbr:     0,
	}
	sigmobSeatBid := &sigmob.SigmobSeatbid{}
	sigmobSeatBid.Bid = make([]*sigmob.SigmobBid, 0)

	for _, candidate := range request.Response.GetAdCandidateList() {
		if len(candidate.GetSelectedMaterialList()) == 0 {
			return err_code.ErrBrokerResponseMaterialEmpty
		}

		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		//zap.L().Info("[KuaiShouTrafficBroker] ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", candidate.GetDspResponseAd())))))
		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		sigmobBid := &sigmob.SigmobBid{}
		sigmobBid.Id = request.GetRequestId()
		sigmobBid.Impid = request.ImpressionId

		bidPrice := candidate.GetBidPrice()
		sigmobBid.Price = int32(bidPrice.Price)
		sigmobBid.Adid = strconv.Itoa(int(genericAd.GetAdId()))
		if creative.GetCreativeId() == 0 {
			sigmobBid.Crid = creative.GetCreativeKey()
		}

		sigmobBid.Crid = creative.GetCreativeKey()
		if len(sigmobBid.Crid) == 0 {
			sigmobBid.Crid = strconv.Itoa(int(creative.GetCreativeId()))
		}

		sigmobBid.Crid += "_" + time.Now().Format("0102150405")

		//sigmobBid.Wurl = []string{bid.MonitorData.WinNotice}
		sigmobBid.Landingpage = candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen)
		sigmobBid.Deeplinkurl = candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen)

		//	case dict.LandingTypeDownload:
		//		return 2
		sigmobBid.Interactiontype = 1
		if sigmobBid.Interactiontype == 1 && len(sigmobBid.Deeplinkurl) != 0 {
			sigmobBid.Interactiontype = 99
		}

		dpHeader := net_utils.GetURLHeadFromUrl(sigmobBid.Deeplinkurl)
		if strings.Contains(dpHeader, "market") {
			sigmobBid.MarketDeeplink = sigmobBid.Deeplinkurl
			sigmobBid.Deeplinkurl = ""
			sigmobBid.Interactiontype = 1
		}

		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			sigmobBid.Interactiontype = 2
		}

		/*
			3=image;
			6=only video；
			4=video + image for 激励视频/全屏视频请求(响应信息取 video对象下)
			5=video + htmlsrc
			7=video + htmlurl 创意类型

			for 原生
			8= 原生广告，读取广告响应信息中的native 对象
		*/
		sigmobBid.Attr = 8

		//1=激励视频/全屏视频；
		//2=开屏；
		//3=原生；
		//4=banner;
		//6=插屏
		instl, _ := request.GetMediaExtraInt64(sigInstl)
		reqAdxTemplateMap := request.GetMediaExtraDataWithDefault(AdxTemplateKey, emptyAdxTemplateMap).(map[uint64]int)
		switch instl {
		case 1:
			sigmobBid.Attr = 4
		case 2:
			sigmobBid.Attr = 3
		case 3:
			sigmobBid.Attr = 8
		case 4:
			sigmobBid.Attr = 3
		case 6:
			key1 := candidate.GetActiveCreativeTemplateKey()
			keyInt := key1.Uint64()
			sigmobBid.Attr = reqAdxTemplateMap[keyInt]
		}

		sigmobBid.Material = &sigmob.SigmobRespMaterial{}
		if instl == 3 {
			sigmobBid.Material.Native = s.getNativeObj(request, candidate)
		} else {
			s.setBannerObj(request, candidate, sigmobBid)
		}

		sigmobBid.Trackings = make([]*sigmob.SigmobRespTrackings, 0)
		impTracker := candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)

		sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
			Etype: "imp",
			Eurl:  impTracker,
		})

		clickMonitor := candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen)
		sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
			Etype: "click",
			Eurl:  clickMonitor,
		})

		dpMonitor := candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen)
		if len(genericAd.GetDeepLinkUrl()) > 0 {
			dpMonitor = append(dpMonitor, candidate.ReplaceUrlMacro(genericAd.GetDpSuccess(), traffic, trackingGen))
		}
		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
				Etype: "open_deeplink",
				Eurl:  dpMonitor,
			})
		}

		if len(genericAd.GetVideoStartUrlList()) > 0 {
			sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
				Etype: "start",
				Eurl:  candidate.ReplaceUrlMacroList(genericAd.GetVideoStartUrlList(), traffic, trackingGen),
			})
		}

		if len(genericAd.GetVideoCloseUrlList()) > 0 {
			sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
				Etype: "complete",
				Eurl:  candidate.ReplaceUrlMacroList(genericAd.GetVideoCloseUrlList(), traffic, trackingGen),
			})
		}

		if len(genericAd.GetAppDownloadStartedMonitorList()) > 0 {
			sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
				Etype: "download_start",
				Eurl:  candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadStartedMonitorList(), traffic, trackingGen),
			})
		}

		if len(genericAd.GetAppDownloadFinishedMonitorList()) > 0 {
			sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
				Etype: "download_finish",
				Eurl:  candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadFinishedMonitorList(), traffic, trackingGen),
			})
		}

		if len(genericAd.GetAppInstallStartMonitorList()) > 0 {
			sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
				Etype: "install_start",
				Eurl:  candidate.ReplaceUrlMacroList(genericAd.GetAppInstallStartMonitorList(), traffic, trackingGen),
			})
		}

		if len(genericAd.GetAppInstalledMonitorList()) > 0 {
			sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
				Etype: "install_finish",
				Eurl:  candidate.ReplaceUrlMacroList(genericAd.GetAppInstalledMonitorList(), traffic, trackingGen),
			})
		}

		if sigmobBid.Material != nil && sigmobBid.Material.Video != nil && sigmobBid.Material.Video.Dur > 0 {

			for _, delayTracker := range genericAd.GetDelayMonitorUrlList() {
				eventType := entity.GetVideoTrackingEvent(delayTracker.Delay, int(sigmobBid.Material.Video.Dur))
				switch eventType {
				case entity.KVideoTrackingEventStart:
					sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
						Etype: "start",
						Eurl:  []string{delayTracker.Url},
					})
				case entity.KVideoTrackingEventFirst: //四分之一
					sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
						Etype: "play_quarter",
						Eurl:  []string{delayTracker.Url},
					})
				case entity.KVideoTrackingEventMid: // 二分之一
					sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
						Etype: "play_two_quarters",
						Eurl:  []string{delayTracker.Url},
					})
				case entity.KVideoTrackingEventThird: // 四分之三
					sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
						Etype: "play_three_quarters",
						Eurl:  []string{delayTracker.Url},
					})
				case entity.KVideoTrackingEventComplete:
					sigmobBid.Trackings = append(sigmobBid.Trackings, &sigmob.SigmobRespTrackings{
						Etype: "complete",
						Eurl:  []string{delayTracker.Url},
					})
				}
			}
		}

		if genericAd.GetAppInfo() != nil {
			sigmobBid.Bundle = genericAd.GetAppInfo().PackageName
			sigmobBid.DownloadFileName = genericAd.GetAppInfo().AppName
			sigmobBid.Developer = genericAd.GetAppInfo().Develop
			sigmobBid.AppVer = genericAd.GetAppInfo().AppVersion
			sigmobBid.Privacy = genericAd.GetAppInfo().Privacy
			sigmobBid.PermissionsUrl = genericAd.GetAppInfo().Permission

			if sigmobBid.Material != nil && len(genericAd.GetAppInfo().Icon) > 0 {
				sigmobBid.Material.Icon = genericAd.GetAppInfo().Icon
			}
		}
		sigmobSeatBid.Bid = append(sigmobSeatBid.Bid, sigmobBid)
	}
	response.Seatbid = append(response.Seatbid, sigmobSeatBid)

	zap.L().Debug("SigmobTrafficBroker Build Response end. broker response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response)))))

	//buffer := buffer_pool.NewBufferWriter()
	//defer buffer.Release()
	//
	//_, err := easyjson.MarshalToWriter(response, buffer)
	//if err != nil {
	//	zap.L().Error("SigmobTrafficBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
	//	return err
	//}
	//
	//data := buffer.Get()
	//writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	//if _, err := writer.WriteWithStatus(200, data); err != nil {
	//	return err
	//}

	//if err := s.BuildHttpEasyJsonResponse(request, writer, response); err != nil {
	//	return err
	//}

	if err := s.BuildHttpSonicJsonResponse(request, writer, response); err != nil {
		return err
	}

	s.DoTrafficResponseSampleSonicJson(request, response)
	if request.IsDebug {
		resData, _ := sonic.Marshal(response)
		zap.L().Info("[SigmobTrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resData)))))
	}

	return nil
}

func (s *SigmobTrafficBroker) setBannerObj(request *ad_service.AdRequest, candidate *ad_service.AdCandidate, sigmobBid *sigmob.SigmobBid) {
	width := int32(0)
	height := int32(0)
	if len(request.SlotSize) > 0 {
		width = int32(request.SlotSize[0].Width)
		height = int32(request.SlotSize[0].Height)

	}
	switch sigmobBid.Attr {
	case 600001, 600002, 600003, 600004:
		width = 1280
		height = 720
	case 600005, 600006:
		width = 720
		height = 1280
	}

	for _, resource := range candidate.GetSelectedMaterialList() {
		switch resource.MaterialType {
		case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
			sigmobBidImg := &sigmob.SigmobImage{
				Url: resource.Url,
				W:   resource.Width,
				H:   resource.Height,
			}

			if width != 0 && height != 0 {
				sigmobBidImg.W = width
				sigmobBidImg.H = height
			}

			sigmobBid.Material.Img = sigmobBidImg

			if sigmobBid.Attr == 4 {
				if sigmobBid.Material.Video == nil {
					sigmobBid.Material.Video = &sigmob.SigmobVideo{}
				}
				sigmobBid.Material.Video.Img = sigmobBidImg
			}

		case entity.MaterialTypeIcon, entity.MaterialTypeLogo:
			sigmobBid.Material.Icon = resource.Url

		case entity.MaterialTypeVideo:
			if sigmobBid.Attr == 3 {
				sigmobBid.Attr = 6
			}

			sigmobBidVideo := &sigmob.SigmobVideo{
				Width:  resource.Width,
				Height: resource.Height,
				Dur:    int32(resource.Duration),
				Url:    resource.Url,
			}
			if width != 0 && height != 0 {
				sigmobBidVideo.Width = width
				sigmobBidVideo.Height = height
			}

			if sigmobBid.Material.Video == nil {
				sigmobBid.Material.Video = sigmobBidVideo
			} else {
				if sigmobBid.Material.Video.Img != nil {
					sigmobImg := &sigmob.SigmobImage{
						Url: sigmobBid.Material.Video.Img.Url,
						W:   sigmobBid.Material.Video.Img.W,
						H:   sigmobBid.Material.Video.Img.H,
					}
					sigmobBidVideo.Img = sigmobImg
					sigmobBid.Material.Video = sigmobBidVideo
					//sigmobBid.Material.Video.Img = sigmobImg
				} else {
					sigmobBid.Material.Video = sigmobBidVideo
				}
			}
		case entity.MaterialTypeTitle:
			sigmobBid.Material.Title = resource.Data
		case entity.MaterialTypeDesc:
			sigmobBid.Material.Desc = resource.Data
		}
	}
}

func (s *SigmobTrafficBroker) getNativeObj(request *ad_service.AdRequest, candidate *ad_service.AdCandidate) *sigmob.SigmobNative {
	sigmobNative := &sigmob.SigmobNative{
		Assets: []*sigmob.SigmobNativeAssets{},
	}

	width := int32(0)
	height := int32(0)
	if len(request.SlotSize) > 0 {
		width = int32(request.SlotSize[0].Width)
		height = int32(request.SlotSize[0].Height)

	}

	for _, resource := range candidate.GetSelectedMaterialList() {
		switch resource.MaterialType {
		case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
			imgRow := &sigmob.SigmobNativeAssets{
				Required: 0,
				Image: &sigmob.SigmobAdmNativeImg{
					Url: resource.Url,
					W:   resource.Width,
					H:   resource.Height,
				},
			}
			if width != 0 && height != 0 {
				imgRow.Image.W = width
				imgRow.Image.H = height
			}

			if assetId, ok := request.GetMediaExtraInt64(imageAssetId); ok {
				imgRow.Required = 1
				imgRow.Id = int(assetId)
				sigmobNative.Assets = append(sigmobNative.Assets, imgRow)
			}
		case entity.MaterialTypeIcon, entity.MaterialTypeLogo:
			imgRow := &sigmob.SigmobNativeAssets{
				Required: 0,
				Image: &sigmob.SigmobAdmNativeImg{
					Url: resource.Url,
					W:   resource.Width,
					H:   resource.Height,
				},
			}
			if assetId, ok := request.GetMediaExtraInt64(iconAssetId); ok {
				imgRow.Id = int(assetId)
				sigmobNative.Assets = append(sigmobNative.Assets, imgRow)
			}
			//if resource.MaterialType == entity.MaterialTypeIcon {
			//	admobject.Icon = resource.Url
			//}

		case entity.MaterialTypeVideo:
			//if sigmobBidAttr == 4 || sigmobBidAttr == 6 {
			//	admobject.Video = &sigmob.SigmobVideo{
			//		Width:  resource.Width,
			//		Height: resource.Height,
			//		Dur:    int32(resource.Duration),
			//		Url:    resource.Url,
			//		//Img: &sigmob.SigmobImage{
			//		//	Url: resource.Image.Url,
			//		//	W:   resource.Video.Width,
			//		//	H:   resource.Video.Height,
			//		//},
			//	}
			//	if width != 0 && height != 0 {
			//		admobject.Video.Width = width
			//		admobject.Video.Height = height
			//	}
			//}

			if assetId, ok := request.GetMediaExtraInt64(videoAssetId); ok {
				videoAsset := &sigmob.SigmobNativeAssets{
					Id:       int(assetId),
					Required: 1,
					Video: &sigmob.SigmobAdmNativeVideo{
						Url:      resource.Url,
						Duration: int32(resource.Duration),
						W:        resource.Width,
						H:        resource.Height,
					},
				}

				if width != 0 && height != 0 {
					videoAsset.Video.W = width
					videoAsset.Video.H = height
				}

				sigmobNative.Assets = append(sigmobNative.Assets, videoAsset)
			}
		case entity.MaterialTypeTitle:
			sigmobText := &sigmob.SigmobNativeAssets{
				Required: 0,
				Text: &sigmob.SigmobAdmNativeText{
					Context: resource.Data,
				},
			}
			if assetId, ok := request.GetMediaExtraInt64(titleAssetId); ok {
				sigmobText.Id = int(assetId)
				sigmobText.Required = 1
				sigmobNative.Assets = append(sigmobNative.Assets, sigmobText)
			}
			//admobject.Title = resource.Data
		case entity.MaterialTypeDesc:
			sigmobText := &sigmob.SigmobNativeAssets{
				Required: 0,
				Text: &sigmob.SigmobAdmNativeText{
					Context: resource.Data,
				},
			}
			if assetId, ok := request.GetMediaExtraInt64(descAssetId); ok {
				sigmobText.Id = int(assetId)
				sigmobText.Required = 1
				sigmobNative.Assets = append(sigmobNative.Assets, sigmobText)
			}
			//admobject.Desc = resource.Data
		}
	}

	return sigmobNative
}

func (s *SigmobTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	writer.WriteWithStatus(204, nil)
	return nil
}

func (s *SigmobTrafficBroker) mappingNetworkType(connectionType int) entity.ConnectionType {
	switch connectionType {
	case 101:
		return entity.ConnectionTypeNetEthernet
	case 100:
		return entity.ConnectionTypeWifi
	case 2:
		return entity.ConnectionType2G
	case 3:
		return entity.ConnectionType3G
	case 4:
		return entity.ConnectionType4G
	case 5:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func (s *SigmobTrafficBroker) mappingCarrier(carrier string) entity.OperatorType {
	switch carrier {
	case "China Mobile":
		return entity.OperatorTypeChinaMobile
	case "China Telecom":
		return entity.OperatorTypeChinaTelecom
	case "China Unicom":
		return entity.OperatorTypeChinaUnicom
	case "China Tietong":
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func (s *SigmobTrafficBroker) mappingDeviceType(deviceType int) entity.DeviceType {
	switch deviceType {
	case 1:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePad
	default:
		return entity.DeviceTypeUnknown
	}
}
