package wannianli_traffic_broker

import (
	"errors"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/wannianli_traffic_broker/wannianli_traffic_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
)

type WannianliTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        *zap.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewWannianliTrafficBroker(mediaId utils.ID) *WannianliTrafficBroker {
	return &WannianliTrafficBroker{
		log:     zap.L().With(zap.String("broker", "WannianliTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "${WIN_PRICE}",
			MediaClickDownXMacro: "$CLK_DOWN_X",
			MediaClickDownYMacro: "$CLK_DOWN_Y",
			MediaClickUpXMacro:   "$CLK_UP_X",
			MediaClickUpYMacro:   "$CLK_UP_Y",
		},
	}
}

func (w *WannianliTrafficBroker) GetMediaId() utils.ID {
	return w.mediaId
}

func (w *WannianliTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(w.SendResponse)
	request.Response.SetFallbackResponseBuilder(w.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = w.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = w.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &wannianli_traffic_entity.BidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		w.log.WithError(err).Error("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		w.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	request.SetMediaId(w.GetMediaId())
	request.SetRequestId(bidRequest.ID)
	if len(bidRequest.ID) == 0 {
		request.SetRequestId(request.GenerateRequestId())
	}

	w.parseTag(bidRequest, request)
	w.parseApp(bidRequest, request)
	w.parseUser(bidRequest, request)
	w.parseDevice(bidRequest, request)
	if err = w.parseImp(bidRequest, request); err != nil {
		w.log.WithError(err).WithField("request", bidRequest).Debug("BrokeRequest, parseImp failed")
		return err
	}

	return nil
}

func (w *WannianliTrafficBroker) parseTag(bidRequest *wannianli_traffic_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.Tag == nil {
		w.log.WithField("request", bidRequest).Info("tag is empty")
		return
	}
	adRequest.SetMediaSlotKey(bidRequest.Tag.TagID)
	adRequest.BidFloor = uint32(bidRequest.Tag.Bidfloor)
	adRequest.UseHttps = bidRequest.Tag.Secure == 1
	adRequest.BidType = entity.BidTypeCpm

	adRequest.SlotWidth = uint32(bidRequest.Tag.Width)
	adRequest.SlotHeight = uint32(bidRequest.Tag.Height)
}

func (w *WannianliTrafficBroker) parseApp(bidRequest *wannianli_traffic_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.App == nil {
		w.log.WithField("request", bidRequest).Debug("App is empty")
		return
	}
	adRequest.App.AppName = bidRequest.App.AppName
	adRequest.App.AppVersion = bidRequest.App.AppVersion
	adRequest.App.AppBundle = bidRequest.App.Bundle
}

func (w *WannianliTrafficBroker) parseUser(bidRequest *wannianli_traffic_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.User == nil {
		w.log.WithField("request", bidRequest).Debug("User is empty")
		return
	}
	adRequest.UserId = bidRequest.User.ID
	adRequest.UserAge = int32(bidRequest.User.Age)
	adRequest.UserGender = mappingGender(bidRequest.User.Gender)
}

func (w *WannianliTrafficBroker) parseDevice(bidRequest *wannianli_traffic_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.Device == nil {
		w.log.WithField("request", bidRequest).Debug("Device is empty")
		return
	}

	adRequest.Device = ad_service.AdRequestDevice{
		RequestIp:           bidRequest.Device.IP,
		UserAgent:           bidRequest.Device.UA,
		OsType:              mappingOsType(bidRequest.Device.OS),
		OsVersion:           bidRequest.Device.OSV,
		OperatorType:        mappingOperatorType(bidRequest.Device.Carrier),
		OperatorName:        mappingOperatorType(bidRequest.Device.Carrier).String(),
		ConnectionType:      mappingConnectionType(bidRequest.Device.Network),
		ScreenHeight:        int32(parseResolution(bidRequest.Device.Resolution, true)),
		ScreenWidth:         int32(parseResolution(bidRequest.Device.Resolution, false)),
		PPI:                 int32(bidRequest.Device.Ppi),
		AndroidId:           bidRequest.Device.Aid,
		Oaid:                bidRequest.Device.Oaid,
		Imei:                bidRequest.Device.Imei,
		Imsi:                bidRequest.Device.Imsi,
		AliAaid:             bidRequest.Device.Aaid, // 阿里aaid
		Idfa:                bidRequest.Device.Idfa,
		Idfv:                bidRequest.Device.Idfv,
		OpenUdid:            bidRequest.Device.OpenUDID,
		Mac:                 bidRequest.Device.MAC,
		ScreenOrientation:   mappingScreenOrientation(bidRequest.Device.Orientation),
		Vendor:              bidRequest.Device.Vendor,
		Brand:               bidRequest.Device.Vendor,
		Model:               bidRequest.Device.Model,
		BootMark:            bidRequest.Device.BootMark,
		UpdateMark:          bidRequest.Device.UpdateMark,
		AppStoreVersion:     bidRequest.Device.AppstoreVersion,
		VercodeHms:          bidRequest.Device.HMSVersion,
		DeviceInitTime:      bidRequest.Device.BirthTime,
		Paid:                bidRequest.Device.Paid,
		DeviceStartupTime:   bidRequest.Device.StartupTime,
		DeviceUpgradeTime:   bidRequest.Device.MDTime,
		DeviceName:          bidRequest.Device.DeviceName,
		HardwareMachineCode: bidRequest.Device.HardwareMachine,
		SystemTotalDisk:     bidRequest.Device.DiskTotal,
		SystemTotalMem:      bidRequest.Device.MemTotal,
		CountryCode:         bidRequest.Device.Nation,
		Language:            bidRequest.Device.Lan,
		TimeZone: func() int32 {
			zone, err := strconv.Atoi(bidRequest.Device.Zone)
			if err != nil {
				return 0
			}
			return int32(zone)
		}(),
		SystemTotalCpu:     bidRequest.Device.CPUNum,
		SystemCPUFrequency: bidRequest.Device.CPUFrequency,
		SystemBatteryPower: bidRequest.Device.BatteryPower,

		DeviceType: entity.DeviceTypeMobile,
		SdkVersion: strconv.Itoa(bidRequest.Device.APILevel),
		IsMobile:   true,
	}

	if bidRequest.Device.Geo != nil {
		adRequest.Device.Lat = bidRequest.Device.Geo.Lat
		adRequest.Device.Lon = bidRequest.Device.Geo.Lon
	}

	if len(bidRequest.Device.InstalledApps) != 0 {
		adRequest.App.InstalledApp = bidRequest.Device.InstalledApps
	}

	for _, item := range bidRequest.Device.Caids {
		caid := strings.Join([]string{item.Ver, item.ID}, "_")
		adRequest.Device.Caid = caid
		adRequest.Device.CaidRaw = item.ID
		adRequest.Device.CaidVersion = item.Ver
		adRequest.Device.Caids = append(adRequest.Device.Caids, caid)
	}

}

func (w *WannianliTrafficBroker) parseImp(bidRequest *wannianli_traffic_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	// 图文
	imgTemplate := creative_entity.NewCreativeTemplateKey()
	imgTemplate.Title().AddRequiredCount(1).SetOptional(true)
	imgTemplate.Desc().AddRequiredCount(1).SetOptional(true)
	imgTemplate.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	imgTemplate.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
	adRequest.AppendCreativeTemplateKey(imgTemplate)

	// 视频
	videoTemplate := creative_entity.NewCreativeTemplateKey()
	videoTemplate.Title().AddRequiredCount(1).SetOptional(true)
	videoTemplate.Desc().AddRequiredCount(1).SetOptional(true)
	videoTemplate.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
	videoTemplate.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	videoTemplate.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
	adRequest.AppendCreativeTemplateKey(videoTemplate)

	adRequest.ImpressionId = bidRequest.ID

	return nil
}

func (w *WannianliTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		w.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("WannianliTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return w.SendFallbackResponse(request, writer)
	}

	adResponse, err := w.buildResponse(request)
	if err != nil {
		w.log.WithError(err).Error("buildResponse err")
		return err
	}

	err = w.BuildHttpSonicJsonResponse(request, writer, adResponse)
	if err != nil {
		return err
	}

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(adResponse)
		w.log.Infof("SendResponse success, response: %s", responseStr)
	}

	return nil
}

func (w *WannianliTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		w.log.Infof("Build Fallback Response start. bid response: [%v]", request.Response)
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Encoding", "gzip")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	_, _ = writer.WriteWithStatus(204, nil)
	return nil
}

func (w *WannianliTrafficBroker) buildResponse(request *ad_service.AdRequest) (*wannianli_traffic_entity.BidResponse, error) {
	adResponse := &wannianli_traffic_entity.BidResponse{
		ID:     request.GetRequestId(),
		Status: 1000,
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		ad := &wannianli_traffic_entity.Ad{
			ID:    request.GetRequestId(),
			Crid:  creative.GetCreativeId().String(),
			Price: int(candidate.GetBidPrice().Price),
		}

		// 设置交互类型
		switch genericAd.GetLandingAction() {
		case entity.LandingTypeInWebView:
			ad.ActionType = 1
		case entity.LandingTypeDownload:
			ad.ActionType = 2
		default:
			ad.ActionType = 1
		}

		// 设置素材信息
		for _, material := range candidate.GetSelectedMaterialList() {
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				ad.Title = material.Data
			case entity.MaterialTypeDesc:
				ad.Desc = material.Data
			case entity.MaterialTypeIcon:
				ad.IconURL = material.Url
			case entity.MaterialTypeImage:
				ad.ImageURLs = append(ad.ImageURLs, candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen))
				ad.Width = int(material.Width)
				ad.Height = int(material.Height)
			case entity.MaterialTypeVideo:
				ad.VideoURL = candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen)
				ad.VideoDuration = int(material.Duration)
				ad.VideoSize = int(material.FileSize) * 1024 // 转换为字节
				ad.Width = int(material.Width)
				ad.Height = int(material.Height)
			}
		}

		// 设置落地页信息
		ad.ClickURL = candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen)
		ad.DeepLinkURL = genericAd.GetDeepLinkUrl()

		// 设置上报信息
		ad.ViewTrackURLs = candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
		ad.ClickTrackURLs = candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen)
		ad.DownloadStartTrackURLs = candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadStartedMonitorList(), traffic, trackingGen)
		ad.DownloadSuccessTrackURLs = candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadFinishedMonitorList(), traffic, trackingGen)
		ad.InstallStartTrackURLs = candidate.ReplaceUrlMacroList(genericAd.GetAppInstallStartMonitorList(), traffic, trackingGen)
		ad.InstallSuccessTrackURLs = candidate.ReplaceUrlMacroList(genericAd.GetAppInstalledMonitorList(), traffic, trackingGen)
		ad.ActiveURLs = candidate.ReplaceUrlMacroList(genericAd.GetAppOpenMonitorList(), traffic, trackingGen)

		// 设置视频事件追踪
		if len(genericAd.GetVideoStartUrlList()) > 0 {
			urls := candidate.ReplaceUrlMacroList(genericAd.GetVideoStartUrlList(), traffic, trackingGen)
			for _, url := range urls {
				ad.VideoEventTrackings = append(ad.VideoEventTrackings, &wannianli_traffic_entity.Event{
					Event: "start",
					URL:   url,
				})
			}
		}
		if len(genericAd.GetVideoCloseUrlList()) > 0 {
			urls := candidate.ReplaceUrlMacroList(genericAd.GetVideoCloseUrlList(), traffic, trackingGen)
			for _, url := range urls {
				ad.VideoEventTrackings = append(ad.VideoEventTrackings, &wannianli_traffic_entity.Event{
					Event: "complete",
					URL:   url,
				})
			}
		}

		// 设置深度链接事件追踪
		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			urls := candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen)
			for _, url := range urls {
				ad.DeepLinkTrackEvents = append(ad.DeepLinkTrackEvents, &wannianli_traffic_entity.Event{
					Event: "open_url_app",
					URL:   url,
				})
			}
		}
		if len(genericAd.GetDeepLinkFailedMonitorList()) > 0 {
			urls := candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkFailedMonitorList(), traffic, trackingGen)
			for _, url := range urls {
				ad.DeepLinkTrackEvents = append(ad.DeepLinkTrackEvents, &wannianli_traffic_entity.Event{
					Event: "dpl_failed",
					URL:   url,
				})
			}
		}

		// 设置应用推广信息
		if genericAd.GetAppInfo() != nil {
			ad.AppInfo = &wannianli_traffic_entity.AppInfo{
				AppID:          genericAd.GetAppInfo().AppID,
				AppName:        genericAd.GetAppInfo().AppName,
				AppVersion:     genericAd.GetAppInfo().AppVersion,
				Developer:      genericAd.GetAppInfo().Develop,
				PrivacyURL:     genericAd.GetAppInfo().Privacy,
				PermissionURL:  genericAd.GetAppInfo().Permission,
				PermissionDesc: genericAd.GetAppInfo().Permission,
				AppIntro:       genericAd.GetAppInfo().AppDesc,
				PackageName:    genericAd.GetAppInfo().PackageName,
				AppIconURL:     genericAd.GetAppInfo().Icon,
				Size:           genericAd.GetAppInfo().PackageSize * 1024, // 转换为字节
			}
		}

		adResponse.Data = []*wannianli_traffic_entity.Ad{ad}
		break
	}

	return adResponse, nil
}

func mappingGender(gender string) entity.UserGenderType {
	switch strings.ToUpper(gender) {
	case "M":
		return entity.UserGenderMan
	case "F":
		return entity.UserGenderWoman
	default:
		return entity.UserGenderUnknown
	}
}

func mappingOsType(os string) entity.OsType {
	switch strings.ToLower(os) {
	case "android":
		return entity.OsTypeAndroid
	case "ios":
		return entity.OsTypeIOS
	default:
		return entity.OsTypeOther
	}
}

func mappingScreenOrientation(orientation int) entity.ScreenOrientationType {
	switch orientation {
	case 0:
		return entity.ScreenOrientationTypePortrait
	case 1:
		return entity.ScreenOrientationTypeLandscape
	default:
		return entity.ScreenOrientationTypeUnknown
	}
}

func mappingOperatorType(operator int) entity.OperatorType {
	switch operator {
	case 1:
		return entity.OperatorTypeChinaMobile
	case 2:
		return entity.OperatorTypeChinaUnicom
	case 3:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType int) entity.ConnectionType {
	switch connectionType {
	case 1:
		return entity.ConnectionTypeNetEthernet
	case 2:
		return entity.ConnectionTypeWifi
	case 3:
		return entity.ConnectionTypeCellular
	case 4:
		return entity.ConnectionType2G
	case 5:
		return entity.ConnectionType3G
	case 6:
		return entity.ConnectionType4G
	case 7:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

// parseResolution 解析分辨率字符串，返回宽度或高度
func parseResolution(resolution string, isHeight bool) int {
	parts := strings.Split(resolution, "*")
	if len(parts) != 2 {
		return 0
	}

	width, _ := strconv.Atoi(parts[0])
	height, _ := strconv.Atoi(parts[1])

	if isHeight {
		return height
	}
	return width
}
