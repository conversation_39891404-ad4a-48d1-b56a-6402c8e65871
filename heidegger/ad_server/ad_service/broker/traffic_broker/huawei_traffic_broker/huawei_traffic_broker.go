package huawei_traffic_broker

import (
	"fmt"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/huawei_traffic_broker/huawei_broker_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"strconv"
	"strings"
	"time"
)

const (
	adxTemplateKey = "adxTemplate"
)

type HuaweiBroker struct {
	traffic_broker.TrafficBrokerBase

	mediaId    utils.ID
	MediaMacro *macro_builder.MediaMacro
}

func NewHuaweiBroker(mediaId utils.ID) *HuaweiBroker {
	return &HuaweiBroker{
		mediaId: mediaId,
		MediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro: "${AUCTION_PRICE}",
		},
	}
}

func (h *HuaweiBroker) GetMediaId() utils.ID {
	return h.mediaId
}

func (h *HuaweiBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(h.SendResponse)
	request.Response.SetFallbackResponseBuilder(h.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = h.MediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = h.MediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[HuaweiTrafficBroker] request body empty")
	}

	bidRequest := &huawei_broker_entity.BidRequest{}
	err := sonic.Unmarshal(body, &bidRequest)
	if err != nil {
		zap.L().Error("[HuaweiTrafficBroker] Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if request.IsDebug || bidRequest.Test == 1 {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[HuaweiTrafficBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	request.SetMediaId(h.GetMediaId())
	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}

	if err = h.parseApp(bidRequest, request); err != nil {
		zap.L().Debug("[HuaweiTrafficBroker] BrokeRequest, parseApp failed")
		return err
	}

	if err = h.parseDevice(bidRequest, request); err != nil {
		zap.L().Debug("[HuaweiTrafficBroker] BrokeRequest, parseDevice failed")
		return err
	}

	if err = h.parseImp(bidRequest, request); err != nil {
		zap.L().Debug("[HuaweiTrafficBroker] BrokeRequest, parseImp failed")
		return err
	}
	h.DoTrafficSample(request, body)

	return nil
}

func (h *HuaweiBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("HuaweiBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("HuaweiBroker")
	}

	if request.Response.NoCandidate() {
		return h.SendFallbackResponse(request, writer)
	}

	bidResponse, err := h.buildResponse(request)
	if err != nil {
		zap.L().Error("HuaweiBroker Error in buildResponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	if err = h.BuildHttpSonicJsonResponse(request, writer, bidResponse); err != nil {
		return err
	}

	h.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[HuaweiBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", responseStr)))))
	}

	return nil
}

func (h *HuaweiBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[HuaweiBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	_, err := writer.WriteWithStatus(204, nil)
	if err != nil {
		return err
	}
	return nil
}

func (h *HuaweiBroker) parseDevice(request *huawei_broker_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	adRequest.Device = ad_service.AdRequestDevice{}
	if request.Device == nil {
		return nil
	}

	adRequest.Device = ad_service.AdRequestDevice{
		OsType:            mappingOsType(request.Device.OS),
		OsVersion:         request.Device.OSV,
		DeviceType:        mappingDeviceType(request.Device.DeviceType),
		IsMobile:          request.Device.OS == "android" || request.Device.OS == "ios",
		VercodeAg:         request.Device.Uagever,
		AndroidIdMd5:      request.Device.Dpimd5,
		Oaid:              request.Device.OaId,
		Model:             request.Device.Model,
		RomVersion:        request.Device.Make,
		Language:          request.Device.Language,
		CountryCode:       request.Device.LocaleCountry,
		ScreenHeight:      int32(request.Device.H),
		ScreenWidth:       int32(request.Device.W),
		ScreenOrientation: entity.ScreenOrientationTypePortrait,
		UserAgent:         request.Device.UA,
		PPI:               int32(request.Device.PPI),
		RequestIp:         request.Device.IP,
		IsIp6:             strings.Count(request.Device.IP, ":") >= 2,
		ConnectionType:    mappingConnectionType(request.Device.Connectiontype),
		OperatorType:      mappingOperatorType(request.Device.Carrier),
	}

	if request.Device.Ext != nil {
		if len(request.Device.Ext.Brand) > 0 {
			adRequest.Device.Brand = request.Device.Ext.Brand[0]
		}
		adRequest.Device.UpdateMark = request.Device.Ext.UpdateMark
		adRequest.Device.BootMark = request.Device.Ext.BootMark
	}

	if request.Device.Geo != nil {
		adRequest.Device.CountryCode = request.Device.Geo.Country
		adRequest.Device.GpsTime = time.Now().UnixMilli() + int64(request.Device.Geo.Utcoffset*60*1000)
	}

	if len(adRequest.Device.RequestIp) < 1 {
		adRequest.Device.RequestIp = request.Device.IP
	}

	return nil
}

func (h *HuaweiBroker) parseApp(request *huawei_broker_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	if request.App == nil {
		return nil
	}

	adRequest.App = ad_service.AdRequestApp{
		AppBundle:       request.App.Bundle,
		AppName:         request.App.Name,
		AppVersion:      request.App.Ver,
		InstalledApp:    nil,
		InstalledAppIds: nil,
	}
	for _, industry := range request.App.Industry {
		if len(industry.ChildType) > 0 {
			adRequest.App.AdxAppCategory = append(adRequest.App.AdxAppCategory, industry.ChildType)
			continue
		}
		adRequest.App.AdxAppCategory = append(adRequest.App.AdxAppCategory, industry.ParentType)
	}

	if request.Ext != nil {
		adRequest.App.InstalledApp = request.Ext.Ail
	}

	return nil
}

func (h *HuaweiBroker) parseImp(request *huawei_broker_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	if len(request.Imp) == 0 {
		return fmt.Errorf("[HuaweiBroker] Impressions empty")
	}

	imp := request.Imp[0]

	adRequest.ImpressionId = adRequest.GetRequestId()
	adRequest.SetMediaSlotKey(imp.Id)
	adRequest.BidFloor = uint32(imp.Bidfloor)
	adRequest.UseHttps = true
	adRequest.BidType = entity.BidTypeCpm
	adRequest.SlotType = mappingSlotType(imp.Adtype)
	//if request.App != nil && request.App.Ext != nil {
	//	adRequest.SupportDeeplink = request.App.Ext.LinkAllowed == 1
	//}

	if imp.Pmp != nil && len(imp.Pmp.Deals) > 0 {
		for _, deal := range imp.Pmp.Deals {
			adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
				DealId:   deal.Id,
				BidFloor: int64(deal.Bidfloor),
			})
		}
	}

	adxTemplateMap := make(map[uint64]int)
	if imp.Native != nil {
		for _, asset := range imp.Native.Assets {
			templateKey := creative_entity.NewCreativeTemplateKey()
			switch asset.Templateid {
			case 1, 4, 9, 13, 18, 19, 29, 30, 34, 35:
				templateKey.Title().AddRequiredCount(1)
				templateKey.Desc().AddRequiredCount(1).SetOptional(true)
				templateKey.Image().AddRequiredCount(1)
				adRequest.AppendCreativeTemplateKey(templateKey)
				adxTemplateMap[templateKey.Uint64()] = asset.Templateid
				adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{Width: int64(asset.W), Height: int64(asset.H)})
			case 3:
				templateKey.Title().AddRequiredCount(1)
				templateKey.Desc().AddRequiredCount(1).SetOptional(true)
				templateKey.Image().AddRequiredCount(3)
				adRequest.AppendCreativeTemplateKey(templateKey)
				adxTemplateMap[templateKey.Uint64()] = asset.Templateid
				adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{Width: int64(asset.W), Height: int64(asset.H)})
			case 6, 15, 17, 21, 22, 23, 24, 25, 26, 27, 38, 49:
				templateKey.Title().AddRequiredCount(1)
				templateKey.Desc().AddRequiredCount(1).SetOptional(true)
				templateKey.Video().AddRequiredCount(1)
				adRequest.AppendCreativeTemplateKey(templateKey)
				adxTemplateMap[templateKey.Uint64()] = asset.Templateid
				adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{Width: int64(asset.W), Height: int64(asset.H)})
			case 28:
				templateKey.Title().AddRequiredCount(1).SetOptional(true)
				templateKey.Desc().AddRequiredCount(1).SetOptional(true)
				templateKey.Icon().AddRequiredCount(1)
				adRequest.AppendCreativeTemplateKey(templateKey)
				adxTemplateMap[templateKey.Uint64()] = asset.Templateid
				adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{Width: int64(asset.W), Height: int64(asset.H)})
			case 37:
				templateKey.Title().AddRequiredCount(1)
				templateKey.Desc().AddRequiredCount(1).SetOptional(true)
				templateKey.Image().AddRequiredCount(2)
				adRequest.AppendCreativeTemplateKey(templateKey)
				adxTemplateMap[templateKey.Uint64()] = asset.Templateid
				adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{Width: int64(asset.W), Height: int64(asset.H)})
			}
		}
	}

	if imp.Video != nil {
		templateKey := creative_entity.NewCreativeTemplateKey()
		templateKey.Desc().AddRequiredCount(1).SetOptional(true)
		templateKey.Title().AddRequiredCount(1).SetOptional(true)
		templateKey.Video().AddRequiredCount(1)
		templateKey.Video().AddRequiredSizeType(creative_entity.RT_SIZE_NULL)

		if imp.Video.Ext != nil && len(imp.Video.Ext.Durations) > 0 {
			adRequest.VideoMaxDuration = int32(imp.Video.Ext.Durations[0])
			if len(imp.Video.Ext.Durations) > 1 {
				adRequest.VideoMinDuration = int32(imp.Video.Ext.Durations[0]) / 1000
				adRequest.VideoMaxDuration = int32(imp.Video.Ext.Durations[1]) / 1000
			}
		}

		adRequest.AppendCreativeTemplateKey(templateKey)
		adxTemplateMap[templateKey.Uint64()] = 0
	}

	adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)
	return nil
}

func (h *HuaweiBroker) buildResponse(request *ad_service.AdRequest) (*huawei_broker_entity.BidResponse, error) {
	bidResponse := &huawei_broker_entity.BidResponse{
		Id:      request.GetRequestId(),
		Bidid:   request.GetRequestId(),
		Seatbid: make([]huawei_broker_entity.Seatbid, 0),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)
		bid := huawei_broker_entity.Bid{
			Id:       request.GetRequestId(),
			ImpId:    request.GetMediaSlotKey(),
			Price:    float64(candidate.GetBidPrice().Price),
			Lurl:     "", //竞胜失败URL。TODO 后续支持
			Crid:     strconv.Itoa(int(creative.GetCreativeId())),
			Language: request.Device.GetLanguage(),
		}
		if creative.GetCreativeId() == 0 {
			bid.Crid = creative.GetCreativeKey()
		}
		bid.Cid = bid.Crid
		if candidate.GetIndexDeal() != nil {
			bid.Dealid = candidate.GetIndexDeal().DealId
		}

		emptyAdxTemplateMap := make(map[uint64]int32)
		adxTemplateMap := request.GetMediaExtraDataWithDefault(adxTemplateKey, emptyAdxTemplateMap).(map[uint64]int32)

		bid.Nativersp = &huawei_broker_entity.NativeRsp{
			Templateid: int(adxTemplateMap[candidate.GetActiveCreativeTemplateKey().Uint64()]),
			Brand:      request.Device.Brand,
			Link: &huawei_broker_entity.Link{
				Interactiontype: mappingLandingType(genericAd),
				Url:             genericAd.GetLandingUrl(),
				Deeplink:        genericAd.GetDeepLinkUrl(),
				Clicktrackers:   candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
			},
			Imptrackers:   candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			Eventtrackers: nil,
		}

		if genericAd.GetAppInfo() != nil {
			bid.Nativersp.Link.App = &huawei_broker_entity.Apk{
				Name:      genericAd.GetAppInfo().AppName,
				IconUrl:   genericAd.GetAppInfo().Icon,
				AppSource: 2,
				Ver:       genericAd.GetAppInfo().AppVersion,
				Filesize:  genericAd.GetAppInfo().PackageSize * 1024,
				Url:       genericAd.GetDownloadUrl(),
				Securl:    genericAd.GetLandingUrl(),
			}
			bid.Nativersp.Link.Pkgname = genericAd.GetAppInfo().PackageName
			for _, permission := range genericAd.GetAppInfo().PermissionDesc {
				bid.Nativersp.Permissions = append(bid.Nativersp.Permissions, huawei_broker_entity.Permission{
					PermissionLabel: permission.PermissionLab,
					GroupDesc:       permission.PermissionDesc,
				})
			}
		}

		if len(genericAd.GetVideoStartUrlList()) > 0 {
			bid.Nativersp.Eventtrackers = append(bid.Nativersp.Eventtrackers, huawei_broker_entity.Tracking{
				EventType: "playStart",
				Url:       genericAd.GetVideoStartUrlList(),
			})
		}
		if len(genericAd.GetVideoCloseUrlList()) > 0 {
			bid.Nativersp.Eventtrackers = append(bid.Nativersp.Eventtrackers, huawei_broker_entity.Tracking{
				EventType: "playEnd",
				Url:       genericAd.GetVideoCloseUrlList(),
			})
		}

		for _, material := range candidate.GetSelectedMaterialList() {
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				if len(material.Data) > 0 {
					bid.Nativersp.Title = material.Data
				}
			case entity.MaterialTypeIcon:
				if len(material.Data) > 0 {
					img := huawei_broker_entity.Image{
						Url:  material.Url,
						W:    int(material.Width),
						H:    int(material.Height),
						Mime: "image/jpeg",
					}
					if strings.Contains(material.Url, ".png") {
						img.Mime = "image/png"
					}
					bid.Nativersp.Icon = append(bid.Nativersp.Icon, img)
				}
			case entity.MaterialTypeImage:
				if len(material.Url) > 0 {
					img := huawei_broker_entity.Image{
						Url:  material.Url,
						W:    int(material.Width),
						H:    int(material.Height),
						Mime: "image/jpeg",
					}
					if strings.Contains(material.Url, ".png") {
						img.Mime = "image/png"
					}
					bid.Nativersp.Images = append(bid.Nativersp.Images, img)
					bid.Nativersp.Brand = net_utils.GetDomainFromUrl(material.Url)
				}
			case entity.MaterialTypeVideo:
				if len(material.Url) > 0 {
					bid.Nativersp.Video = &huawei_broker_entity.ResVideo{
						Url:      material.Url,
						W:        int(material.Width),
						H:        int(material.Height),
						Duration: int(material.Duration * 1000),
						Mime:     "video/mp4",
					}
					bid.Nativersp.Brand = net_utils.GetDomainFromUrl(material.Url)
				}
			default:
				continue
			}
		}

		bidResponse.Seatbid = append(bidResponse.Seatbid, huawei_broker_entity.Seatbid{
			Bid:  []huawei_broker_entity.Bid{bid},
			Seat: strconv.Itoa(int(candidate.GetDspSlotId())) + "_" + candidate.GetDspSlotKey(),
		})
		break
	}

	return bidResponse, nil
}

func mappingSlotType(adType int) entity.SlotType {
	switch adType {
	case 1:
		return entity.SlotTypeVideoOpening
	case 3, 31:
		return entity.SlotTypeFeeds
	case 7:
		return entity.SlotTypeRewardVideo
	case 8:
		return entity.SlotTypeBanner
	case 12:
		return entity.SlotTypePopup
	case 60:
		return entity.SlotTypeVideo
	default:
		return entity.SlotTypeUnknown
	}
}

func mappingOperatorType(carrier string) entity.OperatorType {
	switch carrier {
	case "China Mobile":
		return entity.OperatorTypeChinaMobile
	case "China Unicom":
		return entity.OperatorTypeChinaUnicom
	case "China Telecom":
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType int) entity.ConnectionType {
	switch connectionType {
	case 1:
		return entity.ConnectionTypeNetEthernet
	case 2:
		return entity.ConnectionTypeWifi
	case 3:
		return entity.ConnectionTypeCellular
	case 4:
		return entity.ConnectionType2G
	case 5:
		return entity.ConnectionType3G
	case 6:
		return entity.ConnectionType4G
	case 7:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingOsType(osType string) entity.OsType {
	switch osType {
	case "android":
		return entity.OsTypeAndroid
	case "ios":
		return entity.OsTypeIOS
	case "windows":
		return entity.OsTypeWindows
	case "ott":
		return entity.OsTypeOtt
	default:
		return entity.OsTypeUnknown
	}
}

func mappingDeviceType(deviceType int) entity.DeviceType {
	switch deviceType {
	case 4:
		return entity.DeviceTypeMobile
	case 5:
		return entity.DeviceTypePad
	case 6:
		return entity.DeviceTypePc
	}
	return entity.DeviceTypeUnknown
}

func mappingLandingType(genericAd entity.GenericAd) int {
	if genericAd.GetLandingAction() == entity.LandingTypeDownload {
		return 2
	}
	if genericAd.GetLandingAction() == entity.LandingTypeDeepLink {
		if net_utils.IsQuickApp(genericAd.GetDeepLinkUrl()) {
			return 4
		}
		return 3
	}

	if genericAd.GetLandingAction() == entity.LandingTypeInWebView && genericAd.GetLandingUrl() != "" {
		return 1
	}

	return 0
}
