package wifi_feisuo_broker

import (
	"fmt"
	"strconv"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/wifi_feisuo_broker/wifi_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
)

type (
	WifiFeisuoBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId    utils.ID
		MediaMacro *macro_builder.MediaMacro
	}
)

func NewWifiFeisuoBroker(mediaId utils.ID) *WifiFeisuoBroker {
	return &WifiFeisuoBroker{
		mediaId: mediaId,
		MediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      macro_builder.MediaCostMacro, //一价
			MediaClickUpXMacro:   "__UP_X__",
			MediaClickUpYMacro:   "__UP_Y__",
			MediaClickDownXMacro: "__DOWN_X__",
			MediaClickDownYMacro: "__DOWN_Y__",
		},
	}
}

func (mb *WifiFeisuoBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *WifiFeisuoBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	request.AdRequestMedia.MediaMacro = mb.MediaMacro
	request.AdRequestMedia.WinPriceMacro = mb.MediaMacro.MediaPriceMacro
	return mb.ParseAdRequest(request)
}

func (mb *WifiFeisuoBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[WifiFeisuoBroker]request body empty")
	}

	bidRequest := &wifi_proto.BidRequest{}
	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[WifiFeisuoBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if err = mb.buildRequest(request, bidRequest); err != nil {
		zap.L().Debug("[WifiFeisuoBroker]BrokeRequest, parseUser failed")
		return err
	}
	mb.DoTrafficSamplePb(request, bidRequest)
	return nil
}
func (mb *WifiFeisuoBroker) buildRequest(request *ad_service.AdRequest, bidRequest *wifi_proto.BidRequest) error {
	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[WifiFeisuoBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	request.SetRequestId(bidRequest.RequestId)
	if len(bidRequest.RequestId) < 1 {
		request.SetRequestId(utils.NewUUID())
	}
	request.SetMediaId(mb.mediaId)

	if err := mb.parseImp(bidRequest, request); err != nil {
		zap.L().Debug("[WifiFeisuoBroker] BrokeRequest, parseImp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, request); err != nil {
		zap.L().Debug("[WifiFeisuoBroker]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseUser(bidRequest, request); err != nil {
		zap.L().Debug("[WifiFeisuoBroker]BrokeRequest, parseUser failed")
		return err
	}

	if err := mb.parseApp(bidRequest, request); err != nil {
		zap.L().Debug("[WifiFeisuoBroker]BrokeRequest, parseApp failed")
		return err
	}
	return nil
}

func (mb *WifiFeisuoBroker) parseImp(mediaBidRequest *wifi_proto.BidRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Slot == nil {
		zap.L().Debug("[WifiFeisuoBroker]parseImp, vendor: , imp nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return fmt.Errorf("parseImp, imp nil")
	}
	item := mediaBidRequest.Slot

	bidReq.SetMediaSlotKey(item.Id)
	bidReq.BidFloor = item.BidFloor

	for _, id := range item.MaterialTypes {
		bidReq.AdxTemplateId = append(bidReq.AdxTemplateId, strconv.FormatInt(int64(id), 10))
		switch id {
		case wifi_proto.MaterialType_MATERIAL_TYPE_HORIZONTAL_PIC:
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Icon().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
		case wifi_proto.MaterialType_MATERIAL_TYPE_VERTICAL_PIC:
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Icon().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			bidReq.AppendCreativeTemplateKey(key)
		case wifi_proto.MaterialType_MATERIAL_TYPE_HORIZONTAL_VIDEO:
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Icon().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL).SetOptional(true)
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
		case wifi_proto.MaterialType_MATERIAL_TYPE_VERTICAL_VIDEO:
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Icon().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL).SetOptional(true)
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			bidReq.AppendCreativeTemplateKey(key)
		case wifi_proto.MaterialType_MATERIAL_TYPE_MULTI_PICS:
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Icon().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			bidReq.AppendCreativeTemplateKey(key)
		}
	}
	if len(bidReq.GetCreativeTemplateKeyList()) < 1 { //默认图片
		key := creative_entity.NewCreativeTemplateKey()
		key.Title().AddRequiredCount(1).SetOptional(true)
		key.Desc().AddRequiredCount(1).SetOptional(true)
		key.Icon().AddRequiredCount(1).SetOptional(true)
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		bidReq.AppendCreativeTemplateKey(key)
	}

	return nil
}

func (mb *WifiFeisuoBroker) parseDevice(mediaBidRequest *wifi_proto.BidRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Device == nil {
		zap.L().Error("parseDevice, device nil", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", mediaBidRequest)))))
		return fmt.Errorf("parseDevice failed")
	}

	if mediaBidRequest.Network != nil {
		bidReq.Device.RequestIp = mediaBidRequest.Network.Ipv4
		if len(bidReq.Device.RequestIp) == 0 {
			bidReq.Device.RequestIp = mediaBidRequest.Network.Ipv6
			bidReq.Device.IsIp6 = true
		}
		bidReq.Device.OperatorType = func() entity.OperatorType {
			switch mediaBidRequest.Network.Carrier {
			case wifi_proto.Carrier_CARRIER_MOBILE:
				return entity.OperatorTypeChinaMobile
			case wifi_proto.Carrier_CARRIER_UNICOM:
				return entity.OperatorTypeChinaUnicom
			case wifi_proto.Carrier_CARRIER_TELECOM:
				return entity.OperatorTypeChinaTelecom
			default:
				return entity.OperatorTypeUnknown
			}
		}()
		bidReq.Device.ConnectionType = func() entity.ConnectionType {
			switch mediaBidRequest.Network.Type {
			case wifi_proto.NetType_NET_TYPE_ETHERNET:
				return entity.ConnectionTypeNetEthernet
			case wifi_proto.NetType_NET_TYPE_WIFI:
				return entity.ConnectionTypeWifi
			case wifi_proto.NetType_NET_TYPE_CELLULAR_UNKNOWN:
				return entity.ConnectionTypeCellular
			case wifi_proto.NetType_NET_TYPE_CELLULAR_2G:
				return entity.ConnectionType2G
			case wifi_proto.NetType_NET_TYPE_CELLULAR_3G:
				return entity.ConnectionType3G
			case wifi_proto.NetType_NET_TYPE_CELLULAR_4G:
				return entity.ConnectionType4G
			case wifi_proto.NetType_NET_TYPE_CELLULAR_5G:
				return entity.ConnectionType5G
			default:
				return entity.ConnectionTypeUnknown
			}
		}()
	}

	device := mediaBidRequest.Device

	bidReq.Device.UserAgent = device.UserAgent
	bidReq.Device.OsType = func() entity.OsType {
		switch device.Os {
		case wifi_proto.OS_OS_ANDROID:
			return entity.OsTypeAndroid
		case wifi_proto.OS_OS_IOS:
			return entity.OsTypeIOS
		default:
			return entity.OsTypeUnknown
		}
	}()
	bidReq.Device.OsVersion = device.OsVersion
	bidReq.Device.Model = device.Model
	bidReq.Device.Vendor = device.Vendor
	bidReq.Device.DeviceType = func() entity.DeviceType {
		switch device.Type {
		case wifi_proto.DeviceType_DEVICE_TYPE_PHONE:
			return entity.DeviceTypeMobile
		case wifi_proto.DeviceType_DEVICE_TYPE_PC:
			return entity.DeviceTypePc
		case wifi_proto.DeviceType_DEVICE_TYPE_TV:
			return entity.DeviceTypeOtt
		default:
			return entity.DeviceTypeUnknown
		}
	}()

	if device.DeviceId != nil {
		bidReq.Device.Imei = device.DeviceId.Imei
		bidReq.Device.Idfa = device.DeviceId.Idfa
		bidReq.Device.Oaid = device.DeviceId.Oaid
		bidReq.Device.Mac = device.DeviceId.Mac
		bidReq.Device.AndroidId = device.DeviceId.AndroidId
		bidReq.Device.Paid = device.DeviceId.Paid
		for _, v := range device.DeviceId.Caids {
			bidReq.Device.Caid = string_utils.ConcatString(v.Version, "_", v.Caid)
			bidReq.Device.Caids = append(bidReq.Device.Caids, bidReq.Device.Caid)
		}
	}

	bidReq.Device.ScreenHeight = int32(device.ScreenWidth)
	bidReq.Device.ScreenWidth = int32(device.ScreenHeight)
	bidReq.Device.DPI = int32(device.ScreenDpi)
	bidReq.Device.PPI = int32(device.ScreenPpi)

	if mediaBidRequest.Geo != nil {
		bidReq.Device.Lat = float64(mediaBidRequest.Geo.Latitude)
		bidReq.Device.Lon = float64(mediaBidRequest.Geo.Longitude)
	}

	bidReq.Device.BootMark = device.BootMark
	bidReq.Device.UpdateMark = device.UpdateMark
	bidReq.Device.DeviceStartupTime = device.BootTime
	bidReq.Device.DeviceUpgradeTime = device.SysUpdateTime
	bidReq.Device.DeviceInitTime = device.BirthTime
	bidReq.Device.VercodeAg = device.HwAgVerCode
	bidReq.Device.VercodeHms = device.HwHmsVerCode

	return nil

}

func (mb *WifiFeisuoBroker) parseApp(mediaBidRequest *wifi_proto.BidRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.SourceApp == nil {
		zap.L().Debug("parseApp, vendor: , app nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	app := mediaBidRequest.SourceApp
	bidReq.App.AppName = app.Name
	bidReq.App.AppBundle = app.PkgName

	return nil
}

func (mb *WifiFeisuoBroker) parseUser(mediaBidRequest *wifi_proto.BidRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.User == nil {
		zap.L().Debug("parseUser, vendor: , user nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	user := mediaBidRequest.User

	switch user.Gender {
	case wifi_proto.Gender_MALE:
		bidReq.UserGender = entity.UserGenderMan
	case wifi_proto.Gender_FEMALE:
		bidReq.UserGender = entity.UserGenderWoman
	}

	if len(user.InstalledApps) > 0 {
		bidReq.App.MediaInstalledAppIds = user.InstalledApps
	}
	return nil
}

func (mb *WifiFeisuoBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("WifiFeisuoBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("WifiFeisuoBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}
	bidResponse, err := mb.buildResponse(request)
	if err != nil {
		zap.L().Error("WifiFeisuoBroker Error in buildResponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}
	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()

	buffer.EnsureSize(bidResponse.Size())
	_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		zap.L().Error("WifiFeisuoBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/octet-stream")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	mb.DoTrafficResponseSamplePb(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[WifiFeisuoBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", responseStr)))))
	}

	return nil
}

func (mb *WifiFeisuoBroker) buildResponse(request *ad_service.AdRequest) (*wifi_proto.BidResponse, error) {
	bidResponse := &wifi_proto.BidResponse{
		RequestId: request.GetRequestId(),
		SlotId:    request.GetMediaSlotKey(),
		Ads:       []*wifi_proto.Ad{},
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		creative := candidate.GetCreative()
		if creative == nil {
			continue
		}
		bid := &wifi_proto.Ad{
			Material: &wifi_proto.Material{
				IdeaId: creative.GetCreativeId().String(),
				//UserId: candidate.GetDspAdId(),
				LandingUrl:  candidate.GetMacroReplaceLandingUrl(),
				DeeplinkUrl: candidate.GetMacroReplaceDeepLinkUrl(),
				Images:      make([]*wifi_proto.Image, 0),
				App:         &wifi_proto.TargetApp{},
			},
			TrackingList: &wifi_proto.TrackingList{
				//WinUrls: candidate.GetMacroReplaceImpressionMonitorList(),
				ImpUrls:           candidate.GetMacroReplaceImpressionMonitorList(),
				ClickUrls:         candidate.GetMacroReplaceClickMonitorList(),
				DeeplinkClickUrls: candidate.GetMacroReplaceDeepLinkMonitorList(),
				VideoStartUrls:    candidate.GetVideoStartUrlList(),
				VideoEndUrls:      candidate.GetVideoCloseUrlList(),
			},
			Cpm: uint32(candidate.GetBidPrice().Price),
		}

		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				bid.Material.Images = append(bid.Material.Images, &wifi_proto.Image{
					Url:    rsc.Url,
					Width:  uint32(rsc.Width),
					Height: uint32(rsc.Height),
				})
			case entity.MaterialTypeIcon:
				bid.Material.App.Icon = rsc.Url
			case entity.MaterialTypeLogo:
				bid.Material.App.Icon = rsc.Url
			case entity.MaterialTypeTitle:
				bid.Material.Title = rsc.Data
			case entity.MaterialTypeDesc:
				bid.Material.Desc = rsc.Data
			case entity.MaterialTypeVideo:
				bid.Material.Video = &wifi_proto.Video{
					Url:      rsc.Url,
					Duration: uint32(rsc.Duration),
					Size_:    uint32(rsc.FileSize),
					Width:    uint32(rsc.Width),
					Height:   uint32(rsc.Height),
				}
			default:
				continue
			}
		}

		if request.SlotType == entity.SlotTypeOpening {
			bid.CacheDuration = 60
		}

		if candidate.GetLandingAction() == entity.LandingTypeDownload {
			bid.Material.DownloadUrl = candidate.GetMacroReplaceLandingUrl()
			bid.TrackingList.DownloadStartUrls = candidate.GetMacroReplaceAppDownloadStartedMonitorList()
			bid.TrackingList.DownloadEndUrls = candidate.GetMacroReplaceAppDownloadFinishedMonitorList()
			bid.TrackingList.InstallEndUrls = candidate.GetMacroReplaceAppInstalledMonitorList()
		}

		if candidate.GetAppInfo() != nil {
			if len(candidate.GetAppInfo().Icon) > 0 {
				bid.Material.App.Icon = candidate.GetAppInfo().Icon
			}
			bid.Material.App.Name = candidate.GetAppInfo().AppName
			bid.Material.App.PkgName = candidate.GetAppInfo().PackageName
			bid.Material.App.Id = candidate.GetAppInfo().AppID
			bid.Material.App.VersionName = candidate.GetAppInfo().AppVersion
			bid.Material.App.Size_ = uint32(candidate.GetAppInfo().PackageSize)
			bid.Material.App.Privacy = candidate.GetAppInfo().Privacy
			bid.Material.App.PermissionUrl = candidate.GetAppInfo().Permission
			bid.Material.App.IntroductionUrl = candidate.GetAppInfo().AppDescURL
			bid.Material.App.Developer = candidate.GetAppInfo().Develop
			bid.Material.App.Permissions = []*wifi_proto.Permission{}
			for _, desc := range candidate.GetAppInfo().PermissionDesc {
				bid.Material.App.Permissions = append(bid.Material.App.Permissions, &wifi_proto.Permission{
					Name: desc.PermissionLab,
					Desc: desc.PermissionDesc,
				})
			}

			if candidate.GetAppInfo().WechatExt != nil {
				bid.Material.MiniprogramAppid = candidate.GetAppInfo().WechatExt.ProgramId
				bid.Material.MiniprogramPagePath = candidate.GetAppInfo().WechatExt.ProgramPath
			}
		}
		bidResponse.Ads = append(bidResponse.Ads, bid)
	}
	return bidResponse, nil
}

func (mb *WifiFeisuoBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[WifiFeisuoBroker] Build FallbackResponse start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/octet-stream")
	writer.WriteWithStatus(204, nil)
	return nil
}
