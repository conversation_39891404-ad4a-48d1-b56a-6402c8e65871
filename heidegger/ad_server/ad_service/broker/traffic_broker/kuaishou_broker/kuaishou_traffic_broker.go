package kuaishou_broker

import (
	"encoding/json"
	"errors"
	"net/url"
	"strconv"
	"strings"

	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/kuaishou_broker/kuaishou_broker_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

const (
	AdTypeKey          = "adType"
	AdTypeMapKey       = "adTypeMap"
	CreativeTypeMapKey = "creativeTypeListMap"
)

var (
	emptyCreativeTypeMap map[kuaishou_broker_proto.Adm_CreativeType]string  = make(map[kuaishou_broker_proto.Adm_CreativeType]string)
	emptyAdTypeMap       map[kuaishou_broker_proto.AdTypeEnum_AdType]string = make(map[kuaishou_broker_proto.AdTypeEnum_AdType]string)
)

// https://ssp-debug.test.gifshow.com/adx-test-platform/index.html#/docs/rtb/rtb-v1.48.html
// NOTE: 快手流量会混发,所以APK和广告位不能一一对应
var kuaishouSlotBundleMap = map[string]string{
	// 广告位：快手发现页信息流第五位
	"3543010": "com.smile.gifmaker", // 安卓
	"3543040": "com.jiangjia.gif",   // ios
	// 广告位：快手主app开屏广告
	"3543016": "com.smile.gifmaker", // 安卓
	"3543033": "com.smile.gifmaker", // 安卓
	"3543046": "com.jiangjia.gif",   // ios
	"3543063": "com.jiangjia.gif",   // ios
	// 广告位：快手极速版开屏广告
	"3543017": "com.kuaishou.nebula", // 安卓
	"3543034": "com.kuaishou.nebula", // 安卓
	"3543047": "com.kuaishou.nebula", // ios
	"3543064": "com.kuaishou.nebula", // ios
	// 广告位：快手主app发现页信息流滑滑板样式广告
	"3543018": "com.smile.gifmaker", // 安卓
	"3543048": "com.jiangjia.gif",   // ios
	// 广告位：快手极速版app发现页信息流滑滑板样式广告
	"3543019": "com.kuaishou.nebula", // 安卓
	"3543049": "com.kuaishou.nebula", // ios
	// 广告位：极速版激励视频
	"3543025": "com.kuaishou.nebula", // 安卓
	"3543055": "com.kuaishou.nebula", // ios
	// 广告位：主app快享便利贴
	"3543028": "com.smile.gifmaker", // 安卓
	"3543058": "com.jiangjia.gif",   // ios
	// 广告位：极速版快享便利贴
	"3543027": "com.kuaishou.nebula", // 安卓
	"3543057": "com.kuaishou.nebula", // ios
	// 广告位：快手主APP精选页
	"3543015": "com.smile.gifmaker", // 安卓
	"3543044": "com.jiangjia.gif",   // ios
	// 广告位：极速版信息流品牌
	"3543031": "com.kuaishou.nebula", // 安卓
	"3543061": "com.kuaishou.nebula", // ios
	// 广告位：快手主app搜索广告
	"3543035": "com.smile.gifmaker", // 安卓
	"3543065": "com.jiangjia.gif",   // ios
	// 广告位：快手极速版搜索广告
	"3543036": "com.kuaishou.nebula", // 安卓
	"3543066": "com.kuaishou.nebula", // ios
}

type KuaiShouTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	mediaId utils.ID

	log        *zap.Logger
	mediaMacro *macro_builder.MediaMacro
}

func NewKuaiShouTrafficBroker(mediaId utils.ID) *KuaiShouTrafficBroker {
	return &KuaiShouTrafficBroker{
		mediaId: mediaId,
		log:     zap.L().With(zap.String("broker", "KuaiShouTrafficBroker")),
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro: "${WIN_PRICE}",
		},
	}
}

func (k *KuaiShouTrafficBroker) GetMediaId() utils.ID {
	return k.mediaId
}

func (k *KuaiShouTrafficBroker) Do(request *ad_service.AdRequest) error {
	return k.ParseAdRequest(request)
}

func (k *KuaiShouTrafficBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(k.SendResponse)
	request.Response.SetFallbackResponseBuilder(k.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = k.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = k.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("[KuaiShouTrafficBroker] request body empty")
	}

	ksRequest := &kuaishou_broker_proto.BidRequest{}
	err := proto.Unmarshal(body, ksRequest)
	if err != nil {
		k.log.WithError(err).Error("Request body unmarshal failed")
		return err_code.ErrRawRequest.Wrap(err)
	}

	if request.IsDebug {
		reqBody, _ := json.Marshal(ksRequest)
		k.log.WithField("request", string(reqBody)).Info("Parse Request start")
	}

	if ksRequest.GetImp() == nil || len(ksRequest.GetImp()) <= 0 {
		k.log.Debug("KuaiShouTrafficBroker request invalid, no imp")
		return err_code.ErrInvalidImpression
	}

	request.SetRequestId(ksRequest.GetRequestId())
	if len(request.GetRequestId()) < 1 {
		request.SetRequestId(utils.NewUUID())
	}
	request.SetMediaId(k.mediaId)

	if err := k.parseImp(ksRequest, request); err != nil {
		k.log.Debug("parseImp failed")
		return err
	}

	if err := k.parseDevice(ksRequest, request); err != nil {
		k.log.Debug("parseDevice failed")
		return err
	}

	if v, ok := kuaishouSlotBundleMap[request.GetMediaSlotKey()]; ok {
		if len(request.App.AppBundle) == 0 {
			request.App.AppBundle = v
		}
		request.App.AppName = "快手"
		if request.App.AppBundle == "com.kuaishou.nebula" {
			request.App.AppName = "快手极速版"
		}
	}

	k.DoTrafficSamplePb(request, ksRequest)

	return nil
}

func (k *KuaiShouTrafficBroker) parseImp(ksRequest *kuaishou_broker_proto.BidRequest, request *ad_service.AdRequest) error {

	if ksRequest.Imp == nil {
		return errors.New("parseImp imp nil")
	}

	imp0 := ksRequest.Imp[0]
	request.ImpressionId = imp0.GetImpId()
	request.SetMediaSlotKey(imp0.GetTagId())

	request.CpcBidFloor = uint32(imp0.GetBidFloor() * 100)
	request.BidFloor = uint32(imp0.GetCpmBidFloor() * 100)
	if imp0.GetSecure() == 1 {
		request.UseHttps = true
	}

	request.SlotType = entity.SlotTypeUnknown

	creativeTypeMap := make(map[kuaishou_broker_proto.Adm_CreativeType]string)
	adTypeMap := make(map[kuaishou_broker_proto.AdTypeEnum_AdType]string)

	for _, imp := range ksRequest.Imp {
		var width, height uint32
		if imp.Native != nil && imp.Native.Size_ != nil {
			width, height = imp.Native.GetSize_().GetWidth(), imp.Native.GetSize_().GetHeight()
			request.SlotSize = append(request.SlotSize, ad_service.Size{
				Width:  int64(width),
				Height: int64(height)})
			request.SlotWidth, request.SlotHeight = width, height
		}

		if imp.GetPmp() != nil && len(imp.GetPmp().Deal) > 0 {
			for _, reqDeal := range imp.GetPmp().Deal {
				sourceDeal := ad_service.SourceDeal{DealId: reqDeal.GetDealId(), BidFloor: int64(reqDeal.GetBidFloor() * 100)}
				request.SourceDeal = append(request.SourceDeal, sourceDeal)
			}
		}

		switch imp.GetAdStyle() {
		case 1:
			request.SlotType = entity.SlotTypeFeeds
		case 2:
			request.SlotType = entity.SlotTypeRewardVideo
		case 3:
			request.SlotType = entity.SlotTypePopup
		case 4:
			request.SlotType = entity.SlotTypeOpening
		case 5:
			request.SlotType = entity.SlotTypeBanner
		}

		// TODO: 开屏使用AdType, 非开屏使用CreativeType
		// adxTemplateMap := make(map[uint64]string)
		for _, adType := range imp.GetAdType() {
			impIdAdTypeKey := imp.ImpId + AdTypeKey

			request.AddMediaExtraInt64(impIdAdTypeKey, int64(adType))
			request.AddMediaExtraInt64(AdTypeKey, int64(adType))

			adTypeMap[adType] = imp.ImpId
		}

		request.AdxTemplateId = make([]string, 0)
		var slotWidth, slotHeight = int(width), int(height)
		//todo template
		for _, cType := range imp.GetCreativeType() {
			creativeTypeMap[cType] = imp.ImpId
			switch cType {
			//case kuaishou_broker_proto.Adm_NO_TYPE:
			//	bidReqImp.AllowMimes = append(bidReqImp.AllowMimes, dict.MimeJpg, dict.MimePng, dict.MimeGif,
			//		dict.MimeJpeg, dict.MimeSwf, dict.MimeMp4, dict.MimeFlv, dict.MimeText, dict.MimeFeed, dict.MimeVideoFeed)
			//case kuaishou_broker_proto.Adm_TEXT:
			//	bidReqImp.AllowMimes = append(bidReqImp.AllowMimes, dict.MimeText, dict.MimeFeed)
			//case kuaishou_broker_proto.Adm_IMAGE:
			//	bidReqImp.AllowMimes = append(bidReqImp.AllowMimes, dict.MimeJpg, dict.MimePng, dict.MimeGif, dict.MimeJpeg, dict.MimeFeed)

			case kuaishou_broker_proto.Adm_IMAGE:
				//key := creative_entity.NewCreativeTemplateKey()
				request.AdxTemplateId = append(request.AdxTemplateId, kuaishou_broker_proto.Adm_IMAGE.String())
				//key.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(slotWidth, slotHeight)
				//request.AppendCreativeTemplateKey(key)
			case kuaishou_broker_proto.Adm_TEXT_ICON:
				key := creative_entity.NewCreativeTemplateKey()
				request.AdxTemplateId = append(request.AdxTemplateId, kuaishou_broker_proto.Adm_VERTICAL_IMAGE.String())
				request.AdxTemplateId = append(request.AdxTemplateId, kuaishou_broker_proto.Adm_HORIZONTAL_IMAGE.String())
				key.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(slotWidth, slotHeight)
				key.Title().AddRequiredCount(1).SetOptional(true)
				request.AppendCreativeTemplateKey(key)
			case kuaishou_broker_proto.Adm_VIDEO:
				key := creative_entity.NewCreativeTemplateKey()
				request.AdxTemplateId = append(request.AdxTemplateId, kuaishou_broker_proto.Adm_VIDEO.String())
				// NOTE: 快手可以自适应图片转视频,视频模板可以不返回视频只返回图片
				key.Video().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(slotWidth, slotHeight).SetOptional(true)
				key.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(slotWidth, slotHeight)
				request.AppendCreativeTemplateKey(key)
			case kuaishou_broker_proto.Adm_VERTICAL_SCREEN:
				key := creative_entity.NewCreativeTemplateKey()
				key.Video().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL).SetOptional(true)
				key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)

				//key.Title().SetRequiredCount(1)
				request.AppendCreativeTemplateKey(key)

			case kuaishou_broker_proto.Adm_HORIZONTAL_SCREEN:
				key := creative_entity.NewCreativeTemplateKey()
				key.Video().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL).SetOptional(true)
				key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
				//key.Title().SetRequiredCount(1)
				request.AppendCreativeTemplateKey(key)

			case kuaishou_broker_proto.Adm_VERTICAL_IMAGE:
				key := creative_entity.NewCreativeTemplateKey()
				key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
				//key.Title().SetRequiredCount(1)
				request.AppendCreativeTemplateKey(key)

			case kuaishou_broker_proto.Adm_HORIZONTAL_IMAGE:
				key := creative_entity.NewCreativeTemplateKey()
				key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
				//key.Title().SetRequiredCount(1)
				request.AppendCreativeTemplateKey(key)
			case kuaishou_broker_proto.Adm_ATLAS:
				key := creative_entity.NewCreativeTemplateKey()
				key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				request.AppendCreativeTemplateKey(key)

				//case kuaishou_broker_proto.Adm_VIDEO:
				//	bidReqImp.AllowMimes = append(bidReqImp.AllowMimes, dict.MimeMp4, dict.MimeFlv, dict.MimeVideoFeed)
				//case kuaishou_broker_proto.Adm_STICKER:
				//	bidReqImp.AllowMimes = append(bidReqImp.AllowMimes, dict.MimeJpg, dict.MimePng, dict.MimeGif, dict.MimeJpeg, dict.MimeFeed)
				//case kuaishou_broker_proto.Adm_VERTICAL_SCREEN:
				//	bidReqImp.AllowMimes = append(bidReqImp.AllowMimes, dict.MimeMp4, dict.MimeFlv, dict.MimeVideoFeed)
				//case kuaishou_broker_proto.Adm_HORIZONTAL_SCREEN:
				//	bidReqImp.AllowMimes = append(bidReqImp.AllowMimes, dict.MimeMp4, dict.MimeFlv, dict.MimeVideoFeed)
				//case kuaishou_broker_proto.Adm_VERTICAL_IMAGE:
				//	bidReqImp.AllowMimes = append(bidReqImp.AllowMimes, dict.MimeJpg, dict.MimePng, dict.MimeGif, dict.MimeJpeg)
				//case kuaishou_broker_proto.Adm_HORIZONTAL_IMAGE:
				//	bidReqImp.AllowMimes = append(bidReqImp.AllowMimes, dict.MimeJpg, dict.MimePng, dict.MimeGif, dict.MimeJpeg)
			}
			request.AdxTemplateId = append(request.AdxTemplateId, cType.String())
		}
	}

	request.AddMediaExtraData(AdTypeMapKey, adTypeMap)
	request.AddMediaExtraData(CreativeTypeMapKey, creativeTypeMap)

	return nil
}

func (k *KuaiShouTrafficBroker) parseDevice(ksRequest *kuaishou_broker_proto.BidRequest, bidRequest *ad_service.AdRequest) error {
	device := ksRequest.GetDevice()
	if device.GetIp() != "" {
		bidRequest.Device.RequestIp = device.GetIp()
		bidRequest.Device.IsIp6 = false
	} else if device.GetIpv6() != "" {
		bidRequest.Device.RequestIp = device.GetIpv6()
		bidRequest.Device.IsIp6 = true
	}
	bidRequest.Device.UserAgent = device.GetUserAgent()
	if strings.Contains(device.GetBrowserUa(), "Mozilla") {
		ua, err := url.QueryUnescape(device.GetBrowserUa())
		if err == nil {
			bidRequest.Device.UserAgent = ua
		}
	}
	if strings.Contains(device.GetUa(), "Mozilla") {
		bidRequest.Device.UserAgent = device.GetUa()
	}

	if device.Geo != nil {
		bidRequest.Device.Lat = device.Geo.Lat
		bidRequest.Device.Lon = device.Geo.Lon
	}

	bidRequest.Device.DeviceType = k.GetDeviceType(device.GetDeviceType())
	if bidRequest.Device.DeviceType == entity.DeviceTypeMobile || bidRequest.Device.DeviceType == entity.DeviceTypePad {
		bidRequest.Device.IsMobile = true
	}

	bidRequest.Device.OsType = k.GetOsType(device.GetOsType())
	if device.GetOsVersion() != nil {
		if device.GetOsVersion().Major > 0 {
			bidRequest.Device.OsVersion = type_convert.GetAssertString(device.GetOsVersion().Major) + "." +
				type_convert.GetAssertString(device.GetOsVersion().Minor) + "." + type_convert.GetAssertString(device.GetOsVersion().Micro)
		}
	}

	bidRequest.Device.Brand = device.GetMake()
	bidRequest.Device.Vendor = device.GetMake()
	bidRequest.Device.Model = device.GetModel()
	bidRequest.Device.BootMark = device.GetBootMark()
	bidRequest.Device.UpdateMark = device.GetUpdateMark()

	if len(device.GetModelWithoutBrand()) > 0 {
		bidRequest.Device.Model = device.GetModelWithoutBrand()
	}
	if bidRequest.Device.Model == "" {
		bidRequest.Device.Model = device.GetDeviceModel()
	}
	bidRequest.Device.HardwareMachineCode = device.GetDeviceModel()
	// e.g.: HUAWEI(BRQ-AN00)
	if strings.ContainsRune(device.GetDeviceModel(), '(') {
		parts := strings.SplitN(device.GetDeviceModel(), "(", 2)
		brand := strings.TrimSpace(parts[0])
		if len(brand) > 0 {
			bidRequest.Device.Brand = brand
		}
		model := strings.TrimSpace(strings.TrimSuffix(parts[1], ")"))
		if len(model) > 0 {
			bidRequest.Device.Model = model
		}
	}

	if device.GetScreenSize() != nil {
		bidRequest.Device.ScreenWidth = int32(device.GetScreenSize().GetWidth())
		bidRequest.Device.ScreenHeight = int32(device.GetScreenSize().GetHeight())
	}

	bidRequest.App.AppBundle = device.PackageName
	bidRequest.App.InstalledApp = device.InstallApp
	bidRequest.App.MediaInstalledAppIds = device.InstallAppMd5

	// bidRequest.Mobile.Source

	if device.Udid != nil {
		udid := device.Udid
		bidRequest.Device.Idfa = udid.GetIdfa()
		bidRequest.Device.IdfaMd5 = udid.GetIdfaMd5()
		bidRequest.Device.Imei = udid.GetImei()
		bidRequest.Device.ImeiMd5 = udid.GetImeiMd5()
		bidRequest.Device.AndroidId = udid.GetAndroidId()
		bidRequest.Device.AndroidIdMd5 = udid.GetAndroididMd5()
		bidRequest.Device.Oaid = udid.GetOaid()
		bidRequest.Device.CaidRaw = udid.GetCurrentCaid()
		bidRequest.Device.CaidVersion = udid.GetCurrentCaidVersion()
		bidRequest.Device.Caid = string_utils.ConcatString(udid.GetCurrentCaidVersion(), "_", udid.GetCurrentCaid())
		bidRequest.Device.AliAaid = udid.GetAaid()
		bidRequest.Device.Paid = udid.GetPaid()
		if len(bidRequest.Device.Caid) > 0 {
			bidRequest.Device.Caids = append(bidRequest.Device.Caids, bidRequest.Device.Caid)
		}
		if len(udid.GetLastCaid()) > 0 {
			bidRequest.Device.Caids = append(bidRequest.Device.Caids, string_utils.ConcatString(udid.GetLastCaidVersion(), "_", udid.GetLastCaid()))
		}
	}

	app := ksRequest.GetApp()
	if app != nil {
		if app.GetBundle() != "" {
			bidRequest.App.AppBundle = app.GetBundle()
		}

		if len(bidRequest.App.AppBundle) == 0 && len(ksRequest.Imp) > 0 && len(ksRequest.Imp[0].MediumPackageName) > 0 {
			bidRequest.App.AppBundle = ksRequest.Imp[0].MediumPackageName
		}

		bidRequest.App.AppName = app.GetName()
		if bidRequest.App.AppName == "" {
			bidRequest.App.AppName = app.GetAppId()
		}

		//if len(bidRequest.App.AppBundle) == 0 && strings.Contains(app.GetAppId(), "kuaishou") {
		//	bidRequest.App.AppName = "快手"
		//	if device.GetOsType() == kuaishou_broker_proto.Device_IOS {
		//		bidRequest.App.AppBundle = "com.jiangjia.gif"
		//	} else {
		//		bidRequest.App.AppBundle = "com.smile.gifmaker"
		//	}
		//}

		if app.GetVersion() != nil {
			if app.GetVersion().GetMajor() != 0 {
				bidRequest.App.AppVersion = type_convert.GetAssertString(app.GetVersion().Major) + "." +
					type_convert.GetAssertString(app.GetVersion().Minor) + "." + type_convert.GetAssertString(app.GetVersion().Micro)
			}
		}

		bidRequest.App.AdxAppCategory = app.GetCat()
	}

	network := ksRequest.GetNetwork()
	if network != nil {
		bidRequest.Device.OperatorType = k.GetCarrierType(network.GetOperatorType())
		bidRequest.Device.ConnectionType = k.GetNetworkType(network.GetConnectionType())
		if network.Ipv4 != "" {
			bidRequest.Device.RequestIp = network.Ipv4
		}
	}

	return nil
}

func (k *KuaiShouTrafficBroker) GetDeviceType(ksType kuaishou_broker_proto.Device_DeviceType) entity.DeviceType {
	switch ksType {
	case kuaishou_broker_proto.Device_UNKNOWN_DEVICE_TYPE:
		return entity.DeviceTypeUnknown
	case kuaishou_broker_proto.Device_PHONE:
		return entity.DeviceTypeMobile
	case kuaishou_broker_proto.Device_TABLET:
		return entity.DeviceTypePad
	default:
		return entity.DeviceTypeUnknown
	}
}

func (k *KuaiShouTrafficBroker) GetOsType(ksType kuaishou_broker_proto.Device_OsType) entity.OsType {
	switch ksType {
	case kuaishou_broker_proto.Device_UNKNOWN_OS_TYPE:
		return entity.OsTypeUnknown
	case kuaishou_broker_proto.Device_ANDROID:
		return entity.OsTypeAndroid
	case kuaishou_broker_proto.Device_IOS:
		return entity.OsTypeIOS
	default:
		return entity.OsTypeUnknown
	}
}

func (k *KuaiShouTrafficBroker) GetNetworkType(ksType kuaishou_broker_proto.Network_ConnectionType) entity.ConnectionType {
	switch ksType {
	case kuaishou_broker_proto.Network_CONNECTION_UNKNOWN:
		return entity.ConnectionTypeUnknown
	case kuaishou_broker_proto.Network_CELL_UNKNOWN:
		return entity.ConnectionTypeCellular
	case kuaishou_broker_proto.Network_WIFI:
		return entity.ConnectionTypeWifi
	case kuaishou_broker_proto.Network_CELL_2G:
		return entity.ConnectionType2G
	case kuaishou_broker_proto.Network_CELL_3G:
		return entity.ConnectionType3G
	case kuaishou_broker_proto.Network_CELL_4G:
		return entity.ConnectionType4G
	case kuaishou_broker_proto.Network_CELL_5G:
		return entity.ConnectionType5G
	case kuaishou_broker_proto.Network_ETHERNET:
		return entity.ConnectionTypeNetEthernet
	default:
		return entity.ConnectionTypeUnknown
	}
}

func (k *KuaiShouTrafficBroker) GetCarrierType(ksType kuaishou_broker_proto.Network_OperatorType) entity.OperatorType {
	switch ksType {
	case kuaishou_broker_proto.Network_UNKNOWN_OPERATOR:
		return entity.OperatorTypeUnknown
	case kuaishou_broker_proto.Network_CHINA_MOBILE:
		return entity.OperatorTypeChinaMobile
	case kuaishou_broker_proto.Network_CHINA_UNICOM:
		return entity.OperatorTypeChinaUnicom
	case kuaishou_broker_proto.Network_CHINA_TELECOM:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func (k *KuaiShouTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		for adId, errCode := range request.Response.GetTotalAdCandidateList().GetErrCodeMap() {
			k.log.Infof("sendFallbackResponse adCode: %d, errCode: %s", adId, errCode)
		}
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/x-protobuf")
	writer.WriteWithStatus(204, nil)
	return nil
}

func (k *KuaiShouTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		k.log.Infof("sendResponse adCode: %+v", request.Response.GetTotalAdCandidateList().GetErrCodeMap())
	}

	if request.Response.NoCandidate() {
		return k.SendFallbackResponse(request, writer)
	}

	response := &kuaishou_broker_proto.BidResponse{}

	response.RequestId = request.GetRequestId()
	response.BidId = request.GetRequestId()
	response.Status = 1
	response.SeatBid = make([]*kuaishou_broker_proto.SeatBid, 0)

	for _, candidate := range request.Response.GetAdCandidateList() {
		response.Status = 0
		seatBid := &kuaishou_broker_proto.SeatBid{}
		seatBid.Bid = make([]*kuaishou_broker_proto.Bid, 0)
		response.SeatBid = append(response.SeatBid, seatBid)

		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		ksBid := &kuaishou_broker_proto.Bid{}
		ksBid.BidId = request.GetRequestId()
		ksBid.ImpId = request.ImpressionId

		// XXX: 快手ID,新户: 2315504645, 老户: 4724705771
		ksBid.UserId = 4724705771

		bidPrice := candidate.GetBidPrice()

		ksBid.Price = float64(bidPrice.Price) / 100.0
		ksBid.AdId = strconv.Itoa(int(genericAd.GetAdId()))
		ksBid.BidType = kuaishou_broker_proto.Bid_CPM
		if bidPrice.Type == entity.BidTypeCpc {
			ksBid.BidType = kuaishou_broker_proto.Bid_CPC
		}

		ksBid.Size_ = &kuaishou_broker_proto.Size{
			Width:  request.GetSlotWidth(),
			Height: request.GetSlotHeight(),
		}

		//开屏会延迟曝光，故把winnotice放到曝光监控里面
		//ksBid.NoticeUrl = bid.MonitorData.WinNotice

		if creative != nil {
			if creative.GetCreativeId() > 0 {
				ksBid.CreativeId = strconv.Itoa(int(creative.GetCreativeId()))
			} else {
				ksBid.CreativeId = creative.GetCreativeKey()
			}
		}

		//}
		//ksBid.AdvertiserUsername = bid.MaterialData.AdvertiserName

		if candidate.GetIndexDeal() != nil {
			ksBid.DealId = candidate.GetIndexDeal().DealId
			ksBid.Price = 0
			ksBid.BidType = 0
		}

		ksBid.AdTracking = make([]*kuaishou_broker_proto.Tracking, 0)

		//todo winnotice
		impTracker := candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
		ksBid.AdTracking = append(ksBid.AdTracking, &kuaishou_broker_proto.Tracking{
			TrackingEvent: kuaishou_broker_proto.Tracking_AD_EXPOSURE,
			TrackingUrl:   impTracker,
		})

		clickMonitor := candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen)

		if len(clickMonitor) > 0 {
			if ksBid.BidType == kuaishou_broker_proto.Bid_CPC {
				clickMonitor[0] = clickMonitor[0] + "&p=" + k.mediaMacro.MediaPriceMacro
			}
			ksBid.AdTracking = append(ksBid.AdTracking, &kuaishou_broker_proto.Tracking{
				TrackingEvent: kuaishou_broker_proto.Tracking_AD_CLICK,
				TrackingUrl:   clickMonitor,
			})
		}

		dpMonitor := candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen)
		if len(genericAd.GetDeepLinkUrl()) > 0 {
			dpMonitor = append(dpMonitor, candidate.ReplaceUrlMacro(genericAd.GetDpSuccess(), traffic, trackingGen))
		}
		if len(dpMonitor) > 0 {
			ksBid.AdTracking = append(ksBid.AdTracking, &kuaishou_broker_proto.Tracking{
				TrackingEvent: kuaishou_broker_proto.Tracking_APP_AD_ACTIVE,
				TrackingUrl:   dpMonitor,
			})
		}

		if len(genericAd.GetAppDownloadStartedMonitorList()) > 0 {
			ksBid.AdTracking = append(ksBid.AdTracking, &kuaishou_broker_proto.Tracking{
				TrackingEvent: kuaishou_broker_proto.Tracking_APP_AD_DOWNLOAD,
				TrackingUrl:   candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadStartedMonitorList(), traffic, trackingGen),
			})
		}
		if len(genericAd.GetAppInstallStartMonitorList()) > 0 {
			ksBid.AdTracking = append(ksBid.AdTracking, &kuaishou_broker_proto.Tracking{
				TrackingEvent: kuaishou_broker_proto.Tracking_APP_AD_INSTALL,
				TrackingUrl:   candidate.ReplaceUrlMacroList(genericAd.GetAppInstallStartMonitorList(), traffic, trackingGen),
			})
		}

		if len(genericAd.GetVideoStartUrlList()) > 0 {
			ksBid.AdTracking = append(ksBid.AdTracking, &kuaishou_broker_proto.Tracking{
				TrackingEvent: kuaishou_broker_proto.Tracking_VIDEO_AD_START,
				TrackingUrl:   candidate.ReplaceUrlMacroList(genericAd.GetVideoStartUrlList(), traffic, trackingGen),
			})
		}

		if len(genericAd.GetVideoCloseUrlList()) > 0 {
			ksBid.AdTracking = append(ksBid.AdTracking, &kuaishou_broker_proto.Tracking{
				TrackingEvent: kuaishou_broker_proto.Tracking_VIDEO_AD_END,
				TrackingUrl:   candidate.ReplaceUrlMacroList(genericAd.GetVideoCloseUrlList(), traffic, trackingGen),
			})
		}

		ksBid.Adm = &kuaishou_broker_proto.Adm{
			//CreativeType:    k.toKuaiShouCreativeType(bid.MaterialData.MimeType),
			InteractionType: kuaishou_broker_proto.Adm_SURFING,
			ClickUrl:        candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
			DeeplinkUrl:     genericAd.GetDeepLinkUrl(),
			//BundleId:        bid.MonitorData.LandingDesc.AppId,
		}

		if genericAd.GetAppInfo() != nil {
			ksBid.Adm.PackageName = genericAd.GetAppInfo().PackageName
			ksBid.Adm.AppName = genericAd.GetAppInfo().AppName
			ksBid.Adm.DeveloperName = genericAd.GetAppInfo().Develop
			ksBid.Adm.AppVersion = genericAd.GetAppInfo().AppVersion
			ksBid.Adm.AppPrivacyUrl = genericAd.GetAppInfo().Privacy
			ksBid.Adm.AppPermission = genericAd.GetAppInfo().Permission
			ksBid.Adm.PackageSize = uint32(genericAd.GetAppInfo().PackageSize)
			ksBid.Adm.PackageSizeV2 = uint64(genericAd.GetAppInfo().PackageSize * 1024)
		}

		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			ksBid.Adm.InteractionType = kuaishou_broker_proto.Adm_DOWNLOAD
		}

		if len(ksBid.DealId) > 0 {
			tm := time_utils.GetTimeUnixSecondUnsafe()
			ksBid.Adm.DateTimestamp = uint64(tm - tm%86400 - 8*3600)
		}

		imageList := make([]string, 0)
		coverImage := ""
		hasVideo := false
		hasImage := false

		for _, material := range candidate.GetSelectedMaterialList() {
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				ksBid.Adm.Title = material.Data
			case entity.MaterialTypeDesc:
				ksBid.Adm.Desc = material.Data
			case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
				imageList = append(imageList, candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen))
				if material.Width != 0 && material.Height != 0 {
					ksBid.Size_.Width = uint32(material.Width)
					ksBid.Size_.Height = uint32(material.Height)
				}

				hasImage = true
				if material.MaterialType == entity.MaterialTypeCoverImage {
					coverImage = material.Url
				}
			case entity.MaterialTypeIcon:
				ksBid.Adm.IconUrl = material.Url
			case entity.MaterialTypeImageCard:
				ksBid.Card = &kuaishou_broker_proto.Card{
					CardId:      md5_utils.GetMd5String(material.Url),
					CardUrl:     material.Url,
					CardWidth:   material.Width,
					CardHeight:  material.Height,
					AdxCardType: kuaishou_broker_proto.Card_PIC_CARD,
					//CardTitle:   "",
				}
			case entity.MaterialTypeVideo:
				ksBid.Adm.VideoUrl = candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen)
				ksBid.Adm.VideoDuration = uint32(material.Duration)
				if material.Width != 0 && material.Height != 0 {
					ksBid.Size_.Width = uint32(material.Width)
					ksBid.Size_.Height = uint32(material.Height)
				}
				hasVideo = true
			}
		}

		if len(ksBid.Adm.Title) == 0 {
			for _, rsc := range creative.GetMaterialList() {
				switch rsc.MaterialType {
				case entity.MaterialTypeTitle:
					ksBid.Adm.Title = rsc.Data
				case entity.MaterialTypeDesc:
					ksBid.Adm.Desc = rsc.Data
				}
			}
		}

		if hasVideo && hasImage {
			if coverImage != "" {
				ksBid.Adm.CoverUrl = coverImage
			} else {
				ksBid.Adm.CoverUrl = imageList[0]
			}
		} else if hasImage {
			ksBid.Adm.PicUrls = imageList
		}

		if ksBid.Adm.Title == "" {
			ksBid.Adm.Title = "点击查看详情"
		}

		if ksBid.Adm.Desc == "" {
			ksBid.Adm.Desc = "点击查看详情"
		}

		impIdAdTypeKey := ksBid.ImpId + AdTypeKey
		adTypeValue, ok := request.GetMediaExtraInt64(impIdAdTypeKey)
		if !ok {
			adTypeValue, _ = request.GetMediaExtraInt64(AdTypeKey)
		}

		adType := kuaishou_broker_proto.AdTypeEnum_AdType(adTypeValue)
		ksBid.Adm.AdType = adType
		//kaiping
		if adType == kuaishou_broker_proto.AdTypeEnum_KUAISHOU_SPLASH_SLOT_VIDEO || adType == kuaishou_broker_proto.AdTypeEnum_KUAISHOU_SPLASH_SLOT_PIC_TEXT {
			//匹配开屏里面的impid 对应的adtype,视频是6 图片是7
			reqAdTypeMap, ok1 := request.GetMediaExtraDataWithDefault(AdTypeMapKey, emptyAdTypeMap).(map[kuaishou_broker_proto.AdTypeEnum_AdType]string)
			if ok1 {
				if hasVideo {
					if _, ok2 := reqAdTypeMap[kuaishou_broker_proto.AdTypeEnum_KUAISHOU_SPLASH_SLOT_VIDEO]; ok2 {
						ksBid.Adm.AdType = kuaishou_broker_proto.AdTypeEnum_KUAISHOU_SPLASH_SLOT_VIDEO
						ksBid.ImpId = reqAdTypeMap[kuaishou_broker_proto.AdTypeEnum_KUAISHOU_SPLASH_SLOT_VIDEO]
					}
				} else {
					if _, ok2 := reqAdTypeMap[kuaishou_broker_proto.AdTypeEnum_KUAISHOU_SPLASH_SLOT_PIC_TEXT]; ok2 {
						ksBid.Adm.AdType = kuaishou_broker_proto.AdTypeEnum_KUAISHOU_SPLASH_SLOT_PIC_TEXT
						ksBid.ImpId = reqAdTypeMap[kuaishou_broker_proto.AdTypeEnum_KUAISHOU_SPLASH_SLOT_PIC_TEXT]
					}
				}
			}
		}

		reqCretiveTypeMap := request.GetMediaExtraDataWithDefault(CreativeTypeMapKey, emptyCreativeTypeMap).(map[kuaishou_broker_proto.Adm_CreativeType]string)
		if hasVideo {
			ksBid.Adm.CreativeType = kuaishou_broker_proto.Adm_VIDEO
			if len(reqCretiveTypeMap[kuaishou_broker_proto.Adm_VERTICAL_SCREEN]) > 0 || len(reqCretiveTypeMap[kuaishou_broker_proto.Adm_HORIZONTAL_SCREEN]) > 0 {
				ksBid.Adm.CreativeType = kuaishou_broker_proto.Adm_VERTICAL_SCREEN
				if ksBid.Size_.Width > ksBid.Size_.Height {
					ksBid.Adm.CreativeType = kuaishou_broker_proto.Adm_HORIZONTAL_SCREEN
				}
			}
		} else if len(reqCretiveTypeMap[kuaishou_broker_proto.Adm_TEXT_ICON]) > 0 {
			ksBid.Adm.CreativeType = kuaishou_broker_proto.Adm_TEXT_ICON
		} else if len(reqCretiveTypeMap[kuaishou_broker_proto.Adm_ATLAS]) > 0 {
			// https://docs.qingque.cn/d/home/<USER>
			ksBid.Adm.CreativeType = kuaishou_broker_proto.Adm_ATLAS
		} else {
			ksBid.Adm.CreativeType = kuaishou_broker_proto.Adm_VERTICAL_IMAGE
			if ksBid.Size_.Width > ksBid.Size_.Height {
				ksBid.Adm.CreativeType = kuaishou_broker_proto.Adm_HORIZONTAL_IMAGE
			}
		}

		if len(reqCretiveTypeMap[ksBid.Adm.CreativeType]) > 0 {
			ksBid.ImpId = reqCretiveTypeMap[ksBid.Adm.CreativeType]
		}

		seatBid.Bid = append(seatBid.Bid, ksBid)
		break

	}

	k.log.Debugf("Build Response end, response:[%v]", response)

	buffer := buffer_pool.NewBuffer()
	defer buffer.Release()

	buffer.EnsureSize(response.Size())
	_, err := response.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		k.log.WithError(err).Error("MarshalToSizedBuffer error")
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/x-protobuf")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	k.DoTrafficResponseSamplePb(request, response)
	if request.IsDebug {
		responseStr, _ := json.Marshal(response)
		k.log.WithField("response", string(responseStr)).Info("SendResponse success")
	}
	return nil
}

//func (k *KuaiShouTrafficBroker) toKuaiShouCreativeType(myType dict.MimeType) kuaishou_broker_proto.Adm_CreativeType {
//	switch myType {
//	case dict.MimeJpg, dict.MimeJpeg, dict.MimePng, dict.MimeGif:
//		return kuaishou_broker_proto.Adm_TEXT_ICON
//	case dict.MimeFlv, dict.MimeMp4, dict.MimeVideoFeed:
//		return kuaishou_broker_proto.Adm_VIDEO
//	case dict.MimeText:
//		return kuaishou_broker_proto.Adm_TEXT
//	case dict.MimeFeed:
//		return kuaishou_broker_proto.Adm_TEXT_ICON
//	default:
//		return kuaishou_broker_proto.Adm_NO_TYPE
//	}
//}

//func (k *KuaiShouTrafficBroker) toKuaiShouLandingType(myType dict.LandingType) kuaishou_broker_proto.Adm_InteractionType {
//	switch myType {
//	case dict.LandingTypeDownload:
//		return kuaishou_broker_proto.Adm_DOWNLOAD
//	default:
//		return kuaishou_broker_proto.Adm_SURFING
//	}
//}
