package qiming_traffic_broker

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/qiming_traffic_broker/qiming_broker_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
)

const (
	adxTemplateKey = "adxTemplate"
	adxTemplateId  = "adxTemplateId"
)

type QimingTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        *zap.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewQimingTrafficBroker(mediaId utils.ID) *QimingTrafficBroker {
	return &QimingTrafficBroker{
		log:     zap.L().With(zap.String("broker", "QimingTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "__ADS_BID_PRICE__",
			MediaClickDownXMacro: "__ADS_DOWN_X__", // ⽤户⼿指按下时相对广告位的横坐标
			MediaClickDownYMacro: "__ADS_DOWN_Y__", // ⽤户⼿指按下时相对广告位的纵坐标
			MediaClickUpXMacro:   "__ADS_UP_X__",   // ⽤户⼿指抬起时相对广告位的横坐标
			MediaClickUpYMacro:   "__ADS_UP_Y__",   // ⽤户⼿指抬起时相对广告位的纵坐标
		},
	}
}

func (q *QimingTrafficBroker) GetMediaId() utils.ID {
	return q.mediaId
}

func (q *QimingTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(q.SendResponse)
	request.Response.SetFallbackResponseBuilder(q.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = q.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = q.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &qiming_broker_entity.BidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		q.log.WithError(err).Error("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		q.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	request.SetMediaId(q.GetMediaId())
	request.SetRequestId(bidRequest.ReqID)
	if len(bidRequest.ReqID) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}

	q.parseApp(bidRequest, request)
	q.parseDevice(bidRequest, request)
	q.parseNetwork(bidRequest, request)
	q.parseGeo(bidRequest, request)
	if err = q.parseImp(bidRequest, request); err != nil {
		q.log.WithError(err).Error("AdRequest, parseImp failed")
		return err
	}

	q.DoTrafficSample(request, body)

	return nil
}

func (q *QimingTrafficBroker) parseApp(request *qiming_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.App != nil {
		adRequest.App.AppName = request.App.AppName
		adRequest.App.AppVersion = request.App.Version
		adRequest.App.AppBundle = request.App.Pkg
		if request.App.Category != "" {
			adRequest.App.AdxAppCategory = append(adRequest.App.AdxAppCategory, request.App.Category)
		}
	}
}

func (q *QimingTrafficBroker) parseDevice(request *qiming_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.Device == nil {
		return
	}

	adRequest.Device = ad_service.AdRequestDevice{
		OsType:              mappingOsType(request.Device.OsType),
		OsVersion:           request.Device.OsVersion,
		DeviceType:          mappingDeviceType(request.Device.DeviceType),
		ScreenHeight:        request.Device.ScreenHeight,
		ScreenWidth:         request.Device.ScreenWidth,
		PPI:                 request.Device.Ppi,
		SdkVersion:          strconv.Itoa(request.Device.AndroidApiLevel),
		Model:               request.Device.Model,
		Brand:               request.Device.Vendor,
		Vendor:              request.Device.Vendor,
		UserAgent:           request.Device.UA,
		WebviewUA:           request.Device.UA,
		AndroidId:           request.Device.AndroidID,
		AndroidIdMd5:        request.Device.AndroidIDMD5,
		Imei:                request.Device.IMEI,
		ImeiMd5:             request.Device.IMEIMD5,
		Idfv:                request.Device.IDFV,
		IdfvMd5:             request.Device.IDFVMD5,
		Oaid:                request.Device.OAID,
		OaidMd5:             request.Device.OAIDMD5,
		Idfa:                request.Device.IDFA,
		IdfaMd5:             request.Device.IDFAMD5,
		OpenUdid:            request.Device.OpenUDID,
		DeviceName:          request.Device.PhoneName,
		DeviceNameMd5:       request.Device.PhoneNameMD5,
		SystemTotalDisk:     request.Device.DiskSize,
		SystemTotalMem:      request.Device.MemorySize,
		SystemCompileTime:   request.Device.CompTime,
		SystemTotalCpu:      request.Device.CPUNumber,
		SystemCPUFrequency:  request.Device.CPUFrequency,
		SystemBatteryStatus: request.Device.BatteryStatus,
		SystemBatteryPower:  request.Device.BatteryPower,
		IsMobile:            request.Device.DeviceType == 1,
		DeviceInitTime:      request.Device.DeviceBirthTime,
		DeviceStartupTime:   strconv.Itoa(request.Device.BootTime),
		DeviceUpgradeTime: func() string {
			if request.Device.OsUpdateExactTime != "" {
				return request.Device.OsUpdateExactTime
			}
			return strconv.Itoa(request.Device.OsUpdateTime)
		}(),
		BootMark:        request.Device.OsBootMark,
		UpdateMark:      request.Device.OsUpdateMark,
		VercodeAg:       request.Device.HarmonyOsVer,
		AppStoreVersion: request.Device.StoreVersion,
		VercodeHms:      request.Device.HMSVer,
		Paid:            request.Device.PAID,
		Language:        request.Device.Language,

		CountryCode: request.Device.CountryCode,

		ScreenOrientation:   mappingScreenOrientation(request.Device.Orientation),
		HardwareMachineCode: request.Device.HardwareModel,
		SystemOsUIVersion:   request.Device.OsUIVersion,
		SystemElapseTime:    request.Device.ElapseTime,
	}
	if len(request.Device.TimeZone) > 0 {
		tz, _ := parseTimezone(request.Device.TimeZone)
		adRequest.Device.TimeZone = int32(tz)
	}

	// app
	adRequest.App.InstalledApp = request.Device.InstallApp

	// 处理 AndroidID MD5
	if len(request.Device.AndroidIDMD5) == 0 && len(request.Device.AndroidID) > 0 {
		adRequest.Device.AndroidIdMd5 = md5_utils.GetMd5String(request.Device.AndroidID)
	}

	// 处理 IMEI MD5
	if len(request.Device.IMEIMD5) == 0 && len(request.Device.IMEI) > 0 {
		adRequest.Device.ImeiMd5 = md5_utils.GetMd5String(request.Device.IMEI)
	}

	// 处理 IDFV MD5
	if len(request.Device.IDFVMD5) == 0 && len(request.Device.IDFV) > 0 {
		adRequest.Device.IdfvMd5 = md5_utils.GetMd5String(request.Device.IDFV)
	}

	// 处理 OAID MD5
	if len(request.Device.OAIDMD5) == 0 && len(request.Device.OAID) > 0 {
		adRequest.Device.OaidMd5 = md5_utils.GetMd5String(request.Device.OAID)
	}

	// 处理 IDFA MD5
	if len(request.Device.IDFAMD5) == 0 && len(request.Device.IDFA) > 0 {
		adRequest.Device.IdfaMd5 = md5_utils.GetMd5String(request.Device.IDFA)
	}
	if len(request.Device.CAID) > 0 {
		// caid的格式为"version_caid值"
		caid := strings.Join([]string{request.Device.CAIDVer, request.Device.CAID}, "_")
		adRequest.Device.Caid = caid
		adRequest.Device.CaidRaw = request.Device.CAID
		adRequest.Device.CaidVersion = request.Device.CAIDVer
		adRequest.Device.Caids = append(adRequest.Device.Caids, caid)
	}

}

func (q *QimingTrafficBroker) parseNetwork(request *qiming_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.Network == nil {
		return
	}
	adRequest.Device.Mac = request.Network.MAC
	adRequest.Device.MacMD5 = request.Network.MACMD5
	adRequest.Device.RequestIp = request.Network.IPv4
	adRequest.Device.ConnectionType = mappingConnectionType(request.Network.ConnectType)
	adRequest.Device.OperatorType = mappingOperatorType(request.Network.Operator)
	adRequest.Device.OperatorName = mappingOperatorType(request.Network.Operator).String()
	adRequest.Device.Imsi = request.Network.IMSI
	// 处理 Imsi MD5
	if len(request.Network.IMSI) > 0 {
		adRequest.Device.ImsiMd5 = md5_utils.GetMd5String(request.Network.IMSI)
	}

	if len(request.Network.IPv6) > 0 {
		adRequest.Device.IsIp6 = true
		adRequest.Device.RequestIp = request.Network.IPv6
	}
}

func (q *QimingTrafficBroker) parseGeo(request *qiming_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.Geo == nil {
		return
	}

	lat, _ := strconv.ParseFloat(request.Geo.Lat, 64)
	lng, _ := strconv.ParseFloat(request.Geo.Lng, 64)
	adRequest.Device.Lat = lat
	adRequest.Device.Lon = lng
	adRequest.Device.GpsTime = request.Geo.Timestamp
	adRequest.Device.GpsType = mappingGpsType(request.Geo.CoordinateType)
}

func (q *QimingTrafficBroker) parseImp(request *qiming_broker_entity.BidRequest, adRequest *ad_service.AdRequest) error {

	// 图文
	imgTemplate := creative_entity.NewCreativeTemplateKey()
	imgTemplate.Title().AddRequiredCount(1).SetOptional(true)
	imgTemplate.Desc().AddRequiredCount(1).SetOptional(true)
	imgTemplate.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	imgTemplate.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
	adRequest.AppendCreativeTemplateKey(imgTemplate)

	// 视频
	videoTemplate := creative_entity.NewCreativeTemplateKey()
	videoTemplate.Title().AddRequiredCount(1).SetOptional(true)
	videoTemplate.Desc().AddRequiredCount(1).SetOptional(true)
	videoTemplate.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
	videoTemplate.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	videoTemplate.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
	adRequest.AppendCreativeTemplateKey(videoTemplate)

	adRequest.ImpressionId = request.ReqID
	adRequest.SetMediaSlotKey(request.AdPositionID)
	if request.BidInfo != nil {
		adRequest.BidFloor = request.BidInfo.BidFloor
	}
	adRequest.UseHttps = request.HTTPSRequired
	adRequest.BidType = entity.BidTypeCpm

	adRequest.SlotWidth = request.AdWidth
	adRequest.SlotHeight = request.AdHeight

	adRequest.AddMediaExtraData(adxTemplateId, request.AdType)

	return nil
}

func (q *QimingTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		q.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("Qiming")
	}

	if request.Response.NoCandidate() {
		return q.SendFallbackResponse(request, writer)
	}

	adResponse, err := q.buildResponse(request)
	if err != nil {
		q.log.WithError(err).Error("buildResponse err")
		return err
	}

	err = q.BuildHttpSonicJsonResponse(request, writer, adResponse)
	if err != nil {
		return err
	}

	q.DoTrafficResponseSampleSonicJson(request, adResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(adResponse)
		q.log.Infof("SendResponse success, response: %s", responseStr)
	}

	return nil
}

func (q *QimingTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		q.log.Infof("Build Fallback Response start. bid response: [%v]", request.Response)
	}

	adResponse := &qiming_broker_entity.BidResponse{
		Code:     204,
		ErrorMsg: "",
		Data:     make([]*qiming_broker_entity.Ad, 0),
	}

	err := q.BuildHttpSonicJsonResponse(request, writer, adResponse)

	return err
}

func (q *QimingTrafficBroker) buildResponse(request *ad_service.AdRequest) (*qiming_broker_entity.BidResponse, error) {
	adResponse := &qiming_broker_entity.BidResponse{
		Code: 200,
		Data: make([]*qiming_broker_entity.Ad, 1),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		ad := &qiming_broker_entity.Ad{
			ResID:             request.GetRequestId(),
			BidPrice:          candidate.GetBidPrice().Price,
			WinNoticeURLArray: []string{},
		}

		// 设置交互类型
		switch genericAd.GetLandingAction() {
		case entity.LandingTypeInWebView:
			ad.Action = 1
		case entity.LandingTypeDownload:
			ad.Action = 2
		case entity.LandingTypeDeepLink:
			ad.Action = 3
		}

		// 设置广告类型
		ad.AdType = request.GetMediaExtraString(adxTemplateId, "")

		// 设置素材信息
		VideoDuration := int(0)
		for _, material := range candidate.GetSelectedMaterialList() {
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				ad.Title = material.Data
			case entity.MaterialTypeDesc:
				ad.Desc = material.Data
			case entity.MaterialTypeIcon:
				ad.Icon = material.Url
			case entity.MaterialTypeImage:
				ad.Images = append(ad.Images, candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen))
				ad.ImageWidth = material.Width
				ad.ImageHeight = material.Height
			case entity.MaterialTypeVideo:
				ad.Video = &qiming_broker_entity.Video{
					URL:         candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen),
					VideoWidth:  material.Width,
					VideoHeight: material.Height,
					Duration:    int(material.Duration),
				}
				VideoDuration = int(material.Duration)
			}
		}

		// 设置落地页信息
		ad.LandingURL = candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen)
		ad.DeeplinkURL = genericAd.GetDeepLinkUrl()

		// 设置上报信息
		ad.Tracker = &qiming_broker_entity.Tracker{
			Display:       candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			Click:         candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
			Deeplink:      candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen),
			DownloadStart: candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadStartedMonitorList(), traffic, trackingGen),
			DownloadEnd:   candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadFinishedMonitorList(), traffic, trackingGen),
			InstallStart:  candidate.ReplaceUrlMacroList(genericAd.GetAppInstallStartMonitorList(), traffic, trackingGen),
			InstallEnd:    candidate.ReplaceUrlMacroList(genericAd.GetAppInstalledMonitorList(), traffic, trackingGen),
			Open:          candidate.ReplaceUrlMacroList(genericAd.GetAppOpenMonitorList(), traffic, trackingGen),
			VideoStart:    candidate.ReplaceUrlMacroList(genericAd.GetVideoStartUrlList(), traffic, trackingGen),
			VideoClose:    candidate.ReplaceUrlMacroList(genericAd.GetVideoCloseUrlList(), traffic, trackingGen),
		}

		for _, delayMonitor := range candidate.GetGenericAd().GetDelayMonitorUrlList() {
			event := entity.GetVideoTrackingEvent(delayMonitor.Delay, VideoDuration)
			switch event {
			case entity.KVideoTrackingEventStart:
				ad.Tracker.VideoStart = append(ad.Tracker.VideoStart, delayMonitor.Url)
			case entity.KVideoTrackingEventFirst:
				ad.Tracker.VideoQuarter = append(ad.Tracker.VideoQuarter, delayMonitor.Url)
			case entity.KVideoTrackingEventMid:
				ad.Tracker.VideoMiddle = append(ad.Tracker.VideoMiddle, delayMonitor.Url)
			case entity.KVideoTrackingEventThird:
				ad.Tracker.VideoThirdQuarter = append(ad.Tracker.VideoThirdQuarter, delayMonitor.Url)
			case entity.KVideoTrackingEventComplete:
				ad.Tracker.VideoEnd = append(ad.Tracker.VideoEnd, delayMonitor.Url)
			}
		}

		// 设置应用推广信息
		if genericAd.GetAppInfo() != nil {
			ad.AppPromotion = &qiming_broker_entity.AppPromotion{
				AppName:        genericAd.GetAppInfo().AppName,
				AppVersion:     genericAd.GetAppInfo().AppVersion,
				AppIconURL:     genericAd.GetAppInfo().Icon,
				AdvertiserName: genericAd.GetAppInfo().Develop,
				// PrivacyPolicyURL:    genericAd.GetAppInfo().Privacy,
				PrivacyPolicyInfo: genericAd.GetAppInfo().Privacy,
				AppBundle:         genericAd.GetAppInfo().PackageName,
				// PrivacyAuthURL:      genericAd.GetAppInfo().Permission,
				PrivacyAuthInfo:     genericAd.GetAppInfo().Permission,
				AppIntroductionURL:  genericAd.GetAppInfo().AppDescURL,
				AppIntroductionInfo: genericAd.GetAppInfo().AppDesc,
			}
		}

		adResponse.Data[0] = ad
		break
	}

	return adResponse, nil
}

func mappingOsType(osType int) entity.OsType {
	switch osType {
	case 1:
		return entity.OsTypeAndroid
	case 2:
		return entity.OsTypeIOS
	default:
		return entity.OsTypeUnknown
	}
}

func mappingScreenOrientation(orientation int) entity.ScreenOrientationType {
	switch orientation {
	case 0:
		return entity.ScreenOrientationTypePortrait
	case 1:
		return entity.ScreenOrientationTypeLandscape
	default:
		return entity.ScreenOrientationTypeUnknown
	}
}

func mappingOperatorType(operator int) entity.OperatorType {
	switch operator {
	case 1:
		return entity.OperatorTypeChinaMobile
	case 2:
		return entity.OperatorTypeChinaUnicom
	case 3:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType int) entity.ConnectionType {
	switch connectionType {
	case 101:
		return entity.ConnectionTypeNetEthernet
	case 100:
		return entity.ConnectionTypeWifi
	case 1:
		return entity.ConnectionTypeCellular
	case 2:
		return entity.ConnectionType2G
	case 3:
		return entity.ConnectionType3G
	case 4:
		return entity.ConnectionType4G
	case 5:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingDeviceType(deviceType int) entity.DeviceType {
	switch deviceType {
	case 1:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePad
	default:
		return entity.DeviceTypeUnknown
	}
}

func mappingGpsType(gpsType int) entity.GpsType {
	switch gpsType {
	case 1:
		return entity.GpsTypeWSG84
	case 2:
		return entity.GpsTypeGCJ02
	case 3:
		return entity.GpsTypeBd09
	default:
		return entity.GpsTypeUnknown
	}
}

// parseTimezone 将时区字符串转换为秒
// 支持的格式: "GMT+8", "GMT+08:00", "GMT-8", "GMT-08:00"
func parseTimezone(timezone string) (int, error) {
	// 移除 "GMT" 前缀并标准化格式
	offsetStr := strings.TrimPrefix(timezone, "GMT")
	if offsetStr == timezone {
		return 0, fmt.Errorf("invalid timezone format: %s", timezone)
	}

	// 处理正负号
	sign := 1
	if strings.HasPrefix(offsetStr, "-") {
		sign = -1
		offsetStr = offsetStr[1:]
	} else if strings.HasPrefix(offsetStr, "+") {
		offsetStr = offsetStr[1:]
	}

	// 解析小时和分钟
	parts := strings.Split(offsetStr, ":")
	if len(parts) > 2 {
		return 0, fmt.Errorf("invalid timezone format: %s", timezone)
	}

	// 解析小时
	hours, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, fmt.Errorf("invalid hours in timezone: %s", timezone)
	}

	// 解析分钟
	minutes := 0
	if len(parts) == 2 {
		minutes, err = strconv.Atoi(parts[1])
		if err != nil {
			return 0, fmt.Errorf("invalid minutes in timezone: %s", timezone)
		}
	}

	// 计算总秒数
	return sign * (hours*3600 + minutes*60), nil
}
