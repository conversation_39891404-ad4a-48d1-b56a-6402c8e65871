package wax_broker

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/wax_broker/wax_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
)

const (
	AdxTemplateKey = "adxTemplate"
)

var (
	emptyAdxTemplateMap map[uint64]string = make(map[uint64]string)
)

type (
	WaxTrafficBroker struct {
		traffic_broker.TrafficBrokerBase
		log        *zap.Logger
		mediaId    utils.ID
		mediaMacro *macro_builder.MediaMacro
	}
)

func NewWaxTrafficBroker(mediaId utils.ID) *WaxTrafficBroker {
	return &WaxTrafficBroker{
		log:     zap.L().With(zap.String("broker", "WaxTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro: "__AUCTION_PRICE__",
		},
	}
}

func (mb *WaxTrafficBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *WaxTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = mb.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = mb.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("request body empty")
	}

	bidRequest := &wax_entity.WaxRequest{}
	err := easyjson.Unmarshal(body, bidRequest)
	if err != nil {
		mb.log.WithError(err).Error("Request body unmarshal failed")
		return fmt.Errorf("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := easyjson.Marshal(bidRequest)
		mb.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}
	request.SetMediaId(mb.mediaId)

	if err := mb.parseUser(bidRequest, request); err != nil {
		mb.log.WithError(err).Error("BrokeRequest, parseUser failed")
		return err
	}

	if err := mb.parseApp(bidRequest, request); err != nil {
		mb.log.WithError(err).Error("BrokeRequest, parseApp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, request); err != nil {
		mb.log.WithError(err).Error("BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseImp(bidRequest, request); err != nil {
		mb.log.WithError(err).Error("BrokeRequest, parseImp failed")
		return err
	}

	mb.DoTrafficSample(request, body)

	return nil
}

func (mb *WaxTrafficBroker) parseUser(mediaBidRequest *wax_entity.WaxRequest, bidReq *ad_service.AdRequest) error {
	if mediaBidRequest.User == nil {
		mb.log.Debugf("[WaxTrafficBroker]parseUser, user nil")
		return nil
	}

	user := mediaBidRequest.User
	bidReq.UserId = user.Id

	return nil
}

func (mb *WaxTrafficBroker) parseImp(mediaBidRequest *wax_entity.WaxRequest,
	bidReq *ad_service.AdRequest) error {

	if len(mediaBidRequest.Imp) == 0 {
		mb.log.Debugf("[WaxTrafficBroker]parseImp, vendor: %d, imp nil", bidReq.GetMediaId())
		return fmt.Errorf("parseImp, imp nil")
	}

	item := mediaBidRequest.Imp[0]
	if item == nil {
		return fmt.Errorf("parseImp, first imp nil")
	}

	bidReq.ImpressionId = item.Id      // imp id
	bidReq.SetMediaSlotKey(item.TagId) // 广告位id
	bidReq.BidFloor = uint32(item.BidFloor)

	if item.Banner != nil {
		bidReq.SlotType = entity.SlotTypeBanner
		if item.Banner.H > 0 && item.Banner.W > 0 {
			bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
				Width:  int64(item.Banner.W),
				Height: int64(item.Banner.H),
			})
		}

		adxTemplateMap := make(map[uint64]string)
		for _, adType := range item.Banner.AdTypes {
			key := creative_entity.NewCreativeTemplateKey()
			switch adType {
			case 6: // banner
				key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
				key.Title().SetOptional(true)
			case 9: // pic_text
				key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
				key.Title().SetRequiredCount(1)
				key.Desc().SetRequiredCount(1)

			default:
				continue
			}
			bidReq.AppendCreativeTemplateKey(key)
			keyId := key.Uint64()
			adxTemplateMap[keyId] = strconv.Itoa(adType)
		}
		bidReq.AddMediaExtraData(AdxTemplateKey, adxTemplateMap)
	}

	if item.Feed != nil {
		bidReq.SlotType = entity.SlotTypeFeeds
		adxTemplateMap := make(map[uint64]string)
		for _, adType := range item.Feed.AdTypes {
			key := creative_entity.NewCreativeTemplateKey()
			switch adType {
			case 1: // blog 普通图文
				key.Image().SetRequiredCount(9).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
				key.Desc().SetRequiredCount(1)
			case 2: // 2：blog_video 普通视频
				key.Video().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
				key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
				key.Title().SetRequiredCount(1)
			case 3: // 3：feed_activity 大图 card
				key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
				key.Title().SetRequiredCount(1)
				key.Desc().SetRequiredCount(1)
			case 4: // 4：feed_video 视频 card
				key.Video().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
				key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
				key.Title().SetRequiredCount(1)
				key.Desc().SetRequiredCount(1)
			default:
				continue
			}
			bidReq.AppendCreativeTemplateKey(key)
			keyId := key.Uint64()
			adxTemplateMap[keyId] = strconv.Itoa(adType)
		}
		bidReq.AddMediaExtraData(AdxTemplateKey, adxTemplateMap)
	}

	return nil
}

func (mb *WaxTrafficBroker) parseDevice(mediaBidRequest *wax_entity.WaxRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Device == nil {
		mb.log.Debugf("[WaxTrafficBroker]parseDevice, device nil")
		return nil
	}

	device := mediaBidRequest.Device
	bidReq.Device.RequestIp = device.Ip
	bidReq.Device.UserAgent = device.WebviewUa
	bidReq.Device.OsType = mb.mappingOsType(device.Os)
	bidReq.Device.OsVersion = device.Osv
	bidReq.Device.Model = device.Model
	bidReq.Device.Brand = device.Brand
	bidReq.Device.ScreenWidth = int32(device.Width)
	bidReq.Device.ScreenHeight = int32(device.Height)
	bidReq.Device.BootMark = device.BootMark
	bidReq.Device.UpdateMark = device.UpdateMark
	bidReq.Device.DeviceInitTime = device.InitializeMark
	bidReq.Device.DeviceType = mb.mappingDeviceType(device.DeviceType)
	bidReq.Device.OperatorType = mb.mappingCarrier(device.Carrier)
	bidReq.Device.ConnectionType = mb.mappingConnectionType(device.ConnectionType)

	if device.Ext != nil {
		deviceExt := device.Ext
		bidReq.Device.Idfa = deviceExt.Idfa
		bidReq.Device.IdfaMd5 = deviceExt.Idfa1
		if len(deviceExt.RyCaid) > 0 {
			bidReq.Device.Caids = append(bidReq.Device.Caids, deviceExt.RyCaid)
		}
		if deviceExt.GxCaid != "" {
			bidReq.Device.Caid = deviceExt.GxCaid
			bidReq.Device.Caids = append(bidReq.Device.Caids, deviceExt.GxCaid)
			parts := strings.Split(deviceExt.GxCaid, "_")
			if len(parts) == 2 {
				bidReq.Device.CaidRaw = parts[1]
				bidReq.Device.CaidVersion = parts[0]
			} else {
				bidReq.Device.CaidRaw = deviceExt.GxCaid
			}
		}
		if deviceExt.GxCaid1 != "" {
			bidReq.Device.CaidMd5 = deviceExt.GxCaid1
		} else {
			bidReq.Device.CaidMd5 = deviceExt.Caid1
		}
		bidReq.Device.Oaid = deviceExt.Oaid
		bidReq.Device.OaidMd5 = deviceExt.Oaid1
		bidReq.Device.ImeiMd5 = deviceExt.Imei
		bidReq.Device.AndroidIdMd5 = deviceExt.AndroidId
		bidReq.Device.Mac = deviceExt.Mac
		bidReq.Device.Aaid = deviceExt.AliAaid
	}

	return nil
}

func (mb *WaxTrafficBroker) parseApp(mediaBidRequest *wax_entity.WaxRequest,
	bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.App == nil {
		mb.log.Debugf("parseApp, vendor: %d, app nil", bidReq.GetMediaId())
		return nil
	}

	app := mediaBidRequest.App

	bidReq.App.AppName = app.Name
	bidReq.App.AppBundle = app.Name

	return nil
}

func (mb *WaxTrafficBroker) mappingCarrier(carrier string) entity.OperatorType {
	switch carrier {
	case "1":
		return entity.OperatorTypeChinaMobile
	case "2":
		return entity.OperatorTypeChinaUnicom
	case "3":
		return entity.OperatorTypeChinaTelecom
	case "4":
		return entity.OperatorTypeTietong
	}
	return entity.OperatorTypeUnknown
}

func (mb *WaxTrafficBroker) mappingOsType(s string) entity.OsType {
	switch strings.ToLower(s) {
	case "ios":
		return entity.OsTypeIOS
	case "android":
		return entity.OsTypeAndroid
	default:
		return entity.OsTypeUnknown
	}
}

func (mb *WaxTrafficBroker) mappingDeviceType(s int) entity.DeviceType {
	switch s {
	case 0:
		return entity.DeviceTypeMobile
	case 1:
		return entity.DeviceTypePad
	default:
		return entity.DeviceTypeUnknown
	}
}

func (mb *WaxTrafficBroker) mappingConnectionType(s int) entity.ConnectionType {
	switch s {
	case 1:
		return entity.ConnectionTypeNetEthernet
	case 2:
		return entity.ConnectionTypeWifi
	case 3:
		return entity.ConnectionTypeCellular
	case 4:
		return entity.ConnectionType2G
	case 5:
		return entity.ConnectionType3G
	case 6:
		return entity.ConnectionType4G
	case 7:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func (mb *WaxTrafficBroker) buildAppInfo(appInfo *entity.AppInfo, genericAd entity.GenericAd) *wax_entity.AppInfo {
	if appInfo == nil {
		return nil
	}
	return &wax_entity.AppInfo{
		Name:          appInfo.AppName,
		IconUrl:       appInfo.Icon,
		Size:          appInfo.PackageSize,
		DownloadUrl:   genericAd.GetDownloadUrl(),
		VersionName:   appInfo.AppVersion,
		PackageName:   appInfo.PackageName,
		Developer:     appInfo.Develop,
		PermissionUrl: appInfo.Permission,
		PrivacyUrl:    appInfo.Privacy,
		Description:   appInfo.AppDesc,
	}
}

func (mb *WaxTrafficBroker) processBannerMaterials(materials []*entity.Material, bannerExt *wax_entity.BannerExt) {
	for _, material := range materials {
		switch material.MaterialType {
		case entity.MaterialTypeImage:
			bannerExt.Burl = material.Url
		case entity.MaterialTypeTitle:
			bannerExt.Title = material.Data
		case entity.MaterialTypeDesc:
			bannerExt.Desc = material.Data
		}
	}
}

func (mb *WaxTrafficBroker) processFeedMaterials(materials []*entity.Material, feedExt *wax_entity.FeedExt) {
	for _, material := range materials {
		switch material.MaterialType {
		case entity.MaterialTypeCoverImage:
			feedExt.ImgUrls = append(feedExt.ImgUrls, material.Url)
		case entity.MaterialTypeImage:
			feedExt.ImgUrls = append(feedExt.ImgUrls, material.Url)
			feedExt.ImgWidth = int(material.Width)
			feedExt.ImgHeight = int(material.Height)
		case entity.MaterialTypeTitle:
			feedExt.Title = material.Data
		case entity.MaterialTypeDesc:
			feedExt.Desc = material.Data
			feedExt.MblogText = material.Data
		case entity.MaterialTypeVideo:
			feedExt.Video = &wax_entity.Video{
				Vurl:     material.Url,
				Width:    int(material.Width),
				Height:   int(material.Height),
				Duration: int(material.Duration),
			}
		}
	}
}

func (mb *WaxTrafficBroker) buildResponse(request *ad_service.AdRequest) *wax_entity.WaxResponse {
	bidResponse := &wax_entity.WaxResponse{}
	bidResponse.Id = request.GetRequestId()
	bidResponse.BidId = request.GetRequestId()
	bidResponse.SeatBid = make([]*wax_entity.SeatBid, 1)
	bidResponse.SeatBid[0] = &wax_entity.SeatBid{}
	bidResponse.SeatBid[0].Bid = make([]*wax_entity.Bid, 1)

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil || len(candidate.GetSelectedMaterialList()) == 0 {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		bid := &wax_entity.Bid{}
		bid.Id = strconv.Itoa(int(genericAd.GetAdId()))
		bid.ImpId = request.ImpressionId
		bidPrice := candidate.GetBidPrice()
		bid.Price = float64(bidPrice.Price)
		bid.Nurl = "" // TODO 竞胜回调地址

		bidExt := &wax_entity.BidExt{}
		// 限制pm和cm的条数为前三条
		pmList := candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
		if len(pmList) > 3 {
			pmList = pmList[:3]
		}
		bidExt.Pm = pmList

		cmList := candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen)
		if len(cmList) > 3 {
			cmList = cmList[:3]
		}
		bidExt.Cm = cmList

		vmList := candidate.ReplaceUrlMacroList(genericAd.GetVideoStartUrlList(), traffic, trackingGen)
		if len(vmList) > 3 {
			vmList = vmList[:3]
		}
		bidExt.Vm = vmList

		reqAdxTemplateMap := request.GetMediaExtraDataWithDefault(AdxTemplateKey, emptyAdxTemplateMap).(map[uint64]string)
		key1 := candidate.GetActiveCreativeTemplateKey()
		keyInt := key1.Uint64()
		adType := reqAdxTemplateMap[keyInt]
		adTypeInt, err := strconv.Atoi(adType)
		if err != nil {
			mb.log.Errorf("转换adType失败: %s", err.Error())
			// 如果没有匹配的adType，根据广告位类型设置默认值
			if request.SlotType == entity.SlotTypeBanner {
				adTypeInt = 6 // banner默认值
			} else if request.SlotType == entity.SlotTypeFeeds {
				adTypeInt = 1 // feed默认值
			} else {
				adTypeInt = 0 // 其他类型默认值
			}
		}

		materials := candidate.GetSelectedMaterialList()
		if request.SlotType == entity.SlotTypeBanner {
			bannerExt := &wax_entity.BannerExt{
				AdType: adTypeInt,
				Ldp:    candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
				Durl:   candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen),
			}
			mb.processBannerMaterials(materials, bannerExt)
			if appInfo := genericAd.GetAppInfo(); appInfo != nil && genericAd.GetLandingAction() == entity.LandingTypeDownload {
				bannerExt.Package = appInfo.PackageName
				bannerExt.AppInfo = mb.buildAppInfo(appInfo, genericAd)
			}
			bidExt.Banner = bannerExt
		} else if request.SlotType == entity.SlotTypeFeeds {
			feedExt := &wax_entity.FeedExt{
				AdType: adTypeInt,
				Ldp:    candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
				Durl:   candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen),
			}
			mb.processFeedMaterials(materials, feedExt)
			if appInfo := genericAd.GetAppInfo(); appInfo != nil && genericAd.GetLandingAction() == entity.LandingTypeDownload {
				feedExt.Package = appInfo.PackageName
				feedExt.AppInfo = mb.buildAppInfo(appInfo, genericAd)
			}
			bidExt.Feed = feedExt
		}

		bid.Ext = bidExt
		bidResponse.SeatBid[0].Bid[0] = bid
		break
	}

	mb.log.Debugf("WaxTrafficBroker Build Response end. broker response:[%v]", bidResponse)
	return bidResponse
}

func (mb *WaxTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		mb.log.Infof("WaxTrafficBroker Build Response start. bid response:[%v]", request.Response)
		request.Response.Dump("WaxTrafficBroker")
		mb.log.Infof("[WaxTrafficBroker] adCode:%+v", request.Response.GetTotalAdCandidateList().GetErrCodeMap())
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}

	bidResponse := mb.buildResponse(request)
	return mb.BuildHttpEasyJsonResponse(request, writer, bidResponse)
}

func (mb *WaxTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	_, err := writer.WriteWithStatus(204, nil)
	if err != nil {
		return err
	}
	return nil
}
