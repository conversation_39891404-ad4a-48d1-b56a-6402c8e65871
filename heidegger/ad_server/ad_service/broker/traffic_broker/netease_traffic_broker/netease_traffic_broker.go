package netease_traffic_broker

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"github.com/spf13/cast"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/netease_traffic_broker/netease_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/geo_parser"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
)

const (
	adxTemplateKey = "adxTemplate"
	bidFloorCurKey = "bidFloorCur"
)

type NeteaseTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        *zap.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewNeteaseTrafficBroker(mediaId utils.ID) *NeteaseTrafficBroker {
	return &NeteaseTrafficBroker{
		log:     zap.L().With(zap.String("broker", "NeteaseTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro: "${AUCTION_PRICE}",
		},
	}
}

func (n *NeteaseTrafficBroker) GetMediaId() utils.ID {
	return n.mediaId
}

func (n *NeteaseTrafficBroker) Do(adRequest *ad_service.AdRequest) error {
	adRequest.Response.SetResponseBuilder(n.SendResponse)
	adRequest.Response.SetFallbackResponseBuilder(n.SendFallbackResponse)
	adRequest.AdRequestMedia.WinPriceMacro = n.mediaMacro.MediaPriceMacro
	adRequest.AdRequestMedia.MediaMacro = n.mediaMacro

	body := adRequest.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return err_code.ErrRawRequest.Wrap(errors.New("request body empty"))
	}

	bidRequest := &netease_broker_entity.BidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		n.log.WithError(err).Error("Request body unmarshal failed")
		return err_code.ErrRawRequest
	}

	if adRequest.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		n.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	adRequest.SetMediaId(n.GetMediaId())
	adRequest.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		adRequest.SetRequestId(adRequest.GenerateRequestId())
	}
	/* 网易不按Tmax字段判断超时,按广告位置固定判断超时: 开屏/激励视频250, 信息流400
	if bidRequest.Tmax > 0 {
		adRequest.TMax = bidRequest.Tmax
	}
	*/

	n.parseApp(bidRequest, adRequest)
	n.parseUser(bidRequest, adRequest)
	n.parseDevice(bidRequest, adRequest)
	if err = n.parseImp(bidRequest, adRequest); err != nil {
		n.log.WithError(err).Debugf("BrokeRequest, parseImp failed")
		return err_code.ErrInvalidImpression.Wrap(err)
	}

	n.DoTrafficSample(adRequest, body)

	return nil
}

func (n *NeteaseTrafficBroker) parseApp(bidRequest *netease_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.App != nil {
		adRequest.App.AppName = bidRequest.App.Name
		adRequest.App.AppVersion = bidRequest.App.Ver
		adRequest.App.AppBundle = bidRequest.App.Bundle
	}
}

func (n *NeteaseTrafficBroker) parseUser(bidRequest *netease_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.User != nil {
		adRequest.UserId = bidRequest.User.Id
	}
}

func (n *NeteaseTrafficBroker) parseDevice(bidRequest *netease_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.Device != nil {
		adRequest.Device = ad_service.AdRequestDevice{
			OsType:         mappingOsType(bidRequest.Device.Os),
			DeviceType:     entity.DeviceTypeMobile,
			IsMobile:       true,
			BootMark:       bidRequest.Device.BootMark,
			UpdateMark:     bidRequest.Device.UpdateMark,
			VercodeAg:      bidRequest.Device.HuaweiAGVersion,
			VercodeHms:     bidRequest.Device.HMSCoreVersion,
			Idfa:           bidRequest.Device.Idfa,
			Imei:           bidRequest.Device.Imei,
			Mac:            bidRequest.Device.Mac,
			AndroidId:      bidRequest.Device.AndroidId,
			Oaid:           bidRequest.Device.Oaid,
			IdfaMd5:        bidRequest.Device.IdfaMd5,
			ImeiMd5:        bidRequest.Device.ImeiMd5,
			MacMD5:         bidRequest.Device.MacMd5,
			AndroidIdMd5:   bidRequest.Device.AndroidIdMd5,
			OaidMd5:        bidRequest.Device.OaidMd5,
			Model:          bidRequest.Device.Model,
			Brand:          bidRequest.Device.Manufacturer,
			Vendor:         bidRequest.Device.Model,
			ScreenHeight:   int32(bidRequest.Device.ScreenHeight),
			ScreenWidth:    int32(bidRequest.Device.ScreenWidth),
			OsVersion:      bidRequest.Device.OsVersion,
			UserAgent:      bidRequest.Device.UA,
			WebviewUA:      bidRequest.Device.UA,
			RequestIp:      bidRequest.Device.IP,
			IsIp6:          geo_parser.IsIPv6(bidRequest.Device.IP),
			ConnectionType: mappingConnectType(bidRequest.Device.ConnectionType),
			OperatorType:   mappingOperatorType(bidRequest.Device.Carrier),
			OperatorName:   mappingOperatorType(bidRequest.Device.Carrier).String(),
		}

		if bidRequest.Device.OptDeviceInfo != nil {
			if len(bidRequest.Device.OptDeviceInfo.DeviceType) > 0 {
				adRequest.Device.Model = bidRequest.Device.OptDeviceInfo.DeviceType
			}
			adRequest.Device.DeviceStartupTime = bidRequest.Device.OptDeviceInfo.StartTime
			adRequest.Device.DeviceInitTime = bidRequest.Device.OptDeviceInfo.DeviceInitTime
			adRequest.Device.SystemTotalMem = cast.ToInt64(bidRequest.Device.OptDeviceInfo.MemTotal) * 1024
			adRequest.Device.SystemTotalDisk = cast.ToInt64(bidRequest.Device.OptDeviceInfo.DiskTotal) * 1024
			adRequest.Device.DeviceUpgradeTime = bidRequest.Device.OptDeviceInfo.MbTime
			adRequest.Device.CountryCode = bidRequest.Device.OptDeviceInfo.Country
			adRequest.Device.Language = bidRequest.Device.OptDeviceInfo.Language
			adRequest.Device.TimeZone = cast.ToInt32(bidRequest.Device.OptDeviceInfo.TimeZone)
		}

		if bidRequest.Device.Geo != nil {
			adRequest.Device.Lat = cast.ToFloat64(bidRequest.Device.Geo.Lat)
			adRequest.Device.Lon = cast.ToFloat64(bidRequest.Device.Geo.Lon)
			adRequest.Device.GeoStandard = mappingGeoStandard(bidRequest.Device.Geo.Type)
		}

		if len(bidRequest.Device.Caid) > 0 {
			adRequest.Device.Caid = bidRequest.Device.Caid
			adRequest.Device.CaidRaw = bidRequest.Device.Caid
			if len(bidRequest.Device.CaidVersion) > 0 {
				adRequest.Device.CaidVersion = bidRequest.Device.CaidVersion
				adRequest.Device.Caid = bidRequest.Device.CaidVersion + "_" + bidRequest.Device.Caid
			}
		}
		if len(bidRequest.Device.PreCaid) > 0 {
			caid := bidRequest.Device.PreCaid
			if len(bidRequest.Device.PreCaidVersion) > 0 {
				caid = bidRequest.Device.PreCaidVersion + "_" + caid
			}
			if len(adRequest.Device.Caid) > 0 {
				adRequest.Device.Caids = append(adRequest.Device.Caids, caid)
			} else {
				adRequest.Device.Caid = caid
				adRequest.Device.CaidRaw = bidRequest.Device.PreCaid
				adRequest.Device.CaidVersion = bidRequest.Device.PreCaidVersion
			}
		}
		if len(bidRequest.Device.CaidMd5) > 0 {
			adRequest.Device.CaidMd5 = bidRequest.Device.CaidMd5
			adRequest.Device.CaidMd5Raw = bidRequest.Device.CaidMd5
			if len(bidRequest.Device.CaidVersion) > 0 {
				adRequest.Device.CaidMd5 = bidRequest.Device.CaidVersion + "_" + bidRequest.Device.CaidMd5
				if len(adRequest.Device.CaidVersion) < 1 {
					adRequest.Device.CaidVersion = bidRequest.Device.CaidVersion
				}
			}
		}
		if len(bidRequest.Device.PreCaidMd5) > 0 {
			caid := bidRequest.Device.PreCaidMd5
			if len(bidRequest.Device.PreCaidVersion) > 0 {
				caid = bidRequest.Device.PreCaidVersion + "_" + caid
			}
			if len(adRequest.Device.CaidMd5) > 0 {
				adRequest.Device.Caids = append(adRequest.Device.Caids, caid)
			} else {
				adRequest.Device.CaidMd5 = caid
				adRequest.Device.CaidMd5Raw = bidRequest.Device.PreCaidMd5
				if len(adRequest.Device.CaidVersion) < 1 {
					adRequest.Device.CaidVersion = bidRequest.Device.PreCaidVersion
				}
			}
		}

		adRequest.App.MediaInstalledAppIds = append(adRequest.App.MediaInstalledAppIds, bidRequest.Device.AppList...)
	}
}

func (n *NeteaseTrafficBroker) parseImp(bidRequest *netease_broker_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	for _, imp := range bidRequest.Imp {
		if imp == nil {
			continue
		}
		adRequest.ImpressionId = imp.Id
		adRequest.SetMediaSlotKey(imp.TagId)
		// format: os_tagId
		adRequest.SetMediaSlotKeyMapping(fmt.Sprintf("%d_%s", adRequest.Device.OsType, imp.TagId))
		adRequest.BidFloor = uint32(imp.BidFloor)
		adRequest.BidType = entity.BidTypeCpm
		bidFloorCur := imp.BidFloorCur
		allowStyle := imp.AllowStyle

		if imp.Pmp != nil && imp.Pmp.PrivateAuction == 1 {
			for _, deal := range imp.Pmp.Deals {
				adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
					DealId:   deal.Id,
					BidFloor: int64(deal.BidFloor),
				})
				bidFloorCur = deal.BidFloorCur
				allowStyle = append(allowStyle, deal.AllowStyle...)
			}
		}
		if bidFloorCur == "" && len(bidRequest.Cur) > 0 {
			bidFloorCur = bidRequest.Cur[0]
		}
		if bidFloorCur == "" {
			bidFloorCur = "CNY"
		}
		adRequest.AddMediaExtraString(bidFloorCurKey, bidFloorCur)

		adxTemplateMap := make(map[uint64]int)
		for _, style := range allowStyle {
			key := creative_entity.NewCreativeTemplateKey()
			switch style {
			case 1010017, 1010021, 1010018, 1010010, 1010011, 1010082, 1010083, 1010087, 1010090, 1010091, 1040056, 1040058, 2010006, 2010008, 3010001, 3010003, 3020001, 3020002, 3020008, 3030001, 3030003, 3030008, 3010005, 3010007, 3030013, 4010024, 4010025: //1个图片 横版
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			case 1010025, 1010113, 1010031, 1010046, 1010092, 1040042, 1040044, 1040046, 1040048, 1040050, 1040052, 1040054, 1010111, 1040031, 1040022, 1040026, 3030014, 1040018, 1040019, 4010026, 4010022, 4010023: //1个图片 竖版
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			case 1010012, 1010029, 1010105, 1010106, 1040057: //1个视频+1个图片 横版
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			case 1010047: //1个视频+1个图片 竖版
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			case 1010088, 3030002: //1个视频 横版
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			case 1010100, 1010101, 1040051, 1040053, 1010112, 1040027, 1080011, 1080012, 1040043, 1040045, 1040047, 1040055, 1040032, 4010027: //1个视频 竖版
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL).SetOptional(true)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			case 1010076, 1010077, 1010078, 1010079: //2个图片 横版
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Image().AddRequiredCount(2).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			case 1010084, 1010085, 2010007, 3010002, 3010006: //3个图片 横版
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Image().AddRequiredCount(3).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			default:
				continue
			}

			if key.Uint64() != 0 {
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = style
			}
		}

		// 未知style设置默认图片
		if len(adxTemplateMap) < 1 {
			for _, style := range allowStyle {
				key := creative_entity.NewCreativeTemplateKey()
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				adRequest.AppendCreativeTemplateKey(key)
				adxTemplateMap[key.Uint64()] = style
				break
			}
		}

		if len(adxTemplateMap) < 1 {
			continue
		}
		adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)
		return nil
	}

	return errors.New("unknown style")
}

func (n *NeteaseTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		n.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("NeteaseTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return n.SendFallbackResponse(request, writer)
	}

	bidResponse, err := n.buildResponse(request)
	if err != nil {
		n.log.WithError(err).Error("buildResponse err")
		return err
	}

	marshal, err := sonic.Marshal(bidResponse)
	if err != nil {
		n.log.WithError(err).Error("Marshal response err")
		return n.SendFallbackResponse(request, writer)
	}

	if request.IsDebug {
		n.log.Infof("SendResponse success, response:%s", string(marshal))
	}

	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	if _, err := writer.WriteWithStatus(200, marshal); err != nil {
		return err
	}

	n.DoTrafficResponseSample(request, marshal)

	return nil
}

func (n *NeteaseTrafficBroker) buildResponse(request *ad_service.AdRequest) (*netease_broker_entity.BidResponse, error) {
	bidResponse := &netease_broker_entity.BidResponse{
		Id:      request.GetRequestId(),
		SeatBid: make([]netease_broker_entity.SeatBid, 0),
		BidId:   request.GetRequestId(),
		Cur:     request.GetMediaExtraString(bidFloorCurKey, "CNY"),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)
		resBid := &netease_broker_entity.Bid{
			Id:     "0",
			ImpId:  request.ImpressionId,
			Price:  float64(candidate.GetBidPrice().Price),
			PVM:    candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			ClickM: candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
			Ext: &netease_broker_entity.Ext{
				LinkUrl:      genericAd.GetLandingUrl(),
				Deeplink:     genericAd.GetDeepLinkUrl(),
				IsFullScreen: 0,
				Advertiser: &netease_broker_entity.Advertiser{
					Id:          net_utils.GetDomainFromUrl(genericAd.GetLandingUrl()),
					Industry:    "01",
					SubIndustry: "0103",
				},
			},
		}

		if creative.GetCreativeId() != 0 {
			resBid.Crid = strconv.FormatInt(int64(creative.GetCreativeId()), 10)
		} else {
			resBid.Crid = creative.GetCreativeKey()
		}

		if candidate.GetIndexDeal() != nil {
			resBid.DealId = candidate.GetIndexDeal().DealId
		}

		if len(genericAd.GetDownloadUrl()) > 0 {
			resBid.Ext.ActionType = 1
			resBid.Ext.AndroidUrl = genericAd.GetDownloadUrl()
			if request.Device.OsType == entity.OsTypeIOS {
				resBid.Ext.IosUrl = genericAd.GetDownloadUrl()
			}
		}
		if len(genericAd.GetDeepLinkUrl()) > 0 {
			resBid.Ext.AppType = 1
		}
		for _, s := range genericAd.GetAppDownloadStartedMonitorList() {
			resBid.EventTracker = append(resBid.EventTracker, &netease_broker_entity.EventTracker{
				Type: 1,
				Url:  s,
			})
		}
		for _, s := range genericAd.GetAppDownloadFinishedMonitorList() {
			resBid.EventTracker = append(resBid.EventTracker, &netease_broker_entity.EventTracker{
				Type: 2,
				Url:  s,
			})
		}
		for _, s := range genericAd.GetAppInstallStartMonitorList() {
			resBid.EventTracker = append(resBid.EventTracker, &netease_broker_entity.EventTracker{
				Type: 3,
				Url:  s,
			})
		}
		for _, s := range genericAd.GetAppOpenMonitorList() {
			resBid.EventTracker = append(resBid.EventTracker, &netease_broker_entity.EventTracker{
				Type: 4,
				Url:  s,
			})
		}
		for _, s := range genericAd.GetDeepLinkMonitorList() {
			resBid.EventTracker = append(resBid.EventTracker, &netease_broker_entity.EventTracker{
				Type: 5,
				Url:  s,
			})
		}
		for _, s := range genericAd.GetVideoStartUrlList() {
			resBid.EventTracker = append(resBid.EventTracker, &netease_broker_entity.EventTracker{
				Type: 6,
				Url:  s,
			})
		}
		for _, s := range genericAd.GetVideoCloseUrlList() {
			resBid.EventTracker = append(resBid.EventTracker, &netease_broker_entity.EventTracker{
				Type: 8,
				Url:  s,
			})
		}

		if genericAd.GetAppInfo() != nil {
			resBid.Ext.AppName = genericAd.GetAppInfo().AppName
			resBid.Ext.PackageName = genericAd.GetAppInfo().PackageName
			resBid.Ext.PkgDeveloper = genericAd.GetAppInfo().Develop
			resBid.Ext.PkgVersion = genericAd.GetAppInfo().AppVersion
			resBid.Ext.Authority = genericAd.GetAppInfo().Permission
			resBid.Ext.PrivacyPolicy = genericAd.GetAppInfo().Privacy
			if len(genericAd.GetAppInfo().AppName) > 0 {
				resBid.Ext.Advertiser.Id = genericAd.GetAppInfo().AppName
			}

			if genericAd.GetAppInfo().WechatExt != nil {
				resBid.Ext.PrgId = genericAd.GetAppInfo().WechatExt.ProgramId
				resBid.Ext.PrgPath = genericAd.GetAppInfo().WechatExt.ProgramPath
				resBid.Ext.AppType = 2
			}
		}

		adxTemplateMap := request.GetMediaExtraDataWithDefault(adxTemplateKey, make(map[uint64]int)).(map[uint64]int)
		resBid.Ext.Style = adxTemplateMap[uint64(candidate.GetActiveCreativeTemplateKey())]
		for _, material := range candidate.GetSelectedMaterialList() {
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				resBid.Ext.Title = material.Data
			case entity.MaterialTypeDesc:
				resBid.Ext.SubTitle = material.Data
			case entity.MaterialTypeIcon:
				resBid.Ext.Icon = material.Url
			case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
				resBid.Ext.Adm = append(resBid.Ext.Adm, &netease_broker_entity.Adm{
					Url:  material.Url,
					Type: 0,
					W:    int(material.Width),
					H:    int(material.Height),
				})
			case entity.MaterialTypeVideo:
				resBid.Ext.Adm = append(resBid.Ext.Adm, &netease_broker_entity.Adm{
					Url:  material.Url,
					Type: 1,
					W:    int(material.Width),
					H:    int(material.Height),
				})
			default:
			}
		}

		bidResponse.SeatBid = append(bidResponse.SeatBid,
			netease_broker_entity.SeatBid{Bid: []*netease_broker_entity.Bid{resBid}})
		break
	}

	if len(bidResponse.SeatBid) < 1 {
		return nil, err_code.ErrNoCandidate
	}

	return bidResponse, nil
}

func (n *NeteaseTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		n.log.Infof("Build Fallback Response start. bid response: [%v]", request.Response)
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	_, err := writer.WriteWithStatus(204, nil)
	if err != nil {
		return err
	}
	return nil
}

func mappingConnectType(connectionType string) entity.ConnectionType {
	switch strings.ToLower(connectionType) {
	case "wifi":
		return entity.ConnectionTypeWifi
	case "2g":
		return entity.ConnectionType2G
	case "3g":
		return entity.ConnectionType3G
	case "4g":
		return entity.ConnectionType4G
	case "5g":
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingOperatorType(carrier string) entity.OperatorType {
	switch carrier {
	case "cm":
		return entity.OperatorTypeChinaMobile
	case "cu":
		return entity.OperatorTypeChinaUnicom
	case "ct":
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingOsType(os string) entity.OsType {
	switch os {
	case "ios":
		return entity.OsTypeIOS
	case "android":
		return entity.OsTypeAndroid
	default:
		return entity.OsTypeUnknown
	}
}

func mappingGeoStandard(t int) int {
	switch t {
	case 2:
		return 0
	case 1:
		return 1
	default:
		return 2
	}
}
