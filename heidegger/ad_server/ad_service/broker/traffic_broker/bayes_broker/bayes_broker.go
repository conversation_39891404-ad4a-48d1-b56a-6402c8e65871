package bayes_broker

import (
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/slice_utils"
	"strings"
)

type (
	BayesBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId       utils.ID
		WinPriceMacro string
	}
)

func NewBayesBroker(mediaId utils.ID) *BayesBroker {
	return &BayesBroker{
		mediaId:       mediaId,
		WinPriceMacro: "__DSP_PRICE__",
	}
}

func (mb *BayesBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *BayesBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro
	return mb.ParseAdRequest(request)
}

func (mb *BayesBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[BayesBroker]request body empty")
	}

	bidRequest := &BayesRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[BayesBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if err = mb.buildRequest(request, bidRequest); err != nil {
		zap.L().Debug("[BayesBroker]BrokeRequest, parseUser failed")
		return err
	}
	mb.DoTrafficSample(request, body)
	return nil
}

func (mb *BayesBroker) buildRequest(request *ad_service.AdRequest, bidRequest *BayesRequest) error {
	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[BayesBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	request.SetRequestId(bidRequest.Sid)
	if len(bidRequest.Sid) < 1 {
		request.SetRequestId(utils.NewUUID())
	}
	request.SetMediaId(mb.mediaId)

	if err := mb.parseImp(bidRequest, request); err != nil {
		zap.L().Debug("[BayesBroker] BrokeRequest, parseImp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, request); err != nil {
		zap.L().Debug("[BayesBroker]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseApp(bidRequest, request); err != nil {
		zap.L().Debug("[BayesBroker]BrokeRequest, parseApp failed")
		return err
	}

	return nil
}

func (mb *BayesBroker) parseImp(mediaRequest *BayesRequest, bidReq *ad_service.AdRequest) error {
	if mediaRequest.Adspot == nil {
		return errors.New("no imp")
	}
	imp := mediaRequest.Adspot
	bidReq.SetMediaSlotKey(imp.Id)
	bidReq.BidFloor = uint32(imp.Bidfloor)
	bidReq.UseHttps = true
	switch imp.Adtype {
	case 1:
		bidReq.SlotType = entity.SlotTypeOpening
	case 2:
		bidReq.SlotType = entity.SlotTypeFeeds
	case 3:
		bidReq.SlotType = entity.SlotTypeVideoPause
	case 4:
		bidReq.SlotType = entity.SlotTypeBanner
	case 5:
		bidReq.SlotType = entity.SlotTypePopup
	case 6:
		bidReq.SlotType = entity.SlotTypeRewardVideo
	default:
		bidReq.SlotType = entity.SlotTypeUnknown
	}
	bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
		Width:  int64(imp.W),
		Height: int64(imp.H),
	})
	bidReq.AddMediaExtraInt64(bidReq.SlotType.String(), imp.Adtype)

	for _, v := range imp.SupportCreativeType {
		switch v {
		case 1, 3, 4, 6, 16:
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraInt64(bidReq.SlotType.String()+"_"+key.String(), v)
		case 2, 11, 15, 10:
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().SetRequiredCount(2).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraInt64(bidReq.SlotType.String()+"_"+key.String(), v)
		case 7:
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraInt64(bidReq.SlotType.String()+"_"+key.String(), v)
		case 8:
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().SetRequiredCount(3).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraInt64(bidReq.SlotType.String()+"_"+key.String(), v)
		case 9:
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Video().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraInt64(bidReq.SlotType.String()+"_"+key.String(), v)
		}
	}
	if len(mediaRequest.Deal) > 0 {
		for _, d := range mediaRequest.Deal {
			sd := ad_service.SourceDeal{
				DealId:   d.DealId,
				BidFloor: int64(d.DealPrice),
			}
			bidReq.SourceDeal = append(bidReq.SourceDeal, sd)
		}
	}

	return nil
}

func (mb *BayesBroker) parseDevice(mediaRequest *BayesRequest, bidReq *ad_service.AdRequest) error {

	device := mediaRequest.Device
	bidReq.Device.UserAgent = device.Ua
	bidReq.Device.RequestIp = device.Ip
	if strings.Contains(device.Ip, ":") {
		bidReq.Device.IsIp6 = true
	}
	bidReq.Device.Lat = device.Lat
	bidReq.Device.Lon = device.Lon
	bidReq.Device.ScreenWidth = device.Sw
	bidReq.Device.ScreenHeight = device.Sh
	bidReq.Device.PPI = device.Ppi
	bidReq.Device.DPI = device.Dpi
	bidReq.Device.ScreenDensity = device.Density
	bidReq.Device.Model = device.Model
	bidReq.Device.Vendor = device.Make
	bidReq.Device.OsType = func() entity.OsType {
		switch device.Os {
		case 2:
			return entity.OsTypeAndroid
		case 1:
			return entity.OsTypeIOS
		default:
			return entity.OsTypeUnknown
		}
	}()
	bidReq.Device.OsVersion = device.Osv
	bidReq.Device.Imei = device.Imei
	bidReq.Device.ImeiMd5 = device.ImeiMd5
	bidReq.Device.Mac = device.Mac
	bidReq.Device.MacMd5 = device.MacMd5
	bidReq.Device.AndroidId = device.AndroidId
	bidReq.Device.AndroidIdMd5 = device.AndroidIdMd5
	bidReq.Device.Oaid = device.Oaid
	bidReq.Device.OaidMd5 = device.OaidMd5
	bidReq.Device.Idfa = device.Idfa
	bidReq.Device.IdfaMd5 = device.IdfaMd5
	bidReq.Device.Aaid = device.Aaid
	bidReq.Device.Paid = device.Paid
	bidReq.Device.OperatorType = func() entity.OperatorType {
		switch device.Carrier {
		case "46000", "46002", "46004", "46007", "46008", "45412":
			return entity.OperatorTypeChinaMobile
		case "46001", "46006", "46009", "46010":
			return entity.OperatorTypeChinaUnicom
		case "46003", "46005", "46011":
			return entity.OperatorTypeChinaTelecom
		case "46015", "46020":
			return entity.OperatorTypeTietong
		default:
			return entity.OperatorTypeUnknown
		}
	}()
	bidReq.Device.ConnectionType = func() entity.ConnectionType {
		switch device.Network {
		case 1:
			return entity.ConnectionTypeWifi
		case 2:
			return entity.ConnectionType2G
		case 3:
			return entity.ConnectionType3G
		case 4:
			return entity.ConnectionType4G
		case 5:
			return entity.ConnectionType5G
		case 6:
			return entity.ConnectionTypeNetEthernet
		default:
			return entity.ConnectionTypeUnknown
		}
	}()

	bidReq.Device.DeviceType = func() entity.DeviceType {
		switch device.Devicetype {
		case 1:
			return entity.DeviceTypeMobile
		case 2:
			return entity.DeviceTypePad
		case 4:
			return entity.DeviceTypeOtt
		default:
			return entity.DeviceTypeUnknown
		}
	}()

	bidReq.Device.BootMark = device.BootMark
	bidReq.Device.UpdateMark = device.UpdateMark
	bidReq.Device.VercodeHms = device.HmsCode
	bidReq.Device.VercodeAg = device.AgCode
	bidReq.Device.DeviceInitTime = device.BirthTime
	bidReq.Device.DeviceStartupTime = device.BootTime
	bidReq.Device.DeviceUpgradeTime = device.UpdateTime

	if len(device.Caid) > 0 {
		bidReq.Device.Caid = device.CaidVersion + "_" + device.Caid
		bidReq.Device.Caids = append(bidReq.Device.Caids, bidReq.Device.Caid)
	}
	if len(device.CaidList) > 0 {
		for _, caid := range device.CaidList {
			bidReq.Device.Caid = caid.Version + "_" + caid.Caid
			bidReq.Device.Caids = append(bidReq.Device.Caids, bidReq.Device.Caid)
		}
	}
	return nil

}

func (mb *BayesBroker) parseApp(mediaRequest *BayesRequest, bidReq *ad_service.AdRequest) error {
	if mediaRequest.Media != nil {
		bidReq.App.AppName = mediaRequest.Media.Cname
		bidReq.App.AppBundle = mediaRequest.Media.Bundle
		bidReq.App.AppVersion = mediaRequest.Media.Appver
	}

	if mediaRequest.Ext != nil {
		if len(mediaRequest.Ext.Suplist) > 0 {
			bidReq.App.MediaInstalledAppIds = slice_utils.Int64ToStringSlice(mediaRequest.Ext.Suplist)
		}
	}
	return nil
}

func (mb *BayesBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("BayesBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("BayesBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}
	bidResponse, err := mb.buildResponse(request)
	if err != nil {
		zap.L().Error("BayesBroker Error in buildResponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := []byte(bidResponse.String())
	writer.SetHeader("Content-Type", "application/json")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	mb.DoTrafficResponseSample(request, data)
	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[BayesBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", responseStr)))))
	}

	return nil
}

func (mb *BayesBroker) buildResponse(request *ad_service.AdRequest) (*BayesResponse, error) {
	response := &BayesResponse{
		Sid:  request.GetRequestId(),
		Code: 200,
		Bid:  []*BayesResponseSeatBid{},
	}
	for _, candidate := range request.Response.GetAdCandidateList() {
		creative := candidate.GetCreative()
		if creative == nil {
			continue
		}
		bid := &BayesResponseSeatBid{
			Impid:       request.ImpressionId,
			Price:       int32(candidate.GetBidPrice().Price),
			Crid:        candidate.GetCreative().GetCreativeId().String(),
			Action:      1,
			Link:        candidate.GetLandingUrl(),
			Deeplink:    candidate.GetDeepLinkUrl(),
			Imptk:       candidate.GetMacroReplaceImpressionMonitorList(),
			Clicktk:     candidate.GetMacroReplaceClickMonitorList(),
			Deeplinktk:  candidate.GetMacroReplaceDeepLinkMonitorList(),
			DownloadApp: &BayesResponseApp{},
		}
		bid.Adtype, _ = request.GetMediaExtraInt64(request.SlotType.String())
		tempalteKey := candidate.GetActiveCreativeTemplateKey()
		bid.CreativeType, _ = request.GetMediaExtraInt64(request.SlotType.String() + "_" + tempalteKey.String())

		switch candidate.GetLandingAction() {
		case entity.LandingTypeDownload:
			bid.Adtype = 2
			bid.Downloadtk = candidate.GetMacroReplaceAppDownloadStartedMonitorList()
			bid.Downloadedtk = candidate.GetMacroReplaceAppDownloadFinishedMonitorList()
			bid.Installtk = candidate.GetMacroReplaceAppInstallStartMonitorList()
			bid.Installedtk = candidate.GetMacroReplaceAppInstalledMonitorList()
		case entity.LandingTypeWeChatProgram:
			bid.Adtype = 4
		}

		if candidate.GetIndexDeal() != nil {
			bid.Deal = &BayesResponseDeal{
				DealId:    candidate.GetIndexDeal().DealId,
				DealPrice: int(candidate.GetIndexDeal().DealPrice),
			}
		}

		if candidate.GetAppInfo() != nil {
			bid.Adsource = candidate.GetAppInfo().AppName
			bid.Logo = candidate.GetAppInfo().Icon
			bid.PackageName = candidate.GetAppInfo().PackageName

			bid.DownloadApp.Name = candidate.GetAppInfo().AppName
			bid.DownloadApp.Appver = candidate.GetAppInfo().AppVersion
			bid.DownloadApp.Developer = candidate.GetAppInfo().Develop
			bid.DownloadApp.PermissionUrl = candidate.GetAppInfo().Permission
			bid.DownloadApp.Desc = candidate.GetAppInfo().AppDesc
			bid.DownloadApp.DescUrl = candidate.GetAppInfo().AppDescURL
			bid.DownloadApp.PrivacyUrl = candidate.GetAppInfo().Privacy
			bid.DownloadApp.Bundle = candidate.GetAppInfo().PackageName
			bid.DownloadApp.Size = candidate.GetAppInfo().PackageSize
			bid.DownloadApp.Icon = candidate.GetAppInfo().Icon
			if len(candidate.GetAppInfo().PermissionDesc) > 0 {
				bid.DownloadApp.PermissionsDesc = map[string]string{}
				for _, permissionDesc := range candidate.GetAppInfo().PermissionDesc {
					bid.DownloadApp.PermissionsDesc[permissionDesc.PermissionLab] = permissionDesc.PermissionDesc
				}

			}
			if candidate.GetAppInfo().WechatExt != nil {
				bid.Ext = &BayesResponseExt{
					UserName: candidate.GetAppInfo().WechatExt.ProgramId,
					Path:     candidate.GetAppInfo().WechatExt.ProgramPath,
				}
			}
		}

		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				if len(rsc.Url) > 0 {
					bid.Image = append(bid.Image, rsc.Url)
					bid.Width = rsc.Width
					bid.Height = rsc.Height
				}
			case entity.MaterialTypeTitle:
				bid.Title = rsc.Data
			case entity.MaterialTypeDesc:
				bid.Desc = rsc.Data
			case entity.MaterialTypeVideo:
				if len(rsc.Url) > 0 {
					bid.Vurl = rsc.Url
					bid.Width = rsc.Width
					bid.Height = rsc.Height
					bid.Duration = int64(rsc.Duration)
				}
			default:

			}
		}

		if len(bid.Vurl) > 0 && len(bid.Image) > 0 {
			bid.VideoImage = bid.Image[0]
		}

		if bid.Duration > 0 {
			for _, delayMonitor := range candidate.GetGenericAd().GetDelayMonitorUrlList() {
				event := entity.GetVideoTrackingEvent(delayMonitor.Delay, int(bid.Duration))
				switch event {
				case entity.KVideoTrackingEventStart:
					bid.Starttk = append(bid.Starttk, delayMonitor.Url)
				case entity.KVideoTrackingEventFirst:
					bid.Firsttk = append(bid.Firsttk, delayMonitor.Url)
				case entity.KVideoTrackingEventMid:
					bid.Midtk = append(bid.Midtk, delayMonitor.Url)
				case entity.KVideoTrackingEventThird:
					bid.Thirdtk = append(bid.Thirdtk, delayMonitor.Url)
				case entity.KVideoTrackingEventComplete:
					bid.Endtk = append(bid.Endtk, delayMonitor.Url)
				}
			}
		}
		response.Bid = []*BayesResponseSeatBid{bid}
	}

	return response, nil
}

func (mb *BayesBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[BayesBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json")
	writer.WriteWithStatus(204, nil)
	return nil
}
