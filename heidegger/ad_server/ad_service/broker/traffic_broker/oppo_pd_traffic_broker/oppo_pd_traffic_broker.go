package oppo_pd_traffic_broker

import (
	"fmt"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/oppo_pd_traffic_broker/oppo_pd_broker_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"strings"
)

type (
	OppoPdTrafficBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId utils.ID
		host    string

		WinPriceMacro string
	}
)

func NewOppoPdTrafficBroker(mediaId utils.ID) *OppoPdTrafficBroker {
	return &OppoPdTrafficBroker{
		mediaId:       mediaId,
		WinPriceMacro: "${AUCTION_PRICE}",
	}
}

func (mb *OppoPdTrafficBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *OppoPdTrafficBroker) Do(request *ad_service.AdRequest) error {
	return mb.ParseAdRequest(request)
}

func (mb *OppoPdTrafficBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro

	body := request.RawHttpRequest.GetBodyContent()

	if len(body) == 0 {
		return fmt.Errorf("[OppoPdTrafficBroker]request body empty")
	}

	bidRequest := &oppo_pd_broker_entity.BidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[OppoPdTrafficBroker]BrokeRequest, Request body unmarshal failed, err:, body", zap.Error(err), zap.String("param2", fmt.Sprintf("%v", string(body))))
		return fmt.Errorf("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[OppoPdTrafficBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	request.SetRequestId(bidRequest.ID)
	if len(bidRequest.ID) < 1 {
		request.SetRequestId(utils.NewUUID())
	}
	request.SetMediaId(mb.mediaId)

	if len(bidRequest.User.ID) > 0 {
		request.UserId = bidRequest.User.ID
	}

	if err = mb.parseDevice(request, bidRequest); err != nil {
		return err
	}

	if err = mb.parseApp(request, bidRequest); err != nil {
		return err
	}

	if err = mb.parseImp(request, bidRequest); err != nil {
		zap.L().Debug("[OppoPdTrafficBroker] BrokeRequest, parseImp failed")
		return err
	}

	mb.DoTrafficSample(request, body)

	return nil

}

func (mb *OppoPdTrafficBroker) parseImp(request *ad_service.AdRequest, bidRequest *oppo_pd_broker_entity.BidRequest) error {
	if len(bidRequest.Imp) == 0 {
		return fmt.Errorf("[OppoPdTrafficBroker]parseImp, imp nil")
	}
	item := bidRequest.Imp[0]

	request.ImpressionId = item.ID
	request.SetMediaSlotKey(item.TagID)
	//request.SlotWidth = uint32(item.Width)
	//request.SlotHeight = uint32(item.Height)
	//request.VideoMinDuration = item.MinPlaytime
	//request.VideoMaxDuration = item.MaxPlaytime
	minBidFloor := uint32(0)
	if len(item.PMP.Deals) > 0 {
		for _, deal := range item.PMP.Deals {
			if uint32(deal.BidFloor) < minBidFloor {
				minBidFloor = uint32(deal.BidFloor)
			}
			sourceDeal := ad_service.SourceDeal{
				DealId:   deal.ID,
				BidFloor: int64(deal.BidFloor),
			}
			request.SourceDeal = append(request.SourceDeal, sourceDeal)
			//mb.fixTemplateByDeal(request, deal.ID)
		}
	}

	request.AdxTemplateId = append(request.AdxTemplateId, "2", "25", "87", "89", "58")
	mb.addTemplateKeyByAdx(request, request.AdxTemplateId)

	request.BidFloor = minBidFloor
	return nil
}

func (mb *OppoPdTrafficBroker) addTemplateKeyByAdx(request *ad_service.AdRequest, adxTemplateId []string) {
	for _, tempId := range adxTemplateId {
		switch tempId {
		case "2":
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			request.AppendCreativeTemplateKey(key)
		case "25":
			key := creative_entity.NewCreativeTemplateKey()
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			request.AppendCreativeTemplateKey(key)
		case "87":
			key := creative_entity.NewCreativeTemplateKey()
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1)
			request.AppendCreativeTemplateKey(key)
		case "89":
			key := creative_entity.NewCreativeTemplateKey()
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			request.AppendCreativeTemplateKey(key)
		case "58":
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			key.Title().AddRequiredCount(1)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			request.AppendCreativeTemplateKey(key)

		}
	}
}

func (mb *OppoPdTrafficBroker) fixTemplateByDeal(request *ad_service.AdRequest, dealId string) {
	switch dealId {
	case "1":
		key := creative_entity.NewCreativeTemplateKey()
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		key.Title().AddRequiredCount(1)
		key.Desc().AddRequiredCount(1)
		request.AppendCreativeTemplateKey(key)
		request.AdxTemplateId = append(request.AdxTemplateId, "2")
	case "1867092", "1867093":
		key := creative_entity.NewCreativeTemplateKey()
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
		key.Title().AddRequiredCount(1)
		key.Desc().AddRequiredCount(1)
		request.AppendCreativeTemplateKey(key)
		request.AdxTemplateId = append(request.AdxTemplateId, "58")

	}
}

func (mb *OppoPdTrafficBroker) parseApp(request *ad_service.AdRequest, bidRequest *oppo_pd_broker_entity.BidRequest) error {

	if bidRequest.App == nil {
		return nil
	}

	app := bidRequest.App

	request.App.AppName = app.Name
	request.App.AppBundle = app.Bundle
	request.App.AppVersion = app.Ver

	return nil
}

func (mb *OppoPdTrafficBroker) parseDevice(request *ad_service.AdRequest, bidRequest *oppo_pd_broker_entity.BidRequest) error {

	if bidRequest.Device == nil {
		zap.L().Error("parseDevice, device nil", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", bidRequest)))))
		return fmt.Errorf("parseDevice failed")
	}

	device := bidRequest.Device
	request.Device.RequestIp = device.IP
	request.Device.UserAgent = device.UA
	request.Device.DeviceType = mb.getDeviceType(device.DeviceType)
	request.Device.OsType = mb.getOsType(device.OS)
	request.Device.OsVersion = device.OSV
	request.Device.Brand = device.Make
	request.Device.Model = device.Model
	request.Device.ScreenWidth = int32(device.ScreenWidth)
	request.Device.ScreenHeight = int32(device.ScreenHeight)
	request.Device.ConnectionType = mb.getConnectionType(device.ConnectionType)
	request.Device.ImeiMd5 = device.Didmd5
	request.Device.Oaid = device.OAID
	request.Device.OaidMd5 = device.OAIDMD5
	request.Device.BootMark = device.BootMark
	request.Device.UpdateMark = device.UpdateMark
	request.Device.OperatorType = mb.getCarrier(device.Carrier)
	return nil

}

func (mb *OppoPdTrafficBroker) getCarrier(carrier string) entity.OperatorType {
	switch carrier {
	case "mobile":
		return entity.OperatorTypeChinaMobile
	case "unicom":
		return entity.OperatorTypeChinaUnicom
	case "telecom":
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func (mb *OppoPdTrafficBroker) getDeviceType(deviceType int) entity.DeviceType {
	switch deviceType {
	case 1, 4, 6:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePc
	case 5:
		return entity.DeviceTypePad
	case 3, 7:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeMobile
	}
}

func (mb *OppoPdTrafficBroker) getConnectionType(connectionType int) entity.ConnectionType {
	switch connectionType {
	case 1:
		return entity.ConnectionTypeNetEthernet
	case 2:
		return entity.ConnectionTypeWifi
	case 3:
		return entity.ConnectionTypeCellular
	case 4:
		return entity.ConnectionType2G
	case 5:
		return entity.ConnectionType3G
	case 6:
		return entity.ConnectionType4G
	case 7:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func (mb *OppoPdTrafficBroker) getOsType(osType string) entity.OsType {
	switch strings.ToLower(osType) {
	case "android":
		return entity.OsTypeAndroid
	case "ios":
		return entity.OsTypeIOS
	default:
		return entity.OsTypeAndroid
	}
}

func (mb *OppoPdTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("OppoPdTrafficBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("OppoPdTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}

	bidResponse := &oppo_pd_broker_entity.BidResponse{
		ID:    request.GetRequestId(),
		BidID: request.GetRequestId(),
		NBR:   0,
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()

		if genericAd == nil || creative == nil {
			continue
		}

		if len(candidate.GetSelectedMaterialList()) == 0 {
			zap.L().Error("[OppoPdTrafficBroker]no matierial")
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		bidPrice := candidate.GetBidPrice()

		seatBid := oppo_pd_broker_entity.SeatBid{
			Bid:  nil,
			Seat: "",
		}

		bid := oppo_pd_broker_entity.Bid{
			ID:               request.GetRequestId(),
			ImpID:            request.ImpressionId,
			Price:            float64(bidPrice.Price),
			NURL:             "",
			LURL:             "",
			Adm:              "",
			TrackerVisualize: 0,
			BeginTime:        0,
			EndTime:          0,
			Ext:              oppo_pd_broker_entity.Ext{},
		}

		//bid.CrID = strconv.Itoa(int(creative.GetCreativeId()))
		//if creative.GetCreativeId() == 0 {
		//	bid.CrID = creative.GetCreativeKey()
		//}

		if candidate.GetIndexDeal() != nil {
			bid.DealID = candidate.GetIndexDeal().DealId
			bid.Price = float64(candidate.GetIndexDeal().DealPrice)
		}

		bid.ImpTrackers = candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
		bid.ClickTrackers = candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen)

		if len(genericAd.GetVideoStartUrlList()) > 0 {
			bid.PlaySTrackers = candidate.ReplaceUrlMacroList(genericAd.GetVideoCloseUrlList(), traffic, trackingGen)
		}

		if len(genericAd.GetVideoCloseUrlList()) > 0 {
			bid.PlayETrackers = candidate.ReplaceUrlMacroList(genericAd.GetVideoCloseUrlList(), traffic, trackingGen)
		}

		pmpResponse := &oppo_pd_broker_entity.PMPResponse{
			URL:             candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
			DeeplinkURL:     candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen),
			BrandName:       "淘宝",
			FormatType:      58, //todo
			BtnName:         "点击跳转",
			JumpType:        1,
			HeatValue:       "1001000",
			HeatIconType:    2,
			InteractiveMode: 1,
		}

		if len(genericAd.GetMediaTemplateIndex()) > 0 {
			template := genericAd.GetMediaTemplateIndex()[0]
			templates := strings.Split(template, "_")
			if len(templates) == 2 {
				pmpResponse.FormatType = type_convert.GetAssertInt(templates[1])
			}
		}

		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				matFile := oppo_pd_broker_entity.MaterialFile{
					URL:      rsc.Url,
					Height:   int(rsc.Height),
					Width:    int(rsc.Width),
					FileType: 0,
				}
				pmpResponse.MatFiles = append(pmpResponse.MatFiles, matFile)
			case entity.MaterialTypeIcon:
				pmpResponse.BrandLogo = rsc.Url
			case entity.MaterialTypeLogo:
				pmpResponse.BrandLogo = rsc.Url
			case entity.MaterialTypeTitle:
				pmpResponse.Title = rsc.Data
			case entity.MaterialTypeDesc:
				pmpResponse.Description = rsc.Data
			case entity.MaterialTypeVideo:
				matFile := oppo_pd_broker_entity.MaterialFile{
					URL:      rsc.Url,
					MD5:      "",
					Height:   int(rsc.Height),
					Width:    int(rsc.Width),
					FileType: 1,
					Duration: int(rsc.Duration),
				}
				pmpResponse.MatFiles = append(pmpResponse.MatFiles, matFile)
			}
		}

		admResponse := oppo_pd_broker_entity.AdmResponse{
			Pmp: pmpResponse}

		adm, _ := sonic.Marshal(admResponse)
		bid.Adm = string(adm)

		seatBid.Bid = append(seatBid.Bid, bid)
		bidResponse.SeatBid = append(bidResponse.SeatBid, seatBid)
		break
	}

	if err := mb.BuildHttpSonicJsonResponse(request, writer, bidResponse); err != nil {
		return err
	}

	mb.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		resBody, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[OppoPdTrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resBody)))))
	}

	return nil
}

func (mb *OppoPdTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[OppoPdTrafficBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		zap.L().Info("[OppoPdTrafficBroker] adCode:%+v").GetErrCodeMap())
	}

	response := &oppo_pd_broker_entity.BidResponse{
		ID:    request.GetRequestId(),
		BidID: request.GetRequestId(),
		NBR:   1002,
	}

	mb.BuildHttpSonicJsonResponse(request, writer, response)
	return nil
}
