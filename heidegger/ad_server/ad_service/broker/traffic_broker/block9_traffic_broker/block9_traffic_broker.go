package block9_traffic_broker

import (
	rand2 "crypto/rand"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"strconv"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/block9_traffic_broker/block9_traffic_broker_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

const skipSystemDeeplinkCheckKey = "6f0e8faadf8bfa11332350b6484ef455"

type Block9TrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	mediaId      utils.ID
	host         string
	clientSecret string

	WinPriceMacro string
	MediaMacro    *macro_builder.MediaMacro
}

func NewBlock9TrafficBroker(mediaId utils.ID, clientSecret string) *Block9TrafficBroker {
	return &Block9TrafficBroker{
		mediaId:       mediaId,
		clientSecret:  clientSecret,
		WinPriceMacro: "__X_WP__",
		MediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "__X_WP__",
			MediaClickDownXMacro: "__AZMX__",
			MediaClickDownYMacro: "__AZMY__",
			MediaClickUpXMacro:   "__AZCX__",
			MediaClickUpYMacro:   "__AZCY__",
		},
	}
}

func (mb *Block9TrafficBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *Block9TrafficBroker) Do(request *ad_service.AdRequest) error {
	return mb.ParseAdRequest(request)
}

func (mb *Block9TrafficBroker) CheckSecret(mRequest *ad_service.AdRequest) error {
	if mb.clientSecret == "" {
		return err_code.ErrEmptySecret
	}

	adsTime := mRequest.GetRawHttpHeader("X-ADS-TIME")

	if len(adsTime) == 0 {
		return err_code.ErrEmptyHeaderXADSTime

	}

	reqAdsKey := mRequest.GetRawHttpHeader("X-ADS-KEY")
	if len(reqAdsKey) == 0 {
		return err_code.ErrEmptyHeaderXADSKey
	}

	adsKey := md5_utils.GetMd5String(adsTime + mb.clientSecret)

	if adsKey != reqAdsKey {
		return err_code.ErrSecretCheck
	}

	return nil

}

func (mb *Block9TrafficBroker) ParseAdRequest(mRequest *ad_service.AdRequest) error {
	mRequest.Response.SetResponseBuilder(mb.SendResponse)
	mRequest.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	mRequest.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro
	mRequest.AdRequestMedia.MediaMacro = mb.MediaMacro

	err := mb.CheckSecret(mRequest)
	if err != nil {
		zap.L().Error("[Block9TrafficBroker]Check secret error, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	body := mRequest.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[Block9TrafficBroker] request body empty")
	}

	//50 % 抽样
	if !mRequest.GetIsSampled() {
		mRequest.SetIsSampled(true)
		//mRequest.SetIsSampled(rand.Int31n(10000) < 5000)
	}

	bidRequest := &block9_traffic_broker_entity.Request{}
	//err = easyjson.Unmarshal(body, bidRequest)
	err = sonic.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[Block9TrafficBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if mRequest.IsDebug {
		zap.L().Info("Block9TrafficBroker Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", bidRequest.DumpJson())))))
	}

	if len(bidRequest.Imp) != 1 {
		zap.L().Error("[Block9TrafficBroker]BrokeRequest, Request body invalid, imp count not equal to 1")
		return fmt.Errorf("only support one imp unit")
	}

	if bidRequest.MediaId != int(mb.mediaId) {
		zap.L().Error("[Block9TrafficBroker]BrokeRequest, Request body invalid, media id not equal")
		return fmt.Errorf("media id not equal")
	}

	imp := &bidRequest.Imp[0]
	mRequest.SetMediaId(mb.GetMediaId())
	mRequest.SetRequestId(imp.Id)
	if len(imp.Id) == 0 {
		mRequest.SetRequestId(mRequest.GenerateRequestId())
	}

	if err := mb.parseImp(bidRequest, mRequest, imp); err != nil {
		zap.L().Debug("[Block9TrafficBroker] BrokeRequest, parseUser failed")
		return err
	}

	if err := mb.parseApp(bidRequest, mRequest, &bidRequest.App); err != nil {
		zap.L().Debug("[Block9TrafficBroker] BrokeRequest, parseApp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, mRequest, &bidRequest.Device); err != nil {
		zap.L().Debug("[Block9TrafficBroker] BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseUser(bidRequest, mRequest, &bidRequest.User); err != nil {
		zap.L().Debug("[Block9TrafficBroker] BrokeRequest, parseUser failed")
		return err
	}

	if err := mb.parseGeo(bidRequest, mRequest, &bidRequest.Geo); err != nil {
		zap.L().Debug("[Block9TrafficBroker] BrokeRequest, parseGeo failed")
		return err
	}

	mb.DoTrafficSample(mRequest, body)

	return nil
}

func (mb *Block9TrafficBroker) parseImp(bidRequest *block9_traffic_broker_entity.Request, mRequest *ad_service.AdRequest, imp *block9_traffic_broker_entity.Imp) error {
	mRequest.SetMediaSlotKey(fmt.Sprintf("%d", imp.Slot))
	mRequest.SetSourceSlotId(fmt.Sprintf("%d_%d", bidRequest.MediaId, imp.Slot))
	mRequest.SetBidFloor(uint32(imp.BidFloor))
	mRequest.UseHttps = imp.Secure == 1
	return nil
}

func (mb *Block9TrafficBroker) parseApp(bidRequest *block9_traffic_broker_entity.Request, mRequest *ad_service.AdRequest, app *block9_traffic_broker_entity.App) error {
	mRequest.App.AppBundle = app.BundleId
	mRequest.App.AppName = app.AppName
	mRequest.App.AppVersion = app.AppVersion
	mRequest.App.InstalledApp = app.Installed
	mRequest.App.InstalledAppIds = app.InstalledId
	return nil
}

func (mb *Block9TrafficBroker) parseDevice(bidRequest *block9_traffic_broker_entity.Request, mRequest *ad_service.AdRequest, device *block9_traffic_broker_entity.Device) error {
	mRequest.Device.RequestIp = device.Ip
	//if mRequest.Device.RequestIp == "client" || len(mRequest.Device.RequestIp) < 1 {
	//	clientIp, _ := net_utils.ParseIpPort(mRequest.RawHttpRequest.GetRemoteAddress())
	//	if clientIp != nil {
	//		mRequest.Device.RequestIp = clientIp.String()
	//	}
	//}

	clientIp, _ := net_utils.ParseIpPort(mRequest.RawHttpRequest.GetRemoteAddress())
	if clientIp != nil {
		mRequest.Device.RequestIp = clientIp.String()
	}

	mRequest.Device.UserAgent = device.Ua
	mRequest.Device.OsType = block9_traffic_broker_entity.ParseOsType(device.Os)
	mRequest.Device.OsVersion = device.OsVersion
	mRequest.Device.DeviceType = block9_traffic_broker_entity.ParseDeviceType(device.DeviceType)
	mRequest.Device.ScreenWidth = int32(device.ScreenWidth)
	mRequest.Device.ScreenHeight = int32(device.ScreenHeight)
	mRequest.Device.ScreenDensity = float32(device.Dpi)
	mRequest.Device.PPI = int32(device.Ppi)
	mRequest.Device.ConnectionType = block9_traffic_broker_entity.ParseConnectionType(device.ConnectionType)
	mRequest.Device.OperatorType = block9_traffic_broker_entity.ParseOperatorType(device.Carrier)
	mRequest.Device.Brand = device.Brand
	mRequest.Device.Model = device.Model
	mRequest.Device.BootMark = device.BootMark
	mRequest.Device.UpdateMark = device.UpdateMark
	mRequest.Device.Imei = device.RawImei
	mRequest.Device.ImeiMd5 = device.Md5Imei
	mRequest.Device.AndroidId = device.RawAndroidId
	mRequest.Device.AndroidIdMd5 = device.Md5AndroidId
	mRequest.Device.Oaid = device.RawOaid
	mRequest.Device.OaidMd5 = device.Md5Oaid
	mRequest.Device.Idfa = device.RawIdfa
	mRequest.Device.IdfaMd5 = device.Md5Idfa
	mRequest.Device.Mac = device.RawMac
	mRequest.Device.MacMd5 = device.Md5Mac
	mRequest.Device.Caid = device.RawCaid
	mRequest.Device.Aaid = device.RawAaid

	if device.Detail != nil {
		mRequest.Device.Paid = device.Detail.Paid
		mRequest.Device.DeviceStartupTime = device.Detail.StartupTime
		mRequest.Device.DeviceUpgradeTime = device.Detail.UpdateTime
		mRequest.Device.DeviceInitTime = device.Detail.InitTime
		mRequest.Device.SystemTotalMem = type_convert.StrconvInt64WithDefault(device.Detail.TotalMem, 0)
		mRequest.Device.SystemTotalDisk = type_convert.StrconvInt64WithDefault(device.Detail.TotalDisk, 0)
		phoneName, _ := base64.RawURLEncoding.DecodeString(device.Detail.PhoneName)
		if len(phoneName) > 0 {
			mRequest.Device.RomName = string(phoneName)
		}
		mRequest.Device.HardwareMachineCode = device.Detail.HardwareMachine
		mRequest.Device.Language = device.Detail.Language
		mRequest.Device.CountryCode = device.Detail.Country
		mRequest.Device.OperatorName = device.Detail.CarrierName
		mRequest.Device.SystemCompileTime = device.Detail.CompileTime
		mRequest.Device.AppStoreVersion = device.Detail.AgVer
		mRequest.Device.RomVersion = device.Detail.HmsVer
	}

	return nil
}

func (mb *Block9TrafficBroker) parseUser(bidRequest *block9_traffic_broker_entity.Request, mRequest *ad_service.AdRequest, user *block9_traffic_broker_entity.User) error {
	mRequest.UserGender = block9_traffic_broker_entity.ParseGender(user.Gender)
	mRequest.UserAge = int32(user.Age)
	return nil
}

func (mb *Block9TrafficBroker) parseGeo(bidRequest *block9_traffic_broker_entity.Request, mRequest *ad_service.AdRequest, geo *block9_traffic_broker_entity.Geo) error {
	mRequest.Device.Lat = geo.Lat
	mRequest.Device.Lon = geo.Lon
	mRequest.Device.GpsTime = geo.Time
	mRequest.Device.GpsType = block9_traffic_broker_entity.ParseGpsType(geo.Type)

	return nil
}

func (mb *Block9TrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		request.Response.Dump("Block9TrafficBroker")
	}

	response := &block9_traffic_broker_entity.Response{
		Code:        int(request.Response.GetAdErrorCode().Code),
		ProcessTime: time.Now().Sub(request.RequestTime).Nanoseconds(),
	}

	if err := mb.BuildHttpSonicJsonResponse(request, writer, response); err != nil {
		zap.L().Error("[Block9TrafficBroker] SendFallbackResponse, marshal response failed, err", zap.Error(err))
		return err_code.ErrBrokerResponseInternalFail.Wrap(err)
	}

	mb.DoTrafficResponseSampleJson(request, response)
	return nil
}

func (mb *Block9TrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		request.Response.Dump("Block9TrafficBroker [SendResponse]")
		zap.L().Info("[Block9TrafficBroker] adCode:%+v").GetErrCodeMap())
	}

	if len(request.Response.GetAdCandidateList()) == 0 {
		return mb.SendFallbackResponse(request, writer)
	}

	bidResponse := &block9_traffic_broker_entity.Response{
		Code:        0,
		ProcessTime: time.Now().Sub(request.RequestTime).Nanoseconds(),
		Ads:         make([]*block9_traffic_broker_entity.ResponseAds, 0),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		responseAd, err := mb.buildResponseAd(request, candidate)
		if err != nil {
			zap.L().Error("[Block9TrafficBroker] SendResponse, buildResponseAd failed, err", zap.Error(err))
			return err_code.ErrBrokerResponseInternalFail.Wrap(err)
		}

		bidResponse.Ads = append(bidResponse.Ads, responseAd)
	}

	//buffer := buffer_pool.NewBufferWriter()
	//defer buffer.Release()
	//
	//_, err := easyjson.MarshalToWriter(bidResponse, buffer)
	//if err != nil {
	//	zap.L().Error("Block9TrafficBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
	//	return err
	//}
	//
	//data := buffer.Get()
	//writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	//if _, err := writer.WriteWithStatus(200, data); err != nil {
	//	return err
	//}

	if err := mb.BuildHttpSonicJsonResponse(request, writer, bidResponse); err != nil {
		return err
	}

	mb.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		resbody, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[Block9TrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	return nil
}

func (mb *Block9TrafficBroker) buildResponseAd(request *ad_service.AdRequest, candidate *ad_service.AdCandidate) (*block9_traffic_broker_entity.ResponseAds, error) {
	genericAd := candidate.GetGenericAd()
	creative := candidate.GetCreative()
	traffic := candidate.GetModifiedTrafficData()
	trackingGen := candidate.GetTrackingGen(traffic)

	bidPrice := candidate.GetBidPrice()
	responseAd := &block9_traffic_broker_entity.ResponseAds{
		Id:         request.GetRequestId(),
		BidPrice:   int(bidPrice.Price),
		BidType:    block9_traffic_broker_entity.ToBidType(bidPrice.Type),
		ActionType: 0, //TODO FIX
		Events: block9_traffic_broker_entity.ResponseEvent{
			DownloadUrls:   candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadStartedMonitorList(), traffic, trackingGen),
			DownloadedUrls: candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadFinishedMonitorList(), traffic, trackingGen),
			InstallUrls:    candidate.ReplaceUrlMacroList(genericAd.GetAppInstallStartMonitorList(), traffic, trackingGen),
			InstalledUrls:  candidate.ReplaceUrlMacroList(genericAd.GetAppInstalledMonitorList(), traffic, trackingGen),
			DeeplinkUrls:   candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen),
			ImpUrls:        candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			ClickUrls:      candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
		},
		Creative: block9_traffic_broker_entity.ResponseCreative{
			Cid:      creative.GetCreativeKey(),
			Land:     candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
			Deeplink: candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen),
		},
	}

	if len(responseAd.Creative.Cid) < 1 {
		responseAd.Creative.Cid = strconv.FormatInt(int64(creative.GetCreativeId()), 10)
	}

	if len(genericAd.GetDeepLinkUrl()) > 0 {
		responseAd.Events.DeeplinkUrls = append(responseAd.Events.DeeplinkUrls, candidate.ReplaceUrlMacro(genericAd.GetDpSuccess(), traffic, trackingGen))
	}

	adsExt := block9_traffic_broker_entity.ResponseAdsExt{
		Interval:                   0,
		ReportPkg:                  candidate.GetModifiedTrafficData().GetAppBundle(),
		Timeout:                    500,
		ReqFreq:                    1,
		MinReqInterval:             300,
		DspType:                    candidate.GetDspProtocol(),
		SkipSystemDeeplinkCheckKey: skipSystemDeeplinkCheckKey,
	}
	sdkInteraction := genericAd.GetSdkInteraction()
	if sdkInteraction != nil {
		adsExt.SkipSystemDeeplinkCheck = sdkInteraction.SkipSystemDeeplinkCheck
		if !adsExt.SkipSystemDeeplinkCheck { // 如果没有启用，生成随机的假key
			adsExt.SkipSystemDeeplinkCheckKey, _ = generateAESKey()
		}
	}

	// 根据配置返回是否需要上传点击监测
	mediaSlotInfo := request.GetMediaSlotInfo()
	if mediaSlotInfo != nil && mediaSlotInfo.SdkSlotConfig != nil && mediaSlotInfo.SdkSlotConfig.ClickRatio > -1 {
		adsExt.NoClickTrack = rand.Float64() > float64(mediaSlotInfo.SdkSlotConfig.ClickRatio)/10000
	}

	adsExtData, _ := json.Marshal(adsExt)
	responseAd.Ext = string(adsExtData)

	downloadUrl := genericAd.GetDownloadUrl()
	if len(downloadUrl) > 0 {
		downloadUrl = candidate.ReplaceUrlMacro(downloadUrl, traffic, trackingGen)
	}

	if genericAd.GetLandingAction() == entity.LandingTypeDownload {
		responseAd.ActionType = 2
		if genericAd.GetAppInfo() != nil && len(downloadUrl) > 0 && len(genericAd.GetH5LandingUrl()) > 0 {
			responseAd.Creative.Land = candidate.ReplaceUrlMacro(genericAd.GetH5LandingUrl(), traffic, trackingGen)
		}
	} else if genericAd.GetLandingAction() == entity.LandingTypeDeepLink {
		responseAd.ActionType = 1
	}

	responseAd.Creative.Adm.TemplateType = candidate.GetSelectedTemplateType()

	for _, asset := range candidate.GetSelectedMaterialList() {
		switch asset.MaterialType {
		case entity.MaterialTypeImage:
			responseAd.Creative.Adm.Imgs = append(responseAd.Creative.Adm.Imgs, block9_traffic_broker_entity.ResponseImg{
				Url:    candidate.ReplaceUrlGeneral(asset.Url),
				Width:  int(asset.Width),
				Height: int(asset.Height),
			})
		case entity.MaterialTypeIcon:
			responseAd.Creative.Adm.Icon = &block9_traffic_broker_entity.ResponseImg{
				Url:    candidate.ReplaceUrlGeneral(asset.Url),
				Width:  int(asset.Width),
				Height: int(asset.Height),
			}
		case entity.MaterialTypeTitle:
			responseAd.Creative.Adm.Title = &block9_traffic_broker_entity.ResponseText{
				Text: asset.Data,
			}
		case entity.MaterialTypeDesc:
			responseAd.Creative.Adm.Desc = &block9_traffic_broker_entity.ResponseText{
				Text: asset.Data,
			}
		case entity.MaterialTypeVideo:
			responseAd.Creative.Adm.Videos = append(responseAd.Creative.Adm.Videos, block9_traffic_broker_entity.ResponseVideo{
				Url:      candidate.ReplaceUrlGeneral(asset.Url),
				Width:    int(asset.Width),
				Height:   int(asset.Height),
				Duration: int(asset.Duration),
			})
		}

		appInfo := genericAd.GetAppInfo()
		if appInfo != nil && len(appInfo.PackageName) != 0 {
			responseAd.AdApp = &block9_traffic_broker_entity.ResponseAdApp{
				Name:          appInfo.AppName,
				IconUrl:       appInfo.Icon,
				PackageName:   appInfo.PackageName,
				PackageSize:   appInfo.PackageSize,
				AppVersion:    appInfo.AppVersion,
				DescUrl:       appInfo.AppDescURL,
				PrivacyUrl:    appInfo.Privacy,
				PermissionUrl: appInfo.Permission,
				Developer:     appInfo.Develop,
				DownloadUrl:   downloadUrl,
				AppAge:        appInfo.AppAge,
				AppBeian:      appInfo.AppBeian,
			}
		}
	}

	if sdkInteraction != nil {
		adsExt.SkipSystemDeeplinkCheck = sdkInteraction.SkipSystemDeeplinkCheck
		responseAd.Interaction = &block9_traffic_broker_entity.ResponseInteraction{
			Countdown:           sdkInteraction.Countdown,
			VideoSound:          sdkInteraction.VideoSound,
			CloseCountdown:      sdkInteraction.CloseCountdown,
			Shake:               sdkInteraction.Shake,
			Slide:               sdkInteraction.Slide,
			Rotate:              sdkInteraction.Rotate,
			FullScreenClick:     sdkInteraction.FullScreenClick,
			RedPacketRain:       sdkInteraction.RedPacketRain,
			PopupCountdown:      sdkInteraction.PopupCountdown,
			PopupCloseCountdown: sdkInteraction.PopupCloseCountdown,
			FeedsGlide:          sdkInteraction.FeedsGlide,
			FeedsGlideDelay:     sdkInteraction.FeedsGlideDelay,
			FeedsGlideDisplay:   sdkInteraction.FeedsGlideDisplay,
			AdCache:             sdkInteraction.AdCache,
		}
	}

	return responseAd, nil
}

// generateAESKey generates a random 32 length AES key.
// It returns the key as a hex-encoded string.
func generateAESKey() (string, error) {
	key := make([]byte, 16)
	_, err := io.ReadFull(rand2.Reader, key)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(key), nil
}
