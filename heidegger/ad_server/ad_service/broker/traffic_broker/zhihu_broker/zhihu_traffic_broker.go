package zhihu_broker

import (
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/zhihu_broker/zhihu_json_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type (
	ZhihuTrafficBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId       utils.ID
		host          string
		useRawMonitor bool

		WinPriceMacro string
	}
)

func NewZhihuTrafficBroker(mediaId utils.ID) *ZhihuTrafficBroker {
	return &ZhihuTrafficBroker{
		mediaId:       mediaId,
		WinPriceMacro: "",
	}
}

func (mb *ZhihuTrafficBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *ZhihuTrafficBroker) Do(request *ad_service.AdRequest) error {
	return mb.ParseAdRequest(request)
}

func (mb *ZhihuTrafficBroker) ParseAdRequest(mRequest *ad_service.AdRequest) error {
	zap.L().Info("[ZhihuTrafficBroker] ParseAdRequest")

	mRequest.Response.SetResponseBuilder(mb.SendResponse)
	mRequest.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	mRequest.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro

	body := mRequest.RawHttpRequest.GetBodyContent()

	if len(body) == 0 {
		return fmt.Errorf("[ZhihuTrafficBroker]request body empty")
	}

	bidRequest := &zhihu_json_entity.ZhihuBidRequest{}
	//err := easyjson.Unmarshal(body, bidRequest)
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[ZhihuTrafficBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if mRequest.IsDebug {
		zap.L().Info("[ZhihuTrafficBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", body)))))
	}

	mRequest.SetRequestId(bidRequest.RequestId)
	if len(bidRequest.RequestId) < 1 {
		mRequest.SetRequestId(utils.NewUUID())
	}

	mRequest.SetMediaId(mb.mediaId)

	if err := mb.parseDevice(bidRequest, mRequest); err != nil {
		zap.L().Debug("[ZhihuTrafficBroker]BrokeRequest, parseDevice failed")
		return err
	}
	mRequest.ImpressionId = mRequest.GetRequestId()

	if err := mb.parseSlot(bidRequest, mRequest); err != nil {
		zap.L().Debug("[ZhihuTrafficBroker]BrokeRequest, parseDevice failed")
		return err
	}

	mb.DoTrafficSample(mRequest, body)

	return nil
}

func (mb *ZhihuTrafficBroker) parseSlot(mediaBidRequest *zhihu_json_entity.ZhihuBidRequest, bidReq *ad_service.AdRequest) error {
	bidReq.SetMediaSlotKey(type_convert.GetAssertString(mediaBidRequest.SlotId))
	/*
		21 知乎 App 开屏
		8 知乎 App 首页信息流
		30 知乎 App 问题页信息流
		105 知乎 App 回答页信息流
		31 知乎 App 评论页信息流*/
	switch mediaBidRequest.SlotId {
	case 21:
		bidReq.SlotType = entity.SlotTypeOpening
	case 8, 30, 31, 105:
		bidReq.SlotType = entity.SlotTypeFeeds
	}
	key := creative_entity.NewCreativeTemplateKey()
	key.Title().AddRequiredCount(1)
	key.Desc().AddRequiredCount(1)
	key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
	bidReq.AppendCreativeTemplateKey(key)

	return nil

}

func (mb *ZhihuTrafficBroker) parseDevice(mediaBidRequest *zhihu_json_entity.ZhihuBidRequest, bidReq *ad_service.AdRequest) error {
	if mediaBidRequest.Device == nil {
		zap.L().Error("parseDevice, device nil", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", mediaBidRequest)))))
		return fmt.Errorf("parseDevice failed")
	}

	device := mediaBidRequest.Device

	bidReq.Device.DeviceType = entity.DeviceTypeMobile

	bidReq.Device.RequestIp = device.Ip

	bidReq.Device.UserAgent = device.Ua

	bidReq.Device.OsType = mb.mappingOsType(device.Os)
	bidReq.Device.OsVersion = device.Osv
	bidReq.Device.Model = device.Model
	bidReq.Device.Brand = device.Brand

	bidReq.Device.Idfa = device.Idfa
	bidReq.Device.IdfaMd5 = device.IdfaMd5

	bidReq.Device.Imei = device.Imei
	bidReq.Device.ImeiMd5 = device.ImeiMd5

	bidReq.Device.Oaid = device.Oaid
	bidReq.Device.OaidMd5 = device.OaidMd5

	bidReq.Device.AndroidId = device.AndroidId
	bidReq.Device.AndroidIdMd5 = device.AndroidIdMd5

	bidReq.Device.Mac = device.Mac
	bidReq.Device.MacMd5 = device.MacMd5

	bidReq.Device.ScreenHeight = device.ScreenHeight
	bidReq.Device.ScreenWidth = device.ScreenWidth

	bidReq.Device.Lat = device.Lat
	bidReq.Device.Lon = device.Lon

	bidReq.Device.ConnectionType = mb.mappingNetworkType(device.Network)

	return nil

}

func (mb *ZhihuTrafficBroker) mappingOsType(s int) entity.OsType {
	switch s {
	case 1:
		return entity.OsTypeIOS
	case 2:
		return entity.OsTypeAndroid
	default:
		return entity.OsTypeUnknown
	}
}

func (mb *ZhihuTrafficBroker) mappingNetworkType(s int32) entity.ConnectionType {
	switch s {
	case 0:
		return entity.ConnectionTypeUnknown
	case 1:
		return entity.ConnectionTypeWifi
	case 2:
		return entity.ConnectionType2G
	case 3:
		return entity.ConnectionType3G
	case 4:
		return entity.ConnectionType4G
	case 5:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func (mb *ZhihuTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	zap.L().Info("[ZhihuTrafficBroker] SendResponse start. bid response:[], debug", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))), zap.String("param2", fmt.Sprintf("%v", request.IsDebug)))

	if request.IsDebug {
		zap.L().Info("ZhihuTrafficBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("ZhihuTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}

	bidResponse := &zhihu_json_entity.ZhihuResponse{
		RequestID: request.GetRequestId(),
		Code:      0,
		Msg:       "",
	}

	bidResponseAdJson := &zhihu_json_entity.ZhihuAdJSON{
		Ads: make([]*zhihu_json_entity.ZhihuAd, 0),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()

		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		bid := &zhihu_json_entity.ZhihuAd{
			ID:               int32(genericAd.GetAdId()),
			ImpressionTracks: candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			ClickTracks:      candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
			Creatives:        make([]*zhihu_json_entity.ZhihuAdCreative, 0),
		}

		zhihuCreative := &zhihu_json_entity.ZhihuAdCreative{
			ID: int32(creative.GetCreativeId()),
			Asset: &zhihu_json_entity.ZhihuAdAsset{
				LandingURL: candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
				DeepURL:    candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen),
			},
		}
		bid.Creatives = append(bid.Creatives, zhihuCreative)

		bidResponseAdJson.Ads = append(bidResponseAdJson.Ads, bid)
		break
	}
	adjson, _ := easyjson.Marshal(bidResponseAdJson)
	bidResponse.AdJSON = string(adjson)

	if err := mb.BuildHttpSonicJsonResponse(request, writer, &bidResponse); err != nil {
		return err
	}

	mb.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		resdata, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[ZhihuTrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resdata)))))
	}

	return nil
}

func (mb *ZhihuTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[ZhihuTrafficBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		zap.L().Info("[ZhihuTrafficBroker] adCode:%+v").GetErrCodeMap())
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	writer.WriteWithStatus(204, nil)
	return nil
}
