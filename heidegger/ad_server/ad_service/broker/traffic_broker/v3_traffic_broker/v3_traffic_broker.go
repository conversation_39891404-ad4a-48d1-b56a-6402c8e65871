package v3_traffic_broker

import (
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/v3_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"math/rand"
	"net"
	"fmt"
)

type V3TrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	mediaId           utils.ID
	host              string
	skipSystemMonitor bool
}

func NewV3TrafficBroker(mediaId utils.ID) *V3TrafficBroker {
	return &V3TrafficBroker{
		mediaId: mediaId,
	}
}

func (b *V3TrafficBroker) SetSkipSystemMonitor(skipSystemMonitor bool) {
	b.skipSystemMonitor = skipSystemMonitor
}

func (b *V3TrafficBroker) SetHost(host string) {
	b.host = host
}

func (b *V3TrafficBroker) GetMediaId() utils.ID {
	return b.mediaId
}

func (b *V3TrafficBroker) Do(request *ad_service.AdRequest) error {
	return b.ParseAdRequest(request)
}

func (b *V3TrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	//response := &v3_dsp_broker.V3BrokerResponse{
	//	RequestId: request.GetRequestId(),
	//}
	//
	//data, err := json.Marshal(response)
	//if err != nil {
	//	return err
	//}
	//
	//if _, err := writer.WriteWithStatus(200, data); err != nil {
	//	return err
	//}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	writer.WriteWithStatus(204, nil)

	return nil
}

func (b *V3TrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {

	response := &v3_dsp_broker.V3BrokerResponse{
		RequestId: request.GetRequestId(),
		//Candidate: request.Response.GetTotalAdCandidateList().GetErrCodeMap(),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		adResponse, err := b.buildAdResponse(candidate)
		if err != nil {
			return err_code.ErrBrokerResponseMediaData.Wrap(err)
		}

		response.Ads = append(response.Ads, adResponse)
	}

	if err := b.BuildHttpSonicJsonResponse(request, writer, &response); err != nil {
		//zap.L().Error("V3TrafficBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	//data, err := json.Marshal(response)
	//if err != nil {
	//	return err
	//}

	//writer.SetHeader("Content-Type", "application/json")
	//if _, err := writer.WriteWithStatus(200, data); err != nil {
	//	return err
	//}

	b.DoTrafficResponseSampleSonicJson(request, response)

	if request.IsDebug {
		resbody, _ := sonic.Marshal(response)
		zap.L().Info("[V3TrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resbody)))))
	}

	return nil
}

func (b *V3TrafficBroker) buildAdResponse(candidate *ad_service.AdCandidate) (*v3_dsp_broker.V3BrokerResponseAd, error) {
	serviceAd := candidate.GetGenericAd()
	trafficData := candidate.GetModifiedTrafficData()
	trackingGen := candidate.GetTrackingGen(trafficData)

	clickDelay := candidate.GetAd().GetAdMonitorInfo().ClickDelay
	if clickDelay > 0 {
		clickDelay = rand.Intn(clickDelay) + 1
	}

	landStayTime := candidate.GetAd().GetAdMonitorInfo().LandingStayTime
	if landStayTime > 0 {
		landStayTime = rand.Intn(landStayTime + 1)
	}

	response := &v3_dsp_broker.V3BrokerResponseAd{
		//UserAgent:       trafficData.GetUserAgent(),
		Referer:         trafficData.GetReferer(),
		ImpMonitors:     candidate.ReplaceUrlMacroList(serviceAd.GetImpressionMonitorList(), trafficData, trackingGen),
		ClickDelay:      clickDelay,
		LandingUrl:      candidate.ReplaceUrlMacro(serviceAd.GetLandingUrl(), trafficData, trackingGen),
		ClickMonitors:   candidate.ReplaceUrlMacroList(serviceAd.GetClickMonitorList(), trafficData, trackingGen),
		DeepLink:        candidate.ReplaceUrlMacro(serviceAd.GetDeepLinkUrl(), trafficData, trackingGen),
		DeepLinkMonitor: candidate.ReplaceUrlMacroList(serviceAd.GetDeepLinkMonitorList(), trafficData, trackingGen),
		LandStayTime:    landStayTime,
		Action:          1,
	}

	if trafficData.ReplaceUa() {
		response.UserAgent = trafficData.GetUserAgent()
	}

	for _, delayMonitor := range serviceAd.GetDelayMonitorUrlList() {
		response.DelayMonitor = append(response.DelayMonitor, v3_dsp_broker.V3BrokerResponseDelayMonitor{
			Url:   candidate.ReplaceUrlMacro(delayMonitor.Url, trafficData, trackingGen),
			Delay: delayMonitor.Delay,
		})
	}

	//todo doland rate
	if len(response.LandingUrl) != 0 && candidate.GetShouldDoArtificialLanding() {
		response.DoLand = 1
	}

	//if len(response.ClickMonitors) != 0 && candidate.GetShouldDoArtificialClick() {
	//	response.DoClick = 1
	//}

	response.DoClick = 1
	if candidate.GetAd().GetAdMonitorInfo().LandingStayTime > 0 {
		response.DoLand = 1
	}
	if !candidate.GetPacingDecision().ShouldClick() {
		response.DoClick = 0
		response.DoLand = 0
	}

	//
	//for _, material := range candidate.Material {
	//	response.Material = append(response.Material, OptBrokerResponseMaterial{
	//		Title:        material.Title,
	//		Desc:         material.Desc,
	//		CreativeType: material.CreativeType,
	//		Format:       material.Format,
	//	})
	//
	//	for _, image := range material.Image {
	//		response.Material[len(response.Material)-1].Image = append(response.Material[len(response.Material)-1].Image, OptBrokerResponseMaterialImage{
	//			Url:    image.Url,
	//			Width:  image.Width,
	//			Height: image.Height,
	//		})
	//	}
	//
	//	for _, video := range material.Video {
	//		response.Material[len(response.Material)-1].Video = append(response.Material[len(response.Material)-1].Video, OptBrokerResponseMaterialVideo{
	//			Url:      video.Url,
	//			Width:    video.Width,
	//			Height:   video.Height,
	//			Duration: video.Duration,
	//		})
	//	}
	//}

	return response, nil
}

func (b *V3TrafficBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(b.SendResponse)
	request.Response.SetFallbackResponseBuilder(b.SendFallbackResponse)
	request.SetMaxAdNum(1)

	body := request.RawHttpRequest.GetBodyContent()

	br := &v3_dsp_broker.V3BrokerRequest{}
	if err := sonic.Unmarshal(body, br); err != nil {
		zap.L().Info("[V3TrafficBroker] Unmarshal Request error:. broker request:[%s]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))), body)

		return err
	}

	if request.IsDebug {
		zap.L().Info("[V3TrafficBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", body)))))
	}

	request.SetRequestId(br.RequestId)
	request.SetMediaId(b.mediaId)
	request.SetMediaSlotKey(br.TagId)

	if br.IP == "client" {
		clientIp, _ := net_utils.ParseIpPort(request.RawHttpRequest.GetRemoteAddress())
		request.Device.RequestIp = clientIp.String()
	} else {
		requestIp := net.ParseIP(br.IP)
		request.Device.RequestIp = requestIp.String()
	}

	request.Device.UserAgent = br.UA
	request.Device.UserId = br.Cid
	request.Device.Idfa = br.Idfa
	request.Device.Imei = br.Imei
	request.Device.Oaid = br.Oaid
	request.Device.Aaid = br.AliAaid
	request.Device.Mac = br.Mac
	request.Device.AndroidId = br.AndroidId
	request.Device.ImeiMd5 = br.Md5Imei
	request.Device.IdfaMd5 = br.Md5Idfa
	request.Device.OaidMd5 = br.Md5Oaid
	request.Device.AndroidIdMd5 = br.Md5AndroidId
	request.Device.Brand = br.Brand
	request.Device.Model = br.Model
	request.Device.OsType = entity.OsType(br.Os)
	request.Device.OsVersion = br.OsVersion
	request.Device.DeviceType = entity.DeviceType(br.DeviceType)
	request.Device.ConnectionType = entity.ConnectionType(br.Network)
	request.Device.OperatorType = entity.OperatorType(br.Carrier)
	request.Device.BootMark = br.BootMark
	request.Device.UpdateMark = br.UpdateMark
	request.App.AppBundle = br.AppBundle
	request.App.AppName = br.AppName
	request.Device.Caids = br.Caids

	if !request.Device.DeviceType.IsValid() {
		return err_code.ErrInvalidDeviceType
	}

	if !request.Device.OsType.IsValid() {
		return err_code.ErrInvalidOsType
	}

	if !request.Device.ConnectionType.IsValid() {
		return err_code.ErrInvalidConnectionType
	}

	if !request.Device.OperatorType.IsValid() {
		return err_code.ErrInvalidOperatorType
	}

	b.DoTrafficSample(request, body)

	return nil
}
