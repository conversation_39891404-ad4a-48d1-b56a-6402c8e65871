package common_v2_json_broker

import (
	"fmt"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/common_v2_json_broker/common_v2_json_broker_entity"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"math"
	"strconv"
	"strings"
)

const (
	AdxTemplateKey = "adxTemplate"
)

var (
	emptyAdxTemplateMap map[uint64]int = make(map[uint64]int)
)

type (
	CommonV2JsonTrafficBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId       utils.ID
		host          string
		useRawMonitor bool

		WinPriceMacro string
	}
)

func NewCommonV2JsonTrafficBroker(mediaId utils.ID) *CommonV2JsonTrafficBroker {
	return &CommonV2JsonTrafficBroker{
		mediaId:       mediaId,
		WinPriceMacro: "__D_PRICE__",
	}
}

func (mb *CommonV2JsonTrafficBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *CommonV2JsonTrafficBroker) Do(request *ad_service.AdRequest) error {
	return mb.ParseAdRequest(request)
}

func (mb *CommonV2JsonTrafficBroker) ParseAdRequest(mRequest *ad_service.AdRequest) error {
	mRequest.Response.SetResponseBuilder(mb.SendResponse)
	mRequest.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	mRequest.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro

	body := mRequest.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[CommonV2JsonTrafficBroker]request body empty")
	}

	bidRequest := &common_v2_json_broker_entity.CommonV2JsonRequest{}
	err := easyjson.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[CommonV2JsonTrafficBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if mRequest.IsDebug {
		zap.L().Info("CommonV2JsonTrafficBroker Parse Request start. broker request domain:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", mRequest.GetReqHost())))))
		zap.L().Info("CommonV2JsonTrafficBroker Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", bidRequest.DumpJson())))))
	}

	mRequest.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		mRequest.SetRequestId(utils.NewUUID())
	}
	mRequest.SetMediaId(mb.mediaId)
	mRequest.Referer = bidRequest.Ref
	mRequest.Url = bidRequest.Url

	if err := mb.parseUser(bidRequest, mRequest); err != nil {
		zap.L().Debug("[CommonV2JsonTrafficBroker]BrokeRequest, parseUser failed")
		return err
	}

	if err := mb.parseApp(bidRequest, mRequest); err != nil {
		zap.L().Debug("[CommonV2JsonTrafficBroker]BrokeRequest, parseApp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, mRequest); err != nil {
		zap.L().Debug("[CommonV2JsonTrafficBroker]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseImp(bidRequest, mRequest); err != nil {
		zap.L().Debug("[CommonV2JsonTrafficBroker] BrokeRequest, parseImp failed")
		return err
	}

	if mRequest.Device.RequestIp == "client" || len(mRequest.Device.RequestIp) < 1 {
		clientIp, _ := net_utils.ParseIpPort(mRequest.RawHttpRequest.GetRemoteAddress())
		if clientIp != nil {
			mRequest.Device.RequestIp = clientIp.String()
		}
	}
	mb.DoTrafficSample(mRequest, body)

	return nil
}

func (mb *CommonV2JsonTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		request.Response.Dump("CommonV2JsonTrafficBroker")
		zap.L().Info("[CommonV2JsonTrafficBroker] adCode:%+v").GetErrCodeMap())
	}

	if len(request.Response.GetAdCandidateList()) == 0 {
		return mb.SendFallbackResponse(request, writer)
	}

	bidResponse := &common_v2_json_broker_entity.CommonV2JsonResponse{}
	bidResponse.Code = 0
	bidResponse.Rid = request.GetRequestId()
	bidResponse.Bid = make([]*common_v2_json_broker_entity.CommonV2JsonBid, 0)

	for _, candidate := range request.Response.GetAdCandidateList() {

		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()

		if request.IsDebug {
			zap.L().Info("[CommonV2JsonTrafficBroker] ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", candidate.GetDspResponseAd())))))
		}

		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		bid := &common_v2_json_broker_entity.CommonV2JsonBid{
			Native: &common_v2_json_broker_entity.CommonV2JsonRespNative{},
		}

		bid.ImpId = request.ImpressionId
		bidPrice := candidate.GetBidPrice()
		bid.Price = int(bidPrice.Price)
		//bid.DealId = seatBid.DealId
		bid.AdId = strconv.Itoa(int(creative.GetCreativeId()))
		bid.Landing = candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen)
		bid.DpUrl = candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen)

		//bid.NUrl =
		bid.ImpTracking = candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
		bid.ClickTracking = candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen)

		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			bid.DpTracking = candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen)
		}

		//bid.DownStartTrackers = append(bid.DownStartTrackers, seatBid.MonitorData.StartDownloadTracker...)
		//bid.DownCompTrackers = append(bid.DownCompTrackers, seatBid.MonitorData.CompleteDownloadTracker...)
		//bid.InstallCompTrackers = append(bid.InstallStartTrackers, seatBid.MonitorData.StartInstallTracker...)
		//bid.InstallCompTrackers = append(bid.InstallCompTrackers, seatBid.MonitorData.CompleteInstallTracker...)
		//bid.SdkSwitchExt = seatBid.BidExtData.SdkSwitchExt
		//bid.VideoCloseTrackers = append(bid.VideoCloseTrackers, seatBid.MonitorData...)

		for _, ev := range genericAd.GetDelayMonitorUrlList() {
			bid.DelayTracking = append(bid.DelayTracking, &common_v2_json_broker_entity.CommonV2JsonDelayImp{
				DelayTime: int(mb.RoundSeconds(ev.Delay)),
				Url:       ev.Url,
			})
		}

		adm := bid.Native
		bid.MrType = 3 // 1.图片 2.视频 3.naitve
		reqAdxTemplateMap := request.GetMediaExtraDataWithDefault(AdxTemplateKey, emptyAdxTemplateMap).(map[uint64]int)
		key1 := candidate.GetActiveCreativeTemplateKey()
		keyInt := key1.Uint64()
		adm.TemplateId = reqAdxTemplateMap[keyInt]
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				adm.Assets = append(adm.Assets, &common_v2_json_broker_entity.CommonV2JsonRespAsset{
					Type: common_v2_json_broker_entity.TemplateAssetTypeImage,
					Image: &common_v2_json_broker_entity.CommonV2JsonRespImage{
						Url: rsc.Url,
						W:   int(rsc.Width),
						H:   int(rsc.Height),
					},
				})
			case entity.MaterialTypeIcon:
				adm.Assets = append(adm.Assets, &common_v2_json_broker_entity.CommonV2JsonRespAsset{
					Type: common_v2_json_broker_entity.TemplateAssetTypeIcon,
					Image: &common_v2_json_broker_entity.CommonV2JsonRespImage{
						Url: rsc.Url,
						W:   int(rsc.Width),
						H:   int(rsc.Height),
					},
				})
			case entity.MaterialTypeLogo:

			case entity.MaterialTypeTitle:
				adm.Assets = append(adm.Assets, &common_v2_json_broker_entity.CommonV2JsonRespAsset{
					Type: common_v2_json_broker_entity.TemplateAssetTypeTitle,
					Text: rsc.Data,
				})
			case entity.MaterialTypeDesc:
				adm.Assets = append(adm.Assets, &common_v2_json_broker_entity.CommonV2JsonRespAsset{
					Type: common_v2_json_broker_entity.TemplateAssetTypeDesc,
					Text: rsc.Data,
				})
			//case dict.MaterialResourceLayoutAdvertiser:
			//	adm.Ext.AdvertiserName = rsc.Text.Text

			case entity.MaterialTypeVideo:
				adm.Assets = append(adm.Assets, &common_v2_json_broker_entity.CommonV2JsonRespAsset{
					Type: common_v2_json_broker_entity.TemplateAssetTypeVideo,
					Video: &common_v2_json_broker_entity.CommonV2JsonRespVideo{
						Url:      rsc.Url,
						W:        int(rsc.Width),
						H:        int(rsc.Height),
						Duration: int(rsc.Duration),
					},
				})
			//case dict.MaterialResourceLayoutButtonText:
			//	adm.Ext.Btn = rsc.Text.Text
			default:
				continue
			}
		}

		//switch seatBid.MaterialData.MimeType {
		//case dict.MimeJpg`, dict.MimeJpeg, dict.MimePng, dict.MimeGif:
		//	genImage(seatBid, adm)
		//case dict.MimeFlv, dict.MimeMp4:
		//	genVideo(seatBid, adm)
		//case dict.MimeText:
		//	genText(seatBid, adm)
		//case dict.MimeFeed, dict.MimeVideoFeed:
		//	genFeed(seatBid, adm)
		//default:
		//}

		//adm.Ext.Style = seatBid.MaterialData.AdxTemplateId
		//bid.TemplateSpec = seatBid.MaterialData.HtmlTemplate
		//1.页面跳转 2.下载 3.小程序唤起 4.原生deeplink唤起
		bid.Action = 1
		//adm.Formid, _ = strconv.Atoi(seatBid.MaterialData.AdxTemplateId)
		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			bid.Action = 2
		} else if genericAd.GetLandingAction() == entity.LandingTypeDeepLink {
			bid.Action = 4
		}

		//adm.LandDesc.ClickAreaClick = seatBid.BidExtData.ClickAreaClick
		//adm.LandDesc.ShakeModel = seatBid.BidExtData.ShakeModel

		if genericAd.GetAppInfo() != nil {
			if bid.AppDesc == nil {
				bid.AppDesc = &common_v2_json_broker_entity.CommonV2JsonAppDesc{}
			}

			bid.AppDesc.Name = genericAd.GetAppInfo().AppName
			//adm.App.Icon = seatBid.MonitorData.LandingDesc.AppIcon
			bid.AppDesc.Package = genericAd.GetAppInfo().PackageName
			//if bid.AppDesc.Package != "" {
			//	bid.Action = 2
			//}
		}

		//if seatBid.MonitorData.LandingDesc.LandingAction == dict.LandingTypeWeChatProgram {
		//	if adm.App == nil {
		//		adm.App = &json_broker.DownloadApp{}
		//	}
		//	if adm.App.WeChatExt == nil {
		//		adm.App.WeChatExt = &json_broker.WeChatExt{}
		//	}
		//	adm.App.WeChatExt.BindApp = seatBid.MonitorData.LandingDesc.WechatBindedApp
		//	adm.App.WeChatExt.ProgramId = seatBid.MonitorData.LandingDesc.WechatProgramId
		//	adm.App.WeChatExt.ProgramPath = seatBid.MonitorData.LandingDesc.WechatProgramPath
		//}

		bidResponse.Bid = append(bidResponse.Bid, bid)

	}

	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()

	_, err := easyjson.MarshalToWriter(bidResponse, buffer)
	if err != nil {
		zap.L().Error("CommonV2JsonTrafficBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	mb.DoTrafficResponseSample(request, data)

	if request.IsDebug {
		zap.L().Info("[CommonV2JsonTrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", data)))))
	}

	return nil
}

func (mb *CommonV2JsonTrafficBroker) RoundSeconds(duration int) int32 {
	const VideoDurationUnit = 1
	return int32(math.Round(float64(duration) / VideoDurationUnit))
}

func (mb *CommonV2JsonTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[CommonV2JsonTrafficBroker] SendFallbackResponse start")
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	writer.WriteWithStatus(200, nil)
	return nil
}

func (mb *CommonV2JsonTrafficBroker) parseDevice(mediaBidRequest *common_v2_json_broker_entity.CommonV2JsonRequest,
	bidReq *ad_service.AdRequest) error {

	//bidReq.IsMobile = true
	device := mediaBidRequest.Device

	bidReq.Device.DeviceType = mb.mappingDeviceType(mediaBidRequest.DeviceType)

	bidReq.Device.RequestIp = device.Ip

	bidReq.Device.UserAgent = device.Ua

	bidReq.Device.OsType = mb.mappingOsType(device.Os)
	bidReq.Device.OsVersion = device.Osv
	bidReq.Device.Model = device.Model
	bidReq.Device.Brand = device.Vendor

	if bidReq.Device.OsType == entity.OsTypeIOS {
		bidReq.Device.Idfa = device.Idfa
		bidReq.Device.IdfaMd5 = device.IdfaMd5
	} else {
		bidReq.Device.Imei = device.Imei
		bidReq.Device.ImeiMd5 = device.ImeiMd5

		bidReq.Device.Oaid = device.Oaid
		bidReq.Device.OaidMd5 = device.OaidMd5

		bidReq.Device.AndroidId = device.AndroidId
		bidReq.Device.AndroidIdMd5 = device.AndroidIdMd5
	}
	bidReq.Device.Caid = device.Caid
	bidReq.Device.CaidRaw = device.Caid
	//bidReq.Device.CaidVersion = device.Caid

	bidReq.Device.Mac = device.Mac
	bidReq.Device.MacMd5 = device.MacMd5

	bidReq.Device.ScreenHeight = int32(device.Height)
	bidReq.Device.ScreenWidth = int32(device.Width)
	//bidReq.Device.ScreenDensity = device.Den
	bidReq.Device.Lat = device.Lat
	bidReq.Device.Lon = device.Lng

	bidReq.Device.OperatorType = mb.mappingOperator(device.Operator)
	bidReq.Device.ConnectionType = mb.mappingConnectionType(device.Net)

	bidReq.Device.BootMark = device.BootMark
	bidReq.Device.UpdateMark = device.UpdateMark

	return nil
}

func (mb *CommonV2JsonTrafficBroker) parseUser(mediaBidRequest *common_v2_json_broker_entity.CommonV2JsonRequest,
	bidReq *ad_service.AdRequest) error {

	user := mediaBidRequest.User

	bidReq.UserId = user.Uid
	return nil
}

func (mb *CommonV2JsonTrafficBroker) parseApp(mediaBidRequest *common_v2_json_broker_entity.CommonV2JsonRequest,
	bidReq *ad_service.AdRequest) error {

	app := mediaBidRequest.App

	bidReq.App.AppName = app.Name
	bidReq.App.AppBundle = app.Package
	bidReq.App.AppVersion = app.Version

	return nil
}

func (mb *CommonV2JsonTrafficBroker) parseImp(mediaBidRequest *common_v2_json_broker_entity.CommonV2JsonRequest,
	bidReq *ad_service.AdRequest) error {

	rImp := mediaBidRequest.Imp

	bidReq.ImpressionId = rImp.Id
	bidReq.SetMediaSlotKey(rImp.SlotId)
	bidReq.BidFloor = uint32(rImp.BidFloor)
	bidReq.SlotType = mb.mappingSlotType(rImp.SlotType)

	for _, d := range rImp.Deal {
		if d == nil {
			continue
		}
		sd := ad_service.SourceDeal{
			DealId:   d.DealId,
			BidFloor: int64(d.DealFloor),
		}

		bidReq.SourceDeal = append(bidReq.SourceDeal, sd)
	}

	adxTemplateMap := make(map[uint64]int)

	for _, nativeTempalte := range rImp.Native.Templates {
		//nativeTempalte.Id
		if nativeTempalte == nil {
			continue
		}
		key := creative_entity.NewCreativeTemplateKey()
		for _, asset := range nativeTempalte.Assets {
			if asset == nil {
				continue
			}
			switch asset.Type {
			case common_v2_json_broker_entity.TemplateAssetTypeImage:
				sizeType := creative_entity.RT_SIZE_VERTICAL
				if len(asset.Image.AllowSize) > 0 {
					sizeArr := strings.Split(asset.Image.AllowSize[0], "*")
					if len(sizeArr) > 1 {
						width, _ := strconv.Atoi(sizeArr[0])
						height, _ := strconv.Atoi(sizeArr[1])
						if width > height {
							sizeType = creative_entity.RT_SIZE_HORIZONTAL
						}
					}
				}
				key.Image().SetRequiredCount(1).SetRequiredSizeType(sizeType)
			case common_v2_json_broker_entity.TemplateAssetTypeVideo:
				sizeType := creative_entity.RT_SIZE_VERTICAL
				if len(asset.Video.AllowSize) > 0 {
					sizeArr := strings.Split(asset.Video.AllowSize[0], "*")
					if len(sizeArr) > 1 {
						width, _ := strconv.Atoi(sizeArr[0])
						height, _ := strconv.Atoi(sizeArr[1])
						if width > height {
							sizeType = creative_entity.RT_SIZE_HORIZONTAL
						}
					}
				}
				key.Video().SetRequiredCount(1).SetRequiredSizeType(sizeType)
			}
		}
		bidReq.AppendCreativeTemplateKey(key)
		keyId := key.Uint64()
		adxTemplateMap[keyId] = nativeTempalte.Id
	}

	//for _, l := range item.SupportInteract {
	//	bidReq.ClickAction = append(bidReq.ClickAction, dict.LandingType(l))
	//}
	//if len(impInfo.ClickAction) < 1 {
	//	impInfo.ClickAction = append(impInfo.ClickAction, dict.LandingAll)
	//}
	bidReq.UseHttps = mb.mappingSecure(mediaBidRequest.Https)
	bidReq.AddMediaExtraData(AdxTemplateKey, adxTemplateMap)

	return nil
}

func (mb *CommonV2JsonTrafficBroker) mappingSecure(s int) bool {

	switch s {
	case 0:
		return false
	case 1:
		return true
	case 2:
		return false
	default:
		return false
	}
}

func (mb *CommonV2JsonTrafficBroker) mappingSlotType(s int) entity.SlotType {
	switch s {
	case 1:
		return entity.SlotTypeVideo
	case 2:
		return entity.SlotTypeBanner
	case 4:
		return entity.SlotTypePopup
	case 5:
		return entity.SlotTypeBanner
	case 6:
		return entity.SlotTypeOpening
	case 7:
		return entity.SlotTypePopup
	case 8:
		return entity.SlotTypeFeeds
	default:
		return entity.SlotTypeUnknown
	}
}

func (mb *CommonV2JsonTrafficBroker) mappingConnectionType(s int) entity.ConnectionType {
	switch s {
	case 1:
		return entity.ConnectionTypeWifi
	case 2:
		return entity.ConnectionType2G
	case 3:
		return entity.ConnectionType3G
	case 4:
		return entity.ConnectionType4G
	case 5:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func (mb *CommonV2JsonTrafficBroker) mappingOperator(s int) entity.OperatorType {
	switch s {
	case 1:
		return entity.OperatorTypeChinaMobile
	case 2:
		return entity.OperatorTypeChinaUnicom
	case 3:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeChinaUnicom
	}
}

func (mb *CommonV2JsonTrafficBroker) mappingOsType(s int) entity.OsType {
	switch s {
	case 1:
		return entity.OsTypeAndroid
	case 2:
		return entity.OsTypeIOS
	default:
		return entity.OsTypeUnknown
	}
}

func (mb *CommonV2JsonTrafficBroker) mappingDeviceType(s int) entity.DeviceType {
	switch s {
	case 1:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePad
	case 3:
		return entity.DeviceTypePc
	case 4:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeUnknown
	}
}
