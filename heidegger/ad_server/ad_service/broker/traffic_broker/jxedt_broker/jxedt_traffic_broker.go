package jxedt_broker

import (
	"encoding/json"
	"errors"
	"strconv"
	"strings"

	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/jxedt_broker/protos"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	objectpool "gitlab.com/dev/heidegger/library/object_pool"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"fmt"
)

const (
	AdxTemplateKey = "adxTemplate"
)

var (
	bidRequestPool = objectpool.NewObjectPool(func() *protos.Request {
		return new(protos.Request)
	})
	bidResponsePool = objectpool.NewObjectPool(func() *protos.Response {
		return new(protos.Response)
	})
	bidResponseBidPool = objectpool.NewObjectPool(func() *protos.Response_Bid {
		return new(protos.Response_Bid)
	})
)

type JxedtTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	mediaId       utils.ID
	WinPriceMacro string
	log           *zap.Logger
}

// var _ TrafficBrokerInterface = (*JxedtTrafficBroker)(nil)

func NewJxedtTrafficBroker(mediaId utils.ID) *JxedtTrafficBroker {
	return &JxedtTrafficBroker{
		mediaId:       mediaId,
		WinPriceMacro: "__win_price__",
		log:           zap.L().With(zap.String("broker", "JxedtTrafficBroker")),
	}
}

func (b *JxedtTrafficBroker) Do(request *ad_service.AdRequest) error {
	return b.ParseBidRequest(request)
}

func (b *JxedtTrafficBroker) GetMediaId() utils.ID {
	return b.mediaId
}

func (b *JxedtTrafficBroker) ParseBidRequest(adRequest *ad_service.AdRequest) error {
	adRequest.Response.SetResponseBuilder(b.BuildResponse)
	adRequest.Response.SetFallbackResponseBuilder(b.BuildFallbackResponse)
	adRequest.AdRequestMedia.WinPriceMacro = b.WinPriceMacro

	body := adRequest.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("[JxedtTrafficBroker] request body empty")
	}

	bidRequest := bidRequestPool.Get()
	defer bidRequestPool.Put(bidRequest)

	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		b.log.WithError(err).Error("ParseBidRequest error")
		return errors.New("[JxedtTrafficBroker] request body invalid")
	}

	if adRequest.IsDebug {
		reqBody, _ := json.Marshal(bidRequest)
		b.log.WithField("request", string(reqBody)).Info("parse request start")
	}

	adRequest.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		adRequest.SetRequestId(utils.NewUUID())
	}
	adRequest.SetMediaId(b.mediaId)

	if err := b.parseImp(bidRequest, adRequest); err != nil {
		b.log.Debug("parseImp fail")
		return err
	}

	if err := b.parseSite(bidRequest, adRequest); err != nil {
		b.log.Debug("parseSite fail")
		return err
	}

	if err := b.parseApp(bidRequest, adRequest); err != nil {
		b.log.Debug("parseApp fail")
		return err
	}

	if err := b.parseUser(bidRequest, adRequest); err != nil {
		b.log.Debug("parseUser fail")
		return err
	}

	if err := b.parseDevice(bidRequest, adRequest); err != nil {
		b.log.Debug("parseDevice fail")
		return err
	}

	b.DoTrafficSamplePb(adRequest, bidRequest)

	return nil
}

func (b *JxedtTrafficBroker) BuildResponse(adRequest *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if adRequest.IsDebug {
		b.log.Info("BuildResponse start")
		adRequest.Response.Dump("JxedtTrafficBroker")
	}

	if adRequest.Response.NoCandidate() {
		return b.BuildFallbackResponse(adRequest, writer)
	}

	bidResponse := bidResponsePool.Get()
	defer bidResponsePool.Put(bidResponse)

	bidResponse.Id = adRequest.GetRequestId()
	bidResponse.BidId = adRequest.GetRequestId()
	bidResponse.Code = 204
	bidResponse.Msg = ""

	for _, candidate := range adRequest.Response.GetAdCandidateList() {
		bidResponse.Code = 200
		bid := bidResponseBidPool.Get()
		defer bidResponseBidPool.Put(bid)
		bidResponse.Bid = bid

		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		trafficData := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(trafficData)
		bidPrice := candidate.GetBidPrice()

		bid.ImpId = adRequest.ImpressionId
		bid.Price = int32(bidPrice.Price)
		bid.Crid = creative.GetCreativeKey()

		adxTemplateMapI, ok := adRequest.GetMediaExtraData(AdxTemplateKey)
		if ok {
			adxTemplateMap := adxTemplateMapI.(map[uint64]string)
			key := candidate.GetActiveCreativeTemplateKey()
			keyInt := key.Uint64()
			bid.TemplateId = adxTemplateMap[keyInt]
		}

		isVideo, coverUrl, videoUrl := false, "", ""
		var height, width int32 = 0, 0
		for _, asset := range candidate.GetSelectedMaterialList() {
			switch asset.MaterialType {
			case entity.MaterialTypeImage: // may be video cover
				bid.ImgUrl = candidate.ReplaceUrlGeneral(asset.Url)
				bid.ImgWidth = asset.Width
				bid.ImgHeight = asset.Height
				coverUrl = bid.ImgUrl
			case entity.MaterialTypeIcon:
				bid.IconUrl = candidate.ReplaceUrlGeneral(asset.Url)
			case entity.MaterialTypeTitle:
				bid.Title = asset.Data
			case entity.MaterialTypeDesc:
				bid.Desc = asset.Data
			case entity.MaterialTypeVideo:
				bid.ImgUrl = candidate.ReplaceUrlGeneral(asset.Url)
				bid.ImgWidth = asset.Width
				bid.ImgHeight = asset.Height
				bid.VideoDuration = int32(asset.Duration)
				bid.VideoSize = float32(asset.FileSize)
				isVideo = true
				videoUrl = bid.ImgUrl
				height, width = asset.Height, asset.Width
			}
		}
		if isVideo { // restore video data
			bid.ImgUrl = videoUrl
			bid.ImgHeight = height
			bid.ImgWidth = width
			bid.CoverUrl = coverUrl
		}

		bid.Nurl = ""
		bid.ImpressionTrackingUrl = candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), trafficData, trackingGen)
		bid.ClickTrackingUrl = candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), trafficData, trackingGen)
		bid.WakeUpTrackingUrl = candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), trafficData, trackingGen)
		bid.DeeplinkUrl = candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), trafficData, trackingGen)
		if trafficData.GetOsType() == entity.OsTypeIOS {
			bid.UlkUrl = candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), trafficData, trackingGen)
		}

		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			bid.LandingPageUrl = candidate.ReplaceUrlMacro(genericAd.GetDownloadUrl(), trafficData, trackingGen)
		} else {
			bid.LandingPageUrl = candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), trafficData, trackingGen)
		}

		appInfo := genericAd.GetAppInfo()
		if appInfo != nil && len(appInfo.PackageName) > 0 {
			bid.AppPackageName = appInfo.PackageName
			bid.AppName = appInfo.AppName
		}

		break
	}

	// check creative and landing page for iqiyi(dsp)
	if bidResponse.Bid == nil ||
		len(bidResponse.Bid.ImgUrl) == 0 ||
		len(bidResponse.Bid.LandingPageUrl) == 0 {
		return b.BuildFallbackResponse(adRequest, writer)
	}

	buffer := buffer_pool.NewBuffer()
	defer buffer.Release()

	buffer.EnsureSize(bidResponse.Size())
	_, err := bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		b.log.WithError(err).Error("bidResponse marshal error")
		return err
	}
	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/octet-stream;charset=utf-8")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	b.DoTrafficResponseSamplePb(adRequest, bidResponse)
	if adRequest.IsDebug {
		data, _ := json.Marshal(bidResponse)
		b.log.WithField("response", string(data)).Info("BuildResponse success")
	}
	return nil
}

func (b *JxedtTrafficBroker) BuildFallbackResponse(adRequest *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/octet-stream;charset=utf-8")
	writer.WriteWithStatus(204, nil)

	if adRequest.IsDebug {
		for adId, errCode := range adRequest.Response.GetTotalAdCandidateList().GetErrCodeMap() {
			b.zap.L().Info("BuildFallbackResponse", zap.String("ad", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", adId)))), zap.String("code", fmt.Sprintf("%v", errCode)))
		}
	}
	return nil
}

func (b *JxedtTrafficBroker) parseImp(bidRequest *protos.Request, adRequest *ad_service.AdRequest) error {
	if bidRequest.GetImp() == nil {
		b.log.Error("parseImp no imp")
		return errors.New("[JxedtTrafficBroker] request no imp")
	}

	imp := bidRequest.GetImp()
	adRequest.ImpressionId = imp.GetId()
	adRequest.SetMediaSlotKey(imp.GetTagId())
	adRequest.SetBidFloor(uint32(imp.GetBidFloor()))

	var width, height = 0, 0
	// banner ad
	banner := imp.GetBanner()
	if banner != nil {
		if banner.GetHeight() > 0 && banner.GetWidth() > 0 {
			width, height = int(banner.GetWidth()), int(banner.GetHeight())
			adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
				Width:  int64(width),
				Height: int64(height),
			})
			adRequest.SlotWidth = uint32(width)
			adRequest.SlotHeight = uint32(height)
		}

		// get SlotType by media tag id
		switch banner.GetPosType() {
		case 1:
			adRequest.SlotType = entity.SlotTypeOpening
		case 2:
			adRequest.SlotType = entity.SlotTypeFeeds
		case 3:
			adRequest.SlotType = entity.SlotTypeBanner
		case 4:
			adRequest.SlotType = entity.SlotTypePopup
		case 5:
			adRequest.SlotType = entity.SlotTypeRewardVideo
		}

		// TODO:
		// banner.GetMaterialType()
		// banner.GetInteractionType()
	}

	adxTemplateMap := make(map[uint64]string)
	adRequest.AdxTemplateId = imp.GetTemplateId()
	for _, templateId := range imp.GetTemplateId() {
		switch templateId {
		case "1": // 1 image
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = templateId
		case "2": // 1 image with title
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
			key.Title().SetRequiredCount(1)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = templateId
		case "3": // 1 image with title, desc
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
			key.Title().SetRequiredCount(1)
			key.Desc().SetRequiredCount(1)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = templateId
		case "4": // 1 video
			key := creative_entity.NewCreativeTemplateKey()
			key.Video().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = templateId
		case "5": // 1 video with title
			key := creative_entity.NewCreativeTemplateKey()
			key.Video().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
			key.Title().SetRequiredCount(1)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = templateId
		case "6": // 1 video with title, desc
			key := creative_entity.NewCreativeTemplateKey()
			key.Video().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
			key.Title().SetRequiredCount(1)
			key.Desc().SetRequiredCount(1)
			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = templateId
		}
	}
	adRequest.AddMediaExtraData(AdxTemplateKey, adxTemplateMap)

	for _, dealId := range imp.GetDeals() {
		adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
			DealId:   dealId,
			BidFloor: int64(imp.GetBidFloor()),
		})
	}

	return nil
}

func (b *JxedtTrafficBroker) parseSite(bidRequest *protos.Request, adRequest *ad_service.AdRequest) error {
	site := bidRequest.GetSite()
	if site != nil {
	}

	return nil
}

func (b *JxedtTrafficBroker) parseApp(bidRequest *protos.Request, adRequest *ad_service.AdRequest) error {
	app := bidRequest.GetApp()
	if app != nil {
		adRequest.App.AppName = app.GetName()
		adRequest.App.AppBundle = app.GetBundle()
		adRequest.App.AppVersion = app.GetAppVersion()
		adRequest.App.MediaInstalledAppIds = app.GetUserTag()
	}

	return nil
}

func (b *JxedtTrafficBroker) parseDevice(bidRequest *protos.Request, adRequest *ad_service.AdRequest) error {
	device := bidRequest.GetDevice()
	if device != nil {
		adRequest.Device.UserAgent = device.GetUa()
		adRequest.Device.RequestIp = device.GetIp()
		adRequest.Device.RomVersion = device.GetMake()
		adRequest.Device.Vendor = device.GetMake()
		adRequest.Device.Brand = device.GetMake()
		adRequest.Device.Model = device.GetModel()
		adRequest.Device.OsVersion = device.GetOsv()
		adRequest.Device.ScreenWidth = device.GetWidth()
		adRequest.Device.ScreenHeight = device.GetHeight()
		adRequest.Device.PPI = device.GetPpi()

		adRequest.Device.Idfa = device.GetIdfa()
		adRequest.Device.IdfaMd5 = device.GetIdfaMd5()
		adRequest.Device.Imei = device.GetImei()
		adRequest.Device.ImeiMd5 = device.GetImeiMd5()
		adRequest.Device.Oaid = device.GetOaid()
		adRequest.Device.AndroidId = device.GetAndroidId()
		adRequest.Device.Mac = device.GetMac()
		adRequest.Device.DeviceStartupTime = device.GetBootTime()
		adRequest.Device.DeviceUpgradeTime = device.GetUpdateTime()
		adRequest.Device.CountryCode = device.GetCountry()
		adRequest.Device.Language = device.GetLanguage()
		adRequest.Device.DeviceName = device.GetPhoneName()
		if len(device.GetMemorySize()) > 0 {
			adRequest.Device.SystemTotalMem, _ = strconv.ParseInt(device.GetMemorySize(), 10, 64)
		}
		if len(device.GetDiskSize()) > 0 {
			adRequest.Device.SystemTotalDisk, _ = strconv.ParseInt(device.GetDiskSize(), 10, 64)
		}
		adRequest.Device.HardwareMachineCode = device.GetModelCode()
		// device.GetTimezone()
		adRequest.Device.BootMark = device.GetBootMark()
		adRequest.Device.UpdateMark = device.GetUpdateMark()
		adRequest.Device.DeviceInitTime = device.GetBirthTime()
		adRequest.Device.VercodeAg = device.GetAgVersion()
		adRequest.Device.VercodeHms = device.GetHmsVersion()
		adRequest.Device.Paid = device.GetPaid()
		adRequest.Device.CaidRaw = device.GetCaid()
		adRequest.Device.CaidVersion = device.GetCaidVersion()
		if len(device.GetCaid()) > 0 {
			caid := string_utils.ConcatString(device.GetCaidVersion(), "_", device.GetCaid())
			adRequest.Device.Caid = caid
			adRequest.Device.Caids = append(adRequest.Device.Caids, caid)
		}

		geo := device.GetGeo()
		if geo != nil {
			adRequest.Device.Lat = geo.GetLat()
			adRequest.Device.Lon = geo.GetLon()
		}

		switch device.GetDeviceType() {
		case 0:
			adRequest.Device.DeviceType = entity.DeviceTypeMobile
		case 1:
			adRequest.Device.DeviceType = entity.DeviceTypePad
		case 2:
			adRequest.Device.DeviceType = entity.DeviceTypePc
		case 3:
			adRequest.Device.DeviceType = entity.DeviceTypeOtt
		default:
			adRequest.Device.DeviceType = entity.DeviceTypeUnknown
		}

		switch strings.ToLower(device.GetOs()) {
		case "ios":
			adRequest.Device.OsType = entity.OsTypeIOS
			adRequest.Device.IsMobile = true
		case "android":
			adRequest.Device.OsType = entity.OsTypeAndroid
			adRequest.Device.IsMobile = true
		default:
			adRequest.Device.OsType = entity.OsTypeUnknown
		}

		switch device.GetNetwork() {
		case 1:
			adRequest.Device.ConnectionType = entity.ConnectionTypeWifi
		case 2:
			adRequest.Device.ConnectionType = entity.ConnectionType2G
		case 3:
			adRequest.Device.ConnectionType = entity.ConnectionType3G
		case 4:
			adRequest.Device.ConnectionType = entity.ConnectionType4G
		case 5:
			adRequest.Device.ConnectionType = entity.ConnectionType5G
		default:
			adRequest.Device.ConnectionType = entity.ConnectionTypeUnknown
		}

		switch device.GetCarrier() {
		case 1:
			adRequest.Device.OperatorType = entity.OperatorTypeChinaMobile
		case 2:
			adRequest.Device.OperatorType = entity.OperatorTypeChinaTelecom
		case 3:
			adRequest.Device.OperatorType = entity.OperatorTypeChinaUnicom
		default:
			adRequest.Device.OperatorType = entity.OperatorTypeUnknown
		}
	}

	return nil
}

func (b *JxedtTrafficBroker) parseUser(bidRequest *protos.Request, adRequest *ad_service.AdRequest) error {
	user := bidRequest.GetUser()
	if user != nil {
		switch user.GetGender() {
		case 1:
			adRequest.UserGender = entity.UserGenderMan
		case 2:
			adRequest.UserGender = entity.UserGenderWoman
		}

		// TODO:
		// user.GetAge()
	}

	return nil
}
