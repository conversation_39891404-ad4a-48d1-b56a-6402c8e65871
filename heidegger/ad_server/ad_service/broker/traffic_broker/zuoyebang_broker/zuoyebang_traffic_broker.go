package zuoyebang_broker

import (
	"errors"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/zuoyebang_broker/zuoyebang_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"fmt"
)

type ZuoyebangTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	mediaId       utils.ID
	WinPriceMacro string
	log           *zap.Logger
}

// var _ TrafficBrokerInterface = (*ZuoyebangTrafficBroker)(nil)

func NewZuoyebangTrafficBroker(mediaId utils.ID) *ZuoyebangTrafficBroker {
	return &ZuoyebangTrafficBroker{
		mediaId:       mediaId,
		WinPriceMacro: "__ZADX_AUCTION_PRICE__",
		log:           zap.L().With(zap.String("broker", "ZuoyebangTrafficBroker")),
	}
}

func (b *ZuoyebangTrafficBroker) Do(request *ad_service.AdRequest) error {
	return b.ParseBidRequest(request)
}

func (b *ZuoyebangTrafficBroker) GetMediaId() utils.ID {
	return b.mediaId
}

func (b *ZuoyebangTrafficBroker) ParseBidRequest(adRequest *ad_service.AdRequest) error {
	adRequest.Response.SetResponseBuilder(b.BuildResponse)
	adRequest.Response.SetFallbackResponseBuilder(b.BuildFallbackResponse)
	adRequest.AdRequestMedia.WinPriceMacro = b.WinPriceMacro

	body := adRequest.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("[ZuoyebangTrafficBroker] request body empty")
	}

	bidRequest := new(zuoyebang_entity.BidRequest)
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		b.log.WithError(err).WithField("body", string(body)).Error("ParseBidRequest error")
		return errors.New("[ZuoyebangTrafficBroker] request body invalid")
	}

	if adRequest.IsDebug {
		b.log.WithField("request", string(body)).Info("parse request start")
	}

	adRequest.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		adRequest.SetRequestId(utils.NewUUID())
	}
	adRequest.SetMediaId(b.mediaId)

	if err := b.parseImp(bidRequest, adRequest); err != nil {
		b.log.Debug("parseImp fail")
		return err
	}

	if err := b.parseApp(bidRequest, adRequest); err != nil {
		b.log.Debug("parseApp fail")
		return err
	}

	if err := b.parseDevice(bidRequest, adRequest); err != nil {
		b.log.Debug("parseDevice fail")
		return err
	}

	b.DoTrafficSample(adRequest, body)

	return nil
}

func (b *ZuoyebangTrafficBroker) BuildResponse(adRequest *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if adRequest.IsDebug {
		b.log.Info("BuildResponse start")
		adRequest.Response.Dump("ZuoyebangTrafficBroker")
	}

	if adRequest.Response.NoCandidate() {
		return b.BuildFallbackResponse(adRequest, writer)
	}

	bidResponse := new(zuoyebang_entity.BidResponse)
	bidResponse.Id = adRequest.GetRequestId()
	bidResponse.RetCode = 204

	for _, candidate := range adRequest.Response.GetAdCandidateList() {
		bidResponse.RetCode = 200
		ad := new(zuoyebang_entity.BidResponseAd)

		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		bidPrice := candidate.GetBidPrice()

		if creative.GetCreativeId() > 0 {
			ad.CreativeId = creative.GetCreativeId().String()
		} else {
			ad.CreativeId = creative.GetCreativeKey()
		}
		ad.AdId = adRequest.GetMediaSlotKey()
		ad.Price = int(bidPrice.Price)

		_ = b.buildMaterial(ad, candidate)
		_ = b.buildTracking(ad, candidate)

		appInfo := genericAd.GetAppInfo()
		if appInfo != nil && len(appInfo.AppName) > 0 {
			ad.AppLogo = appInfo.Icon
			ad.AppName = appInfo.AppName
			ad.AppVersion = appInfo.AppVersion
			ad.AppDeveloper = appInfo.Develop
			ad.AppPrivacyUrl = appInfo.Privacy
			ad.AppFuncDescUrl = appInfo.AppDescURL
			ad.AppPermissionUrl = appInfo.Permission
		}

		bidResponse.Ad = append(bidResponse.Ad, ad)
		break
	}

	if err := b.BuildHttpSonicJsonResponse(adRequest, writer, &bidResponse); err != nil {
		return err
	}

	b.DoTrafficResponseSampleSonicJson(adRequest, bidResponse)
	if adRequest.IsDebug {
		data, _ := sonic.Marshal(bidResponse)
		b.log.WithField("response", string(data)).Info("BuildResponse success")
	}
	return nil
}

func (b *ZuoyebangTrafficBroker) BuildFallbackResponse(adRequest *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if adRequest.IsDebug {
		for adId, errCode := range adRequest.Response.GetTotalAdCandidateList().GetErrCodeMap() {
			b.zap.L().Info("BuildFallbackResponse", zap.String("ad", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", adId)))), zap.String("code", fmt.Sprintf("%v", errCode)))
		}
	}

	bidResponse := new(zuoyebang_entity.BidResponse)
	bidResponse.Id = adRequest.GetRequestId()
	bidResponse.RetCode = 204

	if err := b.BuildHttpSonicJsonResponse(adRequest, writer, &bidResponse); err != nil {
		return err
	}
	return nil
}

func (b *ZuoyebangTrafficBroker) parseImp(bidRequest *zuoyebang_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	if len(bidRequest.Imp) < 1 {
		b.log.Error("parseImp no imp")
		return errors.New("[ZuoyebangTrafficBroker] request no imp")
	}

	imp := bidRequest.Imp[0]
	adRequest.ImpressionId = bidRequest.Id
	adRequest.SetMediaSlotKey(imp.AdId)
	adRequest.SetBidFloor(uint32(imp.BidFloor))

	var width, height = imp.Width, imp.Height
	adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
		Width:  int64(width),
		Height: int64(height),
	})
	adRequest.SlotWidth = uint32(width)
	adRequest.SlotHeight = uint32(height)
	// Set/get SlotType on media slot
	// adRequest.SlotType

	// image
	tmp1 := creative_entity.NewCreativeTemplateKey()
	tmp1.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
	tmp1.Title().SetRequiredCount(1).SetOptional(true)
	tmp1.Desc().SetRequiredCount(1).SetOptional(true)
	adRequest.AppendCreativeTemplateKey(tmp1)

	// video
	tmp2 := creative_entity.NewCreativeTemplateKey()
	tmp2.Video().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
	tmp2.Image().SetRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height).SetOptional(true)
	tmp2.Title().SetRequiredCount(1).SetOptional(true)
	tmp2.Desc().SetRequiredCount(1).SetOptional(true)
	adRequest.AppendCreativeTemplateKey(tmp2)

	// add more creative template as needed, Zuoyebang can adaptive rendering

	return nil
}

func (b *ZuoyebangTrafficBroker) parseApp(bidRequest *zuoyebang_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	app := bidRequest.App
	if app != nil {
		adRequest.App.AppName = app.Name
		adRequest.App.AppBundle = app.Bundle
		adRequest.App.AppVersion = app.Ver
	}

	return nil
}

func (b *ZuoyebangTrafficBroker) parseDevice(bidRequest *zuoyebang_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	device := bidRequest.Device
	if device != nil {
		adRequest.Device.UserAgent = device.Ua
		adRequest.Device.RequestIp = device.Ip
		adRequest.Device.Vendor = device.Manufacturer
		adRequest.Device.Brand = device.Manufacturer
		adRequest.Device.Model = device.Model
		adRequest.Device.OsVersion = device.OsVersion
		adRequest.Device.ScreenWidth = int32(device.ScreenWidth)
		adRequest.Device.ScreenHeight = int32(device.ScreenHeight)
		adRequest.Device.DeviceName = device.DeviceNameMd5
		adRequest.Device.RomVersion = device.SysRomVersion
		adRequest.Device.DeviceInitTime = device.DeviceBirthSec
		adRequest.Device.DeviceStartupTime = device.DeviceStartSec
		adRequest.Device.DeviceUpgradeTime = device.SystemUpdateSec
		adRequest.Device.CountryCode = device.Country
		adRequest.Device.Language = device.Language
		adRequest.Device.HardwareMachineCode = device.HardwareMachine
		adRequest.Device.BootMark = device.BootMark
		adRequest.Device.UpdateMark = device.UpdateMark
		adRequest.Device.VercodeAg = device.AppStoreVersion
		adRequest.Device.VercodeHms = device.HmsCoreVersion
		adRequest.Device.CaidRaw = device.Caid
		adRequest.Device.CaidVersion = device.CaidVersion
		adRequest.App.InstalledApp = device.AppList

		adRequest.Device.DeviceType = entity.DeviceTypeMobile
		adRequest.Device.IsMobile = true

		adRequest.Device.Idfa = device.Idfa
		if device.Idfa != "" {
			adRequest.Device.IdfaMd5 = md5_utils.GetMd5String(device.Idfa)
		}
		adRequest.Device.Imei = device.Imei
		if device.Imei != "" {
			adRequest.Device.ImeiMd5 = md5_utils.GetMd5String(device.Imei)
		}
		adRequest.Device.Oaid = device.Oaid
		if device.Oaid != "" {
			adRequest.Device.OaidMd5 = md5_utils.GetMd5String(device.Oaid)
		}
		adRequest.Device.AndroidId = device.AndroidId
		if device.AndroidId != "" {
			adRequest.Device.AndroidIdMd5 = md5_utils.GetMd5String(device.AndroidId)
		}
		adRequest.Device.Mac = device.Mac
		if device.Mac != "" {
			adRequest.Device.MacMd5 = md5_utils.GetMd5String(device.Mac)
		}

		if len(device.TimeZone) > 0 {
			tz, _ := strconv.ParseInt(device.TimeZone, 10, 32)
			adRequest.Device.TimeZone = int32(tz)
		}
		if len(device.PhysicalMemoryByte) > 0 {
			adRequest.Device.SystemTotalMem, _ = strconv.ParseInt(device.PhysicalMemoryByte, 10, 64)
		}
		if len(device.HardDiskSizeByte) > 0 {
			adRequest.Device.SystemTotalDisk, _ = strconv.ParseInt(device.HardDiskSizeByte, 10, 64)
		}

		switch strings.ToLower(device.Os) {
		case "ios":
			adRequest.Device.OsType = entity.OsTypeIOS
		case "android":
			adRequest.Device.OsType = entity.OsTypeAndroid
		default:
			adRequest.Device.OsType = entity.OsTypeUnknown
		}

		switch device.NetworkType {
		case 1:
			adRequest.Device.ConnectionType = entity.ConnectionTypeWifi
		case 2:
			adRequest.Device.ConnectionType = entity.ConnectionType2G
		case 3:
			adRequest.Device.ConnectionType = entity.ConnectionType3G
		case 4:
			adRequest.Device.ConnectionType = entity.ConnectionType4G
		case 5:
			adRequest.Device.ConnectionType = entity.ConnectionType5G
		default:
			adRequest.Device.ConnectionType = entity.ConnectionTypeUnknown
		}

		switch device.Carrier {
		case 1:
			adRequest.Device.OperatorType = entity.OperatorTypeChinaMobile
		case 2:
			adRequest.Device.OperatorType = entity.OperatorTypeChinaUnicom
		case 3:
			adRequest.Device.OperatorType = entity.OperatorTypeChinaTelecom
		default:
			adRequest.Device.OperatorType = entity.OperatorTypeUnknown
		}
	}

	return nil
}

func (b *ZuoyebangTrafficBroker) buildMaterial(ad *zuoyebang_entity.BidResponseAd, candidate *ad_service.AdCandidate) error {
	isVideo := false
	imgUrl := ""
	for _, asset := range candidate.GetSelectedMaterialList() {
		switch asset.MaterialType {
		case entity.MaterialTypeImage:
			imgUrl = candidate.ReplaceUrlGeneral(asset.Url)
			ad.ImageList = append(ad.ImageList, &zuoyebang_entity.BidResponseImage{
				Width:  int(asset.Width),
				Height: int(asset.Height),
				Url:    imgUrl,
			})
		case entity.MaterialTypeTitle:
			ad.Title = asset.Data
		case entity.MaterialTypeDesc:
			ad.Description = asset.Data
		case entity.MaterialTypeVideo:
			isVideo = true
			ad.Video = &zuoyebang_entity.BidResponseVideo{
				Duration: int(asset.Duration),
				Width:    int(asset.Width),
				Height:   int(asset.Height),
				Url:      candidate.ReplaceUrlGeneral(asset.Url),
				Size:     int(asset.FileSize),
			}
		}
	}
	if isVideo { // restore video data
		ad.Video.PreviewImgUrl = imgUrl
		ad.ImageList = nil
	}

	return nil
}

func (b *ZuoyebangTrafficBroker) buildTracking(ad *zuoyebang_entity.BidResponseAd, candidate *ad_service.AdCandidate) error {
	genericAd := candidate.GetGenericAd()
	trafficData := candidate.GetModifiedTrafficData()
	trackingGen := candidate.GetTrackingGen(trafficData)

	if len(genericAd.GetImpressionMonitorList()) > 0 {
		// ad.WinNoticeUrl =
		// ad.LossNoticeUrl =
		ad.EventTracks = append(ad.EventTracks, &zuoyebang_entity.BidResponseEventTrack{
			Urls:      candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), trafficData, trackingGen),
			EventType: 100,
		})
	}
	if len(genericAd.GetClickMonitorList()) > 0 {
		ad.EventTracks = append(ad.EventTracks, &zuoyebang_entity.BidResponseEventTrack{
			Urls:      candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), trafficData, trackingGen),
			EventType: 200,
		})
	}
	if len(genericAd.GetDeepLinkMonitorList()) > 0 {
		ad.EventTracks = append(ad.EventTracks, &zuoyebang_entity.BidResponseEventTrack{
			Urls:      candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), trafficData, trackingGen),
			EventType: 401,
		})
	}
	if len(genericAd.GetDeepLinkFailedMonitorList()) > 0 {
		ad.EventTracks = append(ad.EventTracks, &zuoyebang_entity.BidResponseEventTrack{
			Urls:      candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkFailedMonitorList(), trafficData, trackingGen),
			EventType: 402,
		})
	}
	if len(genericAd.GetVideoStartUrlList()) > 0 {
		ad.EventTracks = append(ad.EventTracks, &zuoyebang_entity.BidResponseEventTrack{
			Urls:      candidate.ReplaceUrlMacroList(genericAd.GetVideoStartUrlList(), trafficData, trackingGen),
			EventType: 512,
		})
	}
	if len(genericAd.GetVideoCloseUrlList()) > 0 {
		ad.EventTracks = append(ad.EventTracks, &zuoyebang_entity.BidResponseEventTrack{
			Urls:      candidate.ReplaceUrlMacroList(genericAd.GetVideoCloseUrlList(), trafficData, trackingGen),
			EventType: 511,
		})
	}
	if len(genericAd.GetDelayMonitorUrlList()) > 0 {
		urls := make([]string, 0, len(genericAd.GetDelayMonitorUrlList()))
		for _, url := range genericAd.GetDelayMonitorUrlList() {
			urls = append(urls, candidate.ReplaceUrlMacro(url.Url, trafficData, trackingGen))
		}
		ad.EventTracks = append(ad.EventTracks, &zuoyebang_entity.BidResponseEventTrack{
			Urls:      urls,
			EventType: 505,
		})
	}

	ad.Deeplink = candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), trafficData, trackingGen)
	if trafficData.GetOsType() == entity.OsTypeIOS {
		ad.UniversalLink = candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), trafficData, trackingGen)
	}

	if genericAd.GetLandingAction() == entity.LandingTypeDownload {
		ad.DownloadType = 1
		ad.TargetUrl = candidate.ReplaceUrlMacro(genericAd.GetDownloadUrl(), trafficData, trackingGen)
	} else {
		ad.TargetUrl = candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), trafficData, trackingGen)
	}

	return nil
}
