package qimao_traffic_broker

import (
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"github.com/spf13/cast"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/qimao_traffic_broker/qimao_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	redisclient "gitlab.com/dev/heidegger/library/redis_client"
	"gitlab.com/dev/heidegger/library/utils"
)

const (
	adxTemplateKey  = "adxTemplate"
	reqSegmentKey   = "reqSegment"
	redisKeyExpired = 180 * time.Second //素材默认过期时间
)

type QiMaoTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log          *zap.Logger
	mediaId      utils.ID
	mediaMacro   *macro_builder.MediaMacro
	redisCluster *redisclient.RedisClusterClient
}

func NewQiMaoTrafficBroker(mediaId utils.ID, redisCluster *redisclient.RedisClusterClient) *QiMaoTrafficBroker {
	return &QiMaoTrafficBroker{
		log:     zap.L().With(zap.String("broker", "QiMaoTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "__PRICE__",
			MediaClickDownXMacro: "__DOWN_X__",
			MediaClickDownYMacro: "__DOWN_Y__",
			MediaClickUpXMacro:   "__UP_X__",
			MediaClickUpYMacro:   "__UP_Y__",
			MediaHWSldMacro:      "__SLD__",
			MediaLossPriceMacro:  "__WIN_PRICE__",
		},
		redisCluster: redisCluster,
	}
}

func (q *QiMaoTrafficBroker) GetMediaId() utils.ID {
	return q.mediaId
}

func (q *QiMaoTrafficBroker) GetAd(c echo.Context) error {
	bidResponse := &qimao_proto.AdResponse{}
	debug := cast.ToBool(c.QueryParam("debug"))

	body, _ := io.ReadAll(c.Request().Body)
	if len(body) == 0 {
		bidResponse.Code = 1
		bidResponse.Msg = "request body empty"
	} else {
		bidRequest := &qimao_proto.AdRequest{}
		err := proto.Unmarshal(body, bidRequest)
		if err != nil {
			q.log.WithError(err).Error("Request body unmarshal failed")
			bidResponse.Code = 1
			bidResponse.Msg = "request body invalid"
			goto res
		}

		if debug {
			reqBody, _ := sonic.Marshal(bidRequest)
			q.log.Infof("Parse GetAd Request start. broker request: [%s]", reqBody)
		}

		if len(bidRequest.Token) < 1 {
			bidResponse.Code = 1
			bidResponse.Msg = "empty token"
			goto res
		}

		// token检验
		if err = uuid.Validate(bidRequest.Token); err != nil {
			q.log.WithError(err).Error("token validate failed")
			bidResponse.Code = 1
			bidResponse.Msg = "empty cache"
			goto res
		}
		redisKey := q.buildRedisKey(bidRequest.Token)
		bytes, err := q.redisCluster.GetBytes(redisKey)
		if err != nil {
			bidResponse.Code = 1
			bidResponse.Msg = "get cache err"
			goto res
		}
		go q.redisCluster.Del(redisKey)
		if len(bytes) < 1 {
			bidResponse.Code = 1
			bidResponse.Msg = "empty cache"
			goto res
		}

		priceResponse := &qimao_proto.PriceResponse{}
		if err = proto.Unmarshal(bytes, priceResponse); err != nil {
			q.log.WithError(err).Error("redis value unmarshal failed")
			bidResponse.Code = 1
			bidResponse.Msg = err.Error()
			goto res
		}

		if priceResponse.Data == nil {
			bidResponse.Code = 2
			bidResponse.Msg = "empty priceResponse data"
			goto res
		}
		bidResponse.Data = &qimao_proto.AdResponse_Data{
			RequestId: bidRequest.RequestId,
			Token:     bidRequest.Token,
			Ext:       priceResponse.Data.Ext,
		}

		if ads := priceResponse.Data.Ads; ads != nil {
			bidResponse.Data.Aid = ads.Aid
			bidResponse.Data.Cid = ads.Cid

			bidResponse.Data.Ads = &qimao_proto.AdResponse_Data_Ads{
				Action:         ads.Action,
				TargetUrl:      ads.TargetUrl,
				ButtonText:     ads.ButtonText,
				Title:          ads.Title,
				Desc:           ads.Desc,
				Deeplink:       ads.Deeplink,
				Source:         ads.Source,
				RewardDuration: ads.RewardDuration,
			}

			if ads.Icon != nil {
				bidResponse.Data.Ads.Icon = &qimao_proto.AdResponse_Data_Ads_Icon{
					Url: ads.Icon.Url,
					W:   ads.Icon.W,
					H:   ads.Icon.H,
				}
			}
			for _, img := range ads.Image {
				bidResponse.Data.Ads.Image = append(bidResponse.Data.Ads.Image, &qimao_proto.AdResponse_Data_Ads_Image{
					Url: img.Url,
					W:   img.W,
					H:   img.H,
				})
			}
			if ads.Video != nil {
				bidResponse.Data.Ads.Video = &qimao_proto.AdResponse_Data_Ads_Video{
					CoverUrl: ads.Video.CoverUrl,
					Duration: ads.Video.Duration,
					H:        ads.Video.H,
					W:        ads.Video.W,
					Size_:    ads.Video.Size_,
					Url:      ads.Video.Url,
				}
			}
			if ads.AppInfo != nil {
				bidResponse.Data.Ads.AppInfo = &qimao_proto.AdResponse_Data_Ads_AppInfo{
					AppName:        ads.AppInfo.AppName,
					AppPermission:  ads.AppInfo.AppPermission,
					AppPrivacy:     ads.AppInfo.AppPrivacy,
					AppSize:        ads.AppInfo.AppSize,
					AppVersion:     ads.AppInfo.AppVersion,
					Developer:      ads.AppInfo.Developer,
					DownloadUrl:    ads.AppInfo.DownloadUrl,
					PackageName:    ads.AppInfo.PackageName,
					AppDescription: ads.AppInfo.AppDescription,
					AppMd5:         ads.AppInfo.AppMd5,
				}
			}
			if ads.Applet != nil {
				bidResponse.Data.Ads.Applet = &qimao_proto.AdResponse_Data_Ads_Applet{
					OriginalId: ads.Applet.OriginalId,
					Path:       ads.Applet.Path,
				}
			}
		}
		if priceResponse.Data.TrackUrls != nil {
			bidResponse.Data.TrackUrls = &qimao_proto.AdResponse_Data_TrackUrls{
				ExposeUrls:       priceResponse.Data.TrackUrls.ExposeUrls,
				ClickUrls:        priceResponse.Data.TrackUrls.ClickUrls,
				DownloadStart:    priceResponse.Data.TrackUrls.DownloadStart,
				DownloadFinish:   priceResponse.Data.TrackUrls.DownloadFinish,
				InstallStart:     priceResponse.Data.TrackUrls.InstallStart,
				InstallFinish:    priceResponse.Data.TrackUrls.InstallFinish,
				DeeplinkSuccess:  priceResponse.Data.TrackUrls.DeeplinkSuccess,
				DeeplinkFail:     priceResponse.Data.TrackUrls.DeeplinkFail,
				VideoPlay0:       priceResponse.Data.TrackUrls.VideoPlay0,
				VideoPlay1:       priceResponse.Data.TrackUrls.VideoPlay1,
				VideoPlay2:       priceResponse.Data.TrackUrls.VideoPlay2,
				VideoPlay3:       priceResponse.Data.TrackUrls.VideoPlay3,
				VideoPlay4:       priceResponse.Data.TrackUrls.VideoPlay4,
				VideoReward:      priceResponse.Data.TrackUrls.VideoReward,
				VideoSkip:        priceResponse.Data.TrackUrls.VideoSkip,
				AppletcallupSucc: priceResponse.Data.TrackUrls.AppletcallupSucc,
				AppletcallupFail: priceResponse.Data.TrackUrls.AppletcallupFail,
			}
		}
	}

res:
	if debug {
		resBody, _ := sonic.Marshal(bidResponse)
		q.log.Infof("Parse GetAd Response end. broker response: %s", resBody)
	}

	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()
	buffer.EnsureSize(bidResponse.Size())
	_, err := bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		q.log.Errorf("Error in JSON marshalling:%s", err.Error())
		return err
	}

	c.Response().Header().Set("Content-Type", "application/octet-stream")
	if _, err = c.Response().Write(buffer.Get()); err != nil {
		return err
	}
	return nil
}

func (q *QiMaoTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(q.SendResponse)
	request.Response.SetFallbackResponseBuilder(q.SendResponse)
	request.AdRequestMedia.WinPriceMacro = q.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = q.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &qimao_proto.PriceRequest{}
	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		q.log.WithError(err).Error("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		q.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	if bidRequest.Tmax > 0 {
		request.TMax = int(bidRequest.Tmax)
	}
	reqSegment := int64(2) // 默认二段请求
	if bidRequest.ReqSegment == 1 {
		reqSegment = 1
	}
	request.AddMediaExtraInt64(reqSegmentKey, reqSegment)
	request.SetMediaId(q.GetMediaId())
	request.SetRequestId(bidRequest.RequestId)
	if len(bidRequest.RequestId) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}

	q.parseApp(bidRequest, request)
	q.parseUser(bidRequest, request)
	q.parseDevice(bidRequest, request)
	if err = q.parseImp(bidRequest, request); err != nil {
		q.log.WithError(err).Debugf("BrokeRequest, parseImp failed")
		return err
	}

	q.DoTrafficSamplePb(request, bidRequest)

	return nil
}

func (q *QiMaoTrafficBroker) parseApp(bidRequest *qimao_proto.PriceRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.Media != nil {
		adRequest.App.AppName = bidRequest.Media.Name
		adRequest.App.AppBundle = bidRequest.Media.Bundle
	}
}

func (q *QiMaoTrafficBroker) parseUser(bidRequest *qimao_proto.PriceRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.User != nil {
		adRequest.UserGender = mappingUserGender(bidRequest.User.Gender)

		appList := q.getAppList(bidRequest.User.Pp)
		if len(appList) > 0 {
			adRequest.App.MediaInstalledAppIds = appList
		}
	}
}

func (q *QiMaoTrafficBroker) parseDevice(bidRequest *qimao_proto.PriceRequest, adRequest *ad_service.AdRequest) {
	if bidRequest.Device != nil {
		adRequest.Device = ad_service.AdRequestDevice{
			OsType:            mappingOsType(bidRequest.Device.Os),
			OsVersion:         bidRequest.Device.Osv,
			DeviceType:        entity.DeviceTypeUnknown,
			DeviceStartupTime: bidRequest.Device.BirthTime,
			AppStoreVersion:   bidRequest.Device.AppStoreVersion,
			BootMark:          bidRequest.Device.BootMark,
			UpdateMark:        bidRequest.Device.UpdateMark,
			VercodeHms:        bidRequest.Device.HmsCoreVersion,
			Idfa:              bidRequest.Device.Idfa,
			IdfaMd5:           bidRequest.Device.IdfaMd5,
			ImeiMd5:           bidRequest.Device.ImeiMd5,
			Mac:               bidRequest.Device.Mac,
			AndroidId:         bidRequest.Device.AndroidId,
			Oaid:              bidRequest.Device.Oaid,
			OaidMd5:           bidRequest.Device.OaidMd5,
			Idfv:              bidRequest.Device.Idfv,
			Model:             bidRequest.Device.Model,
			Brand:             bidRequest.Device.Brand,
			Vendor:            bidRequest.Device.Make,
			Language:          "zh",
			CountryCode:       "CN",
			ScreenHeight:      int32(bidRequest.Device.H),
			ScreenWidth:       int32(bidRequest.Device.W),
			UserAgent:         bidRequest.Device.Ua,
			WebviewUA:         bidRequest.Device.Ua,
			DPI:               bidRequest.Device.Dpi,
			RequestIp:         bidRequest.Ip,
			DeviceInitTime:    bidRequest.Device.BirthTime,
		}

		if len(bidRequest.Device.HmsCoreVersion) > 0 {
			adRequest.Device.VercodeAg = bidRequest.Device.AppStoreVersion
		}

		if bidRequest.Device.Os == qimao_proto.PriceRequest_Device_OsAndroid ||
			bidRequest.Device.Os == qimao_proto.PriceRequest_Device_OsIos {
			adRequest.Device.IsMobile = true
			adRequest.Device.DeviceType = entity.DeviceTypeMobile
		}

		if len(bidRequest.Device.Usecret) > 0 {
			adRequest.Device.CaidRaw = bidRequest.Device.Usecret
			adRequest.Device.CaidVersion = bidRequest.Device.Usecretversion
			adRequest.Device.Caid = bidRequest.Device.Usecretversion + "_" + bidRequest.Device.Usecret
			adRequest.Device.Caids = append(adRequest.Device.Caids, adRequest.Device.Caid)
		}
		if len(bidRequest.Device.Preusecret) > 0 {
			if len(adRequest.Device.Caid) > 0 {
				adRequest.Device.Caids = append(adRequest.Device.Caids, bidRequest.Device.Preusecretversion+"_"+bidRequest.Device.Preusecret)
			} else {
				adRequest.Device.Caid = bidRequest.Device.Preusecretversion + "_" + bidRequest.Device.Preusecret
				adRequest.Device.Caids = append(adRequest.Device.Caids, adRequest.Device.Caid)
			}
		}
	}

	if bidRequest.Network != nil {
		adRequest.Device.OperatorType = mappingOperatorType(bidRequest.Network.Carrier)
		adRequest.Device.ConnectionType = mappingConnectionType(bidRequest.Network.ConnectionType)
	}

	if len(adRequest.Device.BootMark) < 1 {
		adRequest.Device.BootMark = bidRequest.Device.PddBootMark
		adRequest.Device.UpdateMark = bidRequest.Device.PddUpdateMark
	}
}

func (q *QiMaoTrafficBroker) getAppList(pp string) []string {
	decodedBytes, err := base64.StdEncoding.DecodeString(pp)
	if err != nil {
		return make([]string, 0)
	}

	var installedApps []string
	maxPos := len(decodedBytes) * 8
	for pos := range maxPos {
		byteIndex := pos / 8
		bitIndex := pos % 8
		if byteIndex < len(decodedBytes) {
			if (decodedBytes[byteIndex]>>(bitIndex))&1 == 1 {
				installedApps = append(installedApps, strconv.Itoa(pos))
			}
		}
	}

	return installedApps
}

func (q *QiMaoTrafficBroker) parseImp(bidRequest *qimao_proto.PriceRequest, adRequest *ad_service.AdRequest) error {
	if bidRequest.Pos != nil {
		adRequest.SetMediaSlotKey(bidRequest.Pos.Id)
		adRequest.BidFloor = uint32(bidRequest.Pos.Floor)
		adRequest.BidType = entity.BidTypeCpm

		adRequest.SlotWidth = bidRequest.Pos.W
		adRequest.SlotHeight = bidRequest.Pos.H

		adxTemplateMap := make(map[uint64]int)
		for i, materialType := range bidRequest.Pos.MaterialType {
			key := creative_entity.NewCreativeTemplateKey()
			switch materialType {
			case qimao_proto.PriceRequest_Pos_VideoWord:
				// 非WIFI情况下不返回视频素材
				if adRequest.Device.ConnectionType != entity.ConnectionTypeWifi {
					continue
				}
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
				key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			default: //默认图文
				key.Title().AddRequiredCount(1).SetOptional(true)
				key.Desc().AddRequiredCount(1).SetOptional(true)
				key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
			}

			adRequest.AppendCreativeTemplateKey(key)
			adxTemplateMap[key.Uint64()] = i
		}

		adRequest.AddMediaExtraData(adxTemplateKey, adxTemplateMap)

		if len(bidRequest.Pos.DealId) > 0 {
			adRequest.SourceDeal = append(adRequest.SourceDeal, ad_service.SourceDeal{
				DealId:   bidRequest.Pos.DealId,
				BidFloor: int64(bidRequest.Pos.Floor),
			})
		}

		return nil
	}

	return errors.New("empty imp")
}

func (q *QiMaoTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		q.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("QiMaoTrafficBroker")
	}

	bidResponse, err := q.buildResponse(request)
	if err != nil {
		q.log.WithError(err).Error("buildResponse err")
		return err
	}

	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()
	buffer.EnsureSize(bidResponse.Size())
	_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		q.log.Errorf("Error in JSON marshalling:%s", err.Error())
		return err
	}

	q.DoTrafficResponseSamplePb(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		q.log.Infof("SendResponse success, response: %s", responseStr)
	}

	// 如果是二段请求，把第一次请求的响应存到Redis
	if bidResponse.Code == 0 && len(bidResponse.Data.Token) > 0 {
		err = q.redisCluster.Set(q.buildRedisKey(bidResponse.Data.Token), buffer.Get(), redisKeyExpired)
		if err != nil {
			return err
		}

		// 清空素材和监控信息
		bidResponse.Data.Ads = nil
		bidResponse.Data.TrackUrls = nil
		buffer.Reset()
		buffer.EnsureSize(bidResponse.Size())
		_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
		if err != nil {
			q.log.Errorf("Error in JSON marshalling:%s", err.Error())
			return err
		}
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/octet-stream")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	return nil
}

func (q *QiMaoTrafficBroker) buildRedisKey(token string) string {
	return fmt.Sprintf("adcache:%d:%s", q.mediaId, token)
}

func (q *QiMaoTrafficBroker) buildResponse(request *ad_service.AdRequest) (*qimao_proto.PriceResponse, error) {
	if err := request.Response.GetError(); err != nil {
		return &qimao_proto.PriceResponse{Code: 2, Msg: err.Error()}, nil
	}
	if request.Response.NoCandidate() {
		return &qimao_proto.PriceResponse{Code: 2, Msg: "不参与竞价"}, nil
	}

	bidResponse := &qimao_proto.PriceResponse{
		Data: &qimao_proto.PriceResponse_Data{
			RequestId: request.GetRequestId(),
		},
	}
	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		bidResponse.Data.Price = candidate.GetBidPrice().Price
		bidResponse.Data.TrackUrls = &qimao_proto.PriceResponse_Data_TrackUrls{
			ExposeUrls:      candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			ClickUrls:       candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
			DownloadStart:   genericAd.GetAppDownloadStartedMonitorList(),
			DownloadFinish:  genericAd.GetAppDownloadFinishedMonitorList(),
			InstallStart:    genericAd.GetAppInstallStartMonitorList(),
			InstallFinish:   genericAd.GetAppDownloadFinishedMonitorList(),
			DeeplinkSuccess: genericAd.GetDeepLinkMonitorList(),
			DeeplinkFail:    genericAd.GetDeepLinkFailedMonitorList(),
			VideoReward:     genericAd.GetVideoStartUrlList(),
		}

		resBid := &qimao_proto.PriceResponse_Data_Ads{
			TargetUrl: genericAd.GetLandingUrl(),
			Deeplink:  genericAd.GetDeepLinkUrl(),
			Aid:       strconv.FormatInt(int64(traffic.GetAdId()), 10),
			Did:       strconv.FormatInt(int64(traffic.GetAdGroupId()), 10),
		}

		var landingIsDownload bool
		if strings.Contains(genericAd.GetLandingUrl(), ".apk?") ||
			strings.HasSuffix(genericAd.GetLandingUrl(), ".apk") {
			resBid.TargetUrl = genericAd.GetH5LandingUrl()
			landingIsDownload = true
		}

		if creative.GetCreativeId() != 0 {
			resBid.Cid = strconv.FormatInt(int64(creative.GetCreativeId()), 10)
		} else {
			resBid.Cid = creative.GetCreativeKey()
		}

		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			resBid.Action = qimao_proto.AdsAction_Download
		} else if genericAd.GetLandingAction() == entity.LandingTypeWeChatProgram {
			resBid.Action = qimao_proto.AdsAction_WechatApplet
		} else if genericAd.GetLandingAction() == entity.LandingTypeDeepLink {
			resBid.Action = qimao_proto.AdsAction_Deeplink
		} else {
			resBid.Action = qimao_proto.AdsAction_Webview
		}

		if app := genericAd.GetAppInfo(); app != nil {
			resBid.AppInfo = &qimao_proto.PriceResponse_Data_Ads_AppInfo{
				AppName:        app.AppName,
				AppPermission:  app.Permission,
				AppPrivacy:     app.Privacy,
				AppSize:        uint64(app.PackageSize * 1024),
				AppVersion:     app.AppVersion,
				Developer:      app.Develop,
				PackageName:    app.PackageName,
				AppDescription: app.AppDesc,
			}

			if len(app.AppDescURL) > 0 {
				resBid.AppInfo.AppDescription = app.AppDescURL
			}
			if len(genericAd.GetDownloadUrl()) > 0 {
				resBid.AppInfo.DownloadUrl = genericAd.GetDownloadUrl()
			}

			if app.WechatExt != nil {
				resBid.Action = qimao_proto.AdsAction_WechatApplet
				resBid.Applet = &qimao_proto.PriceResponse_Data_Ads_Applet{
					OriginalId: app.WechatExt.ProgramId,
					Path:       app.WechatExt.ProgramPath,
				}
			}
		}
		if landingIsDownload {
			if resBid.AppInfo == nil {
				resBid.AppInfo = &qimao_proto.PriceResponse_Data_Ads_AppInfo{}
			}
			if len(resBid.AppInfo.DownloadUrl) < 1 {
				resBid.AppInfo.DownloadUrl = genericAd.GetLandingUrl()
			}
		}

		for _, material := range candidate.GetSelectedMaterialList() {
			var coverUrl string
			switch material.MaterialType {
			case entity.MaterialTypeTitle:
				resBid.Title = material.Data
			case entity.MaterialTypeDesc:
				resBid.Desc = material.Data
			case entity.MaterialTypeIcon:
				resBid.Icon = &qimao_proto.PriceResponse_Data_Ads_Icon{
					Url: material.Url,
					W:   100,
					H:   100,
				}
				if material.Width > 0 {
					resBid.Icon.W = uint32(material.Width)
				}
				if material.Height > 0 {
					resBid.Icon.H = uint32(material.Height)
				}
			case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
				height, width := material.Height, material.Width
				if height < 1 {
					height = int32(request.SlotHeight)
				}
				if width < 1 {
					width = int32(request.SlotWidth)
				}
				coverUrl = material.Url
				resBid.Image = append(resBid.Image, &qimao_proto.PriceResponse_Data_Ads_Image{
					Url: material.Url,
					W:   uint32(height),
					H:   uint32(width),
				})
			case entity.MaterialTypeVideo:
				resBid.Video = &qimao_proto.PriceResponse_Data_Ads_Video{
					Duration: uint32(material.Duration),
					H:        uint32(material.Height),
					W:        uint32(material.Width),
					Size_:    uint64(material.FileSize * 1024),
					Url:      material.Url,
				}
				if material.Width < 1 {
					resBid.Video.W = request.SlotWidth
				}
				if material.Height < 1 {
					resBid.Video.H = request.SlotHeight
				}
				resBid.RewardDuration = int32(material.Duration)
			default:
			}

			if resBid.Video != nil {
				resBid.Video.CoverUrl = coverUrl
			}
		}

		// 七猫规定：如果是非WiFi环境下，视频素材会转成图片展示
		// 这里直接过滤掉，不返回当前创意
		if request.Device.ConnectionType != entity.ConnectionTypeWifi && resBid.Video != nil {
			continue
		}

		if request.GetMediaExtraInt64WithDefault(reqSegmentKey, 2) == 2 {
			bidResponse.Data.Token = uuid.NewString()
		}
		bidResponse.Data.Ads = resBid
		return bidResponse, nil
	}

	bidResponse.Code = 1
	bidResponse.Msg = "无匹配素材"
	return bidResponse, nil
}

func mappingUserGender(gender qimao_proto.PriceRequest_User_Gender) entity.UserGenderType {
	switch gender {
	case qimao_proto.PriceRequest_User_male:
		return entity.UserGenderMan
	case qimao_proto.PriceRequest_User_female:
		return entity.UserGenderWoman
	default:
		return entity.UserGenderUnknown
	}
}

func mappingOperatorType(carrier qimao_proto.PriceRequest_Network_Carrier) entity.OperatorType {
	switch carrier {
	case qimao_proto.PriceRequest_Network_CarrierChinaMobile:
		return entity.OperatorTypeChinaMobile
	case qimao_proto.PriceRequest_Network_CarrierChinaUnicom:
		return entity.OperatorTypeChinaUnicom
	case qimao_proto.PriceRequest_Network_CarrierChinaTelecom:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType qimao_proto.PriceRequest_Network_ConnType) entity.ConnectionType {
	switch connectionType {
	case qimao_proto.PriceRequest_Network_ConnTypeCellular:
		return entity.ConnectionTypeCellular
	case qimao_proto.PriceRequest_Network_ConnTypeCellular2g:
		return entity.ConnectionType2G
	case qimao_proto.PriceRequest_Network_ConnTypeCellular3g:
		return entity.ConnectionType3G
	case qimao_proto.PriceRequest_Network_ConnTypeCellular4g:
		return entity.ConnectionType4G
	case qimao_proto.PriceRequest_Network_ConnTypeCellular5g:
		return entity.ConnectionType5G
	case qimao_proto.PriceRequest_Network_ConnTypeWifi:
		return entity.ConnectionTypeWifi
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingOsType(osType qimao_proto.PriceRequest_Device_Os) entity.OsType {
	switch osType {
	case qimao_proto.PriceRequest_Device_OsAndroid:
		return entity.OsTypeAndroid
	case qimao_proto.PriceRequest_Device_OsIos:
		return entity.OsTypeIOS
	default:
		return entity.OsTypeUnknown
	}
}
