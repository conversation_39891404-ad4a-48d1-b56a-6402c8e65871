package yingshi_traffic_broker

import (
	"fmt"
	"errors"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/yingshi_traffic_broker/yingshi_traffic_entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"slices"
	"strconv"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
)

type yingshiTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        *zap.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewyingshiTrafficBroker(mediaId utils.ID) *yingshiTrafficBroker {
	return &yingshiTrafficBroker{
		log:     zap.L().With(zap.String("broker", "yingshiTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "${AUCTION_PRICE}",
			MediaClickUpXMacro:   "__UP_X__",
			MediaClickUpYMacro:   "__UP_Y__",
			MediaClickDownXMacro: "__DOWN_X__",
			MediaClickDownYMacro: "__DOWN_Y__",
		},
	}
}

func (a *yingshiTrafficBroker) GetMediaId() utils.ID {
	return a.mediaId
}

func (a *yingshiTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(a.SendResponse)
	request.Response.SetFallbackResponseBuilder(a.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = a.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = a.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}

	bidRequest := &yingshi_broker_entity.BidRequest{}
	err := sonic.Unmarshal(body, bidRequest)
	if err != nil {
		a.log.WithError(err).Error("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		a.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	request.SetMediaId(a.GetMediaId())
	request.SetRequestId(bidRequest.ReqID)
	if len(bidRequest.ReqID) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}
	a.parseDevice(bidRequest, request)
	if err = a.parseImp(bidRequest, request); err != nil {
		a.log.WithError(err).Error("BrokeRequest, parseImp failed")
		return err
	}

	a.DoTrafficSample(request, body)

	return nil
}

func (a *yingshiTrafficBroker) parseDevice(request *yingshi_broker_entity.BidRequest, adRequest *ad_service.AdRequest) {
	if request.DeviceInfo == nil {
		return
	}

	adRequest.Device = ad_service.AdRequestDevice{
		DeviceType:     mappingDeviceType(request.DeviceInfo.DeviceType),
		Idfa:           request.DeviceInfo.Idfa,
		Imei:           request.DeviceInfo.Imei,
		Oaid:           request.DeviceInfo.Oaid,
		AndroidId:      request.DeviceInfo.AndroidID,
		Mac:            request.DeviceInfo.Mac,
		Model:          request.DeviceInfo.Model,
		Brand:          request.DeviceInfo.Brand,
		ScreenWidth:    int32(request.DeviceInfo.ScreenWidth),
		ScreenHeight:   int32(request.DeviceInfo.ScreenHeight),
		ConnectionType: mappingConnectionType(request.DeviceInfo.ConnectionType),
		OperatorType:   mappingOperatorType(request.DeviceInfo.Carrier),
		Idfv:           request.DeviceInfo.Idfv,
		BootMark:       request.DeviceInfo.BootMark,
		UpdateMark:     request.DeviceInfo.UpdateMark,
		VercodeHms:     request.DeviceInfo.VerCodeOfHms,
		VercodeAg:      request.DeviceInfo.VerCodeOfAG,
		//DeviceName:          request.DeviceInfo.HwModel,
		DeviceNameMd5:       request.DeviceInfo.HwName,
		HardwareMachineCode: request.DeviceInfo.HwMachine,
		PPI:                 int32(request.DeviceInfo.Ppi),
		RequestIp:           request.DeviceInfo.IP,
		Language:            request.DeviceInfo.Language,
		OsType:              mappingOsType(request.DeviceInfo.ClientType),
		OsVersion:           request.DeviceInfo.OsVersion,
		DeviceInitTime:      request.DeviceInfo.BirthTime,
		DeviceUpgradeTime:   request.DeviceInfo.OsUpdateTime,
	}

	if request.UserInfo != nil {
		adRequest.Device.UserAgent = request.UserInfo.UserAgent
		//adRequest.App.InstalledApp = request.UserInfo.AppLists
		adRequest.App.MediaInstalledAppIds = append(adRequest.App.MediaInstalledAppIds, request.UserInfo.AppLists...)
	}
	adRequest.Device.Lat = request.DeviceInfo.Lat
	adRequest.Device.Lon = request.DeviceInfo.Lon

	if len(request.DeviceInfo.ScreenDensity) > 0 {
		f64, _ := strconv.ParseFloat(request.DeviceInfo.ScreenDensity, 32)
		adRequest.Device.ScreenDensity = float32(f64)
	}
	if len(request.DeviceInfo.SysMemory) > 0 {
		num, _ := strconv.ParseInt(request.DeviceInfo.SysMemory, 10, 64)
		adRequest.Device.SystemTotalMem = num
	}
	if len(request.DeviceInfo.SysDiskSize) > 0 {
		num, _ := strconv.ParseInt(request.DeviceInfo.SysDiskSize, 10, 64)
		adRequest.Device.SystemTotalDisk = num
	}
	if len(request.DeviceInfo.IP) < 1 && len(request.DeviceInfo.Ipv6) > 0 {
		ipv6json := &yingshi_broker_entity.Ipv6Json{}
		err := sonic.Unmarshal([]byte(request.DeviceInfo.Ipv6), ipv6json)
		if err != nil {
			a.log.WithError(err).Error("Request Ipv6 unmarshal failed")
		}
		if len(ipv6json.Wlan1) > 0 {
			adRequest.Device.RequestIp = ipv6json.Wlan1[0]
			adRequest.Device.IsIp6 = true
		} else if len(ipv6json.RmnetData1) > 0 {
			adRequest.Device.RequestIp = ipv6json.RmnetData1[0]
			adRequest.Device.IsIp6 = true
		} else if len(ipv6json.RmnetData2) > 0 {
			adRequest.Device.RequestIp = ipv6json.RmnetData2[0]
			adRequest.Device.IsIp6 = true
		}
	}

	if adRequest.Device.OsType == entity.OsTypeIOS {
		adRequest.Device.DeviceUpgradeTime = request.DeviceInfo.IosOsUpdateTime
	}
	if len(request.DeviceInfo.Caid) > 0 {
		adRequest.Device.Caid = request.DeviceInfo.CaidVersion + "_" + request.DeviceInfo.Caid
		adRequest.Device.Caids = append(adRequest.Device.Caids, adRequest.Device.Caid)
	}
	if len(request.DeviceInfo.Caids) > 0 {
		for _, caid := range request.DeviceInfo.Caids {
			Caid_ := caid.Version + "_" + caid.Caid
			adRequest.Device.Caid = Caid_
			adRequest.Device.Caids = append(adRequest.Device.Caids, Caid_)
		}

		// 取最新的CAID版本
		if len(adRequest.Device.Caids) > 1 {
			slices.Sort(adRequest.Device.Caids)
			adRequest.Device.Caid = adRequest.Device.Caids[len(adRequest.Device.Caids)-1]
		}
	}

	if request.AppInfo != nil {
		adRequest.App.AppName = request.AppInfo.AppName
		adRequest.App.AppVersion = request.AppInfo.AppVersion
		adRequest.App.AppBundle = request.AppInfo.BundleID
	}

}

func (a *yingshiTrafficBroker) parseImp(request *yingshi_broker_entity.BidRequest, adRequest *ad_service.AdRequest) error {
	if request.ImpInfo == nil {
		return errors.New("impressions empty")
	}
	width := 0
	height := 0
	if request.ImpInfo.Width > 0 && request.ImpInfo.Height > 0 {
		width = request.ImpInfo.Width
		height = request.ImpInfo.Height
		adRequest.SlotWidth = uint32(request.ImpInfo.Width)
		adRequest.SlotHeight = uint32(request.ImpInfo.Height)
		adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
			Width:  int64(request.ImpInfo.Width),
			Height: int64(request.ImpInfo.Height),
		})
	}

	adRequest.ImpressionId = request.ReqID
	adRequest.SetMediaSlotKey(request.ImpInfo.TagID)
	// format: os_tagId
	//adRequest.SetMediaSlotKeyMapping(fmt.Sprintf("%d_%s", adRequest.Device.OsType, request.ImpInfo.TagID))
	adRequest.BidFloor = uint32(request.ImpInfo.BidFloor)
	adRequest.BidType = entity.BidTypeCpm

	key := creative_entity.NewCreativeTemplateKey()
	key.Title().AddRequiredCount(1).SetOptional(true)
	key.Desc().AddRequiredCount(1).SetOptional(true)
	key.Image().AddRequiredCount(1).SetRequiredSizeTypeWithAuto(width, height)
	adRequest.AppendCreativeTemplateKey(key)

	return nil
}

func (a *yingshiTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("yingshiTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return a.SendFallbackResponse(request, writer)
	}

	bidResponse, err := a.buildResponse(request)
	if err != nil {
		a.log.WithError(err).Error("buildResponse err")
		return err
	}

	err = a.BuildHttpSonicJsonResponse(request, writer, bidResponse)
	if err != nil {
		return err
	}
	a.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		a.log.Infof("SendResponse success, response: %s", responseStr)
	}

	return nil
}

func (a *yingshiTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Infof("Build Fallback Response start. bid response: [%v]", request.Response)
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	_, _ = writer.WriteWithStatus(204, nil)
	return nil
}

func (a *yingshiTrafficBroker) buildResponse(request *ad_service.AdRequest) (*yingshi_broker_entity.BidResponse, error) {
	bidResponse := &yingshi_broker_entity.BidResponse{
		ID:   request.GetRequestId(),
		Code: 70200,
		Ads:  make([]*yingshi_broker_entity.Ads, 0),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		resBid := &yingshi_broker_entity.Ads{
			Price:      float32(candidate.GetBidPrice().Price),
			CreativeID: creative.GetCreativeKey(),
			Deeplink:   genericAd.GetDeepLinkUrl(),
			LoadingURL: genericAd.GetLandingUrl(),
			Monitor: yingshi_broker_entity.Monitor{
				ImpressUrls: candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
				ClickUrls:   candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
			},
			Img: yingshi_broker_entity.Img{},
		}

		if len(resBid.CreativeID) == 0 {
			resBid.CreativeID = "1"
		}

		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			resBid.Monitor.DpMonitorUrls = genericAd.GetDeepLinkMonitorList()
		}

		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				resBid.Img.URL = rsc.Url
				resBid.Img.Width = int(rsc.Width)
				resBid.Img.Height = int(rsc.Height)
			case entity.MaterialTypeTitle:
				resBid.Title = rsc.Data
			case entity.MaterialTypeDesc:
				resBid.Desc = rsc.Data
			}
		}

		if genericAd.GetAppInfo() != nil {
			if genericAd.GetAppInfo().WechatExt != nil {
				resBid.MpID = genericAd.GetAppInfo().WechatExt.ProgramId
				resBid.MpPath = genericAd.GetAppInfo().WechatExt.ProgramPath
			}
		}
		bidResponse.Ads = []*yingshi_broker_entity.Ads{resBid}
		break
	}

	return bidResponse, nil
}
func mappingSlotType(s entity.SlotType) int {
	switch s {
	//case entity.SlotTypeBanner:
	//	return 1
	case entity.SlotTypeFeeds:
		return 3
	case entity.SlotTypeVideoOpening:
		return 1
	case entity.SlotTypeVideo, entity.SlotTypeVideoPause:
		return 7
	case entity.SlotTypePopup:
		return 5
	default:
		return 2
	}
}
func (mb *yingshiTrafficBroker) mappingSlotType(s int32) entity.SlotType {
	switch s {
	case 9:
		return entity.SlotTypeVideoPause
	case 2:
		return entity.SlotTypeBanner
	case 1:
		return entity.SlotTypeOpening
	case 8:
		return entity.SlotTypeRewardVideo
	case 5:
		return entity.SlotTypeFeeds
	case 3:
		return entity.SlotTypePopup

	default:
		return entity.SlotTypeUnknown
	}
}

func mappingOsType(os string) entity.OsType {
	switch os {
	case "1":
		return entity.OsTypeIOS
	case "3":
		return entity.OsTypeAndroid
	default:
		return entity.OsTypeAndroid
	}
}

func mappingScreenOrientation(orientation int) entity.ScreenOrientationType {
	switch orientation {
	case 1:
		return entity.ScreenOrientationTypePortrait
	case 2:
		return entity.ScreenOrientationTypeLandscape
	default:
		return entity.ScreenOrientationTypePortrait
	}
}

func mappingOperatorType(carrier int) entity.OperatorType {
	switch carrier {
	case 2:
		return entity.OperatorTypeChinaMobile
	case 1:
		return entity.OperatorTypeChinaUnicom
	case 0:
		return entity.OperatorTypeChinaTelecom
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType int) entity.ConnectionType {
	switch connectionType {
	case 0:
		return entity.ConnectionTypeWifi
	case 1:
		return entity.ConnectionType2G
	case 2:
		return entity.ConnectionType3G
	case 3:
		return entity.ConnectionType4G
	case 6:
		return entity.ConnectionType5G
	case 4:
		return entity.ConnectionTypeNetEthernet
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingDeviceType(deviceType int) entity.DeviceType {
	switch deviceType {
	case 1:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePad
	case 3:
		return entity.DeviceTypePc
	default:
		return entity.DeviceTypeMobile
	}
}
