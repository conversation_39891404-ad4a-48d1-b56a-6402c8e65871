package meitu_traffic_broker

import (
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/meitu_traffic_broker/meitu_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"strings"
)

var meituAssets = "reqAssets"

type (
	MeiTuBroker struct {
		traffic_broker.TrafficBrokerBase

		mediaId       utils.ID
		WinPriceMacro string
	}
)

func NewMeiTuBroker(mediaId utils.ID) *MeiTuBroker {
	return &MeiTuBroker{
		mediaId:       mediaId,
		WinPriceMacro: "%%WINNING_PRICE%%",
	}
}

func (mb *MeiTuBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *MeiTuBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(mb.SendResponse)
	request.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro
	return mb.ParseAdRequest(request)
}

func (mb *MeiTuBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[MeiTuBroker]request body empty")
	}

	bidRequest := &meitu_proto.BidRequest{}
	err := proto.Unmarshal(body, bidRequest)
	if err != nil {
		zap.L().Error("[MeiTuBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if err = mb.buildRequest(request, bidRequest); err != nil {
		zap.L().Debug("[MeiTuBroker]BrokeRequest, parseUser failed")
		return err
	}
	mb.DoTrafficSamplePb(request, bidRequest)
	return nil
}
func (mb *MeiTuBroker) buildRequest(request *ad_service.AdRequest, bidRequest *meitu_proto.BidRequest) error {
	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[MeiTuBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	request.SetRequestId(bidRequest.GetId())
	if len(bidRequest.GetId()) < 1 {
		request.SetRequestId(utils.NewUUID())
	}
	request.SetMediaId(mb.mediaId)

	if err := mb.parseImp(bidRequest, request); err != nil {
		zap.L().Debug("[MeiTuBroker] BrokeRequest, parseImp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, request); err != nil {
		zap.L().Debug("[MeiTuBroker]BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseUser(bidRequest, request); err != nil {
		zap.L().Debug("[MeiTuBroker]BrokeRequest, parseUser failed")
		return err
	}

	if err := mb.parseApp(bidRequest, request); err != nil {
		zap.L().Debug("[MeiTuBroker]BrokeRequest, parseApp failed")
		return err
	}

	request.TMax = int(bidRequest.GetTmax())
	request.IsTest = bidRequest.GetTest()
	return nil
}

func (mb *MeiTuBroker) parseImp(mediaBidRequest *meitu_proto.BidRequest, bidReq *ad_service.AdRequest) error {

	if len(mediaBidRequest.Imp) == 0 {
		zap.L().Debug("[MeiTuBroker]parseImp, vendor: , imp nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return fmt.Errorf("parseImp, imp nil")
	}
	item := mediaBidRequest.Imp[0]

	bidReq.SetMediaSlotKey(item.GetTagid())
	bidReq.ImpressionId = item.GetId()
	bidReq.BidFloor = uint32(item.GetBidfloor())

	if item.GetPmp() != nil {
		for _, d := range item.GetPmp().Deals {
			sd := ad_service.SourceDeal{
				DealId:   d.GetId(),
				BidFloor: int64(d.GetBidfloor()),
			}

			bidReq.SourceDeal = append(bidReq.SourceDeal, sd)
		}
	}
	if item.GetBanner() != nil {
		banner := item.GetBanner()
		bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
			Width:  int64(banner.GetW()),
			Height: int64(banner.GetH()),
		})
		for _, format := range banner.GetFormat() {
			if format != nil {
				bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
					Width:  int64(format.GetW()),
					Height: int64(format.GetH()),
				})
			}
		}
	}
	if item.GetNative() != nil {
		native := item.GetNative()
		if native.GetRequestNative() != nil {
			requestNative := native.GetRequestNative()
			bidReq.AddMediaExtraData(meituAssets, requestNative.Assets)

			key := creative_entity.NewCreativeTemplateKey()
			for _, asset := range requestNative.Assets {
				if asset != nil {
					if asset.GetImg() != nil {
						img := asset.GetImg()
						switch img.GetType() {
						case meitu_proto.ImageAssetType_ICON:
							key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
						case meitu_proto.ImageAssetType_LOGO:
							key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
						case meitu_proto.ImageAssetType_MAIN, meitu_proto.ImageAssetType_COVER:
							key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
						}
						bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
							Width:  int64(img.GetW()),
							Height: int64(img.GetH()),
						})
					} else if asset.GetTitle() != nil {
						key.Title().SetOptional(true)
					} else if asset.GetVideo() != nil {
						video := asset.GetVideo()
						key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
						bidReq.SlotSize = append(bidReq.SlotSize, ad_service.Size{
							Width:  int64(video.GetW()),
							Height: int64(video.GetH()),
						})
					} else if asset.GetData() != nil {
						if asset.GetData().GetType() == meitu_proto.DataAssetType_DESC {
							key.Desc().AddRequiredCount(1).SetOptional(true)
						}
					}
				}
			}
			bidReq.AppendCreativeTemplateKey(key)
		}
	}
	for _, id := range item.GetStyleIds() {
		bidReq.AdxTemplateId = append(bidReq.AdxTemplateId, id)
		switch id {
		case "23", "28":
			bidReq.SlotType = entity.SlotTypeFeeds
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraString(key.String()+"_"+bidReq.SlotType.String(), "28")
		case "31":
			bidReq.SlotType = entity.SlotTypeFeeds
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraString(key.String()+"_"+bidReq.SlotType.String(), id)
		case "36":
			bidReq.SlotType = entity.SlotTypeFeeds
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraString(key.String()+"_"+bidReq.SlotType.String(), id)
		case "2":
			bidReq.SlotType = entity.SlotTypeFeeds
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Icon().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_1X1)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraString(key.String()+"_"+bidReq.SlotType.String(), id)
		case "37":
			bidReq.SlotType = entity.SlotTypeFeeds
			key := creative_entity.NewCreativeTemplateKey()
			key.Title().AddRequiredCount(1).SetOptional(true)
			key.Desc().AddRequiredCount(1).SetOptional(true)
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
			bidReq.AddMediaExtraString(key.String()+"_"+bidReq.SlotType.String(), id)
		case "6":
			bidReq.SlotType = entity.SlotTypeOpening
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			bidReq.AddMediaExtraString(key.String()+"_"+bidReq.SlotType.String(), id)
		case "32":
			bidReq.SlotType = entity.SlotTypePopup
			key := creative_entity.NewCreativeTemplateKey()
			key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
			bidReq.AppendCreativeTemplateKey(key)
			bidReq.AddMediaExtraString(key.String()+"_"+bidReq.SlotType.String(), id)
		}
	}

	return nil
}

func (mb *MeiTuBroker) parseDevice(mediaBidRequest *meitu_proto.BidRequest, bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.Device == nil {
		zap.L().Error("parseDevice, device nil", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", mediaBidRequest)))))
		return fmt.Errorf("parseDevice failed")
	}

	device := mediaBidRequest.GetDevice()
	bidReq.Device.UserAgent = device.GetUa()
	bidReq.Device.RequestIp = device.GetIp()
	if strings.Contains(bidReq.Device.RequestIp, ":") {
		bidReq.Device.IsIp6 = true
	}
	if len(device.GetIpv6()) > 0 {
		bidReq.Device.RequestIp = device.GetIpv6()
		bidReq.Device.IsIp6 = true
	}
	if device.GetGeo() != nil {
		bidReq.Device.Lat = device.GetGeo().GetLat()
		bidReq.Device.Lon = device.GetGeo().GetLon()
	}
	bidReq.Device.ImeiMd5 = device.GetDidmd5()
	bidReq.Device.Imei = device.GetDid()
	bidReq.Device.AndroidIdMd5 = device.GetDpidmd5()
	bidReq.Device.OperatorType = func() entity.OperatorType {
		switch device.GetCarrier() {
		case "46000":
			return entity.OperatorTypeChinaMobile
		case "46001":
			return entity.OperatorTypeChinaUnicom
		case "46002":
			return entity.OperatorTypeChinaTelecom
		default:
			return entity.OperatorTypeUnknown
		}
	}()
	bidReq.Device.Language = device.GetLanguage()
	bidReq.Device.Brand = device.GetMake()
	bidReq.Device.Model = device.GetModel()
	bidReq.Device.OsType = func() entity.OsType {
		if device.GetOs() == "android" {
			return entity.OsTypeAndroid
		} else if device.GetOs() == "iOS" {
			return entity.OsTypeIOS
		} else {
			return entity.OsTypeUnknown
		}
	}()
	bidReq.Device.OsVersion = device.GetOsv()
	bidReq.Device.ScreenWidth = device.GetW()
	bidReq.Device.ScreenHeight = device.GetH()
	bidReq.Device.ConnectionType = func() entity.ConnectionType {
		switch device.GetConnectiontype() {
		case meitu_proto.ConnectionType_WIFI:
			return entity.ConnectionTypeWifi
		case meitu_proto.ConnectionType_CELL_2G:
			return entity.ConnectionType2G
		case meitu_proto.ConnectionType_CELL_3G:
			return entity.ConnectionType3G
		case meitu_proto.ConnectionType_CELL_4G:
			return entity.ConnectionType4G
		default:
			return entity.ConnectionTypeUnknown
		}
	}()
	if bidReq.Device.OsType == entity.OsTypeAndroid {
		bidReq.Device.AndroidId = device.GetIfa()
	} else if bidReq.Device.OsType == entity.OsTypeIOS {
		bidReq.Device.Idfa = device.GetIfa()
	}
	bidReq.Device.Mac = device.GetMac()
	bidReq.Device.MacMd5 = device.GetMacmd5()
	bidReq.Device.Oaid = device.GetOaid()
	bidReq.Device.Aaid = device.GetAaid()
	caids := strings.Split(device.GetCaid(), ",")
	if len(caids) > 0 {
		bidReq.Device.Caid = caids[0]
		bidReq.Device.Caids = append(bidReq.Device.Caids, bidReq.Device.Caid)
	}
	bidReq.Device.VercodeHms = device.GetHmsVersion()
	bidReq.Device.VercodeAg = device.GetHwAgVersion()
	bidReq.Device.DeviceInitTime = device.GetBirthTime()
	bidReq.Device.DeviceUpgradeTime = device.GetUpdateTime()
	bidReq.Device.DeviceStartupTime = device.GetBootTime()
	bidReq.Device.BootMark = device.GetBootMark()
	bidReq.Device.UpdateMark = device.GetUpdateMark()

	return nil

}

func (mb *MeiTuBroker) parseApp(mediaBidRequest *meitu_proto.BidRequest, bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.GetApp() == nil {
		zap.L().Debug("parseApp, vendor: , app nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	app := mediaBidRequest.GetApp()
	bidReq.App.AppName = app.GetName()
	bidReq.App.AppBundle = app.GetBundle()
	bidReq.App.AppVersion = app.GetVer()

	return nil
}

func (mb *MeiTuBroker) parseUser(mediaBidRequest *meitu_proto.BidRequest, bidReq *ad_service.AdRequest) error {

	if mediaBidRequest.User == nil {
		zap.L().Debug("parseUser, vendor: , user nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(bidReq.GetMediaId())))))
		return nil
	}

	user := mediaBidRequest.User

	if user.GetGender() == "M" {
		bidReq.UserGender = entity.UserGenderMan
	} else if user.GetGender() == "F" {
		bidReq.UserGender = entity.UserGenderWoman
	}

	return nil
}

func (mb *MeiTuBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("MeiTuBroker Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
		request.Response.Dump("MeiTuBroker")
	}

	if request.Response.NoCandidate() {
		return mb.SendFallbackResponse(request, writer)
	}
	bidResponse, err := mb.buildResponse(request)
	if err != nil {
		zap.L().Error("MeiTuBroker Error in buildResponse", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}
	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()

	buffer.EnsureSize(bidResponse.Size())
	_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		zap.L().Error("MeiTuBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/octet-stream")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	mb.DoTrafficResponseSamplePb(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		zap.L().Info("[MeiTuBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", responseStr)))))
	}

	return nil
}

func (mb *MeiTuBroker) buildResponse(request *ad_service.AdRequest) (*meitu_proto.BidResponse, error) {
	bidResponse := &meitu_proto.BidResponse{
		Id:      proto.String(request.GetRequestId()),
		Bidid:   proto.String(request.GetRequestId()),
		Seatbid: []*meitu_proto.BidResponse_SeatBid{},
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		creative := candidate.GetCreative()
		if creative == nil {
			continue
		}
		bid := &meitu_proto.BidResponse_SeatBid_Bid{
			Id:      proto.String(request.GetRequestId()),
			Impid:   proto.String(request.GetRequestId()),
			Price:   proto.Float64(float64(candidate.GetBidPrice().Price)),
			Adid:    proto.String(candidate.GetDspAdId()),
			Crid:    proto.String(candidate.GetDspAdId()),
			StyleId: proto.String(request.GetMediaExtraString(candidate.GetActiveCreativeTemplateKey().String()+"_"+request.SlotType.String(), "28")),
		}

		native := &meitu_proto.NativeResponse{
			Ver:         proto.String("2.4"),
			Imptrackers: candidate.GetMacroReplaceImpressionMonitorList(),
			Link: &meitu_proto.NativeResponse_Link{
				Url:            proto.String(candidate.GetDeepLinkUrl()),
				Clicktrackers:  candidate.GetMacroReplaceClickMonitorList(),
				Download:       &meitu_proto.NativeResponse_Link_Download{},
				Dplinktrackers: candidate.GetMacroReplaceDeepLinkMonitorList(),
			},
		}
		if len(candidate.GetDeepLinkUrl()) == 0 {
			native.Link.Url = proto.String(candidate.GetLandingUrl())
		}

		if candidate.GetIndexDeal() != nil {
			bid.Dealid = proto.String(candidate.GetIndexDeal().DealId)
		}

		title := ""
		desc := ""
		video := &entity.Material{}
		imgs := []*entity.Material{}
		logo := &entity.Material{}
		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				imgs = append(imgs, rsc)
			case entity.MaterialTypeIcon, entity.MaterialTypeLogo:
				logo = rsc
			case entity.MaterialTypeTitle:
				title = rsc.Data
			case entity.MaterialTypeDesc:
				desc = rsc.Data
			case entity.MaterialTypeVideo:
				video = rsc
			default:
				continue
			}
		}

		if candidate.GetLandingAction() == entity.LandingTypeDownload {
			native.Link.Download.DetailUrl = proto.String(candidate.GetLandingUrl())
			native.Link.Download.DownloadBeginUrl = candidate.GetMacroReplaceAppDownloadStartedMonitorList()
			native.Link.Download.DownloadSuccUrl = candidate.GetMacroReplaceAppDownloadFinishedMonitorList()
			native.Link.Download.InstallBeginUrl = candidate.GetMacroReplaceAppInstallStartMonitorList()
			native.Link.Download.InstallSuccUrl = candidate.GetMacroReplaceAppInstalledMonitorList()
		}

		if candidate.GetAppInfo() != nil {
			if len(logo.Url) == 0 && len(candidate.GetAppInfo().Icon) > 0 {
				logo.Url = candidate.GetAppInfo().Icon
			}
			native.Link.Download.AppName = proto.String(candidate.GetAppInfo().AppName)
			native.Link.Download.VersionCode = proto.String(candidate.GetAppInfo().AppVersion)
			bid.Bundle = proto.String(candidate.GetAppInfo().PackageName)
			native.Link.Download.DetailUrl = proto.String(candidate.GetAppInfo().AppDescURL)
			native.Link.Download.AppPrivacyPolicyUrl = proto.String(candidate.GetAppInfo().Privacy)
			native.Link.Download.AppDeveloper = proto.String(candidate.GetAppInfo().Develop)
			native.Link.Download.AppIconUrl = proto.String(candidate.GetAppInfo().Icon)
			native.Link.Download.AppSize = proto.Int32(int32(candidate.GetAppInfo().PackageSize))
			native.Link.Download.AppPermission = []*meitu_proto.NativeResponse_Link_AppPermission{}
			for _, permissionDesc := range candidate.GetAppInfo().PermissionDesc {
				native.Link.Download.AppPermission = append(native.Link.Download.AppPermission, &meitu_proto.NativeResponse_Link_AppPermission{
					Title:    proto.String(permissionDesc.PermissionLab),
					Describe: proto.String(permissionDesc.PermissionDesc),
				})
			}
			if candidate.GetAppInfo().WechatExt != nil {
				native.Link.Applet = &meitu_proto.NativeResponse_Link_Applet{
					Username: proto.String(candidate.GetAppInfo().WechatExt.ProgramId),
					Type:     proto.String("0"),
					Path:     proto.String(candidate.GetAppInfo().WechatExt.ProgramPath),
				}
			}
		}

		assets := []*meitu_proto.NativeResponse_Asset{}
		reqAsset, ok := request.GetMediaExtraData(meituAssets)
		videoDuration := 0
		if ok {
			for _, asset := range reqAsset.([]*meitu_proto.NativeRequest_Asset) {
				// 1:小图 2:大图 3:标题 4:描述 5:点击按钮文字 8:视频 9:封面图 10:组图
				switch asset.GetId() {
				case 1:
					if len(logo.Url) > 0 {
						imgAsset := &meitu_proto.NativeResponse_Asset{
							Id: asset.Id,
							AssetOneof: &meitu_proto.NativeResponse_Asset_Img{
								Img: &meitu_proto.NativeResponse_Asset_Image{
									Url: &logo.Url,
									W:   &logo.Width,
									H:   &logo.Height,
								},
							},
						}
						assets = append(assets, imgAsset)
					}

				case 2, 9:
					if len(imgs) > 0 {
						imgAsset := &meitu_proto.NativeResponse_Asset{
							Id: asset.Id,
							AssetOneof: &meitu_proto.NativeResponse_Asset_Img{
								Img: &meitu_proto.NativeResponse_Asset_Image{
									Url: &imgs[0].Url,
									W:   &imgs[0].Width,
									H:   &imgs[0].Height,
								},
							},
						}
						bid.W = &imgs[0].Width
						bid.H = &imgs[0].Height
						assets = append(assets, imgAsset)
					}
				case 3:
					if len(title) > 0 {
						assetValue := &meitu_proto.NativeResponse_Asset{
							Id: asset.Id,
							AssetOneof: &meitu_proto.NativeResponse_Asset_Title_{
								Title: &meitu_proto.NativeResponse_Asset_Title{
									Text: &title,
								}}}
						assets = append(assets, assetValue)
					}
				case 4:
					if len(desc) > 0 {
						assetValue := &meitu_proto.NativeResponse_Asset{
							Id: asset.Id,
							AssetOneof: &meitu_proto.NativeResponse_Asset_Data_{
								Data: &meitu_proto.NativeResponse_Asset_Data{
									Value: &desc,
								}}}
						assets = append(assets, assetValue)
					}
				case 5:
					assetValue := &meitu_proto.NativeResponse_Asset{
						Id: asset.Id,
						AssetOneof: &meitu_proto.NativeResponse_Asset_Data_{
							Data: &meitu_proto.NativeResponse_Asset_Data{
								Value: proto.String("点击打开"),
							}}}
					assets = append(assets, assetValue)
				case 8:
					if len(video.Url) > 0 {
						videoAsset := &meitu_proto.NativeResponse_Asset{
							Id: asset.Id,
							AssetOneof: &meitu_proto.NativeResponse_Asset_Video_{
								Video: &meitu_proto.NativeResponse_Asset_Video{
									Url:      &video.Url,
									Duration: proto.Int32(int32(video.Duration)),
									W:        &video.Width,
									H:        &video.Height,
								}}}

						bid.W = &video.Width
						bid.H = &video.Height
						videoDuration = int(video.Duration)
						assets = append(assets, videoAsset)
					}
				case 10:
					if len(imgs) > 0 {
						// 组图
						groupImgAsset := &meitu_proto.NativeResponse_Asset{}
						groupImgAsset.Id = asset.Id
						for _, nativeImage := range imgs {
							img := meitu_proto.NativeResponse_Asset_Image{
								Url: &nativeImage.Url,
								W:   &nativeImage.Width,
								H:   &nativeImage.Height,
							}
							groupImgAsset.GetGroupimg().Img = append(groupImgAsset.GetGroupimg().Img, &img)
						}
						assets = append(assets, groupImgAsset)
					}
				}
			}
		} else if len(imgs) > 0 {
			imgAsset := &meitu_proto.NativeResponse_Asset{}
			openAssetTypeId := int32(2)
			imgAsset.Id = &openAssetTypeId
			imgAsset.AssetOneof = &meitu_proto.NativeResponse_Asset_Img{
				Img: &meitu_proto.NativeResponse_Asset_Image{
					Url: &imgs[0].Url,
					W:   &imgs[0].Width,
					H:   &imgs[0].Height,
				},
			}
			bid.W = &imgs[0].Width
			bid.H = &imgs[0].Height
			assets = append(assets, imgAsset)
		}
		native.Assets = assets
		bid.CreativeType = proto.Int32(1)
		if videoDuration > 0 {
			bid.CreativeType = proto.Int32(2)
		}

		bid.AdmOneof = &meitu_proto.BidResponse_SeatBid_Bid_AdmNative{
			AdmNative: native,
		}
	}
	return bidResponse, nil
}

func (mb *MeiTuBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[MeiTuBroker] Build Response start. bid response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.Response)))))
	}

	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Content-Type", "application/octet-stream")
	writer.WriteWithStatus(204, nil)
	return nil
}
