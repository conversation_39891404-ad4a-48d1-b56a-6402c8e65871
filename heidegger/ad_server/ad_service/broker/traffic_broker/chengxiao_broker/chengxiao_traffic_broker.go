package chengxiao_traffic_broker

import (
	"errors"
	"strconv"

	"github.com/bytedance/sonic"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/chengxiao_dsp_broker/chengxiao_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"fmt"
)

type ChengXiaoTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	log        *zap.Logger
	mediaId    utils.ID
	mediaMacro *macro_builder.MediaMacro
}

func NewChengXiaoTrafficBroker(mediaId utils.ID) *ChengXiaoTrafficBroker {
	return &ChengXiaoTrafficBroker{
		log:     zap.L().With(zap.String("broker", "ChengXiaoTrafficBroker")),
		mediaId: mediaId,
		mediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "__win_price__",
			MediaClickDownXMacro: "__clk_x__",
			MediaClickDownYMacro: "__clk_y__",
			MediaClickUpXMacro:   "__clk_up_x__",
			MediaClickUpYMacro:   "__clk_up_y__",
		},
	}
}

func (a *ChengXiaoTrafficBroker) GetMediaId() utils.ID {
	return a.mediaId
}

func (a *ChengXiaoTrafficBroker) Do(request *ad_service.AdRequest) error {
	request.Response.SetResponseBuilder(a.SendResponse)
	request.Response.SetFallbackResponseBuilder(a.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = a.mediaMacro.MediaPriceMacro
	request.AdRequestMedia.MediaMacro = a.mediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return errors.New("request body empty")
	}
	var err error
	bidRequest := &chengxiao_proto.BidRequest3{}
	err = proto.Unmarshal(body, bidRequest)
	if err != nil {
		a.log.WithError(err).Error("Request body unmarshal failed")
		return errors.New("request body invalid")
	}

	if request.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		a.log.Infof("Parse Request start. broker request: [%s]", reqBody)
	}

	request.SetMediaId(a.GetMediaId())
	request.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		request.SetRequestId(request.GenerateRequestId())
	}

	a.parseDevice(bidRequest, request)
	if err = a.parseImp(bidRequest, request); err != nil {
		a.log.WithError(err).Error("BrokeRequest, parseImp failed")
		return err
	}

	a.DoTrafficSamplePb(request, bidRequest)

	return nil
}

func (a *ChengXiaoTrafficBroker) parseDevice(request *chengxiao_proto.BidRequest3, adRequest *ad_service.AdRequest) {
	app := request.App
	device := request.Device

	if device == nil {
		return
	}

	if request.Site != nil {
		adRequest.Url = request.Site.Content
	}

	if request.User != nil {
		switch request.User.Gender {
		case 2:
			adRequest.UserGender = entity.UserGenderWoman
		case 1:
			adRequest.UserGender = entity.UserGenderMan
		}
		adRequest.UserAge = request.User.Age
	}

	if app != nil {
		adRequest.App.AppName = app.Name
		adRequest.App.AppBundle = app.Bundle
		adRequest.App.AppVersion = app.Version
		adRequest.App.InstalledApp = device.AppList
	}

	adRequest.Device = ad_service.AdRequestDevice{
		UserAgent:           device.Ua,
		RequestIp:           device.Ip,
		DeviceType:          mappingDeviceType(device.DeviceType),
		Model:               device.Model,
		Brand:               device.Brand,
		Vendor:              device.Make,
		OsType:              mappingOsType(device.Os),
		OsVersion:           device.Osv,
		ConnectionType:      mappingConnectionType(device.ConnectionType),
		OperatorType:        mappingOperatorType(device.Carrier),
		Idfa:                device.Idfa,
		IdfaMd5:             device.IdfaMd5,
		Idfv:                device.Idfv,
		IdfvMd5:             device.IdfvMd5,
		OpenUdid:            device.OpenUdId,
		Imei:                device.Imei,
		ImeiMd5:             device.ImeiMd5,
		Oaid:                device.Oaid,
		OaidMd5:             device.OaidMd5,
		AndroidId:           device.AndroidId,
		AndroidIdMd5:        device.AndroidIdMd5,
		Mac:                 device.Mac,
		MacMd5:              device.MacMd5,
		ScreenWidth:         device.W,
		ScreenHeight:        device.H,
		PPI:                 device.Ppi,
		BootMark:            device.BootMark,
		UpdateMark:          device.UpdateMark,
		AppStoreVersion:     device.AppStoreVer,
		VercodeAg:           device.HwagV,
		RomVersion:          device.RomV,
		DeviceStartupTime:   device.SystemStartSec,
		DeviceUpgradeTime:   device.SystemUpdateSec,
		DeviceInitTime:      device.SystemInitSec,
		DeviceName:          device.DeviceName,
		ScreenOrientation:   mappingScreenOrientation(device.St),
		Paid:                device.Paid,
		Aaid:                device.AliAdId,
		CountryCode:         device.Con,
		Language:            device.Lan,
		HardwareMachineCode: device.HardwareMachine,
	}
	if adRequest.Device.DeviceType == entity.DeviceTypeMobile || adRequest.Device.DeviceType == entity.DeviceTypePad {
		adRequest.Device.IsMobile = true
	}
	if adRequest.Device.OsType == entity.OsTypeIOS {
		adRequest.Device.Model = device.HardwareModel
	}

	if request.Geo != nil {
		adRequest.Device.Lat = float64(request.Geo.Lat)
		adRequest.Device.Lon = float64(request.Geo.Lon)
	}

	if len(device.MemorySize) > 0 {
		adRequest.Device.SystemTotalMem, _ = strconv.ParseInt(device.MemorySize, 10, 64)
	}
	if len(device.DiskSize) > 0 {
		adRequest.Device.SystemTotalDisk, _ = strconv.ParseInt(device.DiskSize, 10, 64)
	}
	if len(device.TimeZone) > 0 {
		tz, _ := strconv.ParseInt(device.TimeZone, 10, 32)
		adRequest.Device.TimeZone = int32(tz)
	}

	if len(device.Ipv6) > 0 {
		adRequest.Device.IsIp6 = true
		adRequest.Device.RequestIp = device.Ipv6
	}

	if len(device.Caid) > 0 {
		adRequest.Device.Caid = string_utils.ConcatString(device.CaidVer, "_", device.Caid)
		adRequest.Device.Caids = append(adRequest.Device.Caids, device.Caid)
		adRequest.Device.CaidRaw = device.Caid
		adRequest.Device.CaidVersion = device.CaidVer
	}

}

func (a *ChengXiaoTrafficBroker) parseImp(request *chengxiao_proto.BidRequest3, adRequest *ad_service.AdRequest) error {
	if request.Imp == nil {
		return errors.New("impressions empty")
	}
	imp := request.Imp

	adRequest.ImpressionId = imp.Id
	adRequest.SetMediaSlotKey(imp.SlotId)
	adRequest.BidFloor = uint32(imp.Bidfloor)
	adRequest.UseHttps = imp.IsSupportHttps == 1
	adRequest.SupportDeeplink = imp.IsDeeplink == 0
	adRequest.BidType = entity.BidTypeCpm
	//adRequest.SlotType = mappingSlotType(imp.SlotType)

	if imp.Image != nil {
		if imp.Image.W > 0 && imp.Image.H > 0 {
			adRequest.SlotWidth = uint32(imp.Image.W)
			adRequest.SlotHeight = uint32(imp.Image.H)
			adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
				Width:  int64(imp.Image.W),
				Height: int64(imp.Image.H),
			})
		}
	}
	if imp.Video != nil {
		if imp.Video.W > 0 && imp.Video.H > 0 {
			adRequest.SlotWidth = uint32(imp.Video.W)
			adRequest.SlotHeight = uint32(imp.Video.H)
			adRequest.SlotSize = append(adRequest.SlotSize, ad_service.Size{
				Width:  int64(imp.Video.W),
				Height: int64(imp.Video.H),
			})
		}
		adRequest.VideoMaxDuration = imp.Video.MaxDuration
		adRequest.VideoMinDuration = imp.Video.MinDuration

	}

	switch imp.SlotType {
	case 1:
		adRequest.SlotType = entity.SlotTypeBanner
		key := creative_entity.NewCreativeTemplateKey()
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		adRequest.AppendCreativeTemplateKey(key)
	case 2:
		adRequest.SlotType = entity.SlotTypeFeeds
		key := creative_entity.NewCreativeTemplateKey()
		key.Title().AddRequiredCount(1).SetOptional(true)
		key.Desc().AddRequiredCount(1).SetOptional(true)
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		adRequest.AppendCreativeTemplateKey(key)
	case 3:
		adRequest.SlotType = entity.SlotTypeOpening
		key := creative_entity.NewCreativeTemplateKey()
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
		key.Video().SetRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_VERTICAL).SetOptional(true)
		adRequest.AppendCreativeTemplateKey(key)
	case 5:
		adRequest.SlotType = entity.SlotTypePopup
		key := creative_entity.NewCreativeTemplateKey()
		key.Image().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		adRequest.AppendCreativeTemplateKey(key)
	case 4:
		adRequest.SlotType = entity.SlotTypeVideo
		key := creative_entity.NewCreativeTemplateKey()
		key.Video().AddRequiredCount(1).SetRequiredSizeType(creative_entity.RT_SIZE_NULL)
		adRequest.AppendCreativeTemplateKey(key)
	}

	return nil
}

func (a *ChengXiaoTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Infof("Build Response start. bid response: [%v]", request.Response)
		request.Response.Dump("ChengXiaoTrafficBroker")
	}

	if request.Response.NoCandidate() {
		return a.SendFallbackResponse(request, writer)
	}

	bidResponse, err := a.buildResponse(request)
	if err != nil {
		a.log.WithError(err).Error("buildResponse err")
		return err
	}

	buffer := buffer_pool.NewBufferWriter()
	defer buffer.Release()

	buffer.EnsureSize(bidResponse.Size())

	_, err = bidResponse.MarshalToSizedBuffer(buffer.Get())
	if err != nil {
		zap.L().Error("FGTrafficBroker Error in JSON marshalling", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return err
	}

	data := buffer.Get()
	writer.SetHeader("Content-Type", "application/x-protobuf")
	if _, err := writer.WriteWithStatus(200, data); err != nil {
		return err
	}

	a.DoTrafficResponseSamplePb(request, bidResponse)

	if request.IsDebug {
		responseStr, _ := sonic.Marshal(bidResponse)
		a.log.Infof("SendResponse success, response: %s", responseStr)
	}

	return nil
}

func (a *ChengXiaoTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		a.log.Infof("Build Fallback Response start. bid response: [%v]", request.Response)
	}
	writer.SetHeader("Content-Length", "0")
	writer.SetHeader("Version", "2.2")
	writer.SetHeader("Content-Type", "application/x-protobuf")
	writer.WriteWithStatus(204, nil)
	return nil
}

func (a *ChengXiaoTrafficBroker) buildResponse(request *ad_service.AdRequest) (*chengxiao_proto.BidResponse3, error) {
	bidResponse := &chengxiao_proto.BidResponse3{
		Id:   request.GetRequestId(),
		Code: 0,
		SeatBid: &chengxiao_proto.BidResponseSeatBid{
			BidId: request.GetRequestId(),
			Bid:   make([]*chengxiao_proto.BidResponseBid, 0),
		},
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		if genericAd == nil || creative == nil {
			continue
		}

		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		resBid := &chengxiao_proto.BidResponseBid{
			ImpId:    request.ImpressionId,
			Cid:      creative.GetCreativeKey(),
			Price:    candidate.GetBidPrice().Price,
			SlotType: mappingSlotType(request.SlotType),
			Imps:     candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen),
			Clk:      candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
			Lp:       candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
			DeepLink: genericAd.GetDeepLinkUrl(),
		}

		if len(genericAd.GetDeepLinkFailedMonitorList()) > 0 {
			resBid.Df = genericAd.GetDeepLinkFailedMonitorList()
		}
		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			resBid.Dt = genericAd.GetDeepLinkMonitorList()
		}
		if len(genericAd.GetDeepLinkUrl()) > 0 {
			resBid.Dt = append(resBid.Dt, genericAd.GetDpSuccess())
		}

		if len(genericAd.GetVideoStartUrlList()) > 0 {
			resBid.Vs = genericAd.GetVideoStartUrlList()
		}
		if len(genericAd.GetVideoCloseUrlList()) > 0 {
			resBid.Ve = genericAd.GetVideoCloseUrlList()
		}

		if candidate.GetLandingAction() == entity.LandingTypeDownload {
			resBid.Action = 1
		}

		for _, rsc := range candidate.GetSelectedMaterialList() {
			switch rsc.MaterialType {
			case entity.MaterialTypeImage:
				resBid.ImageUrl = append(resBid.ImageUrl, candidate.ReplaceUrlMacro(rsc.Url, traffic, trackingGen))
				resBid.W = rsc.Width
				resBid.H = rsc.Height
			case entity.MaterialTypeTitle:
				resBid.Title = rsc.Data
			case entity.MaterialTypeDesc:
				resBid.Desc = rsc.Data
			case entity.MaterialTypeVideo:
				resBid.VideoUrl = candidate.ReplaceUrlMacro(rsc.Url, traffic, trackingGen)
				resBid.VideoDuration = int32(rsc.Duration)
				resBid.VideoTitle = resBid.Title
				resBid.VideoDesc = resBid.Desc

				video25 := int(rsc.Duration / 4)
				video50 := int(rsc.Duration / 2)
				video75 := int(rsc.Duration * 75 / 100)
				if len(genericAd.GetDelayMonitorUrlList()) > 0 {
					for _, delay := range genericAd.GetDelayMonitorUrlList() {
						switch delay.Delay {
						case video25:
							resBid.Vf = append(resBid.Vf, delay.Url)
						case video50:
							resBid.Vm = append(resBid.Vm, delay.Url)
						case video75:
							resBid.Vt = append(resBid.Vt, delay.Url)
						}
					}
				}
			}
		}

		if genericAd.GetAppInfo() != nil {
			resBid.App = &chengxiao_proto.BidResponseBidApp{
				Bundle:          genericAd.GetAppInfo().PackageName,
				Name:            genericAd.GetAppInfo().AppName,
				Version:         genericAd.GetAppInfo().AppVersion,
				Icon:            genericAd.GetAppInfo().Icon,
				Size_:           int32(genericAd.GetAppInfo().PackageSize),
				PermissionsLink: genericAd.GetAppInfo().Permission,
				PrivatePolicy:   genericAd.GetAppInfo().Privacy,
				Developer:       genericAd.GetAppInfo().Develop,
				AppDesc:         genericAd.GetAppInfo().AppDesc,
			}
			//事件
			if len(genericAd.GetAppDownloadStartedMonitorList()) > 0 {
				resBid.App.Ds = genericAd.GetAppDownloadStartedMonitorList()
			}
			if len(genericAd.GetAppDownloadFinishedMonitorList()) > 0 {
				resBid.App.Df = genericAd.GetAppDownloadFinishedMonitorList()
			}

			if len(genericAd.GetAppInstallStartMonitorList()) > 0 {
				resBid.App.Is = genericAd.GetAppInstallStartMonitorList()
			}
			if len(genericAd.GetAppInstalledMonitorList()) > 0 {
				resBid.App.If = genericAd.GetAppInstalledMonitorList()
			}
			if len(genericAd.GetActionCallbackUrl()) > 0 {
				resBid.App.Ac = append(resBid.App.Ac, genericAd.GetActionCallbackUrl())
			}

			if genericAd.GetAppInfo().WechatExt != nil {
				resBid.WxId = genericAd.GetAppInfo().WechatExt.ProgramId
				resBid.WxPath = genericAd.GetAppInfo().WechatExt.ProgramPath
			}
		}

		bidResponse.SeatBid.Bid = append(bidResponse.SeatBid.Bid, resBid)
		break
	}

	return bidResponse, nil
}
func mappingSlotType(s entity.SlotType) int32 {
	switch s {
	case entity.SlotTypeBanner:
		return 1
	case entity.SlotTypeFeeds:
		return 2
	case entity.SlotTypeVideoOpening:
		return 3
	case entity.SlotTypeVideo:
		return 4
	case entity.SlotTypePopup:
		return 5
	default:
		return 2
	}
}

func mappingOsType(os chengxiao_proto.MobReqDeviceOsType) entity.OsType {
	switch os {
	case 0:
		return entity.OsTypeAndroid
	case 1:
		return entity.OsTypeIOS
	case 2:
		return entity.OsTypeWindows
	case 3:
		return entity.OsTypeMacOs
	default:
		return entity.OsTypeAndroid
	}
}

func mappingScreenOrientation(orientation int32) entity.ScreenOrientationType {
	switch orientation {
	case 0:
		return entity.ScreenOrientationTypePortrait
	case 1:
		return entity.ScreenOrientationTypeLandscape
	default:
		return entity.ScreenOrientationTypePortrait
	}
}

func mappingOperatorType(carrier chengxiao_proto.MobReqDeviceCarrierType) entity.OperatorType {
	switch carrier {
	case 46000, 46002:
		return entity.OperatorTypeChinaMobile
	case 46001:
		return entity.OperatorTypeChinaUnicom
	case 46003:
		return entity.OperatorTypeChinaTelecom
	case 46020:
		return entity.OperatorTypeTietong
	default:
		return entity.OperatorTypeUnknown
	}
}

func mappingConnectionType(connectionType chengxiao_proto.MobDeviceConnectionType) entity.ConnectionType {
	switch connectionType {
	case 1:
		return entity.ConnectionTypeWifi
	case 2:
		return entity.ConnectionType2G
	case 3:
		return entity.ConnectionType3G
	case 4:
		return entity.ConnectionType4G
	case 5:
		return entity.ConnectionType5G
	default:
		return entity.ConnectionTypeUnknown
	}
}

func mappingDeviceType(deviceType chengxiao_proto.MobReqDeviceType) entity.DeviceType {
	switch deviceType {
	case 1:
		return entity.DeviceTypeMobile
	case 2:
		return entity.DeviceTypePad
	case 3:
		return entity.DeviceTypePc
	case 4:
		return entity.DeviceTypeOtt
	default:
		return entity.DeviceTypeMobile
	}
}
