package qtt_traffic_broker

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/qtt_traffic_broker/qtt_traffic_broker_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"gitlab.com/dev/heidegger/tracking/tracking_gen"
)

const AdxTemplateKey = "adxTemplate"
const nativeType = "native_type"

var (
	emptyAdxTemplateMap map[uint64]int = make(map[uint64]int)
)

type QttTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	host    string
	mediaId utils.ID

	WinPriceMacro string
	MediaMacro    *macro_builder.MediaMacro
}

func NewQttTrafficBroker(mediaId utils.ID) *QttTrafficBroker {
	return &QttTrafficBroker{
		mediaId:       mediaId,
		WinPriceMacro: "{WIN_PRICE}",
		MediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "{WIN_PRICE}",
			MediaClickDownXMacro: "${down_x}",
			MediaClickDownYMacro: "${down_y}",
			MediaClickUpXMacro:   "${up_x}",
			MediaClickUpYMacro:   "${up_y}",
			MediaHWSldMacro:      "${sld}",
		},
	}
}

func (mb *QttTrafficBroker) GetMediaId() utils.ID {
	return mb.mediaId
}

func (mb *QttTrafficBroker) Do(request *ad_service.AdRequest) error {
	return mb.ParseAdRequest(request)
}

func (mb *QttTrafficBroker) ParseAdRequest(adRequest *ad_service.AdRequest) error {
	adRequest.Response.SetResponseBuilder(mb.SendResponse)
	adRequest.Response.SetFallbackResponseBuilder(mb.SendFallbackResponse)
	adRequest.AdRequestMedia.WinPriceMacro = mb.WinPriceMacro
	adRequest.AdRequestMedia.MediaMacro = mb.MediaMacro

	body := adRequest.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[QttTrafficBroker] request body empty")
	}

	bidRequest := qtt_traffic_broker_entity.QttRequest{}
	//err := easyjson.Unmarshal(body, &bidRequest)
	err := sonic.Unmarshal(body, &bidRequest)
	if err != nil {
		zap.L().Error("[QttTrafficBroker] Request body unmarshal failed, err", zap.Error(err))
		return fmt.Errorf("request body invalid")
	}

	if adRequest.IsDebug {
		reqBody, _ := sonic.Marshal(bidRequest)
		zap.L().Info("[QttTrafficBroker] Parse Request start. broker request:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqBody)))))
	}

	adRequest.SetRequestId(bidRequest.Id)
	if len(bidRequest.Id) < 1 {
		adRequest.SetRequestId(adRequest.GenerateRequestId())
	}

	adRequest.SetMediaId(mb.GetMediaId())

	if err := mb.parseUser(bidRequest, adRequest); err != nil {
		zap.L().Debug("[QttTrafficBroker] BrokeRequest, parseUser failed")
		return err
	}

	if err := mb.parseGeo(bidRequest, adRequest); err != nil {
		zap.L().Debug("[QttTrafficBroker] BrokeRequest, parseGeo failed")
		return err
	}

	if err := mb.parseApp(bidRequest, adRequest); err != nil {
		zap.L().Debug("[QttTrafficBroker] BrokeRequest, parseApp failed")
		return err
	}

	if err := mb.parseDevice(bidRequest, adRequest); err != nil {
		zap.L().Debug("[QttTrafficBroker] BrokeRequest, parseDevice failed")
		return err
	}

	if err := mb.parseImp(bidRequest, adRequest); err != nil {
		zap.L().Debug("[QttTrafficBroker] BrokeRequest, parseImp failed")
		return err
	}

	if adRequest.Device.RequestIp == "client" || len(adRequest.Device.RequestIp) < 1 {
		clientIp, _ := net_utils.ParseIpPort(adRequest.RawHttpRequest.GetRemoteAddress())
		if clientIp != nil {
			adRequest.Device.RequestIp = clientIp.String()
		}
	}
	mb.DoTrafficSample(adRequest, body)

	return nil
}

func (mb *QttTrafficBroker) parseUser(bidRequest qtt_traffic_broker_entity.QttRequest, adRequest *ad_service.AdRequest) error {
	adRequest.UserGender = qtt_traffic_broker_entity.ParseQttGender(bidRequest.User.Gender)
	adRequest.UserAge = int32(bidRequest.User.Age)
	return nil
}

func (mb *QttTrafficBroker) parseGeo(bidRequest qtt_traffic_broker_entity.QttRequest, adRequest *ad_service.AdRequest) error {
	adRequest.Device.Lat = bidRequest.Geo.Lat
	adRequest.Device.Lon = bidRequest.Geo.Lon
	switch bidRequest.Geo.LbsType {
	case 0:
		adRequest.Device.GeoStandard = 1
	case 1:
		adRequest.Device.GeoStandard = 2
	case 2:
		adRequest.Device.GeoStandard = 3
	}
	return nil
}

func (mb *QttTrafficBroker) parseApp(bidRequest qtt_traffic_broker_entity.QttRequest, adRequest *ad_service.AdRequest) error {
	if len(bidRequest.App.IndustryId) != 0 {
		adRequest.App.AdxAppCategory = append(adRequest.App.AdxAppCategory, bidRequest.App.IndustryId)
	}
	adRequest.App.AppName = bidRequest.App.AppName
	adRequest.App.AppBundle = bidRequest.App.AppBundleId
	adRequest.App.AppVersion = bidRequest.App.AppVersion
	adRequest.App.InstalledApp = bidRequest.App.InstalledClientApps
	for _, id := range bidRequest.App.InstalledClientAppIds {
		adRequest.App.MediaInstalledAppIds = append(adRequest.App.MediaInstalledAppIds, strconv.Itoa(id))
	}
	return nil
}

func (mb *QttTrafficBroker) parseDevice(bidRequest qtt_traffic_broker_entity.QttRequest, adRequest *ad_service.AdRequest) error {
	device := &bidRequest.Device

	adRequest.Device.DeviceType = qtt_traffic_broker_entity.ParseQttDeviceType(device.DeviceType)
	adRequest.Device.ConnectionType = qtt_traffic_broker_entity.ParseQttConnectionType(device.ConnectionType)
	adRequest.Device.OperatorType = qtt_traffic_broker_entity.ParseQttCarrier(device.Carrier)
	adRequest.Device.OsType = qtt_traffic_broker_entity.ParseQttOsType(device.Os)
	adRequest.Device.OsVersion = device.OsVersion

	adRequest.Device.Brand = bidRequest.Device.Brand
	adRequest.Device.Model = bidRequest.Device.Model

	adRequest.Device.Imei = device.Imei
	adRequest.Device.ImeiMd5 = device.ImeiMd5

	adRequest.Device.Oaid = device.Oaid
	adRequest.Device.OaidMd5 = device.OaidMd5

	adRequest.Device.Idfa = device.Idfa
	adRequest.Device.IdfaMd5 = device.IdfaMd5

	adRequest.Device.AndroidId = device.AndroidId
	adRequest.Device.AndroidIdMd5 = device.AndroidIdMd5

	adRequest.Device.Mac = device.Mac
	adRequest.Device.MacMd5 = device.MacMd5

	adRequest.Device.RequestIp = device.Ip
	if len(device.Ip) == 0 && len(device.IpV6) > 0 {
		adRequest.Device.RequestIp = device.IpV6
		adRequest.Device.IsIp6 = true
	}
	// device.Ip may be ipv6 too
	if strings.ContainsRune(adRequest.Device.RequestIp, ':') {
		adRequest.Device.IsIp6 = true
	}

	adRequest.Device.UserAgent = device.UserAgent
	adRequest.Device.WebviewUA = device.BrowserUserAgent
	adRequest.Device.ScreenWidth = int32(device.ScreenWidth)
	adRequest.Device.ScreenHeight = int32(device.ScreenHeight)

	adRequest.Device.BootMark = device.BootMark
	adRequest.Device.UpdateMark = device.UpdateMark

	adRequest.Device.VercodeHms = device.HuaweiVerCodeOfHms
	adRequest.Device.VercodeAg = device.AgVersion
	adRequest.Device.Paid = device.Paid
	adRequest.Device.ScreenDensity = float32(device.Dpi)
	adRequest.Device.PPI = int32(device.Ppi)

	adRequest.Device.DeviceStartupTime = device.StartupTime
	adRequest.Device.DeviceInitTime = device.DeviceStartTime
	adRequest.Device.DeviceUpgradeTime = device.MbTime
	adRequest.Device.RomVersion = device.RomVersion
	adRequest.Device.RomName = device.PhoneName
	adRequest.Device.DeviceName = device.DeviceName
	adRequest.Device.HardwareMachineCode = device.HwMachine
	adRequest.Device.SystemTotalMem = device.PhysicalMemory
	adRequest.Device.SystemTotalDisk = device.StorageMemory
	adRequest.Device.SystemCompileTime = device.SysComplingTime
	if len(adRequest.Device.DeviceStartupTime) == 0 && device.ElapseTime != 0 {
		adRequest.Device.DeviceStartupTime = type_convert.GetAssertString(device.ElapseTime)
	}

	for i, v := range device.Caids {
		caid := string_utils.ConcatString(v.Version, "_", v.Caid)
		// set first as default
		if i == 0 {
			adRequest.Device.CaidRaw = v.Caid
			adRequest.Device.CaidVersion = v.Version
			adRequest.Device.Caid = caid
		}
		adRequest.Device.Caids = append(adRequest.Device.Caids, caid)
	}

	if adRequest.Device.OsType == entity.OsTypeIOS {
		adRequest.Device.SystemTotalMem = int64(device.MemTotal)
		adRequest.Device.SystemTotalDisk = int64(device.DiskTotal)
		adRequest.Device.DeviceName = device.PhoneName
		adRequest.Device.RomName = device.DeviceTypeModel
	}

	return nil
}

func (mb *QttTrafficBroker) parseImp(bidRequest qtt_traffic_broker_entity.QttRequest, adRequest *ad_service.AdRequest) error {
	if len(bidRequest.Impressions) == 0 {
		return fmt.Errorf("[QttTrafficBroker] Impressions empty")
	}

	imp := bidRequest.Impressions[0]

	adRequest.ImpressionId = imp.Id
	adRequest.SetMediaSlotKey(imp.AdSlotId)
	adRequest.BidFloor = uint32(imp.BidFloor)
	adRequest.SupportDeeplink = imp.SupportDeepLink
	adRequest.UseHttps = true // 自2025-01-01起，不再支持http物料
	adRequest.BidType = qtt_traffic_broker_entity.ParseBidType(imp.ChargeType)

	adxTemplateMap := make(map[uint64]int)
	defaultNativeType := 0

	for idx := range imp.Natives {
		if err := mb.parseNative(&imp.Natives[idx], adRequest, &adxTemplateMap, &defaultNativeType); err != nil {
			return err
		}
	}
	adRequest.AddMediaExtraData(AdxTemplateKey, adxTemplateMap)
	adRequest.AddMediaExtraInt64(nativeType, int64(defaultNativeType))

	return nil
}

func (mb *QttTrafficBroker) parseNative(native *qtt_traffic_broker_entity.QttRequestImpressionNative, adRequest *ad_service.AdRequest, adxTemplateMap *map[uint64]int, defaultType *int) error {
	templateKey := creative_entity.NewCreativeTemplateKey()
	if *defaultType == 0 {
		*defaultType = native.Type
	}
	switch native.Type {
	case 1:
		templateKey.Title().SetRequiredCount(1)
	case 2:
		templateKey.Title().SetRequiredCount(1)
		templateKey.Desc().SetRequiredCount(1)
	case 3:
		templateKey.Title().SetRequiredCount(1)
	case 4:
		templateKey.Title().SetRequiredCount(1)
		templateKey.Desc().SetRequiredCount(1)
	case 7:
		templateKey.Title().SetRequiredCount(1)
		templateKey.Desc().SetRequiredCount(1)
	case 8:
		templateKey.Title().SetRequiredCount(1)
		templateKey.Desc().SetRequiredCount(1)
	}

	for _, img := range native.Imgs {
		switch img.Type {
		case 0: // 大图
			templateKey.Image().AddRequiredCount(1)
			templateKey.Image().AddRequiredSizeType(qtt_traffic_broker_entity.ParseQttAspectRatio(img.AspectRatio))
		case 1: // 小图(图文/组图)
			templateKey.Image().AddRequiredCount(1)
			templateKey.Image().AddRequiredSizeType(qtt_traffic_broker_entity.ParseQttAspectRatio(img.AspectRatio))
		case 2: // 图标
			templateKey.Icon().AddRequiredCount(1).SetOptional(true)
		}
	}

	for _, video := range native.Videos {
		switch video.Type {
		case 0:
			templateKey.Video().AddRequiredCount(1)
			templateKey.Video().AddRequiredSizeType(creative_entity.RT_SIZE_NULL)
		case 1:
			templateKey.Video().AddRequiredCount(1)
			templateKey.Video().AddRequiredSizeType(creative_entity.RT_SIZE_VERTICAL)
		case 2:
			templateKey.Video().AddRequiredCount(1)
			templateKey.Video().AddRequiredSizeType(creative_entity.RT_SIZE_HORIZONTAL)
		}
	}

	//zap.L().Info("[QttTrafficBroker] templateKey:%+v"))
	adRequest.AppendCreativeTemplateKey(templateKey)
	keyId := templateKey.Uint64()
	(*adxTemplateMap)[keyId] = native.Type

	return nil
}

func (mb *QttTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		zap.L().Info("[QttTrafficBroker] SendFallbackResponse start")
	}

	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	writer.WriteWithStatus(200, fmt.Appendf(nil, `{"code":0,"request_id": "%s"}`, request.GetRequestId()))

	return nil
}

func (mb *QttTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	if request.IsDebug {
		request.Response.Dump("QttTrafficBroker")
		zap.L().Info("[QttTrafficBroker] adCode:%+v").GetErrCodeMap())
	}

	if len(request.Response.GetAdCandidateList()) == 0 {
		return mb.SendFallbackResponse(request, writer)
	}

	candidate := request.Response.GetAdCandidateList()[0]

	bidResponse := qtt_traffic_broker_entity.QttResponse{}
	bidResponse.Code = 200
	bidResponse.RequestId = request.GetRequestId()
	bidResponse.SeatBids = []qtt_traffic_broker_entity.QttResponseSeatBid{
		{
			ImpressionId: request.ImpressionId,
			Bids: []qtt_traffic_broker_entity.QttResponseBid{
				mb.buildBid(request, candidate),
			},
		},
	}

	//if err := mb.BuildHttpEasyJsonResponse(request, writer, &bidResponse); err != nil {
	//	return err
	//}

	if err := mb.BuildHttpSonicJsonResponse(request, writer, &bidResponse); err != nil {
		return err
	}

	mb.DoTrafficResponseSampleSonicJson(request, bidResponse)

	if request.IsDebug {
		zap.L().Info("[QttTrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", bidResponse.DumpJson())))))
	}

	return nil
}

func (mb *QttTrafficBroker) buildBid(request *ad_service.AdRequest, candidate *ad_service.AdCandidate) qtt_traffic_broker_entity.QttResponseBid {
	bid := qtt_traffic_broker_entity.QttResponseBid{
		Id: request.GetRequestId(),
	}

	bidPrice := candidate.GetBidPrice()
	bid.BidPrice = int32(bidPrice.Price)

	genericAd := candidate.GetGenericAd()
	creative := candidate.GetCreative()
	traffic := candidate.GetModifiedTrafficData()
	trackingGen := candidate.GetTrackingGen(traffic)

	bid.Creative.LandingUrl = candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen)
	if request.Device.GetOsType() == entity.OsTypeIOS {
		bid.Creative.UniversalLink = candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen)
	} else {
		bid.Creative.DeepLinkUrl = candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen)
	}

	switch genericAd.GetLandingAction() {
	case entity.LandingTypeDeepLink:
		// 快应用预算，interaction_type传0
		if net_utils.IsQuickApp(genericAd.GetDeepLinkUrl()) {
			bid.Creative.InteractionType = 0
		} else {
			bid.Creative.InteractionType = 1
		}
	case entity.LandingTypeDownload:
		bid.Creative.InteractionType = 1
	case entity.LandingTypeWeChatProgram:
		bid.Creative.InteractionType = 3
	default:
		bid.Creative.InteractionType = 0
	}

	if genericAd.GetAppInfo() != nil {
		app := genericAd.GetAppInfo()
		bid.Creative.App = &qtt_traffic_broker_entity.QttResponseApp{
			AppName:               app.AppName,
			PackageName:           app.PackageName,
			PackageSize:           app.PackageSize,
			AppVersion:            app.AppVersion,
			Developers:            app.Develop,
			PrivacyProtocolUrl:    app.Privacy,
			PermissionProtocolUrl: app.Permission,
			FunctionDescUrl:       app.AppDescURL,
		}

		if bid.Creative.InteractionType == 1 {
			bid.Creative.App.Src = candidate.ReplaceUrlMacro(genericAd.GetDownloadUrl(), traffic, trackingGen)
			if len(bid.Creative.App.Src) == 0 {
				bid.Creative.App.Src = candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen)
			}
		} else if bid.Creative.InteractionType == 3 && app.WechatExt != nil {
			bid.Creative.Weixin = &qtt_traffic_broker_entity.QttResponseWeixin{
				ReleaseType: 0,
				ProgramId:   app.WechatExt.ProgramId,
				ProgramPath: app.WechatExt.ProgramPath,
			}
		}
	}

	bid.ImpUrls = candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
	bid.ClkUrls = candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen)

	/* QTT do not support DP callback from v1.44
	bid.DpClks = candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen)
	if len(genericAd.GetDeepLinkUrl()) > 0 {
		bid.DpClks = append(bid.DpClks, candidate.ReplaceUrlMacro(genericAd.GetDpSuccess(), traffic, trackingGen))
	}
	if len(genericAd.GetDeepLinkFailedMonitorList()) > 0 {
		bid.DpFailedClks = candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkFailedMonitorList(), traffic, trackingGen)
	}
	*/

	if len(genericAd.GetAppDownloadStartedMonitorList()) > 0 {
		bid.DbmUrls = candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadStartedMonitorList(), traffic, trackingGen)
	}

	if len(genericAd.GetAppDownloadFinishedMonitorList()) > 0 {
		bid.DemUrls = candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadFinishedMonitorList(), traffic, trackingGen)
	}

	if len(genericAd.GetAppInstallStartMonitorList()) > 0 {
		bid.IbmUrls = candidate.ReplaceUrlMacroList(genericAd.GetAppInstallStartMonitorList(), traffic, trackingGen)
	}

	if len(genericAd.GetAppInstalledMonitorList()) > 0 {
		bid.IemUrls = candidate.ReplaceUrlMacroList(genericAd.GetAppInstalledMonitorList(), traffic, trackingGen)
	}

	if len(genericAd.GetAppOpenMonitorList()) > 0 {
		bid.AomUrls = candidate.ReplaceUrlMacroList(genericAd.GetAppOpenMonitorList(), traffic, trackingGen)
	}

	if len(genericAd.GetAppCopyMonitorList()) > 0 {
		bid.LpCopyUrls = candidate.ReplaceUrlMacroList(genericAd.GetAppCopyMonitorList(), traffic, trackingGen)
	}

	if len(genericAd.GetClkInLandingMonitorList()) > 0 {
		bid.LpClkUrls = candidate.ReplaceUrlMacroList(genericAd.GetClkInLandingMonitorList(), traffic, trackingGen)
	}

	if genericAd.GetAdExtInfo() != nil {
		bid.Ext = &qtt_traffic_broker_entity.QttResponseExt{
			XRequestWithPackagename: genericAd.GetAdExtInfo().XRequestWithPackageName,
			ReplacedXua:             genericAd.GetAdExtInfo().XUserAgent,
			ReplacedXrw:             genericAd.GetAdExtInfo().XRequestWithPackageName,
			DeliveryType:            genericAd.GetAdExtInfo().DeliveryType,
		}
	}
	adSource := qtt_traffic_broker_entity.ParseDspAdSource(candidate.GetDspProtocol())
	if len(adSource) > 0 {
		if bid.Ext == nil {
			bid.Ext = &qtt_traffic_broker_entity.QttResponseExt{
				DspAdSource: adSource,
			}
		} else {
			bid.Ext.DspAdSource = adSource
		}
	}

	bid.Creative.Id = strconv.Itoa(int(creative.GetCreativeId()))
	if creative.GetCreativeId() == 0 {
		bid.Creative.Id = creative.GetCreativeKey()
	}

	reqAdxTemplateMap := request.GetMediaExtraDataWithDefault(AdxTemplateKey, emptyAdxTemplateMap).(map[uint64]int)
	// native type set
	reqAdxTemplateSet := make(map[int]bool, len(reqAdxTemplateMap))
	for _, v := range reqAdxTemplateMap {
		reqAdxTemplateSet[v] = true
	}

	key1 := candidate.GetActiveCreativeTemplateKey()
	keyInt := key1.Uint64()
	bid.Creative.Type = reqAdxTemplateMap[keyInt]
	var hasImg, hasVideo, hasTitle, hasDesc bool
	for _, material := range candidate.GetSelectedMaterialList() {
		switch material.MaterialType {
		case entity.MaterialTypeTitle:
			bid.Creative.Title = &qtt_traffic_broker_entity.QttResponseTitle{
				Title: material.Data,
			}
			hasTitle = true
		case entity.MaterialTypeDesc:
			bid.Creative.Desc = &qtt_traffic_broker_entity.QttResponseDesc{
				Desc: material.Data,
			}
			hasDesc = true
		case entity.MaterialTypeIcon:
			img := qtt_traffic_broker_entity.QttResponseImg{
				Type:   2,
				Src:    candidate.ReplaceUrlGeneral(material.Url),
				Width:  int(material.Width),
				Height: int(material.Height),
			}
			bid.Creative.Imgs = append(bid.Creative.Imgs, img)
		case entity.MaterialTypeImage:
			img := qtt_traffic_broker_entity.QttResponseImg{
				Type: 0,
				// 为了适配中广传媒的素材和落地页宏替换需求
				Src:    candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen),
				Width:  int(material.Width),
				Height: int(material.Height),
			}

			if bid.Creative.Type == 2 || bid.Creative.Type == 3 {
				img.Type = 1
			}

			hasImg = true
			bid.Creative.Imgs = append(bid.Creative.Imgs, img)
		case entity.MaterialTypeVideo:
			video := qtt_traffic_broker_entity.QttResponseVideo{
				Type: 0,
				// 为了适配中广传媒的素材和落地页宏替换需求
				Src:      candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen),
				Width:    int(material.Width),
				Height:   int(material.Height),
				Duration: int(material.Duration),
				EventImp: mb.buildVideoTracking(candidate, genericAd, traffic, trackingGen),
			}

			if bid.Creative.Type == 5 {
				video.Type = 1
			}

			hasVideo = true
			bid.Creative.Videos = append(bid.Creative.Videos, video)
		}
	}

	// NOTE: 物料类型对应且包含于Request.impressions[].creatives[].type中，否则会被过滤
	// 优先使用开屏5
	if reqAdxTemplateSet[5] {
		bid.Creative.Type = 5
	}
	// 根据物料情况来判断
	if bid.Creative.Type == 0 {
		if hasVideo {
			if hasImg && reqAdxTemplateSet[8] {
				bid.Creative.Type = 8
			} else if reqAdxTemplateSet[7] {
				bid.Creative.Type = 7
			}
		} else if hasImg {
			if hasTitle && reqAdxTemplateSet[1] {
				bid.Creative.Type = 1
			} else if hasTitle && hasDesc && reqAdxTemplateSet[2] {
				bid.Creative.Type = 2
			} else if hasTitle && reqAdxTemplateSet[3] {
				bid.Creative.Type = 3
			} else if hasTitle && hasDesc && reqAdxTemplateSet[4] {
				bid.Creative.Type = 4
			} else if reqAdxTemplateSet[6] {
				bid.Creative.Type = 6
			}
		}
	}
	// 默认第一个
	if bid.Creative.Type == 0 {
		bid.Creative.Type = int(request.GetMediaExtraInt64WithDefault(nativeType, 0))
	}

	return bid
}

func (mb *QttTrafficBroker) buildVideoTracking(candidate *ad_service.AdCandidate, genericAd entity.GenericAd, trafficData ad_service_entity.TrafficData, trackingGen *tracking_gen.TrackingGen) []qtt_traffic_broker_entity.QttResponseEventImp {
	events := make([]qtt_traffic_broker_entity.QttResponseEventImp, 0)
	if len(genericAd.GetVideoStartUrlList()) > 0 {
		events = append(events, qtt_traffic_broker_entity.QttResponseEventImp{
			Event: "start",
			Imps:  candidate.ReplaceUrlMacroList(genericAd.GetVideoStartUrlList(), trafficData, trackingGen),
		})
	}
	if len(genericAd.GetVideoCloseUrlList()) > 0 {
		events = append(events, qtt_traffic_broker_entity.QttResponseEventImp{
			Event: "complete",
			Imps:  candidate.ReplaceUrlMacroList(genericAd.GetVideoCloseUrlList(), trafficData, trackingGen),
		})
	}
	return events
}
