package topon_broker

import (
	"encoding/json"
	"fmt"
	"slices"
	"strconv"
	"strings"

	"github.com/gogo/protobuf/jsonpb"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_broker/topon_broker/topon_broker_proto"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/string_utils"
	"gitlab.com/dev/heidegger/library/utils/vast_utils"
)

const (
	ReqNativeKey = "reqNative"
	videoRequest = "video_request"
)

type TopOnTrafficBroker struct {
	traffic_broker.TrafficBrokerBase

	mediaId       utils.ID
	host          string
	useRawMonitor bool

	WinPriceMacro string
	MediaMacro    *macro_builder.MediaMacro
}

func NewTopOnTrafficBroker(mediaId utils.ID) *TopOnTrafficBroker {
	return &TopOnTrafficBroker{
		mediaId:       mediaId,
		WinPriceMacro: "${AUCTION_PRICE}",
		MediaMacro: &macro_builder.MediaMacro{
			MediaPriceMacro:      "${AUCTION_PRICE}",
			MediaClickDownXMacro: "{__RE_DOWN_X__}",
			MediaClickDownYMacro: "{__RE_DOWN_Y__}",
			MediaClickUpXMacro:   "{__RE_UP_X__}",
			MediaClickUpYMacro:   "{__RE_UP_Y__}",
			MediaHWSldMacro:      "{__SLD__}",
		},
	}
}

func (k *TopOnTrafficBroker) GetMediaId() utils.ID {
	return k.mediaId
}

func (k *TopOnTrafficBroker) Do(request *ad_service.AdRequest) error {
	return k.ParseAdRequest(request)
}

func (k *TopOnTrafficBroker) ParseAdRequest(request *ad_service.AdRequest) error {
	zap.L().Debug("[TopOnTrafficBroker] ParseAdRequest")

	request.Response.SetResponseBuilder(k.SendResponse)
	request.Response.SetFallbackResponseBuilder(k.SendFallbackResponse)
	request.AdRequestMedia.WinPriceMacro = k.WinPriceMacro
	request.AdRequestMedia.MediaMacro = k.MediaMacro

	body := request.RawHttpRequest.GetBodyContent()
	if len(body) == 0 {
		return fmt.Errorf("[TopOnTrafficBroker]request body empty")
	}

	toponRequest := &topon_broker_proto.BidRequest{}

	err := jsonpb.UnmarshalString(string(body), toponRequest)
	//err := proto.Unmarshal(body, toponRequest)
	if err != nil {
		zap.L().Error("[TopOnTrafficBroker]BrokeRequest, Request body unmarshal failed, err", zap.Error(err))
		return errors.Wrapf(err, "[TopOnTrafficBroker]request body invalid")
	}

	if toponRequest.GetImp() == nil || len(toponRequest.GetImp()) <= 0 {
		zap.L().Debug("TopOnTrafficBroker request invalid", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request)))))
		return err_code.ErrInvalidImpression
	}

	request.SetRequestId(toponRequest.Id)
	if len(request.GetRequestId()) < 1 {
		request.SetRequestId(utils.NewUUID())
	}
	request.SetMediaId(k.mediaId)

	if toponRequest.Site != nil {
		request.Url = toponRequest.Site.Page
		request.Referer = toponRequest.Site.Ref
	}

	if toponRequest.App != nil {
		request.App.AppName = toponRequest.App.Name
		request.App.AppBundle = toponRequest.App.Bundle
		request.App.AppVersion = toponRequest.App.Ver
	}

	if toponRequest.User != nil {
		if toponRequest.User.Gender == "M" {
			request.UserGender = entity.UserGenderMan
		} else if toponRequest.User.Gender == "F" {
			request.UserGender = entity.UserGenderWoman
		}
	}

	if err := k.parseImp(toponRequest, request); err != nil {
		zap.L().Debug("[TopOnTrafficBroker] BrokeRequest, parseImp failed")
		return err
	}

	if err := k.parseDevice(toponRequest, request); err != nil {
		zap.L().Debug("[TopOnTrafficBroker] BrokeRequest, parseDevice failed")
		return err
	}

	k.DoTrafficSamplePb(request, toponRequest)
	return nil
}

func (k *TopOnTrafficBroker) parseDevice(toponRequest *topon_broker_proto.BidRequest, bidRequest *ad_service.AdRequest) error {
	if toponRequest.Device == nil {
		return nil
	}
	device := toponRequest.Device

	bidRequest.Device.UserAgent = device.Ua
	if device.Geo != nil {
		bidRequest.Device.Lat = device.Geo.Lat
		bidRequest.Device.Lon = device.Geo.Lon
	}
	if len(device.Ip) > 0 {
		bidRequest.Device.RequestIp = device.Ip
	} else if len(device.Ipv6) > 0 {
		bidRequest.Device.RequestIp = device.Ipv6
	}
	bidRequest.Device.DeviceType = k.mappingDeviceType(device.Devicetype)
	bidRequest.Device.Vendor = device.Make
	bidRequest.Device.Brand = device.Make
	bidRequest.Device.Model = device.Model
	bidRequest.Device.OsType = k.mappingOs(device.Os)
	bidRequest.Device.OsVersion = device.Osv
	bidRequest.Device.ScreenHeight = device.H
	bidRequest.Device.ScreenWidth = device.W
	bidRequest.Device.ScreenDensity = float32(device.Ppi)
	bidRequest.Device.OperatorType = k.mappingCarrier(device.Carrier)
	bidRequest.Device.ConnectionType = k.mappingConnectionType(device.Connectiontype)
	bidRequest.Device.Language = device.Language

	if bidRequest.Device.OsType == entity.OsTypeIOS {
		bidRequest.Device.Idfa = device.Ifa
	} else if bidRequest.Device.OsType == entity.OsTypeAndroid {
		bidRequest.Device.Oaid = device.Ifa
	}
	bidRequest.Device.ImeiMd5 = device.Didmd5
	bidRequest.Device.IdfvMd5 = device.Dpidmd5
	bidRequest.Device.MacMd5 = device.Macmd5

	if device.Ext != nil {
		if len(device.Ext.Common) > 0 {
			deviceExt := topon_broker_proto.TopOnBidRequestDeviceExt{}
			err := json.Unmarshal([]byte(device.Ext.Common), &deviceExt)
			if err == nil {
				bidRequest.Device.DeviceStartupTime = deviceExt.SysBootTime
				bidRequest.Device.DeviceUpgradeTime = deviceExt.SysUpdateTime
				bidRequest.Device.DeviceInitTime = deviceExt.BirthTime
				bidRequest.Device.BootMark = deviceExt.BootMark
				bidRequest.Device.UpdateMark = deviceExt.UpdateMark
				bidRequest.Device.RomName = deviceExt.Firm
				bidRequest.Device.RomVersion = deviceExt.FirmVersion
				bidRequest.Device.AppStoreVersion = deviceExt.FirmVersion
				bidRequest.Device.VercodeHms = deviceExt.HmsVersion
				bidRequest.Device.SdkVersion = deviceExt.Osvc
				bidRequest.Device.DeviceName = deviceExt.DeviceName
				bidRequest.Device.SystemTotalMem = deviceExt.MemTotal
				bidRequest.Device.SystemTotalDisk = deviceExt.DiskTotal
				bidRequest.App.MediaInstalledAppIds = deviceExt.AppList

				if len(deviceExt.Oaid) > 0 {
					bidRequest.Device.Oaid = deviceExt.Oaid
				}
				if len(deviceExt.Did) > 0 {
					bidRequest.Device.Imei = deviceExt.Did
				}
				if len(deviceExt.Dpid) > 0 {
					switch bidRequest.Device.OsType {
					case entity.OsTypeIOS:
						bidRequest.Device.Idfv = deviceExt.Dpid
					case entity.OsTypeAndroid:
						bidRequest.Device.AndroidId = deviceExt.Dpid
					}
				}
				if len(deviceExt.CaidList) > 0 {
					for _, item := range deviceExt.CaidList {
						caid := string_utils.ConcatString(item.Version, "_", item.Caid)
						bidRequest.Device.Caids = append(bidRequest.Device.Caids, caid)
						bidRequest.Device.Caid = caid
					}
					// 取最新的CAID版本
					if len(bidRequest.Device.Caids) > 1 {
						slices.Sort(bidRequest.Device.Caids)
						bidRequest.Device.Caid = bidRequest.Device.Caids[len(bidRequest.Device.Caids)-1]
					}
				}

				if len(deviceExt.HmsVersion) > 0 {
					bidRequest.Device.VercodeAg = deviceExt.FirmVersion
				}
				if deviceExt.WxApplet == "1" {
					bidRequest.BlockWechatMiniProgram = true
				}
				if deviceExt.QuickApp == "1" {
					bidRequest.BlockQuickapp = true
				}
			}
		}
	}

	return nil
}

func (k *TopOnTrafficBroker) parseImp(toponRequest *topon_broker_proto.BidRequest, request *ad_service.AdRequest) error {
	if toponRequest.Imp == nil {
		zap.L().Debug("parseImp, vendor: , imp nil", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(request.GetMediaId())))))
		return errors.New("TopOnTrafficBroker parseImp, imp nil")
	}

	for _, imp := range toponRequest.Imp {
		if imp == nil {
			continue
		}

		request.ImpressionId = imp.Id
		request.SetMediaSlotKey(imp.Tagid)
		request.BidFloor = uint32(imp.Bidfloor)
		if imp.GetSecure() == 1 {
			request.UseHttps = true
		}

		if imp.Banner != nil {
			request.SlotType = entity.SlotTypeBanner
			banner := imp.Banner
			request.SlotSize = make([]ad_service.Size, 0)
			if banner.W != 0 && banner.H != 0 {
				request.SlotSize = append(request.SlotSize, ad_service.Size{
					Width:  int64(banner.W),
					Height: int64(banner.H)})
			}

		}

		if imp.Video != nil {
			request.SlotType = entity.SlotTypeVideo
			video := imp.Video
			request.SlotSize = make([]ad_service.Size, 0)
			if video.W != 0 && video.H != 0 {
				request.SlotSize = append(request.SlotSize, ad_service.Size{
					Width:  int64(video.W),
					Height: int64(video.H)})
			}
			request.VideoMinDuration = video.Minduration
			request.VideoMaxDuration = video.Maxduration
			key := creative_entity.NewCreativeTemplateKey()
			sizeType := creative_entity.RT_SIZE_NULL
			key.Title().SetRequiredCount(1).SetOptional(true)
			key.Image().SetRequiredCount(1).SetRequiredSizeType(sizeType).SetOptional(true)
			key.Video().SetRequiredCount(1).SetRequiredSizeType(sizeType)
			request.AddMediaExtraInt64(videoRequest, 1)
			request.AppendCreativeTemplateKey(key)
		}

		if imp.Native != nil {
			request.SlotType = entity.SlotTypeFeeds
			native := imp.Native
			if len(native.Request) > 0 && native.RequestNative == nil {
				requestNative := topon_broker_proto.TopOnBidRequestNative{}
				err := json.Unmarshal([]byte(native.Request), &requestNative)
				if err != nil {
					zap.L().Error("[TopOnTrafficBroker]json unmarshal error, body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", []byte(native.Request))))))
					continue
				}
				native.RequestNative = &requestNative.Native
			}

			if native.RequestNative != nil {
				request.AddMediaExtraData(ReqNativeKey, native.RequestNative)
				key := creative_entity.NewCreativeTemplateKey()
				for _, asset := range native.RequestNative.Assets {
					if asset == nil {
						continue
					}

					if asset.Img != nil && asset.Required == 1 && asset.Img.Type == topon_broker_proto.ImageAssetType_MAIN {
						//sizeType := creative_entity.RT_SIZE_VERTICAL
						//if asset.Img.W > asset.Img.H {
						//	sizeType = creative_entity.RT_SIZE_HORIZONTAL
						//}
						request.SlotSize = append(request.SlotSize, ad_service.Size{
							Width:  int64(asset.Img.W),
							Height: int64(asset.Img.H),
						})

						sizeType := creative_entity.RT_SIZE_NULL
						key.Image().SetRequiredCount(1).SetRequiredSizeType(sizeType)
					}

					if asset.Video != nil && asset.Required == 1 {
						//sizeType := creative_entity.RT_SIZE_VERTICAL
						//if asset.Video.W > asset.Video.H {
						//	sizeType = creative_entity.RT_SIZE_HORIZONTAL
						//}
						request.SlotSize = append(request.SlotSize, ad_service.Size{
							Width:  int64(asset.Video.W),
							Height: int64(asset.Video.H),
						})
						sizeType := creative_entity.RT_SIZE_NULL
						key.Video().SetRequiredCount(1).SetRequiredSizeType(sizeType)
					}

					if asset.Title != nil && asset.Required == 1 {
						key.Title().SetRequiredCount(1)
					}

					if asset.Data != nil && asset.Required == 1 && asset.Data.Type == topon_broker_proto.DataAssetType_DESC {
						key.Desc().SetRequiredCount(1)
					}
				}
				request.AppendCreativeTemplateKey(key)
			}
		}
		break

	}

	return nil
}

func (k *TopOnTrafficBroker) mappingDeviceType(deviceType topon_broker_proto.DeviceType) entity.DeviceType {
	switch deviceType {
	case topon_broker_proto.DeviceType_MOBILE, topon_broker_proto.DeviceType_HIGHEND_PHONE:
		return entity.DeviceTypeMobile
	case topon_broker_proto.DeviceType_PERSONAL_COMPUTER:
		return entity.DeviceTypePc
	case topon_broker_proto.DeviceType_CONNECTED_TV:
		return entity.DeviceTypeOtt
	case topon_broker_proto.DeviceType_TABLET:
		return entity.DeviceTypePad
	}
	return entity.DeviceTypeUnknown
}

func (k *TopOnTrafficBroker) mappingOs(os string) entity.OsType {
	switch strings.ToLower(os) {
	case "ios":
		return entity.OsTypeIOS
	case "android":
		return entity.OsTypeAndroid
	case "windowsphone":
		return entity.OsTypeWindowsPhone
	case "windows":
		return entity.OsTypeWindows
	}
	return entity.OsTypeUnknown
}

func (k *TopOnTrafficBroker) mappingCarrier(carrier string) entity.OperatorType {
	switch carrier {
	case "46000", "46002", "46007":
		return entity.OperatorTypeChinaMobile
	case "46001", "46006":
		return entity.OperatorTypeChinaUnicom
	case "46003", "46005":
		return entity.OperatorTypeChinaTelecom
	case "46020":
		return entity.OperatorTypeTietong
	}
	return entity.OperatorTypeUnknown
}

func (k *TopOnTrafficBroker) mappingConnectionType(connectionType topon_broker_proto.ConnectionType) entity.ConnectionType {
	switch connectionType {
	case topon_broker_proto.ConnectionType_ETHERNET:
		return entity.ConnectionTypeNetEthernet
	case topon_broker_proto.ConnectionType_WIFI:
		return entity.ConnectionTypeWifi
	case topon_broker_proto.ConnectionType_CELL_UNKNOWN:
		return entity.ConnectionTypeCellular
	case topon_broker_proto.ConnectionType_CELL_2G:
		return entity.ConnectionType2G
	case topon_broker_proto.ConnectionType_CELL_3G:
		return entity.ConnectionType3G
	case topon_broker_proto.ConnectionType_CELL_4G:
		return entity.ConnectionType4G
	case 7:
		return entity.ConnectionType5G
	}
	return entity.ConnectionTypeUnknown
}

func (k *TopOnTrafficBroker) GenerateVastAdm(request *ad_service.AdRequest, candidate *ad_service.AdCandidate) string {
	genericAd := candidate.GetGenericAd()
	traffic := candidate.GetModifiedTrafficData()
	trackingGen := candidate.GetTrackingGen(traffic)
	videoVast := vast_utils.NewVast()

	var title, desc, video, img string
	duration := 0
	width := 0
	height := 0

	for _, material := range candidate.GetSelectedMaterialList() {
		switch material.MaterialType {
		case entity.MaterialTypeTitle:
			title = material.Data
		case entity.MaterialTypeDesc:
			desc = material.Data
		case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
			img = candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen)
		case entity.MaterialTypeIcon:
			//icon = material.Url
		case entity.MaterialTypeVideo:
			video = candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen)
			width = int(material.Width)
			height = int(material.Height)
			duration = int(material.Duration)
		}
	}

	vastInLine := videoVast.AppendAd("1", "block9", title, desc).Ads[0].InLine
	landing := candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen)

	impTracker := candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
	clickTrackers := candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen)

	vastInLine = vastInLine.AppendImpressions("", impTracker)
	linear := vastInLine.AppendCreative("1", 0, duration).Creatives.Creative[0].Linear
	linear.AppendMediaFile("", "video/mp4", 0, width, height, video).
		AppendVideoClicks("", landing).VideoClicks.
		AppendClickTrackings(clickTrackers)

	if len(genericAd.GetVideoStartUrlList()) > 0 {
		for _, vurl := range genericAd.GetVideoStartUrlList() {
			linear.AppendTrackingEvents("start", vurl)
		}
	}

	if len(genericAd.GetVideoCloseUrlList()) > 0 {
		for _, vurl := range genericAd.GetVideoCloseUrlList() {
			linear.AppendTrackingEvents("close", vurl)
		}
	}

	if len(img) > 0 {
		companionAds := vast_utils.CompanionAds{}
		companionAd := &vast_utils.CompanionAd{
			Width:  width,
			Height: height,
			StaticResource: &vast_utils.StaticResource{
				CreativeType: "image/jpg",
				URI:          img,
			},
		}

		companionAd.AppendClickThrough("", landing).AppendClickTrackings(clickTrackers)
		companionAds.CompanionAd = append(companionAds.CompanionAd, companionAd)
		imgCreative := &vast_utils.Creative{CompanionAds: companionAds}
		vastInLine.Creatives.Creative = append(vastInLine.Creatives.Creative, imgCreative)
	}

	return videoVast.XmlMarshal()
}

func (k *TopOnTrafficBroker) GenerateNativeAdm(request *ad_service.AdRequest, candidate *ad_service.AdCandidate) string {
	nativeassetMap := make(map[string][]*topon_broker_proto.NativeResponse_Asset)
	genericAd := candidate.GetGenericAd()
	traffic := candidate.GetModifiedTrafficData()
	trackingGen := candidate.GetTrackingGen(traffic)

	for _, material := range candidate.GetSelectedMaterialList() {
		switch material.MaterialType {
		case entity.MaterialTypeTitle:
			if nativeassetMap["title"] == nil {
				nativeassetMap["title"] = make([]*topon_broker_proto.NativeResponse_Asset, 0)
			}
			asset := &topon_broker_proto.NativeResponse_Asset{
				Title: &topon_broker_proto.NativeResponse_Asset_Title{
					Text: material.Data,
					Len:  int32(len(material.Data)),
				}}
			nativeassetMap["title"] = append(nativeassetMap["title"], asset)
		case entity.MaterialTypeDesc:
			if nativeassetMap["desc"] == nil {
				nativeassetMap["desc"] = make([]*topon_broker_proto.NativeResponse_Asset, 0)
			}
			asset := &topon_broker_proto.NativeResponse_Asset{
				Data: &topon_broker_proto.NativeResponse_Asset_Data{
					Type:  topon_broker_proto.DataAssetType_DESC,
					Value: material.Data,
					Len:   int32(len(material.Data)),
				}}
			nativeassetMap["desc"] = append(nativeassetMap["desc"], asset)
		case entity.MaterialTypeImage, entity.MaterialTypeCoverImage:
			if nativeassetMap["img"] == nil {
				nativeassetMap["img"] = make([]*topon_broker_proto.NativeResponse_Asset, 0)
			}
			asset := &topon_broker_proto.NativeResponse_Asset{
				Img: &topon_broker_proto.NativeResponse_Asset_Image{
					Type: topon_broker_proto.ImageAssetType_MAIN,
					Url:  candidate.ReplaceUrlMacro(material.Url, traffic, trackingGen),
					W:    material.Width,
					H:    material.Height,
				}}
			nativeassetMap["img"] = append(nativeassetMap["img"], asset)
		case entity.MaterialTypeIcon:
			if nativeassetMap["icon"] == nil {
				nativeassetMap["icon"] = make([]*topon_broker_proto.NativeResponse_Asset, 0)
			}
			asset := &topon_broker_proto.NativeResponse_Asset{
				Img: &topon_broker_proto.NativeResponse_Asset_Image{
					Type: topon_broker_proto.ImageAssetType_ICON,
					Url:  material.Url,
					W:    material.Width,
					H:    material.Height,
				}}
			nativeassetMap["icon"] = append(nativeassetMap["icon"], asset)
		case entity.MaterialTypeVideo:
			if nativeassetMap["video"] == nil {
				nativeassetMap["video"] = make([]*topon_broker_proto.NativeResponse_Asset, 0)
			}
			asset := &topon_broker_proto.NativeResponse_Asset{
				//todo vast
				Video: &topon_broker_proto.NativeResponse_Asset_Video{}}
			nativeassetMap["video"] = append(nativeassetMap["video"], asset)

		}

	}

	admNative := topon_broker_proto.NativeResponse{
		//Ver:           "",
		Assets: make([]*topon_broker_proto.NativeResponse_Asset, 0),
		//Assetsurl:     "",
		//Dcourl:        "",
		//Imptrackers:   nil,
		//Jstracker:     "",
		Eventtrackers: make([]*topon_broker_proto.NativeResponse_EventTracker, 0),
	}

	impTracker := candidate.ReplaceUrlMacroList(genericAd.GetImpressionMonitorList(), traffic, trackingGen)
	admNative.Imptrackers = append(admNative.Imptrackers, impTracker...)
	//for _, imp := range impTracker {
	//	impEventTracker := &topon_broker_proto.NativeResponse_EventTracker{
	//		Event:  topon_broker_proto.EventType_IMPRESSION,
	//		Method: topon_broker_proto.EventTrackingMethod_IMG,
	//		Url:    imp,
	//	}
	//	admNative.Eventtrackers = append(admNative.Eventtrackers, impEventTracker)
	//}

	admNative.Link = &topon_broker_proto.NativeResponse_Link{
		Url:           candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen),
		Clicktrackers: candidate.ReplaceUrlMacroList(genericAd.GetClickMonitorList(), traffic, trackingGen),
	}

	requestNativeData, ok := request.GetMediaExtraData(ReqNativeKey)
	if ok {
		if requestNative, ok1 := requestNativeData.(*topon_broker_proto.NativeRequest); ok1 && requestNative != nil {
			imgIndex := 0
			videoIndex := 0
			titleIndex := 0
			descIndex := 0
			for _, requestAsset := range requestNative.Assets {
				if requestAsset.Required == 0 {
					continue
				}
				asset := &topon_broker_proto.NativeResponse_Asset{}
				if requestAsset.Img != nil && len(nativeassetMap["img"]) > 0 {
					index := imgIndex % len(nativeassetMap["img"])
					asset = nativeassetMap["img"][index]
					asset.Id = requestAsset.Id
					imgIndex++
					admNative.Assets = append(admNative.Assets, asset)
				}

				if requestAsset.Title != nil && len(nativeassetMap["title"]) > 0 {
					index := titleIndex % len(nativeassetMap["title"])
					asset = nativeassetMap["title"][index]
					asset.Id = requestAsset.Id
					titleIndex++
					admNative.Assets = append(admNative.Assets, asset)
				}

				if requestAsset.Video != nil && len(nativeassetMap["video"]) > 0 {
					index := videoIndex % len(nativeassetMap["video"])
					asset = nativeassetMap["video"][index]
					asset.Id = requestAsset.Id
					videoIndex++
					admNative.Assets = append(admNative.Assets, asset)
				}

				if requestAsset.Data != nil && len(nativeassetMap["desc"]) > 0 {
					index := descIndex % len(nativeassetMap["desc"])
					asset = nativeassetMap["desc"][index]
					asset.Id = requestAsset.Id
					descIndex++
					admNative.Assets = append(admNative.Assets, asset)
				}
			}

		}
	}
	topOnNative := &topon_broker_proto.TopOnBidResponseNative{Native: admNative}
	adm, _ := json.Marshal(topOnNative)

	return string(adm)
}

func (k *TopOnTrafficBroker) SendResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	zap.L().Debug("TopOnTrafficBroker SendResponse, enter")

	if request.IsDebug {
		zap.L().Info("[TopOnTrafficBroker] adCode:%+v").GetErrCodeMap())
	}

	if request.Response.NoCandidate() {
		return k.SendFallbackResponse(request, writer)
	}

	response := &topon_broker_proto.BidResponse{}

	response.Id = request.GetRequestId()
	response.Bidid = request.GetRequestId()
	response.Seatbid = make([]*topon_broker_proto.BidResponse_SeatBid, 0)
	for _, candidate := range request.Response.GetAdCandidateList() {
		seatBid := &topon_broker_proto.BidResponse_SeatBid{}
		seatBid.Seat = "1"
		seatBid.Bid = make([]*topon_broker_proto.BidResponse_SeatBid_Bid, 0)
		response.Seatbid = append(response.Seatbid, seatBid)

		genericAd := candidate.GetGenericAd()
		creative := candidate.GetCreative()
		//zap.L().Info("[TopOnTrafficBroker] ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", candidate.GetDspResponseAd())))))
		traffic := candidate.GetModifiedTrafficData()
		trackingGen := candidate.GetTrackingGen(traffic)

		topBid := &topon_broker_proto.BidResponse_SeatBid_Bid{}
		topBid.Id = request.GetRequestId()
		topBid.Impid = request.ImpressionId
		if len(request.SlotSize) > 0 {
			topBid.W = int32(request.SlotSize[0].Width)
			topBid.H = int32(request.SlotSize[0].Height)
		}

		bidPrice := candidate.GetBidPrice()

		topBid.Price = float64(bidPrice.Price)
		topBid.Adid = strconv.Itoa(int(genericAd.GetAdId()))
		if creative != nil {
			if creative.GetCreativeId() != 0 {
				topBid.Cid = strconv.Itoa(int(creative.GetCreativeId()))
				topBid.Crid = strconv.Itoa(int(creative.GetCreativeId()))
			} else {
				topBid.Cid = creative.GetCreativeKey()
				topBid.Crid = creative.GetCreativeKey()
			}
		}

		topBid.Ext = &topon_broker_proto.BidResponse_SeatBid_Bid_Ext{}
		topBid.Ext.CommonCn = &topon_broker_proto.BidResponse_SeatBid_Bid_CommonCN{
			LinkType: 3,
		}

		if genericAd.GetAppInfo() != nil {
			topBid.Ext.CommonCn.AppDeveloper = genericAd.GetAppInfo().Develop
			topBid.Ext.CommonCn.AppPrivacy = genericAd.GetAppInfo().Privacy
			topBid.Ext.CommonCn.AppPermissions = genericAd.GetAppInfo().Permission
			topBid.Ext.CommonCn.AppName = genericAd.GetAppInfo().AppName
			topBid.Ext.CommonCn.AppDesc = genericAd.GetAppInfo().AppDesc
			topBid.Ext.CommonCn.AppVersion = genericAd.GetAppInfo().AppVersion

			if genericAd.GetAppInfo().WechatExt != nil {
				topBid.Ext.CommonCn.WxUsername = genericAd.GetAppInfo().WechatExt.ProgramId
				topBid.Ext.CommonCn.WxPath = genericAd.GetAppInfo().WechatExt.ProgramPath
			}
		}

		if len(genericAd.GetDownloadUrl()) > 0 {
			topBid.Ext.CommonCn.DownloadLink = candidate.ReplaceUrlMacro(genericAd.GetDownloadUrl(), traffic, trackingGen)
		}

		if genericAd.GetLandingAction() == entity.LandingTypeDownload {
			topBid.Ext.CommonCn.LinkType = 4
			if len(topBid.Ext.CommonCn.DownloadLink) == 0 {
				topBid.Ext.CommonCn.DownloadLink = candidate.ReplaceUrlMacro(genericAd.GetLandingUrl(), traffic, trackingGen)
			}
		}

		if len(genericAd.GetDeepLinkUrl()) > 0 {
			topBid.Ext.CommonCn.Deeplink = candidate.ReplaceUrlMacro(genericAd.GetDeepLinkUrl(), traffic, trackingGen)
		}

		if len(genericAd.GetDeepLinkMonitorList()) > 0 {
			topBid.Ext.CommonCn.DpStart = candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen)
			topBid.Ext.CommonCn.DpSucc = candidate.ReplaceUrlMacroList(genericAd.GetDeepLinkMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetDeepLinkUrl()) > 0 {
			topBid.Ext.CommonCn.DpSucc = append(topBid.Ext.CommonCn.DpSucc, candidate.ReplaceUrlMacro(genericAd.GetDpSuccess(), traffic, trackingGen))
		}

		if len(genericAd.GetAppDownloadStartedMonitorList()) > 0 {
			topBid.Ext.CommonCn.ApkDlStar = candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadStartedMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetAppDownloadFinishedMonitorList()) > 0 {
			topBid.Ext.CommonCn.ApkDlEnd = candidate.ReplaceUrlMacroList(genericAd.GetAppDownloadFinishedMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetAppInstallStartMonitorList()) > 0 {
			topBid.Ext.CommonCn.ApkStartInstall = candidate.ReplaceUrlMacroList(genericAd.GetAppInstallStartMonitorList(), traffic, trackingGen)
		}

		if len(genericAd.GetAppInstalledMonitorList()) > 0 {
			topBid.Ext.CommonCn.ApkInstall = candidate.ReplaceUrlMacroList(genericAd.GetAppInstalledMonitorList(), traffic, trackingGen)
		}
		if request.GetMediaExtraInt64WithDefault(videoRequest, 0) == 1 {
			topBid.Adm = k.GenerateVastAdm(request, candidate)
		} else {
			topBid.Adm = k.GenerateNativeAdm(request, candidate)
		}

		if genericAd.GetAdExtInfo() != nil {
			if genericAd.GetAdExtInfo().AdSource == "jd" {
				// 默认最大缓存时间1小时,针对京东预算下发30分钟
				topBid.Exp = 1800
			}
		}

		seatBid.Bid = append(seatBid.Bid, topBid)
		break
	}

	zap.L().Debug("TopOnTrafficBroker Build Response end. broker response:[]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response)))))

	//buffer := buffer_pool.NewBuffer()
	//defer buffer.Release()

	//buffer.EnsureSize(response.Size())
	//_, err := response.MarshalToSizedBuffer(buffer.Get())
	//if err != nil {
	//	zap.L().Error("[TopOnTrafficBroker] MarshalToSizedBuffer err", zap.Error(err))
	//	return err
	//}
	//
	//data := buffer.Get()
	//writer.SetHeader("Content-Type", "application/x-protobuf")

	data, err := json.Marshal(response)
	if err != nil {
		zap.L().Error("[TopOnTrafficBroker] json marshal err", zap.Error(err))
		return err
	}
	writer.SetHeader("Content-Type", "application/json;charset=utf-8")
	if request.RawHttpRequest.AcceptGzip() && buffer_pool.ShouldGzip(data) {
		writer.SetHeader("Content-Encoding", "gzip")
		gzipBuffer := buffer_pool.GzipEncodeBuffer(data)
		defer gzipBuffer.Release()

		if _, err := writer.WriteWithStatus(200, gzipBuffer.Get()); err != nil {
			return err
		}
	} else {
		if _, err := writer.WriteWithStatus(200, data); err != nil {
			return err
		}
	}

	//if _, err := writer.WriteWithStatus(200, data); err != nil {
	//	return err
	//}

	k.DoTrafficResponseSamplePb(request, response)

	if request.IsDebug {
		zap.L().Info("[TopOnTrafficBroker] SendResponse success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", response.String())))))
	}

	return nil
}

func (k *TopOnTrafficBroker) SendFallbackResponse(request *ad_service.AdRequest, writer ad_service.HttpResponse) error {
	zap.L().Debug("TopOnTrafficBroker SendFallbackResponse, Generate None Bid Response")

	if request.IsDebug {
		for adId, errCode := range request.Response.GetTotalAdCandidateList().GetErrCodeMap() {
			zap.L().Info("[TopOnTrafficBroker] adCode:, errCode:%s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(adId)))), zap.Int64("param2", int64(errCode)))
		}
	}

	writer.SetHeader("Content-Length", "0")
	writer.WriteWithStatus(204, nil)
	return nil
}
