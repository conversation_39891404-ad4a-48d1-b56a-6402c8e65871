package smart_price

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/pricing_strategy"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker"
	"gitlab.com/dev/heidegger/library/entity"
	"fmt"
)

type SmartPriceStrategy struct {
	ranker ranker.Ranker

	fallback pricing_strategy.PricingStrategy
}

func NewSmartPriceStrategy(
	ranker ranker.Ranker,
	fallback pricing_strategy.PricingStrategy) *SmartPriceStrategy {
	return &SmartPriceStrategy{
		ranker:   ranker,
		fallback: fallback,
	}
}

func (s *SmartPriceStrategy) Price(query *pricing_strategy.PricingQuery) (*pricing_strategy.PricingDecision, error) {
	fallbackResult, err := s.fallback.Price(query)
	if err != nil {
		return nil, err
	}

	rankerQuery := ranker.RankerRequest{
		MediaId:       query.GetMediaId(),
		MediaSlotId:   query.GetMediaSlotId(),
		MediaSlotType: query.GetMediaSlotType(),
		DspId:         query.GetDspId(),
		CpmPrice:      uint32(query.GetAdBidPrice().Price),
		BidFloor:      uint32(query.GetBidFloor().Price),
	}

	profitRate := query.GetProfitRate()
	rankerQuery.MinProfitRate = profitRate.PriceRate
	rankerQuery.MaxProfitRate = profitRate.PriceRate

	if profitRate.HasRateBounds() {
		rankerQuery.MinProfitRate = profitRate.PriceRateLowerBound
		rankerQuery.MaxProfitRate = profitRate.PriceRateUpperBound
	}

	rankerResult := ranker.RankerResult{}
	if err := s.ranker.Predict(rankerQuery, &rankerResult); err != nil {
		return nil, err
	}

	decision := pricing_strategy.PricingDecision{
		ChargePrice:  fallbackResult.ChargePrice,
		BidPrice:     entity.CreatePriceItem(rankerResult.SuggestedBidPrice, entity.BidTypeCpm),
		EstWinRate:   rankerResult.EWinRate,
		EstCost:      rankerResult.ECostPrice,
		EstClickRate: rankerResult.ECtr,
		EstProfit:    entity.CreateProfitItem(rankerResult.EProfit, entity.BidTypeCpm),
	}

	zap.L().Info("SmartPriceStrategy", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", decision)))))

	if decision.EstCost != 0 {
		decision.EstProfit = decision.ChargePrice.Sub(entity.CreatePriceItem(decision.EstCost, entity.BidTypeCpm))
	} else {
		decision.EstProfit = decision.ChargePrice.Sub(decision.BidPrice)
	}

	return &decision, nil
}
