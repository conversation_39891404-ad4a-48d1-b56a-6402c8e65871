package ad_service

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/mclock"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"fmt"
)

type AdWorkflowTaskInterface interface {
	Start() error
	Stop()

	Do(request *AdRequest) error
	GetTaskName() string
}

type AdWorkflow struct {
	task     []AdWorkflowTaskInterface
	fallback []AdWorkflowTaskInterface

	useMetrics bool
}

func NewAdWorkflow() *AdWorkflow {
	return &AdWorkflow{
		task:     make([]AdWorkflowTaskInterface, 0),
		fallback: make([]AdWorkflowTaskInterface, 0),

		useMetrics: false,
	}
}

func (wf *AdWorkflow) SetUseMetrics(useMetrics bool) {
	wf.useMetrics = useMetrics
}

func (wf *AdWorkflow) AddTask(task AdWorkflowTaskInterface) {
	if wf.useMetrics {
		task = NewMetricsWorkflowTask(task)
	}

	wf.task = append(wf.task, task)
}

func (wf *AdWorkflow) AddFallback(task AdWorkflowTaskInterface) {
	if wf.useMetrics {
		task = NewMetricsWorkflowTask(task)
	}

	wf.fallback = append(wf.fallback, task)
}

func (wf *AdWorkflow) GetTaskName() string {
	return "AdWorkflow"
}

func (wf *AdWorkflow) Start() error {
	for _, task := range wf.task {
		if err := task.Start(); err != nil {
			zap.L().Error("[AdWorkflow] error in task:, err:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", task.GetTaskName())))), err)
			return err
		}
	}
	return nil
}

func (wf *AdWorkflow) Stop() {
	for _, task := range wf.task {
		task.Stop()
	}
}

func (wf *AdWorkflow) Do(request *AdRequest) error {
	for _, task := range wf.task {
		if err := task.Do(request); err != nil {
			request.Response.SetError(err)
			break
		}

		if request.Response.GetError() != nil {
			break
		}
	}

	for _, task := range wf.fallback {
		if err := task.Do(request); err != nil {
			zap.L().Debug("[AdWorkflow] error in fallback task:, err:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", task.GetTaskName())))), err)
		}
	}

	return nil
}

type MetricsWorkflowTask struct {
	inner     AdWorkflowTaskInterface
	histogram *prometheus_helper.SimpleHistogram
}

func NewMetricsWorkflowTask(inner AdWorkflowTaskInterface) *MetricsWorkflowTask {
	return &MetricsWorkflowTask{
		inner: inner,
		histogram: prometheus_helper.RegisterSimpleHistogramWithLabel(
			"AdService_Workflow",
			[]string{"workflow"},
			[]string{inner.GetTaskName()},
		),
	}
}

func (t *MetricsWorkflowTask) GetTaskName() string {
	return t.inner.GetTaskName()
}

func (t *MetricsWorkflowTask) Start() error {
	return t.inner.Start()
}

func (t *MetricsWorkflowTask) Stop() {
	t.inner.Stop()
}

func (t *MetricsWorkflowTask) Do(request *AdRequest) error {
	start := mclock.Now()
	err := t.inner.Do(request)
	elapsed := mclock.Now() - start
	second := float64(elapsed) / 1e9

	t.histogram.Observe(second)

	return err
}
