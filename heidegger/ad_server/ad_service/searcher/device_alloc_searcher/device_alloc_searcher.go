package device_alloc_searcher

import (
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/ad_server/ad_service/traffic_modifier/traffic_device_allocator"
	"fmt"
)

type DeviceAllocSearcher struct {
	deviceAllocManager traffic_device_allocator.DeviceAllocManager
}

func NewDeviceAllocSearcher(deviceAllocManager traffic_device_allocator.DeviceAllocManager) *DeviceAllocSearcher {
	return &DeviceAllocSearcher{
		deviceAllocManager: deviceAllocManager,
	}
}

func (s *DeviceAllocSearcher) GetTaskName() string {
	return "DeviceAllocSearcher"
}

func (s *DeviceAllocSearcher) Start() error {
	return nil
}

func (s *DeviceAllocSearcher) Stop() {

}

func (s *DeviceAllocSearcher) Do(request *ad_service.AdRequest) error {
	deviceAllocationData := make([]traffic_device_allocator.DeviceAllocExtractInterface, 0)
	for _, candidate := range request.Response.GetAdCandidateList() {
		setting := candidate.GetAd().GetDeviceAllocationSetting()
		if setting == nil {
			continue
		}

		deviceAllocationData = append(deviceAllocationData, candidate.GetGroundTruthTrafficData())
	}

	deviceAllocMap, deviceAllocRrr := s.deviceAllocManager.AllocDeviceBatch(deviceAllocationData)

	//data, _ := json.Marshal(deviceAllocMap)
	//zap.L().Info("deviceAllocMap", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(data))))))

	for _, candidate := range request.Response.GetAdCandidateList() {
		setting := candidate.GetAd().GetDeviceAllocationSetting()
		if setting == nil {
			request.Response.PushCandidates(candidate)
			continue
		}

		if deviceAllocRrr != nil {
			candidate.FilterByError(err_code.ErrDeviceAllocationFail.Wrap(deviceAllocRrr))
			continue
		}

		deviceAlloc := deviceAllocMap[candidate.GetAd().GetAdId()]
		if deviceAlloc == nil && setting.IsMust() {
			candidate.FilterByError(err_code.ErrDeviceAllocationNoResponse)
			continue
		}

		if deviceAlloc.Code != 0 && setting.IsMust() {
			candidate.FilterByError(err_code.ErrDeviceAllocationNoDevice)
			continue
		}

		//zap.L().Info("[DeviceAllocSearcher] ad_id: , device_alloc: %v", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(candidate.GetAd())))).GetAdId(), deviceAlloc)

		if deviceAlloc != nil {
			candidate.SetDeviceAlloc(deviceAlloc)
		}

		request.Response.PushCandidates(candidate)
	}

	request.Response.SwapAndClearCandidate()

	if request.Response.NoCandidate() {
		request.Response.SetError(err_code.ErrDeviceAllocationFail)
	}

	return nil
}
