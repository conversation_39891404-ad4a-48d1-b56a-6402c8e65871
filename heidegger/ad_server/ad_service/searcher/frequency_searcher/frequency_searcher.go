package frequency_searcher

import (
	"context"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/frequency/frequency_service"
	"gitlab.com/dev/heidegger/library/entity"
	"time"
	"fmt"
)

type FrequencySearcher struct {
	freqManager frequency_service.FrequencyManagerInterface
}

func NewFrequencySearcher(freqManager frequency_service.FrequencyManagerInterface) *FrequencySearcher {
	return &FrequencySearcher{
		freqManager: freqManager,
	}
}

func (s *FrequencySearcher) GetTaskName() string {
	return "FrequencySearcher"
}

func (s *FrequencySearcher) Start() error {
	return nil
}

func (s *FrequencySearcher) Stop() {

}

func (s *FrequencySearcher) Do(request *ad_service.AdRequest) error {
	freqQuery := s.buildFrequencyQuery(request, request.Response.GetAdCandidateList())
	if freqQuery.IsEmpty() {
		return nil
	}

	ctx, _ := context.WithTimeout(context.TODO(), time.Millisecond*5)

	frequencyResult, frequencyResultErr := s.freqManager.Query(ctx, freqQuery)

	if request.IsDebug {
		zap.L().Info("[FrequencySearcher] freqResult", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", frequencyResult)))))
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		freqControl := candidate.GetAd().GetFrequencyControl()
		if freqControl == nil {
			request.Response.PushCandidates(candidate)
			continue
		}

		if frequencyResultErr != nil {
			candidate.FilterByError(err_code.ErrFrequencyFail.Wrap(frequencyResultErr))
			continue
		}

		if freqControl.HasDeviceIdControl() {
			trafficData := candidate.GetModifiedTrafficData()
			freqKey, _ := trafficData.GetDeviceIdWithType()

			freqResult, ok := frequencyResult.GetResult(freqKey, uint32(freqControl.GetId()))
			if !ok {
				candidate.FilterByError(err_code.ErrFrequencyFail)
			} else {
				candidate.SetFrequencyResult(freqResult)

				if freqResult.BlockingCode != frequency_service.BlockingCodeSuccess {
					switch freqResult.BlockingCode {
					case frequency_service.BlockingCodeImpressionLimit:
						candidate.FilterByError(err_code.ErrFrequencyImpressionCap)
					case frequency_service.BlockingCodeClickLimit:
						candidate.FilterByError(err_code.ErrFrequencyClickCap)
					case frequency_service.BlockingCodeBroadcastLimit:
						candidate.FilterByError(err_code.ErrFrequencyBroadcastCap)
					case frequency_service.BlockingCodeBidLimit:
						candidate.FilterByError(err_code.ErrFrequencyBidCap)
					default:
						candidate.FilterByError(err_code.ErrFrequencyCapUnknown)
					}
				}
			}
		}

		if freqControl.HasIpControl() {
			freqKey := request.Device.GetRequestIp()

			freqResult, ok := frequencyResult.GetResult(freqKey, uint32(freqControl.GetId()))
			if !ok {
				candidate.FilterByError(err_code.ErrFrequencyFail)
			} else {
				candidate.SetFrequencyResult(freqResult)

				if freqResult.BlockingCode != frequency_service.BlockingCodeSuccess {
					switch freqResult.BlockingCode {
					case frequency_service.BlockingCodeImpressionLimit:
						candidate.FilterByError(err_code.ErrFrequencyImpressionIpCap)
					case frequency_service.BlockingCodeClickLimit:
						candidate.FilterByError(err_code.ErrFrequencyClickIpCap)
					case frequency_service.BlockingCodeBroadcastLimit:
						candidate.FilterByError(err_code.ErrFrequencyBroadcastIpCap)
					case frequency_service.BlockingCodeBidLimit:
						candidate.FilterByError(err_code.ErrFrequencyBidIpCap)
					default:
						candidate.FilterByError(err_code.ErrFrequencyCapIpUnknown)
					}
				}
			}
		}

		if !candidate.IsFiltered() {
			request.Response.PushCandidates(candidate)
		}
	}

	request.Response.SwapAndClearCandidate()

	if request.Response.NoCandidate() {
		request.Response.SetError(err_code.ErrFrequencyCap)
	}

	return nil
}

func (s *FrequencySearcher) buildFrequencyQuery(request *ad_service.AdRequest, adCandidateList ad_service.AdCandidateList) frequency_service.FrequencyBatchQuery {
	var ipQueryList []uint32
	deviceQueryMap := make(map[string][]uint32)

	for _, candidate := range adCandidateList {
		freqControl := candidate.GetAd().GetFrequencyControl()
		if freqControl == nil {
			continue
		}

		trafficData := candidate.GetModifiedTrafficData()
		if freqControl.HasDeviceIdControl() {
			deviceId, _ := trafficData.GetDeviceIdWithType()
			deviceQueryMap[deviceId] = append(deviceQueryMap[deviceId], uint32(freqControl.GetId()))
		}

		if freqControl.HasIpControl() {
			ipQueryList = append(ipQueryList, uint32(freqControl.GetId()))
		}
	}

	result := frequency_service.FrequencyBatchQuery{
		Items: make([]frequency_service.FrequencyQueryItem, 0),
	}

	for deviceId, freqControlIdList := range deviceQueryMap {
		result.Items = append(result.Items, frequency_service.FrequencyQueryItem{
			Key:                    deviceId,
			KeyType:                uint32(entity.FrequencyKeyTypeDeviceId),
			FrequencyControlIdList: freqControlIdList,
		})
	}

	if len(ipQueryList) > 0 {
		result.Items = append(result.Items, frequency_service.FrequencyQueryItem{
			Key:                    request.Device.GetRequestIp(),
			KeyType:                uint32(entity.FrequencyKeyTypeIP),
			FrequencyControlIdList: ipQueryList,
		})
	}

	return result
}
