package frequency_searcher

import (
	"context"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/frequency/frequency_service"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/frequency_control_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
	"time"
	"fmt"
)

type FrequencyControlTrackingReporter struct {
	freqManager            frequency_service.FrequencyManagerInterface
	frequencyControlLoader frequency_control_loader.FrequencyControlLoader
	controlType            entity.FrequencyControlType
}

func NewFrequencyControlReporter(
	freqManager frequency_service.FrequencyManagerInterface,
	frequencyControlLoader frequency_control_loader.FrequencyControlLoader,
	controlType entity.FrequencyControlType) *FrequencyControlTrackingReporter {
	return &FrequencyControlTrackingReporter{
		freqManager:            freqManager,
		frequencyControlLoader: frequencyControlLoader,
		controlType:            controlType,
	}
}

func (p *FrequencyControlTrackingReporter) Start() error {
	return nil
}

func (p *FrequencyControlTrackingReporter) Stop() {

}

func (p *FrequencyControlTrackingReporter) GetName() string {
	return "FrequencyControlTrackingReporter"
}

func (p *FrequencyControlTrackingReporter) Do(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		return err
	}

	if extData.GetFrequencyControlId() == 0 {
		return nil
	}

	freqControl := p.frequencyControlLoader.GetFrequencyControlById(utils.ID(extData.GetFrequencyControlId()))
	if freqControl == nil {
		return nil
	}

	incrRequest := frequency_service.FrequencyBatchIncr{}

	if freqControl.HasDeviceIdControl() && freqControl.HasControlType(p.controlType) {
		deviceId := extData.DeviceId
		incrRequest.Items = append(incrRequest.Items, frequency_service.FrequencyIncrItem{
			FreqKey:         deviceId,
			FreqKeyType:     uint32(entity.FrequencyKeyTypeDeviceId),
			FreqControlId:   uint32(extData.GetFrequencyControlId()),
			FreqControlType: uint32(p.controlType),
		})
	}

	if freqControl.HasIpControl() && freqControl.HasControlType(p.controlType) && len(extData.RequestIp) != 0 {
		ip := extData.RequestIp
		incrRequest.Items = append(incrRequest.Items, frequency_service.FrequencyIncrItem{
			FreqKey:         ip,
			FreqKeyType:     uint32(entity.FrequencyKeyTypeIP),
			FreqControlId:   uint32(extData.GetFrequencyControlId()),
			FreqControlType: uint32(p.controlType),
		})
	}

	//zap.L().Info("FrequencyControlTrackingReporter controlType:, incrRequest", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", p.controlType)))), zap.String("param2", fmt.Sprintf("%v", incrRequest)))

	ctx, _ := context.WithTimeout(context.TODO(), time.Millisecond*5)
	if err := p.freqManager.Incr(ctx, incrRequest); err != nil {
		zap.L().Error("FrequencyControlTrackingReporter failed. err", zap.Error(err))
	}

	return nil
}
