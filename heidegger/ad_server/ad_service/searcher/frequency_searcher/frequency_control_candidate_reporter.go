package frequency_searcher

import (
	"context"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/frequency/frequency_service"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/frequency_control_loader"
	"fmt"
)

type FrequencyControlCandidateReporter struct {
	freqManager            frequency_service.FrequencyManagerInterface
	frequencyControlLoader frequency_control_loader.FrequencyControlLoader
	controlType            entity.FrequencyControlType
}

func NewFrequencyControlCandidateReporter(
	freqManager frequency_service.FrequencyManagerInterface,
	frequencyControlLoader frequency_control_loader.FrequencyControlLoader,
	controlType entity.FrequencyControlType) *FrequencyControlCandidateReporter {
	return &FrequencyControlCandidateReporter{
		freqManager:            freqManager,
		frequencyControlLoader: frequencyControlLoader,
		controlType:            controlType,
	}
}

func (p *FrequencyControlCandidateReporter) GetTaskName() string {
	return "FrequencyControlCandidateReporter"
}

func (p *FrequencyControlCandidateReporter) Start() error {
	return nil
}

func (p *FrequencyControlCandidateReporter) Stop() {

}

func (s *FrequencyControlCandidateReporter) Do(request *ad_service.AdRequest) error {
	candidateList := s.BuildCandidateList(request)

	if len(candidateList) == 0 {
		return nil
	}

	if err := s.do(request, candidateList); err != nil {
		zap.L().Debug("[FrequencyControlCandidateReporter] do error", zap.Error(err))
	}

	return nil
}

func (s *FrequencyControlCandidateReporter) BuildCandidateList(request *ad_service.AdRequest) ad_service.AdCandidateList {
	var candidateList ad_service.AdCandidateList

	if s.controlType == entity.FrequencyControlTypeBroadcast {
		for _, candidate := range request.Response.GetTotalAdCandidateList() {
			if candidate.GetIsDspRequested() {
				candidateList = append(candidateList, candidate)
			}
		}
	} else {
		candidateList = request.Response.GetAdCandidateList()
	}

	return candidateList
}

func (s *FrequencyControlCandidateReporter) do(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) error {
	incrRequest := frequency_service.FrequencyBatchIncr{}

	for _, candidate := range candidateList {
		freqControl := candidate.GetAd().GetFrequencyControl()
		if freqControl == nil {
			continue
		}

		if freqControl.HasDeviceIdControl() && freqControl.HasControlType(s.controlType) {
			trafficData := candidate.GetModifiedTrafficData()
			freqKey, _ := trafficData.GetDeviceIdWithType()

			incrRequest.Items = append(incrRequest.Items, frequency_service.FrequencyIncrItem{
				FreqKey:         freqKey,
				FreqKeyType:     uint32(entity.FrequencyKeyTypeDeviceId),
				FreqControlId:   uint32(freqControl.GetId()),
				FreqControlType: uint32(s.controlType),
			})
		}

		if freqControl.HasIpControl() && freqControl.HasControlType(s.controlType) {
			freqKey := request.Device.GetRequestIp()

			incrRequest.Items = append(incrRequest.Items, frequency_service.FrequencyIncrItem{
				FreqKey:         freqKey,
				FreqKeyType:     uint32(entity.FrequencyKeyTypeIP),
				FreqControlId:   uint32(freqControl.GetId()),
				FreqControlType: uint32(s.controlType),
			})
		}
	}

	if len(incrRequest.Items) == 0 {
		return nil
	}

	if request.IsDebug {
		zap.L().Info("[FrequencyControlCandidateReporter] controlType:, candidateList", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", s.controlType)))), zap.String("param2", fmt.Sprintf("%v", incrRequest)))
	}

	return s.freqManager.Incr(context.TODO(), incrRequest)
}
