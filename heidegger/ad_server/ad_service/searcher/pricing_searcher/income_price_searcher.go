package pricing_searcher

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/ranking_searcher/ranker/st_cpa_ranker/cpa_task_info_provider"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils/math_utils"
)

type IncomePricingSearcher struct {
	cpaTaskInfoProvider cpa_task_info_provider.CpaTaskInfoProvider
}

func NewIncomePricingSearcher(qihangTaskInfoProvider cpa_task_info_provider.CpaTaskInfoProvider) *IncomePricingSearcher {
	return &IncomePricingSearcher{
		cpaTaskInfoProvider: qihangTaskInfoProvider,
	}
}

func (s *IncomePricingSearcher) GetTaskName() string {
	return "IncomePricingSearcher"
}

func (s *IncomePricingSearcher) Start() error {
	return nil
}

func (s *IncomePricingSearcher) Stop() {

}

func (s *IncomePricingSearcher) Do(request *ad_service.AdRequest) error {
	for _, candidate := range request.Response.GetAdCandidateList() {
		if candidate.GetAd().GetAdType() == entity.AdTypeDsp {
			candidate.SetSuggestedIncomePrice(candidate.GetDspBidPrice())
		} else {
			priceStrategy := candidate.GetPricingStrategy()
			if priceStrategy == nil || !priceStrategy.IsValid() {
				candidate.FilterByError(err_code.ErrPricingStrategyInvalid)
				continue
			}

			candidate.SetDspBidPrice(priceStrategy.GetBidPrice())
			candidate.SetSuggestedIncomePrice(priceStrategy.GetBidPrice())
		}

		if len(candidate.GetAd().GetRankingModel()) != 0 {
			if candidate.GetDspProtocol() == entity.DspProtoTypeQihangCPA || (candidate.GetAd().GetBudgetPlatform() != nil && candidate.GetAd().GetBudgetPlatform().Key == entity.BudgetPlatformSurge) {
				taskId := candidate.GetGenericAd().GetDspTaskId()
				taskInfo := s.cpaTaskInfoProvider.GetTaskInfo(candidate.GetAd().GetDspId(), entity.DspProtoTypeQihangCPA, taskId)

				if taskInfo != nil {
					candidate.SetSuggestedIncomePrice(entity.CreatePriceItem(taskInfo.CpaPrice, entity.BidTypeCpa))
				}
			}
		}

		if price := candidate.GetSuggestedIncomePrice(); price.Type == entity.BidTypeCpm {
			rankerResult := candidate.GetRankerResult()
			rankerResult.ECpm = math_utils.MustConvertUint32(price.Price)
		}

		if request.IsDebug {
			suggestedIncomePrice := candidate.GetSuggestedIncomePrice()
			zap.L().Info("[IncomePricingSearcher] AdId: , SuggestedIncomePrice: %s, rankerEcpm:%d", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(candidate.GetAd())))).GetAdId(),
				suggestedIncomePrice.String(),
				candidate.GetRankerResult().ECpm,
			)
		}

		request.Response.PushCandidates(candidate)
	}

	request.Response.SwapAndClearCandidate()

	if request.Response.NoCandidate() {
		request.Response.SetError(err_code.ErrRankingSuggestedIncomePrice)
	}

	return nil
}
