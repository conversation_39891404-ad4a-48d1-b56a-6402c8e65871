package pricing_searcher

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/ad_server/ad_service/pricing_strategy"
	"gitlab.com/dev/heidegger/ad_server/ad_service/pricing_strategy/pricing_strategy_manager"
	"gitlab.com/dev/heidegger/ad_server/ranking_service"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"fmt"
)

const (
	MinCpcBidPrice = 0
	MinBidPrice    = 0
	MaxBidPrice    = 100 * 1000
	MaxCpcBidPrice = 100 * 1000
	MaxChargePrice = 100 * 1000
	MinProfit      = -10000
)

type PricingSearcher struct {
	rankingManager  *ranking_service.RankingManager
	pricingStrategy pricing_strategy.PricingStrategy
}

func NewPricingSearcher(rankingManager *ranking_service.RankingManager) *PricingSearcher {
	return &PricingSearcher{
		rankingManager:  rankingManager,
		pricingStrategy: pricing_strategy_manager.CreatePricingStrategyManager(rankingManager),
	}
}

func (s *PricingSearcher) GetTaskName() string {
	return "PricingSearcher"
}

func (s *PricingSearcher) Start() error {
	return nil
}

func (s *PricingSearcher) Stop() {

}

func (s *PricingSearcher) Do(request *ad_service.AdRequest) error {
	input := pricing_strategy.PricingQuery{
		MediaId:     request.GetMediaId(),
		MediaSlotId: request.GetMediaSlotId(),
		OsType:      request.Device.GetOsType(),
		DeviceType:  request.Device.GetDeviceType(),
	}

	for _, candidate := range request.Response.GetAdCandidateList() {

		if candidate.GetAd().GetAdType() == entity.AdTypeAttribution {
			request.Response.PushCandidates(candidate)
			continue
		}

		pricingStrategy := candidate.GetPricingStrategy()
		if !pricingStrategy.IsValid() {
			candidate.FilterByError(err_code.ErrPricingStrategyInvalid)
			continue
		}

		biddingStrategyType := pricingStrategy.GetBiddingStrategyType()
		chargeStrategyType := pricingStrategy.GetChargeStrategyType()
		input.ProfitRate = pricingStrategy.GetProfitRatio()

		if pricingStrategy.GetIndexProfitRate() != nil {
			switch pricingStrategy.GetIndexProfitRate().GetName() {
			case "app_bundle_id":
				input.ProfitRate = pricingStrategy.GetProfitRatioWithIndex(request.App.GetAppBundle(), 0)
			case "task_id":
				if candidate.GetDspProtocol() == entity.DspProtoTypeQihang || candidate.GetDspProtocol() == entity.DspProtoTypeQihangCPA {
					input.ProfitRate = pricingStrategy.GetProfitRatioWithIndex(candidate.GetAd().GetDspTaskId(), 0)
				}
			case "user_score":
				if candidate.GetDspProtocol() == entity.DspProtoTypeQihang || candidate.GetDspProtocol() == entity.DspProtoTypeQihangCPA {
					input.ProfitRate = pricingStrategy.GetProfitRatioWithIndex(candidate.GetAd().GetDspTaskId(), float64(candidate.GetUserScore()))
				}
			case "dsp_price":
				input.ProfitRate = pricingStrategy.GetProfitRatioWithIndex(utils.EmptyString, float64(candidate.GetDspBidPrice().Price))
				//if rand.Uint32()%100 == 0 {
				//	zap.L().Info("PricingSearcher: ratio:%s, price:, range:%s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(input.ProfitRate)))), zap.Int64("id", int64(candidate.GetDspBidPrice())).Price, pricingStrategy.GetIndexProfitRate())
				//}
			}
		}
		input.UnderBidFloorProfitRatio = pricingStrategy.GetUnderBidFloorProfitRatio()

		input.AdBidPrice = candidate.GetDspBidPrice()
		if !candidate.GetRankerResult().IsShadowed() {
			input.AdBidPrice = entity.CreatePriceItem(candidate.GetRankerResult().GetECpm(), entity.BidTypeCpm)
		}

		if input.AdBidPrice.Type != entity.BidTypeCpm {
			candidate.FilterByError(err_code.ErrPricingBidTypeInvalid)
			continue
		}

		input.AdId = candidate.GetAd().GetAdId()
		input.BiddingStrategyType = biddingStrategyType
		input.ChargeStrategyType = chargeStrategyType
		input.StaticBidPrice = pricingStrategy.GetBidPrice()
		input.StaticChargePrice = pricingStrategy.GetChargePrice()
		input.ChargePriceRatio = pricingStrategy.GetChargePriceRatio()
		input.BidFloor = candidate.GetBidFloor()
		input.CpcBidFloor = candidate.GetCpcBidFloor()

		if input.ChargePriceRatio.PriceRate == 0 {
			input.ChargePriceRatio.PriceRate = 0.95
		}

		decision, err := s.pricingStrategy.Price(&input)
		if err != nil {
			candidate.FilterByError(err_code.ErrPricingStrategyFail.Wrap(err))
			continue
		}

		candidate.SetBidPrice(decision.BidPrice)
		candidate.SetChargePrice(decision.ChargePrice)

		bidPrice := candidate.GetBidPrice()
		chargePrice := candidate.GetChargePrice()

		//if candidate.GetAd().AdId == 50002 && rand.Uint32()%100 == 0 {
		//	zap.L().Info("[PricingSearcher] [Pricing] pricingStrategy:, bundle:, input:%s, charge:%s, bid:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", //		pricingStrategy)))), zap.String("param2", fmt.Sprintf("%v", request.App.GetAppBundle())), input.String(), chargePrice.Price, bidPrice.Price)
		//}

		if request.IsDebug {
			zap.L().Info("[PricingSearcher] [Pricing] adId:, mediaSlotId:%d, input:%s, bidPrice:%s, chargePrice:%s, bidFloor:%s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(candidate.GetAd())))).GetAdId(),
				candidate.GetAdRequest().GetMediaSlotId(),
				input.String(),
				bidPrice.String(),
				chargePrice.String(),
				input.BidFloor.String())
		}

		//if candidate.GetAd().GetAdId() == 642 {
		//	dspBidPrice, _ := candidate.GetDspBidPriceAndType()
		//	zap.L().Info("[PricingSearcher] [Pricing] adId:, mediaSlotId:%d, input:%s, rank:%s, dspBidPrice:%d, bidPrice:%d, chargePrice:%d", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(//		candidate.GetAd())))).GetAdId(),
		//		candidate.GetAdRequest().GetMediaSlotId(),
		//		input,
		//		candidate.GetRankerResult(),
		//		dspBidPrice,
		//		bidPrice,
		//		chargePrice,
		//	)
		//}

		if candidate.GetAd().GetAdType() == entity.AdTypeDsp &&
			candidate.GetPricingStrategy().BidFloorStrategyType == entity.BidFloorStrategyTypeStaticValue &&
			pricingStrategy.GetBiddingStrategyType() != entity.BiddingStrategyTypeStaticPrice {
			dspBidPrice := candidate.GetDspBidPrice().Price
			bidFloor := candidate.GetBidFloor().Price
			if dspBidPrice < bidFloor {
				candidate.FilterByError(err_code.ErrDspPricingLowerThanFloor)
				continue
			}
		}
		if bidPrice.Type == entity.BidTypeCpm {
			if candidate.GetAd().GetAdType() != entity.AdTypeAttribution && bidPrice.Price <= MinBidPrice {
				candidate.FilterByError(err_code.ErrBiddingPricingTooLow)
				continue
			}

			if bidPrice.Price < input.BidFloor.Price {
				candidate.FilterByError(err_code.ErrPricingLowerThanFloor)
				continue
			}

			if bidPrice.Price > MaxBidPrice {
				candidate.FilterByError(err_code.ErrBiddingPricingTooHigh)
				continue
			}
		} else if bidPrice.Type == entity.BidTypeCpc {
			if candidate.GetAd().GetAdType() != entity.AdTypeAttribution && bidPrice.Price <= MinCpcBidPrice {
				candidate.FilterByError(err_code.ErrBiddingPricingTooLow)
				continue
			}
			if bidPrice.Price < input.CpcBidFloor.Price {
				candidate.FilterByError(err_code.ErrPricingLowerThanFloor)
				continue
			}

			if bidPrice.Price > MaxCpcBidPrice {
				candidate.FilterByError(err_code.ErrBiddingPricingTooHigh)
				continue
			}
		}

		if chargePrice.Price > MaxChargePrice {
			candidate.FilterByError(err_code.ErrChargePricingTooHigh)
			continue
		}

		if chargePrice.Price < 0 {
			candidate.FilterByError(err_code.ErrChargePricingTooLow)
			continue
		}

		//if bidPrice > chargePrice {
		//	candidate.FilterByError(err_code.ErrProfitTooLow)
		//	continue
		//}

		if profit := chargePrice.Price - bidPrice.Price; profit < MinProfit {
			candidate.FilterByError(err_code.ErrProfitTooLow)
			continue
		}

		//zap.L().Info("[PricingSearcher] [Pricing] adId:, mediaSlotId:%d, input:%ss, bidPrice:%d, chargePrice:%d, bidFloor:%d, human:%s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(//	candidate.GetAd())))).GetAdId(),
		//	candidate.GetAdRequest().GetMediaSlotId(),
		//	input,
		//	bidPrice,
		//	chargePrice,
		//	input.BidFloor,
		//	candidate.GetErrorCode().Human())

		request.Response.PushCandidates(candidate)
	}

	request.Response.SwapAndClearCandidate()

	if request.Response.NoCandidate() {
		request.Response.SetError(err_code.ErrPricingStrategyFail)
	}

	return nil
}
