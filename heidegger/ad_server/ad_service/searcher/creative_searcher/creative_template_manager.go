package creative_searcher

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/bitmap_index_v2"
	"gitlab.com/dev/heidegger/library/echo_helper"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/entity_loader/creative_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"sync"
	"time"
)

const (
	FieldIdMediaId = bitmap_index_v2.FieldId(iota)
	FieldIdMediaSlotId
	FieldIdSourceSlotId
	FieldIdOsType
	FieldIdDeviceType
	FieldIdSlotType
	FieldIdSize
	FieldIdScreenOrientationType
	FieldIdCreativeTemplateKey
	FieldIdMediaTemplate
)

type CreativeTemplateManager struct {
	indexManager           *bitmap_index_v2.IndexManager
	creativeTemplateLoader creative_loader.CreativeTemplateLoader
	creativeLoader         creative_loader.CreativeLoader

	mediaSpecificCreativeTemplateCache map[int64]*creative_entity.CreativeTemplate
	lock                               sync.RWMutex

	term chan struct{}
}

func NewCreativeTemplateManager(
	creativeTemplateLoader creative_loader.CreativeTemplateLoader,
	creativeLoader creative_loader.CreativeLoader,
) *CreativeTemplateManager {
	return &CreativeTemplateManager{
		creativeTemplateLoader: creativeTemplateLoader,
		creativeLoader:         creativeLoader,

		mediaSpecificCreativeTemplateCache: make(map[int64]*creative_entity.CreativeTemplate),

		term: make(chan struct{}),
	}
}

func (manager *CreativeTemplateManager) Start() error {
	if err := manager.buildIndex(); err != nil {
		return err
	}

	go manager.loop()

	return nil
}

func (manager *CreativeTemplateManager) Stop() {
	close(manager.term)
}

func (manager *CreativeTemplateManager) loop() {
	ticker := time.NewTicker(time.Minute)

	for {
		select {
		case <-manager.term:
			return
		case <-ticker.C:
			if err := manager.buildIndex(); err != nil {
				zap.L().Error("[CreativeTemplateManager] buildIndex error", zap.Error(err))
			}
		}

	}
}

func (manager *CreativeTemplateManager) RegisterEcho(service *echo.Echo) {
	group := service.Group("/creative_template")
	group.Any("/match", manager.HandleHttpMatchTemplate)
	group.Any("/query_index", manager.HandleHttpQueryIndex)
}

func (manager *CreativeTemplateManager) HandleHttpMatchTemplate(ctx echo.Context) error {
	type Request struct {
		CreativeTemplateId int64                                        `json:"creative_template_id"`
		CreativeId         int64                                        `json:"creative_id"`
		CreativeTemplate   *creative_entity.CreativeTemplate            `json:"creative_template"`
		Creative           *entity.Creative                             `json:"creative"`
		Option             creative_entity.CreativeTemplateMapperOption `json:"option"`
	}

	var request Request
	if err := ctx.Bind(&request); err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	if request.CreativeTemplate == nil {
		request.CreativeTemplate = manager.creativeTemplateLoader.GetCreativeTemplateById(utils.ID(request.CreativeTemplateId))
		if request.CreativeTemplate == nil {
			return echo_helper.ErrorResponse(ctx, fmt.Errorf("creative_template not found, id:%d", request.CreativeTemplateId))
		}
	}

	if request.Creative == nil {
		request.Creative = manager.creativeLoader.GetCreativeById(utils.ID(request.CreativeId))
		if request.Creative == nil {
			return echo_helper.ErrorResponse(ctx, fmt.Errorf("creative not found, id:%d", request.CreativeId))
		}
	}

	type Response struct {
		CreativeTemplate *creative_entity.CreativeTemplate      `json:"creative_template"`
		Mapper           creative_entity.CreativeTemplateMapper `json:"mapper"`
	}

	mapper, err := manager.MatchTemplate(request.CreativeTemplate, request.Creative, request.Option)
	if err != nil {
		return echo_helper.ErrorResponseWithData(ctx, err, Response{
			CreativeTemplate: request.CreativeTemplate,
			Mapper:           mapper,
		})
	}

	return echo_helper.Response(ctx, Response{
		CreativeTemplate: request.CreativeTemplate,
		Mapper:           mapper,
	})
}

func (manager *CreativeTemplateManager) HandleHttpQueryIndex(ctx echo.Context) error {
	var request CreativeTemplateQuery
	if err := ctx.Bind(&request); err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	result, err := manager.QueryCreativeTemplate(request)
	if err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	return echo_helper.Response(ctx, result)
}

func (manager *CreativeTemplateManager) MatchTemplate(
	creativeTemplate *creative_entity.CreativeTemplate,
	creative entity.GenericCreative,
	option creative_entity.CreativeTemplateMapperOption) (creative_entity.CreativeTemplateMapper, error) {

	mapper := creative_entity.NewCreativeTemplateMapper()
	mapper.Option = option

	mapper.SetMaterialList(creative.GetMaterialList())
	err := mapper.MatchTemplate(creativeTemplate)
	if err != nil {
		return mapper, err
	}

	return mapper, nil
}

func (manager *CreativeTemplateManager) buildIndex() error {
	indexManager := bitmap_index_v2.NewIndexManager()

	indexManager.MustAddField(FieldIdSlotType, bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(FieldIdSize, bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(FieldIdScreenOrientationType, bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(FieldIdMediaId, bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(FieldIdMediaSlotId, bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(FieldIdSourceSlotId, bitmap_index_v2.FieldTypeString)
	indexManager.MustAddField(FieldIdOsType, bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(FieldIdDeviceType, bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(FieldIdCreativeTemplateKey, bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(FieldIdMediaTemplate, bitmap_index_v2.FieldTypeString)

	templateList := manager.creativeTemplateLoader.GetCreativeTemplateList()
	for _, template := range templateList {
		index := template.CreativeTemplateIndex
		if index == nil {
			zap.L().Warn("[CreativeTemplateManager] template index not found, template", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(template.Id)))))
			continue
		}

		doc := bitmap_index_v2.Doc{
			DocId: bitmap_index_v2.Id(template.Id),
		}

		if len(index.SlotType) > 0 {
			doc.AddIntTarget(FieldIdSlotType, index.SlotType, true)
		}

		if len(index.Size) > 0 {
			doc.AddIntTarget(FieldIdSize, index.Size, true)
		}

		if len(index.Orientation) > 0 {
			doc.AddIntTarget(FieldIdScreenOrientationType, index.Orientation, true)
		}

		if len(index.MediaId) > 0 {
			doc.AddIntTarget(FieldIdMediaId, index.MediaId, true)
		}

		if len(index.MediaSlotId) > 0 {
			doc.AddIntTarget(FieldIdMediaSlotId, index.MediaSlotId, true)
		}

		if len(index.SourceSlotId) > 0 {
			doc.AddStringTarget(FieldIdSourceSlotId, index.SourceSlotId, true)
		}

		if len(index.OsType) > 0 {
			doc.AddIntTarget(FieldIdOsType, index.OsType, true)
		}

		if len(index.DeviceType) > 0 {
			doc.AddIntTarget(FieldIdDeviceType, index.DeviceType, true)
		}

		if len(index.MediaTemplate) > 0 {
			doc.AddStringTarget(FieldIdMediaTemplate, []string{index.MediaTemplate}, true)
		}

		// 只有通用模版使用 通用的 CreativeTemplateKey的定向方式
		// 定制化模版过滤掉这种希望使用通用模版的流量
		if template.IsGeneric {
			key := template.GetCreativeTemplateKeyWithCache()
			zap.L().Info("[CreativeTemplateManager] add template name:, key", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", template.Name)))), zap.String("param2", fmt.Sprintf("%v", key.DebugString())))
			templateKey := int64(key)
			doc.AddIntTarget(FieldIdCreativeTemplateKey, []int64{templateKey}, true)
		} else {
			doc.AddIntTarget(FieldIdCreativeTemplateKey, []int64{0}, true)
		}

		if err := indexManager.AddDoc(&doc); err != nil {
			zap.L().Warn("[CreativeTemplateManager] indexManager.AddDoc error: , adid", zap.Error(err), zap.Int64("id", int64(template.Id)))
			return err
		}
	}

	if err := indexManager.BuildIndex(); err != nil {
		return err
	}

	manager.indexManager = indexManager
	return nil
}

func (manager *CreativeTemplateManager) GetTemplateById(id utils.ID) *creative_entity.CreativeTemplate {
	return manager.creativeTemplateLoader.GetCreativeTemplateById(id)
}

func (manager *CreativeTemplateManager) QueryCreativeTemplate(query CreativeTemplateQuery) (creative_entity.CreativeTemplateList, error) {
	indexCtx := manager.indexManager.CreateContext()
	defer indexCtx.Release()

	for {

		if len(query.CreativeTemplateKeyList) != 0 {
			if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64List(indexCtx, FieldIdCreativeTemplateKey, query.CreativeTemplateKeyList); err != nil {
				return nil, err
			} else if !hasCandidate {
				break
			}

		} else {
			if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, FieldIdCreativeTemplateKey, 0); err != nil {
				return nil, err
			} else if !hasCandidate {
				break
			}
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, FieldIdSlotType, int64(query.GetSlotType())); err != nil {
			return nil, err
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, FieldIdSize, int64(query.GetSlotWidth()*1000+query.GetSlotHeight())); err != nil {
			return nil, err
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, FieldIdScreenOrientationType, int64(query.GetScreenOrientation())); err != nil {
			return nil, err
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, FieldIdMediaId, int64(query.GetMediaId())); err != nil {
			return nil, err
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, FieldIdMediaSlotId, int64(query.GetMediaSlotId())); err != nil {
			return nil, err
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexString(indexCtx, FieldIdSourceSlotId, query.GetSourceSlotId()); err != nil {
			return nil, err
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, FieldIdOsType, int64(query.GetOsType())); err != nil {
			return nil, err
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, FieldIdDeviceType, int64(query.GetDeviceType())); err != nil {
			return nil, err
		} else if !hasCandidate {
			break
		}

		mediaTemplateList := make([]string, 0)
		for _, mediaTemplate := range query.GetMediaTemplateList() {
			mediaTemplateList = append(mediaTemplateList, query.GetMediaId().String()+"_"+mediaTemplate)
		}
		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexStringList(indexCtx, FieldIdMediaTemplate, mediaTemplateList); err != nil {
			return nil, err
		} else if !hasCandidate {
			break
		}

		break
	}

	var result creative_entity.CreativeTemplateList

	idList := indexCtx.GetDocs()
	idList = append(idList)
	if len(idList) != 0 && len(query.CreativeTemplateIdList) == 0 {
		idMap := make(map[int64]struct{}, len(idList)+len(query.CreativeTemplateIdList))

		result = make(creative_entity.CreativeTemplateList, 0, len(idList)+len(query.CreativeTemplateIdList))
		for _, id := range idList {
			if _, ok := idMap[int64(id)]; ok {
				continue
			}

			template := manager.creativeTemplateLoader.GetCreativeTemplateById(utils.ID(id))
			if template == nil {
				zap.L().Warn("[CreativeTemplateManager] template not found, id", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(id)))))
				continue
			}

			result = append(result, template)
			idMap[int64(id)] = struct{}{}
		}

		for _, id := range query.CreativeTemplateIdList {
			if _, ok := idMap[int64(id)]; ok {
				continue
			}

			template := manager.creativeTemplateLoader.GetCreativeTemplateById(utils.ID(id))
			if template == nil {
				zap.L().Warn("[CreativeTemplateManager] template not found, id", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(id)))))
				continue
			}

			result = append(result, template)
			idMap[int64(id)] = struct{}{}
		}
	}

	//if len(query.CreativeTemplateKeyList) != 0 && len(result) == 0 {
	//	return nil, fmt.Errorf("creative_template not found, key:%v", query.CreativeTemplateKeyList)
	//}

	return result, nil
}

func (manager *CreativeTemplateManager) CreateCreativeTemplateForKey(key int64) (*creative_entity.CreativeTemplate, error) {
	templateCache := manager.GetMediaSpecificCreativeTemplateCache(key)
	if templateCache != nil {
		return templateCache, nil
	}

	template := creative_entity.CreativeTemplate{}
	template.BuildWithCreativeTemplateKey(creative_entity.CreativeTemplateKey(key))

	manager.SetMediaSpecificCreativeTemplateCache(key, &template)
	return &template, nil
}

func (manager *CreativeTemplateManager) CreateCreativeTemplate(creativeTemplateKeyList []int64) (creative_entity.CreativeTemplateList, error) {
	var result creative_entity.CreativeTemplateList

	for _, key := range creativeTemplateKeyList {
		template, err := manager.CreateCreativeTemplateForKey(key)
		if err != nil {
			return nil, err
		}
		result = append(result, template)
	}

	return result, nil
}

func (manager *CreativeTemplateManager) GetMediaSpecificCreativeTemplateCache(key int64) *creative_entity.CreativeTemplate {
	manager.lock.RLock()
	defer manager.lock.RUnlock()

	return manager.mediaSpecificCreativeTemplateCache[key]
}

func (manager *CreativeTemplateManager) SetMediaSpecificCreativeTemplateCache(key int64, template *creative_entity.CreativeTemplate) {
	manager.lock.Lock()
	defer manager.lock.Unlock()

	manager.mediaSpecificCreativeTemplateCache[key] = template
}

type CreativeTemplateQuery struct {
	CreativeTemplateIdList  []utils.ID                   `json:"creative_template_id_list"`
	MediaId                 utils.ID                     `json:"media_id"`
	MediaSlotId             utils.ID                     `json:"media_slot_id"`
	SourceSlotId            string                       `json:"source_slot_id"`
	SlotType                entity.SlotType              `json:"slot_type"`
	SlotWidth               uint32                       `json:"slot_width"`
	SlotHeight              uint32                       `json:"slot_height"`
	ScreenOrientation       entity.ScreenOrientationType `json:"screen_orientation"`
	OsType                  entity.OsType                `json:"os_type"`
	DeviceType              entity.DeviceType            `json:"device_type"`
	CreativeTemplateKeyList []int64                      `json:"creative_template_key_list"`
	MediaTemplateList       []string                     `json:"media_template_list"`
}

func (query CreativeTemplateQuery) GetMediaId() utils.ID {
	return query.MediaId
}

func (query CreativeTemplateQuery) GetMediaSlotId() utils.ID {
	return query.MediaSlotId
}

func (query CreativeTemplateQuery) GetSourceSlotId() string {
	return query.SourceSlotId
}

func (query CreativeTemplateQuery) GetSlotType() entity.SlotType {
	return query.SlotType
}

func (query CreativeTemplateQuery) GetSlotWidth() uint32 {
	return query.SlotWidth
}

func (query CreativeTemplateQuery) GetSlotHeight() uint32 {
	return query.SlotHeight
}

func (query CreativeTemplateQuery) GetScreenOrientation() entity.ScreenOrientationType {
	return query.ScreenOrientation
}

func (query CreativeTemplateQuery) GetOsType() entity.OsType {
	return query.OsType
}

func (query CreativeTemplateQuery) GetDeviceType() entity.DeviceType {
	return query.DeviceType
}

func (query CreativeTemplateQuery) GetMediaTemplateList() []string {
	return query.MediaTemplateList
}
