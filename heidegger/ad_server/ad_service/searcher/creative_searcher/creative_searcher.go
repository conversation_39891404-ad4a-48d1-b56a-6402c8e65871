package creative_searcher

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/entity_loader/app_list_loader"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
)

type CreativeSearcher struct {
	creativeTemplateManager        *CreativeTemplateManager
	creativeTemplateMetricsCounter *prometheus_helper.LabelCounter

	appListLoader app_list_loader.AppListLoader
}

func NewCreativeSearcher(creativeTemplateManager *CreativeTemplateManager, appListLoader app_list_loader.AppListLoader) *CreativeSearcher {
	return &CreativeSearcher{
		creativeTemplateManager: creativeTemplateManager,
		creativeTemplateMetricsCounter: prometheus_helper.RegisterLabelCounter(
			"ad_server_creative_searcher_template",
			[]string{"ad_id", "template_id", "asset", "result"},
		),
		appListLoader: appListLoader,
	}
}

func (s *CreativeSearcher) GetTaskName() string {
	return "CreativeSearcher"
}

func (s *CreativeSearcher) Start() error {
	return nil
}

func (s *CreativeSearcher) Stop() {

}

func (s *CreativeSearcher) Do(request *ad_service.AdRequest) error {
	for _, candidate := range request.Response.GetAdCandidateList() {
		if candidate.GetAd().GetAdType() == entity.AdTypeLocal {
			if candidate.GetAd().GetCreative() == nil {
				candidate.FilterByError(err_code.ErrCreativeNil)
				continue
			}
			candidate.SetCreative(candidate.GetAd().GetCreative())
		}

		if candidate.GetAd().GetAdType() == entity.AdTypeDsp &&
			candidate.GetCreative() == nil {

			if candidate.GetDspResponseAd() != nil && candidate.GetDspResponseAd().GetCreative() != nil {
				candidate.SetCreative(candidate.GetDspResponseAd().GetCreative())
			} else if candidate.GetAd().GetCreative() != nil {
				candidate.SetCreative(candidate.GetAd().GetCreative())
			}
		}

		if candidate.GetAd().GetAdType() == entity.AdTypeAttribution {
			continue
		}

		modifier := candidate.GetTrafficResponseModifier()
		if modifier == nil {
			continue
		}

		creative, err := modifier.ModifyCreative(candidate.GetCreative())
		if err != nil {
			candidate.FilterByError(err_code.ErrCreativeModifierFailed.Wrap(err))
			continue
		}

		candidate.SetCreative(creative)
	}

	if len(request.GetSlotCreativeTemplateList()) != 0 {

		mappingOption := creative_entity.CreativeTemplateMapperOption{
			Verbose: request.IsDebug,
		}

		for _, candidate := range request.Response.GetAdCandidateList() {
			if candidate.GetAd().GetAdType() == entity.AdTypeAttribution {
				request.Response.PushCandidates(candidate)
				continue
			}

			if candidate.GetCreative() == nil {
				candidate.FilterByError(err_code.ErrCreativeNotFound)
				continue
			}

			if request.IsDebug {
				zap.L().Info("[CreativeSearcher] [creative] , %s, materialCount:%d", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(candidate.GetCreative())))).GetCreativeId(), candidate.GetCreative(), len(candidate.GetCreative().GetMaterialList()))
			}

			hasMatch := false
			var matchErr error
			for _, template := range request.GetSlotCreativeTemplateList() {
				mapper, err := s.creativeTemplateManager.MatchTemplate(template, candidate.GetCreative(), mappingOption)

				labels := []string{
					candidate.GetAd().GetAdId().String(),
					template.Id.String(),
					"success",
					"success",
				}
				if template.Id == 0 {
					labels[1] = template.CreativeTemplateKey.String()
				}

				if err != nil {
					labels[2] = mapper.ErrAsset
					labels[3] = err.Error()
				}

				s.creativeTemplateMetricsCounter.Inc(labels)

				if err != nil {
					matchErr = err
					continue
				}

				//补充包名icon
				s.fillAppInfo(request, candidate)

				candidate.SetMainMaterial(mapper.MainMaterial)
				candidate.SetSelectedMaterialList(mapper.GetSelectedMaterialList())
				candidate.SetActiveCreativeTemplateKey(template.GetCreativeTemplateKeyWithCache())
				candidate.SetSelectedTemplateType(template.GetTemplateType())
				hasMatch = true

				if request.IsDebug {
					zap.L().Info("[CreativeSearcher] [creative] match , %s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(candidate.GetCreative())))).GetCreativeId(), candidate.GetCreative())
					zap.L().Info("[CreativeSearcher] [template] match , %s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(template.Id)))), zap.Int64("param2", int64(template)))
					for _, material := range candidate.GetSelectedMaterialList() {
						zap.L().Info("[CreativeSearcher] [material] match , %s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(material.Id)))), zap.Int64("param2", int64(material)))
					}
				}

				break
			}

			if !hasMatch {
				candidate.FilterByError(err_code.ErrCreativeTemplateNotMatch.Wrap(matchErr))
				continue
			}

			request.Response.PushCandidates(candidate)
		}

		request.Response.SwapAndClearCandidate()

		if request.Response.NoCandidate() {
			request.Response.SetError(err_code.ErrCreativeNotMatch)
		}
	}

	return nil
}

func (s *CreativeSearcher) fillAppInfo(request *ad_service.AdRequest, candidate *ad_service.AdCandidate) {
	dspAd := candidate.GetDspResponseAd()
	if dspAd != nil && len(dspAd.GetDeepLinkUrl()) > 0 {
		if dspAd.GetAppInfo() == nil {
			dspAd.AppInfo = &entity.AppInfo{}
		}

		if len(dspAd.GetAppInfo().PackageName) == 0 || len(dspAd.GetAppInfo().Icon) == 0 {
			applist := s.getApp(dspAd.GetDeepLinkUrl())
			if applist != nil {
				if len(dspAd.GetAppInfo().PackageName) == 0 {
					dspAd.GetAppInfo().PackageName = applist.GetPackageNameByOs(request.Device.OsType)
				}
				if len(dspAd.GetAppInfo().AppName) == 0 {
					dspAd.GetAppInfo().AppName = applist.Name
				}
				if len(dspAd.GetAppInfo().Icon) == 0 {
					dspAd.GetAppInfo().Icon = applist.AdvertiserDisplayLogo
				}
			}

		}
	}
}

func (s *CreativeSearcher) getApp(dplink string) *entity.AppList {
	if len(dplink) == 0 {
		return nil
	}

	urlHead := net_utils.GetURLHeadFromUrl(dplink)

	if len(urlHead) == 0 {
		return nil
	}

	return s.appListLoader.GetAppByScheme(urlHead)
}
