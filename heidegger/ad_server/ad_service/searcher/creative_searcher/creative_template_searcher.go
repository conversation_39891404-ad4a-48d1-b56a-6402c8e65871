package creative_searcher

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
)

type CreativeTemplateSearcher struct {
	creativeTemplateManager *CreativeTemplateManager
}

func NewCreativeTemplateSearcher(creativeTemplateManager *CreativeTemplateManager) *CreativeTemplateSearcher {
	return &CreativeTemplateSearcher{
		creativeTemplateManager: creativeTemplateManager,
	}
}

func (s *CreativeTemplateSearcher) GetTaskName() string {
	return "CreativeTemplateSearcher"
}

func (s *CreativeTemplateSearcher) Start() error {
	return nil
}

func (s *CreativeTemplateSearcher) Stop() {

}

func (s *CreativeTemplateSearcher) Do(request *ad_service.AdRequest) error {
	err := s.doSearch(request)
	if err != nil {
		return err
	}

	request.GetSlotCreativeTemplateList().Sort()
	return nil
}

func (s *CreativeTemplateSearcher) doSearch(request *ad_service.AdRequest) error {
	if len(request.GetMediaSlotInfo().CreativeTemplateIdList) != 0 {
		for _, templateId := range request.GetMediaSlotInfo().CreativeTemplateIdList {
			template := s.creativeTemplateManager.GetTemplateById(templateId)
			if template == nil {
				zap.L().Warn("creative template id[] is found in media slot info", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(templateId)))))
				continue
			}

			request.AppendSlotCreativeTemplate(template)
		}
	}

	if len(request.GetSlotCreativeTemplateList()) != 0 {
		return nil
	}

	templateCandidate, err := s.creativeTemplateManager.QueryCreativeTemplate(CreativeTemplateQuery{
		CreativeTemplateIdList:  request.GetMediaSlotInfo().CreativeTemplateIdList,
		SlotType:                request.GetSlotType(),
		MediaId:                 request.GetMediaId(),
		MediaSlotId:             request.GetMediaSlotId(),
		SourceSlotId:            request.GetSourceSlotId(),
		SlotWidth:               request.GetSlotWidth(),
		SlotHeight:              request.GetSlotHeight(),
		DeviceType:              request.Device.GetDeviceType(),
		OsType:                  request.Device.GetOsType(),
		ScreenOrientation:       request.Device.GetScreenOrientation(),
		CreativeTemplateKeyList: request.GetCreativeTemplateKeyList(),
		MediaTemplateList:       request.AdxTemplateId,
	})
	if err != nil {
		return err_code.ErrCreativeTemplateNotMatch.Wrap(err)
	}

	request.SetSlotCreativeTemplateList(templateCandidate)

	for _, key := range request.GetCreativeTemplateKeyList() {
		if request.GetSlotCreativeTemplateList().ContainsKey(creative_entity.CreativeTemplateKey(key)) {
			continue
		}

		creativeTemplate, err := s.creativeTemplateManager.CreateCreativeTemplateForKey(key)
		if err != nil {
			return err_code.ErrCreativeTemplateNotMatch.Wrap(err)
		}

		request.AppendSlotCreativeTemplate(creativeTemplate)
	}

	return nil
}
