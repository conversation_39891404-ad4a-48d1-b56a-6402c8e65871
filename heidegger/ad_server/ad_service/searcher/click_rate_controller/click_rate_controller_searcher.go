package click_rate_controller

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"math/rand"
)

type ClickRateControllerSearcher struct {
	clickRateController ClickRateController
}

func NewClickRateControllerSearcher(clickRateController ClickRateController) *ClickRateControllerSearcher {
	return &ClickRateControllerSearcher{
		clickRateController: clickRateController,
	}
}

func (s *ClickRateControllerSearcher) GetTaskName() string {
	return "ClickRateControllerSearcher"
}

func (s *ClickRateControllerSearcher) Start() error {
	return nil
}

func (s *ClickRateControllerSearcher) Stop() {

}

func (s *ClickRateControllerSearcher) Do(request *ad_service.AdRequest) error {
	for _, candidate := range request.Response.GetAdCandidateList() {
		if candidate.GetAd().AdId != 50008 {
			continue
		}

		if candidate.GetDspResponseAd() == nil {
			continue
		}

		deviceId, _ := candidate.GetModifiedTrafficData().GetDeviceIdWithType()
		controlKey := fmt.Sprintf("%d", 50003)

		clickControlInfo, err := s.clickRateController.GetClickControlInfo(deviceId, controlKey)
		if err != nil {
			zap.L().Error("GetClickControlInfo error", zap.Error(err))
			continue
		}

		clickRate := clickControlInfo.GetClickRate()

		//if clickControlInfo.ImpressionCount != 0 {
		//	zap.L().Info("GetClickControlInfo success, deviceId: %s, controlKey: %s, clickRate: %f, clickCount:, impCount", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(deviceId)))), zap.Int64("param2", int64(controlKey)), zap.Int64("param3", int64(clickRate)), zap.Int64("param4", int64(clickControlInfo.ClickCount)), zap.Int64("param5", int64(clickControlInfo.ImpressionCount)))
		//}

		if clickRate <= 0.5 {
			if rand.Uint32()%5 == 0 {
				candidate.GetDspResponseAd().AdMonitorInfo.ClickMonitorList = nil

				//candidate.GetDspResponseAd().AdMonitorInfo.LandingUrl = clickControlInfo.GetLandingPage()
				//candidate.GetDspResponseAd().AdMonitorInfo.DeepLinkUrl = clickControlInfo.GetDeepLink()
				candidate.GetDspResponseAd().AdMonitorInfo.LandingUrl = "https://ccc-x.jd.com/dsp/nc"
				candidate.GetDspResponseAd().AdMonitorInfo.DeepLinkUrl = ""
				candidate.SetIsShadowedClick(true)
				//zap.L().Info("SetIsShadowedClick random, deviceId: %s, controlKey: %s, requestId: %s, landingUrl: %s, deepLinkUrl: %s, click:, imp", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(//	deviceId)))), zap.Int64("param2", int64(controlKey)), zap.Int64("id", int64(request.GetRequestId())),
				//	candidate.GetDspResponseAd().AdMonitorInfo.LandingUrl,
				//	candidate.GetDspResponseAd().AdMonitorInfo.DeepLinkUrl,
				//	clickControlInfo.ClickCount, clickControlInfo.ImpressionCount,
				//)
			} else {
				if err := s.clickRateController.MarkIntendedClick(
					deviceId,
					controlKey,
					request.GetRequestId(),
					candidate.GetDspResponseAd().AdMonitorInfo.LandingUrl,
					candidate.GetDspResponseAd().AdMonitorInfo.DeepLinkUrl); err != nil {
					zap.L().Error("MarkIntendedClick error", zap.Error(err))
				}
			}
		} else {
			if clickControlInfo.IsConfirmed() {
				candidate.GetDspResponseAd().AdMonitorInfo.ClickMonitorList = nil

				//candidate.GetDspResponseAd().AdMonitorInfo.LandingUrl = clickControlInfo.GetLandingPage()
				//candidate.GetDspResponseAd().AdMonitorInfo.DeepLinkUrl = clickControlInfo.GetDeepLink()
				candidate.GetDspResponseAd().AdMonitorInfo.LandingUrl = "https://ccc-x.jd.com/dsp/nc"
				candidate.GetDspResponseAd().AdMonitorInfo.DeepLinkUrl = ""
				candidate.SetIsShadowedClick(true)
				//zap.L().Info("SetIsShadowedClick, deviceId: %s, controlKey: %s, requestId: %s, landingUrl: %s, deepLinkUrl: %s, click:, imp", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(//	deviceId)))), zap.Int64("param2", int64(controlKey)), zap.Int64("id", int64(request.GetRequestId())),
				//	candidate.GetDspResponseAd().AdMonitorInfo.LandingUrl,
				//	candidate.GetDspResponseAd().AdMonitorInfo.DeepLinkUrl,
				//	clickControlInfo.ClickCount, clickControlInfo.ImpressionCount,
				//)
			} else {
				if err := s.clickRateController.MarkIntendedClick(
					deviceId,
					controlKey,
					request.GetRequestId(),
					candidate.GetDspResponseAd().AdMonitorInfo.LandingUrl,
					candidate.GetDspResponseAd().AdMonitorInfo.DeepLinkUrl); err != nil {
					zap.L().Error("MarkIntendedClick old intention not confirm error", zap.Error(err))
				} else {
					zap.L().Info("MarkIntendedClick success old intention not confirm, deviceId: , controlKey: , requestId: , landingUrl: %s, deepLinkUrl: %s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", deviceId)))), zap.String("param2", fmt.Sprintf("%v", controlKey)), zap.String("param3", fmt.Sprintf("%v", request.GetRequestId())),
						candidate.GetDspResponseAd().AdMonitorInfo.LandingUrl,
						candidate.GetDspResponseAd().AdMonitorInfo.DeepLinkUrl)
				}
			}
		}
	}

	return nil
}
