package click_rate_controller

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/tracking/tracking_service"
	"time"
)

type ClickRateControllerClickTrackingHandler struct {
	clickRateController ClickRateController
}

func NewClickRateControllerClickTrackingHandler(clickRateController ClickRateController) *ClickRateControllerClickTrackingHandler {
	return &ClickRateControllerClickTrackingHandler{
		clickRateController: clickRateController,
	}
}

func (p *ClickRateControllerClickTrackingHandler) Start() error {
	return nil
}

func (p *ClickRateControllerClickTrackingHandler) Stop() {

}

func (p *ClickRateControllerClickTrackingHandler) GetName() string {
	return "ClickRateControllerClickTrackingHandler"
}

func (p *ClickRateControllerClickTrackingHandler) Do(request *tracking_service.TrackingRequest) error {
	extData, err := request.GetExtData()
	if err != nil {
		return err
	}

	if extData.IsShadowedClick == 1 {
		return nil
	}

	if extData.AdId != 50008 {
		return nil
	}

	if err := p.clickRateController.TryConfirmClick(
		extData.DeviceId,
		fmt.Sprintf("%d", 50003),
		extData.GetRequestId()); err != nil {
		zap.L().Error("TryConfirmClick error: , elapse:%f", zap.Error(err), zap.String("param2", fmt.Sprintf("%v", float64(time.Now())).UnixNano()-extData.Timestamp)/1e9)
	} else {
		//zap.L().Info("TryConfirmClick success, deviceId: %s, adId: , requestId: %s, elapse:%f", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(//	extData.DeviceId)))), zap.Int64("id", int64(//	extData.AdId)), zap.Int64("id", int64(//	extData.GetRequestId())),
		//	float64(time.Now().UnixNano()-extData.Timestamp)/1e9)
	}

	return nil
}
