package attribution_sender_searcher

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"strings"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_sampler"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/slice_utils"
	"gitlab.com/dev/heidegger/tracking/attribution_sender"
)

type AttributionSenderSearcher struct {
	sender  *attribution_sender.AttributionSender
	counter *prometheus_helper.LabelCounter

	TrafficSampler traffic_sampler.TrafficSampler
}

func NewAttributionSenderSearcher(sender *attribution_sender.AttributionSender, trafficSampler traffic_sampler.TrafficSampler) *AttributionSenderSearcher {
	return &AttributionSenderSearcher{
		sender: sender,
		counter: prometheus_helper.RegisterLabelCounter(
			"AdService_AttributionSender",
			[]string{"ad_id"},
		),
		TrafficSampler: trafficSampler,
	}
}

func (w *AttributionSenderSearcher) GetTaskName() string {
	return "AttributionSenderSearcher"
}

func (w *AttributionSenderSearcher) Start() error {
	return nil
}

func (w *AttributionSenderSearcher) Stop() {

}

func (w *AttributionSenderSearcher) Do(request *ad_service.AdRequest) error {
	if request.SlotType != entity.SlotTypeAttribution &&
		!request.GetMediaSlotInfo().HasSlotTag(entity.SlotTagBackgroundS2S) {
		return nil
	}

	if len(request.Response.GetAdCandidateList()) == 0 {
		return nil
	}
	hasC2S := false
	for _, candidate := range request.Response.GetAdCandidateList() {
		ad := candidate.GetGenericAd()

		if candidate.GetErrorCode().Code != 0 {
			continue
		}

		if ad.GetAdType() != entity.AdTypeAttribution {
			if request.GetMediaSlotInfo().HasSlotTag(entity.SlotTagBackgroundS2S) {
				request.Response.PushCandidates(candidate)
				continue
			} else {
				candidate.FilterByError(err_code.ErrAttributionFilter)
				continue
			}
		}

		candidate.SetAdCandidateChargePriceEncoder(func(chargePrice uint32) string {
			return fmt.Sprintf("%d", chargePrice)
		})

		if err := w.doAttribution(request, candidate, &hasC2S); err != nil {
			candidate.FilterByError(err_code.ErrAttributionFailed)
			continue
		}

		if ad.GetAdType() != entity.AdTypeAttribution && request.SlotType != entity.SlotTypeAttribution {
			candidate.FilterByError(err_code.ErrAttributionInBackground)
		}

		request.Response.PushCandidates(candidate)
	}

	request.Response.SwapAndClearCandidate()

	if request.Response.NoCandidate() {
		request.Response.SetError(err_code.ErrAttributionFilter)
	}

	return nil
}

func (w *AttributionSenderSearcher) doAttribution(request *ad_service.AdRequest, candidate *ad_service.AdCandidate, hasC2s *bool) error {
	genericAd := candidate.GetAd()

	urlList := make([]string, 0)
	// creative
	creativeKey := request.GetMediaExtraString("mediaCreativeId", utils.EmptyString)
	if creativeKey != utils.EmptyString {
		creative := entity.NewCreative()
		creative.CreativeKey = creativeKey
		candidate.SetCreative(creative) // 设置创意
	}

	traffic := candidate.GetModifiedTrafficData()
	trackingGen := candidate.GetTrackingGen(traffic)

	if request.AttributionMod != entity.AttributionModClick { //点击的报曝光
		// 将服务端上报当作一次impression
		systemImpressionList := genericAd.GetImpressionMonitorList()
		systemImpressionList = candidate.ReplaceUrlMacroList(systemImpressionList, traffic, trackingGen)

		urlList = append(urlList, systemImpressionList...)
	}

	// 将上报客户当作一个click
	//流量扣量在前面就扣了
	//if request.RequestTime.UnixNano()%int64(10000) < (10000 - int64(genericAd.GetAttributionRate())) {
	//if len(genericAd.GetLandingUrl()) != 0 {
	//	advertiserLand := genericAd.GetLandingUrl()
	//	advertiserLand = candidate.ReplaceUrlMacro(advertiserLand, traffic, trackingGen)
	//	urlList = append(urlList, advertiserLand)
	//}

	if len(genericAd.GetC2SClickUrl()) > 0 && *hasC2s {
		return err_code.ErrAttributionC2sFilter
	}

	if len(genericAd.GetC2SClickUrl()) > 0 {
		*hasC2s = true
	}

	mediaCallback := request.GetMediaExtraString("callback", utils.EmptyString)
	if strings.HasPrefix(mediaCallback, "http") {
		mediaCallback = candidate.ReplaceUrlMacro(mediaCallback, traffic, trackingGen)
	} else {
		mediaCallback = utils.EmptyString
	}

	if true {
		if request.AttributionMod != entity.AttributionModImp { //曝光不报点击
			systemClickList := genericAd.GetClickMonitorList()
			systemClickList = candidate.ReplaceUrlMacroList(systemClickList, traffic, trackingGen)

			err := w.checkClickMonitor(systemClickList)
			if err != nil {
				zap.L().Error("[AttributionSenderSearcher][doAttribution] checkClickMonitor fail, err", zap.Error(err))
			}
			urlList = append(urlList, systemClickList...)
		}

		//key := fmt.Sprintf("%d_%s", genericAd.GetAdId(), request.GetRequestId())
		//if err := w.sender.StoreMediaAttributionCandidate(key, &attribution_sender.AttributionCandidate{
		//	Timestamp:    uint64(time.Now().UnixNano()),
		//	SlotId:       request.SourceSlotId,
		//	AdId:         int32(genericAd.GetAdId()),
		//	Callback:     mediaCallback,
		//	CallbackRate: int32(genericAd.GetAttributionCallbackRate()),
		//}); err != nil {
		//	zap.L().Error("[AttributionSenderSearcher][doAttribution] fail, err", zap.Error(err))
		//	return err
		//}

	} else {
		//废弃了
		//systemClickList := genericAd.GetClickMonitorList()
		//systemClickList = candidate.ReplaceUrlMacroList(systemClickList, traffic, trackingGen)
		//err := w.checkClickMonitor(systemClickList)
		//if err != nil {
		//	zap.L().Error("[AttributionSenderSearcher][doAttribution] checkClickMonitor fail, err", zap.Error(err))
		//}
		//urlList = append(urlList, systemClickList...)
		//
		//key := fmt.Sprintf("%d_%s", genericAd.GetAdId(), request.GetRequestId())
		//if err := w.sender.StoreMediaAttributionCandidate(key, &attribution_sender.AttributionCandidate{
		//	Timestamp:    uint64(time.Now().UnixNano()),
		//	SlotId:       request.GetSourceSlotId(),
		//	AdId:         int32(genericAd.GetAdId()),
		//	Callback:     mediaCallback,
		//	CallbackRate: int32(genericAd.GetAttributionCallbackRate()),
		//}); err != nil {
		//	zap.L().Error("[AttributionSenderSearcher][doAttribution] fail, err", zap.Error(err))
		//	return err
		//}
	}
	//}

	if len(urlList) == 0 {
		return nil
	}

	if rand.Intn(10000) == 0 || request.IsDebug {
		for _, item := range urlList {
			zap.L().Info("do Attribution Url:%s, ad", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(item)))), zap.Int64("id", int64(genericAd.GetAdId())))
		}
	}

	//if candidate.GetAd().AdId == 263 && len(urlList) == 3 {
	//	urlInfo, _ := url.Parse(urlList[2])
	//	zap.L().Info("[Super263] , media:%s, bid:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", urlInfo.Query()))))["callbackURL"][0], request.GetMediaExtraString("callback", utils.EmptyString), request.RawHttpRequest.GetUrl())
	//}

	w.counter.Inc([]string{fmt.Sprintf("%d", genericAd.GetAdId())})
	w.SampleAttrBroadcastRequest(candidate, urlList)
	return w.sender.QueryUrl(urlList)
}

func (w *AttributionSenderSearcher) SampleAttrBroadcastRequest(candidate *ad_service.AdCandidate, urlList []string) {
	if w.TrafficSampler == nil || !candidate.GetAdRequest().GetIsSampled() {
		return
	}

	reportUrlList := make([]string, 0)

	for _, item := range urlList {
		if w.innerUrl(item, "bid.adbiding.cn") || w.innerUrl(item, "t.adbiding.cn") {
			continue
		}
		reportUrlList = append(reportUrlList, item)
	}

	data, _ := json.Marshal(reportUrlList)

	candidate.SetDspRequestSample(slice_utils.CopyBuffer(data))
	candidate.SetIsSampled(true)
}

func (w *AttributionSenderSearcher) innerUrl(url string, prefix string) bool {
	urlHttp := "http://" + prefix
	if strings.HasPrefix(url, urlHttp) {
		return true
	}

	urlHttps := "https://" + prefix
	if strings.HasPrefix(url, urlHttps) {
		return true
	}

	return false
}

func (w *AttributionSenderSearcher) checkClickMonitor(clickList []string) error {
	if len(clickList) == 0 {
		return fmt.Errorf("click empty")
	}

	for _, click := range clickList {
		if strings.Contains(click, "__CURL__") ||
			strings.Contains(click, "__CALLBACK__") {
			return fmt.Errorf("callback missing")
		}
	}

	return nil
}
