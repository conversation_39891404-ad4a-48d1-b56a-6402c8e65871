package broadcast_searcher

import (
	"fmt"
	"math/rand"
	"strings"
	"sync"
	"time"

	"gitlab.com/dev/heidegger/library/utils/time_utils"

	"github.com/panjf2000/ants/v2"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_sampler"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker_library/broadcast_scheduler"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker_library/broadcast_scheduler_v2"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/ad_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/app_list_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/dsp_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/dsp_slot_info_loader"
	"gitlab.com/dev/heidegger/library/price/price_manager"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/limiter"
	"gitlab.com/dev/heidegger/master_server/master_server"
	"gitlab.com/dev/heidegger/user_segment"
)

type BroadcastManager struct {
	dspLoader             dsp_loader.DspLoader
	adLoader              ad_loader.AdLoader
	dspSlotInfoLoader     dsp_slot_info_loader.DspSlotInfoLoader
	trafficSampler        traffic_sampler.TrafficSampler
	priceManager          *price_manager.PriceManager
	appListLoader         app_list_loader.AppListLoader
	externalMappingLoader app_list_loader.ExternalMappingLoader

	broadcastScheduler   *broadcast_scheduler.BroadcastScheduler
	broadcastSchedulerV2 *broadcast_scheduler_v2.BroadcastSchedulerV2

	dspClientMap       map[utils.ID]*BroadcastDspClient
	adRateLimiter      map[utils.ID]*limiter.SimplePresetRateLimiter
	dspSlotRateLimiter map[utils.ID]*limiter.SimplePresetRateLimiter
	dspRateLimiter     map[utils.ID]*limiter.SimplePresetRateLimiter

	adRateLimiterServiceCount int
	adRateLimiterRand         float64
	serviceRegister           master_server.ServiceRegister
	userSegmentClient         user_segment.UserSegmentClient

	dspPricingMetrics                  *prometheus_helper.LabelHistogram
	broadcastSchedulerPacingInMetrics  *prometheus_helper.LabelCounter
	broadcastSchedulerPacingOutMetrics *prometheus_helper.LabelCounter

	worker *ants.Pool

	term chan struct{}
}

func NewBroadcastManager(
	dspLoader dsp_loader.DspLoader,
	adLoader ad_loader.AdLoader,
	appListLoader app_list_loader.AppListLoader,
	externalMappingLoader app_list_loader.ExternalMappingLoader,
	dspSlotInfoLoader dsp_slot_info_loader.DspSlotInfoLoader,
	trafficSampler traffic_sampler.TrafficSampler,
	priceManager *price_manager.PriceManager,
	serviceRegister master_server.ServiceRegister,
	userSegmentClient user_segment.UserSegmentClient) *BroadcastManager {
	worker, err := ants.NewPool(
		1024*32,
		ants.WithNonblocking(true),
		ants.WithPreAlloc(true))
	if err != nil {
		panic(err)
	}

	return &BroadcastManager{
		dspLoader:             dspLoader,
		adLoader:              adLoader,
		appListLoader:         appListLoader,
		externalMappingLoader: externalMappingLoader,
		dspSlotInfoLoader:     dspSlotInfoLoader,
		trafficSampler:        trafficSampler,
		priceManager:          priceManager,
		serviceRegister:       serviceRegister,
		userSegmentClient:     userSegmentClient,
		worker:                worker,
		dspClientMap:          make(map[utils.ID]*BroadcastDspClient),
		term:                  make(chan struct{}),

		dspPricingMetrics:                  prometheus_helper.RegisterLabelHistogramPricingBucket("ad_server_dsp_pricing", []string{"dsp_id", "ad_id"}),
		broadcastSchedulerPacingInMetrics:  prometheus_helper.RegisterLabelCounter("ad_server_broadcast_scheduler_pacing_in", []string{"method", "id", "level"}),
		broadcastSchedulerPacingOutMetrics: prometheus_helper.RegisterLabelCounter("ad_server_broadcast_scheduler_pacing_out", []string{"method", "id", "level"}),
	}
}

func (manager *BroadcastManager) SetBroadcastScheduler(broadcastScheduler *broadcast_scheduler.BroadcastScheduler) {
	manager.broadcastScheduler = broadcastScheduler
}

func (manager *BroadcastManager) SetBroadcastSchedulerV2(broadcastSchedulerV2 *broadcast_scheduler_v2.BroadcastSchedulerV2) {
	manager.broadcastSchedulerV2 = broadcastSchedulerV2
}

func (manager *BroadcastManager) Start() error {
	if manager.dspLoader == nil {
		return fmt.Errorf("dspLoader is nil")
	}

	for _, dsp := range manager.dspLoader.GetDspList() {
		if err := manager.RegisterDsp(dsp); err != nil {
			return err
		}
	}

	if err := manager.registerDspSlotInfo(); err != nil {
		return err
	}

	if err := manager.refreshDspRateLimiter(); err != nil {
		return err
	}

	if err := manager.refreshAdRateLimiter(); err != nil {
		return err
	}

	if err := manager.refreshDspSlotRateLimiter(); err != nil {
		return err
	}

	go manager.loop()

	return nil
}

func (manager *BroadcastManager) Stop() {
	close(manager.term)
}

func (manager *BroadcastManager) RegisterDsp(dsp *entity.Dsp) error {
	if manager.dspClientMap[dsp.DspId] != nil {
		return fmt.Errorf("duplicate dsp id: %d", dsp.DspId)
	}

	dspClient := NewBroadcastDspClient(dsp)
	dspClient.SetTrafficSampler(manager.trafficSampler)
	dspClient.SetPriceManager(manager.priceManager)
	dspClient.SetUserSegmentClient(manager.userSegmentClient)
	dspClient.SetAppListLoader(manager.appListLoader)
	dspClient.SetExternalMappingLoader(manager.externalMappingLoader)

	if err := dspClient.Start(); err != nil {
		zap.L().Error("[BroadcastManager][RegisterDsp] err", zap.Error(err))
		return nil
	}

	manager.dspClientMap[dsp.DspId] = dspClient
	return nil
}

func (manager *BroadcastManager) BroadcastTaskList(request *ad_service.AdRequest, taskMap BroadcastTaskMap) error {
	if len(taskMap) == 0 {
		return nil
	}

	maxTimeout := taskMap.GetMaxTimeout()
	if maxTimeout == 0 {
		maxTimeout = 1000
	}

	ch := make(chan struct{}, 1)
	if err := manager.worker.Submit(func() {
		wg := sync.WaitGroup{}
		for id, _ := range taskMap {
			taskId := id
			wg.Add(1)
			if err := manager.worker.Submit(func() {
				defer wg.Done()
				manager.BroadcastTask(taskMap[taskId])
			}); err != nil {
				wg.Done()
				taskMap[taskId].FilterByError(err_code.ErrBroadcastOverload)
			}
		}
		wg.Wait()
		ch <- struct{}{}
	}); err != nil {
		return err_code.ErrBroadcastOverload.Wrap(err)
	}

	select {
	case <-ch:
		for _, task := range taskMap {
			if !task.IsFiltered() {
				if err := manager.selectResponseCandidate(task); err != nil {
					task.FilterByError(err_code.ErrBroadcastResponseCandidateError)
				}
			}
		}
	case <-time.After(time.Duration(maxTimeout) * time.Millisecond):
		for _, task := range taskMap {
			task.LockWrite()
			if len(task.GetDspResponseCandidate()) != 0 { // is this a success task
				task.UnlockWrite()

				if err := manager.selectResponseCandidate(task); err != nil {
					if adErr, ok := err.(err_code.AdErrorCode); ok {
						task.FilterByError(adErr)
					} else {
						task.FilterByError(err_code.ErrBroadcastResponseCandidateError)
					}
				}
				continue
			}

			// if dsp response with no AdCandidate, it will be a fake timeout
			if task.GetDspResponseCandidate() == nil {
				task.FilterByError(err_code.ErrBroadcastTimeout)
			} else {
				task.FilterByError(err_code.ErrBroadcastNoBidding)
			}
			task.UnlockWrite()
		}
	}

	return nil
}

func (manager *BroadcastManager) BroadcastTask(task *BroadcastTask) {
	if err := manager.doBroadcastTask(task); err != nil {
		if adErrCode, ok := err.(err_code.AdErrorCode); ok {
			task.FilterByError(adErrCode)
		} else {
			task.FilterByError(err_code.ErrBroadcastFail.Wrap(err))
		}
	}
}

func (manager *BroadcastManager) CreateBroadcastTaskMap(request *ad_service.AdRequest) BroadcastTaskMap {
	broadcastTaskMap := AcquireBroadcastTaskMap()
	dspCandidateMap := make(map[utils.ID]ad_service.AdCandidateList)

	// 首先确保 ad 和 dspSlot 的 QPS限制
	for _, candidate := range request.Response.GetAdCandidateList() {
		ad := candidate.GetAd()
		dspId := ad.DspId
		if ad.GetAdType() != entity.AdTypeDsp {
			if ad.GetQpsLimit() != 0 && manager.CheckAndAddAdQpsLimit(ad.GetAdId()) == false {
				candidate.FilterByError(err_code.ErrBroadcastAdQpsLimit)
			}
			continue
		}

		client := manager.dspClientMap[dspId]
		if client == nil {
			candidate.FilterByError(err_code.ErrDspNotFound)
			continue
		}

		candidate.SetIsDspReadyToSend(true)
		candidate.SetDspProtocol(client.dsp.Protocol)

		broadcastCandidate := candidate
		broadcastStrategy := broadcastCandidate.GetBroadcastStrategy()

		if broadcastStrategy == nil ||
			broadcastStrategy.GetBroadcastControlStrategy() == entity.BroadcastControlStrategyKnown ||
			broadcastStrategy.GetBroadcastControlStrategy() == entity.BroadcastControlStrategyDefault {
			if err := manager.BroadcastStrategyDefaultSchedule(client, request, ad_service.AdCandidateList{broadcastCandidate}); err != nil {
				candidate.FilterByError(err_code.ExtractError(err))
				continue
			}
		} else if broadcastStrategy.GetBroadcastControlStrategy() == entity.BroadcastControlStrategySmartScheduleV2 {
			if err := manager.BroadcastStrategySmartScheduleV2(client, request, ad_service.AdCandidateList{broadcastCandidate}); err != nil {
				candidate.FilterByError(err_code.ExtractError(err))
				continue
			}
		} else {
			candidate.FilterByError(err_code.ErrBroadcastControlStrategy)
			continue
		}

		if ad.GetQpsLimit() != 0 && manager.CheckAdQpsLimit(ad.GetAdId()) == false {
			candidate.FilterByError(err_code.ErrBroadcastAdQpsLimit)
			continue
		}

		if ad.GetDspSlotId() != 0 && manager.CheckDspSlotQpsLimit(ad.GetDspSlotId()) == false {
			candidate.FilterByError(err_code.ErrBroadcastDspSlotQpsLimit)
			continue
		}

		dspCandidateMap[dspId] = append(dspCandidateMap[dspId], candidate)

		candidate.SetIsDspScheduled(true)
	}

	dspCountMap := make(map[utils.ID]int)
	dspSlotIdMap := make(map[utils.ID]struct{})

	for dspId, candidateList := range dspCandidateMap {
		dspInfo := manager.dspClientMap[dspId]
		if dspInfo == nil {
			for _, candidate := range candidateList {
				candidate.FilterByError(err_code.ErrBroadcastNilDspClient)
			}
			continue
		}

		if dspInfo.dsp.QpsLimit != 0 && manager.CheckDspQpsLimit(dspId) == false {
			for _, candidate := range candidateList {
				candidate.FilterByError(err_code.ErrBroadcastQpsLimit)
			}
			continue
		}

		rand.Shuffle(len(candidateList), func(i, j int) {
			candidateList[i], candidateList[j] = candidateList[j], candidateList[i]
		})

		// remove duplicate dsp slot id, keep add task until fulfill
		for _, candidate := range candidateList {
			if _, ok := dspSlotIdMap[candidate.GetAd().GetDspSlotId()]; ok {
				candidate.FilterByError(err_code.ErrBroadcastNotSelectedSameSlot)
				continue
			}

			if dspInfo.MaxRequestCandidate() <= dspCountMap[dspId] {
				candidate.FilterByError(err_code.ErrBroadcastNotSelected)
				continue
			}

			// we create task for each ad,
			// so before we actually send the request to dsp, we need to select which ad to send
			broadcastTask := broadcastTaskMap.GetTask(request, dspId, candidate.GetAd().GetAdId(), dspInfo.dsp)
			broadcastTask.AddCandidate(candidate)

			dspSlotIdMap[candidate.GetAd().GetDspSlotId()] = struct{}{}
			dspCountMap[dspId]++
		}
	}

	return broadcastTaskMap
}

func (manager *BroadcastManager) doBroadcastTask(task *BroadcastTask) error {
	client := manager.dspClientMap[task.DspId]
	if client == nil {
		return err_code.ErrDspNotFound
	}

	if len(task.GetAdCandidateList()) == 0 {
		return nil
	}

	// here currently we only support to send one candidate in each request
	// if in the future we need multiple candidate for one request, we need to modify this part
	if len(task.AdCandidate) > 1 {
		if err := manager.selectBroadcastCandidate(task, 1); err != nil {
			return err
		}
	}

	for _, candidate := range task.GetAdCandidateList() {
		manager.AddDspSlotQpsLimit(candidate.GetDspSlotId())
		manager.AddAdQpsLimit(candidate.GetAd().GetAdId())
	}

	manager.AddDspQpsLimit(task.DspId)

	if err := client.Do(task); err != nil {
		return err
	}

	if len(task.GetDspResponseCandidate()) == 0 {
		return err_code.ErrBroadcastNoBidding
	}

	return nil
}

func (manager *BroadcastManager) selectBroadcastCandidate(task *BroadcastTask, count int) error {
	adCandidateList := task.GetAdCandidateList()
	rand.Shuffle(len(adCandidateList), func(i, j int) {
		adCandidateList[i], adCandidateList[j] = adCandidateList[j], adCandidateList[i]
	})

	task.SetAdCandidateList(adCandidateList[:count])

	for _, candidate := range adCandidateList[count:] {
		candidate.FilterByError(err_code.ErrBroadcastNotSelected)
	}

	return nil
}

func (manager *BroadcastManager) selectResponseCandidate(task *BroadcastTask) error {
	responseCandidateList := task.GetDspResponseCandidate()
	if len(responseCandidateList) == 0 {
		return err_code.ErrBroadcastNoBidding
	}

	if len(responseCandidateList) > 1 {
		rand.Shuffle(len(responseCandidateList), func(i, j int) {
			responseCandidateList[i], responseCandidateList[j] = responseCandidateList[j], responseCandidateList[i]
		})
	}

	responseCandidate := responseCandidateList[0]
	requestCandidate := task.GetAdCandidateList()[0]

	if len(responseCandidate.GetDspAdId()) == 0 {
		responseCandidate.SetDspAdID(requestCandidate.GetAd().AdId.String())
	}

	requestCandidate.SetDspAdId(responseCandidate.GetDspAdId())
	requestCandidate.SetDspProtocol(responseCandidate.GetDspProtocol())
	requestCandidate.SetDspResponseAd(responseCandidate.GetAd())

	requestCandidate.SetCreative(responseCandidate.GetCreative())
	requestCandidate.SetAdCandidateChargePriceEncoder(responseCandidate.GetAdCandidateChargePriceEncoder())
	requestCandidate.SetAdCandidateEncodeCBURL(responseCandidate.GetAdCandidateCallBackUrlEncoder())

	requestCandidate.SetDspBidPrice(entity.CreatePriceItem(responseCandidate.GetBidPrice(), responseCandidate.GetBidType()))

	// 非 CPM 出价的 DSP, 将在后续覆盖 SuggestedIncomePrice
	requestCandidate.SetSuggestedIncomePrice(entity.CreatePriceItem(responseCandidate.GetBidPrice(), responseCandidate.GetBidType()))

	if len(responseCandidate.GetDspTagList()) != 0 {
		manager.setDspUserSegment(requestCandidate, responseCandidate.GetDspTagList())
	}

	manager.dspPricingMetrics.Observe(requestCandidate.GetAd().GetLabelCache_DspId_AdId(), float64(responseCandidate.GetBidPrice()))

	return nil
}

func (manager *BroadcastManager) loop() {
	zap.L().Info("[BroadcastManager][loop] start")

	ticker := time.NewTicker(time.Second * 60)
	defer ticker.Stop()

	for {
		select {
		case <-manager.term:
			return
		case <-ticker.C:
			if err := manager.refreshDspInfo(); err != nil {
				zap.L().Error("[BroadcastManager][loop] err", zap.Error(err))
			}

			if err := manager.refreshDspRateLimiter(); err != nil {
				zap.L().Error("[BroadcastManager][loop] err", zap.Error(err))
			}

			if err := manager.refreshAdRateLimiter(); err != nil {
				zap.L().Error("[BroadcastManager][loop] err", zap.Error(err))
			}

			if err := manager.registerDspSlotInfo(); err != nil {
				zap.L().Error("[BroadcastManager][loop] err", zap.Error(err))
			}

			if err := manager.refreshDspSlotRateLimiter(); err != nil {
				zap.L().Error("[BroadcastManager][loop] err", zap.Error(err))
			}
		}
	}
}

func (manager *BroadcastManager) registerDspSlotInfo() error {
	clients := manager.dspClientMap
	totalSlot := manager.dspSlotInfoLoader.GetDspSlotInfoList()

	for _, client := range clients {
		broker := client.GetDspBroker()
		register := broker.GetDspSlotRegister()
		if register == nil {
			continue
		}

		if err := manager.registerDspSlotInfoToRegister(register, totalSlot); err != nil {
			zap.L().Error("[BroadcastManager][registerDspSlotInfo] broker:, err:%s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(broker.GetDspId())))), err)
		}
	}

	return nil
}

func (manager *BroadcastManager) refreshDspInfo() error {
	dspList := manager.dspLoader.GetDspList()
	for _, dsp := range dspList {
		if manager.dspClientMap[dsp.DspId] == nil {
			if err := manager.RegisterDsp(dsp); err != nil {
				zap.L().Error("[BroadcastManager][refreshDspInfo] err", zap.Error(err))
			}
		} else {
			client := manager.dspClientMap[dsp.DspId]
			if err := client.UpdateDspInfo(dsp); err != nil {
				zap.L().Error("[BroadcastManager][refreshDspInfo] err", zap.Error(err))
			}
		}
	}

	return nil

}

func (manager *BroadcastManager) registerDspSlotInfoToRegister(
	register dsp_slot_register.DspSlotRegisterInterface,
	totalSlot entity.DspSlotInfoList) error {
	filteredSlots := totalSlot.FilterByDspId(register.GetDspId())
	if err := register.UpdateDspSlotInfo(filteredSlots); err != nil {
		return err
	}

	zap.L().Info("[BroadcastManager][registerDspSlotInfoToRegister] dspId:, slotCount:%d", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(register.GetDspId())))), len(filteredSlots))
	return nil
}

func (manager *BroadcastManager) refreshDspRateLimiter() error {
	serviceCount := manager.serviceRegister.GetServiceCount("ad_server")
	newRateLimiter := make(map[utils.ID]*limiter.SimplePresetRateLimiter)
	rateLimiterRand := rand.Float64()

	zap.L().Info("[BroadcastManager][refreshDspRateLimiter] serviceCount:, dspRateLimiterRand:%f", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(serviceCount)))), zap.Int64("param2", int64(rateLimiterRand)))

	dspInfoList := manager.dspLoader.GetDspList()
	for _, dsp := range dspInfoList {
		limit := limiter.GetDistributedRateLimit(uint32(dsp.QpsLimit), uint32(serviceCount), rateLimiterRand)

		zap.L().Info("[BroadcastManager][refreshDspRateLimiter] dspId:, limit", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(dsp.DspId)))), zap.Int64("param2", int64(limit)))

		original := manager.dspRateLimiter[dsp.DspId]
		if original == nil {
			original = limiter.NewSimplePresetRateLimiter(limit)
		} else {
			original.SetLimit(limit)
		}

		newRateLimiter[dsp.DspId] = original
	}

	manager.dspRateLimiter = newRateLimiter

	return nil
}

func (manager *BroadcastManager) refreshAdRateLimiter() error {
	serviceCount := manager.serviceRegister.GetServiceCount("ad_server")
	newRateLimiter := make(map[utils.ID]*limiter.SimplePresetRateLimiter)
	adRateLimiterRand := rand.Float64()

	zap.L().Info("[BroadcastManager][refreshAdRateLimiter] serviceCount:, adRateLimiterRand:%f", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(serviceCount)))), zap.Int64("param2", int64(adRateLimiterRand)))

	adInfoList := manager.adLoader.GetAdList()
	for _, ad := range adInfoList {
		if ad.AdId == 0 {
			continue
		}

		limit := limiter.GetDistributedRateLimit(uint32(ad.QpsLimit), uint32(serviceCount), adRateLimiterRand)

		original := manager.adRateLimiter[ad.AdId]
		if original == nil {
			original = limiter.NewSimplePresetRateLimiter(limit)
		} else {
			original.SetLimit(limit)
		}

		weekHourLimit := ad.QpsLimitWeekHour
		if weekHourLimit != nil {
			now := time_utils.GetTimeNowTime()
			if v, ok := weekHourLimit[int(now.Weekday())*100+now.Hour()]; ok {
				original.SetLimit(limiter.GetDistributedRateLimit(v, uint32(serviceCount), adRateLimiterRand))
			}
		}

		newRateLimiter[ad.AdId] = original
	}

	manager.adRateLimiter = newRateLimiter

	return nil
}

func (manager *BroadcastManager) refreshDspSlotRateLimiter() error {
	serviceCount := manager.serviceRegister.GetServiceCount("ad_server")
	newRateLimiter := make(map[utils.ID]*limiter.SimplePresetRateLimiter)
	dspSlotRateLimiterRand := rand.Float64()

	zap.L().Info("[BroadcastManager][refreshDspSlotRateLimiter] serviceCount:, dspSlotRateLimiterRand:%f", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(serviceCount)))), zap.Int64("slotId", int64(dspSlotRateLimiterRand)))

	dspSlotList := manager.dspSlotInfoLoader.GetDspSlotInfoList()
	for _, dspSlot := range dspSlotList {
		if dspSlot.GetQps() == 0 {
			continue
		}

		limit := limiter.GetDistributedRateLimit(uint32(dspSlot.Qps), uint32(serviceCount), dspSlotRateLimiterRand)

		original := manager.dspSlotRateLimiter[dspSlot.Id]
		if original == nil {
			original = limiter.NewSimplePresetRateLimiter(limit)
		} else {
			original.SetLimit(limit)
		}

		newRateLimiter[dspSlot.Id] = original
	}

	manager.dspSlotRateLimiter = newRateLimiter
	return nil
}

func (manager *BroadcastManager) CheckDspQpsLimit(dspId utils.ID) bool {
	l := manager.dspRateLimiter[dspId]
	if l == nil {
		return false
	}

	return l.Check()
}

func (manager *BroadcastManager) AddDspQpsLimit(dspId utils.ID) {
	l := manager.dspRateLimiter[dspId]
	if l == nil {
		return
	}

	l.Add(1)
}

func (manager *BroadcastManager) CheckAndAddAdQpsLimit(adId utils.ID) bool {
	l := manager.adRateLimiter[adId]
	if l == nil {
		return false
	}

	return l.CheckAndAdd()
}

func (manager *BroadcastManager) CheckAdQpsLimit(adId utils.ID) bool {
	l := manager.adRateLimiter[adId]
	if l == nil {
		return false
	}

	return l.Check()
}

func (manager *BroadcastManager) AddAdQpsLimit(adId utils.ID) {
	l := manager.adRateLimiter[adId]
	if l == nil {
		return
	}

	l.Add(1)
}

func (manager *BroadcastManager) CheckDspSlotQpsLimit(dspSlotId utils.ID) bool {
	l := manager.dspSlotRateLimiter[dspSlotId]
	//不限制
	if l == nil {
		return true
	}

	return l.Check()
}

func (manager *BroadcastManager) AddDspSlotQpsLimit(dspSlotId utils.ID) {
	l := manager.dspSlotRateLimiter[dspSlotId]
	if l == nil {
		return
	}

	l.Add(1)
}

func (manager *BroadcastManager) setDspUserSegment(
	candidate *ad_service.AdCandidate,
	tagList []ad_service.DspTag) {
	trafficData := candidate.GetGroundTruthTrafficData()
	deviceId, _ := trafficData.GetDeviceIdWithType()

	for _, tag := range tagList {
		manager.userSegmentClient.AddUserSegmentAsync(deviceId, tag.Tag, 1, tag.ExpireSeconds, 0)
	}
}

func (manager *BroadcastManager) GetDspClient(dspId utils.ID) *BroadcastDspClient {
	client := manager.dspClientMap[dspId]

	return client
}

func (manager *BroadcastManager) BroadcastStrategyDefaultSchedule(client *BroadcastDspClient, request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) error {
	broadcastCandidate := candidateList[0]

	broadcastStrategy := broadcastCandidate.GetBroadcastStrategy()
	if broadcastStrategy == nil {
		return client.CheckBroadcastContext(request, candidateList)
	}

	trafficLevel := broadcastCandidate.GetTrafficLevel()
	broadcastControl := broadcastStrategy.GetBroadcastControlWithIndex(utils.EmptyString, float64(trafficLevel))
	if broadcastControl.BroadcastRatio < 1 {
		if rand.Float64() > broadcastControl.BroadcastRatio {
			return err_code.ErrBroadcastUserScoreNotEnough
		}
	}

	return client.CheckBroadcastContext(request, candidateList)
}

func (manager *BroadcastManager) BroadcastStrategySmartScheduleV2(client *BroadcastDspClient, request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) error {
	if manager.broadcastSchedulerV2 == nil {
		return manager.BroadcastStrategyDefaultSchedule(client, request, candidateList)
	}

	broadcastCandidate := candidateList[0]
	level := broadcastCandidate.GetTrafficLevel()

	shouldLog := rand.Int31()%1000 == 0

	id := broadcastCandidate.GetAd().GetAdId()
	mediaSlotId := request.GetMediaSlotId()
	mediaSlotType := request.GetSlotType()
	appBundle := strings.ToLower(request.App.AppBundle)
	dspSlotId := broadcastCandidate.GetAd().GetDspSlotId()

	scheduleV2 := manager.broadcastSchedulerV2.GetBroadcastSchedule(int32(id), int32(mediaSlotId), int32(mediaSlotType), appBundle, int32(dspSlotId), level)
	if scheduleV2 == nil {
		return err_code.ErrTrafficStrategyFiltered
	}

	metricsLabel := []string{"v2", broadcastCandidate.GetAd().GetAdId().String(), fmt.Sprintf("%02d", level/5*5)}
	manager.broadcastSchedulerPacingInMetrics.Add(metricsLabel, 1)

	if rand.Float64() > scheduleV2.RealPacingRate {
		return err_code.ErrTrafficStrategyFiltered
	}

	if scheduleV2.Limiter.CheckAndAdd() == false {
		return err_code.ErrTrafficStrategyFiltered
	}

	if shouldLog {
		//zap.L().Info("[BroadcastManager][BroadcastStrategySmartScheduleV1] id:, mediaSlotId:, mediaSlotType:, appBundle:%s, dspSlotId:, level:, RealPacingRate:%f, rand:%f, save, device:%s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(//	id)))), zap.Int64("id", int64(mediaSlotId)), zap.Int64("param3", int64(mediaSlotType)), zap.Int64("param4", int64(appBundle)), zap.Int64("id", int64(dspSlotId)), zap.Int64("param6", int64(level)), zap.Int64("param7", int64(scheduleV2.RealPacingRate)), zap.Int64("param8", int64(rand.Float64())), request.Device.GetDeviceId())
	}

	manager.broadcastSchedulerPacingOutMetrics.Add(metricsLabel, 1)
	return nil
}
