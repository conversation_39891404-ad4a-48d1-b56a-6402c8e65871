package broadcast_searcher

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/adglink_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/admate_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/adprof_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/adview_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/adx2345_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/b612_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/baidu_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/bayes_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/chengxiao_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/common_json_v2_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/csj_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/diansi_dsp_broker"
	didi_broker "gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/didi_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/doujing_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/doyy_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/error_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/fancy_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/fengzhi_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/fg_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/guangyin_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/huawei_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/huichuan_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/iqiyi_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jd_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jdtg3_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jdtg_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jingyi_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jlhz_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/kaijie_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/kaweiapu_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/keji_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/kuaikan_broker"
	kuasihou_broker "gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/kuaishou_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/lenovo_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/liyue_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/magic_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/mango_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/meitu_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/meituan_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/mifu_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/mosken_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/oppo_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/opt_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/pdd_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/qianzhan_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/qihang_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/qihoo360_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/quanku_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/ruixunda_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/shihuo_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/shuziyuedong_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/sigmob_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/sinax_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/tanx_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/topon_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/v3_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/ximalaya_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/xinghe_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/xunyu_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/yiguang_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/youju_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/youlianghui_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/youtui_dsp_broker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/zhihu_broker"
	"gitlab.com/dev/heidegger/library/entity"
)

func BuildDspBroker(dsp *entity.Dsp) (DspBrokerInterface, error) {
	var result DspBrokerInterface
	switch dsp.Protocol {
	case entity.DspProtoTypeOpt:
		result = opt_dsp_broker.NewOptDspBroker(dsp.DspId)
	case entity.DspProtoTypeV3:
		result = v3_dsp_broker.NewV3DspBroker(dsp.DspId)
	case entity.DspProtoTypeKuaikan:
		result = kuaikan_broker.NewKuaiKanDspBroker(dsp.DspId)
	case entity.DspProtoTypeMeitu:
		result = meitu_broker.NewMeituDspBroker(dsp.DspId)
	case entity.DspProtoTypeQihang, entity.DspProtoTypeQihangCPA:
		result = qihang_broker.NewQihangDspBroker(dsp.DspId)
	case entity.DspProtoTypeTanx, entity.DspProtoTypeTanxNew:
		result = tanx_broker.NewTanxDspBroker(dsp.DspId)
	case entity.DspProtoTypeMeiTuan:
		result = meituan_broker.NewMeiTuanDspBroker(dsp.DspId)
	case entity.DspProtoTypePdd:
		result = pdd_broker.NewPddDspBroker(dsp.DspId)
	case entity.DspProtoCommonJsonV2:
		result = common_json_v2_dsp_broker.NewCommonJsonV2nDspBroker(dsp.DspId)
	case entity.DspProtoTypeKeJi:
		result = keji_broker.NewKeJiDspBroker(dsp.DspId)
	case entity.DspProtoTypeJd:
		result = jd_broker.NewJdPBDspBroker(dsp.DspId)
	case entity.DspProtoTypeHuichuan:
		result = huichuan_broker.NewHuichuanDspBroker(dsp.DspId)
	case entity.DspProtoTypeZhihu:
		result = zhihu_broker.NewZhihuDspBroker(dsp.DspId)
	case entity.DspProtoTypeSinax:
		result = sinax_dsp_broker.NewSinaXDspBroker(dsp.DspId)
	case entity.DspProtoTypeDidi:
		result = didi_broker.NewDiDiDspBroker(dsp.DspId)
	case entity.DspProtoTypeHuawei:
		result = huawei_dsp_broker.NewHuaWeiDspBroker(dsp.DspId)
	case entity.DspProtoTypeYouLiangHui:
		result = youlianghui_dsp_broker.NewYLHDspBroker(dsp.DspId)
	case entity.DspProtoTypeShiHuo:
		result = shihuo_dsp_broker.NewShiHuoDspBroker(dsp.DspId)
	case entity.DspProtoTypeYouJu:
		result = youju_dsp_broker.NewYouJuDspBroker(dsp.DspId)
	case entity.DspProtoTypeBaiduECB:
		result = baidu_dsp_broker.NewBaiduDspBrokerECB(dsp.DspId)
	case entity.DspProtoTypeBaiduCBC:
		result = baidu_dsp_broker.NewBaiduDspBrokerCBC(dsp.DspId)
	case entity.DspProtoTypeLenovo:
		result = lenovo_dsp_broker.NewLenovoDspBroker(dsp.DspId)
	case entity.DspProtoTypeFG, entity.DspProtoTypeInner:
		result = fg_dsp_broker.NewFGDspBroker(dsp.DspId)
	case entity.DspProtoTypeInnerJson:
		result = fg_dsp_broker.NewInnerJsonDspBroker(dsp.DspId)
	case entity.DspProtoTypeKS:
		result = kuasihou_broker.NewKuaiShouDspBroker(dsp.DspId)
	case entity.DspProtoTypeQianZhan:
		result = qianzhan_dsp_broker.NewQianZhanDspBroker(dsp.DspId)
	case entity.DspProtoTypeLiYue:
		result = liyue_dsp_broker.NewLiYueDspBroker(dsp.DspId)
	case entity.DspProtoTypeXingHe:
		result = xinghe_dsp_broker.NewXingHeDspBroker(dsp.DspId)
	case entity.DspProtoTypeQuanKu:
		result = quanku_dsp_broker.NewQuanKuDspBroker(dsp.DspId)
	case entity.DspProtoTypeQuanKuPb:
		result = quanku_dsp_broker.NewQuanKuPbDspBroker(dsp.DspId)
	case entity.DspProtoTypeFancy:
		result = fancy_dsp_broker.NewFancyDspBroker(dsp.DspId)
	case entity.DspProtoTypeDouJing:
		result = doujing_dsp_broker.NewDouJingDspBroker(dsp.DspId)
	case entity.DspProtoTypeKaiJie:
		result = kaijie_dsp_broker.NewKaiJieDspBroker(dsp.DspId)
	case entity.DspProtoTypeMiFu:
		result = mifu_dsp_broker.NewMiFuDspBroker(dsp.DspId)
	case entity.DspProtoTypeCSJ:
		result = csj_dsp_broker.NewCSJDspBroker(dsp.DspId)
	case entity.DspProtoTypeXunYu:
		result = xunyu_dsp_broker.NewXunYuDspBroker(dsp.DspId)
	case entity.DspProtoTypeIQiYi:
		result = iqiyi_dsp_broker.NewIQiYiDspBroker(dsp.DspId)
	case entity.DspProtoTypeAdview:
		result = adview_dsp_broker.NewAdviewDspBroker(dsp.DspId)
	case entity.DspProtoTypeFengZhi:
		result = fengzhi_dsp_broker.NewFengZhiDspBroker(dsp.DspId)
	case entity.DspProtoTypeJingYi:
		result = jingyi_dsp_broker.NewJingYiDspBroker(dsp.DspId)
	case entity.DspProtoTypeB612:
		result = b612_dsp_broker.NewB612DspBroker(dsp.DspId)
	case entity.DspProtoTypeYiGuang:
		result = yiguang_dsp_broker.NewYiGuangDspBroker(dsp.DspId)
	case entity.DspProtoTypeSigmob:
		result = sigmob_dsp_broker.NewSigmobDspBroker(dsp.DspId)
	case entity.DspProtoTypeOppo:
		result = oppo_dsp_broker.NewOppoDspBroker(dsp.DspId)
	case entity.DspProtoTypeBayes:
		result = bayes_dsp_broker.NewBayesDspBroker(dsp.DspId)
	case entity.DspProtoTypeXMLY:
		result = ximalaya_dsp_broker.NewXimalayaDspBroker(dsp.DspId)
	case entity.DspProtoTypeDianSi:
		result = diansi_dsp_broker.NewDianSiDspBroker(dsp.DspId)
	case entity.DspProtoTypeYouTui:
		result = youtui_dsp_broker.NewYouTuiDspBroker(dsp.DspId)
	case entity.DspProtoTypeJlhz:
		result = jlhz_dsp_broker.NewJlhzDspBroker(dsp.DspId)
	case entity.DspProtoTypeAdMate:
		result = admate_dsp_broker.NewAdMateDspBroker(dsp.DspId)
	case entity.DspProtoType360:
		result = qihoo360_dsp_broker.NewQihoo360DspBroker(dsp.DspId)
	case entity.DspProtoTypeShuZiYueDong:
		result = shuziyuedong_dsp_broker.NewShuZiYueDongDspBroker(dsp.DspId)
	case entity.DspProtoTypeAdprof:
		result = adprof_dsp_broker.NewAdprofDspBroker(dsp.DspId)
	case entity.DspProtoTypeJdTg:
		result = jdtg_dsp_broker.NewJdTgDspBroker(dsp.DspId)
	case entity.DspProtoTypeJdTg3:
		result = jdtg3_dsp_broker.NewJdTg3DspBroker(dsp.DspId)
	case entity.DspProtoTypeTopOn:
		result = topon_dsp_broker.NewTopOnDspBroker(dsp.DspId)
	case entity.DspProtoTypeKaweiapu:
		result = kaweiapu_dsp_broker.NewKaweiapuDspBroker(dsp.DspId)
	case entity.DspProtoTypeAdGlink:
		result = adglink_dsp_broker.NewAdGlinkDspBroker(dsp.DspId)
	case entity.DspProtoTypeMagic:
		result = magic_dsp_broker.NewMagicDspBroker(dsp.DspId)
	case entity.DspProtoTypeChengXiao:
		result = chengxiao_dsp_broker.NewChengXiaoDspBroker(dsp.DspId)
	case entity.DspProtoTypeDoyy:
		result = doyy_dsp_broker.NewDoyyDspBroker(dsp.DspId)
	case entity.DspProtoTypeMango:
		result = mango_dsp_broker.NewMangoDspBroker(dsp.DspId)
	case entity.DspProtoTypeRuiXunDa:
		result = ruixunda_dsp_broker.NewRuiXunDaDspBroker(dsp.DspId)
	case entity.DspProtoTypeMosken:
		result = mosken_dsp_broker.NewMoskenDspBroker(dsp.DspId)
	case entity.DspProtoTypeGuangYin:
		result = guangyin_dsp_broker.NewGuangYinDspBroker(dsp.DspId)
	case entity.DspProtoTypeAdx2345:
		result = adx2345_dsp_broker.NewAdx2345DspBroker(dsp.DspId)
	default:
		zap.L().Error("[BuildDspBroker] unknown protocol:%s, id", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(dsp.Protocol)))), zap.Int64("id", int64(dsp.DspId)))
		result = error_dsp_broker.NewErrorDspBroker(dsp.DspId)
	}

	if err := result.UpdateDspInfo(dsp); err != nil {
		return nil, err
	}

	return result, nil
}
