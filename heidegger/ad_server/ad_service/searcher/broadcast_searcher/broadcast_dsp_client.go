package broadcast_searcher

import (
	"fmt"
	"crypto/tls"
	"net/http"
	"time"

	"github.com/sony/gobreaker"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_broker/jd_broker"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/dsp_slot_register"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_sampler"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/app_list_loader"
	"gitlab.com/dev/heidegger/library/mclock"
	"gitlab.com/dev/heidegger/library/price/price_manager"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/user_segment"
)

const (
	qpsLimitWindow = 2
)

type DspBrokerInterface interface {
	GetDspId() utils.ID
	UpdateDspInfo(dsp *entity.Dsp) error
	BuildRequest(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) (*http.Request, error)
	ParseResponse(request *ad_service.AdRequest, broadcastCandidateList ad_service.AdCandidateList, response *http.Response) (ad_service.DspAdCandidateList, error)
	GetDspSlotRegister() dsp_slot_register.DspSlotRegisterInterface
	SetTrafficSampler(trafficSampler traffic_sampler.TrafficSampler)
	SetPriceManager(priceManager *price_manager.PriceManager)

	CheckBroadcastContext(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) error
	SetUserSegmentClient(client user_segment.UserSegmentClient)
	SetExternalMappingLoader(externalMappingLoader app_list_loader.ExternalMappingLoader)
	SetAppListLoader(appListLoader app_list_loader.AppListLoader)
}

type BroadcastDspClient struct {
	dsp               *entity.Dsp
	dspBroker         DspBrokerInterface
	client            *http.Client
	trafficSampler    traffic_sampler.TrafficSampler
	priceManager      *price_manager.PriceManager
	userSegmentClient user_segment.UserSegmentClient

	appListLoader         app_list_loader.AppListLoader
	externalMappingLoader app_list_loader.ExternalMappingLoader

	dspHistogram   *prometheus_helper.LabelHistogram
	circuitBreaker *gobreaker.CircuitBreaker
}

func NewBroadcastDspClient(dsp *entity.Dsp) *BroadcastDspClient {
	timeOut := dsp.Timeout

	if timeOut == 0 {
		timeOut = 500
	}

	if timeOut > 1000 {
		timeOut = 1000
	}

	//cb := gobreaker.NewCircuitBreaker(gobreaker.Settings{
	//	Name:     fmt.Sprintf("Broadcast Breaker, DspId:%s, Name:%s", dsp.DspId, dsp.Name),
	//	Timeout:  5 * time.Second, // 熔断后的等待时间
	//	Interval: 5 * time.Minute, // 每 1 分钟重置统计数据
	//	ReadyToTrip: func(counts gobreaker.Counts) bool {
	//		total := float64(counts.Requests)
	//		failures := float64(counts.TotalFailures)
	//		if total > 100 && (failures/total) > 0.10 {
	//			return true
	//		}
	//		return false
	//	},
	//	OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
	//		zap.L().Info("[BroadcastDspClient] CircuitBreaker state change, name:, from:, to", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", name)))), zap.String("param2", fmt.Sprintf("%v", from)), zap.String("param3", fmt.Sprintf("%v", to)))
	//	},
	//})

	return &BroadcastDspClient{
		dsp: dsp,
		// here we set the timeout to the max timeout we could have
		// the actual process timeout is controlled outside the client call with timer
		// this prevents frequent disconnect when server overload
		client: &http.Client{
			Transport: &http.Transport{
				MaxIdleConns:          4096,
				MaxIdleConnsPerHost:   2048,
				MaxConnsPerHost:       1024 * 32, // as a connection working at 0.5s latency, provides 16k qps
				IdleConnTimeout:       time.Second * 60,
				ResponseHeaderTimeout: time.Millisecond * 500,
				TLSNextProto:          make(map[string]func(authority string, c *tls.Conn) http.RoundTripper),
			},
			Timeout: time.Millisecond * time.Duration(timeOut),
		},
		dspHistogram: prometheus_helper.RegisterLabelHistogramTimeoutBucket("ad_server_dsp_client", []string{"dsp_id", "code", "human", "dsp_slot_id", "ad_id"}),
	}
}

func (client *BroadcastDspClient) GetDspBroker() DspBrokerInterface {
	return client.dspBroker
}

func (client *BroadcastDspClient) SetExternalMappingLoader(externalMappingLoader app_list_loader.ExternalMappingLoader) {
	client.externalMappingLoader = externalMappingLoader
}

func (client *BroadcastDspClient) SetAppListLoader(appListLoader app_list_loader.AppListLoader) {
	client.appListLoader = appListLoader
}

func (client *BroadcastDspClient) SetTrafficSampler(sampler traffic_sampler.TrafficSampler) {
	client.trafficSampler = sampler
}

func (client *BroadcastDspClient) SetPriceManager(priceManager *price_manager.PriceManager) {
	client.priceManager = priceManager
}

func (client *BroadcastDspClient) SetUserSegmentClient(c user_segment.UserSegmentClient) {
	client.userSegmentClient = c
}

func (client *BroadcastDspClient) UpdateDspInfo(dsp *entity.Dsp) error {
	client.dsp = dsp
	return client.dspBroker.UpdateDspInfo(dsp)
}

func (client *BroadcastDspClient) Start() error {
	broker, err := BuildDspBroker(client.dsp)
	if err != nil {
		return err
	}

	broker.SetPriceManager(client.priceManager)
	broker.SetTrafficSampler(client.trafficSampler)
	broker.SetUserSegmentClient(client.userSegmentClient)
	broker.SetExternalMappingLoader(client.externalMappingLoader)
	broker.SetAppListLoader(client.appListLoader)
	if err := broker.UpdateDspInfo(client.dsp); err != nil {
		return err
	}

	client.dspBroker = broker
	return nil
}

func (client *BroadcastDspClient) Stop() {

}

func (client *BroadcastDspClient) MaxRequestCandidate() int {
	maxRequest := client.dsp.MaxRequestCandidate
	if maxRequest <= 0 {
		maxRequest = 1
	}

	return maxRequest
}

func (client *BroadcastDspClient) Do(task *BroadcastTask) error {
	timeStart := mclock.Now()

	labels := [5]string{client.dsp.DspId.String(), err_code.Success.CodeStr(), err_code.Success.Human(), task.GetAdCandidateList()[0].GetDspSlotId().String(), task.GetAdCandidateList()[0].GetAd().GetAdId().String()}

	var err error

	if client.circuitBreaker != nil {
		_, err = client.circuitBreaker.Execute(func() (interface{}, error) {
			err := client.do(task)
			return nil, err
		})
	} else {
		err = client.do(task)
	}

	timeElapseSeconds := float64(mclock.Now()-timeStart) / 1e9

	if err != nil {
		labels[1] = err_code.GetCode(err).String()
		labels[2] = err_code.GetHumanReadable(err)
		client.dspHistogram.Observe(labels[:], timeElapseSeconds)
	} else {
		client.dspHistogram.Observe(labels[:], timeElapseSeconds)
	}
	return err
}

func (client *BroadcastDspClient) CheckBroadcastContext(request *ad_service.AdRequest, candidateList ad_service.AdCandidateList) error {
	if err := client.dspBroker.CheckBroadcastContext(request, candidateList); err != nil {
		return err_code.ErrBroadcastDspContextFilter.Wrap(err)
	}
	return nil
}

func (client *BroadcastDspClient) do(task *BroadcastTask) error {
	request, err := client.dspBroker.BuildRequest(task.AdRequest, task.AdCandidate)
	if err != nil {
		return err
	}

	for _, candidate := range task.GetAdCandidateList() {
		candidate.SetIsDspRequested(true)
	}

	resp, err := client.client.Do(request)
	if err != nil {
		zap.L().Debug("[BroadcastDspClient][Do] err:, dsp:%s()", zap.Error(err), zap.Int64("param2", int64(client.dsp.Name)), zap.Int64("id", int64(client.dsp.DspId)))
		return err_code.ErrBroadcastNetworkFail
	}
	defer resp.Body.Close()

	// lock task write, the manager timeout procedure will check the task status
	task.LockWrite()
	defer task.UnlockWrite()

	if task.IsFiltered() {
		return task.GetError()
	}

	responseCandidate, err := client.dspBroker.ParseResponse(task.AdRequest, task.AdCandidate, resp)
	if err != nil {
		client.ProcessDspScore(task, nil, err)
		if err_code.IsAdErrorCode(err) {
			return err
		}
		return err_code.ErrBroadcastNetworkFail.Wrap(err)
	}

	task.SetDspResponseCandidate(responseCandidate)
	client.ProcessDspScore(task, responseCandidate, nil)

	return nil
}

func (client *BroadcastDspClient) ProcessDspScore(task *BroadcastTask, candidateList ad_service.DspAdCandidateList, err error) {
	if client.dsp.UserScoreType == entity.UserScoreTypeV1 {
		client.ProcessDspScoreV1(task, candidateList, err)
	}
}

func (client *BroadcastDspClient) ProcessDspScoreV1(task *BroadcastTask, candidateList ad_service.DspAdCandidateList, err error) {
	if len(task.GetAdCandidateList()) == 0 {
		return
	}
	userScore := jd_broker.JdUserScore{}
	broadcastCandidate := task.GetAdCandidateList()[0]

	if err != nil || len(candidateList) == 0 {
		userScore.UpdateUserScore(
			client.userSegmentClient,
			task.AdRequest.Device.GetDeviceId(),
			uint32(broadcastCandidate.GetDspSlotId()),
			1,
			0,
			0)
	} else {
		candidate := candidateList[0]
		userScore.UpdateUserScore(
			client.userSegmentClient,
			task.AdRequest.Device.GetDeviceId(),
			uint32(broadcastCandidate.GetDspSlotId()),
			1,
			1,
			candidate.GetBidPrice(),
		)
	}
}
