package dmp_searcher

import (
	"context"
	"encoding/json"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/dmp_cache"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_manager"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/user_segment/user_segment_entity"
	"fmt"
)

type DmpSearcher struct {
	dmpCache   *dmp_cache.DmpCache
	dmpManager *dmp_manager.DmpManager
}

func NewDmpSearcher(dmpCache *dmp_cache.DmpCache, dmpManager *dmp_manager.DmpManager) *DmpSearcher {
	return &DmpSearcher{
		dmpCache:   dmpCache,
		dmpManager: dmpManager,
	}
}

func (s *DmpSearcher) GetTaskName() string {
	return "DmpSearcher"
}

func (s *DmpSearcher) Start() error {
	return nil
}

func (s *DmpSearcher) Stop() {

}

func (s *DmpSearcher) CheckIndexRtaIds(request *ad_service.AdRequest, ad *entity.Ad) error {
	if len(request.RtaIds) == 0 {
		return nil
	}

	dmpRequirementList := ad.GetDmpTagRequirementItemList()
	if len(dmpRequirementList) == 0 {
		return err_code.ErrAdNotIndexRtaIds
	}

	rtaIdMap := make(map[utils.ID]struct{})
	for _, rtaId := range request.RtaIds {
		rtaIdMap[rtaId] = struct{}{}
	}

	for _, requirement := range dmpRequirementList {
		if _, ok := rtaIdMap[requirement.DmpTagId]; ok {
			return nil
		}
	}
	return err_code.ErrAdNotIndexRtaIds
}

func (s *DmpSearcher) Do(request *ad_service.AdRequest) error {
	dmpRequirement := entity.NewDmpTagRequirement()

	for _, candidate := range request.Response.GetAdCandidateList() {
		ad := candidate.GetAd()
		err := s.CheckIndexRtaIds(request, ad)
		if err != nil {
			candidate.FilterByError(err_code.ErrAdNotIndexRtaIds)
			continue
		}
		dmpRequirementList := ad.GetDmpTagRequirementItemList()
		if len(dmpRequirementList) == 0 {
			continue
		}

		dmpRequirement.AddItemList(dmpRequirementList)
	}

	if dmpRequirement.NoRequirement() {
		return nil
	}
	if request.IsDebug {
		queryBody, _ := json.Marshal(dmpRequirement)
		zap.L().Info("[DmpSearcher] get dmp data, query:, debug", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", queryBody)))), zap.String("param2", fmt.Sprintf("%v", request.IsDebug)))
	}

	result, dmpRequestErr := s.dmpCache.GetDmpData(context.TODO(), CreateDmpRequestWrapper(request.GetRequestId(), request, request.IsDebug), dmpRequirement)

	if request.IsDebug {
		resultBody, _ := json.Marshal(result)
		zap.L().Info("[DmpSearcher] get dmp data, result", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", resultBody)))))
	}

	for _, candidate := range request.Response.GetAdCandidateList() {
		ad := candidate.GetAd()
		dmpRequirementList := ad.GetDmpTagRequirementItemList()
		if len(dmpRequirementList) == 0 {
			request.Response.PushCandidates(candidate)
			continue
		} else {
			ad.SetDmpTagResult(result)
			dmpCheckErr := dmpRequestErr
			if dmpCheckErr == nil {
				dmpCheckErr = s.checkDmpResult(result, candidate)
			}
			if dmpCheckErr != nil {
				candidate.FilterByError(err_code.ErrDmpFilter.Wrap(dmpCheckErr))
				continue
			} else {
				request.Response.PushCandidates(candidate)
				continue
			}
		}
	}

	request.Response.SwapAndClearCandidate()

	if request.Response.NoCandidate() {
		request.Response.SetError(err_code.ErrDmpFilter)
	}

	return nil
}

func (s *DmpSearcher) checkDmpResult(result *entity.DmpTagRequirement, candidate *ad_service.AdCandidate) error {
	dmpRequirementList := candidate.GetAd().GetDmpTagRequirementItemList()
	if len(dmpRequirementList) == 0 {
		return nil
	}

	if result == nil {
		return err_code.ErrDmpTagNegative
	}

	for _, requirement := range dmpRequirementList {
		if result.IsSatisfied(requirement) {
			continue
		} else {
			return err_code.ErrDmpTagNegative
		}
	}

	return nil
}

type DmpRequestWrapper struct {
	*ad_service.AdRequestDevice
	adRequest *ad_service.AdRequest
	requestId string
	IsDebug   bool
}

func CreateDmpRequestWrapper(requestId string, adRequest *ad_service.AdRequest, isDebug bool) *DmpRequestWrapper {
	return &DmpRequestWrapper{
		AdRequestDevice: &adRequest.Device,
		adRequest:       adRequest,
		requestId:       requestId,
		IsDebug:         isDebug,
	}
}

func (w *DmpRequestWrapper) GetRequestId() string {
	return w.requestId
}

func (w *DmpRequestWrapper) GetIsDebug() bool {
	return w.IsDebug
}

func (w *DmpRequestWrapper) GetUserSegment() *user_segment_entity.UserSegmentRedisValue {
	return &w.adRequest.UserSegment
}
