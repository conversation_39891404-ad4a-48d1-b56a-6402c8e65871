package index_searcher

import (
	"strings"
	"time"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/bitmap_index"
	"gitlab.com/dev/heidegger/library/bitmap_index_v2"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/ad_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/hash_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"fmt"
)

const (
	FieldIdMediaId = bitmap_index.FieldId(iota)
	FieldIdMediaSlotId
	FieldIdWeekHour
	FieldIdGeoCode
	FieldIdOsType
	FieldIdDeviceType
	FieldIdSlotType
	FieldIdWidthHeight
	FieldIdDeviceDetail
	FieldIdDeviceBucket
	FieldIdIp
	FieldIdDeviceId
	FieldIdCaidVersion
	FieldIdAppPackage
	FieldIdDate
	FieldIdInstalledApp
	FieldIdOsVersion
	FieldIdBrandModel
	FieldIdMediaTemplate
	FieldIdMediaSlotKey
	FieldIdBrand
)

func fieldToId(field string) bitmap_index.FieldId {
	switch field {
	case "media_id":
		return FieldIdMediaId
	case "media_slot_id":
		return FieldIdMediaSlotId
	case "week_hour":
		return FieldIdWeekHour
	case "geo_code":
		return FieldIdGeoCode
	case "os_type":
		return FieldIdOsType
	case "device_type":
		return FieldIdDeviceType
	case "slot_type":
		return FieldIdSlotType
	case "width_height":
		return FieldIdWidthHeight
	case "device_detail":
		return FieldIdDeviceDetail
	case "device_bucket":
		return FieldIdDeviceBucket
	case "ip":
		return FieldIdIp
	case "device_id":
		return FieldIdDeviceId
	case "caid":
		return FieldIdCaidVersion
	case "package":
		return FieldIdAppPackage
	case "date":
		return FieldIdDate
	case "app_list":
		return FieldIdInstalledApp
	case "os_version":
		return FieldIdOsVersion
	case "brand_model":
		return FieldIdBrandModel
	case "media_template":
		return FieldIdMediaTemplate
	case "media_slot_key":
		return FieldIdMediaSlotKey
	case "device_brand":
		return FieldIdBrand
	default:
		return -1
	}
}

type IndexSearcher struct {
	adLoader       ad_loader.AdLoader
	indexManager   *bitmap_index.IndexManager
	indexManagerV2 *bitmap_index_v2.IndexManager

	term chan struct{}
}

func NewIndexSearcher(adLoader ad_loader.AdLoader) *IndexSearcher {
	return &IndexSearcher{
		adLoader: adLoader,
		term:     make(chan struct{}),
	}
}

func (s *IndexSearcher) GetTaskName() string {
	return "IndexSearcher"
}

func (s *IndexSearcher) Start() error {
	if err := s.BuildIndexV2(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-s.term:
				return
			case <-time.After(time.Second * 60):
				if err := s.BuildIndexV2(); err != nil {
					zap.L().Error("[IndexSearcher] BuildIndexV2 error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (s *IndexSearcher) Stop() {
	close(s.term)
}

func (s *IndexSearcher) BuildIndexV2() error {
	adList := s.adLoader.GetAdList()
	indexManager := bitmap_index_v2.NewIndexManager()

	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdMediaId), bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdMediaSlotId), bitmap_index_v2.FieldTypeString)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdWeekHour), bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdGeoCode), bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdOsType), bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdDeviceType), bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdSlotType), bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdWidthHeight), bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdDeviceDetail), bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdDeviceBucket), bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdIp), bitmap_index_v2.FieldTypeString)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdDeviceId), bitmap_index_v2.FieldTypeString)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdCaidVersion), bitmap_index_v2.FieldTypeString)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdAppPackage), bitmap_index_v2.FieldTypeString)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdDate), bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdInstalledApp), bitmap_index_v2.FieldTypeString)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdOsVersion), bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdBrandModel), bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdMediaTemplate), bitmap_index_v2.FieldTypeString)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdMediaSlotKey), bitmap_index_v2.FieldTypeString)
	indexManager.MustAddField(bitmap_index_v2.FieldId(FieldIdBrand), bitmap_index_v2.FieldTypeString)

	for _, ad := range adList {
		adIndex := ad.GetAdIndex()
		if adIndex == nil {
			zap.L().Debug("[IndexSearcherV2] adIndex not found, adid", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.GetAdId())))))
			continue
		}

		doc := bitmap_index_v2.Doc{
			DocId: bitmap_index_v2.Id(ad.GetAdId()),
		}

		for field, value := range adIndex.TargetStr {
			inclusive := true
			if strings.HasPrefix(field, "!") {
				inclusive = false
				field = strings.TrimPrefix(field, "!")
			}

			fieldId := fieldToId(field)
			if fieldId == -1 {
				zap.L().Warn("[IndexSearcherV2] field: not found", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", field)))))
				continue
			}

			doc.AddStringTarget(bitmap_index_v2.FieldId(fieldId), value, inclusive)
		}

		for field, value := range adIndex.TargetInt {
			if field == "week_hour_qps_limit" { //跟week_hour绑定，这里跳过
				continue
			}
			inclusive := true
			if strings.HasPrefix(field, "!") {
				inclusive = false
				field = strings.TrimPrefix(field, "!")
			}

			fieldId := fieldToId(field)
			if fieldId == -1 {
				zap.L().Warn("[IndexSearcherV2] field: not found", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", field)))))
				continue
			}

			doc.AddIntTarget(bitmap_index_v2.FieldId(fieldId), value, inclusive)
		}

		if err := indexManager.AddDoc(&doc); err != nil {
			zap.L().Warn("[IndexSearcherV2] indexManager.AddDoc error: , adid", zap.Error(err), zap.Int64("id", int64(ad.GetAdId())))
			return err
		}
	}

	if err := indexManager.BuildIndex(); err != nil {
		return err
	}

	s.indexManagerV2 = indexManager
	zap.L().Info("[IndexSearcherV2] buildIndex success, doc count", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(len(adList))))))
	return nil
}

func (s *IndexSearcher) SearchIndexV2(request *ad_service.AdRequest) ([]bitmap_index_v2.Id, error) {
	if s.indexManagerV2 == nil {
		return nil, err_code.ErrIndexNotReady
	}

	indexCtx := s.indexManagerV2.CreateContext()
	defer indexCtx.Release()

	for {
		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, bitmap_index_v2.FieldId(FieldIdMediaId), int64(request.GetMediaId())); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		//media_id + "_" + media_slot_id
		mediaSlotIndexValue := request.GetSourceSlotId()
		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexString(indexCtx, bitmap_index_v2.FieldId(FieldIdMediaSlotId), mediaSlotIndexValue); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		now := time.Now()
		weekHour := int(now.Weekday())*100 + now.Hour()
		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, bitmap_index_v2.FieldId(FieldIdWeekHour), int64(weekHour)); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, bitmap_index_v2.FieldId(FieldIdDate), int64(time_utils.GetTimeNow20060102Int())); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64List(indexCtx, bitmap_index_v2.FieldId(FieldIdGeoCode), []int64{
			int64(request.GeoCode),
			int64(request.GeoCode) / 1e6 * 1e6,
			int64(request.GeoCode) / 1e9 * 1e9}); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, bitmap_index_v2.FieldId(FieldIdOsType), int64(request.Device.OsType)); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, bitmap_index_v2.FieldId(FieldIdDeviceType), int64(request.Device.DeviceType)); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if request.SlotType != entity.SlotTypeAttribution && request.GetMediaSlotInfo().HasSlotTag(entity.SlotTagBackgroundS2S) {
			if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64List(indexCtx, bitmap_index_v2.FieldId(FieldIdSlotType), []int64{int64(request.SlotType), int64(entity.SlotTypeAttribution)}); err != nil {
				return nil, err_code.ErrIndexFailed.Wrap(err)
			} else if !hasCandidate {
				break
			}
		} else {
			if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, bitmap_index_v2.FieldId(FieldIdSlotType), int64(request.SlotType)); err != nil {
				return nil, err_code.ErrIndexFailed.Wrap(err)
			} else if !hasCandidate {
				break
			}
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, bitmap_index_v2.FieldId(FieldIdWidthHeight), int64(request.SlotWidth*1000+request.SlotHeight)); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		deviceId := request.Device.GetDeviceId()
		deviceIdBucket := int64(0)
		if len(deviceId) > 0 {
			// FNV1a is more evenly distributed than xxHash
			deviceIdBucket = int64(hash_utils.FNV1a32Hash(deviceId))
		}
		deviceIdBucket = deviceIdBucket%10 + 1

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, bitmap_index_v2.FieldId(FieldIdDeviceBucket), deviceIdBucket); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		deviceIdTypes := s.GetDeviceIdTypes(request)
		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64List(indexCtx, bitmap_index_v2.FieldId(FieldIdDeviceDetail), deviceIdTypes); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexString(indexCtx, bitmap_index_v2.FieldId(FieldIdIp), request.Device.GetRequestIp()); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexString(indexCtx, bitmap_index_v2.FieldId(FieldIdDeviceId), request.Device.GetDeviceId()); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexString(indexCtx, bitmap_index_v2.FieldId(FieldIdCaidVersion), device_utils.GetCaidVersion(request.Device.GetCaid())); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexString(indexCtx, bitmap_index_v2.FieldId(FieldIdAppPackage), request.App.AppBundle); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		installedApp := make([]string, 0)
		for _, app := range request.App.InstalledApp {
			installAppIn := request.GetMediaId().String() + "_" + app
			installedApp = append(installedApp, installAppIn)
		}

		for _, app := range request.App.MediaInstalledAppIds {
			installAppIn := request.GetMediaId().String() + "_" + app
			installedApp = append(installedApp, installAppIn)
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexStringList(indexCtx, bitmap_index_v2.FieldId(FieldIdInstalledApp), installedApp); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, bitmap_index_v2.FieldId(FieldIdOsVersion), int64(request.Device.UserAgentParser.OsVersionId)); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64List(indexCtx, bitmap_index_v2.FieldId(FieldIdBrandModel), []int64{int64(request.Device.UserAgentParser.BrandId), int64(request.Device.UserAgentParser.BrandModelId)}); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexStringList(indexCtx, bitmap_index_v2.FieldId(FieldIdBrand), []string{request.Device.Brand}); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		//media_id + "_" + media_template
		mediaTemplateList := make([]string, 0)
		for _, mediaTemplate := range request.AdxTemplateId {
			mediaTemplateList = append(mediaTemplateList, request.GetMediaId().String()+"_"+mediaTemplate)
		}
		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexStringList(indexCtx, bitmap_index_v2.FieldId(FieldIdMediaTemplate), mediaTemplateList); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := s.indexManagerV2.DoIndexString(indexCtx, bitmap_index_v2.FieldId(FieldIdMediaSlotKey), request.GetMediaSlotKey()); err != nil {
			return nil, err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		break
	}

	adIdList := indexCtx.GetDocs()
	return adIdList, nil
}

func (s *IndexSearcher) Do(request *ad_service.AdRequest) error {
	adIdList, err := s.SearchIndexV2(request)
	if err != nil {
		zap.L().Error("[IndexSearcher] SearchIndexV2 error", zap.Error(err))
		return err
	}

	for _, adId := range adIdList {
		ad := s.adLoader.GetAdById(utils.ID(adId))
		if ad == nil {
			zap.L().Debug("[IndexSearcher] ad not found, adid", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(adId)))))
			continue
		}
		request.Response.AddCandidates(ad)
	}
	request.Response.SwapAndClearCandidate()

	for _, candidate := range request.Response.GetAdCandidateList() {
		if candidate.GetAd().GetAdGroup() == nil {
			candidate.FilterByError(err_code.ErrAdGroupNotFound)
		}

		if candidate.GetAd().AdType == entity.AdTypeAttribution || candidate.GetAd().AdType == entity.AdTypeLocal {
			if candidate.GetAd().AdId == 1030 {
				request.SetIsSampled(true)
			}

			candidate.SetIsSampled(request.GetIsSampled())
		}

		request.Response.PushCandidates(candidate)
	}

	request.Response.SwapAndClearCandidate()

	if request.Response.NoCandidate() {
		request.Response.SetError(err_code.ErrNoCandidate)
	}

	return nil
}

func (s *IndexSearcher) GetDeviceIdTypes(request *ad_service.AdRequest) []int64 {
	deviceDetailIndex := make([]int64, 0, 9)

	if request.Device.DeviceType == entity.DeviceTypeOtt {
		if len(request.Device.MacMd5) > 0 {
			deviceDetailIndex = append(deviceDetailIndex, int64(DeviceIdTypeMacMd5))
		}
		if len(request.Device.Mac) > 0 {
			deviceDetailIndex = append(deviceDetailIndex, int64(DeviceIdTypeMac))
		}
	} else if request.Device.DeviceType == entity.DeviceTypePc {
		if len(request.UserId) > 0 {
			deviceDetailIndex = append(deviceDetailIndex, int64(DeviceIdTypeCookie))
		}
	} else {
		if request.Device.OsType == entity.OsTypeIOS {
			if len(request.Device.IdfaMd5) > 0 {
				deviceDetailIndex = append(deviceDetailIndex, int64(DeviceIdTypeIdfaMd5))
			}
			if len(request.Device.Idfa) > 0 {
				deviceDetailIndex = append(deviceDetailIndex, int64(DeviceIdTypeIdfa))
			}

			if len(request.Device.Aaid) > 0 {
				deviceDetailIndex = append(deviceDetailIndex, int64(DeviceIdTypeAaid))
			}

			if len(request.Device.Caid) > 0 {
				deviceDetailIndex = append(deviceDetailIndex, int64(DeviceIdTypeCaid))
			}
		} else {
			if len(request.Device.ImeiMd5) > 0 {
				deviceDetailIndex = append(deviceDetailIndex, int64(DeviceIdTypeImeiMd5))
			}
			if len(request.Device.OaidMd5) > 0 {
				deviceDetailIndex = append(deviceDetailIndex, int64(DeviceIdTypeOaidMd5))
			}

			if len(request.Device.Imei) > 0 {
				deviceDetailIndex = append(deviceDetailIndex, int64(DeviceIdTypeImei))
			}

			if len(request.Device.Oaid) > 0 {
				deviceDetailIndex = append(deviceDetailIndex, int64(DeviceIdTypeOaid))
			}
		}
	}

	return deviceDetailIndex
}

func (s *IndexSearcher) checkEqual(adIdList []bitmap_index.Id, adIdListV2 []bitmap_index_v2.Id) {
	if len(adIdList) != len(adIdListV2) {
		zap.L().Error("[IndexSearcher] checkEqual error, adIdList:, adIdListV2:%d", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(len(adIdList))))), len(adIdListV2))
		return
	}

	for i := 0; i < len(adIdList); i++ {
		if int(adIdList[i]) != int(adIdListV2[i]) {
			zap.L().Error("[IndexSearcher] checkEqual error, adIdList:, adIdListV2", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", adIdList)))), zap.String("param2", fmt.Sprintf("%v", adIdListV2)))
			return
		}
	}
}
