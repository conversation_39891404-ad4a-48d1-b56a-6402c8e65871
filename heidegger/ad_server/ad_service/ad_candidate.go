package ad_service

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/ad_service_entity"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/ad_server/ad_service/traffic_modifier/traffic_device_allocator"
	"gitlab.com/dev/heidegger/ad_server/ad_service/traffic_modifier/traffic_request_modifier_manager"
	"gitlab.com/dev/heidegger/ad_server/pacing_service/pacing_entity"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker"
	"gitlab.com/dev/heidegger/frequency/frequency_service"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/entity/traffic_strategy_entity"
	"gitlab.com/dev/heidegger/library/macro_builder"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/tracking/tracking_gen"
	"strconv"
	"sync"
)

type AdCandidateChargePriceEncoder func(chargePrice uint32) string
type AdCandidateEncodeCBURL func(url string) string

var (
	adCandidatePool = sync.Pool{New: func() any {
		return &AdCandidate{}
	}}
)

type AdCandidate struct {
	request *AdRequest
	ad      *entity.Ad

	// dspSlotId 如果某个流程手动set了 dspSlotId
	// 那么我们优先使用 candidate中的 slotId, 否则在非修改场景下直接食用 ad.GetDspSlotId()
	dspSlotId     utils.ID
	dspResponseAd *entity.Ad
	dspAdId       string
	dspProtocol   string
	dspSlotKey    string
	userScore     int32
	trafficLevel  int32

	creative                  entity.GenericCreative
	mainMaterial              *entity.Material
	selectedMaterialList      entity.MaterialList
	activeCreativeTemplateKey creative_entity.CreativeTemplateKey
	selectedTemplateType      int

	// bidPrice 向媒体竞价价格，会参考 ad 与 dspResponseAd 中的价格
	bidPrice entity.PriceItem

	// suggestedIncomePrice 参考收入价, 用于 ecpm 计算, 用于cpc, cpa 场景, cpm 情况下 等于 dspBidPrice
	// 部分消耗方, 会同时给出 CPM 与 CPA 价格, 此时 将非CPM价格存入 suggestedIncomePrice
	// 默认与 dspBidPrice 一致
	suggestedIncomePrice entity.PriceItem

	// dspBidPrice dsp向我们竞价价格，需要在Broadcast过程中填入
	dspBidPrice entity.PriceItem

	// bidFloor 竞价底价，会参考 ad 与 dspResponseAd 中的底价
	bidFloor entity.PriceItem

	cpcBidFloor entity.PriceItem

	// chargePrice 计费价格
	chargePrice entity.PriceItem

	adCandidateChargePriceEncoder AdCandidateChargePriceEncoder
	adCandidateEncodeCBURL        AdCandidateEncodeCBURL

	artificialLandingRate float64
	artificialClickRate   float64

	frequencyResult frequency_service.FrequencyResult

	pacingRate  float64
	pacingCount int32

	deviceAlloc          *traffic_device_allocator.DeviceAlloc
	TrafficStrategyChain traffic_strategy_entity.TrafficStrategyChain

	indexDeal *entity.DealInfo

	code    int
	adError err_code.AdErrorCode

	groundTruthData GroundTruthTrafficData

	pacingDecision pacing_entity.PacingDecision
	rankerResult   ranker.RankerResult

	isDspReadyToSend  bool
	isDspScheduled    bool
	isDspRequested    bool
	isShadowedClick   bool
	isSampled         bool
	dspRequestSample  []byte
	dspResponseSample []byte
}

func NewAdCandidate(request *AdRequest, ad *entity.Ad) *AdCandidate {
	result := &AdCandidate{
		request: request,
		ad:      ad,
	}

	result.groundTruthData = CreateGroundTruthTrafficData(result)
	return result
}

func NewAdCandidateWithPool(request *AdRequest, ad *entity.Ad) *AdCandidate {
	result := adCandidatePool.Get().(*AdCandidate)
	result.request = request
	result.ad = ad
	result.groundTruthData = CreateGroundTruthTrafficData(result)
	return result
}

func (a *AdCandidate) Release() {
	a.Reset()
	adCandidatePool.Put(a)
}

func (a *AdCandidate) Reset() {
	*a = AdCandidate{}
}

func (a *AdCandidate) IsValid() bool {
	return a != nil && a.ad != nil && a.GetErrorCode().Code == err_code.Success.Code
}

// GetGenericAd
// 这里获取的是一个经过所有逻辑加工的Ad，应该要结合本地Ad，DspResponseAd，和 TrafficModifier
// 从而对外形成一个Ad黑盒
func (a *AdCandidate) GetGenericAd() entity.GenericAd {
	return ad_service_entity.NewServiceAd(a.GetAd(), a.GetDspResponseAd())
}

func (a *AdCandidate) GetAd() *entity.Ad {
	return a.ad
}

func (a *AdCandidate) GetDspSlotId() utils.ID {
	if a.dspSlotId != 0 {
		return a.dspSlotId
	}
	return a.GetAd().GetDspSlotId()
}

func (a *AdCandidate) GetDspSlotKey() string {
	return a.dspSlotKey
}

func (a *AdCandidate) GetDspSlotKeyByOs(os entity.OsType) string {
	if a.GetAd().GetDspSlotInfo() != nil {
		return a.GetAd().GetDspSlotInfo().GetDspSlotIdByOs(os)
	}

	return ""
}

func (a *AdCandidate) SetDspSlotId(dspSlotId utils.ID) {
	a.dspSlotId = dspSlotId
}

func (a *AdCandidate) SetDspSlotKey(dspSlotKey string) {
	a.dspSlotKey = dspSlotKey
}

func (a *AdCandidate) SetUserScore(score int32) {
	a.userScore = score
}

func (a *AdCandidate) GetUserScore() int32 {
	return a.userScore
}

func (a *AdCandidate) SetTrafficLevel(level int32) {
	a.trafficLevel = level
}

func (a *AdCandidate) GetTrafficLevel() int32 {
	return a.trafficLevel
}

func (a *AdCandidate) SetDspResponseAd(dspResponseAd *entity.Ad) {
	a.dspResponseAd = dspResponseAd
}

func (a *AdCandidate) GetDspResponseAd() *entity.Ad {
	return a.dspResponseAd
}

func (a *AdCandidate) SetDspAdId(dspAdId string) {
	a.dspAdId = dspAdId
}

func (a *AdCandidate) GetDspAdId() string {
	return a.dspAdId
}

func (a *AdCandidate) SetDspProtocol(dspProtocol string) {
	a.dspProtocol = dspProtocol
}

func (a *AdCandidate) GetDspProtocol() string {
	return a.dspProtocol
}

func (a *AdCandidate) SetCreative(creative entity.GenericCreative) {
	a.creative = creative
}

func (a *AdCandidate) SetMainMaterial(material *entity.Material) {
	a.mainMaterial = material
}

func (a *AdCandidate) SetSelectedMaterialList(materialList entity.MaterialList) {
	if len(materialList) == 0 {
		return
	}

	a.selectedMaterialList = materialList
}

func (a *AdCandidate) GetSelectedMaterialList() entity.MaterialList {
	return a.selectedMaterialList
}

func (a *AdCandidate) SetActiveCreativeTemplateKey(key creative_entity.CreativeTemplateKey) {
	a.activeCreativeTemplateKey = key
}

func (a *AdCandidate) GetActiveCreativeTemplateKey() creative_entity.CreativeTemplateKey {
	return a.activeCreativeTemplateKey
}

func (a *AdCandidate) SetSelectedTemplateType(templatekey int) {
	a.selectedTemplateType = templatekey
}

func (a *AdCandidate) GetSelectedTemplateType() int {
	return a.selectedTemplateType
}

func (a *AdCandidate) GetMainMaterial() *entity.Material {
	return a.mainMaterial
}

func (a *AdCandidate) GetCreative() entity.GenericCreative {
	return a.creative
}

func (a *AdCandidate) GetCode() int {
	return a.code
}

func (a *AdCandidate) GetAdRequest() *AdRequest {
	return a.request
}

func (a *AdCandidate) GetGroundTruthTrafficData() ad_service_entity.TrafficData {
	return a.groundTruthData
}

func (a *AdCandidate) GetModifiedTrafficData() ad_service_entity.TrafficData {
	var result ad_service_entity.TrafficData
	result = a.groundTruthData

	if a.deviceAlloc != nil {
		result = traffic_device_allocator.CreateDeviceAllocTrafficData(result, a.deviceAlloc)
	}

	if a.GetTrafficRequestModifier() != nil {
		result = traffic_request_modifier_manager.CreateTrafficRequestModifierData(result, a.GetTrafficRequestModifier())
	}

	return result
}

func (a *AdCandidate) SetDeviceAlloc(deviceAlloc *traffic_device_allocator.DeviceAlloc) {
	a.deviceAlloc = deviceAlloc
}

func (a *AdCandidate) GetDeviceAlloc() *traffic_device_allocator.DeviceAlloc {
	return a.deviceAlloc
}

func (a *AdCandidate) SetIndexDeal(indexDeal *entity.DealInfo) {
	a.indexDeal = indexDeal
}

func (a *AdCandidate) GetIndexDeal() *entity.DealInfo {
	return a.indexDeal
}

func (a *AdCandidate) AppendTrafficStrategy(trafficStrategy *entity.TrafficStrategy) {
	a.TrafficStrategyChain.Append(trafficStrategy)
}

func (a *AdCandidate) InitTrafficStrategy() error {
	return a.TrafficStrategyChain.Init()
}

func (a *AdCandidate) GetTrafficStrategyList() entity.TrafficStrategyList {
	return a.TrafficStrategyChain.StrategyList
}

func (a *AdCandidate) GetPricingStrategy() *entity.PricingStrategy {
	return a.TrafficStrategyChain.GetPricingStrategy()
}

func (s *AdCandidate) GetBroadcastStrategy() *entity.BroadcastStrategy {
	return s.TrafficStrategyChain.GetBroadcastStrategy()
}

func (a *AdCandidate) GetTrafficRequestModifier() *entity.TrafficRequestModifier {
	if a.TrafficStrategyChain.HasTrafficRequestModifierCache() {
		return a.TrafficStrategyChain.GetTrafficRequestModifierCache()
	}

	return a.TrafficStrategyChain.GetTrafficRequestModifierWithCache(entity.TrafficStrategyTargetQuery{
		Timestamp:   uint64(a.request.RequestTime.UnixNano()),
		OsType:      a.GetGroundTruthTrafficData().GetOsType(),
		GeoCode:     a.GetGroundTruthTrafficData().GetGeoCode(),
		DspId:       0,
		DspSlotId:   0,
		CreativeKey: utils.EmptyString,
	})
}

func (a *AdCandidate) GetTrafficResponseModifier() *entity.TrafficResponseModifier {
	if a.TrafficStrategyChain.HasTrafficResponseModifierCache() {
		return a.TrafficStrategyChain.GetTrafficResponseModifierCache()
	}

	query := entity.TrafficStrategyTargetQuery{
		Timestamp: uint64(a.request.RequestTime.UnixNano()),
		OsType:    a.GetGroundTruthTrafficData().GetOsType(),
		GeoCode:   a.GetGroundTruthTrafficData().GetGeoCode(),
		DspId:     a.GetAd().DspId,
		DspSlotId: a.GetDspSlotId(),
	}

	creative := a.GetCreative()
	if creative != nil {
		query.CreativeKey = creative.GetCreativeKey()
	} else {
		query.CreativeKey = utils.EmptyString
	}

	return a.TrafficStrategyChain.GetTrafficResponseModifierWithCache(query)
}

func (a *AdCandidate) GetTrafficStrategyChain() *traffic_strategy_entity.TrafficStrategyChain {
	return &a.TrafficStrategyChain
}

func (a *AdCandidate) IsFiltered() bool {
	return a.code != 0
}

func (a *AdCandidate) FilterByError(err err_code.AdErrorCode) {
	a.code = int(err.Code)
	a.adError = err
}

func (a *AdCandidate) GetErrorCode() err_code.AdErrorCode {
	return a.adError
}

func (a *AdCandidate) ReplaceUrlMacroList(url []string, trafficData ad_service_entity.TrafficData, trackingGen *tracking_gen.TrackingGen) []string {
	result := make([]string, 0, len(url))
	for _, u := range url {
		result = append(result, a.ReplaceUrlMacro(u, trafficData, trackingGen))
	}
	return result
}

func (a *AdCandidate) ReplaceUrlMacro(url string, trafficData ad_service_entity.TrafficData, trackingGen *tracking_gen.TrackingGen) string {
	return a.ReplaceUrlGeneral(macro_builder.ReplaceMacro(url, trafficData, trackingGen))
}

func (a *AdCandidate) ReplaceUrlGeneral(url string) string {
	if a.request.UseHttps != true {
		return url
	}

	return macro_builder.ReplaceHttpToHttps(url)
}

func (a *AdCandidate) GetTrackingGen(trafficData ad_service_entity.TrafficData) *tracking_gen.TrackingGen {
	return tracking_gen.CreateTrackingGen(
		trafficData,
		a.groundTruthData,
		a)
}

func (a *AdCandidate) SetBidPrice(bidPrice entity.PriceItem) {
	a.bidPrice = bidPrice
}

func (a *AdCandidate) GetBidPrice() entity.PriceItem {
	return a.bidPrice
}

func (a *AdCandidate) SetDspBidPrice(dspBidPrice entity.PriceItem) {
	a.dspBidPrice = dspBidPrice
}

func (a *AdCandidate) GetDspBidPrice() entity.PriceItem {
	return a.dspBidPrice
}

func (a *AdCandidate) SetChargePrice(chargePrice entity.PriceItem) {
	a.chargePrice = chargePrice
}

func (a *AdCandidate) GetChargePrice() entity.PriceItem {
	return a.chargePrice
}

func (a *AdCandidate) SetAdCandidateChargePriceEncoder(encoder AdCandidateChargePriceEncoder) {
	a.adCandidateChargePriceEncoder = encoder
}

func (a *AdCandidate) GetAdCandidateChargePriceEncoder() AdCandidateChargePriceEncoder {
	return a.adCandidateChargePriceEncoder
}

func (a *AdCandidate) SetAdCandidateEncodeCBURL(encoder AdCandidateEncodeCBURL) {
	a.adCandidateEncodeCBURL = encoder
}
func (a *AdCandidate) GetAdCandidateCallBackUrlEncoder() AdCandidateEncodeCBURL {
	return a.adCandidateEncodeCBURL
}

func (a *AdCandidate) SetBidFloor(bidFloor entity.PriceItem) {
	a.bidFloor = bidFloor
}

func (a *AdCandidate) GetBidFloor() entity.PriceItem {
	return a.bidFloor
}

func (a *AdCandidate) SetCpcBidFloor(bidFloor entity.PriceItem) {
	a.cpcBidFloor = bidFloor
}

func (a *AdCandidate) GetCpcBidFloor() entity.PriceItem {
	return a.cpcBidFloor
}

func (a *AdCandidate) GetEncodedChargePrice() string {
	if a.chargePrice.Price < 0 {
		zap.L().Error("chargePrice is negative, chargePrice", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(a.chargePrice.Price)))))
		return "0"
	}

	if a.adCandidateChargePriceEncoder == nil {
		return strconv.FormatInt(int64(a.chargePrice.Price), 10)
	}

	return a.adCandidateChargePriceEncoder(uint32(a.chargePrice.Price))
}

func (a *AdCandidate) GetEncodeCBURL(url string) string {
	if a.adCandidateEncodeCBURL == nil {
		return url
	}

	return a.adCandidateEncodeCBURL(url)
}

func (a *AdCandidate) GetMediaMacro() *macro_builder.MediaMacro {
	return a.request.AdRequestMedia.MediaMacro
}

func (a *AdCandidate) SetSuggestedIncomePrice(price entity.PriceItem) {
	a.suggestedIncomePrice = price
}

func (a *AdCandidate) GetSuggestedIncomePrice() entity.PriceItem {
	return a.suggestedIncomePrice
}

func (a *AdCandidate) GetArtificialLandingRate() float64 {
	return a.artificialLandingRate
}

func (a *AdCandidate) SetArtificialLandingRate(rate float64) {
	a.artificialLandingRate = rate
}

func (a *AdCandidate) GetArtificialClickRate() float64 {
	return a.artificialClickRate
}

func (a *AdCandidate) SetArtificialClickRate(rate float64) {
	a.artificialClickRate = rate
}

func (a *AdCandidate) GetShouldDoArtificialClick() bool {
	return utils.RandomFloat64() < a.GetArtificialClickRate()
}

func (a *AdCandidate) GetShouldDoArtificialLanding() bool {
	return utils.RandomFloat64() < a.GetArtificialLandingRate()
}

func (a *AdCandidate) SetIsDspReadyToSend(v bool) {
	a.isDspReadyToSend = v
}

func (a *AdCandidate) GetIsDspReadyToSend() bool {
	return a.isDspReadyToSend
}

func (a *AdCandidate) SetIsDspScheduled(v bool) {
	a.isDspScheduled = v
}

func (a *AdCandidate) GetIsDspScheduled() bool {
	return a.isDspScheduled
}

func (a *AdCandidate) SetIsDspRequested(v bool) {
	a.isDspRequested = v
}

func (a *AdCandidate) GetIsDspRequested() bool {
	return a.isDspRequested
}

func (a *AdCandidate) SetIsSampled(v bool) {
	a.isSampled = v
}

func (a *AdCandidate) GetIsSampled() bool {
	return a.isSampled
}

func (a *AdCandidate) SetDspRequestSample(data []byte) {
	a.dspRequestSample = data
}

func (a *AdCandidate) GetDspRequestSample() []byte {
	return a.dspRequestSample
}

func (a *AdCandidate) SetDspResponseSample(data []byte) {
	a.dspResponseSample = data
}

func (a *AdCandidate) GetDspResponseSample() []byte {
	return a.dspResponseSample
}

func (a *AdCandidate) SetPacingDecision(decision pacing_entity.PacingDecision) {
	a.pacingDecision = decision
}

func (a *AdCandidate) GetPacingDecision() *pacing_entity.PacingDecision {
	return &a.pacingDecision
}

func (a *AdCandidate) SetRankerResult(result ranker.RankerResult) {
	a.rankerResult = result
}

func (a *AdCandidate) GetRankerResult() *ranker.RankerResult {
	return &a.rankerResult
}

func (a *AdCandidate) SetIsShadowedClick(v bool) {
	a.isShadowedClick = v
}

func (a *AdCandidate) GetIsShadowedClick() bool {
	return a.isShadowedClick
}

// ==============================quick==============================
func (a *AdCandidate) MacroReplace(url string) string {
	return a.ReplaceUrlMacro(
		url,
		a.GetModifiedTrafficData(),
		a.GetTrackingGen(a.GetModifiedTrafficData()))
}
func (a *AdCandidate) MacroReplaceList(url []string) []string {
	result := make([]string, 0, len(url))
	for _, u := range url {
		result = append(result, a.MacroReplace(u))
	}
	return result
}

func (a *AdCandidate) GetLandingUrl() string {
	//return a.MacroReplace(a.GetGenericAd().GetLandingUrl())
	return a.GetGenericAd().GetLandingUrl()
}
func (a *AdCandidate) GetDeepLinkUrl() string {
	//return a.MacroReplace(a.GetGenericAd().GetDeepLinkUrl())
	return a.GetGenericAd().GetDeepLinkUrl()
}
func (a *AdCandidate) GetMacroReplaceLandingUrl() string {
	//return a.MacroReplace(a.GetGenericAd().GetLandingUrl())
	return a.MacroReplace(a.GetGenericAd().GetLandingUrl())
}
func (a *AdCandidate) GetMacroReplaceDeepLinkUrl() string {
	//return a.MacroReplace(a.GetGenericAd().GetDeepLinkUrl())
	return a.MacroReplace(a.GetGenericAd().GetDeepLinkUrl())
}
func (a *AdCandidate) GetMacroReplaceBidFailed() string {
	return a.MacroReplace(a.GetGenericAd().GetBidFailed())
}
func (a *AdCandidate) GetMacroReplaceImpressionMonitorList() []string {
	return a.MacroReplaceList(a.GetGenericAd().GetImpressionMonitorList())
}
func (a *AdCandidate) GetMacroReplaceClickMonitorList() []string {
	return a.MacroReplaceList(a.GetGenericAd().GetClickMonitorList())
}
func (a *AdCandidate) GetMacroReplaceDeepLinkMonitorList() []string {
	return a.MacroReplaceList(append(a.GetGenericAd().GetDeepLinkMonitorList(), a.GetGenericAd().GetDpSuccess()))
}
func (a *AdCandidate) GetMacroReplaceAppInstallStartMonitorList() []string {
	return a.MacroReplaceList(a.GetGenericAd().GetAppInstallStartMonitorList())
}
func (a *AdCandidate) GetMacroReplaceAppInstalledMonitorList() []string {
	return a.MacroReplaceList(a.GetGenericAd().GetAppInstalledMonitorList())
}
func (a *AdCandidate) GetMacroReplaceAppDownloadStartedMonitorList() []string {
	return a.MacroReplaceList(a.GetGenericAd().GetAppDownloadStartedMonitorList())
}
func (a *AdCandidate) GetMacroReplaceAppDownloadFinishedMonitorList() []string {
	return a.MacroReplaceList(a.GetGenericAd().GetAppDownloadFinishedMonitorList())
}

func (a *AdCandidate) GetVideoStartUrlList() []string {
	return a.GetGenericAd().GetVideoStartUrlList()
}
func (a *AdCandidate) GetVideoCloseUrlList() []string {
	return a.GetGenericAd().GetVideoCloseUrlList()
}

func (a *AdCandidate) GetAppInfo() *entity.AppInfo {
	return a.GetGenericAd().GetAppInfo()
}
func (a *AdCandidate) GetLandingAction() entity.LandingType {
	return a.GetGenericAd().GetLandingAction()
}
