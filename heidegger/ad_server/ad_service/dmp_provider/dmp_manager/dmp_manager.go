package dmp_manager

import (
	"errors"
	"math/rand/v2"
	"time"

	"github.com/panjf2000/ants/v2"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity_loader/dmp_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/limiter"
	"gitlab.com/dev/heidegger/master_server/master_server"
	"fmt"
)

var (
	ErrorDmpNotFound = errors.New("dmp not found")
	ErrorDmpOverload = errors.New("dmp overload")
)

type DmpClient interface {
	GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error)
	SetConfigString(config string) error
}

type DmpAsyncCallback func(err error, tags []string)

type DmpManager struct {
	dmpProviderLoader dmp_loader.DmpProviderLoader
	serviceRegister   master_server.ServiceRegister
	dmpClients        map[utils.ID]DmpClient
	dmpQpsLimiter     map[utils.ID]*limiter.SimplePresetRateLimiter

	worker *ants.Pool
	term   chan struct{}
}

func NewDmpManager(dmpProviderLoader dmp_loader.DmpProviderLoader, serviceRegister master_server.ServiceRegister) *DmpManager {
	worker, err := ants.NewPool(1024, ants.WithNonblocking(true))
	if err != nil {
		panic(err)
	}

	return &DmpManager{
		dmpClients:        make(map[utils.ID]DmpClient),
		dmpProviderLoader: dmpProviderLoader,
		serviceRegister:   serviceRegister,

		worker: worker,
		term:   make(chan struct{}),
	}
}

func (d *DmpManager) Start() error {
	if err := d.reloadDmpClient(); err != nil {
		return err
	}

	go d.loop()

	return nil
}

func (d *DmpManager) Stop() {
	close(d.term)
}

func (d *DmpManager) GetDmpClient(dmpId utils.ID) DmpClient {
	return d.dmpClients[dmpId]
}

func (d *DmpManager) QueryAsync(data dmp_provider.DmpRequestData, dmpId utils.ID, queryKeys []string, callback DmpAsyncCallback) {
	err := d.worker.Submit(func() {
		tags, err := d.Query(data, dmpId, queryKeys)
		callback(err, tags)
	})
	if err != nil {
		callback(ErrorDmpOverload, nil)
	}
}

func (d *DmpManager) Query(data dmp_provider.DmpRequestData, dmpId utils.ID, queryKeys []string) ([]string, error) {
	client := d.dmpClients[dmpId]
	if client == nil {
		zap.L().Error("[DmpManager] dsp not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(dmpId)))))
		return nil, ErrorDmpNotFound
	}
	qpsLimiter := d.dmpQpsLimiter[dmpId]
	// check QPS limit
	if qpsLimiter != nil && !qpsLimiter.CheckAndAdd() {
		return nil, err_code.ErrDmpQpsLimit
	}

	return client.GetDmpData(data, queryKeys)
}

func (d *DmpManager) reloadDmpClient() error {
	serviceCount := d.serviceRegister.GetServiceCount("ad_server")
	rateLimiterRand := rand.Float64()
	rateLimiterMap := make(map[utils.ID]*limiter.SimplePresetRateLimiter)

	dmpProviders := d.dmpProviderLoader.GetDmpProviderList()
	dmpClientMap := make(map[utils.ID]DmpClient)
	for _, dmpProvider := range dmpProviders {
		original := d.GetDmpClient(dmpProvider.DmpId)
		if original == nil {
			client, err := BuildDmpClient(dmpProvider.Protocol, dmpProvider.DmpId, dmpProvider.DmpConfig)
			if err != nil {
				zap.L().Error("[DmpManager] build dmp client error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
				continue
			}
			dmpClientMap[dmpProvider.DmpId] = client
		} else {
			if err := original.SetConfigString(dmpProvider.DmpConfig); err != nil {
				zap.L().Error("[DmpManager][reloadDmpClient] SetConfigString error", zap.Error(err))
				continue
			}

			dmpClientMap[dmpProvider.DmpId] = original
		}

		if dmpProvider.QpsLimit > 0 {
			limit := limiter.GetDistributedRateLimit(uint32(dmpProvider.QpsLimit), uint32(serviceCount), rateLimiterRand)
			oldLimiter := d.dmpQpsLimiter[dmpProvider.DmpId]
			if oldLimiter == nil {
				oldLimiter = limiter.NewSimplePresetRateLimiter(limit)
			} else {
				oldLimiter.SetLimit(limit)
			}
			rateLimiterMap[dmpProvider.DmpId] = oldLimiter
		}
	}

	d.dmpClients = dmpClientMap
	d.dmpQpsLimiter = rateLimiterMap
	return nil
}

func (d *DmpManager) loop() {
	ticker := time.NewTicker(time.Minute * 3)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := d.reloadDmpClient(); err != nil {
				zap.L().Error("[DmpManager][loop] reloadDmpClient error", zap.Error(err))
			}
		case <-d.term:
			return
		}
	}
}
