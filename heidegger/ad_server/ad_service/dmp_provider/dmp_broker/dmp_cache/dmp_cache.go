package dmp_cache

import (
	"context"
	"sync"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_manager"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type DmpCache struct {
	dmpManager *dmp_manager.DmpManager
}

func NewDmpCache(dmpManager *dmp_manager.DmpManager) *DmpCache {
	return &DmpCache{
		dmpManager: dmpManager,
	}
}

func (d *DmpCache) GetDmpData(ctx context.Context, requestData dmp_provider.DmpRequestData, requirement *entity.DmpTagRequirement) (*entity.DmpTagRequirement, error) {
	dmpCacheData, err := d.getDmpCacheData(requestData.GetDeviceId())
	if err != nil {
		return nil, err
	}

	for i := 0; i != dmpCacheData.GetCount(); i++ {
		element, err := dmpCacheData.GetElement(i)
		if err != nil {
			zap.L().Error("[DmpCache][GetDmpData] GetElement error", zap.Error(err))
			break
		}

		requirement.MarkById(utils.ID(element.Id), entity.DmpTagTypeCache)
	}

	wg := sync.WaitGroup{}
	for source, sourceDmpTagList := range requirement.Source {
		queryTagList := sourceDmpTagList.GetQueryTagList()
		if len(queryTagList) == 0 {
			continue
		}

		wg.Add(1)
		//zap.L().Info("[DmpManager] query dmp source", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(source)))))
		d.dmpManager.QueryAsync(requestData, source, queryTagList, func(err error, tags []string) {
			defer wg.Done()
			if err != nil {
				//zap.L().Error("[DmpManager] QueryAsync dsp error:, err:%s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(source)))), zap.Int64("param2", int64(err.Error())))
				return
			}

			for _, tag := range tags {
				requirement.MarkBySourceQueryTag(source, tag, entity.DmpTagTypeRealtime)
			}
		})
	}

	wg.Wait()

	return requirement, nil
}

func (d *DmpCache) getDmpCacheData(deviceId string) (DmpCacheData, error) {
	return DmpCacheData{}, nil
}
