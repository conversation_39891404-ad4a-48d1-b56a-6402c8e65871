package iqiyi_dmp

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/go-querystring/query"
	"go.uber.org/zap"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/simple_kv_data"
)

type IQiyiDmp struct {
	dmpId   utils.ID
	url     string
	timeOut time.Duration

	httpClient *fasthttp.Client
}

type IQiyiRtaRequest struct {
	Appid     string `url:"appid"`
	Aids      string `url:"aids"`
	NeedReqid string `url:"need_reqid"`
	Oaid      string `url:"oaid,omitempty"`
	Imei      string `url:"imei,omitempty"` //设备imei（MD5后转大写）
	Ip        string `url:"ip,omitempty"`
}

type IQiyiRtaResponse struct {
	Code   int    `json:"code"`
	Msg    string `json:"msg"`
	Bid    int    `json:"bid"`
	RawStr string `json:"-"`
}

func NewIQiyiDmp(dmpId utils.ID) *IQiyiDmp {
	return &IQiyiDmp{
		dmpId:      dmpId,
		httpClient: net_utils.CreateFastHttpClient(time.Second*3, time.Second*60),
	}
}

func (d *IQiyiDmp) SetConfigString(config string) error {
	kv := simple_kv_data.SimpleKeyValue{}
	if err := kv.UnmarshalJson([]byte(config)); err != nil {
		return err
	}

	url, err := kv.GetString("url")
	if err != nil {
		return err
	}

	timeout, err := kv.GetInt64("timeout_ms")
	if err != nil {
		timeout = 100
	}

	d.url = url
	d.timeOut = time.Duration(timeout) * time.Millisecond

	return nil
}

func (d *IQiyiDmp) SetUrl(url string) {
	d.url = url
}

func (d *IQiyiDmp) SetTimeOut(timeout time.Duration) {
	d.timeOut = timeout
}

func (d *IQiyiDmp) Start() error {
	return nil
}

func (d *IQiyiDmp) Stop() {
}

func (d *IQiyiDmp) GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error) {
	if len(queryKeys) == 0 {
		return nil, fmt.Errorf("query empty")
	}

	imeiMd5, oaid := d.getDeviceId(data)
	if imeiMd5 == "" && oaid == "" {
		return nil, fmt.Errorf("device id empty")
	}

	dmpReqData := &IQiyiRtaRequest{
		NeedReqid: "f",
		Ip:        data.GetRequestIp(),
	}

	if oaid != "" {
		dmpReqData.Oaid = oaid
	} else {
		dmpReqData.Imei = imeiMd5
	}

	reqDealId := ""
	for _, dealId := range queryKeys {
		dealArr := strings.Split(dealId, "--")
		if len(dealArr) < 2 {
			continue
		}

		dmpReqData.Appid = dealArr[0]
		dmpReqData.Aids = dealArr[1]
		reqDealId = dealId
		break
	}

	if dmpReqData.Appid == "" || dmpReqData.Aids == "" {
		return nil, fmt.Errorf("empty appid or aids")
	}

	v, _ := query.Values(dmpReqData)
	queryStr := v.Encode()

	reqUrl := d.url + "?" + queryStr
	if data.GetIsDebug() {
		zap.L().Info("[IQiyiDmp] doRequest, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", queryStr)))))
	}
	httpRequest := fasthttp.AcquireRequest()
	httpRequest.Header.SetMethod(fasthttp.MethodGet)
	httpRequest.SetRequestURI(reqUrl)
	httpResponse := fasthttp.AcquireResponse()

	if err := d.httpClient.DoTimeout(httpRequest, httpResponse, d.timeOut); err != nil {
		return nil, err
	}
	fasthttp.ReleaseRequest(httpRequest)
	if data.GetIsDebug() {
		zap.L().Info("[IQiyiDmp] get iqiyirta dmp response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", httpResponse.Body())))))
	}

	response := &IQiyiRtaResponse{}
	err := json.Unmarshal(httpResponse.Body(), response)
	if err != nil {
		zap.L().Error("[IQiyiDmp]get iqiyirta response json unmarshal err:, content:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))), httpResponse.Body())
		return nil, err
	}

	if response.Code != 0 {
		return nil, nil
	}
	result := make([]string, 0)
	if response.Bid == 1 {
		result = append(result, reqDealId)
	}
	fasthttp.ReleaseResponse(httpResponse)

	return result, nil
}

func (d *IQiyiDmp) getDeviceId(data dmp_provider.DmpRequestData) (string, string) {
	imeiMd5 := ""
	if len(data.GetMd5Imei()) > 0 {
		imeiMd5 = strings.ToUpper(data.GetMd5Imei())
	} else if len(data.GetImei()) > 0 {
		imeiMd5 = md5_utils.GetMd5StringUpper(data.GetImei())
	}

	oaid := data.GetOaid()
	return imeiMd5, oaid
}
