package baidu_rta

import (
	"errors"
	"fmt"
	"net/url"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
)

// Doc: https://feitian.baidu.com/docs/apidoc/rtc.html
type BaiduRTA struct {
	dmpId   utils.ID
	url     string
	timeout time.Duration
	channel string
	token   string
	account string

	httpClient *fasthttp.Client
	log        *zap.Logger
}

type BaiduRTAConfig struct {
	Timeout int64  `json:"timeout_ms,omitempty"`
	Channel string `json:"channel"`
	Token   string `json:"token"`
	Account string `json:"account,omitempty"`
}

type BaiduRTAResponse struct {
	ErrNo   int    `json:"errno,omitempty"`
	Msg     string `json:"msg,omitempty"`
	Data    int    `json:"data,omitempty"`
	DataArr []struct {
		Type      string `json:"type,omitempty"`
		IsBidding int    `json:"isbidding,omitempty"`
		PidList   []int  `json:"pid_list,omitempty"`
	} `json:"data_arr,omitempty"`
}

// var _ dmp_manager.DmpClient = (*BaiduRTA)(nil)

func NewBaiduRTA(dmpId utils.ID) *BaiduRTA {
	return &BaiduRTA{
		url:        "https://uts.baidu.com/bidfilter",
		dmpId:      dmpId,
		httpClient: net_utils.CreateFastHttpClient(time.Second*3, time.Second*60),
		log:        zap.L().With(zap.String("dmp", "BaiduRTA")),
	}
}

func (d *BaiduRTA) SetConfigString(config string) error {
	c := new(BaiduRTAConfig)
	if err := sonic.Unmarshal([]byte(config), c); err != nil {
		return err
	}

	if len(c.Channel) < 1 {
		return errors.New("missing channel in BaiduRTA config")
	}
	if len(c.Token) < 1 {
		return errors.New("missing token in BaiduRTA config")
	}
	if len(c.Account) > 0 {
		d.account = c.Account
	}

	if c.Timeout == 0 {
		c.Timeout = 100
	}

	d.channel = c.Channel
	d.token = c.Token
	d.timeout = time.Duration(c.Timeout) * time.Millisecond

	return nil
}

func (d *BaiduRTA) SetUrl(url string) {
	d.url = url
}

func (d *BaiduRTA) SetTimeOut(timeout time.Duration) {
	d.timeout = timeout
}

func (d *BaiduRTA) Start() error {
	return nil
}

func (d *BaiduRTA) Stop() {
}

func (d *BaiduRTA) GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error) {
	if len(queryKeys) == 0 {
		return nil, fmt.Errorf("query empty")
	}

	param := make(map[string]string)
	param["channel"] = d.channel
	param["time"] = strconv.FormatInt(time_utils.GetTimeUnixSecondUnsafe(), 10)

	if len(data.GetRequestId()) > 0 {
		param["req_id"] = data.GetRequestId()
	}

	if len(queryKeys) == 1 {
		param["version"] = "1"
		param["type"] = queryKeys[0]
	} else {
		param["version"] = "2"
		param["type"] = strings.Join(queryKeys, ",")
	}

	if len(d.account) > 0 {
		param["account"] = d.account
	}

	if len(data.GetImei()) > 0 {
		param["imei"] = data.GetImei()
	} else if len(data.GetMd5Imei()) > 0 {
		param["imei_md5"] = data.GetMd5Imei()
	}

	if len(data.GetOaid()) > 0 {
		param["oaid"] = data.GetOaid()
	} else if len(data.GetMd5Oaid()) > 0 {
		param["oaid_md5"] = data.GetMd5Oaid()
	}

	if len(data.GetIdfa()) > 0 {
		param["idfa"] = data.GetIdfa()
	} else if len(data.GetMd5Idfa()) > 0 {
		param["idfa_md5"] = data.GetMd5Idfa()
	}

	if len(data.GetCaid()) > 0 {
		caid := device_utils.GetCaidRaw(data.GetCaid())
		param["curr_caid"] = caid
	}

	if len(data.GetRequestIp()) > 0 {
		param["ipv4"] = data.GetRequestIp()
	}

	if len(data.GetAndroidId()) > 0 {
		param["android_id"] = data.GetAndroidId()
	}

	switch data.GetOsType() {
	case entity.OsTypeIOS:
		param["os"] = "1"
	default:
		param["os"] = "2"
	}

	param["sign"] = d.getSignature(param, d.token)
	var urlParam strings.Builder
	var i = 0
	for k, v := range param {
		if i == 0 {
			urlParam.WriteRune('?')
		} else {
			urlParam.WriteRune('&')
		}
		urlParam.WriteString(fmt.Sprintf("%s=%s", k, url.QueryEscape(v)))
		i++
	}

	requestUrl := d.url + urlParam.String()
	httpRequest := fasthttp.AcquireRequest()
	httpRequest.Header.SetMethod(fasthttp.MethodGet)
	httpRequest.SetRequestURI(requestUrl)
	httpResponse := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(httpResponse)
	if err := d.httpClient.DoTimeout(httpRequest, httpResponse, d.timeout); err != nil {
		return nil, err
	}
	fasthttp.ReleaseRequest(httpRequest)

	respData := httpResponse.Body()
	if data.GetIsDebug() {
		d.log.WithField("response", string(respData)).Info("get dmp response")
	}

	if httpResponse.StatusCode() > 399 {
		d.zap.L().Info("dmp response code error", zap.String("url", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", requestUrl)))), zap.String("code", fmt.Sprintf("%v", httpResponse.StatusCode())), zap.String("response", fmt.Sprintf("%v", string(respData))))
		return nil, nil
	}

	response := new(BaiduRTAResponse)
	if err := sonic.Unmarshal(respData, response); err != nil {
		d.log.WithError(err).WithField("response", string(respData)).Error("dmp response json unmarshal error")
		return nil, err
	}

	if response.ErrNo != 0 {
		d.zap.L().Error("dmp response error", zap.String("url", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", requestUrl)))), zap.String("response", fmt.Sprintf("%v", string(respData))))
		return nil, nil
	}

	queryMap := make(map[string]struct{})
	for _, key := range queryKeys {
		queryMap[key] = struct{}{}
	}
	result := make([]string, 0)

	if param["version"] == "1" {
		if response.Data == 0 {
			return nil, nil
		} else {
			return queryKeys, nil
		}
	} else {
		for _, rtaInfo := range response.DataArr {
			if _, ok := queryMap[rtaInfo.Type]; ok && rtaInfo.IsBidding == 1 {
				result = append(result, rtaInfo.Type)
			}
		}
	}

	return result, nil
}

func (d *BaiduRTA) getSignature(param map[string]string, token string) string {
	keys := make([]string, 0, len(param))
	for k := range param {
		keys = append(keys, k)
	}
	slices.Sort(keys)
	var sb strings.Builder
	for _, k := range keys {
		sb.WriteString(fmt.Sprintf("%s=%s", k, param[k]))
	}
	sb.WriteString(token)
	return md5_utils.GetMd5String(sb.String())
}
