package local_rta

import (
	"errors"
	"strconv"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/library/utils"
)

type LocalRTA struct {
	dmpId utils.ID
	log   *zap.Logger
}

func NewLocalRTA(dmpId utils.ID) *LocalRTA {
	return &LocalRTA{
		dmpId: dmpId,
		log:   zap.L().With(zap.String("dmp", "LocalRTA")),
	}
}

func (d *LocalRTA) SetConfigString(config string) error {
	return nil
}

func (d *LocalRTA) GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error) {
	if len(queryKeys) == 0 {
		return nil, errors.New("query empty")
	}

	segment := data.GetUserSegment()
	if segment == nil || segment.IsEmpty() {
		return nil, nil
	}

	if data.GetIsDebug() {
		d.log.WithField("segment", segment.DumpJson()).Info("get user segment")
	}

	queryMap := make(map[string]struct{})
	for _, key := range queryKeys {
		queryMap[key] = struct{}{}
	}
	result := make([]string, 0)
	for _, tag := range segment.Tags {
		// NOTE: for local RTA ID, tag id should be equal to tag key
		id := strconv.FormatInt(int64(tag.TagId), 10)
		if _, ok := queryMap[id]; ok {
			result = append(result, id)
		}
	}

	return result, nil
}
