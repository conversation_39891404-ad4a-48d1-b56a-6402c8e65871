package youdao_dmp

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/go-querystring/query"
	"go.uber.org/zap"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/simple_kv_data"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type YouDaoDmp struct {
	dmpId   utils.ID
	url     string
	timeOut time.Duration

	httpClient *fasthttp.Client
}

type YouDaoRTARequest struct {
	Aids         string `url:"aids"`
	Imei         string `url:"imei,omitempty"`
	ImeiMd5      string `url:"imeiMd5,omitempty"`
	AndroidIdMd5 string `url:"androidIdMd5,omitempty"`
	Oaid         string `url:"oaid,omitempty"`
	OaidMd5      string `url:"oaidMd5,omitempty"`
	Idfa         string `url:"idfa,omitempty"`
	IdfaMd5      string `url:"idfaMd5,omitempty"`
	Caid         string `url:"caid,omitempty"`
	CaidMd5      string `url:"caidMd5,omitempty"`
	Os           int    `url:"os"`
	Ip           string `url:"ip,omitempty"`
	Ua           string `url:"ua,omitempty"`
	Model        string `url:"model,omitempty"`
}

type YouDaoRtaResponseData struct {
	ReqId     string `json:"reqId"`
	BidStatus int    `json:"bidStatus"`
	Aids      []int  `json:"aids"`
}

type YouDaoRtaResponse struct {
	Code   int                    `json:"code"`
	Msg    string                 `json:"msg"`
	Data   *YouDaoRtaResponseData `json:"data"`
	RawStr string                 `json:"-"`
}

func NewYouDaoDmp(dmpId utils.ID) *YouDaoDmp {
	return &YouDaoDmp{
		dmpId:      dmpId,
		httpClient: net_utils.CreateFastHttpClient(time.Second*3, time.Second*60),
	}
}

func (d *YouDaoDmp) SetConfigString(config string) error {
	kv := simple_kv_data.SimpleKeyValue{}
	if err := kv.UnmarshalJson([]byte(config)); err != nil {
		return err
	}

	url, err := kv.GetString("url")
	if err != nil {
		return err
	}

	timeout, err := kv.GetInt64("timeout_ms")
	if err != nil {
		timeout = 100
	}

	d.url = url
	d.timeOut = time.Duration(timeout) * time.Millisecond

	return nil
}

func (d *YouDaoDmp) SetUrl(url string) {
	d.url = url
}

func (d *YouDaoDmp) SetTimeOut(timeout time.Duration) {
	d.timeOut = timeout
}

func (d *YouDaoDmp) Start() error {
	return nil
}

func (d *YouDaoDmp) Stop() {
}

func (d *YouDaoDmp) GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error) {
	if len(queryKeys) == 0 {
		return nil, fmt.Errorf("query empty")
	}

	dmpReqData := &YouDaoRTARequest{
		Aids:  "",
		Os:    1,
		Ip:    "",
		Ua:    "",
		Model: "",
	}

	hasDeviceId := false
	if data.GetImei() != "" {
		dmpReqData.Imei = strings.ToUpper(data.GetImei())
		hasDeviceId = true
	} else if data.GetMd5Imei() != "" {
		dmpReqData.ImeiMd5 = strings.ToUpper(data.GetMd5Imei())
		hasDeviceId = true
	}

	if data.GetIdfa() != "" {
		dmpReqData.Idfa = strings.ToUpper(data.GetIdfa())
		hasDeviceId = true
	} else if data.GetMd5Idfa() != "" {
		dmpReqData.IdfaMd5 = strings.ToUpper(data.GetMd5Idfa())
		hasDeviceId = true
	}

	if data.GetOaid() != "" {
		dmpReqData.Oaid = data.GetOaid()
		hasDeviceId = true
	} else if data.GetMd5Oaid() != "" {
		dmpReqData.OaidMd5 = strings.ToUpper(data.GetMd5Oaid())
		hasDeviceId = true
	}

	if data.GetMd5AndroidId() != "" {
		dmpReqData.AndroidIdMd5 = strings.ToUpper(data.GetMd5AndroidId())
		hasDeviceId = true
	}

	if data.GetCaid() != "" {
		dmpReqData.Caid = strings.ToUpper(data.GetCaid())
		hasDeviceId = true
	}

	if !hasDeviceId {
		return nil, fmt.Errorf("device id empty")
	}

	if data.GetOsType() == entity.OsTypeIOS {
		dmpReqData.Os = 0
	} else if data.GetOsType() == entity.OsTypeAndroid {
		dmpReqData.Os = 1
	} else if data.GetOsType() == entity.OsTypeWindowsPhone {
		dmpReqData.Os = 2
	}

	reqDeal := make(map[string]int)
	isFirst := true
	for _, dealId := range queryKeys {
		if isFirst {
			dmpReqData.Aids = dealId
			isFirst = false
		} else {
			dmpReqData.Aids += "," + dealId
		}
		reqDeal[dealId] = 1
	}

	if len(dmpReqData.Aids) == 0 {
		return nil, fmt.Errorf("empty appid or aids")
	}

	v, _ := query.Values(dmpReqData)
	queryStr := v.Encode()

	reqUrl := d.url + "?" + queryStr
	if data.GetIsDebug() {
		zap.L().Info("[YouDaoDmp] doRequest, request", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", queryStr)))))
	}
	httpRequest := fasthttp.AcquireRequest()
	httpRequest.Header.SetMethod(fasthttp.MethodGet)
	httpRequest.SetRequestURI(reqUrl)
	httpResponse := fasthttp.AcquireResponse()

	if err := d.httpClient.DoTimeout(httpRequest, httpResponse, d.timeOut); err != nil {
		return nil, err
	}
	fasthttp.ReleaseRequest(httpRequest)
	if data.GetIsDebug() {
		zap.L().Info("[YouDaoDmp] get YouDaoDmp dmp response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", httpResponse.Body())))))
	}

	response := &YouDaoRtaResponse{}
	err := json.Unmarshal(httpResponse.Body(), response)
	if err != nil {
		zap.L().Error("[YouDaoDmp]get YouDaoDmp response json unmarshal err:, content:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))), httpResponse.Body())
		return nil, err
	}

	if response.Code != 0 || response.Data == nil {
		return nil, nil
	}

	result := make([]string, 0)

	if response.Data.BidStatus == 1 {
		for _, aid := range response.Data.Aids {
			if _, ok := reqDeal[type_convert.GetAssertString(aid)]; ok {
				result = append(result, type_convert.GetAssertString(aid))
			}
		}
	} else if response.Data.BidStatus == 0 {
		//no match
	}
	fasthttp.ReleaseResponse(httpResponse)

	return result, nil

}
