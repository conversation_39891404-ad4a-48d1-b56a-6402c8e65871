package meituan_rta

import (
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider/dmp_broker/meituan_rta/meituan_rta_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/simple_kv_data"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
)

type MeituanRtaDmp struct {
	dmpId   utils.ID
	url     string
	timeOut time.Duration

	isPing bool
	isTest bool
	//meiTuanAdxTargetId int64 //废弃
	meiTuanSiteId string

	httpClient *fasthttp.Client
}

func NewMeituanRtaDmp(dmpId utils.ID) *MeituanRtaDmp {
	return &MeituanRtaDmp{
		dmpId:      dmpId,
		httpClient: net_utils.CreateFastHttpClient(time.Second*3, time.Second*60),
	}
}

func (d *MeituanRtaDmp) SetConfigString(config string) error {
	kv := simple_kv_data.SimpleKeyValue{}
	if err := kv.UnmarshalJson([]byte(config)); err != nil {
		return err
	}

	url, err := kv.GetString("url")
	if err != nil {
		return err
	}

	//targetId, err := kv.GetInt64("target_id")
	//if err != nil {
	//}

	siteId, err := kv.GetString("site_id")
	if err != nil {
		return err
	}

	timeout, err := kv.GetInt64("timeout_ms")
	if err != nil {
		return err
	}

	d.url = url
	d.timeOut = time.Duration(timeout) * time.Millisecond
	//d.meiTuanAdxTargetId = targetId
	d.meiTuanSiteId = siteId

	return nil
}

func (d *MeituanRtaDmp) SetUrl(url string) {
	d.url = url
}

func (d *MeituanRtaDmp) SetTimeOut(timeout time.Duration) {
	d.timeOut = timeout
}

//
//func (d *MeituanRtaDmp) SetTargetId(targetId int64) {
//	d.meiTuanAdxTargetId = targetId
//}

func (d *MeituanRtaDmp) SetSiteId(siteId string) {
	d.meiTuanSiteId = siteId
}

func (d *MeituanRtaDmp) SetIsPing(isPing bool) {
	d.isPing = isPing
}

func (d *MeituanRtaDmp) SetIsTest(isTest bool) {
	d.isTest = isTest
}

func (d *MeituanRtaDmp) Start() error {
	return nil
}

func (d *MeituanRtaDmp) Stop() {
}

func (d *MeituanRtaDmp) GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error) {
	if len(queryKeys) == 0 {
		return nil, fmt.Errorf("query empty")
	}

	requestData := meituan_rta_proto.RtaRequest{
		Id:     data.GetRequestId(),
		IsPing: d.isPing,
		IsTest: d.isTest,
		SiteId: d.meiTuanSiteId,
		Device: &meituan_rta_proto.RtaRequest_Device{
			Os:              d.toMeituanOs(data.GetOsType()),
			IdfaMd5Sum:      strings.ToLower(data.GetMd5Idfa()),
			ImeiMd5Sum:      strings.ToLower(data.GetMd5Imei()),
			AndroidIdMd5Sum: strings.ToLower(data.GetMd5AndroidId()),
			OaidMd5Sum:      strings.ToLower(data.GetMd5Oaid()),
			Oaid:            strings.ToLower(data.GetOaid()),
			Ip:              data.GetRequestIp(),
		},
	}

	if len(requestData.Device.IdfaMd5Sum) == 0 && len(requestData.Device.ImeiMd5Sum) == 0 &&
		len(requestData.Device.AndroidIdMd5Sum) == 0 && len(requestData.Device.OaidMd5Sum) == 0 &&
		len(requestData.Device.Oaid) == 0 {
		return nil, fmt.Errorf("device id empty")
	}

	//reqDealId := ""
	//for _, dealId := range queryKeys {
	//	reqDealId = dealId
	//	break
	//}
	//
	//requestData.SiteId = reqDealId

	requestBuffer := buffer_pool.NewBuffer()
	defer requestBuffer.Release()

	requestBuffer.EnsureSize(requestData.Size())
	if _, err := requestData.MarshalToSizedBuffer(requestBuffer.Get()); err != nil {
		return nil, err
	}

	httpRequest := fasthttp.AcquireRequest()

	if data.GetIsDebug() {
		zap.L().Info("[MeituanRtaDmp] get MeituanRta dmp body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", requestData.String())))))
	}

	httpRequest.SetBodyRaw(requestBuffer.Get())
	httpRequest.Header.SetMethod(fasthttp.MethodPost)
	httpRequest.Header.SetContentType("application/x-protobuf;charset=UTF-8")
	httpRequest.SetRequestURI(d.url)
	httpResponse := fasthttp.AcquireResponse()
	if err := d.httpClient.DoTimeout(httpRequest, httpResponse, d.timeOut); err != nil {
		return nil, err
	}
	fasthttp.ReleaseRequest(httpRequest)

	responseData := meituan_rta_proto.RtaResponse{}
	if err := responseData.Unmarshal(httpResponse.Body()); err != nil {
		zap.L().Error("[MeituanRtaDmp] parse MeituanRtaDmp dmp response error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return nil, err
	}

	if data.GetIsDebug() {
		zap.L().Info("[MeituanRtaDmp] get MeituanRtaDmp dmp response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", responseData.String())))))
	}

	if responseData.Code != 0 {
		return nil, nil
	}

	reqPromotionId := make(map[string]int)
	for _, dealId := range queryKeys {
		reqPromotionId[dealId] = 1
	}

	result := make([]string, 0)
	for _, targetId := range responseData.PromotionTargetId {
		if _, ok := reqPromotionId[type_convert.GetAssertString(targetId)]; ok {
			result = append(result, type_convert.GetAssertString(targetId))
		}
	}

	fasthttp.ReleaseResponse(httpResponse)
	return result, nil
}

func (d *MeituanRtaDmp) toMeituanOs(osType entity.OsType) meituan_rta_proto.RtaRequest_OperatingSystem {
	switch osType {
	case entity.OsTypeIOS:
		return meituan_rta_proto.RtaRequest_OS_IOS
	case entity.OsTypeAndroid:
		return meituan_rta_proto.RtaRequest_OS_ANDROID
	case entity.OsTypeWindowsPhone:
		return meituan_rta_proto.RtaRequest_OS_WINDOWS
	default:
		return meituan_rta_proto.RtaRequest_OS_UNKNOWN
	}
}
