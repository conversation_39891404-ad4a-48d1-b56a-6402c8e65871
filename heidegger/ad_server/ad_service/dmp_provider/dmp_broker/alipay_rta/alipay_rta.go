package alipay_rta

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/simple_kv_data"
)

const (
	EncryptTypeMD5 = "MD5"
	DeviceTypeIMEI = "IMEI"
	DeviceTypeOAID = "OAID"
	DeviceTypeIDFA = "IDFA"
	DeviceTypeCAID = "CAID"
)

type AlipayRTA struct {
	dmpId   utils.ID
	url     string
	timeOut time.Duration

	httpClient *fasthttp.Client
}

type AlipayRtaRequest struct {
	Devices   map[string]map[string]*AlipayRtaDevice `json:"devices"`
	RtaIdList []string                               `json:"rta_id_list"`
	RequestId string                                 `json:"request_id"`
}

type AlipayRtaDevice struct {
	EncryptType string `json:"encrypt_type"`
	DeviceId    string `json:"device_id"`
}

type AlipayRtaResponse struct {
	ResultCode string                 `json:"resultCode"`
	ResultDesc string                 `json:"resultDesc"`
	Success    bool                   `json:"success"`
	Retriable  bool                   `json:"retriable"`
	Response   *AlipayRtaResponseData `json:"response"`
}

type AlipayRtaResponseData struct {
	PrincipalLabel  string             `json:"principalLabel"`
	RtaInfoList     []RtaInfo          `json:"rtaInfoList"`
	RequiredFlow    bool               `json:"requiredFlow"`
	BiddingScoreMap map[string]float32 `json:"biddingScoreMap"`
	//InviteConsultInfoMap map[string]InviteConsultInfo `json:"inviteConsultInfoMap"`
}

//type InviteConsultInfo struct {
//	AccountList     []string           `json:"accountList"`
//	BiddingScoreMap map[string]float32 `json:"biddingScoreMap"`
//	DeviceId        string             `json:"deviceId"`
//	DeviceLabel     string             `json:"deviceLabel"`
//}

type RtaInfo struct {
	AccountId string `json:"accountId"`
}

func NewAlipayRTA(dmpId utils.ID) *AlipayRTA {
	return &AlipayRTA{
		dmpId:      dmpId,
		httpClient: net_utils.CreateFastHttpClient(time.Second*3, time.Second*60),
	}
}

func (d *AlipayRTA) SetConfigString(config string) error {
	kv := simple_kv_data.SimpleKeyValue{}
	if err := kv.UnmarshalJson([]byte(config)); err != nil {
		return err
	}

	url, err := kv.GetString("url")
	if err != nil {
		return err
	}

	timeout, err := kv.GetInt64("timeout_ms")
	if err != nil {
		timeout = 100
	}

	d.url = url
	d.timeOut = time.Duration(timeout) * time.Millisecond

	return nil
}

func (d *AlipayRTA) SetUrl(url string) {
	d.url = url
}

func (d *AlipayRTA) SetTimeOut(timeout time.Duration) {
	d.timeOut = timeout
}

func (d *AlipayRTA) Start() error {
	return nil
}

func (d *AlipayRTA) Stop() {
}

func (d *AlipayRTA) GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error) {
	if len(queryKeys) == 0 {
		return nil, fmt.Errorf("query empty")
	}

	deviceidMap := make(map[string]*AlipayRtaDevice)

	if len(data.GetImei()) > 0 {
		rtaDevice := &AlipayRtaDevice{
			EncryptType: "",
			DeviceId:    data.GetImei(),
		}
		deviceidMap[DeviceTypeIMEI] = rtaDevice
	} else if len(data.GetMd5Imei()) > 0 {
		rtaDevice := &AlipayRtaDevice{
			EncryptType: EncryptTypeMD5,
			DeviceId:    data.GetMd5Imei(),
		}
		deviceidMap[DeviceTypeIMEI] = rtaDevice
	}

	if len(data.GetOaid()) > 0 {
		rtaDevice := &AlipayRtaDevice{
			EncryptType: "",
			DeviceId:    data.GetOaid(),
		}
		deviceidMap[DeviceTypeOAID] = rtaDevice
	} else if len(data.GetMd5Oaid()) > 0 {
		rtaDevice := &AlipayRtaDevice{
			EncryptType: EncryptTypeMD5,
			DeviceId:    data.GetMd5Oaid(),
		}
		deviceidMap[DeviceTypeOAID] = rtaDevice
	}

	if len(data.GetIdfa()) > 0 {
		rtaDevice := &AlipayRtaDevice{
			EncryptType: "",
			DeviceId:    data.GetIdfa(),
		}
		deviceidMap[DeviceTypeIDFA] = rtaDevice
	} else if len(data.GetMd5Idfa()) > 0 {
		rtaDevice := &AlipayRtaDevice{
			EncryptType: EncryptTypeMD5,
			DeviceId:    data.GetMd5Idfa(),
		}
		deviceidMap[DeviceTypeIDFA] = rtaDevice
	}

	if len(data.GetCaid()) > 0 {
		caid := device_utils.GetCaidRaw(data.GetCaid())
		caidV := device_utils.GetCaidVersion(data.GetCaid())

		caid_version := caid + "_" + caidV

		rtaDevice := &AlipayRtaDevice{
			EncryptType: "",
			DeviceId:    caid_version,
		}
		deviceidMap[DeviceTypeCAID] = rtaDevice
	}

	if len(deviceidMap) == 0 {
		return nil, fmt.Errorf("empty Devices")
	}

	dmpReqData := &AlipayRtaRequest{
		Devices:   make(map[string]map[string]*AlipayRtaDevice),
		RtaIdList: queryKeys,
		RequestId: data.GetRequestId(),
	}

	dmpReqData.Devices[data.GetRequestId()] = deviceidMap

	reqData, _ := sonic.Marshal(dmpReqData)
	if data.GetIsDebug() {
		zap.L().Info("[AlipayRTA] get dmp body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", reqData)))))
	}

	httpRequest := fasthttp.AcquireRequest()
	httpRequest.SetBodyRaw(reqData)
	httpRequest.Header.SetMethod(fasthttp.MethodPost)
	httpRequest.Header.SetContentType("application/json")
	httpRequest.SetRequestURI(d.url)
	httpResponse := fasthttp.AcquireResponse()
	if err := d.httpClient.DoTimeout(httpRequest, httpResponse, d.timeOut); err != nil {
		return nil, err
	}
	fasthttp.ReleaseRequest(httpRequest)
	if data.GetIsDebug() {
		zap.L().Info("[AlipayRTA] get dmp response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", httpResponse.Body())))))
	}

	response := &AlipayRtaResponse{}
	err := json.Unmarshal(httpResponse.Body(), response)
	if err != nil {
		zap.L().Error("[AlipayRTA]get response json unmarshal err:, content:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))), httpResponse.Body())
		return nil, err
	}

	if !response.Success {
		return nil, nil
	}
	queryMap := make(map[string]int)
	for _, key := range queryKeys {
		queryMap[key] = 1
	}

	result := make([]string, 0)
	if response.Response != nil {
		for _, rtaInfo := range response.Response.RtaInfoList {
			if queryMap[rtaInfo.AccountId] == 1 {
				result = append(result, rtaInfo.AccountId)
			}
		}
	}

	fasthttp.ReleaseResponse(httpResponse)

	return result, nil
}
