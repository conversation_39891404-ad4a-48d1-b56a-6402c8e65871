package oneway_rta

import (
	"errors"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/simple_kv_data"
	"fmt"
)

// Doc: https://doc.oneway.mobi/RTA/#/
type OneWayRTA struct {
	dmpId   utils.ID
	url     string
	timeout time.Duration
	channle string

	httpClient *fasthttp.Client
	log        *zap.Logger
}

type OneWayRtaRequest struct {
	Channel  string   `json:"channel"`
	OfferIds []string `json:"offerIds"`
	ImeiMd5  string   `json:"imeiMd5,omitempty"`
	Oaid     string   `json:"oaid,omitempty"`
	OaidMd5  string   `json:"oaidMd5,omitempty"`
	Idfa     string   `json:"idfa,omitempty"`
	IdfaMd5  string   `json:"idfaMd5,omitempty"`
	Platform int32    `json:"platform,omitempty"`
}

type OneWayRtaResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    int32  `json:"code"`
	Data    struct {
		OfferIds []string `json:"offerIds"`
	} `json:"data"`
}

func NewOneWayRTA(dmpId utils.ID) *OneWayRTA {
	return &OneWayRTA{
		dmpId:      dmpId,
		httpClient: net_utils.CreateFastHttpClient(time.Second*3, time.Second*60),
		log:        zap.L().With(zap.String("dmp", "OneWayRTA")),
	}
}

func (d *OneWayRTA) SetConfigString(config string) error {
	kv := simple_kv_data.SimpleKeyValue{}
	if err := kv.UnmarshalJson([]byte(config)); err != nil {
		return err
	}

	url, err := kv.GetString("url")
	if err != nil {
		return err
	}
	channel, err := kv.GetString("channel")
	if err != nil {
		return err
	}

	timeout, err := kv.GetInt64("timeout_ms")
	if err != nil {
		timeout = 100
	}

	d.url = url
	d.channle = channel
	d.timeout = time.Duration(timeout) * time.Millisecond

	return nil
}

func (d *OneWayRTA) SetUrl(url string) {
	d.url = url
}

func (d *OneWayRTA) SetTimeout(timeout time.Duration) {
	d.timeout = timeout
}

func (d *OneWayRTA) Start() error {
	return nil
}

func (d *OneWayRTA) Stop() {
}

func (d *OneWayRTA) GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error) {
	if len(queryKeys) == 0 {
		return nil, errors.New("query empty")
	}

	req := &OneWayRtaRequest{
		Channel:  d.channle,
		OfferIds: queryKeys,
		ImeiMd5:  strings.ToLower(data.GetMd5Imei()),
		Oaid:     data.GetOaid(),
		OaidMd5:  strings.ToLower(data.GetMd5Oaid()),
		Idfa:     data.GetIdfa(),
		IdfaMd5:  strings.ToLower(data.GetMd5Idfa()),
	}

	switch data.GetOsType() {
	case entity.OsTypeIOS:
		req.Platform = 2
	default:
		req.Platform = 1
	}

	payload, err := sonic.Marshal(req)
	if err != nil {
		d.log.WithError(err).WithField("payload", string(payload)).Error("dmp request json marshal error")
		return nil, err
	}
	if data.GetIsDebug() {
		d.log.WithField("payload", string(payload)).Info("send dmp request")
	}

	httpRequest := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(httpRequest)
	httpRequest.Header.SetMethod(fasthttp.MethodPost)
	httpRequest.Header.SetContentType("application/json;charset=UTF-8")
	httpRequest.SetRequestURI(d.url)
	httpRequest.SetBody(payload)
	httpResponse := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(httpResponse)

	if err := d.httpClient.DoTimeout(httpRequest, httpResponse, d.timeout); err != nil {
		return nil, err
	}

	respData := httpResponse.Body()
	if data.GetIsDebug() {
		d.log.WithField("response", string(respData)).Info("get dmp response")
	}

	if httpResponse.StatusCode() > 399 {
		d.zap.L().Info("dmp response code error", zap.String("payload", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(payload))))), zap.String("code", fmt.Sprintf("%v", httpResponse.StatusCode())), zap.String("response", fmt.Sprintf("%v", string(respData))))
		return nil, nil
	}

	response := &OneWayRtaResponse{}
	if err = sonic.Unmarshal(respData, response); err != nil {
		d.log.WithError(err).WithField("response", string(respData)).Error("dmp response json unmarshal error")
		return nil, err
	}

	// 未命中
	if response.Code == 10204 {
		return nil, nil
	}

	if response.Code != 0 && response.Code != 200 {
		d.zap.L().Error("dmp response error", zap.String("payload", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(payload))))), zap.String("response", fmt.Sprintf("%v", string(respData))))
		return nil, nil
	}

	queryMap := make(map[string]struct{})
	for _, key := range queryKeys {
		queryMap[key] = struct{}{}
	}
	result := make([]string, 0)
	for _, id := range response.Data.OfferIds {
		if _, ok := queryMap[id]; ok {
			result = append(result, id)
		}
	}

	return result, nil
}
