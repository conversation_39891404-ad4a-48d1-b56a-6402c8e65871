package vipshop_dmp

import (
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/simple_kv_data"
)

type VipShopDmp struct {
	dmpId   utils.ID
	url     string
	timeOut time.Duration

	httpClient *fasthttp.Client
}

type VipShopAdReq struct {
	Token      string `json:"token"`
	AdId       int64  `json:"adId,omitempty"`
	CampaignId int64  `json:"campaignId,omitempty"`
	AccountId  int64  `json:"accountId,omitempty"`
}

type VipShopRtqRequest struct {
	Id      string `json:"id"`
	V       string `json:"v"`
	Did     string `json:"did"`
	DidType int    `json:"didType"`

	AdReqs []VipShopAdReq `json:"adReqs"`
	TagId  string         `json:"tag_id,omitempty"`
}

type VipShopRtqResponse struct {
	Errno   int                `json:"errno"`
	Msg     string             `json:"msg"`
	BidResp VipShopBidResponse `json:"bidResp"`
}
type VipShopBidResponse struct {
	Id      string          `json:"id"`
	ResType int             `json:"resType"`
	AdResps []VipShopAdResp `json:"adResps"`
}

type VipShopAdResp struct {
	Token      string `json:"token"`
	AdId       int64  `json:"adId"`
	CampaignId int64  `json:"campaignId"`
	AccountId  int64  `json:"accountId"`
	Ac         string `json:"ac"`
}

func NewVipShopDmp(dmpId utils.ID) *VipShopDmp {
	return &VipShopDmp{
		dmpId:      dmpId,
		httpClient: net_utils.CreateFastHttpClient(time.Second*3, time.Second*60),
	}
}

func (d *VipShopDmp) SetConfigString(config string) error {
	kv := simple_kv_data.SimpleKeyValue{}
	if err := kv.UnmarshalJson([]byte(config)); err != nil {
		return err
	}

	url, err := kv.GetString("url")
	if err != nil {
		return err
	}

	timeout, err := kv.GetInt64("timeout_ms")
	if err != nil {
		timeout = 100
	}

	d.url = url
	d.timeOut = time.Duration(timeout) * time.Millisecond

	return nil
}

func (d *VipShopDmp) SetUrl(url string) {
	d.url = url
}

func (d *VipShopDmp) SetTimeOut(timeout time.Duration) {
	d.timeOut = timeout
}

func (d *VipShopDmp) Start() error {
	return nil
}

func (d *VipShopDmp) Stop() {
}

func (d *VipShopDmp) GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error) {
	if len(queryKeys) == 0 {
		//zap.L().Error("[VipShopDmp] empty querey keys")
		return nil, fmt.Errorf("query empty")
	}

	did, didType := d.getDeviceId(data)

	if didType == -1 || len(did) == 0 {
		//zap.L().Error("[VipShopDmp] emptydevice id")
		return nil, fmt.Errorf("device id empty")
	}

	dmpReqData := VipShopRtqRequest{
		Id:      data.GetRequestId(),
		Did:     did,
		DidType: didType,
		V:       "1.0",
	}

	for _, querykey := range queryKeys {
		dmpReq := VipShopAdReq{
			Token: querykey,
		}
		dmpReqData.AdReqs = append(dmpReqData.AdReqs, dmpReq)
	}

	if len(dmpReqData.AdReqs) == 0 {
		zap.L().Error("[VipShopDmp]empty appid or aids")
		return nil, fmt.Errorf("empty appid or aids")
	}
	body, _ := json.Marshal(dmpReqData)
	if data.GetIsDebug() {
		zap.L().Info("[VipShopDmp] get vipshop dmp body", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", body)))))
	}

	httpRequest := fasthttp.AcquireRequest()

	httpRequest.SetBodyRaw(body)
	httpRequest.Header.SetMethod(fasthttp.MethodPost)
	httpRequest.Header.SetContentType("application/json; cahrset=UTF-8")
	httpRequest.SetRequestURI(d.url)
	httpResponse := fasthttp.AcquireResponse()
	if err := d.httpClient.DoTimeout(httpRequest, httpResponse, d.timeOut); err != nil {
		return nil, err
	}
	fasthttp.ReleaseRequest(httpRequest)

	if data.GetIsDebug() {
		zap.L().Info("[VipShopDmp] get vipshop dmp response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", httpResponse.Body())))))
	}

	response := &VipShopRtqResponse{}
	err := json.Unmarshal(httpResponse.Body(), response)
	if err != nil {
		return nil, err
	}

	if response.Errno != 0 {
		return nil, nil
	}

	idCodeMap := make(map[string]string)

	for _, dmpReq := range dmpReqData.AdReqs {
		idCodeMap[dmpReq.Token] = ""
	}

	result := make([]string, 0)
	for _, i := range response.BidResp.AdResps {
		if _, ok := idCodeMap[i.Token]; !ok {
			continue
		}
		switch i.Ac {
		case "y":
			result = append(result, i.Token)
		case "n":
		default:
		}
	}

	fasthttp.ReleaseResponse(httpResponse)

	return result, nil
}

func (d *VipShopDmp) getDeviceId(data dmp_provider.DmpRequestData) (string, int) {
	if len(data.GetMd5Imei()) > 0 {
		return data.GetMd5Imei(), 0
	} else if len(data.GetOaid()) > 0 {
		return data.GetOaid(), 1
	} else if len(data.GetMd5Oaid()) > 0 {
		return data.GetMd5Oaid(), 3
	} else if len(data.GetIdfa()) > 0 {
		return data.GetIdfa(), 2
	} else if len(data.GetMd5Idfa()) > 0 {
		return data.GetMd5Idfa(), 4
	}

	return "", -1
}
