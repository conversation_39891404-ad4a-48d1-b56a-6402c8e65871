package jd_rta

import (
	"errors"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/simple_kv_data"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"fmt"
)

type JdRTA struct {
	dmpId          utils.ID
	url            string
	timeout        time.Duration
	token          string
	secret         string
	callbackUrl    string // 中广人群包上报接口
	callbackToken  string
	callbackSecret string

	httpClient *fasthttp.Client
	log        *zap.Logger
}

type JdRtaDeviceType string

const (
	JdRtaDeviceTypeIDFA = "idfa"
	JdRtaDeviceTypeOAID = "oaid"
	JdRtaDeviceTypeCAID = "caid"
	JdRtaDeviceTypeIMEI = "imei"
)

type JdRtaDeviceInfo struct {
	DeviceId   string          `json:"deviceId"`   // 原值加密 MD5 值，小写
	DeviceType JdRtaDeviceType `json:"deviceType"` // idfa/oaid/caid/imei
}

type JdRtaRequest struct {
	ReqId       string             `json:"reqId"`
	DeviceInfos []*JdRtaDeviceInfo `json:"deviceInfos"`
	RtaIds      []string           `json:"rtaIds,omitempty"`
}

type JdRtaResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data []struct {
		RtaId     string   `json:"rtaId"`
		DeviceIds []string `json:"deviceIds"`
		DataTime  string   `json:"dataTime"` // yyyy-MM-dd HH:mm:ss
		Status    string   `json:"status"`
	} `json:"data"`
}

type ZhongGuangCallbackRequest struct {
	ReqId       string             `json:"reqId"`
	DeviceInfos []*JdRtaDeviceInfo `json:"deviceInfos"`
	RtaId       string             `json:"rtaId,omitempty"`
}

func NewJdRTA(dmpId utils.ID) *JdRTA {
	return &JdRTA{
		dmpId:      dmpId,
		httpClient: net_utils.CreateFastHttpClient(time.Second*3, time.Second*60),
		log:        zap.L().With(zap.String("dmp", "JdRTA")),
	}
}

func (d *JdRTA) SetConfigString(config string) error {
	kv := simple_kv_data.SimpleKeyValue{}
	if err := kv.UnmarshalJson([]byte(config)); err != nil {
		return err
	}

	url, err := kv.GetString("url")
	if err != nil {
		return err
	}
	token, err := kv.GetString("token")
	if err != nil {
		return err
	}
	secret, err := kv.GetString("secret")
	if err != nil {
		return err
	}

	timeout, err := kv.GetInt64("timeout_ms")
	if err != nil {
		timeout = 100
	}

	d.callbackUrl, _ = kv.GetString("callbackUrl")
	d.callbackToken, _ = kv.GetString("callbackToken")
	d.callbackSecret, _ = kv.GetString("callbackSecret")

	d.url = url
	d.token = token
	d.secret = secret
	d.timeout = time.Duration(timeout) * time.Millisecond

	return nil
}

func (d *JdRTA) SetUrl(url string) {
	d.url = url
}

func (d *JdRTA) SetTimeout(timeout time.Duration) {
	d.timeout = timeout
}

func (d *JdRTA) Start() error {
	return nil
}

func (d *JdRTA) Stop() {
}

func (d *JdRTA) GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error) {
	if len(queryKeys) == 0 {
		return nil, errors.New("query empty")
	}

	var deviceInfos []*JdRtaDeviceInfo
	// keep order with `device_utils.GetDeviceIdWithType`
	if len(data.GetIdfa()) > 0 {
		deviceInfos = append(deviceInfos, &JdRtaDeviceInfo{
			DeviceId:   md5_utils.GetMd5String(data.GetIdfa()),
			DeviceType: JdRtaDeviceTypeIDFA,
		})
	} else if len(data.GetMd5Idfa()) > 0 {
		deviceInfos = append(deviceInfos, &JdRtaDeviceInfo{
			DeviceId:   strings.ToLower(data.GetMd5Idfa()),
			DeviceType: JdRtaDeviceTypeIDFA,
		})
	}

	if len(data.GetCaid()) > 0 {
		deviceInfos = append(deviceInfos, &JdRtaDeviceInfo{
			DeviceId:   md5_utils.GetMd5String(data.GetCaid()),
			DeviceType: JdRtaDeviceTypeCAID,
		})
	} else if len(data.GetMd5Caid()) > 0 {
		deviceInfos = append(deviceInfos, &JdRtaDeviceInfo{
			DeviceId:   strings.ToLower(data.GetMd5Caid()),
			DeviceType: JdRtaDeviceTypeCAID,
		})
	}

	if len(data.GetImei()) > 0 {
		deviceInfos = append(deviceInfos, &JdRtaDeviceInfo{
			DeviceId:   md5_utils.GetMd5String(data.GetImei()),
			DeviceType: JdRtaDeviceTypeIMEI,
		})
	} else if len(data.GetMd5Imei()) > 0 {
		deviceInfos = append(deviceInfos, &JdRtaDeviceInfo{
			DeviceId:   strings.ToLower(data.GetMd5Imei()),
			DeviceType: JdRtaDeviceTypeIMEI,
		})
	}

	if len(data.GetOaid()) > 0 {
		deviceInfos = append(deviceInfos, &JdRtaDeviceInfo{
			DeviceId:   md5_utils.GetMd5String(data.GetOaid()),
			DeviceType: JdRtaDeviceTypeOAID,
		})
	} else if len(data.GetMd5Oaid()) > 0 {
		deviceInfos = append(deviceInfos, &JdRtaDeviceInfo{
			DeviceId:   strings.ToLower(data.GetMd5Oaid()),
			DeviceType: JdRtaDeviceTypeOAID,
		})
	}

	if len(deviceInfos) == 0 {
		return nil, errors.New("no device id")
	}

	req := &JdRtaRequest{
		ReqId:       data.GetRequestId(),
		RtaIds:      queryKeys,
		DeviceInfos: deviceInfos,
	}

	payload, err := sonic.Marshal(req)
	if err != nil {
		d.log.WithError(err).WithField("payload", string(payload)).Error("dmp request json marshal error")
		return nil, err
	}
	if data.GetIsDebug() {
		d.log.WithField("payload", string(payload)).Info("send dmp request")
	}

	now := time_utils.GetTimeNowTime().Format("20060102150405")
	httpRequest := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(httpRequest)
	httpRequest.Header.SetMethod(fasthttp.MethodPost)
	httpRequest.Header.SetContentType("application/json;charset=utf-8")
	httpRequest.Header.Set("Token", d.token)
	httpRequest.Header.Set("Time", now)
	httpRequest.Header.Set("Authorization", md5_utils.GetMd5String(d.token+now+d.secret))
	httpRequest.SetRequestURI(d.url)
	httpRequest.SetBody(payload)
	httpResponse := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(httpResponse)

	if err := d.httpClient.DoTimeout(httpRequest, httpResponse, d.timeout); err != nil {
		return nil, err
	}

	respData := httpResponse.Body()
	if data.GetIsDebug() {
		d.log.WithField("response", string(respData)).Info("get dmp response")
	}

	if httpResponse.StatusCode() > 399 {
		d.zap.L().Info("dmp response code error", zap.String("payload", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(payload))))), zap.String("code", fmt.Sprintf("%v", httpResponse.StatusCode())), zap.String("response", fmt.Sprintf("%v", string(respData))))
		return nil, nil
	}

	response := &JdRtaResponse{}
	if err = sonic.Unmarshal(respData, response); err != nil {
		d.log.WithError(err).WithField("response", string(respData)).Error("dmp response json unmarshal error")
		return nil, err
	}

	if response.Code != 2000 || len(response.Data) < 1 {
		return nil, nil
	}

	queryMap := make(map[string]struct{})
	for _, key := range queryKeys {
		queryMap[key] = struct{}{}
	}
	result := make([]string, 0)
	for _, res := range response.Data {
		if res.Status == "1" && len(res.DeviceIds) > 0 {
			if _, ok := queryMap[res.RtaId]; ok {
				result = append(result, res.RtaId)
			}
		}
	}

	// do callback
	if len(result) > 0 && len(d.callbackUrl) > 0 {
		go d.DoCallback(data, req, result[0])
	}

	return result, nil
}

func (d *JdRTA) DoCallback(data dmp_provider.DmpRequestData, req *JdRtaRequest, rtaId string) error {
	cbReq := &ZhongGuangCallbackRequest{
		ReqId:       req.ReqId,
		DeviceInfos: req.DeviceInfos,
		RtaId:       rtaId,
	}

	payload, err := sonic.Marshal(cbReq)
	if err != nil {
		d.log.WithError(err).WithField("payload", string(payload)).Error("dmp callback json marshal error")
		return err
	}
	if data.GetIsDebug() {
		d.log.WithField("payload", string(payload)).Info("send dmp callback")
	}

	now := time_utils.GetTimeNowTime().Format("20060102150405")
	httpRequest := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(httpRequest)
	httpRequest.Header.SetMethod(fasthttp.MethodPost)
	httpRequest.Header.SetContentType("application/json;charset=utf-8")
	httpRequest.Header.Set("Token", d.callbackToken)
	httpRequest.Header.Set("Time", now)
	httpRequest.Header.Set("Authorization", md5_utils.GetMd5String(d.callbackToken+now+d.callbackSecret))
	httpRequest.SetRequestURI(d.callbackUrl)
	httpRequest.SetBody(payload)
	httpResponse := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(httpResponse)

	if err := d.httpClient.Do(httpRequest, httpResponse); err != nil {
		return err
	}

	if data.GetIsDebug() {
		respData := httpResponse.Body()
		d.log.WithField("response", string(respData)).Info("get dmp callback response")
	}
	return nil
}
