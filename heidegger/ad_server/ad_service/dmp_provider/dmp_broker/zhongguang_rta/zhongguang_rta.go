package zhongguang_rta

import (
	"errors"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/simple_kv_data"
	"gitlab.com/dev/heidegger/library/utils/time_utils"
	"fmt"
)

// Deprecated, use JdRTA
type ZhongGuangRTA struct {
	dmpId   utils.ID
	url     string
	timeout time.Duration
	token   string
	secret  string

	httpClient *fasthttp.Client
	log        *zap.Logger
}

type ZhongGuangRtaDeviceType string

const (
	ZhongGuangRtaDeviceTypeIDFA = "idfa"
	ZhongGuangRtaDeviceTypeOAID = "oaid"
	ZhongGuangRtaDeviceTypeCAID = "caid"
	ZhongGuangRtaDeviceTypeIMEI = "imei"
)

type ZhongGuangRtaDeviceInfo struct {
	DeviceId   string                  `json:"deviceId"`   // 原值加密 MD5 值，小写
	DeviceType ZhongGuangRtaDeviceType `json:"deviceType"` // idfa/oaid/caid/imei
}

type ZhongGuangRtaRequest struct {
	ReqId       string                     `json:"reqId"`
	DeviceInfos []*ZhongGuangRtaDeviceInfo `json:"deviceInfos"`
	RtaIds      []string                   `json:"rtaIds,omitempty"`
}

type ZhongGuangRtaResponse struct {
	Status int    `json:"status"`
	Msg    string `json:"msg"`
	Data   []struct {
		RtaId     string   `json:"rtaId"`
		DeviceIds []string `json:"deviceIds"`
		DataTime  string   `json:"dataTime"` // yyyy-MM-dd HH:mm:ss
		Status    string   `json:"status"`
	} `json:"data"`
}

func NewZhongGuangRTA(dmpId utils.ID) *ZhongGuangRTA {
	return &ZhongGuangRTA{
		dmpId:      dmpId,
		httpClient: net_utils.CreateFastHttpClient(time.Second*3, time.Second*60),
		log:        zap.L().With(zap.String("dmp", "ZhongGuangRTA")),
	}
}

func (d *ZhongGuangRTA) SetConfigString(config string) error {
	kv := simple_kv_data.SimpleKeyValue{}
	if err := kv.UnmarshalJson([]byte(config)); err != nil {
		return err
	}

	url, err := kv.GetString("url")
	if err != nil {
		return err
	}
	token, err := kv.GetString("token")
	if err != nil {
		return err
	}
	secret, err := kv.GetString("secret")
	if err != nil {
		return err
	}

	timeout, err := kv.GetInt64("timeout_ms")
	if err != nil {
		timeout = 100
	}

	d.url = url
	d.token = token
	d.secret = secret
	d.timeout = time.Duration(timeout) * time.Millisecond

	return nil
}

func (d *ZhongGuangRTA) SetUrl(url string) {
	d.url = url
}

func (d *ZhongGuangRTA) SetTimeout(timeout time.Duration) {
	d.timeout = timeout
}

func (d *ZhongGuangRTA) Start() error {
	return nil
}

func (d *ZhongGuangRTA) Stop() {
}

func (d *ZhongGuangRTA) GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error) {
	if len(queryKeys) == 0 {
		return nil, errors.New("query empty")
	}

	var deviceInfos []*ZhongGuangRtaDeviceInfo
	// keep order with `device_utils.GetDeviceIdWithType`
	if len(data.GetIdfa()) > 0 {
		deviceInfos = append(deviceInfos, &ZhongGuangRtaDeviceInfo{
			DeviceId:   md5_utils.GetMd5String(data.GetIdfa()),
			DeviceType: ZhongGuangRtaDeviceTypeIDFA,
		})
	} else if len(data.GetMd5Idfa()) > 0 {
		deviceInfos = append(deviceInfos, &ZhongGuangRtaDeviceInfo{
			DeviceId:   strings.ToLower(data.GetMd5Idfa()),
			DeviceType: ZhongGuangRtaDeviceTypeIDFA,
		})
	}

	if len(data.GetCaid()) > 0 {
		deviceInfos = append(deviceInfos, &ZhongGuangRtaDeviceInfo{
			DeviceId:   md5_utils.GetMd5String(data.GetCaid()),
			DeviceType: ZhongGuangRtaDeviceTypeCAID,
		})
	} else if len(data.GetMd5Caid()) > 0 {
		deviceInfos = append(deviceInfos, &ZhongGuangRtaDeviceInfo{
			DeviceId:   strings.ToLower(data.GetMd5Caid()),
			DeviceType: ZhongGuangRtaDeviceTypeCAID,
		})
	}

	if len(data.GetImei()) > 0 {
		deviceInfos = append(deviceInfos, &ZhongGuangRtaDeviceInfo{
			DeviceId:   md5_utils.GetMd5String(data.GetImei()),
			DeviceType: ZhongGuangRtaDeviceTypeIMEI,
		})
	} else if len(data.GetMd5Imei()) > 0 {
		deviceInfos = append(deviceInfos, &ZhongGuangRtaDeviceInfo{
			DeviceId:   strings.ToLower(data.GetMd5Imei()),
			DeviceType: ZhongGuangRtaDeviceTypeIMEI,
		})
	}

	if len(data.GetOaid()) > 0 {
		deviceInfos = append(deviceInfos, &ZhongGuangRtaDeviceInfo{
			DeviceId:   md5_utils.GetMd5String(data.GetOaid()),
			DeviceType: ZhongGuangRtaDeviceTypeOAID,
		})
	} else if len(data.GetMd5Oaid()) > 0 {
		deviceInfos = append(deviceInfos, &ZhongGuangRtaDeviceInfo{
			DeviceId:   strings.ToLower(data.GetMd5Oaid()),
			DeviceType: ZhongGuangRtaDeviceTypeOAID,
		})
	}

	if len(deviceInfos) == 0 {
		return nil, errors.New("no device id")
	}

	req := &ZhongGuangRtaRequest{
		ReqId:       data.GetRequestId(),
		RtaIds:      queryKeys,
		DeviceInfos: deviceInfos,
	}

	payload, err := sonic.Marshal(req)
	if err != nil {
		d.log.WithError(err).WithField("payload", string(payload)).Error("dmp request json marshal error")
		return nil, err
	}
	if data.GetIsDebug() {
		d.log.WithField("payload", string(payload)).Info("send dmp request")
	}

	now := time_utils.GetTimeNowTime().Format("20060102150405")
	httpRequest := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(httpRequest)
	httpRequest.Header.SetMethod(fasthttp.MethodPost)
	httpRequest.Header.SetContentType("application/json;charset=utf-8")
	httpRequest.Header.Set("Token", d.token)
	httpRequest.Header.Set("Time", now)
	httpRequest.Header.Set("Authorization", md5_utils.GetMd5String(d.token+now+d.secret))
	httpRequest.SetRequestURI(d.url)
	httpRequest.SetBody(payload)
	httpResponse := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(httpResponse)

	if err := d.httpClient.DoTimeout(httpRequest, httpResponse, d.timeout); err != nil {
		return nil, err
	}

	respData := httpResponse.Body()
	if data.GetIsDebug() {
		d.log.WithField("response", string(respData)).Info("get dmp response")
	}

	if httpResponse.StatusCode() > 399 {
		d.zap.L().Info("dmp response code error", zap.String("payload", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(payload))))), zap.String("code", fmt.Sprintf("%v", httpResponse.StatusCode())), zap.String("response", fmt.Sprintf("%v", string(respData))))
		return nil, nil
	}

	response := &ZhongGuangRtaResponse{}
	if err = sonic.Unmarshal(respData, response); err != nil {
		d.log.WithError(err).WithField("response", string(respData)).Error("dmp response json unmarshal error")
		return nil, err
	}

	if response.Status != 1 || len(response.Data) < 1 {
		return nil, nil
	}

	queryMap := make(map[string]struct{})
	for _, key := range queryKeys {
		queryMap[key] = struct{}{}
	}
	result := make([]string, 0)
	for _, res := range response.Data {
		if res.Status == "1" && len(res.DeviceIds) > 0 {
			if _, ok := queryMap[res.RtaId]; ok {
				result = append(result, res.RtaId)
			}
		}
	}

	return result, nil
}
