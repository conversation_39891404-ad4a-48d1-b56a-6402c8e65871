package taobao_rta

import (
	"fmt"
	"errors"
	"sort"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/device_utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/simple_kv_data"
)

// Doc: https://open.taobao.com/api.htm?cid=1&docId=48589&docType=2
type TaobaoRTA struct {
	dmpId   utils.ID
	url     string
	timeout time.Duration
	appKey  string
	secret  string
	spaceId string
	channle string

	httpClient *fasthttp.Client
	log        *zap.Logger
}

type TaobaoRtaRequest struct {
	Method             string `json:"method"`
	AppKey             string `json:"app_key"`
	Session            string `json:"session,omitempty"`
	Timestamp          string `json:"timestamp"`
	V                  string `json:"v"`
	SignMethod         string `json:"sign_method"`
	Sign               string `json:"sign"`
	Format             string `json:"format,omitempty"`
	Simplify           string `json:"simplify,omitempty"`
	Profile            string `json:"profile,omitempty"`
	OaidMd5            string `json:"oaid_md5,omitempty"`
	CaidMd5            string `json:"caid_md5,omitempty"`
	IdfaMd5            string `json:"idfa_md5,omitempty"`
	ImeiMd5            string `json:"imei_md5,omitempty"`
	Oaid               string `json:"oaid,omitempty"`
	Idfa               string `json:"idfa,omitempty"`
	Imei               string `json:"imei,omitempty"`
	Os                 string `json:"os,omitempty"`
	AdvertisingSpaceId string `json:"advertising_space_id"`
	Channel            string `json:"channel"`
}

type TaobaoRtaResponse struct {
	RequestId                        string `json:"request_id"`
	ErrorResponse                    string `json:"error_response"`
	Code                             string `json:"code"`
	Msg                              string `json:"msg"`
	SubCode                          string `json:"sub_code"`
	SubMsg                           string `json:"sub_msg"`
	UsergrowthDhhDeliveryAskResponse struct {
		TaskIdList []string `json:"task_id_list"`
		Errcode    int      `json:"errcode"`
		Result     bool     `json:"result"`
		TaskId     string   `json:"task_id"`
	} `json:"usergrowth_dhh_delivery_ask_response"`
}

func (req *TaobaoRtaRequest) ToMap() map[string]string {
	args := make(map[string]string)
	/*
		val := reflect.ValueOf(*req)
		typ := val.Type()
		for i := 0; i < val.NumField(); i++ {
			field := typ.Field(i)
			fieldValue := val.Field(i)
			tag := field.Tag.Get("json")
			if tag == "" || tag == "-" {
				continue
			}
			if strings.Contains(tag, ",omitempty") && fieldValue.IsZero() {
				continue
			}
			jsonKey := strings.Split(tag, ",")[0]
			var valueStr string
			switch fieldValue.Kind() {
			case reflect.String:
				valueStr = fieldValue.String()
			case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
				valueStr = strconv.FormatInt(fieldValue.Int(), 10)
			case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
				valueStr = strconv.FormatUint(fieldValue.Uint(), 10)
			case reflect.Float32, reflect.Float64:
				valueStr = strconv.FormatFloat(fieldValue.Float(), 'f', -1, 64)
			case reflect.Bool:
				valueStr = strconv.FormatBool(fieldValue.Bool())
			default:
				valueStr = fmt.Sprintf("%v", fieldValue.Interface())
			}
			args[jsonKey] = valueStr
		}
	*/

	args["method"] = req.Method
	args["app_key"] = req.AppKey
	if len(req.Session) > 0 {
		args["session"] = req.Session
	}
	args["timestamp"] = req.Timestamp
	args["v"] = req.V
	args["sign_method"] = req.SignMethod
	args["sign"] = req.Sign
	if len(req.Format) > 0 {
		args["format"] = req.Format
	}
	if len(req.Simplify) > 0 {
		args["simplify"] = req.Simplify
	}
	if len(req.Profile) > 0 {
		args["profile"] = req.Profile
	}
	if len(req.OaidMd5) > 0 {
		args["oaid_md5"] = req.OaidMd5
	}
	if len(req.CaidMd5) > 0 {
		args["caid_md5"] = req.CaidMd5
	}
	if len(req.IdfaMd5) > 0 {
		args["idfa_md5"] = req.IdfaMd5
	}
	if len(req.ImeiMd5) > 0 {
		args["imei_md5"] = req.ImeiMd5
	}
	if len(req.Oaid) > 0 {
		args["oaid"] = req.Oaid
	}
	if len(req.Idfa) > 0 {
		args["idfa"] = req.Idfa
	}
	if len(req.Imei) > 0 {
		args["imei"] = req.Imei
	}
	if len(req.Os) > 0 {
		args["os"] = req.Os
	}
	args["advertising_space_id"] = req.AdvertisingSpaceId
	args["channel"] = req.Channel

	return args
}

func NewTaobaoRTA(dmpId utils.ID) *TaobaoRTA {
	return &TaobaoRTA{
		dmpId:      dmpId,
		httpClient: net_utils.CreateFastHttpClient(time.Second*3, time.Second*60),
		log:        zap.L().With(zap.String("dmp", "TaobaoRTA")),
	}
}

func (d *TaobaoRTA) SetConfigString(config string) error {
	kv := simple_kv_data.SimpleKeyValue{}
	if err := kv.UnmarshalJson([]byte(config)); err != nil {
		return err
	}

	url, err := kv.GetString("url")
	if err != nil {
		return err
	}
	appKey, err := kv.GetString("app_key")
	if err != nil {
		return err
	}
	secret, err := kv.GetString("secret")
	if err != nil {
		return err
	}
	spaceId, err := kv.GetString("space_id")
	if err != nil {
		return err
	}
	channel, err := kv.GetString("channel")
	if err != nil {
		return err
	}

	timeout, err := kv.GetInt64("timeout_ms")
	if err != nil {
		timeout = 100
	}

	d.url = url
	d.appKey = appKey
	d.secret = secret
	d.spaceId = spaceId
	d.channle = channel
	d.timeout = time.Duration(timeout) * time.Millisecond

	return nil
}

func (d *TaobaoRTA) SetUrl(url string) {
	d.url = url
}

func (d *TaobaoRTA) SetTimeout(timeout time.Duration) {
	d.timeout = timeout
}

func (d *TaobaoRTA) Start() error {
	return nil
}

func (d *TaobaoRTA) Stop() {
}

func (d *TaobaoRTA) GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error) {
	if len(queryKeys) == 0 {
		return nil, errors.New("query empty")
	}

	req := &TaobaoRtaRequest{
		Method:             "taobao.usergrowth.dhh.delivery.ask",
		AppKey:             d.appKey,
		Timestamp:          time.Now().Format("2006-01-02 15:04:05"),
		V:                  "2.0",
		SignMethod:         "md5",
		Sign:               "",
		Format:             "json",
		Simplify:           "true",
		AdvertisingSpaceId: d.spaceId,
		Channel:            d.channle,
	}

	switch data.GetOsType() {
	case entity.OsTypeAndroid:
		req.Os = "0"
	case entity.OsTypeIOS:
		req.Os = "1"
	case entity.OsTypeWindowsPhone:
		req.Os = "2"
	default:
		req.Os = "3"
	}

	if len(data.GetOaid()) > 0 {
		req.Oaid = data.GetOaid()
	} else if len(data.GetMd5Oaid()) > 0 {
		req.OaidMd5 = strings.ToLower(data.GetMd5Oaid())
	}

	if len(data.GetIdfa()) > 0 {
		req.Idfa = data.GetIdfa()
	} else if len(data.GetMd5Idfa()) > 0 {
		req.IdfaMd5 = strings.ToLower(data.GetMd5Idfa())
	}

	if len(data.GetImei()) > 0 {
		req.Imei = data.GetImei()
	} else if len(data.GetMd5Imei()) > 0 {
		req.ImeiMd5 = strings.ToLower(data.GetMd5Imei())
	}

	if len(data.GetCaid()) > 0 {
		caid := device_utils.GetCaidRaw(data.GetCaid())
		caidV := device_utils.GetCaidVersion(data.GetCaid())

		req.CaidMd5 = caidV + "_" + md5_utils.GetMd5String(caid)
	} else if len(data.GetMd5Caid()) > 0 {
		caid := device_utils.GetCaidRaw(data.GetMd5Caid())
		caidV := device_utils.GetCaidVersion(data.GetMd5Caid())

		req.CaidMd5 = caidV + "_" + strings.ToLower(caid)
	}

	req.Sign = d.getSignature(req)

	if data.GetIsDebug() {
		reqData, _ := sonic.Marshal(req)
		d.log.WithField("payload", reqData).Info("send dmp request")
	}

	httpRequest := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(httpRequest)
	httpRequest.Header.SetMethod(fasthttp.MethodPost)
	httpRequest.Header.SetContentType("application/x-www-form-urlencoded;charset=utf-8")
	httpRequest.SetRequestURI(d.url)
	d.buildPostArgs(httpRequest.PostArgs(), req)
	httpResponse := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(httpResponse)

	if err := d.httpClient.DoTimeout(httpRequest, httpResponse, d.timeout); err != nil {
		return nil, err
	}

	if data.GetIsDebug() {
		d.log.WithField("response", httpResponse.Body()).Info("get dmp response")
	}

	response := &TaobaoRtaResponse{}
	err := sonic.Unmarshal(httpResponse.Body(), response)
	if err != nil {
		d.log.WithError(err).WithField("response", httpResponse.Body()).Error("dmp response json unmarshal error")
		return nil, err
	}

	if response.UsergrowthDhhDeliveryAskResponse.Errcode != 0 {
		d.log.WithError(err).WithField("response", httpResponse.Body()).Error("dmp response error")
		return nil, nil
	}

	queryMap := make(map[string]struct{})
	for _, key := range queryKeys {
		queryMap[key] = struct{}{}
	}
	result := make([]string, 0)
	if response.UsergrowthDhhDeliveryAskResponse.Result {
		for _, id := range response.UsergrowthDhhDeliveryAskResponse.TaskIdList {
			if _, ok := queryMap[id]; ok {
				result = append(result, id)
			}
		}
	}

	return result, nil
}

func (d *TaobaoRTA) getSignature(req *TaobaoRtaRequest) string {
	reqMap := req.ToMap()
	delete(reqMap, "sign")
	keys := make([]string, 0, len(reqMap))
	for k := range reqMap {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	var sb strings.Builder
	sb.WriteString(d.secret)
	for _, k := range keys {
		sb.WriteString(k)
		sb.WriteString(reqMap[k])
	}
	sb.WriteString(d.secret)
	return md5_utils.GetMd5StringUpper(sb.String())
}

func (d *TaobaoRTA) buildPostArgs(args *fasthttp.Args, req *TaobaoRtaRequest) {
	for k, v := range req.ToMap() {
		args.Add(k, v)
	}
}
