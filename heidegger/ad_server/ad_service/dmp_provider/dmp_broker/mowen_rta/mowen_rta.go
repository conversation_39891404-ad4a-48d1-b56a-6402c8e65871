package mowen_rta

import (
	"errors"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"github.com/valyala/fasthttp"
	"gitlab.com/dev/heidegger/ad_server/ad_service/dmp_provider"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/simple_kv_data"
	"fmt"
)

// Doc: https://www.yuque.com/tianxia-ppuz4/gu1bwr/uedkganxqvsey13i
type MoWenRTA struct {
	dmpId   utils.ID
	url     string
	timeout time.Duration
	channle string
	secret  string

	httpClient *fasthttp.Client
	log        *zap.Logger
}

type MoWenRtaRequest struct {
	ReqId        string   `json:"req_id"`
	Channel      string   `json:"channel_id"`
	RtaIds       []string `json:"rta_ids"`
	Imei         string   `json:"imei,omitempty"`
	ImeiMd5      string   `json:"imei_md5,omitempty"`
	AndroidId    string   `json:"android_id,omitempty"`
	AndroidIdMd5 string   `json:"android_id_md5,omitempty"`
	Oaid         string   `json:"oaid,omitempty"`
	OaidMd5      string   `json:"oaid_md5,omitempty"`
	Idfa         string   `json:"idfa,omitempty"`
	IdfaMd5      string   `json:"idfa_md5,omitempty"`
	Caid         string   `json:"caid,omitempty"`
	CaidMd5      string   `json:"caid_md5,omitempty"`
	Caids        []string `json:"caids,omitempty"`
	Os           int      `json:"os,omitempty"`
	Osv          string   `json:"osv,omitempty"`
	Ip           string   `json:"ip,omitempty"`
	Ua           string   `json:"ua,omitempty"`
	Brand        string   `json:"brand,omitempty"`
	Model        string   `json:"model,omitempty"`
}

type MoWenRtaResponse struct {
	Code   int      `json:"code"`
	ReqId  string   `json:"req_id,omitempty"`
	RtaIds []string `json:"rta_ids,omitempty"`
}

func NewMoWenRTA(dmpId utils.ID) *MoWenRTA {
	return &MoWenRTA{
		dmpId:      dmpId,
		httpClient: net_utils.CreateFastHttpClient(time.Second*3, time.Second*60),
		log:        zap.L().With(zap.String("dmp", "MoWenRTA")),
	}
}

func (d *MoWenRTA) SetConfigString(config string) error {
	kv := simple_kv_data.SimpleKeyValue{}
	if err := kv.UnmarshalJson([]byte(config)); err != nil {
		return err
	}

	url, err := kv.GetString("url")
	if err != nil {
		return err
	}
	channel, err := kv.GetString("channel")
	if err != nil {
		return err
	}
	secret, err := kv.GetString("secret")
	if err != nil {
		return err
	}

	timeout, err := kv.GetInt64("timeout_ms")
	if err != nil {
		timeout = 100
	}

	d.url = url
	d.channle = channel
	d.secret = secret
	d.timeout = time.Duration(timeout) * time.Millisecond

	return nil
}

func (d *MoWenRTA) SetUrl(url string) {
	d.url = url
}

func (d *MoWenRTA) SetTimeout(timeout time.Duration) {
	d.timeout = timeout
}

func (d *MoWenRTA) Start() error {
	return nil
}

func (d *MoWenRTA) Stop() {
}

func (d *MoWenRTA) GetDmpData(data dmp_provider.DmpRequestData, queryKeys []string) ([]string, error) {
	if len(queryKeys) == 0 {
		return nil, errors.New("query empty")
	}

	requestId := data.GetRequestId()
	if len(requestId) < 1 {
		requestId = utils.NewUUID()
	}

	req := &MoWenRtaRequest{
		ReqId:        requestId,
		Channel:      d.channle,
		RtaIds:       queryKeys,
		Imei:         data.GetImei(),
		ImeiMd5:      strings.ToUpper(data.GetMd5Imei()),
		AndroidId:    data.GetAndroidId(),
		AndroidIdMd5: strings.ToUpper(data.GetMd5AndroidId()),
		Oaid:         data.GetOaid(),
		OaidMd5:      strings.ToUpper(data.GetMd5Oaid()),
		Idfa:         data.GetIdfa(),
		IdfaMd5:      strings.ToUpper(data.GetMd5Idfa()),
		Caid:         data.GetCaid(),
		CaidMd5:      strings.ToUpper(data.GetMd5Caid()),
		Ip:           data.GetRequestIp(),
	}

	switch data.GetOsType() {
	case entity.OsTypeIOS:
		req.Os = 1
	case entity.OsTypeAndroid:
		req.Os = 2
	default:
		req.Os = 0
	}

	payload, err := sonic.Marshal(req)
	if err != nil {
		d.log.WithError(err).WithField("payload", string(payload)).Error("dmp request json marshal error")
		return nil, err
	}
	if data.GetIsDebug() {
		d.log.WithField("payload", string(payload)).Info("send dmp request")
	}

	httpRequest := fasthttp.AcquireRequest()
	defer fasthttp.ReleaseRequest(httpRequest)
	httpRequest.Header.SetMethod(fasthttp.MethodPost)
	httpRequest.Header.SetContentType("application/json")
	httpRequest.Header.Set("X-RTA-ID", requestId)
	httpRequest.Header.Set("X-RTA-SECRET", md5_utils.GetMd5String(requestId+d.secret))
	httpRequest.SetRequestURI(d.url)
	httpRequest.SetBody(payload)
	httpResponse := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(httpResponse)

	if err := d.httpClient.DoTimeout(httpRequest, httpResponse, d.timeout); err != nil {
		return nil, err
	}

	respData := httpResponse.Body()
	if data.GetIsDebug() {
		d.log.WithField("response", string(respData)).Info("get dmp response")
	}

	if httpResponse.StatusCode() > 399 {
		d.zap.L().Info("dmp response code error", zap.String("payload", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(payload))))), zap.String("code", fmt.Sprintf("%v", httpResponse.StatusCode())), zap.String("response", fmt.Sprintf("%v", string(respData))))
		return nil, nil
	}

	response := &MoWenRtaResponse{}
	if err = sonic.Unmarshal(respData, response); err != nil {
		d.log.WithError(err).WithField("response", string(respData)).Error("dmp response json unmarshal error")
		return nil, err
	}

	// 未命中
	if response.Code != 0 {
		d.zap.L().Debug("dmp response error", zap.String("payload", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(payload))))), zap.String("response", fmt.Sprintf("%v", string(respData))))
		return nil, nil
	}

	queryMap := make(map[string]struct{})
	for _, key := range queryKeys {
		queryMap[key] = struct{}{}
	}
	result := make([]string, 0)
	for _, id := range response.RtaIds {
		if _, ok := queryMap[id]; ok {
			result = append(result, id)
		}
	}

	return result, nil
}
