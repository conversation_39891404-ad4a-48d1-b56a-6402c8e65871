package traffic_device_allocator

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/device_allocator/device_allocator"
	"gitlab.com/dev/heidegger/device_allocator/device_allocator_client"
	"gitlab.com/dev/heidegger/device_allocator/device_allocator_service/proto/device_allocator_proto"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/md5_utils"
)

type DeviceAllocator struct {
	client *device_allocator_client.DeviceAllocatorRapidClient
}

func NewDeviceAllocator(address []string, timeout int) *DeviceAllocator {
	return &DeviceAllocator{
		client: device_allocator_client.NewDeviceAllocatorRapidClient(address, timeout),
	}
}

func (da *DeviceAllocator) Start() error {
	if err := da.client.Start(); err != nil {
		return err
	}

	return nil
}

func (da *DeviceAllocator) Stop() {
	da.client.Stop()
}

func (da *DeviceAllocator) AllocDevice(request DeviceAllocExtractInterface) (*DeviceAlloc, error) {
	req := device_allocator_proto.DeviceAllocatorRequest{}
	req.Items = []device_allocator_proto.DeviceAllocatorRequest_Item{da.dataExtractToRequestItem(request)}

	resp, err := da.client.Get(&req)
	if err != nil {
		return nil, err
	}

	if len(resp.Items) != 1 {
		return nil, fmt.Errorf("[DeviceAllocator] client response size err")
	}

	return da.responseItemToDeviceAlloc(&resp.Items[0]), nil
}

func (da *DeviceAllocator) AllocDeviceBatch(requestList []DeviceAllocExtractInterface) (map[utils.ID]*DeviceAlloc, error) {
	if len(requestList) == 0 {
		return nil, nil
	}

	req := device_allocator_proto.DeviceAllocatorRequest{
		Items: make([]device_allocator_proto.DeviceAllocatorRequest_Item, len(requestList)),
	}

	for i := range requestList {
		req.Items[i] = da.dataExtractToRequestItem(requestList[i])
	}

	resp, err := da.client.Get(&req)
	if err != nil {
		return nil, err
	}

	data, _ := json.Marshal(req)
	zap.L().Info("AllocDeviceBatch req", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(data))))))

	if len(resp.Items) != len(requestList) {
		return nil, fmt.Errorf("[DeviceAllocator] client response size err")
	}

	result := make(map[utils.ID]*DeviceAlloc, len(requestList))
	for i := range resp.Items {
		deviceAlloc := da.responseItemToDeviceAlloc(&resp.Items[i])
		result[deviceAlloc.Id] = deviceAlloc
	}
	return result, nil
}

func (da *DeviceAllocator) dataExtractToRequestItem(request DeviceAllocExtractInterface) device_allocator_proto.DeviceAllocatorRequest_Item {
	allocSetting := request.GetDeviceAllocationSetting()
	item := device_allocator_proto.DeviceAllocatorRequest_Item{}
	item.Id = int32(request.GetAdId())
	item.Name = allocSetting.PackageName
	item.KeyType = int32(allocSetting.KeyType)
	item.DeviceType = int32(da.getDeviceTypeFromRequest(request))
	if allocSetting.IgnoreGeo {
		item.GeoCode = 0
	} else {
		item.GeoCode = int64(request.GetGeoCode())
	}
	return item
}

func (da *DeviceAllocator) getDeviceTypeFromRequest(request DeviceAllocExtractInterface) device_allocator.DeviceType {
	allocSetting := request.GetDeviceAllocationSetting()
	if allocSetting.UseOsReplace {
		return device_allocator.DeviceTypeNone
	}

	if request.GetOsType() == entity.OsTypeIOS {
		return device_allocator.DeviceTypeIOS
	}

	if request.GetDeviceType() == entity.DeviceTypeOtt {
		return device_allocator.DeviceTypeOTT
	}

	return device_allocator.DeviceTypeAndroid
}

func (da *DeviceAllocator) responseItemToDeviceAlloc(item *device_allocator_proto.DeviceAllocatorResponse_Item) *DeviceAlloc {
	result := &DeviceAlloc{
		Code: int(item.Code),
		Id:   utils.ID(item.Id),
	}
	if result.Code != 0 {
		return result
	}

	result.ReplaceDeviceId = item.DeviceId
	result.ReplaceMac = item.MacMd5
	result.ReplaceUa = item.UserAgent
	result.ReplaceBrand = item.DeviceBrand
	result.ReplaceModel = item.DeviceModel
	result.ReplaceOsVersion = item.OsVersion
	result.BootMark = item.BootMark
	result.UpdateMark = item.UpdateMark
	result.ReplaceScreenWidth = uint32(item.ScreenWidth)
	result.ReplaceScreenHeight = uint32(item.ScreenHeight)
	result.ReplaceCaid = item.Caid
	result.ReplaceAndroidId = item.AndroidId
	result.ReplaceAndroidIdMd5 = item.AndroidIdMd5

	switch device_allocator.DeviceIdType(item.DeviceIdType) {
	case device_allocator.DeviceIdTypeMd5IMEI:
		result.ReplaceImeiMd5 = result.ReplaceDeviceId
		result.ReplaceDeviceIdMd5 = result.ReplaceDeviceId
		result.ReplaceOsType = entity.OsTypeAndroid
		result.ReplaceDeviceType = entity.DeviceTypeMobile

	case device_allocator.DeviceIdTypeRawIdfa:
		result.ReplaceIdfa = result.ReplaceDeviceId
		result.ReplaceIdfaMd5 = md5_utils.GetMd5StringUpper(result.ReplaceDeviceId)
		result.ReplaceDeviceIdMd5 = result.ReplaceIdfaMd5
		result.ReplaceOsType = entity.OsTypeIOS
		result.ReplaceDeviceType = entity.DeviceTypeMobile

	case device_allocator.DeviceIdTypeRawOAID:
		result.ReplaceOaid = result.ReplaceDeviceId
		result.ReplaceOaidMd5 = md5_utils.GetMd5StringUpper(result.ReplaceDeviceId)
		result.ReplaceDeviceIdMd5 = result.ReplaceOaidMd5
		result.ReplaceOsType = entity.OsTypeAndroid
		result.ReplaceDeviceType = entity.DeviceTypeMobile

	case device_allocator.DeviceIdTypeMd5MAC:
		result.ReplaceMac = result.ReplaceDeviceId
		result.ReplaceDeviceIdMd5 = result.ReplaceDeviceId
		result.ReplaceOsType = entity.OsTypeAndroid
		result.ReplaceDeviceType = entity.DeviceTypeMobile

	case device_allocator.DeviceIdTypeMd5OAID:
		result.ReplaceOaidMd5 = result.ReplaceDeviceId
		result.ReplaceDeviceIdMd5 = result.ReplaceDeviceId
		result.ReplaceOsType = entity.OsTypeAndroid
		result.ReplaceDeviceType = entity.DeviceTypeMobile

	case device_allocator.DeviceIdTypeMd5Idfa:
		result.ReplaceIdfaMd5 = result.ReplaceDeviceId
		result.ReplaceDeviceIdMd5 = result.ReplaceDeviceId
		result.ReplaceOsType = entity.OsTypeIOS
		result.ReplaceDeviceType = entity.DeviceTypeMobile
	}

	return result
}
