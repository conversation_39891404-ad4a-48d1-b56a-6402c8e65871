package ad_service

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"fmt"
)

func (resp *AdResponse) Dump(contextName string) {
	zap.L().Info("[AdResponse][Dump][] error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", contextName)))), zap.String("param2", fmt.Sprintf("%v", resp.err)))
	zap.L().Info("[AdResponse][Dump][%s] mediaId: , mediaSlotId: , sourceSlotKey:%s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(contextName)))), zap.Int64("id", int64(resp.adRequest.GetMediaId())), resp.adRequest.GetMediaSlotId(), resp.adRequest.GetSourceSlotId())
	zap.L().Info("[AdResponse][Dump][%s] geoCode: , osType: , deviceType: , slotType: %d, slotWidth: %d, slotHeight: %d", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(contextName)))), zap.Int64("param2", int64(resp.adRequest.GeoCode)), zap.Int64("param3", int64(resp.adRequest.Device.GetOsType())),
		resp.adRequest.Device.GetDeviceType(),
		resp.adRequest.GetSlotType(),
		resp.adRequest.GetSlotWidth(),
		resp.adRequest.GetSlotHeight())
	for _, creativeTemplateKey := range resp.adRequest.GetCreativeTemplateKeyList() {
		key := creative_entity.CreativeTemplateKey(creativeTemplateKey)
		zap.L().Info("[AdResponse][Dump][] creativeTemplate", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", contextName)))), zap.String("param2", fmt.Sprintf("%v", key.DebugString())))
	}
	for _, creativeTemplate := range resp.adRequest.GetSlotCreativeTemplateList() {
		zap.L().Info("[AdResponse][Dump][] slotCreativeTemplate", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", contextName)))), zap.String("param2", fmt.Sprintf("%v", creativeTemplate.String())))
	}
	for _, candidate := range resp.totalAdCandidates {
		zap.L().Info("[AdResponse][Dump][%s] ad , code: %s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(contextName)))), zap.Int64("id", int64(candidate.GetAd())).AdId, candidate.GetErrorCode())
		candidate.DumpDetail(contextName)
	}
}

func (a *AdCandidate) DumpDetail(contextName string) {
	switch a.GetErrorCode().Code {
	case err_code.ErrFrequencyCap.Code:
		zap.L().Info("  [AdCandidate][DumpDetail][] frequencyControl:, freqResult:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", contextName)))), zap.String("param2", fmt.Sprintf("%v", a.GetAd())).GetFrequencyControl(),
			a.GetFrequencyResult())
	}
}
