package ad_service

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

type ResponseBuilder func(adRequest *AdRequest, writer <PERSON>ttp<PERSON><PERSON>po<PERSON>) error

type AdResponse struct {
	adRequest *AdRequest

	err                    error
	totalAdCandidates      map[utils.ID]*AdCandidate
	adCandidateList        []*AdCandidate
	filteringCandidateList []*AdCandidate

	responseWriter          HttpResponse
	responseBuilder         ResponseBuilder
	fallbackResponseBuilder ResponseBuilder
}

func (resp *AdResponse) SetError(err error) {
	resp.err = err
}

func (resp *AdResponse) GetError() error {
	return resp.err
}

func (resp *AdResponse) GetAdErrorCode() err_code.AdErrorCode {
	return err_code.ExtractError(resp.err)
}

func (resp *AdResponse) GetTotalAdCandidates() map[utils.ID]*AdCandidate {
	return resp.totalAdCandidates
}

func (resp *AdResponse) GetTotalAdCandidateList() AdCandidateList {
	list := make(AdCandidateList, 0, len(resp.totalAdCandidates))
	for _, candidate := range resp.totalAdCandidates {
		list = append(list, candidate)
	}
	return list
}

func (resp *AdResponse) AddCandidates(ad *entity.Ad) *AdCandidate {
	if resp.totalAdCandidates == nil {
		resp.totalAdCandidates = make(map[utils.ID]*AdCandidate, 0)
	}
	candidate := resp.totalAdCandidates[ad.AdId]
	if candidate == nil {
		candidate = NewAdCandidateWithPool(resp.adRequest, ad)
		resp.totalAdCandidates[ad.AdId] = candidate
	}

	resp.PushCandidates(candidate)
	return candidate
}

func (resp *AdResponse) PushCandidates(candidate *AdCandidate) {
	if candidate.IsFiltered() {
		zap.L().Warn("[AdResponse][PushCandidates] ad  is filtered, code: %d", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(candidate.GetAd())))).AdId, candidate.GetCode())
		return
	}

	resp.filteringCandidateList = append(resp.filteringCandidateList, candidate)
}

func (resp *AdResponse) SwapAndClearCandidate() {
	resp.adCandidateList, resp.filteringCandidateList = resp.filteringCandidateList, resp.adCandidateList
	resp.filteringCandidateList = resp.filteringCandidateList[:0]
}

func (resp *AdResponse) NoCandidate() bool {
	return len(resp.adCandidateList) == 0
}

func (resp *AdResponse) GetAdCandidateList() AdCandidateList {
	return resp.adCandidateList
}

func (resp *AdResponse) GetAdCandidateByAdId(adId utils.ID) *AdCandidate {
	return resp.totalAdCandidates[adId]
}

func (resp *AdResponse) SetHttpResponseWriter(writer HttpResponse) {
	resp.responseWriter = writer
}

func (resp *AdResponse) GetHttpResponseWriter() HttpResponse {
	return resp.responseWriter
}

func (resp *AdResponse) SetResponseBuilder(builder ResponseBuilder) {
	resp.responseBuilder = builder
}

func (resp *AdResponse) GetResponseBuilder() ResponseBuilder {
	return resp.responseBuilder
}

func (resp *AdResponse) SetFallbackResponseBuilder(builder ResponseBuilder) {
	resp.fallbackResponseBuilder = builder
}

func (resp *AdResponse) GetFallbackResponseBuilder() ResponseBuilder {
	return resp.fallbackResponseBuilder
}
