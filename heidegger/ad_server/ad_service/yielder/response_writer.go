package yielder

import (
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
)

type ResponseWriter struct {
}

func NewResponseWriter() *ResponseWriter {
	return &ResponseWriter{}
}

func (w *ResponseWriter) GetTaskName() string {
	return "ResponseWriter"
}

func (w *ResponseWriter) Start() error {
	return nil
}

func (w *ResponseWriter) Stop() {

}

func (w *ResponseWriter) Do(request *ad_service.AdRequest) error {
	if err := w.do(request); err != nil {
		for _, candidate := range request.Response.GetAdCandidateList() {
			candidate.FilterByError(err_code.ErrBrokerResponseMedia)
		}

		request.Response.SwapAndClearCandidate()

		if request.Response.GetFallbackResponseBuilder() != nil {
			request.Response.GetFallbackResponseBuilder()(request, request.Response.GetHttpResponseWriter())
		}
	}

	//if request.GetMediaId() == 1003 && rand.Int31n(10000)%1000 == 0 {
	//	zap.L().Info("[QttTrafficBroker] SendResponse success, candidate:, body:%d", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(//		len(request.Response.GetAdCandidateList()))))),
	//		request.Response.GetHttpResponseWriter().ResponseContentSize())
	//}

	return nil
}

func (w *ResponseWriter) do(request *ad_service.AdRequest) error {
	if len(request.Response.GetAdCandidateList()) > 0 {
		candidate := request.Response.GetAdCandidateList()[0]
		if candidate.GetAd().DspId != 0 && candidate.GetDspResponseAd() == nil {
			zap.L().Info("[BroadcastSearcher] [creative] hasDspAd", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", candidate.GetDspResponseAd())))) != nil)
		}
	}

	if request.Response.GetResponseBuilder() == nil {
		return err_code.ErrInternal.Wrap(fmt.Errorf("no http response builder, path:%s", request.RawHttpRequest.GetUrl()))
	}

	if request.Response.GetHttpResponseWriter() == nil {
		return err_code.ErrInternal.Wrap(fmt.Errorf("no http response writer, path:%s", request.RawHttpRequest.GetUrl()))
	}
	if err := request.Response.GetResponseBuilder()(request, request.Response.GetHttpResponseWriter()); err != nil {
		return err
	}

	return nil
}
