package yielder

import (
	"fmt"
	"gitlab.com/dev/heidegger/ad_server/ad_service"
	"gitlab.com/dev/heidegger/ad_server/ad_service/broker/traffic_sampler"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker_library/app_bundle_statistic_mapping"
	"gitlab.com/dev/heidegger/library/data_aggregator"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"strconv"
	"strings"
)

type StatisticCollector struct {
	dataAggregatorManager     *data_aggregator.DataAggregatorManager
	appBundleStatisticMapping app_bundle_statistic_mapping.AppBundleStatisticMappingInterface

	trafficSampler         traffic_sampler.TrafficSampler
	requestMetrics         *prometheus_helper.LabelCounter
	mediaSlotHistogram     *prometheus_helper.LabelHistogram
	adMetrics              *prometheus_helper.LabelCounter
	adBidPriceHistogram    *prometheus_helper.LabelHistogram
	adChargePriceHistogram *prometheus_helper.LabelHistogram
	//adTrafficLevelHistogram       *prometheus_helper.LabelHistogram
	//adTrafficLevelSentHistogram   *prometheus_helper.LabelHistogram
	mediaRequestTrafficCounter    *prometheus_helper.LabelCounter
	mediaResponseTrafficCounter   *prometheus_helper.LabelCounter
	mediaResponseTrafficHistogram *prometheus_helper.LabelHistogram
	adScoreCounter                *prometheus_helper.LabelCounter
	adScoreSentCounter            *prometheus_helper.LabelCounter
}

func NewStatisticCollector(
	trafficSampler traffic_sampler.TrafficSampler,
	dataAggregatorManager *data_aggregator.DataAggregatorManager,
	appBundleStatisticMapping app_bundle_statistic_mapping.AppBundleStatisticMappingInterface) *StatisticCollector {
	return &StatisticCollector{
		dataAggregatorManager:     dataAggregatorManager,
		trafficSampler:            trafficSampler,
		appBundleStatisticMapping: appBundleStatisticMapping,
		requestMetrics:            prometheus_helper.RegisterLabelCounter("ad_server_request_code", []string{"media_id", "slot_id", "code", "human"}),
		mediaSlotHistogram:        prometheus_helper.RegisterLabelHistogram("ad_server_media_slot", []string{"media_id", "slot_id"}),
		adMetrics:                 prometheus_helper.RegisterLabelCounter("ad_server_ad_code", []string{"ad_id", "code", "human"}),
		adBidPriceHistogram:       prometheus_helper.RegisterLabelHistogramPricingBucket("ad_server_ad_bid_price", []string{"dsp_id", "ad_id"}),
		adChargePriceHistogram:    prometheus_helper.RegisterLabelHistogramPricingBucket("ad_server_ad_charge_price", []string{"dsp_id", "ad_id"}),
		//adTrafficLevelHistogram:     prometheus_helper.RegisterLabelHistogramWithBounds("ad_server_traffic_level", []string{"ad_id"}, prometheus_helper.ScoreUpperBounds),
		//adTrafficLevelSentHistogram: prometheus_helper.RegisterLabelHistogramWithBounds("ad_server_traffic_level_sent", []string{"ad_id"}, prometheus_helper.ScoreUpperBounds),
		mediaRequestTrafficCounter:  prometheus_helper.RegisterLabelCounter("ad_server_media_request_traffic", []string{"media_id"}),
		mediaResponseTrafficCounter: prometheus_helper.RegisterLabelCounter("ad_server_media_response_traffic", []string{"media_id"}),
		adScoreCounter:              prometheus_helper.RegisterLabelCounter("ad_server_ad_score", []string{"ad_id", "score"}),
		adScoreSentCounter:          prometheus_helper.RegisterLabelCounter("ad_server_ad_score_sent", []string{"ad_id", "score"}),
	}
}

func (w *StatisticCollector) GetTaskName() string {
	return "StatisticCollector"
}

func (w *StatisticCollector) Start() error {
	return nil
}

func (w *StatisticCollector) Stop() {

}

func (w *StatisticCollector) Do(request *ad_service.AdRequest) error {
	mediaLabelArray := make([]string, 4)

	if request.Response.GetError() == nil {
		mediaLabelArray[0] = strconv.FormatInt(int64(request.GetMediaId()), 10)
		mediaLabelArray[1] = request.GetMediaSlotId().String()
		mediaLabelArray[2] = "0"
		mediaLabelArray[3] = "参与竞价"
		w.requestMetrics.Inc(mediaLabelArray[:])
	} else {
		adErrorCode, ok := request.Response.GetError().(err_code.AdErrorCode)
		if ok {
			mediaLabelArray[0] = strconv.FormatInt(int64(request.GetMediaId()), 10)
			mediaLabelArray[1] = request.GetMediaSlotId().String()
			mediaLabelArray[2] = strconv.FormatInt(int64(adErrorCode.Code), 10)
			mediaLabelArray[3] = err_code.GetHumanReadableError(adErrorCode.Code)
			w.requestMetrics.Inc(mediaLabelArray[:])
		} else {
			mediaLabelArray[0] = strconv.FormatInt(int64(request.GetMediaId()), 10)
			mediaLabelArray[1] = request.GetMediaSlotId().String()
			mediaLabelArray[2] = "fail"
			mediaLabelArray[3] = "unknown"
			w.requestMetrics.Inc(mediaLabelArray[:])
		}
	}

	w.mediaSlotHistogram.Observe(mediaLabelArray[:2], request.GetProcessTimeSeconds())

	//w.trafficSampler.SampleTraffic(request)

	adLabelArray := make([]string, 3)
	for _, candidate := range request.Response.GetTotalAdCandidates() {
		//if candidate.GetAd().GetAdId() == 81 {
		//	zap.L().Info("ad_id:, ad_type:%d, code:%d", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(candidate.GetAd())))).GetAdId(), candidate.GetAd().GetAdType(), candidate.GetErrorCode().Code)
		//}

		adErrorCode := candidate.GetErrorCode()
		adLabelArray[0] = strconv.FormatInt(int64(candidate.GetAd().GetAdId()), 10)
		adLabelArray[1] = strconv.FormatInt(int64(adErrorCode.Code), 10)
		adLabelArray[2] = err_code.GetHumanReadableError(adErrorCode.Code)
		w.adMetrics.Inc(adLabelArray[:])

		//w.trafficSampler.SampleDsp(request, candidate)

		if !candidate.IsFiltered() {
			bidPrice := candidate.GetBidPrice()
			w.adBidPriceHistogram.Observe(candidate.GetAd().GetLabelCache_DspId_AdId(), float64(bidPrice.Price))

			chargePrice := candidate.GetChargePrice()
			w.adChargePriceHistogram.Observe(candidate.GetAd().GetLabelCache_DspId_AdId(), float64(chargePrice.Price))
		}
	}

	w.mediaRequestTrafficCounter.Add(mediaLabelArray[:1], float64(request.RawHttpRequest.RequestContentSize()))
	w.mediaResponseTrafficCounter.Add(mediaLabelArray[:1], float64(request.Response.GetHttpResponseWriter().ResponseContentSize()))

	w.doDataAggregate(request)
	return nil
}

func (w *StatisticCollector) doDataAggregate(request *ad_service.AdRequest) {
	dummyAggr := w.dataAggregatorManager.GetDataAggregator(data_aggregator.AggrNameDummy)
	if dummyAggr != nil {
		w.doDummyAggr(dummyAggr, request)
	}

	jdUserScore := w.dataAggregatorManager.GetDataAggregator(data_aggregator.AggrNameJztUserScore)
	if jdUserScore != nil {
		w.doJdUserScoreDataAggregate(jdUserScore, request)
	}

}

func (w *StatisticCollector) doDummyAggr(dummyAggr *data_aggregator.DataAggregator, request *ad_service.AdRequest) {
	for _, candidate := range request.Response.GetTotalAdCandidates() {
		key := fmt.Sprintf("%d", candidate.GetAd().GetAdId())
		item := dummyAggr.GetItem(key)
		item.IncrRequestCount(1)

		if candidate.GetDspResponseAd() != nil {
			item.IncrResponseCount(1)
			item.IncrResponsePrice(candidate.GetDspBidPrice().Price)
		}

		if candidate.GetErrorCode().Code == 0 {
			item.IncrBidCount(1)
			item.IncrBidPrice(candidate.GetBidPrice().Price)
		}
	}
}

func (w *StatisticCollector) doJdUserScoreDataAggregate(jdUserScore *data_aggregator.DataAggregator, request *ad_service.AdRequest) {
	for _, candidate := range request.Response.GetTotalAdCandidates() {
		if candidate.GetAd().GetDspInfo() == nil || candidate.GetAd().GetDspInfo().UserScoreType != entity.UserScoreTypeV1 {
			continue
		}

		if candidate.GetIsDspReadyToSend() {
			appBundle := strings.ToLower(request.App.AppBundle)
			if w.appBundleStatisticMapping != nil {
				appBundle = w.appBundleStatisticMapping.GetAppBundle(int32(request.GetMediaSlotId()), int32(request.GetSlotType()), appBundle)
			}

			userLevel := candidate.GetTrafficLevel()
			key := fmt.Sprintf("%d^%d^%d^%s^%d^%d^%d^%02d",
				request.GetSlotType(),
				request.GetMediaId(),
				request.GetMediaSlotId(),
				appBundle,
				candidate.GetAd().GetDspId(),
				candidate.GetAd().GetDspSlotId(),
				candidate.GetAd().AdId,
				userLevel)
			item := jdUserScore.GetItem(key)

			item.IncrRequestCount(1)

			//w.adTrafficLevelHistogram.Observe(
			//	[]string{candidate.GetAd().GetAdId().String()},
			//	float64(candidate.GetUserScore()/100))

			userScore := w.GetAdjustUserScore(candidate.GetUserScore() / 100)
			adScoreLabelArray := make([]string, 2)
			adScoreLabelArray[0] = strconv.FormatInt(int64(candidate.GetAd().GetAdId()), 10)
			adScoreLabelArray[1] = strconv.FormatInt(int64(userScore), 10)
			w.adScoreCounter.Inc(adScoreLabelArray[:])

			if candidate.GetIsDspScheduled() {
				item.IncrScheduleCount(1)
			}

			if candidate.GetIsDspRequested() {
				item.IncrBroadcastCount(1)

				//w.adTrafficLevelSentHistogram.Observe(
				//	[]string{candidate.GetAd().GetAdId().String()},
				//	float64(candidate.GetUserScore()/100))

				userScoreSent := w.GetAdjustUserScore(candidate.GetUserScore() / 100)
				adScoreSentLabelArray := make([]string, 2)
				adScoreSentLabelArray[0] = strconv.FormatInt(int64(candidate.GetAd().GetAdId()), 10)
				adScoreSentLabelArray[1] = strconv.FormatInt(int64(userScoreSent), 10)
				w.adScoreSentCounter.Inc(adScoreSentLabelArray[:])

				if candidate.GetDspBidPrice().Price != 0 {
					item.IncrResponseCount(1)
					item.IncrResponsePrice(candidate.GetDspBidPrice().Price)

					if candidate.GetErrorCode().Code == 0 {
						item.IncrBidCount(1)
						item.IncrBidPrice(candidate.GetBidPrice().Price)
						item.IncrBidDspPrice(candidate.GetDspBidPrice().Price)
					}
				}
			}
		}
	}
}

func (w *StatisticCollector) GetAdjustUserScore(score int32) int32 {
	if score < 25 {
		return score
	} else if score <= 40 {
		return (score / 5) * 5
	} else {
		return 41
	}
}
