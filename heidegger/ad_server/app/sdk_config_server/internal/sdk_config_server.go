package internal

import (
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/labstack/gommon/log"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/sdk_config_server/sdk_entity/sdk_entity_loader"
	"gitlab.com/dev/heidegger/ad_server/sdk_config_server/sdk_slot_config_service"
	"gitlab.com/dev/heidegger/library/entity_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/app_info_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/creative_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/media_loader"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/test_helper"
	"gitlab.com/dev/heidegger/master_server/master_server"
	"gopkg.in/natefinch/lumberjack.v2"
	"io"
	"strings"
	"xorm.io/xorm"
	"fmt"
)

type SdkConfigServer struct {
	env        Env
	dbEngine   *xorm.Engine
	echoServer *echo.Echo
	logFile    *lumberjack.Logger

	platformLoader           sdk_entity_loader.PlatformLoader
	templateLoader           creative_loader.CreativeTemplateLoader
	mediaLoader              media_loader.MediaLoader
	mediaSlotLoader          media_loader.MediaSlotLoader
	sdkSlotConfigLoader      sdk_entity_loader.SdkSlotConfigLoader
	SdkSlotConfigIndexLoader sdk_entity_loader.SdkSlotConfigIndexLoader
	ThirdSdkSlotLoader       sdk_entity_loader.ThirdSdkSlotLoader
	appInfoLoader            app_info_loader.AppInfoLoader
}

func NewSdkConfigServer(logFile *lumberjack.Logger) *SdkConfigServer {
	if logFile == nil {
		logFile = &lumberjack.Logger{
			Filename:   "log/app.log", // 日志文件路径
			MaxSize:    100,           // 每个日志文件的最大大小（MB）
			MaxBackups: 300,           // 保留的旧日志文件最大数量
			MaxAge:     14,            // 保留日志文件的最大天数
			Compress:   false,         // 是否压缩旧日志文件
		}
	}
	return &SdkConfigServer{
		env:     GetEnv(),
		logFile: logFile,
	}
}

func (s *SdkConfigServer) Start() error {
	if err := s.loadEnv(); err != nil {
		return err
	}

	if err := s.startBasic(); err != nil {
		return err
	}

	if err := s.startDataLoader(); err != nil {
		return err
	}

	if err := s.startSdkConfigService(); err != nil {
		return err
	}

	if err := s.startHttp(); err != nil {
		return err
	}

	return nil
}

func (s *SdkConfigServer) loadEnv() error {
	zap.L().Info("[SdkConfigServer] load env")

	client := master_server.NewKeyValueHttpClient("http://local.master_server.com:28000")
	if err := client.GetInto("config", &s.env); err != nil {
		return err
	}

	s.env.DumpJson()
	return nil
}

func (s *SdkConfigServer) startBasic() error {
	// logrus.SetLevel converted - configure zap logger instead

	s.echoServer = echo.New()
	ioWriter := io.MultiWriter(s.logFile)
	// 设置 Echo 的日志输出到 MultiWriter
	s.echoServer.// Logger.SetOutput converted - configure zap logger instead
	// 设置 Echo 的日志级别
	s.echoServer.// Logger.SetLevel converted - configure zap logger instead
	// 使用中间件记录请求日志
	s.echoServer.Use(middleware.LoggerWithConfig(middleware.LoggerConfig{
		Output: ioWriter,
	}))

	s.echoServer.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			if strings.Contains(c.Request().RequestURI, ";") {
				zap.L().Error("Url format error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", c.Request())))).RequestURI)
			}
			return next(c)
		}
	})

	zap.L().Info("[AdServer] database name", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", s.env.DatabaseName)))))
	var engine *xorm.Engine
	if len(s.env.DatabaseConn) != 0 {
		zap.L().Info("[AdServer] database conn", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", s.env.DatabaseConn)))))
		engine = test_helper.GetDatabaseWithEncryptedConn(s.env.DatabaseConn)
	} else if s.env.DatabaseName == "local" {
		engine = test_helper.GetTestXormEngine()
	} else {
		panic("invalid database name")
	}

	s.dbEngine = engine

	prometheus_helper.GetGlobalPrometheusManager().RegisterEcho(s.echoServer)
	return nil
}

func (s *SdkConfigServer) startDataLoader() error {
	reloadInterval := 60

	s.mediaLoader = media_loader.NewMysqlMediaLoader(s.dbEngine, reloadInterval)
	if err := s.mediaLoader.Start(); err != nil {
		return errors.Wrapf(err, "start media loader failed")
	}

	s.mediaSlotLoader = media_loader.NewMysqlMediaSlotLoader(s.dbEngine, reloadInterval)
	if err := s.mediaSlotLoader.Start(); err != nil {
		return errors.Wrapf(err, "start media slot loader failed")
	}

	//s.sdkSlotConfigLoader = sdk_entity_loader.NewDummySdkSlotConfigLoader()
	//s.SdkSlotConfigIndexLoader = sdk_entity_loader.NewDummySdkSlotConfigIndexLoader()

	s.sdkSlotConfigLoader = sdk_entity_loader.NewMysqlSdkSlotConfigLoader(s.dbEngine, reloadInterval)
	if err := s.sdkSlotConfigLoader.Start(); err != nil {
		return errors.Wrapf(err, "start slot config loader failed")
	}

	s.SdkSlotConfigIndexLoader = sdk_entity_loader.NewMysqlSdkSlotIndexLoader(s.dbEngine, reloadInterval)
	if err := s.SdkSlotConfigIndexLoader.Start(); err != nil {
		return errors.Wrapf(err, "start slot index loader failed")
	}

	s.ThirdSdkSlotLoader = sdk_entity_loader.NewMysqlThirdSdkSlotLoader(s.dbEngine, reloadInterval)
	if err := s.ThirdSdkSlotLoader.Start(); err != nil {
		return errors.Wrapf(err, "start third sdk slot loader failed")
	}

	s.templateLoader = creative_loader.NewMysqlCreativeTemplateLoader(s.dbEngine, reloadInterval)
	if err := s.templateLoader.Start(); err != nil {
		return errors.Wrapf(err, "start slot template loader failed")
	}

	s.platformLoader = sdk_entity_loader.NewMysqlPlatformLoader(s.dbEngine, reloadInterval)
	if err := s.platformLoader.Start(); err != nil {
		return errors.Wrapf(err, "start platform loader failed")
	}

	s.appInfoLoader = app_info_loader.NewMysqlAppInfoLoader(s.dbEngine, reloadInterval)
	if err := s.appInfoLoader.Start(); err != nil {
		return errors.Wrapf(err, "start app info loader failed")
	}

	entityLoaderHttpHandler := entity_loader.NewEntityLoaderHttpHandler()
	entityLoaderHttpHandler.SetMediaLoader(s.mediaLoader)
	entityLoaderHttpHandler.SetMediaSlotLoader(s.mediaSlotLoader)

	entityLoaderHttpHandler.RegisterEcho(s.echoServer)
	return nil
}

func (s *SdkConfigServer) startSdkConfigService() error {
	service := sdk_slot_config_service.NewSdkConfigService()
	service.SetMediaLoader(s.mediaLoader)
	service.SetMediaSlotLoader(s.mediaSlotLoader)
	service.SetGeoQueryFilePath(s.env.GeoInfoFilePath)
	service.SetSdkSlotConfigLoader(s.sdkSlotConfigLoader)
	service.SetSdkSlotConfigIndexLoader(s.SdkSlotConfigIndexLoader)
	service.SetThirdSdkSlotLoader(s.ThirdSdkSlotLoader)
	service.SetTemplateLoader(s.templateLoader)
	service.SetPlatformLoader(s.platformLoader)
	service.SetAppInfoLoader(s.appInfoLoader)

	if err := service.Start(); err != nil {
		return errors.Wrapf(err, "start sdk config service failed")
	}

	handler := sdk_slot_config_service.NewSdkConfigHttpHandler(service)
	handler.RegisterEcho(s.echoServer)

	return nil
}

func (s *SdkConfigServer) startHttp() error {
	return s.echoServer.Start(s.env.HttpAddress)
}
