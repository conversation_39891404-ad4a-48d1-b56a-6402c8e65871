package internal

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"os"
)

type Env struct {
	IsDebug bool `json:"-"`

	HttpAddress string `json:"sdk_config_server.http_address"`

	DatabaseName string `json:"engine.database_name"`
	DatabaseConn string `json:"engine.database_conn"`

	GeoInfoFilePath string       `json:"ad_server.geo_info_file_path"`
	LogLevel        zapcore.Level `json:"ad_server.log_level"`
}

func (env *Env) DumpJson() {
	result, _ := json.MarshalIndent(env, "", "  ")
	fmt.Println(string(result))
}

func GetEnv() Env {
	host, _ := os.Hostname()

	zap.L().Info("[GetEnv] Hostname", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", host)))))

	if host == "om-web-01" {
		return GetReleaseEnv()
	}

	return GetLocalEnv()
}

func GetLocalEnv() Env {
	return Env{
		IsDebug:         true,
		LogLevel:        zapcore.DebugLevel,
		HttpAddress:     "127.0.0.1:8080",
		DatabaseName:    "local",
		GeoInfoFilePath: "./ad_server/app/ad_server/data/geo_info.txt",
	}
}

func GetReleaseEnv() Env {
	return Env{
		LogLevel:        zapcore.InfoLevel,
		DatabaseName:    "om_release",
		HttpAddress:     "0.0.0.0:58088",
		GeoInfoFilePath: "./data/geo_info.txt",
	}
}
