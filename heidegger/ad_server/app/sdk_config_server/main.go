package main

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/app/sdk_config_server/internal"
	"gopkg.in/natefinch/lumberjack.v2"
	"io"
	"runtime/debug"
)

func main() {
	// 配置 lumberjack
	logFile := &lumberjack.Logger{
		Filename:   "log/app.log", // 日志文件路径
		MaxSize:    100,           // 每个日志文件的最大大小（MB）
		MaxBackups: 300,           // 保留的旧日志文件最大数量
		MaxAge:     14,            // 保留日志文件的最大天数
		Compress:   false,         // 是否压缩旧日志文件
	}
	ioWriter := io.MultiWriter(logFile)
	// logrus日志输出到logFile
	// logrus.SetOutput converted - configure zap logger instead
	// 打印日志行号
	// logrus.SetReportCaller converted - configure zap logger instead
	// 设置日志级别
	// logrus.SetLevel converted - configure zap logger instead
	defer func() {
		if r := recover(); r != nil {
			stack := string(debug.Stack())
			zap.L().Fatal("Recovered from panic")
		}
	}()

	server := internal.NewSdkConfigServer(logFile)

	if err := server.Start(); err != nil {
		panic(err)
	}
}
