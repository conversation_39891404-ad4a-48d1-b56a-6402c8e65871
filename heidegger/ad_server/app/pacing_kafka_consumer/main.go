package main

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/pacing_service/pacing_controller"
	"fmt"
)

func main() {
	brokerList := []string{"192.168.10.9:9092", "192.168.10.10:9092", "192.168.10.11:9092"}
	topicList := []string{"data_banker_aggr_api"}

	consumer := pacing_controller.NewKafkaRealtimeFeedbackConsumer(brokerList, "3.1.0.0", topicList)
	if err := consumer.Start(); err != nil {
		panic(err)
	}

	for {
		select {
		case feedback := <-consumer.Feedback():
			zap.L().Info("feedback", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", feedback)))))
		}
	}
}
