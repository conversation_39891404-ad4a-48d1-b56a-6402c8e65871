package main

import (
	"fmt"
	"gitlab.com/dev/heidegger/ad_server/app/ad_server/internal"
	"gitlab.com/dev/heidegger/library/entity"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"os"
	//_ "net/http/pprof"
)

func main() {
	version := entity.GetVersion()
	if len(os.Args) > 1 && (os.Args[1] == "--version" || os.Args[1] == "-v") {
		fmt.Println(fmt.Sprintf("Version:    %s\n"+
			"Build Time: %s\n"+
			"Git Commit: %s\n"+
			"Go version: %s\n"+
			"Os/Arch:    %s/%s", version.Version, version.BuildTime, version.Commit,
			version.GoVersion, version.Os, version.Arch))
		return
	}

	// zap log
	zapLog := initZapLog(zapcore.DebugLevel)
	defer zapLog.Sync()

	//go func() {
	//	if err := http.ListenAndServe("0.0.0.0:38000", nil); err != nil {
	//		panic(err)
	//	}
	//}()

	adServer := internal.NewAdServer()

	if err := adServer.Start(); err != nil {
		panic(err)
	}
}

func initZapLog(level zapcore.Level) *zap.Logger {
	zapCfg := zap.NewProductionEncoderConfig()
	zapCfg.EncodeTime = zapcore.ISO8601TimeEncoder
	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(zapCfg),
		zapcore.AddSync(os.Stdout), // 只写入控制台
		//zapcore.NewMultiWriteSyncer(writer, zapcore.AddSync(os.Stdout)), // 同时写入文件和控制台
		level)
	logger := zap.New(core, zap.AddStacktrace(zap.PanicLevel))
	//defer logger.Sync()
	_ = zap.ReplaceGlobals(logger)
	return logger
}
