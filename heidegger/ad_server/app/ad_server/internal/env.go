package internal

import (
	"encoding/json"
	"fmt"
	"os"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker"
)

type RapidOption struct {
	Address []string
	Timeout int
}

type RedisConfig struct {
	Address  string
	Database int
}

type Env struct {
	IsDebug bool `json:"-"`

	FrequencyClient       RapidOption `json:"engine.frequency_client"`
	DeviceAllocatorClient RapidOption `json:"engine.device_allocator_client"`
	PacingRedis           RedisConfig `json:"engine.pacing_redis"`
	AttributionRedis      RedisConfig `json:"engine.attribution_redis"`
	SamplerRedis          RedisConfig `json:"engine.sampler_redis"`
	ShortUrlRedis         RedisConfig `json:"engine.short_url_redis"`
	UserSegmentRedis      []string    `json:"engine.user_segment_redis"`
	DatabaseName          string      `json:"engine.database_name"`
	DatabaseConn          string      `json:"engine.database_conn"`
	MonitorDatabaseConn   string      `json:"engine.monitor_database_conn"`

	SampleRate            int                            `json:"ad_server.sample_rate"`
	VendorParamName       string                         `json:"ad_server.vendor_param_name"`
	BesAdvId              string                         `json:"ad_server.bes_adv_id"`
	BesSlotId             int                            `json:"ad_server.bes_slot_id"`
	KafkaBrokerList       []string                       `json:"ad_server.kafka_broker_list"`
	LogKafkaBrokerList    []string                       `json:"ad_server.log_kafka_broker_list"`
	KafkaVersion          string                         `json:"ad_server.kafka_version"`
	KafkaProducerNum      int                            `json:"ad_server.kafka_producer_num"`
	HttpAddress           string                         `json:"ad_server.http_address"`
	TrackingHttpAddress   string                         `json:"ad_server.tracking_http_address"`
	FastHttpAddress       string                         `json:"ad_server.fast_http_address"`
	ImpressionMonitor     []string                       `json:"ad_server.impression_monitor"`
	ClickMonitor          []string                       `json:"ad_server.click_monitor"`
	ActionCallbackUrl     string                         `json:"ad_server.action_callback_url"`
	ActionCallbackUrlMap  map[string]string              `json:"ad_server.action_callback_url_map"`
	RedirectMonitorUrl    string                         `json:"ad_server.redirect_monitor_url"`
	LoggingPath           string                         `json:"ad_server.logging_path"`
	LoggingRotateInterval int                            `json:"ad_server.logging_rotate_interval"`
	GeoInfoFilePath       string                         `json:"ad_server.geo_info_file_path"`
	GeoInfoV6FilePath     string                         `json:"ad_server.geo_info_v6_file_path"`
	UaFilePath            string                         `json:"ad_server.ua_file_path"`
	PacingServiceAddress  string                         `json:"ad_server.pacing_service_address"`
	LogLevel              zapcore.Level                   `json:"ad_server.log_level"`
	RankingModels         map[string]ranker.RankerConfig `json:"ad_server.ranking_models"`
	RankingLibrary        map[string]ranker.RankerConfig `json:"ad_server.ranking_library"`
}

func (env *Env) DumpJson() {
	result, _ := json.MarshalIndent(env, "", "  ")
	fmt.Println(string(result))
}

func GetEnv() Env {
	host, _ := os.Hostname()

	zap.L().Info("[GetEnv] Hostname", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", host)))))

	return GetLocalEnv()
}

func GetLocalEnv() Env {
	return Env{
		LogLevel:     zapcore.DebugLevel,
		DatabaseName: "local",
		PacingRedis: RedisConfig{
			Address:  "localhost:6379",
			Database: 8,
		},
		AttributionRedis: RedisConfig{
			Address:  "localhost:6379",
			Database: 9,
		},
		FrequencyClient: RapidOption{
			Address: []string{"**************:58001"},
			Timeout: 100,
		},
		DeviceAllocatorClient: RapidOption{
			Address: []string{"**************:58101"},
			Timeout: 100,
		},
		SamplerRedis: RedisConfig{
			Address:  "localhost:6379",
			Database: 10,
		},
		HttpAddress:     "0.0.0.0:8080",
		FastHttpAddress: "0.0.0.0:8081",
		ImpressionMonitor: []string{
			"http://127.0.0.1:8080/tracking/i?p=__SYSWPRICE__&d_=__SYSEXT__",
		},
		ClickMonitor: []string{
			"http://127.0.0.1:8080/tracking/c?d_=__SYSEXT__",
		},
		ActionCallbackUrl:     "http://127.0.0.1:8080/tracking/ocpx?d_=__SYSEXT__",
		RedirectMonitorUrl:    "http://127.0.0.1:8080/tracking/r?d_=__SYSEXT__&rurl=__DEST__",
		LoggingPath:           "./ad_server/app/ad_server/logs",
		LoggingRotateInterval: 600,
		GeoInfoFilePath:       "./ad_server/app/ad_server/data/geo_info.txt",
		GeoInfoV6FilePath:     "./ad_server/app/ad_server/data/geo_info_v6.txt",
		PacingServiceAddress:  "http://**************:58810",
	}
}
