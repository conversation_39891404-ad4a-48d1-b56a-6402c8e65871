package internal

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"os"
)

type RapidOption struct {
	Address []string
	Timeout int
}

type RedisConfig struct {
	Address  string
	Database int
}

type Env struct {
	PacingRedis RedisConfig `json:"engine.pacing_redis"`

	DatabaseName string `json:"engine.database_name"`
	DatabaseConn string `json:"engine.database_conn"`

	PacingKafkaBrokerList    []string     `json:"pacing_builder.pacing_kafka_broker_list"`
	PacingKafkaBrokerVersion string       `json:"pacing_builder.pacing_kafka_broker_version"`
	PacingKafkaTopicList     []string     `json:"pacing_builder.pacing_kafka_topic_list"`
	HttpAddress              string       `json:"pacing_builder.http_address"`
	LogLevel                 zapcore.Level `json:"pacing_builder.log_level"`
	CheckRealtimeLatency     bool         `json:"pacing_builder.check_realtime_latency"`
}

func GetEnv() Env {
	host, _ := os.Hostname()

	zap.L().Info("[GetEnv] Hostname", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", host)))))

	return GetLocalEnv()
}

func (env *Env) DumpJson() {
	result, _ := json.MarshalIndent(env, "", "  ")
	fmt.Println(string(result))
}

func GetLocalEnv() Env {
	return Env{
		LogLevel:     zapcore.DebugLevel,
		DatabaseName: "local",
		DatabaseConn: "",
		PacingRedis: RedisConfig{
			Address:  "localhost:6379",
			Database: 8,
		},
		HttpAddress:          "0.0.0.0:58810",
		CheckRealtimeLatency: false,
	}
}
