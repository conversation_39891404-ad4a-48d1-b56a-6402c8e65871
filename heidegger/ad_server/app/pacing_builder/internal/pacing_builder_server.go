package internal

import (
	"fmt"
	"github.com/go-redis/redis"
	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/pacing_service/pacing_controller"
	"gitlab.com/dev/heidegger/library/entity_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/ad_loader"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/test_helper"
	"gitlab.com/dev/heidegger/master_server/master_server"
	"strings"
	"time"
	"xorm.io/xorm"
)

type PacingBuilder struct {
	env               Env
	dbEngine          *xorm.Engine
	pacingRedisClient *redis.Client
	echoServer        *echo.Echo

	adLoader      ad_loader.AdLoader
	adGroupLoader ad_loader.AdGroupLoader
	adIndexLoader ad_loader.AdIndexLoader

	adPacingControllerBuilder      pacing_controller.PacingController
	adGroupPacingControllerBuilder pacing_controller.PacingController

	realtimeFeedbackMetrics *prometheus_helper.SimpleHistogram
	realtimeFeedbackProcess *prometheus_helper.SimpleHistogram
}

func NewPacingBuilder() *PacingBuilder {
	return &PacingBuilder{
		env: GetEnv(),

		realtimeFeedbackMetrics: prometheus_helper.RegisterSimpleHistogram("pacing_realtime_feedback"),
		realtimeFeedbackProcess: prometheus_helper.RegisterSimpleHistogram("pacing_realtime_process"),
	}
}

func (s *PacingBuilder) Start() error {
	if err := s.loadEnv(); err != nil {
		return err
	}

	if err := s.startBasic(); err != nil {
		return err
	}

	if err := s.startDataLoader(); err != nil {
		return err
	}

	if err := s.startPacingBuilder(); err != nil {
		return err
	}

	if err := s.startPacingKafkaCustomer(); err != nil {
		return nil
	}

	if err := s.startPacingControllerSelfCheck(); err != nil {
		return nil
	}

	if err := s.startHttp(); err != nil {
		return err
	}

	return nil
}

func (s *PacingBuilder) loadEnv() error {
	client := master_server.NewKeyValueHttpClient("http://local.master_server.com:28000")
	if err := client.GetInto("config", &s.env); err != nil {
		return err
	}

	s.env.DumpJson()
	return nil
}

func (s *PacingBuilder) startBasic() error {
	// logrus.SetLevel converted - configure zap logger instead

	s.echoServer = echo.New()

	s.echoServer.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			if strings.Contains(c.Request().RequestURI, ";") {
				zap.L().Error("Url format error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", c.Request())))).RequestURI)
			}
			return next(c)
		}
	})

	zap.L().Info("[AdServer] database name", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", s.env.DatabaseName)))))
	var engine *xorm.Engine
	if len(s.env.DatabaseConn) != 0 {
		zap.L().Info("[AdServer] database conn", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", s.env.DatabaseConn)))))
		engine = test_helper.GetDatabaseWithEncryptedConn(s.env.DatabaseConn)
	} else if s.env.DatabaseName == "local" {
		engine = test_helper.GetTestXormEngine()
	} else {
		panic("invalid database name")
	}

	s.dbEngine = engine

	pacingRedisClient := redis.NewClient(&redis.Options{
		Addr: s.env.PacingRedis.Address,
		DB:   s.env.PacingRedis.Database,
	})
	s.pacingRedisClient = pacingRedisClient

	prometheus_helper.GetGlobalPrometheusManager().RegisterEcho(s.echoServer)

	return nil
}

func (s *PacingBuilder) startDataLoader() error {
	reloadInterval := 60

	s.adIndexLoader = ad_loader.NewMysqlAdIndexLoader(s.dbEngine, reloadInterval)
	if err := s.adIndexLoader.Start(); err != nil {
		return errors.Wrapf(err, "start ad index loader failed")
	}

	s.adGroupLoader = ad_loader.NewMysqlAdGroupLoader(s.dbEngine, reloadInterval)
	if err := s.adGroupLoader.Start(); err != nil {
		return errors.Wrapf(err, "start ad group loader failed")
	}

	s.adLoader = ad_loader.NewMysqlAdLoader(s.dbEngine, reloadInterval).
		WithAdGroupLoader(s.adGroupLoader).
		WithAdIndexLoader(s.adIndexLoader)

	if err := s.adLoader.Start(); err != nil {
		return errors.Wrapf(err, "start ad loader failed")
	}

	entityLoaderHttpHandler := entity_loader.NewEntityLoaderHttpHandler()
	entityLoaderHttpHandler.SetAdLoader(s.adLoader)
	entityLoaderHttpHandler.SetAdGroupLoader(s.adGroupLoader)
	entityLoaderHttpHandler.SetAdIndexLoader(s.adIndexLoader)
	entityLoaderHttpHandler.RegisterEcho(s.echoServer)

	return nil
}

func (s *PacingBuilder) startPacingBuilder() error {
	checkRealtimeLatency := s.env.CheckRealtimeLatency

	adPacingControllerBuilder := pacing_controller.NewPacingControllerBuilder("ad", 60, 5)
	adPacingControllerBuilder.SetRedisClient(s.pacingRedisClient)
	adPacingControllerBuilder.SetCheckRealtimeLatency(checkRealtimeLatency)
	adPacingControllerBuilder.SetPacingTargetLoader(func() []pacing_controller.PacingTarget {
		currentTime := time.Now()
		result := make([]pacing_controller.PacingTarget, 0)
		for _, ad := range s.adLoader.GetAdList() {
			result = append(result, pacing_controller.PacingTarget{
				Id:                  ad.AdId,
				ImpressionLimit:     ad.GetImpressionLimit(currentTime),
				ImpressionLimitCalc: ad.GetImpressionLimitWithCurrentTotal,
				MinutesRemain:       int32(ad.GetDeliveryMinuteRemain(currentTime)),
			})
		}
		return result
	})
	if err := adPacingControllerBuilder.Start(); err != nil {
		return err
	}

	adPacingControllerHttpHandler := pacing_controller.NewPacingControllerHttpHandler(adPacingControllerBuilder)
	adPacingControllerHttpHandler.RegisterEcho(s.echoServer)

	adGroupPacingControllerBuilder := pacing_controller.NewPacingControllerBuilder("adgroup", 60, 5)
	adGroupPacingControllerBuilder.SetRedisClient(s.pacingRedisClient)
	adGroupPacingControllerBuilder.SetCheckRealtimeLatency(checkRealtimeLatency)
	adGroupPacingControllerBuilder.SetPacingTargetLoader(func() []pacing_controller.PacingTarget {
		currentTime := time.Now()
		result := make([]pacing_controller.PacingTarget, 0)
		for _, adGroup := range s.adGroupLoader.GetAdGroupList() {
			result = append(result, pacing_controller.PacingTarget{
				Id:              adGroup.AdGroupId,
				ImpressionLimit: adGroup.GetImpressionLimit(currentTime),
				MinutesRemain:   int32(adGroup.GetDeliveryMinuteRemain(currentTime)),
			})
		}
		return result
	})

	if err := adGroupPacingControllerBuilder.Start(); err != nil {
		return err
	}

	adGroupPacingControllerHttpHandler := pacing_controller.NewPacingControllerHttpHandler(adGroupPacingControllerBuilder)
	adGroupPacingControllerHttpHandler.RegisterEcho(s.echoServer)

	s.adPacingControllerBuilder = adPacingControllerBuilder
	s.adGroupPacingControllerBuilder = adGroupPacingControllerBuilder

	return nil
}

func (s *PacingBuilder) startPacingKafkaCustomer() error {
	brokerList := s.env.PacingKafkaBrokerList
	topicList := s.env.PacingKafkaTopicList
	brokerVersion := s.env.PacingKafkaBrokerVersion

	if len(brokerList) == 0 || len(topicList) == 0 {
		zap.L().Error("[PacingBuilder][startPacingKafkaCustomer] invalid kafka config, start without kafka")
		return nil
	}

	consumer := pacing_controller.NewKafkaRealtimeFeedbackConsumer(brokerList, brokerVersion, topicList)
	if err := consumer.Start(); err != nil {
		panic(err)
	}

	for i := 0; i != 4; i++ {
		s.startPacingKafkaCustomerLoop(consumer)
	}

	return nil
}

func (s *PacingBuilder) startPacingKafkaCustomerLoop(consumer *pacing_controller.KafkaRealtimeFeedbackConsumer) {
	go func() {
		zap.L().Info("[PacingBuilder] start kafka consumer")
		for {
			select {
			case feedback := <-consumer.Feedback():
				s.processFeedback(feedback)
			}
		}
	}()
}

func (s *PacingBuilder) processFeedback(feedback pacing_controller.RealtimeFeedbackData) {
	timeStart := time.Now()

	s.realtimeFeedbackMetrics.Observe(time.Since(feedback.DataTime).Minutes())
	defer s.realtimeFeedbackProcess.FinishTime(time.Now())

	zap.L().Info("[PacingBuilder] process feedback start")

	key := fmt.Sprintf("%s_impression_daily_realtime_%s_%s", feedback.Dimension, feedback.DataTime.Format("20060102"), feedback.Key)
	if err := s.pacingRedisClient.IncrBy(key, int64(feedback.DistinctImpression)).Err(); err != nil {
		zap.L().Error("incr redis error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
	}

	if err := s.pacingRedisClient.Expire(key, 24*time.Hour).Err(); err != nil {
		zap.L().Error("expire redis error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
	}

	if err := s.pacingRedisClient.Set("pacing_realtime_timestamp", feedback.DataTime.Unix(), 0).Err(); err != nil {
		zap.L().Error("set redis error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
	}

	zap.L().Info("[PacingBuilder] process feedback:%+v, time", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", feedback)))), zap.String("param2", fmt.Sprintf("%v", time.Since(timeStart))))
}

func (s *PacingBuilder) startPacingControllerSelfCheck() error {
	selfCheck := pacing_controller.NewPacingControllerSelfCheck(s.adPacingControllerBuilder, s.adLoader)
	if err := selfCheck.Start(); err != nil {
		return err
	}

	selfCheck.RegisterEcho(s.echoServer)
	return nil
}

func (s *PacingBuilder) startHttp() error {
	return s.echoServer.Start(s.env.HttpAddress)
}
