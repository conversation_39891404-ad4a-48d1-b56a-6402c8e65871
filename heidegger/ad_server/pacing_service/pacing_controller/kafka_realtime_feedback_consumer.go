package pacing_controller

import (
	"encoding/json"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/kafka_producer"
	"time"
	"fmt"
)

type KafkaRealtimeFeedbackConsumer struct {
	kafkaConsumer *kafka_producer.KafkaConsumer
	feedbackChan  chan RealtimeFeedbackData

	latestDataTime time.Time

	term chan struct{}
}

func NewKafkaRealtimeFeedbackConsumer(brokerList []string, brokerVersion string, topics []string) *KafkaRealtimeFeedbackConsumer {
	return &KafkaRealtimeFeedbackConsumer{
		kafkaConsumer: kafka_producer.NewKafkaConsumer(brokerList, brokerVersion, "pacing_service", topics),
		feedbackChan:  make(chan RealtimeFeedbackData, 1024),
		term:          make(chan struct{}),
	}
}

func (c *KafkaRealtimeFeedbackConsumer) Start() error {
	if err := c.kafkaConsumer.Start(); err != nil {
		return err
	}

	go c.consumeLoop()
	return nil
}

func (c *KafkaRealtimeFeedbackConsumer) Stop() {
	c.kafkaConsumer.Stop()
}

func (c *KafkaRealtimeFeedbackConsumer) Feedback() <-chan RealtimeFeedbackData {
	return c.feedbackChan
}

func (c *KafkaRealtimeFeedbackConsumer) IsHealthy() bool {
	return time.Since(c.latestDataTime) < 10*time.Minute
}

func (c *KafkaRealtimeFeedbackConsumer) consumeLoop() {
	zap.L().Info("[KafkaRealtimeFeedbackConsumer] start consume loop")
	for {
		select {
		case msg := <-c.kafkaConsumer.Message():
			if msg == nil {
				continue
			}

			//zap.L().Info("[KafkaRealtimeFeedbackConsumer] consume message", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(msg.Value))))))

			data := KafkaRealtimeFeedbackData{}
			if err := json.Unmarshal(msg.Value, &data); err != nil {
				zap.L().Error("unmarshal kafka realtime feedback data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
				continue
			}

			for timeStr, dimMap := range data {
				timeValue, err := time.ParseInLocation("200601021504", timeStr, time.Local)
				if err != nil {
					zap.L().Error("parse time string error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
					continue
				}

				if timeValue.After(c.latestDataTime) {
					c.latestDataTime = timeValue
				}

				for dim, keyMap := range dimMap {
					for key, item := range keyMap {
						select {
						case c.feedbackChan <- RealtimeFeedbackData{
							DataTime:           timeValue,
							Dimension:          dim,
							Key:                key,
							RawImpression:      item.TotalImp,
							DistinctImpression: item.Imp,
						}:
						default:
							zap.L().Warn("feedback channel is full, drop feedback data")
						}
					}
				}
			}

		case <-c.term:
			return
		}
	}
}

type KafkaRealtimeFeedbackDataItem struct {
	TotalImp int32 `json:"total_imps"`
	Imp      int32 `json:"imps"`
}

type KafkaRealtimeFeedbackData map[string] /* time */ map[string] /* dim */ map[string] /* key */ KafkaRealtimeFeedbackDataItem
