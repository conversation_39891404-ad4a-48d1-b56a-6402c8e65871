package pacing_controller

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/alter_sender"
	"gitlab.com/dev/heidegger/library/echo_helper"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/ad_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"sort"
	"strings"
	"sync"
	"time"
)

type PacingControllerSelfCheckMarkerList []*PacingControllerSelfCheckMarker

func (list PacingControllerSelfCheckMarkerList) GetTopNIdString(topN int) string {
	n := topN
	if len(list) == 0 {
		return ""
	}

	if len(list) < n {
		n = len(list)
	}

	idList := make([]string, 0)
	for i := 0; i < n; i++ {
		idList = append(idList, fmt.Sprintf("%d", list[i].Id))
	}

	if topN <= len(list) {
		return strings.Join(idList, ",")
	} else {
		return strings.Join(idList, ",") + "..."
	}
}

type PacingControllerSelfCheckMarker struct {
	DataKey          string    `json:"data_key"`
	Id               utils.ID  `json:"id"`
	LastMarkTime     time.Time `json:"last_mark_time"`
	LastImpression   int64     `json:"last_impression"`
	LastCheckDelta   int64     `json:"last_check_delta"`
	ExceedImpression int64     `json:"exceed_impression"`
	MarkCount        int       `json:"mark_count"`
}

type PacingControllerSelfCheck struct {
	adController PacingController
	adLoader     ad_loader.AdLoader
	adMarker     map[utils.ID]*PacingControllerSelfCheckMarker
	adMarkerLock sync.Mutex

	term chan struct{}
}

func NewPacingControllerSelfCheck(
	adController PacingController,
	adLoader ad_loader.AdLoader) *PacingControllerSelfCheck {
	return &PacingControllerSelfCheck{
		adController: adController,
		adLoader:     adLoader,
		adMarker:     make(map[utils.ID]*PacingControllerSelfCheckMarker),
		term:         make(chan struct{}),
	}
}

func (c *PacingControllerSelfCheck) Start() error {
	go c.loop()
	return nil
}

func (c *PacingControllerSelfCheck) Stop() {
	close(c.term)
}

func (c *PacingControllerSelfCheck) loop() {
	ticker := time.NewTicker(60 * time.Second)
	for {
		select {
		case <-c.term:
			return
		case <-ticker.C:
			if err := c.CheckAdDeliveryLimit(); err != nil {
				zap.L().Error("[PacingControllerSelfCheck] CheckAdDeliveryLimit fail, err", zap.Error(err))
				continue
			}
			c.ReportAdMarker()
		}
	}
}

func (c *PacingControllerSelfCheck) CheckAdDeliveryLimit() error {
	c.adMarkerLock.Lock()
	defer c.adMarkerLock.Unlock()

	pacingDataMap, err := c.adController.GetAllPacingData()
	if err != nil {
		return err
	}

	for id, pacingData := range pacingDataMap {
		ad := c.adLoader.GetAdById(id)
		if ad == nil {
			continue
		}

		if ad.GetPacingType() == entity.PacingTypeUnlimited {
			continue
		}

		impressionLimit := ad.ImpressionLimit
		impressionLimitThreshold := int64(float64(impressionLimit) * 1.05)
		impressionToday := int64(float64(pacingData.ImpressionToday) * 1.0)
		impressionExceed := impressionToday - impressionLimit
		if impressionExceed < 0 {
			impressionExceed = 0
		}

		if impressionLimitThreshold < impressionToday {
			c.MarkAd(ad, impressionToday, impressionExceed, pacingData)
		}
	}

	return nil
}

func (c *PacingControllerSelfCheck) MarkAd(ad *entity.Ad, impression int64, exceedImpression int64, pacingData *PacingData) {
	marker := c.loadAdMark(ad)

	marker.LastCheckDelta = impression - marker.LastImpression
	marker.LastImpression = impression
	marker.ExceedImpression = exceedImpression

	if marker.LastCheckDelta > 10 {
		marker.MarkCount++
		marker.LastMarkTime = time.Now()
	}
}

func (c *PacingControllerSelfCheck) loadAdMark(ad *entity.Ad) *PacingControllerSelfCheckMarker {
	marker := c.adMarker[ad.GetAdId()]
	if marker == nil || time.Since(marker.LastMarkTime).Seconds() > 300 {
		marker = &PacingControllerSelfCheckMarker{
			DataKey:        c.adController.GetDataKey(),
			Id:             ad.GetAdId(),
			LastMarkTime:   time.Now(),
			LastImpression: 0,
			LastCheckDelta: 0,
			MarkCount:      0,
		}
		c.adMarker[ad.GetAdId()] = marker
	}
	return marker
}

func (c *PacingControllerSelfCheck) ReportAdMarker() {
	c.adMarkerLock.Lock()
	exceedReportList := make(PacingControllerSelfCheckMarkerList, 0)
	for _, marker := range c.adMarker {
		if marker.MarkCount > 2 {
			if marker.ExceedImpression > 0 {
				zap.L().Info("[PacingControllerSelfCheck][ReportAdMarker], data_key:, ad_id:, mark_count:, last_check_delta:, exceed_impression", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", marker.DataKey)))), zap.String("param2", fmt.Sprintf("%v", marker.Id)), zap.String("param3", fmt.Sprintf("%v", marker.MarkCount)), zap.String("param4", fmt.Sprintf("%v", marker.LastCheckDelta)), zap.String("param5", fmt.Sprintf("%v", marker.ExceedImpression)))
				exceedReportList = append(exceedReportList, marker)
				delete(c.adMarker, marker.Id)
			}
		}
	}
	c.adMarkerLock.Unlock()

	if len(exceedReportList) != 0 {
		sort.Slice(exceedReportList, func(i, j int) bool {
			return exceedReportList[i].ExceedImpression > exceedReportList[j].ExceedImpression
		})

		message := fmt.Sprintf("ad delivery limit exceed and still running, total:%d, top 5 ad_id:%v, max_exceed_impression:%v",
			len(exceedReportList),
			exceedReportList.GetTopNIdString(5),
			exceedReportList[0].ExceedImpression)

		alter_sender.SendAlert(alter_sender.AlertData{
			alter_sender.LevelError,
			"PacingControllerSelfCheck",
			"",
			"PacingControllerSelfCheck Fail",
			message,
			"dev",
		})

		zap.L().Info("[PacingControllerSelfCheck][ReportAdMarker]", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", message)))))
	}
}

func (c *PacingControllerSelfCheck) GetAdMarkerMap() map[utils.ID]*PacingControllerSelfCheckMarker {
	c.adMarkerLock.Lock()
	defer c.adMarkerLock.Unlock()

	result := make(map[utils.ID]*PacingControllerSelfCheckMarker)
	for k, v := range c.adMarker {
		result[k] = v
	}

	return result
}

func (c *PacingControllerSelfCheck) RegisterEcho(e *echo.Echo) {
	e.GET("/self_check/ad_marker", c.HandleHttpGetAdMarkerMap)
}

func (c *PacingControllerSelfCheck) HandleHttpGetAdMarkerMap(ctx echo.Context) error {
	data := c.GetAdMarkerMap()
	return echo_helper.Response(ctx, data)
}
