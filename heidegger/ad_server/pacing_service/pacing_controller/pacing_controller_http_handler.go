package pacing_controller

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/echo_helper"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/slice_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"sort"
	"strings"
)

type PacingControllerHttpHandler struct {
	controller PacingController
}

func NewPacingControllerHttpHandler(
	controller PacingController) *PacingControllerHttpHandler {
	return &PacingControllerHttpHandler{
		controller: controller,
	}
}

func (handler *PacingControllerHttpHandler) RegisterEcho(e *echo.Echo) {
	g := e.Group(fmt.Sprintf("/pacing/%s", handler.controller.GetDataKey()))
	g.GET("/get_by_id", handler.GetPacingRateById)
	g.GET("/get", handler.GetPacingRate)
	g.GET("/all", handler.GetAllPacingData)
	g.GET("/default", handler.GetAllPacingData)
	g.GET("/data_key", handler.GetDataKey)
	g.GET("/register_delivery_service", handler.RegisterDeliveryService)
	g.GET("/get_delivery_service_list", handler.GetDeliveryServiceList)
	g.GET("/search", handler.GetSearchPacingData)
}

func (handler *PacingControllerHttpHandler) GetPacingRateById(ctx echo.Context) error {
	id := type_convert.GetAssertInt32(ctx.QueryParam("id"))
	zap.L().Info("[PacingControllerHttpHandler][GetPacingRateById], id", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(id)))))

	data, err := handler.controller.GetPacingRateById(utils.ID(id))
	if err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	return echo_helper.Response(ctx, data)
}

func (handler *PacingControllerHttpHandler) GetPacingRate(ctx echo.Context) error {
	idListStr := ctx.QueryParam("ids")
	zap.L().Info("[PacingControllerHttpHandler][GetPacingRate], id_list", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", idListStr)))))

	idList := slice_utils.StringToIdSlice(strings.Split(idListStr, ","))

	data, err := handler.controller.GetPacingRate(idList)
	if err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	return echo_helper.Response(ctx, data)
}

func (handler *PacingControllerHttpHandler) GetAllPacingData(ctx echo.Context) error {
	zap.L().Info("[PacingControllerHttpHandler][GetAllPacingData]")

	data, err := handler.controller.GetAllPacingData()
	if err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	return echo_helper.Response(ctx, data)
}

func (handler *PacingControllerHttpHandler) GetDefaultPacingData(ctx echo.Context) error {
	zap.L().Info("[PacingControllerHttpHandler][GetDefaultPacingData]")

	data := handler.controller.GetDefaultPacingData()
	return echo_helper.Response(ctx, data)
}

func (handler *PacingControllerHttpHandler) GetDataKey(ctx echo.Context) error {
	zap.L().Info("[PacingControllerHttpHandler][GetDataKey]")

	data := handler.controller.GetDataKey()
	return echo_helper.Response(ctx, data)
}

func (handler *PacingControllerHttpHandler) RegisterDeliveryService(ctx echo.Context) error {
	zap.L().Info("[PacingControllerHttpHandler][RegisterDeliveryService]")

	name := ctx.QueryParam("name")
	handler.controller.RegisterDeliveryService(name)
	return echo_helper.Response(ctx, "success")
}

func (handler *PacingControllerHttpHandler) GetDeliveryServiceList(ctx echo.Context) error {
	zap.L().Info("[PacingControllerHttpHandler][GetDeliveryServiceList]")

	data := handler.controller.GetDeliveryServiceList()
	return echo_helper.Response(ctx, data)
}

func (handler *PacingControllerHttpHandler) GetSearchPacingData(ctx echo.Context) error {
	zap.L().Info("[PacingControllerHttpHandler][GetSearchPacingData]")

	sortBy := ctx.QueryParam("sort_by")

	data, err := handler.controller.GetAllPacingData()
	if err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	dataList := make([]*PacingData, 0, len(data))
	for _, v := range data {
		dataList = append(dataList, v)
	}

	if sortBy == "impression_complete_rate_desc" {
		sort.Slice(dataList, func(i, j int) bool {
			return dataList[i].ImpressionCompleteRate > dataList[j].ImpressionCompleteRate
		})
	}

	return echo_helper.Response(ctx, dataList)
}

type PacingControllerHttpClient struct {
	url string
}

func NewPacingControllerHttpClient(url string, dataKey string) *PacingControllerHttpClient {
	return &PacingControllerHttpClient{
		url: fmt.Sprintf("%s/pacing/%s", url, dataKey),
	}
}

func (client *PacingControllerHttpClient) GetPacingRateById(id utils.ID) (*PacingData, error) {
	url := fmt.Sprintf("%s/get_by_id?id=%d", client.url, id)
	data := &PacingData{}
	err := echo_helper.GetJson(url, data)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (client *PacingControllerHttpClient) GetPacingRate(idList []utils.ID) (map[utils.ID]*PacingData, error) {
	url := fmt.Sprintf("%s/get?ids=%s", client.url, slice_utils.IdSliceToString(idList))
	data := make(map[utils.ID]*PacingData)
	err := echo_helper.GetJson(url, &data)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (client *PacingControllerHttpClient) GetAllPacingData() (map[utils.ID]*PacingData, error) {
	url := fmt.Sprintf("%s/all", client.url)
	data := make(map[utils.ID]*PacingData)
	err := echo_helper.GetJson(url, &data)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (client *PacingControllerHttpClient) GetDefaultPacingData() *PacingData {
	url := fmt.Sprintf("%s/default", client.url)
	data := &PacingData{}
	err := echo_helper.GetJson(url, data)
	if err != nil {
		return nil
	}

	return data
}

func (client *PacingControllerHttpClient) GetDataKey() string {
	url := fmt.Sprintf("%s/data_key", client.url)
	data := ""
	err := echo_helper.GetJson(url, &data)
	if err != nil {
		return ""
	}

	return data
}

func (client *PacingControllerHttpClient) RegisterDeliveryService(name string) {
	url := fmt.Sprintf("%s/register_delivery_service?name=%s", client.url, name)
	echo_helper.GetJson(url, nil)
}

func (client *PacingControllerHttpClient) GetDeliveryServiceList() []string {
	url := fmt.Sprintf("%s/get_delivery_service_list", client.url)
	data := make([]string, 0)
	err := echo_helper.GetJson(url, &data)
	if err != nil {
		return nil
	}

	return data
}
