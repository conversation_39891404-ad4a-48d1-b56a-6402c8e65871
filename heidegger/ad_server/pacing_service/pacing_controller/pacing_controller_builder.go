package pacing_controller

import (
	"errors"
	"fmt"
	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/math_utils"
	"math"
	"strconv"
	"sync"
	"time"
)

type PacingControllerBuilder struct {
	client             *redis.Client
	dataKey            string
	timeSpan           int64
	windowSize         int64
	pacingTargetLoader func() []PacingTarget

	defaultPacingData *PacingData
	pacingDataCache   map[utils.ID]*PacingData

	deliveryServiceRegister map[string]int64
	deliveryServiceLock     sync.RWMutex

	pacingBuilderHistogram *prometheus_helper.LabelHistogram
	pacingBuilderGauge     *prometheus_helper.LabelGauge

	checkRealtimeLatency bool

	term chan struct{}
}

func NewPacingControllerBuilder(dataKey string, timeSpan int64, windowSize int64) *PacingControllerBuilder {
	return &PacingControllerBuilder{
		dataKey:    dataKey,
		timeSpan:   timeSpan,
		windowSize: windowSize,
		defaultPacingData: &PacingData{
			ImpressionRate: defaultImpressionRate,
			PacingRate:     defaultPacingRate,
		},
		pacingDataCache:         make(map[utils.ID]*PacingData),
		deliveryServiceRegister: make(map[string]int64),

		pacingBuilderHistogram: prometheus_helper.RegisterLabelHistogram(fmt.Sprintf("PacingBuilder_%s", dataKey), []string{"key"}),
		pacingBuilderGauge:     prometheus_helper.RegisterLabelGauge(fmt.Sprintf("PacingBuilder_Gauge_%s", dataKey), []string{"key", "value"}),

		term: make(chan struct{}),
	}
}

func (c *PacingControllerBuilder) GetDataKey() string {
	return c.dataKey
}

func (c *PacingControllerBuilder) SetRedisClient(client *redis.Client) {
	c.client = client
}

func (c *PacingControllerBuilder) SetPacingTargetLoader(loader func() []PacingTarget) {
	c.pacingTargetLoader = loader
}

func (c *PacingControllerBuilder) SetCheckRealtimeLatency(check bool) {
	c.checkRealtimeLatency = check
}

func (c *PacingControllerBuilder) Start() error {
	if c.client == nil {
		return errors.New("redis client is nil")
	}

	if c.pacingTargetLoader == nil {
		return errors.New("pacing target loader is nil")
	}

	if err := c.StartBuildPacingData(); err != nil {
		return err
	}

	return nil
}

func (c *PacingControllerBuilder) Stop() {
	close(c.term)
}

func (c *PacingControllerBuilder) GetPacingRateById(id utils.ID) (*PacingData, error) {
	if pacingData, ok := c.pacingDataCache[id]; ok {
		return pacingData, nil
	}

	return c.defaultPacingData, nil
}

func (c *PacingControllerBuilder) GetPacingRate(idList []utils.ID) (map[utils.ID]*PacingData, error) {
	result := make(map[utils.ID]*PacingData)
	for _, id := range idList {
		data, err := c.GetPacingRateById(id)
		if err != nil {
			return nil, err
		}

		result[id] = data
	}

	return result, nil
}

func (c *PacingControllerBuilder) GetAllPacingData() (map[utils.ID]*PacingData, error) {
	return c.pacingDataCache, nil
}

func (c *PacingControllerBuilder) GetDefaultPacingData() *PacingData {
	return c.defaultPacingData
}

func (c *PacingControllerBuilder) RegisterDeliveryService(name string) {
	c.deliveryServiceLock.Lock()
	defer c.deliveryServiceLock.Unlock()

	c.deliveryServiceRegister[name] = time.Now().Unix()
}

func (c *PacingControllerBuilder) GetDeliveryServiceCount() int {
	c.deliveryServiceLock.RLock()
	defer c.deliveryServiceLock.RUnlock()

	return len(c.deliveryServiceRegister)
}

func (c *PacingControllerBuilder) GetDeliveryServiceList() []string {
	c.deliveryServiceLock.RLock()
	defer c.deliveryServiceLock.RUnlock()

	result := make([]string, 0, len(c.deliveryServiceRegister))
	for name := range c.deliveryServiceRegister {
		result = append(result, name)
	}

	return result
}

func (c *PacingControllerBuilder) InvalidateDeliveryService() {
	expire := 120
	c.deliveryServiceLock.Lock()
	defer c.deliveryServiceLock.Unlock()

	currentTimestamp := time.Now().Unix()
	for name, timestamp := range c.deliveryServiceRegister {
		if int(currentTimestamp-timestamp) > expire {
			delete(c.deliveryServiceRegister, name)
		}
	}
}

func (c *PacingControllerBuilder) loadPacingData(id utils.ID, currentTimestamp int64, timeSpan int64, windowSize int64) (*PacingData, error) {
	pacingData := &PacingData{
		PacingInWindow:   make([]int64, windowSize),
		PacingOutWindow:  make([]int64, windowSize),
		ImpressionWindow: make([]int64, windowSize),
		ImpressionToday:  0,
	}

	for i := int64(0); i < windowSize; i++ {
		key := getPacingKey(currentTimestamp-(i*timeSpan), id, c.dataKey, timeSpan)
		result, err := c.client.HGetAll(key).Result()
		if err != nil {
			if errors.Is(err, redis.Nil) {
				continue
			} else {
				return nil, err
			}
		}

		for key, value := range result {
			switch key {
			case "pi":
				pacingData.PacingInWindow[i], _ = strconv.ParseInt(value, 10, 64)
			case "po":
				pacingData.PacingOutWindow[i], _ = strconv.ParseInt(value, 10, 64)
			case "i":
				pacingData.ImpressionWindow[i], _ = strconv.ParseInt(value, 10, 64)
			}
		}
	}

	key := getDailyImpressionKey(currentTimestamp, id, c.dataKey)
	impressionTodayFile, err := c.client.Get(key).Int64()
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}

	impressionTodayRealtime, err := c.client.Get(getDailyImpressionRealtimeKey(currentTimestamp, id, c.dataKey)).Int64()
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}

	impressionTodayPacingBackUp, err := c.client.Get(getDailyImpressionPacingBackupKey(currentTimestamp, id, c.dataKey)).Int64()
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}

	resultImpression := impressionTodayRealtime
	if impressionTodayPacingBackUp > int64(float64(impressionTodayRealtime)*1.1) {
		resultImpression = impressionTodayPacingBackUp
	}

	if impressionTodayFile > int64(float64(impressionTodayRealtime)*1.1) {
		resultImpression = impressionTodayFile
	}

	pacingData.ImpressionToday = int64(resultImpression)
	pacingData.ImpressionTodayRealtime = int64(impressionTodayRealtime)
	pacingData.ImpressionTodayPacingBackup = int64(impressionTodayPacingBackUp)
	pacingData.ImpressionTodayFile = int64(impressionTodayFile)

	realtimeTimestamp, err := c.client.Get("pacing_realtime_timestamp").Int64()
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}

	pacingData.PacingRealtimeTimestamp = time.Unix(realtimeTimestamp, 0)

	return pacingData, nil
}

func (c *PacingControllerBuilder) buildPacing() error {
	pacingDataMap := make(map[utils.ID]*PacingData)

	currentTimestamp := time.Now().Unix()

	for _, target := range c.pacingTargetLoader() {
		if target.ImpressionLimit == 0 || target.MinutesRemain == 0 {
			continue
		}

		pacingData, err := c.buildPacingEach(target.Id, currentTimestamp, target.ImpressionLimit, target.MinutesRemain, target)
		if err != nil {
			zap.L().Error("[PacingControllerBuilder] buildPacingEach error", zap.Error(err))
			continue
		}

		if pacingData.Id == 80 {
			zap.L().Info("[PacingControllerBuilder] buildPacing, for target:, data:%s, pacing:%s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(target.Id)))), zap.Int64("param2", int64(c.dataKey)), zap.Int64("param3", int64(pacingData)))
		}
		pacingDataMap[target.Id] = pacingData

		zap.L().Info("[PacingControllerBuilder] buildPacing, for target:, data:%s, pacing:%s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(target.Id)))), zap.Int64("param2", int64(c.dataKey)), zap.Int64("param3", int64(pacingData)))

		c.reportPacingData(pacingData)
	}

	c.pacingDataCache = pacingDataMap
	return nil
}

func (c *PacingControllerBuilder) buildPacingEach(id utils.ID, currentTimestamp int64, impressionLimit int64, minuteRemain int32, target PacingTarget) (*PacingData, error) {
	defer prometheus_helper.LabelHistogramObserverLatency(c.pacingBuilderHistogram, []string{id.String()}, time.Now())

	minuteWindow := c.windowSize

	pacingData, err := c.loadPacingData(id, currentTimestamp, c.timeSpan, c.windowSize)
	if err != nil {
		return nil, err
	}

	pacingData.Id = id
	pacingData.DataKey = c.dataKey
	pacingData.ImpressionLimit = impressionLimit
	pacingData.DeliveryMinutesRemain = minuteRemain
	pacingData.Init()

	if c.checkRealtimeLatency && pacingData.PacingRealtimeLatency > 300 {
		zap.L().Error("[PacingControllerBuilder] buildPacingEach, pacingData:, latency:%f", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", pacingData)))), zap.String("param2", fmt.Sprintf("%v", pacingData.PacingRealtimeLatency)))
		return nil, fmt.Errorf("realtime latency too long")
	}

	// 每次都取历史前五分钟，忽略当前分钟，反应相对滞后，但无所谓

	pacingData.ImpressionLimit = impressionLimit

	impressionRemain := impressionLimit - pacingData.ImpressionToday
	if impressionRemain < 0 {
		impressionRemain = 0
	}
	pacingData.ImpressionRemain = impressionRemain

	if target.ImpressionLimitCalc != nil {
		gap, limit := target.ImpressionLimitCalc(time.Now(), pacingData.ImpressionToday)
		pacingData.ImpressionGap = gap
		pacingData.ImpressionRemainFixed = limit - pacingData.ImpressionToday
		if pacingData.ImpressionRemainFixed < 0 {
			pacingData.ImpressionRemainFixed = 0
		}
	}

	if pacingData.ImpressionRemainFixed != 0 {
		impressionRemain = pacingData.ImpressionRemainFixed
	}

	// 1. 计算下一分钟需要的曝光量
	pacingData.ImpressionNeedNext = int64(math_utils.ProbabilisticRound(math_utils.SafeDiv(float64(impressionRemain), float64(minuteRemain))))
	pacingData.PacingOutNeedNext = int64(math_utils.ProbabilisticRound(math_utils.SafeDiv(float64(pacingData.ImpressionNeedNext), pacingData.ImpressionRate)))
	if pacingData.PacingOutNeedNext == 0 {
		pacingData.PacingOutNeedNext = pacingData.ImpressionNeedNext
	}

	pacingData.EstPacingInNext = int64(math_utils.SafeDiv(float64(pacingData.TotalWindowPacingIn), float64(minuteWindow)))
	pacingData.PacingRate = math_utils.ClipRate(math_utils.SafeDiv(float64(pacingData.PacingOutNeedNext), float64(pacingData.EstPacingInNext)))
	if pacingData.PacingRate == 0 {
		pacingData.PacingRate = defaultPacingRate
	}

	if (pacingData.ImpressionRate == 0 || pacingData.PacingRate == 0) &&
		pacingData.EstPacingInNext > 0 {
		if pacingData.ImpressionRemain/pacingData.EstPacingInNext > 1000 {
			pacingData.PacingRate = 0.05
		} else if pacingData.ImpressionRemain/pacingData.EstPacingInNext > 100 {
			pacingData.PacingRate = 0.05
		} else if pacingData.ImpressionRemain/pacingData.EstPacingInNext > 10 {
			pacingData.PacingRate = 0.02
		} else if pacingData.ImpressionRemain/pacingData.EstPacingInNext > 5 {
			pacingData.PacingRate = 0.01
		}
	}

	pacingData.PacingRate *= pacingRateSpeedup
	pacingData.PacingRate = math_utils.ClipRate(pacingData.PacingRate)

	if pacingData.ImpressionToday >= pacingData.ImpressionLimit {
		pacingData.PacingRate = 0
	}

	pacingData.PacingCount = int64(math.Ceil(math.Ceil(float64(pacingData.EstPacingInNext) * pacingData.PacingRate * defaultPacingMultiple)))
	if pacingData.EstPacingInNext == 0 {
		pacingData.PacingCount = defaultPacingCount
	}

	pacingData.PacingCountTotal = pacingData.PacingCount

	serviceCount := c.GetDeliveryServiceCount()
	if serviceCount != 0 {
		pacingCountEach := float64(pacingData.PacingCount) / float64(serviceCount)
		pacingData.PacingCount = int64(math.Ceil(pacingCountEach))
	}

	pacingData.PacingCountRaw = pacingData.PacingCount

	pacingData.EstPacingOutNext = int64(float64(pacingData.EstPacingInNext) * pacingData.PacingRate)
	pacingData.EstImpressionNext = int64(float64(pacingData.EstPacingOutNext) * pacingData.ImpressionRate)

	pacingData.EstDeliveryFinishMinutes = int32(math_utils.SafeDiv(float64(impressionRemain), pacingData.GetAvgImpression()))
	pacingData.ImpressionCompleteRate = math_utils.SafeDiv(float64(pacingData.ImpressionToday), float64(pacingData.ImpressionLimit))
	pacingData.AvgWindowImpression = pacingData.GetAvgImpression()
	pacingData.AvgWindowPacingIn = pacingData.GetAvgPacingIn()

	return pacingData, nil
}

func (c *PacingControllerBuilder) StartBuildPacingData() error {
	if err := c.buildPacing(); err != nil {
		return err
	}

	go func() {
		zap.L().Info("[PacingControllerBuilder] StartBuildPacingData, check realtime latency", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", c.checkRealtimeLatency)))))
		ticker := time.NewTicker(time.Second * 10)
		defer ticker.Stop()
		for {
			select {
			case <-c.term:
				return
			case <-ticker.C:
				if err := c.buildPacing(); err != nil {
					zap.L().Error("[PacingControllerBuilder] buildPacing error", zap.Error(err))
				}
			}
		}
	}()
	return nil
}

func (c *PacingControllerBuilder) reportPacingData(data *PacingData) {
	labelValues := []string{data.Id.String(), "default"}

	labelValues[1] = "impression_limit"
	c.pacingBuilderGauge.Set(labelValues, float64(data.ImpressionLimit))

	labelValues[1] = "impression_remain"
	c.pacingBuilderGauge.Set(labelValues, float64(data.ImpressionRemain))

	labelValues[1] = "impression_remain_fixed"
	c.pacingBuilderGauge.Set(labelValues, float64(data.ImpressionRemainFixed))

	labelValues[1] = "impression_today"
	c.pacingBuilderGauge.Set(labelValues, float64(data.ImpressionToday))

	labelValues[1] = "impression_today_file"
	c.pacingBuilderGauge.Set(labelValues, float64(data.ImpressionTodayFile))

	labelValues[1] = "impression_today_realtime"
	c.pacingBuilderGauge.Set(labelValues, float64(data.ImpressionTodayRealtime))

	labelValues[1] = "impression_today_pacing_backup"
	c.pacingBuilderGauge.Set(labelValues, float64(data.ImpressionTodayPacingBackup))

	labelValues[1] = "delivery_minutes_remain"
	c.pacingBuilderGauge.Set(labelValues, float64(data.DeliveryMinutesRemain))

	labelValues[1] = "est_pacing_in_next"
	c.pacingBuilderGauge.Set(labelValues, float64(data.EstPacingInNext))

	labelValues[1] = "est_impression_next"
	c.pacingBuilderGauge.Set(labelValues, float64(data.EstImpressionNext))

	labelValues[1] = "impression_need_next"
	c.pacingBuilderGauge.Set(labelValues, float64(data.ImpressionNeedNext))

	labelValues[1] = "est_delivery_finish_minute"
	c.pacingBuilderGauge.Set(labelValues, float64(data.EstDeliveryFinishMinutes))

	labelValues[1] = "pacing_rate"
	c.pacingBuilderGauge.Set(labelValues, data.PacingRate)

	labelValues[1] = "pacing_count"
	c.pacingBuilderGauge.Set(labelValues, float64(data.PacingCount))

	labelValues[1] = "impression_complete_rate"
	c.pacingBuilderGauge.Set(labelValues, data.ImpressionCompleteRate)

	labelValues[1] = "impression_rate"
	c.pacingBuilderGauge.Set(labelValues, data.ImpressionRate)

	labelValues[1] = "avg_window_impression"
	c.pacingBuilderGauge.Set(labelValues, data.AvgWindowImpression)

	labelValues[1] = "avg_pacing_in"
	c.pacingBuilderGauge.Set(labelValues, data.AvgWindowPacingIn)

	labelValues[1] = "time_latency"
	c.pacingBuilderGauge.Set(labelValues, data.GetRealtimeLatency())
}
