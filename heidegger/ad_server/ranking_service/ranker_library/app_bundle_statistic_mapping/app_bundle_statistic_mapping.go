package app_bundle_statistic_mapping

import (
	"archive/tar"
	"compress/gzip"
	"encoding/csv"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/data_fetcher"
	"io"
	"strconv"
	"strings"
)

const DefaultAppBundle = "default.app.bundle"

type AppBundleStatisticMappingInterface interface {
	GetAppBundle(mediaSlotId int32, mediaSlotType int32, appBundle string) string
}

type AppBundleStatisticMapping struct {
	dataFetcher    data_fetcher.VersionedDataFetcher
	slotAppMapping map[int32]map[int32]map[string]struct{}

	term chan struct{}
}

func NewAppBundleStatisticMapping(dataFetcher data_fetcher.VersionedDataFetcher) *AppBundleStatisticMapping {
	return &AppBundleStatisticMapping{
		dataFetcher:    dataFetcher,
		slotAppMapping: make(map[int32]map[int32]map[string]struct{}),
		term:           make(chan struct{}),
	}
}

func (m *AppBundleStatisticMapping) Start() error {
	if err := m.dataFetcher.Start(); err != nil {
		return err
	}

	if err := m.reloadModel(); err != nil {
		return err
	}

	m.dataFetcher.RegisterObserver(60, m.observerCallback)
	return nil
}

func (m *AppBundleStatisticMapping) Stop() {
	m.dataFetcher.Stop()

	close(m.term)
}

func (m *AppBundleStatisticMapping) observerCallback(version string, reader io.ReadCloser) error {
	return m.reloadModelFromReader(version, reader)
}

func (m *AppBundleStatisticMapping) reloadModel() error {
	version, reader, err := m.dataFetcher.GetOriginDataHead()
	if err != nil {
		return err
	}

	return m.reloadModelFromReader(version, reader)
}

func (m *AppBundleStatisticMapping) reloadModelFromReader(version string, reader io.ReadCloser) error {
	defer reader.Close()

	if strings.HasSuffix(version, "tar.gz") {
		if err := m.reloadTarFromReader(reader, "data.csv"); err != nil {
			return fmt.Errorf("[AppBundleStatisticMapping] failed to load tar: %v", err)
		}
	} else {
		if err := m.reloadCsvFromReader(reader); err != nil {
			return fmt.Errorf("[AppBundleStatisticMapping] failed to load CSV: %v", err)
		}
	}

	return nil
}

func (m *AppBundleStatisticMapping) reloadCsvFromReader(reader io.Reader) error {
	csvReader := csv.NewReader(reader)
	csvReader.Comma = '\t'
	// skip header
	header, err := csvReader.Read()
	if err != nil {
		return err
	}

	slotAppMapping := make(map[int32]map[int32]map[string]struct{})

	for {
		row, err := csvReader.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return errors.Wrapf(err, "[AppBundleStatisticMapping] read csv error, header_count:%d", len(header))
		}

		mediaSlotId, _ := strconv.Atoi(row[0])
		mediaSlotType, _ := strconv.Atoi(row[1])
		appId := row[2]

		if _, ok := slotAppMapping[int32(mediaSlotId)]; !ok {
			slotAppMapping[int32(mediaSlotId)] = make(map[int32]map[string]struct{})
		}

		if _, ok := slotAppMapping[int32(mediaSlotId)][int32(mediaSlotType)]; !ok {
			slotAppMapping[int32(mediaSlotId)][int32(mediaSlotType)] = make(map[string]struct{})
		}

		slotAppMapping[int32(mediaSlotId)][int32(mediaSlotType)][appId] = struct{}{}
	}

	m.slotAppMapping = slotAppMapping

	return nil
}

func (m *AppBundleStatisticMapping) reloadTarFromReader(r io.Reader, targetCsvFile string) error {
	// 创建 gzip 解压缩器
	gzr, err := gzip.NewReader(r)
	if err != nil {
		zap.L().Error("[AppBundleStatisticMapping] Failed to create gzip reader", zap.Error(err))
		return err
	}
	defer gzr.Close()

	// 创建 tar 读取器
	tr := tar.NewReader(gzr)

	// 遍历 tar 中的所有文件
	for {
		header, err := tr.Next()
		if err == io.EOF {
			return fmt.Errorf("file not found:%s", targetCsvFile)
		}

		if err != nil {
			return fmt.Errorf("failed to read tar file: %v", err)
		}

		// 只处理特定的文件名
		if header.Name == targetCsvFile {
			zap.L().Info("[AppBundleStatisticMapping] Found target file", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", header.Name)))))

			if err := m.reloadCsvFromReader(tr); err != nil {
				return err
			} else {
				return nil
			}
		}
	}
}

func (m *AppBundleStatisticMapping) GetAppBundle(mediaSlotId int32, mediaSlotType int32, appBundle string) string {
	if m == nil {
		return appBundle
	}

	if slotMap, ok := m.slotAppMapping[mediaSlotId]; ok {
		if appMap, ok := slotMap[mediaSlotType]; ok {
			if _, ok := appMap[appBundle]; ok {
				return appBundle
			}
		}
	}

	return DefaultAppBundle
}

func (m *AppBundleStatisticMapping) RegisterEcho(echoServer *echo.Echo) {
	echoServer.GET("/app_bundle_statistic_mapping/app_bundle", m.GetAppBundleHandler)
}

func (m *AppBundleStatisticMapping) GetAppBundleHandler(c echo.Context) error {
	mediaSlotId, _ := strconv.Atoi(c.QueryParam("media_slot_id"))
	mediaSlotType, _ := strconv.Atoi(c.QueryParam("media_slot_type"))
	appBundle := c.QueryParam("app_bundle")

	return c.JSON(200, m.GetAppBundle(int32(mediaSlotId), int32(mediaSlotType), appBundle))
}

type DefaultAppBundleStatisticMapping struct{}

func NewDefaultAppBundleStatisticMapping() *DefaultAppBundleStatisticMapping {
	return &DefaultAppBundleStatisticMapping{}
}

func (m *DefaultAppBundleStatisticMapping) GetAppBundle(mediaSlotId int32, mediaSlotType int32, appBundle string) string {
	return DefaultAppBundle
}
