package broadcast_scheduler_v2

import (
	"archive/tar"
	"compress/gzip"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker_library/app_bundle_statistic_mapping"
	"gitlab.com/dev/heidegger/library/data_fetcher"
	"gitlab.com/dev/heidegger/library/echo_helper"
	"gitlab.com/dev/heidegger/library/utils/limiter"
	"gitlab.com/dev/heidegger/master_server/master_server"
	"io"
	"math/rand"
	"sort"
	"strconv"
)

type BroadcastScheduleItemV2 struct {
	Level          int32                            `json:"level"`
	RealQpmLimit   float64                          `json:"real_qpm_limit"`
	RealPacingRate float64                          `json:"real_pacing_rate"`
	AllocQpmLimit  int32                            `json:"alloc_qpm_limit"`
	Limiter        *limiter.SimplePresetRateLimiter `json:"-"`
}

func (s *BroadcastScheduleItemV2) String() string {
	data, _ := json.Marshal(s)
	return string(data)
}

type BroadcastScheduleV2 struct {
	Key   string                     `json:"key"`
	Level int32                      `json:"level"`
	Items []*BroadcastScheduleItemV2 `json:"items"`
}

func (s *BroadcastScheduleV2) String() string {
	data, _ := json.Marshal(s)
	return string(data)
}

func (s *BroadcastScheduleV2) GetLevelSchedule(level int32) *BroadcastScheduleItemV2 {
	// binary search
	l, r := 0, len(s.Items)-1
	for l <= r {
		m := l + (r-l)/2
		if s.Items[m].Level == level {
			return s.Items[m]
		} else if s.Items[m].Level < level {
			l = m + 1
		} else {
			r = m - 1
		}
	}

	return nil
}

type BroadcastSchedulerV2 struct {
	dataFetcher               data_fetcher.VersionedDataFetcher
	appBundleStatisticMapping app_bundle_statistic_mapping.AppBundleStatisticMappingInterface
	serviceWatcher            master_server.ServiceWatcher
	schedule                  map[string]*BroadcastScheduleV2
	trafficLevelMapping       map[string]int32

	term chan struct{}
}

func NewBroadcastSchedulerV2(
	dataFetcher data_fetcher.VersionedDataFetcher,
	appBundleStatisticMapping app_bundle_statistic_mapping.AppBundleStatisticMappingInterface,
	serviceWatcher master_server.ServiceWatcher) *BroadcastSchedulerV2 {
	return &BroadcastSchedulerV2{
		dataFetcher:               dataFetcher,
		appBundleStatisticMapping: appBundleStatisticMapping,
		serviceWatcher:            serviceWatcher,
		schedule:                  make(map[string]*BroadcastScheduleV2),
		trafficLevelMapping:       make(map[string]int32),
		term:                      make(chan struct{}),
	}
}

func (m *BroadcastSchedulerV2) Start() error {
	if err := m.dataFetcher.Start(); err != nil {
		return err
	}

	if err := m.reloadModel(); err != nil {
		return err
	}

	m.dataFetcher.RegisterObserver(30, m.observerCallback)
	return nil
}

func (m *BroadcastSchedulerV2) Stop() {
	m.dataFetcher.Stop()

	close(m.term)
}

func (m *BroadcastSchedulerV2) observerCallback(version string, reader io.ReadCloser) error {
	return m.reloadModelFromReader(version, reader)
}

func (m *BroadcastSchedulerV2) reloadModel() error {
	version, reader, err := m.dataFetcher.GetOriginDataHead()
	if err != nil {
		return err
	}

	return m.reloadModelFromReader(version, reader)
}

func (m *BroadcastSchedulerV2) reloadModelFromReader(version string, reader io.ReadCloser) error {
	zap.L().Info("[BroadcastSchedulerV2] reload model from reader, version", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", version)))))

	defer reader.Close()

	// 创建 gzip 解压缩器
	gzr, err := gzip.NewReader(reader)
	if err != nil {
		zap.L().Error("[BroadcastSchedulerV2] Failed to create gzip reader", zap.Error(err))
		return err
	}
	defer gzr.Close()

	// 创建 tar 读取器
	tr := tar.NewReader(gzr)

	var scheduleMap map[string]*BroadcastScheduleV2
	var trafficLevelMap map[string]int32

	// 遍历 tar 中的所有文件
	for {
		header, err := tr.Next()
		if err == io.EOF {
			return fmt.Errorf("file not found")
		}

		if err != nil {
			return fmt.Errorf("failed to read tar file: %v", err)
		}

		// 只处理特定的文件名
		if header.Name == "schedule_data.csv" {
			zap.L().Info("[BroadcastSchedulerV2] Found target file", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", header.Name)))))

			scheduleMap, err = m.reloadScheduleDataFromReader(tr)
			if err != nil {
				return err
			}
		}

		if header.Name == "traffic_level_group_data.csv" {
			zap.L().Info("[BroadcastSchedulerV2] Found target file", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", header.Name)))))

			trafficLevelMap, err = m.reloadTrafficLevelGroupDataFromReader(tr)
			if err != nil {
				return err
			}
		}

		if trafficLevelMap != nil && scheduleMap != nil {
			break
		}
	}

	m.trafficLevelMapping = trafficLevelMap
	m.schedule = scheduleMap

	return nil
}

const (
	rateLimitPeriod = 60 * 2
	rateLimitFactor = 2.5
)

func (m *BroadcastSchedulerV2) reloadScheduleDataFromReader(reader io.Reader) (map[string]*BroadcastScheduleV2, error) {
	csvReader := csv.NewReader(reader)
	csvReader.Comma = '\t'
	// skip header
	header, err := csvReader.Read()
	if err != nil {
		return nil, err
	}

	if len(header) < 8 {
		return nil, errors.Errorf("[BroadcastSchedulerV2] header count error, header_count:%d, header:%v", len(header), header)
	}

	schedule := make(map[string]*BroadcastScheduleV2)
	serviceCount := m.serviceWatcher.GetServiceCount()
	randSeed := rand.Float64()

	for {
		row, err := csvReader.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, errors.Wrapf(err, "[BroadcastSchedulerV2] read csv error, header_count:%d", len(header))
		}

		id, err := strconv.Atoi(row[0])
		if err != nil {
			return nil, errors.Wrapf(err, "[BroadcastSchedulerV2] failed to convert id: %v", err)
		}

		mediaSlotId, err := strconv.Atoi(row[1])
		if err != nil {
			return nil, errors.Wrapf(err, "[BroadcastSchedulerV2] failed to convert media slot id: %v", err)
		}

		mediaSlotType, err := strconv.Atoi(row[2])
		if err != nil {
			return nil, errors.Wrapf(err, "[BroadcastSchedulerV2] failed to convert media slot id: %v", err)
		}

		appBundleId := row[3]
		dspSlotId, err := strconv.Atoi(row[4])
		if err != nil {
			return nil, errors.Wrapf(err, "[BroadcastSchedulerV2] failed to convert dsp slot id: %v", err)
		}

		trafficLevelGroup, err := strconv.Atoi(row[5])
		if err != nil {
			return nil, errors.Wrapf(err, "[BroadcastSchedulerV2] failed to convert traffic level group: %v", err)
		}

		qpmLimit, err := strconv.ParseFloat(row[6], 64)
		if err != nil {
			return nil, errors.Wrapf(err, "[BroadcastSchedulerV2] failed to convert qpm limit: %v", err)
		}

		pacingRate, err := strconv.ParseFloat(row[7], 64)
		if err != nil {
			return nil, errors.Wrapf(err, "[BroadcastSchedulerV2] failed to convert pacing rate: %v", err)
		}

		key := fmt.Sprintf("%d^%d^%d^%s^%d", id, mediaSlotId, mediaSlotType, appBundleId, dspSlotId)

		if _, ok := schedule[key]; !ok {
			schedule[key] = &BroadcastScheduleV2{
				Key:   key,
				Level: int32(trafficLevelGroup),
				Items: make([]*BroadcastScheduleItemV2, 0),
			}
		}

		limitCount := limiter.GetDistributedRateLimit(uint32(qpmLimit*rateLimitFactor), uint32(serviceCount), randSeed)

		var rateLimiter *limiter.SimplePresetRateLimiter
		oldSchedule := schedule[key]
		if oldSchedule != nil {
			oldScheduleItem := oldSchedule.GetLevelSchedule(int32(trafficLevelGroup))
			if oldScheduleItem != nil {
				rateLimiter = oldScheduleItem.Limiter
				rateLimiter.SetLimit(limitCount)
			}
		}

		if rateLimiter == nil {
			rateLimiter = limiter.NewSimplePresetRateLimiterWithSeconds(limitCount, rateLimitPeriod)
		}

		schedule[key].Items = append(schedule[key].Items, &BroadcastScheduleItemV2{
			Level:          int32(trafficLevelGroup),
			RealQpmLimit:   qpmLimit,
			RealPacingRate: pacingRate,
			AllocQpmLimit:  int32(limitCount),
			Limiter:        rateLimiter,
		})
	}

	return schedule, nil
}

func (m *BroadcastSchedulerV2) reloadTrafficLevelGroupDataFromReader(reader io.Reader) (map[string]int32, error) {
	csvReader := csv.NewReader(reader)
	csvReader.Comma = '\t'
	// skip header
	header, err := csvReader.Read()
	if err != nil {
		return nil, err
	}

	if len(header) < 4 {
		return nil, errors.Errorf("[BroadcastSchedulerV2] header count error, header_count:%d, header:%v", len(header), header)
	}

	trafficLevelMapping := make(map[string]int32)

	for {
		row, err := csvReader.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return nil, errors.Wrapf(err, "[BroadcastSchedulerV2] read csv error, header_count:%d", len(header))
		}

		id, err := strconv.Atoi(row[0])
		if err != nil {
			return nil, errors.Wrapf(err, "[BroadcastSchedulerV2] failed to convert id: %v", err)
		}

		appBundleId := row[1]

		trafficLevel, err := strconv.Atoi(row[2])
		if err != nil {
			return nil, errors.Wrapf(err, "[BroadcastSchedulerV2] failed to convert traffic level: %v", err)
		}

		trafficLevelGroup, err := strconv.Atoi(row[3])
		if err != nil {
			return nil, errors.Wrapf(err, "[BroadcastSchedulerV2] failed to convert traffic level group: %v", err)
		}

		key := fmt.Sprintf("%d^%s^%d", id, appBundleId, trafficLevel)

		trafficLevelMapping[key] = int32(trafficLevelGroup)
	}

	return trafficLevelMapping, nil
}

func (m *BroadcastSchedulerV2) publishSchedule(schedule map[string]*BroadcastScheduleV2) {
	for key, _ := range schedule {
		sort.Slice(schedule[key].Items, func(i, j int) bool {
			return schedule[key].Items[i].Level < schedule[key].Items[j].Level
		})
	}

	m.schedule = schedule
}

func (m *BroadcastSchedulerV2) GetBroadcastSchedule(id int32, mediaSlotId int32, mediaSlotType int32, appBundleId string, dspSlotId int32, trafficLevel int32) *BroadcastScheduleItemV2 {
	if m.appBundleStatisticMapping != nil {
		appBundleId = m.appBundleStatisticMapping.GetAppBundle(mediaSlotId, mediaSlotType, appBundleId)
	}

	trafficLevel, ok := m.trafficLevelMapping[fmt.Sprintf("%d^%s^%d", id, appBundleId, trafficLevel)]
	if !ok {
		trafficLevel = 20
	}

	key := fmt.Sprintf("%d^%d^%d^%s^%d", id, mediaSlotId, mediaSlotType, appBundleId, dspSlotId)
	schedule, ok := m.schedule[key]
	if !ok {
		return nil
	}

	return schedule.GetLevelSchedule(trafficLevel)
}

func (m *BroadcastSchedulerV2) RegisterEcho(echoServer *echo.Echo) {
	echoServer.POST("/broadcast_schedule_v2/get_schedule", m.getScheduleHttpHandler)
}

func (m *BroadcastSchedulerV2) getScheduleHttpHandler(c echo.Context) error {
	type Request struct {
		Id            int32  `json:"id"`
		MediaSlotId   int32  `json:"media_slot_id"`
		MediaSlotType int32  `json:"media_slot_type"`
		AppBundleId   string `json:"app_bundle_id"`
		DspSlotId     int32  `json:"dsp_slot_id"`
		TrafficLevel  int32  `json:"traffic_level"`
	}

	req := &Request{}
	if err := c.Bind(req); err != nil {
		return err
	}

	schedule := m.GetBroadcastSchedule(req.Id, req.MediaSlotId, req.MediaSlotType, req.AppBundleId, req.DspSlotId, req.TrafficLevel)
	if schedule == nil {
		return echo_helper.ErrorResponse(c, fmt.Errorf("schedule not found"))
	}

	return echo_helper.Response(c, schedule)
}
