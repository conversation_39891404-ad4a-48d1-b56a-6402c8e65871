package broadcast_scheduler

import (
	"archive/tar"
	"compress/gzip"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker_library/app_bundle_statistic_mapping"
	"gitlab.com/dev/heidegger/library/data_fetcher"
	"gitlab.com/dev/heidegger/library/echo_helper"
	"io"
	"sort"
	"strconv"
	"strings"
)

type BroadcastScheduleItem struct {
	Level          int32   `json:"level"`
	RealQpmLimit   float64 `json:"real_qpm_limit"`
	RealPacingRate float64 `json:"real_pacing_rate"`
	AllocQpmLimit  int32   `json:"alloc_qpm_limit"`
}

func (s *BroadcastScheduleItem) String() string {
	data, _ := json.Marshal(s)
	return string(data)
}

type BroadcastSchedule struct {
	Key   string                   `json:"key"`
	Items []*BroadcastScheduleItem `json:"items"`
}

func (s *BroadcastSchedule) String() string {
	data, _ := json.Marshal(s)
	return string(data)
}

func (s *BroadcastSchedule) GetLevelSchedule(level int32) *BroadcastScheduleItem {
	level = level / 5 * 5
	// binary search
	l, r := 0, len(s.Items)-1
	for l <= r {
		m := l + (r-l)/2
		if s.Items[m].Level == level {
			return s.Items[m]
		} else if s.Items[m].Level < level {
			l = m + 1
		} else {
			r = m - 1
		}
	}

	return nil
}

type BroadcastScheduler struct {
	dataFetcher               data_fetcher.VersionedDataFetcher
	appBundleStatisticMapping *app_bundle_statistic_mapping.AppBundleStatisticMapping
	schedule                  map[string]*BroadcastSchedule

	term chan struct{}
}

func NewBroadcastScheduler(dataFetcher data_fetcher.VersionedDataFetcher, appBundleStatisticMapping *app_bundle_statistic_mapping.AppBundleStatisticMapping) *BroadcastScheduler {
	return &BroadcastScheduler{
		dataFetcher:               dataFetcher,
		appBundleStatisticMapping: appBundleStatisticMapping,
		schedule:                  make(map[string]*BroadcastSchedule),
		term:                      make(chan struct{}),
	}
}

func (m *BroadcastScheduler) Start() error {
	if err := m.dataFetcher.Start(); err != nil {
		return err
	}

	if err := m.reloadModel(); err != nil {
		return err
	}

	m.dataFetcher.RegisterObserver(60, m.observerCallback)
	return nil
}

func (m *BroadcastScheduler) Stop() {
	m.dataFetcher.Stop()

	close(m.term)
}

func (m *BroadcastScheduler) observerCallback(version string, reader io.ReadCloser) error {
	return m.reloadModelFromReader(version, reader)
}

func (m *BroadcastScheduler) reloadModel() error {
	version, reader, err := m.dataFetcher.GetOriginDataHead()
	if err != nil {
		return err
	}

	return m.reloadModelFromReader(version, reader)
}

func (m *BroadcastScheduler) reloadModelFromReader(version string, reader io.ReadCloser) error {
	zap.L().Info("[BroadcastScheduler] reload model from reader, version", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", version)))))

	defer reader.Close()

	if strings.HasSuffix(version, "tar.gz") {
		if err := m.reloadTarFromReader(reader, "data.csv"); err != nil {
			return fmt.Errorf("[BroadcastScheduler] failed to load tar: %v", err)
		}
	} else {
		if err := m.reloadCsvFromReader(reader); err != nil {
			return fmt.Errorf("[BroadcastScheduler] failed to load CSV: %v", err)
		}
	}

	return nil
}

func (m *BroadcastScheduler) reloadCsvFromReader(reader io.Reader) error {
	csvReader := csv.NewReader(reader)
	csvReader.Comma = '\t'
	// skip header
	header, err := csvReader.Read()
	if err != nil {
		return err
	}

	if len(header) < 8 {
		return errors.Errorf("[BroadcastScheduler] header count error, header_count:%d, header:%v", len(header), header)
	}

	schedule := make(map[string]*BroadcastSchedule)

	for {
		row, err := csvReader.Read()
		if err == io.EOF {
			break
		}

		if err != nil {
			return errors.Wrapf(err, "[BroadcastScheduler] read csv error, header_count:%d", len(header))
		}

		id, err := strconv.Atoi(row[0])
		if err != nil {
			return errors.Wrapf(err, "[BroadcastScheduler] failed to convert id: %v", err)
		}

		mediaSlotId, err := strconv.Atoi(row[1])
		if err != nil {
			return errors.Wrapf(err, "[BroadcastScheduler] failed to convert media slot id: %v", err)
		}

		mediaSlotType, err := strconv.Atoi(row[2])
		if err != nil {
			return errors.Wrapf(err, "[BroadcastScheduler] failed to convert media slot id: %v", err)
		}

		appBundleId := row[3]
		dspSlotId, err := strconv.Atoi(row[4])
		if err != nil {
			return errors.Wrapf(err, "[BroadcastScheduler] failed to convert dsp slot id: %v", err)
		}

		trafficLevelGroup, err := strconv.Atoi(row[5])
		if err != nil {
			return errors.Wrapf(err, "[BroadcastScheduler] failed to convert traffic level group: %v", err)
		}

		qpmLimit, err := strconv.ParseFloat(row[6], 64)
		if err != nil {
			return errors.Wrapf(err, "[BroadcastScheduler] failed to convert qpm limit: %v", err)
		}

		pacingRate, err := strconv.ParseFloat(row[7], 64)
		if err != nil {
			return errors.Wrapf(err, "[BroadcastScheduler] failed to convert pacing rate: %v", err)
		}

		key := fmt.Sprintf("%d^%d^%d^%s^%d", id, mediaSlotId, mediaSlotType, appBundleId, dspSlotId)

		if _, ok := schedule[key]; !ok {
			schedule[key] = &BroadcastSchedule{
				Key:   key,
				Items: make([]*BroadcastScheduleItem, 0),
			}
		}

		schedule[key].Items = append(schedule[key].Items, &BroadcastScheduleItem{
			Level:          int32(trafficLevelGroup),
			RealQpmLimit:   qpmLimit,
			RealPacingRate: pacingRate,
		})
	}

	m.publishSchedule(schedule)

	return nil
}

func (m *BroadcastScheduler) reloadTarFromReader(r io.Reader, targetCsvFile string) error {
	// 创建 gzip 解压缩器
	gzr, err := gzip.NewReader(r)
	if err != nil {
		zap.L().Error("[BroadcastScheduler] Failed to create gzip reader", zap.Error(err))
		return err
	}
	defer gzr.Close()

	// 创建 tar 读取器
	tr := tar.NewReader(gzr)

	// 遍历 tar 中的所有文件
	for {
		header, err := tr.Next()
		if err == io.EOF {
			return fmt.Errorf("file not found:%s", targetCsvFile)
		}

		if err != nil {
			return fmt.Errorf("failed to read tar file: %v", err)
		}

		// 只处理特定的文件名
		if header.Name == targetCsvFile {
			zap.L().Info("[BroadcastScheduler] Found target file", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", header.Name)))))

			if err := m.reloadCsvFromReader(tr); err != nil {
				return err
			} else {
				return nil
			}
		}
	}
}

func (m *BroadcastScheduler) publishSchedule(schedule map[string]*BroadcastSchedule) {
	for key, _ := range schedule {
		sort.Slice(schedule[key].Items, func(i, j int) bool {
			return schedule[key].Items[i].Level < schedule[key].Items[j].Level
		})
	}

	m.schedule = schedule
}

func (m *BroadcastScheduler) GetBroadcastSchedule(id int32, mediaSlotId int32, mediaSlotType int32, appBundleId string, dspSlotId int32) *BroadcastSchedule {
	if m.appBundleStatisticMapping != nil {
		appBundleId = m.appBundleStatisticMapping.GetAppBundle(mediaSlotId, mediaSlotType, appBundleId)
	}

	key := fmt.Sprintf("%d^%d^%d^%s^%d", id, mediaSlotId, mediaSlotType, appBundleId, dspSlotId)
	schedule, ok := m.schedule[key]
	if !ok {
		appBundleId = m.appBundleStatisticMapping.GetAppBundle(mediaSlotId, mediaSlotType, "unknown")
		key = fmt.Sprintf("%d^%d^%d^%s^%d", id, mediaSlotId, mediaSlotType, appBundleId, dspSlotId)
	}

	schedule, ok = m.schedule[key]
	if !ok {
		return nil
	}

	return schedule
}

func (m *BroadcastScheduler) RegisterEcho(echoServer *echo.Echo) {
	echoServer.POST("/broadcast_schedule/get_schedule", m.getScheduleHttpHandler)
}

func (m *BroadcastScheduler) getScheduleHttpHandler(c echo.Context) error {
	type Request struct {
		Id            int32  `json:"id"`
		MediaSlotId   int32  `json:"media_slot_id"`
		MediaSlotType int32  `json:"media_slot_type"`
		AppBundleId   string `json:"app_bundle_id"`
		DspSlotId     int32  `json:"dsp_slot_id"`
	}

	req := &Request{}
	if err := c.Bind(req); err != nil {
		return err
	}

	schedule := m.GetBroadcastSchedule(req.Id, req.MediaSlotId, req.MediaSlotType, req.AppBundleId, req.DspSlotId)
	if schedule == nil {
		return echo_helper.ErrorResponse(c, fmt.Errorf("schedule not found"))
	}

	return echo_helper.Response(c, schedule)
}
