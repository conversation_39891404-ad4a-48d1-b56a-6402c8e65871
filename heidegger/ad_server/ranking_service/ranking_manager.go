package ranking_service

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker"
	"gitlab.com/dev/heidegger/library/echo_helper"
)

var (
	ErrRankerNotFound = fmt.Errorf("ranker not found")
)

type RankingManager struct {
	rankers map[string]ranker.Ranker
	term    chan struct{}
}

func NewRankingManager() *RankingManager {
	return &RankingManager{
		rankers: make(map[string]ranker.Ranker),
		term:    make(chan struct{}),
	}
}

func (rm *RankingManager) Start() error {
	return nil
}

func (rm *RankingManager) Stop() {
	close(rm.term)
}

func (rm *RankingManager) StartRanker(config ranker.RankerConfig) error {
	r, err := CreateRanker(config)
	if err != nil {
		if err == ErrRankerNotFound {
			zap.L().Error("[StartRanker] ranker  not found", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", config.ModelName)))))
			return nil
		}
		return err
	}

	if err := r.Start(); err != nil {
		return err
	}

	if err := rm.RegisterRanker(config.ModelName, r); err != nil {
		return err
	}

	return nil
}

func (rm *RankingManager) RegisterRanker(name string, ranker ranker.Ranker) error {
	if _, exists := rm.rankers[name]; exists {
		return fmt.Errorf("ranker %s already exists", name)
	}
	rm.rankers[name] = ranker
	return nil
}

func (rm *RankingManager) GetRanker(name string) (ranker.Ranker, bool) {
	r, exists := rm.rankers[name]
	return r, exists
}

func (rm *RankingManager) Predict(request ranker.RankerRequest, result *ranker.RankerResult) error {
	r, exists := rm.GetRanker(request.Model)
	if !exists {
		return ErrRankerNotFound
	}

	return r.Predict(request, result)
}

type RankingManagerHttpHandler struct {
	manager *RankingManager
}

func NewRankingManagerHttpHandler(manager *RankingManager) *RankingManagerHttpHandler {
	return &RankingManagerHttpHandler{
		manager: manager,
	}
}

func (rmh *RankingManagerHttpHandler) RegisterEcho(e *echo.Echo) {
	e.POST("/ranking_manager/predict", rmh.Predict)
	e.POST("/ranking_manager/batch_predict", rmh.BatchPredict)
	e.Any("/ranking_manager/models", rmh.AllModels)
}

func (rmh *RankingManagerHttpHandler) Predict(c echo.Context) error {
	req := ranker.RankerRequest{}
	if err := c.Bind(&req); err != nil {
		return err
	}

	result := ranker.RankerResult{}
	err := rmh.manager.Predict(req, &result)
	if err != nil {
		return echo_helper.ErrorResponse(c, err)
	}

	return echo_helper.Response(c, result)
}

func (rmh *RankingManagerHttpHandler) BatchPredict(c echo.Context) error {
	reqs := make([]ranker.RankerRequest, 0)
	if err := c.Bind(&reqs); err != nil {
		return err
	}

	results := make([]ranker.RankerResult, 0, len(reqs))
	for _, req := range reqs {
		result := ranker.RankerResult{}
		err := rmh.manager.Predict(req, &result)
		if err != nil {
			return echo_helper.ErrorResponse(c, err)
		}
		results = append(results, result)
	}

	return echo_helper.Response(c, results)
}

func (rmh *RankingManagerHttpHandler) AllModels(c echo.Context) error {
	models := make([]ranker.RankerInfo, 0, len(rmh.manager.rankers))
	for _, model := range rmh.manager.rankers {
		models = append(models, model.GetRankerInfo())
	}

	return echo_helper.Response(c, models)
}
