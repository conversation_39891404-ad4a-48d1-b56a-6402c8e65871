package dsp_bidding_model

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/searcher/ranking_searcher/ranker"
	"gitlab.com/dev/heidegger/library/data_fetcher"
	"testing"
	"fmt"
)

func TestDspBiddingModel(t *testing.T) {
	dataFetcher, err := data_fetcher.CreateVersionedDataFetcher("./test_data/version.txt")
	if err != nil {
		t.Errorf("CreateVersionedDataFetcher failed, err:%v", err)
	}

	if err := dataFetcher.Start(); err != nil {
		t.Errorf("dataFetcher.Start failed, err:%v", err)
	}

	model := NewDspBiddingModel(dataFetcher)

	if err := model.Start(); err != nil {
		t.Errorf("model.Start failed, err:%v", err)
	}

	request := ranker.RankerRequest{
		MediaId:     100005,
		MediaSlotId: 110134,
		DspId:       1003,

		CpmPrice:      3000,
		CpcPrice:      0,
		CpaPrice:      0,
		BidFloor:      100,
		MinProfitRate: 0.4,
		MaxProfitRate: 1,
	}

	result := ranker.RankerResult{}

	if err := model.Predict(request, &result); err != nil {
		t.Errorf("model.Predict failed, err:%v", err)
	}

	zap.L().Info("result", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", &result)))))
}
