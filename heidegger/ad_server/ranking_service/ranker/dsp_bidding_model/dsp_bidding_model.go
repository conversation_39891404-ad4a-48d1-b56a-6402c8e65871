package dsp_bidding_model

import (
	"fmt"
	"go.uber.org/zap"
	"github.com/valyala/fastrand"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker"
	"gitlab.com/dev/heidegger/library/data_fetcher"
	"gitlab.com/dev/heidegger/library/star_tree"
	"gitlab.com/dev/heidegger/library/utils/math_utils"
	"io"
	"math"
	"strconv"
	"strings"
	"time"
)

var (
	ModelName = "dsp_bidding_model"
)

type BiddingPriceExploreItem struct {
}

type DspBiddingModel struct {
	dataFetcher data_fetcher.VersionedDataFetcher
	st          *star_tree.StarTree

	modelInfo ranker.RankerInfo

	term chan struct{}
}

func NewDspBiddingModel(dataFetcher data_fetcher.VersionedDataFetcher) *DspBiddingModel {
	return &DspBiddingModel{
		dataFetcher: dataFetcher,
		term:        make(chan struct{}),
	}
}

func (m *DspBiddingModel) GetModelName() string {
	return ModelName
}

func (p *DspBiddingModel) GetRankerInfo() ranker.RankerInfo {
	return p.modelInfo
}

func (m *DspBiddingModel) Start() error {
	if err := m.reloadModel(); err != nil {
		return err
	}

	zap.L().Info("[DspBiddingModel] Initial model loaded successfully")

	m.dataFetcher.RegisterObserver(600, m.observerCallback)

	return nil
}

func (m *DspBiddingModel) Stop() {
	m.dataFetcher.Stop()
	close(m.term)
}

func (m *DspBiddingModel) reloadModel() error {
	version, reader, err := m.dataFetcher.GetOriginDataHead()
	if err != nil {
		return fmt.Errorf("[DspBiddingModel] failed to get origin data: %v", err)
	}

	return m.reloadModelFromReader(version, reader)
}

func (m *DspBiddingModel) reloadModelFromReader(version string, reader io.ReadCloser) error {
	defer reader.Close()

	builder := star_tree.NewStBuilder()

	if strings.HasSuffix(version, "tar.gz") {
		if err := builder.LoadTarFromReader(reader, "bid_price_explore/bid_price_explore.txt"); err != nil {
			return fmt.Errorf("failed to load tar: %v", err)
		}
	} else {
		if err := builder.LoadCsvFromReader(reader); err != nil {
			return fmt.Errorf("failed to load CSV: %v", err)
		}
	}

	st, err := builder.CreateSt()
	if err != nil {
		return fmt.Errorf("failed to create StarTree: %v", err)
	}

	m.st = st

	m.modelInfo.Name = m.GetModelName()
	m.modelInfo.Version = version
	m.modelInfo.LastUpdateTime = time.Now()
	m.modelInfo.Detail = st.GetFieldNameList()

	zap.L().Info("[DspBiddingModel] reload model success")
	return nil
}

func (m *DspBiddingModel) observerCallback(version string, reader io.ReadCloser) error {
	return m.reloadModelFromReader(version, reader)
}

func (m *DspBiddingModel) Predict(request ranker.RankerRequest, result *ranker.RankerResult) error {
	if m.st == nil {
		return fmt.Errorf("model not ready")
	}

	query := star_tree.NewStarTreeQuery(m.st)
	query.
		QueryString(strconv.Itoa(int(request.MediaId))).
		QueryString(strconv.Itoa(int(request.MediaSlotId))).
		QueryString(strconv.Itoa(int(request.DspId))).
		QueryUint32(request.CpmPrice)

	zap.L().Info("[DspBiddingModel] query", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", query.GetFieldName())))))
	if query.GetFieldName() != "bprice" {
		return fmt.Errorf("invalid field name")
	}

	bidPriceMax := int32(request.CpmPrice)
	if 1-request.MinProfitRate != 0 {
		bidPriceMax = int32(float64(request.CpmPrice) * (1 - request.MinProfitRate))
	}

	bidPriceMin := int32(0)
	if 1-request.MaxProfitRate != 0 {
		bidPriceMin = int32(float64(request.CpmPrice) * (1 - request.MaxProfitRate))
	}

	bestProfit := int32(-1)
	bestBidPrice := bidPriceMax
	currentBidPrice := bidPriceMax
	step := m.getStep(bidPriceMax)

	for ; currentBidPrice > bidPriceMin; currentBidPrice -= step {
		querySnapshot := query.Clone()
		querySnapshot.QueryUint32(uint32(currentBidPrice))

		if querySnapshot.IsError() {
			return querySnapshot.Error()
		}

		if !querySnapshot.IsLeaf() {
			return fmt.Errorf("query not finished")
		}

		values := querySnapshot.Values()
		if len(values) != 7 {
			return fmt.Errorf("invalid values length")
		}

		bidCount := float64(values[0])
		impCount := float64(values[1])
		cost := float64(values[3])
		_ = float64(values[4])
		dspPrice := int32(values[5])
		bidPrice := int32(values[6])

		winRate := math_utils.SafeDiv(impCount, bidCount)
		costPrice := int32(math.Round(math_utils.SafeDiv(cost, impCount)))
		profit := int32(float64(int32(request.CpmPrice)-costPrice) * winRate * 1000)

		zap.L().Info("[DspBiddingModel] currentBid:, dspPrice:, bidPrice:, winRate:%f, costPrice:, profit", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(currentBidPrice)))), zap.Int64("price", int64(dspPrice)), zap.Int64("id", int64(bidPrice)), zap.Int64("param4", int64(winRate)), zap.Int64("price", int64(costPrice)), zap.Int64("param6", int64(profit)))

		if bidPrice > currentBidPrice || bidPrice < int32(float64(currentBidPrice)*0.9) {
			break
		}

		if profit > bestProfit {
			bestProfit = profit
			bestBidPrice = currentBidPrice
		}
	}

	zap.L().Info("[DspBiddingModel] bestProfit:, bestPrice", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(bestProfit)))), zap.Int64("id", int64(bestBidPrice)))

	if bestProfit == -1 {
		bestBidPrice = m.explore(
			bestBidPrice,
			bidPriceMin,
			bidPriceMax,
			0.1,
			0.2)
	} else {
		bestBidPrice = m.explore(
			bestBidPrice,
			bidPriceMin,
			bidPriceMax,
			0.1,
			0.1)
	}

	query.QueryUint32(uint32(bestBidPrice))

	if query.IsError() {
		return query.Error()
	}

	if !query.IsLeaf() {
		return fmt.Errorf("query not finished")
	}

	values := query.Values()
	if len(values) != 7 {
		return fmt.Errorf("invalid values length")
	}

	bidCount := float64(values[0])
	impCount := float64(values[1])
	clickCount := float64(values[2])
	cost := float64(values[3])
	_ = float64(values[4])

	ctr := math_utils.SafeDiv(clickCount, impCount)
	cvr := float64(0)
	winRate := math_utils.SafeDiv(impCount, bidCount)

	result.ECtr = ctr
	result.ECvr = cvr
	result.EWinRate = winRate
	result.ECostPrice = uint32(math.Round(math_utils.SafeDiv(cost, impCount)))
	result.EProfit = bestBidPrice - int32(result.ECostPrice)
	result.ECpm = request.CpmPrice
	result.ECpc = uint32(float64(result.ECpm) / result.ECtr / 1000)
	result.ECpa = 0
	result.SuggestedBidPrice = uint32(bestBidPrice)
	result.Model = m.GetModelName()

	return nil
}

func (m *DspBiddingModel) getStep(bidPrice int32) int32 {
	if bidPrice < 100 {
		return 10
	} else if bidPrice < 1000 {
		return 50
	} else if bidPrice < 10000 {
		return 100
	} else if bidPrice < 20000 {
		return 200
	} else {
		return 500
	}
}

func (m *DspBiddingModel) explore(
	bidPrice int32,
	bidPriceMin int32,
	bidPriceMax int32,
	exploreRange float64,
	exploreRate float64) int32 {

	if bidPrice < bidPriceMin {
		return bidPriceMin
	}

	if bidPrice > bidPriceMax {
		return bidPriceMax
	}

	if fastrand.Uint32()%100 < uint32(exploreRate*100) {
		return bidPrice
	}

	upperBound := int32(float64(bidPrice) * (1 + exploreRange))
	lowerBound := int32(float64(bidPrice) * (1 - exploreRange))

	if upperBound > bidPriceMax {
		upperBound = bidPriceMax
	}

	if lowerBound < bidPriceMin {
		lowerBound = bidPriceMin
	}

	if upperBound <= lowerBound {
		return upperBound
	}

	return lowerBound + int32(fastrand.Uint32n(uint32(upperBound)-uint32(lowerBound)))
}
