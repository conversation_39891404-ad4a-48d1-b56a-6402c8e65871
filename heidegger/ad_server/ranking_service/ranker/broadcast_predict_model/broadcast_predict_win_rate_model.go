package broadcast_predict_model

import (
	"fmt"
	"gitlab.com/dev/heidegger/library/utils/math_utils"
	"io"
	"math"
	"strconv"
	"strings"
	"time"

	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/data_fetcher"
	"gitlab.com/dev/heidegger/library/star_tree"
)

type BroadcastPredictWinRateModel struct {
	dataFetcher data_fetcher.VersionedDataFetcher
	st          *star_tree.StarTree

	modelInfo ranker.RankerInfo

	term chan struct{}
}

func NewBroadcastPredictWinRateModel(dataFetcher data_fetcher.VersionedDataFetcher) *BroadcastPredictWinRateModel {
	return &BroadcastPredictWinRateModel{
		dataFetcher: dataFetcher,
		term:        make(chan struct{}),
	}
}

func (m *BroadcastPredictWinRateModel) GetModelName() string {
	return "broadcast_predict_win_rate_model"
}

func (p *BroadcastPredictWinRateModel) GetRankerInfo() ranker.RankerInfo {
	return p.modelInfo
}

func (m *BroadcastPredictWinRateModel) Start() error {
	if err := m.reloadModel(); err != nil {
		return err
	}

	zap.L().Info("[BroadcastPredictWinRateModel] Initial model loaded successfully")

	m.dataFetcher.RegisterObserver(600, m.observerCallback)

	return nil
}

func (m *BroadcastPredictWinRateModel) Stop() {
	m.dataFetcher.Stop()
	close(m.term)
}

func (m *BroadcastPredictWinRateModel) observerCallback(version string, reader io.ReadCloser) error {
	return m.reloadModelFromReader(version, reader)
}

func (m *BroadcastPredictWinRateModel) reloadModel() error {
	version, reader, err := m.dataFetcher.GetOriginDataHead()
	if err != nil {
		return fmt.Errorf("[BroadcastPredictWinRateModel] failed to get origin data: %v", err)
	}

	return m.reloadModelFromReader(version, reader)
}

func (m *BroadcastPredictWinRateModel) reloadModelFromReader(version string, reader io.ReadCloser) error {
	defer reader.Close()

	builder := star_tree.NewStBuilder()

	if strings.HasSuffix(version, "tar.gz") {
		if err := builder.LoadTarFromReader(reader, "data.csv"); err != nil {
			return fmt.Errorf("[BroadcastPredictWinRateModel] failed to load tar: %v", err)
		}
	} else {
		if err := builder.LoadCsvFromReader(reader); err != nil {
			return fmt.Errorf("[BroadcastPredictWinRateModel] failed to load CSV: %v", err)
		}
	}

	st, err := builder.CreateSt()
	if err != nil {
		return fmt.Errorf("[BroadcastPredictWinRateModel] failed to create StarTree: %v", err)
	}

	m.st = st

	m.modelInfo.Name = m.GetModelName()
	m.modelInfo.Version = version
	m.modelInfo.LastUpdateTime = time.Now()
	m.modelInfo.Detail = st.GetFieldNameList()

	zap.L().Info("[BroadcastPredictWinRateModel] reload model success")
	return nil
}

func (m *BroadcastPredictWinRateModel) Predict(request ranker.RankerRequest, result *ranker.RankerResult) error {
	dspBidPriceConfidence, bidPriceRatioConfidence, err := m.doPredict(request, result)
	if err != nil {
		return err
	}

	if bidPriceRatioConfidence <= 0.2 || bidPriceRatioConfidence > 5 {
		request.BidPrice = request.CpmPrice * 100
		dspBidPriceConfidence, bidPriceRatioConfidence, err = m.doPredict(request, result)
		if err != nil {
			return err
		}
	}

	if dspBidPriceConfidence <= 0.2 {
		request.CpmPrice = 1000 * 100
		dspBidPriceConfidence, bidPriceRatioConfidence, err = m.doPredict(request, result)
		if err != nil {
			return err
		}
	}

	result.Id = request.Id

	return nil
}

func (m *BroadcastPredictWinRateModel) doPredict(request ranker.RankerRequest, result *ranker.RankerResult) (float64, float64, error) {
	if m.st == nil {
		return 0, 0, fmt.Errorf("model not ready")
	}

	bidPriceRatio := uint32(0)
	if request.CpmPrice != 0 {
		bidPriceRatio = uint32(float64(request.BidPrice) / float64(request.CpmPrice) * 1000)
	}

	query := star_tree.NewStarTreeQuery(m.st)
	query.
		QueryString(strconv.Itoa(int(request.MediaSlotType))).
		QueryString(strconv.Itoa(int(request.MediaId))).
		QueryString(strconv.Itoa(int(request.MediaSlotId))).
		QueryString(request.AppBundleId).
		QueryString(strconv.Itoa(int(request.DspId))).
		QueryString(strconv.Itoa(int(request.DspSlotId))).
		QueryUint32(request.CpmPrice).
		QueryUint32(bidPriceRatio)

	if query.IsError() {
		return 0, 0, query.Error()
	}

	values := query.Values()
	if len(values) != 7 {
		return 0, 0, fmt.Errorf("invalid values length")
	}

	resultBid := values[0]
	resultImp := values[1]
	resultClk := values[2]
	resultCost := values[3]
	resultCharge := values[4]
	resultDspBidPrice := values[5]
	resultBidPriceRatio := values[6]

	result.ECtr = math_utils.SafeDiv(float64(resultClk), float64(resultImp))
	result.EWinRate = math_utils.SafeDiv(float64(resultImp), float64(resultBid))
	result.ECostPrice = uint32(math.Ceil(math_utils.SafeDiv(float64(resultCost), float64(resultImp))))
	result.ECpm = uint32(math.Floor(math_utils.SafeDiv(float64(resultCharge), float64(resultImp))))
	result.EProfit = int32(result.ECpm) - int32(result.ECostPrice)

	dspBidPriceConfidence := float64(resultDspBidPrice) / float64(request.CpmPrice)
	bidPriceRatioConfidence := float64(resultBidPriceRatio) / float64(bidPriceRatio)

	result.Confidence = 1.0
	if resultDspBidPrice == 100000 {
		result.Confidence = 0.6

		if resultBidPriceRatio == 100000 {
			result.Confidence = 0.2
		}
	} else {
		if resultBidPriceRatio == 100000 {
			result.Confidence = 0.6
		}
	}

	//zap.L().Info("[BroadcastPredictWinRateModel] request:%s, values:%v, bidPriceRatio:, starNodeCount:%d, ddspBidPriceConfidence:%f, bidPriceRatioConfidence:%f", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(//	request.String())))), values, bidPriceRatio, query.GetStarNodeCount(),
	//	dspBidPriceConfidence, bidPriceRatioConfidence,
	//)

	return dspBidPriceConfidence, bidPriceRatioConfidence, nil
}
