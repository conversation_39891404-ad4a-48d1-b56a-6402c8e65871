package ranking_service

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker/broadcast_predict_model"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker/dsp_bidding_model"
	"gitlab.com/dev/heidegger/ad_server/ranking_service/ranker/st_cpa_model"
	"gitlab.com/dev/heidegger/library/data_fetcher"
	"fmt"
)

func CreateRanker(config ranker.RankerConfig) (ranker.Ranker, error) {
	switch config.ModelName {
	case ranker.ModelNameQihangStModel:
		return buildQihangStModel(config)
	case ranker.ModelNameDspBiddingModel:
		return buildDspBiddingModel(config)
	case ranker.ModelNameBroadcastCtrPredictModel:
		return buildBroadcastCtrPredictModel(config)
	case ranker.ModelNameBroadcastWinRatePredictModel:
		return buildBroadcastWinRatePredictModel(config)
	default:
		return nil, ErrRankerNotFound
	}
}

func buildQihangStModel(config ranker.RankerConfig) (*st_cpa_model.StCpaModel, error) {
	zap.L().Info("[buildQihangStModel] start cpa ranker, path", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", config.ModelData)))))
	dataFetcher, err := data_fetcher.CreateVersionedDataFetcher(config.ModelData)
	if err != nil {
		return nil, err
	}

	if err := dataFetcher.Start(); err != nil {
		return nil, err
	}

	stModel := st_cpa_model.NewStModel(dataFetcher)
	if err := stModel.Start(); err != nil {
		return nil, err
	}

	return stModel, nil
}

func buildDspBiddingModel(config ranker.RankerConfig) (*dsp_bidding_model.DspBiddingModel, error) {
	zap.L().Info("[buildDspBiddingModel] start dsp bidding ranker, path", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", config.ModelData)))))
	dataFetcher, err := data_fetcher.CreateVersionedDataFetcher(config.ModelData)
	if err != nil {
		return nil, err
	}

	if err := dataFetcher.Start(); err != nil {
		return nil, err
	}

	dspBiddingModel := dsp_bidding_model.NewDspBiddingModel(dataFetcher)
	if err := dspBiddingModel.Start(); err != nil {
		return nil, err
	}

	return dspBiddingModel, nil
}

func buildBroadcastCtrPredictModel(config ranker.RankerConfig) (*broadcast_predict_model.BroadcastPredictCtrModel, error) {
	zap.L().Info("[buildBroadcastCtrPredictModel] start broadcast ctr predict ranker, path", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", config.ModelData)))))
	dataFetcher, err := data_fetcher.CreateVersionedDataFetcher(config.ModelData)
	if err != nil {
		return nil, err
	}

	if err := dataFetcher.Start(); err != nil {
		return nil, err
	}

	broadcastCtrPredictModel := broadcast_predict_model.NewBroadcastPredictCtrModel(dataFetcher)
	if err := broadcastCtrPredictModel.Start(); err != nil {
		return nil, err
	}

	return broadcastCtrPredictModel, nil
}

func buildBroadcastWinRatePredictModel(config ranker.RankerConfig) (*broadcast_predict_model.BroadcastPredictWinRateModel, error) {
	zap.L().Info("[buildBroadcastWinRatePredictModel] start broadcast win rate predict ranker, path", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", config.ModelData)))))
	dataFetcher, err := data_fetcher.CreateVersionedDataFetcher(config.ModelData)
	if err != nil {
		return nil, err
	}

	if err := dataFetcher.Start(); err != nil {
		return nil, err
	}

	broadcastWinRatePredictModel := broadcast_predict_model.NewBroadcastPredictWinRateModel(dataFetcher)
	if err := broadcastWinRatePredictModel.Start(); err != nil {
		return nil, err
	}

	return broadcastWinRatePredictModel, nil
}
