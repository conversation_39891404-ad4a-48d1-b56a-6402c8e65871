package sdk_slot_config_service

import (
	"encoding/hex"
	"encoding/json"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"strings"
	"time"
	"fmt"
)

func (handler *SdkConfigHttpHandler) QuerySlotConfigV1(c echo.Context) error {
	mediaId, decodeBody, err := handler.verifyRequest(c)
	if err != nil {
		return c.String(400, "Bad Request")
	}
	var request SdkReqeust
	if err := json.Unmarshal(decodeBody, &request); err != nil {
		return c.String(400, "Bad Request, Unmarshal Error")
	}

	if utils.ID(mediaId) != request.MediaId {
		return c.String(400, "Bad Request, Mismatch mediaId")
	}

	sdkConfigRequest := &SdkConfigRequest{
		ClientIp:       c.RealIP(),
		MediaId:        utils.ID(request.MediaId),
		DeviceId:       request.DeviceId,
		DeviceIdType:   request.DeviceIdType,
		OsType:         entity.OsType(request.Os),
		OsVersion:      request.OsVersion,
		SdkVersion:     request.SdkVersion,
		AppPackageName: request.AppPackageName,
		AppVersion:     request.AppVersion,
	}

	response := &SdkResponse{
		Code:         0,
		Message:      "ok",
		MediaId:      request.MediaId,
		Encrypted:    true,
		ProcessTime:  time.Now().Unix(),
		CooldownTime: 60,
	}

	slotList, err := handler.service.GetMediaSlotList(request.MediaId)
	if err != nil {
		return handler.ErrorResponse(c, response, err)
	}

	result := make(map[string]interface{})

	if !strings.Contains(c.Path(), "third_config") {
		data, err := handler.service.QuerySlotConfigV1(sdkConfigRequest, slotList)
		if err != nil {
			return handler.ErrorResponse(c, response, err)
		}
		result["slot_config"] = data
	}
	data, err := handler.service.QueryThirdSdkSlotConfigV1(sdkConfigRequest, slotList)
	if err != nil {
		return handler.ErrorResponse(c, response, err)
	}
	result["third_config"] = data

	content, err := json.Marshal(result)
	if err != nil {
		return handler.ErrorResponse(c, response, err)
	}

	zap.L().Info("response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(content))))))
	encodeContent, err := handler.service.Encode(request.MediaId, content)
	response.Data = hex.EncodeToString(encodeContent)

	return handler.DoResponse(c, response)
}
