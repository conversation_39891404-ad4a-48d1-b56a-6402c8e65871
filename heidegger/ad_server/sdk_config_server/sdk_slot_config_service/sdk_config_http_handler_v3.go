package sdk_slot_config_service

import (
	"encoding/hex"
	"encoding/json"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/sdk_config_server/sdk_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"fmt"
)

type SdkPlatform struct {
	Platform utils.ID `json:"platform"`
	Name     string   `json:"name"`
	AppId    string   `json:"app_id"`
	AppKey   string   `json:"app_key"`
}

type SdkSlotInfoV3 struct {
	Id                     utils.ID                        `json:"id"`
	Name                   string                          `json:"name"`
	MediaId                utils.ID                        `json:"media_id"`
	MediaSlotKey           string                          `json:"media_slot_key"`
	SlotType               int                             `json:"slot_type"`
	CreativeTemplateIdList []int                           `json:"creative_template_id_list"`
	Timeout                int32                           `json:"timeout"`
	CostType               int                             `json:"cost_type"`
	CostPrice              int32                           `json:"cost_price"`
	BlockLand              []string                        `json:"block_land"`
	WhiteLand              []string                        `json:"white_land"`
	SdkConfig              *sdk_entity.SdkSlotConfigInfoV3 `json:"sdk_config"`
	ThirdSdkConfig         []*sdk_entity.ThirdSdkSlotInfo  `json:"third_sdk_configs"`
}

func (handler *SdkConfigHttpHandler) QuerySlotConfigV3(c echo.Context) error {
	zap.L().Info("request header", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", c.Request())))).Header)
	mediaId, decodeBody, err := handler.verifyRequest(c)
	if err != nil {
		return c.String(400, "Bad Request")
	}

	var request SdkReqeust
	if err := json.Unmarshal(decodeBody, &request); err != nil {
		zap.L().Error("Unmarshal Error", zap.Error(err), zap.String("param2", fmt.Sprintf("%v", string(decodeBody))))
		return c.String(401, "Bad Request, Unmarshal Error")
	}

	if utils.ID(mediaId) != request.MediaId {
		return c.String(402, "Bad Request, Mismatch mediaId")
	}

	sdkConfigRequest := &SdkConfigRequest{
		ClientIp:       c.RealIP(),
		MediaId:        request.MediaId,
		DeviceId:       request.DeviceId,
		DeviceIdType:   request.DeviceIdType,
		OsType:         entity.OsType(request.Os),
		OsVersion:      request.OsVersion,
		SdkVersion:     request.SdkVersion,
		AppPackageName: request.AppPackageName,
		AppVersion:     request.AppVersion,
	}

	response := &SdkResponse{
		Code:         0,
		Message:      "ok",
		MediaId:      request.MediaId,
		Encrypted:    true,
		ProcessTime:  time.Now().Unix(),
		CooldownTime: 60,
	}

	slotList, err := handler.service.GetMediaSlotList(request.MediaId)
	if err != nil {
		return handler.ErrorResponse(c, response, err)
	}

	result := make(map[string]interface{})

	slotConfigSet, err := handler.service.QuerySlotConfigV3(sdkConfigRequest, slotList)
	if err != nil {
		return handler.ErrorResponse(c, response, err)
	}

	thirdSlotConfigList, err := handler.service.QueryThirdSdkSlotConfigV3(sdkConfigRequest, slotList)
	if err != nil {
		return handler.ErrorResponse(c, response, err)
	}

	slotConfigData := make([]*SdkSlotInfoV3, 0)

	for _, slotItem := range slotList {
		ctlList := make([]int, 0)
		for _, ctl := range slotItem.CreativeTemplateIdList {
			ctlList = append(ctlList, int(ctl))
		}

		ssi := &SdkSlotInfoV3{
			Id:                     slotItem.Id,
			Name:                   slotItem.Name,
			MediaId:                slotItem.MediaId,
			MediaSlotKey:           slotItem.MediaSlotKey,
			SlotType:               int(slotItem.SlotType),
			CreativeTemplateIdList: ctlList,
			Timeout:                slotItem.Timeout,
			CostType:               int(slotItem.CostType),
			CostPrice:              slotItem.CostPrice,
			BlockLand:              slotItem.BlockLand,
			WhiteLand:              slotItem.WhiteLand,
		}
		if sdkConfig, ok := slotConfigSet[slotItem.Id]; ok {
			ssi.SdkConfig = sdkConfig
		}
		if thirdSdkConfig, ok := thirdSlotConfigList[slotItem.Id]; ok {
			ssi.ThirdSdkConfig = thirdSdkConfig
		}
		slotConfigData = append(slotConfigData, ssi)
	}

	result["slotList"] = slotConfigData

	platformList, err := handler.service.GetPlatformList()
	if err != nil {
		return handler.ErrorResponse(c, response, err)
	}
	pList := make([]*SdkPlatform, 0)
	for _, platformItem := range platformList {
		item := &SdkPlatform{
			Platform: platformItem.Id,
			Name:     platformItem.Name,
			AppId:    platformItem.AppId,
			AppKey:   platformItem.AppKey,
		}
		pList = append(pList, item)
	}
	result["platform"] = pList

	content, err := json.Marshal(result)
	if err != nil {
		return handler.ErrorResponse(c, response, err)
	}

	response.Encrypted = true
	zap.L().Info("response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", string(content))))))
	encodeContent, err := handler.service.Encode(request.MediaId, content)
	response.Data = hex.EncodeToString(encodeContent)

	return handler.DoResponse(c, response)
}
