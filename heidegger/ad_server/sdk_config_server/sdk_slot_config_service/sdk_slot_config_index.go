package sdk_slot_config_service

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/ad_server/sdk_config_server/sdk_entity/sdk_entity_loader"
	"gitlab.com/dev/heidegger/library/bitmap_index"
	"gitlab.com/dev/heidegger/library/utils"
	"strings"
	"time"
	"fmt"
)

const (
	FieldIdMediaId = bitmap_index.FieldId(iota)
	FieldIdMediaSlotId
	FieldIdWeekHour
	FieldIdGeoCode
	FieldIdOsType
	FieldIdDeviceId
	FieldIdOsVersion
	FieldIdSdkVersion
)

func fieldToId(field string) bitmap_index.FieldId {
	switch field {
	case "media_id":
		return FieldIdMediaId
	case "media_slot_id":
		return FieldIdMediaSlotId
	case "week_hour":
		return FieldIdWeekHour
	case "geo_code":
		return FieldIdGeoCode
	case "os_type":
		return FieldIdOsType
	case "device_id":
		return FieldIdDeviceId
	case "os_version":
		return FieldIdOsVersion
	case "sdk_version":
		return FieldIdSdkVersion
	default:
		return -1
	}
}

type SdkSlotConfigIndex struct {
	indexManager             *bitmap_index.IndexManager
	sdkSlotConfigIndexLoader sdk_entity_loader.SdkSlotConfigIndexLoader

	term chan struct{}
}

func NewSdkSlotConfigIndex(sdkSlotConfigIndexLoader sdk_entity_loader.SdkSlotConfigIndexLoader) *SdkSlotConfigIndex {
	return &SdkSlotConfigIndex{
		sdkSlotConfigIndexLoader: sdkSlotConfigIndexLoader,
		term:                     make(chan struct{}),
	}
}

func (s *SdkSlotConfigIndex) Start() error {
	if err := s.buildIndex(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-s.term:
				return
			case <-time.After(time.Second * 60):
				if err := s.buildIndex(); err != nil {
					zap.L().Error("[SdkSlotConfigIndex] buildIndex error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (s *SdkSlotConfigIndex) Stop() {
	close(s.term)
}

func (s *SdkSlotConfigIndex) buildIndex() error {
	indexList := s.sdkSlotConfigIndexLoader.GetList()
	indexManager := bitmap_index.NewIndexManager()

	indexManager.MustAddField(FieldIdMediaId, bitmap_index.FieldTypeInt)
	indexManager.MustAddField(FieldIdMediaSlotId, bitmap_index.FieldTypeInt)
	indexManager.MustAddField(FieldIdWeekHour, bitmap_index.FieldTypeInt)
	indexManager.MustAddField(FieldIdOsType, bitmap_index.FieldTypeInt)
	indexManager.MustAddField(FieldIdGeoCode, bitmap_index.FieldTypeInt)
	indexManager.MustAddField(FieldIdDeviceId, bitmap_index.FieldTypeString)
	indexManager.MustAddField(FieldIdOsVersion, bitmap_index.FieldTypeString)
	indexManager.MustAddField(FieldIdSdkVersion, bitmap_index.FieldTypeString)

	for _, item := range indexList {
		doc := bitmap_index.Doc{
			DocId: bitmap_index.Id(item.IndexId),
		}

		for field, value := range item.TargetStr {
			inclusive := true
			if strings.HasPrefix(field, "!") {
				inclusive = false
				field = strings.TrimPrefix(field, "!")
			}

			fieldId := fieldToId(field)
			if fieldId == -1 {
				zap.L().Warn("[SdkSlotConfigIndex] field: not found", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", field)))))
				continue
			}

			doc.AddStringTarget(fieldId, value, inclusive)
		}

		for field, value := range item.TargetInt {
			inclusive := true
			if strings.HasPrefix(field, "!") {
				inclusive = false
				field = strings.TrimPrefix(field, "!")
			}

			fieldId := fieldToId(field)
			if fieldId == -1 {
				zap.L().Warn("[SdkSlotConfigIndex] field: not found", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", field)))))
				continue
			}

			doc.AddIntTarget(fieldId, value, inclusive)
		}

		if err := indexManager.AddDoc(&doc); err != nil {
			zap.L().Warn("[SdkSlotConfigIndex] indexManager.AddDoc error: , indexId", zap.Error(err), zap.Int64("id", int64(item.IndexId)))
			return err
		}
	}

	if err := indexManager.BuildIndex(); err != nil {
		return err
	}

	s.indexManager = indexManager
	zap.L().Info("[SdkSlotConfigIndex] buildIndex success, doc count", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(len(indexList))))))
	return nil
}

func (s *SdkSlotConfigIndex) Query(request *SdkConfigRequest) (map[utils.ID]bool, error) {
	if s.indexManager == nil {
		return nil, err_code.ErrIndexNotReady
	}

	indexCtx := s.indexManager.CreateContext()
	defer indexCtx.Release()

	if _, err := s.indexManager.DoIndexInt64(indexCtx, FieldIdMediaId, int64(request.GetMediaId())); err != nil {
		return nil, err_code.ErrIndexFailed.Wrap(err)
	}

	if _, err := s.indexManager.DoIndexInt64(indexCtx, FieldIdMediaSlotId, int64(request.GetMediaSlotId())); err != nil {
		return nil, err_code.ErrIndexFailed.Wrap(err)
	}

	now := time.Now()
	weekHour := int(now.Weekday())*100 + now.Hour()
	if _, err := s.indexManager.DoIndexInt64(indexCtx, FieldIdWeekHour, int64(weekHour)); err != nil {
		return nil, err_code.ErrIndexFailed.Wrap(err)
	}

	geoCode := request.GetGeoCode()
	if _, err := s.indexManager.DoIndexInt64List(indexCtx, FieldIdGeoCode, []int64{
		int64(geoCode),
		int64(geoCode) / 1e6 * 1e6,
		int64(geoCode) / 1e9 * 1e9}); err != nil {
		return nil, err_code.ErrIndexFailed.Wrap(err)
	}

	if _, err := s.indexManager.DoIndexInt64(indexCtx, FieldIdOsType, int64(request.GetOsType())); err != nil {
		return nil, err_code.ErrIndexFailed.Wrap(err)
	}

	if _, err := s.indexManager.DoIndexString(indexCtx, FieldIdDeviceId, request.GetDeviceId()); err != nil {
		return nil, err_code.ErrIndexFailed.Wrap(err)
	}

	if _, err := s.indexManager.DoIndexString(indexCtx, FieldIdOsVersion, request.GetOsVersion()); err != nil {
		return nil, err_code.ErrIndexFailed.Wrap(err)
	}

	if _, err := s.indexManager.DoIndexString(indexCtx, FieldIdSdkVersion, request.GetSdkVersion()); err != nil {
		return nil, err_code.ErrIndexFailed.Wrap(err)
	}

	IndexIdList := indexCtx.GetDocs()

	result := make(map[utils.ID]bool)

	for _, id := range IndexIdList {
		result[utils.ID(id)] = true
	}

	return result, nil
}
