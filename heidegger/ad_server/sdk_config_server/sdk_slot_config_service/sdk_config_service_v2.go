package sdk_slot_config_service

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/sdk_config_server/sdk_entity"
	"gitlab.com/dev/heidegger/library/entity"
)

func (service *SdkConfigService) QuerySlotConfigV2(request *SdkConfigRequest, mediaSlot []*entity.MediaSlotInfo) ([]*sdk_entity.SdkSlotConfigInfoV2, error) {
	candidateMap, err := service.sdkSlotConfigIndex.Query(request)
	if err != nil {
		return nil, err
	}
	result := make([]*sdk_entity.SdkSlotConfigInfoV2, 0)

	for _, slotInfo := range mediaSlot {
		slotConfig := service.sdkSlotConfigLoader.GetById(slotInfo.Id)
		if slotConfig == nil {
			zap.L().Warn("[QuerySlotConfig] can not GetMediaSlot ", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slotInfo.Id)))))
			continue
		}

		item := &sdk_entity.SdkSlotConfigInfoV2{
			SlotId:           slotInfo.Id,
			SlotType:         int(slotInfo.SlotType),
			MediaSlotKey:     slotInfo.MediaSlotKey,
			BidType:          0, //默认cpm
			Timeout:          slotInfo.Timeout,
			Secure:           slotConfig.Secure,
			BidFloor:         getValueFromConfig(slotConfig.BidFloor, candidateMap),
			Countdown:        getValueFromConfig(slotConfig.Countdown, candidateMap),
			VideoSound:       getValueFromConfig(slotConfig.VideoSound, candidateMap),
			CloseCountdown:   getValueFromConfig(slotConfig.CloseCountdown, candidateMap),
			ReqFreq:          getValueFromConfig(slotConfig.ReqFreq, candidateMap),
			ImpFreq:          getValueFromConfig(slotConfig.ImpFreq, candidateMap),
			MinReqInterval:   getValueFromConfig(slotConfig.MinReqInterval, candidateMap),
			FullScreenClick:  getValueFromConfig(slotConfig.FullScreenClick, candidateMap),
			ShakeSensitivity: getValueFromConfig(slotConfig.Shake, candidateMap),
			MinSlideDistance: getValueFromConfig(slotConfig.Slide, candidateMap),
			ChargeType:       0, //默认一价
			DebugLog:         slotConfig.DebugLog,
		}
		for _, templateId := range slotInfo.CreativeTemplateIdList {
			tmpl := service.templateLoader.GetCreativeTemplateById(templateId)
			if tmpl != nil && tmpl.TemplateType > 0 {
				item.TemplateType = tmpl.TemplateType
			}
		}

		result = append(result, item)
	}

	return result, nil
}

func (service *SdkConfigService) QueryThirdSdkSlotConfigV2(request *SdkConfigRequest, mediaSlot []*entity.MediaSlotInfo) ([]map[string][]*sdk_entity.ThirdSdkSlotInfo, error) {
	result := make([]map[string][]*sdk_entity.ThirdSdkSlotInfo, 0)
	for _, slotInfo := range mediaSlot {
		slotItem := make(map[string][]*sdk_entity.ThirdSdkSlotInfo)
		slotItem[slotInfo.MediaSlotKey] = make([]*sdk_entity.ThirdSdkSlotInfo, 0)
		thirdList := service.thirdSdkSlotLoader.GetBySlotId(slotInfo.Id)
		for _, third := range thirdList {
			item := &sdk_entity.ThirdSdkSlotInfo{
				SlotId:         slotInfo.Id,
				ThirdSlotId:    third.ThirdSlotId,
				ThirdAppId:     third.ThirdAppId,
				MediaSlotKey:   slotInfo.MediaSlotKey,
				Platform:       third.Platform,
				SlotType:       third.ThirdSlotType,
				HeadBidding:    third.HeadBidding,
				IsBottom:       third.IsBottom,
				SortPrice:      third.SortPrice,
				ImpFreqByDay:   third.ImpFreqByDay,
				ImpFreqByHour:  third.ImpFreqByHour,
				MinImpInterval: third.MinReqInterval,
				ExtData:        third.ExtData,
			}

			slotConfig := service.sdkSlotConfigLoader.GetById(slotInfo.Id)
			if slotConfig != nil {
				item.DebugLog = slotConfig.DebugLog
			}

			slotItem[slotInfo.MediaSlotKey] = append(slotItem[slotInfo.MediaSlotKey], item)
		}
		result = append(result, slotItem)
	}
	return result, nil
}
