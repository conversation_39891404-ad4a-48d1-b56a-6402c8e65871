package sdk_slot_config_service

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/sdk_config_server/sdk_entity"
	"gitlab.com/dev/heidegger/library/entity"
)

func (service *SdkConfigService) QuerySlotConfigV1(request *SdkConfigRequest, mediaSlot []*entity.MediaSlotInfo) ([]*sdk_entity.SdkSlotConfigInfo, error) {
	candidateMap, err := service.sdkSlotConfigIndex.Query(request)
	if err != nil {
		return nil, err
	}
	result := make([]*sdk_entity.SdkSlotConfigInfo, 0)

	for _, slotInfo := range mediaSlot {
		slotConfig := service.sdkSlotConfigLoader.GetById(slotInfo.Id)
		if slotConfig == nil {
			zap.L().Warn("[QuerySlotConfig] can not GetMediaSlot ", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slotInfo.Id)))))
			continue
		}

		item := &sdk_entity.SdkSlotConfigInfo{
			SlotId:       slotInfo.Id,
			SlotType:     int(slotInfo.SlotType),
			MediaSlotKey: slotInfo.MediaSlotKey,
			//BidType:          0, //默认cpm
			Timeout: slotInfo.Timeout,
			//Secure:           slotConfig.Secure,
			//BidFloor:         getValueFromConfig(slotConfig.BidFloor, candidateMap),
			//Countdown:        getValueFromConfig(slotConfig.Countdown, candidateMap),
			//VideoSound:       getValueFromConfig(slotConfig.VideoSound, candidateMap),
			//CloseCountdown:   getValueFromConfig(slotConfig.CloseCountdown, candidateMap),
			ReqFreq:        getValueFromConfig(slotConfig.ReqFreq, candidateMap),
			ImpFreq:        getValueFromConfig(slotConfig.ImpFreq, candidateMap),
			MinReqInterval: getValueFromConfig(slotConfig.MinReqInterval, candidateMap),
			//FullScreenClick:  getValueFromConfig(slotConfig.FullScreenClick, candidateMap),
			//ShakeSensitivity: getValueFromConfig(slotConfig.Shake, candidateMap),
			//MinSlideDistance: getValueFromConfig(slotConfig.Slide, candidateMap),
			//TemplateType:     make([]int, 0),
		}
		//tidSet := make(map[int]int)
		//for _, templateId := range slotInfo.CreativeTemplateIdList {
		//	tmpl := service.templateLoader.GetCreativeTemplateById(templateId)
		//	if tmpl != nil {
		//		if _, ok := tidSet[tmpl.TemplateType]; !ok {
		//			item.TemplateType = append(item.TemplateType, tmpl.TemplateType)
		//			tidSet[tmpl.TemplateType] = 0
		//		}
		//	}
		//}

		result = append(result, item)
	}

	return result, nil
}

func (service *SdkConfigService) QueryThirdSdkSlotConfigV1(request *SdkConfigRequest, mediaSlot []*entity.MediaSlotInfo) ([]*sdk_entity.ThirdSdkSlotInfo, error) {
	result := make([]*sdk_entity.ThirdSdkSlotInfo, 0)
	for _, slotInfo := range mediaSlot {
		thirdList := service.thirdSdkSlotLoader.GetBySlotId(slotInfo.Id)
		for _, third := range thirdList {
			item := &sdk_entity.ThirdSdkSlotInfo{
				SlotId:         slotInfo.Id,
				ThirdSlotId:    third.ThirdSlotId,
				ThirdAppId:     third.ThirdAppId,
				MediaSlotKey:   slotInfo.MediaSlotKey,
				Platform:       third.Platform,
				SlotType:       third.ThirdSlotType,
				HeadBidding:    third.HeadBidding,
				IsBottom:       third.IsBottom,
				SortPrice:      third.SortPrice,
				ImpFreqByDay:   third.ImpFreqByDay,
				ImpFreqByHour:  third.ImpFreqByHour,
				MinImpInterval: third.MinReqInterval,
				ExtData:        third.ExtData,
			}
			result = append(result, item)
		}
	}
	return result, nil
}
