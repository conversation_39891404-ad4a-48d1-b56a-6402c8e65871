package sdk_slot_config_service

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/sdk_config_server/sdk_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
)

func (service *SdkConfigService) QuerySlotConfigV3(request *SdkConfigRequest, mediaSlot []*entity.MediaSlotInfo) (map[utils.ID]*sdk_entity.SdkSlotConfigInfoV3, error) {
	candidateMap, err := service.sdkSlotConfigIndex.Query(request)
	if err != nil {
		return nil, err
	}
	result := make(map[utils.ID]*sdk_entity.SdkSlotConfigInfoV3)

	for _, slotInfo := range mediaSlot {
		slotConfig := service.sdkSlotConfigLoader.GetById(slotInfo.Id)
		if slotConfig == nil {
			zap.L().Warn("[QuerySlotConfig] can not GetMediaSlot ", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(slotInfo.Id)))))
			continue
		}

		item := &sdk_entity.SdkSlotConfigInfoV3{
			SlotId:           slotInfo.Id,
			Secure:           slotConfig.Secure,
			BidType:          0, //默认cpm
			BidFloor:         getValueFromConfig(slotConfig.BidFloor, candidateMap),
			Countdown:        getValueFromConfig(slotConfig.Countdown, candidateMap),
			VideoSound:       getValueFromConfig(slotConfig.VideoSound, candidateMap),
			CloseCountdown:   getValueFromConfig(slotConfig.CloseCountdown, candidateMap),
			ReqFreq:          getValueFromConfig(slotConfig.ReqFreq, candidateMap),
			ImpFreq:          getValueFromConfig(slotConfig.ImpFreq, candidateMap),
			MinReqInterval:   getValueFromConfig(slotConfig.MinReqInterval, candidateMap),
			ShakeSensitivity: getValueFromConfig(slotConfig.Shake, candidateMap),
			MinSlideDistance: getValueFromConfig(slotConfig.Slide, candidateMap),
			FullScreenClick:  getValueFromConfig(slotConfig.FullScreenClick, candidateMap),
			ChargeType:       0, //默认一价
		}
		for _, templateId := range slotInfo.CreativeTemplateIdList {
			tmpl := service.templateLoader.GetCreativeTemplateById(templateId)
			if tmpl != nil && tmpl.TemplateType > 0 {
				item.TemplateType = tmpl.TemplateType
			}
		}
		result[item.SlotId] = item
	}

	return result, nil
}

func (service *SdkConfigService) QueryThirdSdkSlotConfigV3(request *SdkConfigRequest, mediaSlot []*entity.MediaSlotInfo) (map[utils.ID][]*sdk_entity.ThirdSdkSlotInfo, error) {
	result := make(map[utils.ID][]*sdk_entity.ThirdSdkSlotInfo)
	for _, slotInfo := range mediaSlot {
		configList := make([]*sdk_entity.ThirdSdkSlotInfo, 0)
		thirdList := service.thirdSdkSlotLoader.GetBySlotId(slotInfo.Id)
		for _, third := range thirdList {
			item := &sdk_entity.ThirdSdkSlotInfo{
				Name:           third.Name,
				SlotId:         slotInfo.Id,
				ThirdSlotId:    third.ThirdSlotId,
				ThirdAppId:     third.ThirdAppId,
				MediaSlotKey:   slotInfo.MediaSlotKey,
				Platform:       third.Platform,
				SlotType:       third.ThirdSlotType,
				HeadBidding:    third.HeadBidding,
				IsBottom:       third.IsBottom,
				SortPrice:      third.SortPrice,
				ImpFreqByDay:   third.ImpFreqByDay,
				ImpFreqByHour:  third.ImpFreqByHour,
				MinImpInterval: third.MinReqInterval,
				ExtData:        third.ExtData,
			}
			configList = append(configList, item)

		}
		result[slotInfo.Id] = configList
	}
	return result, nil
}
