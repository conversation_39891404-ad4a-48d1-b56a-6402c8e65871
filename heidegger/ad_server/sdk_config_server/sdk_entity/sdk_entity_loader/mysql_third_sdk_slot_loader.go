package sdk_entity_loader

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/sdk_config_server/sdk_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
	"fmt"
)

type MysqlThirdSdkSlotDo struct {
	Id             int32  `xorm:"id"`
	MediaId        int32  `xorm:"media_id"`
	Name           string `xorm:"name"`
	ThirdAppId     string `xorm:"third_app_id"`
	Platform       int    `xorm:"platform"`
	ThirdSlotId    string `xorm:"third_slot_id"`
	SlotId         int64  `xorm:"slot_id"`
	ThirdSlotType  int    `xorm:"third_slot_type"`
	HeadBidding    bool   `xorm:"head_bidding"`
	IsBottom       bool   `xorm:"is_bottom"`
	BidFloor       int    `xorm:"bid_floor"`
	SortPrice      int    `xorm:"sort_price"`
	ImpFreqByDay   int    `xorm:"imp_freq_by_day"`
	ImpFreqByHour  int    `xorm:"imp_freq_by_hour"`
	MinReqInterval int    `xorm:"min_imp_interval"`
	ExtData        string `xorm:"ext_data"`
}

func (do *MysqlThirdSdkSlotDo) ToEntity() (*sdk_entity.ThirdSdkSlot, error) {
	result := &sdk_entity.ThirdSdkSlot{
		Id:             utils.ID(do.Id),
		MediaId:        do.MediaId,
		Name:           do.Name,
		ThirdAppId:     do.ThirdAppId,
		Platform:       do.Platform,
		ThirdSlotId:    do.ThirdSlotId,
		SlotId:         utils.ID(do.SlotId),
		ThirdSlotType:  do.ThirdSlotType,
		HeadBidding:    do.HeadBidding,
		IsBottom:       do.IsBottom,
		BidFloor:       do.BidFloor,
		SortPrice:      do.SortPrice,
		ImpFreqByDay:   do.ImpFreqByDay,
		ImpFreqByHour:  do.ImpFreqByHour,
		MinReqInterval: do.MinReqInterval,
		ExtData:        do.ExtData,
	}

	return result, nil
}

type MysqlThirdSdkSlotLoader struct {
	engine *xorm.Engine

	itemList    sdk_entity.ThirdSdkSlotList
	itemMap     map[utils.ID]*sdk_entity.ThirdSdkSlot
	slotListMap map[utils.ID][]*sdk_entity.ThirdSdkSlot
	intervalSec int
	term        chan struct{}
}

func NewMysqlThirdSdkSlotLoader(engine *xorm.Engine, intervalSec int) *MysqlThirdSdkSlotLoader {
	return &MysqlThirdSdkSlotLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlThirdSdkSlotLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlThirdSdkSlotLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlThirdSdkSlotLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlThirdSdkSlotLoader) GetList() sdk_entity.ThirdSdkSlotList {
	return loader.itemList
}
func (loader *MysqlThirdSdkSlotLoader) GetById(id utils.ID) *sdk_entity.ThirdSdkSlot {
	return loader.itemMap[id]
}

func (loader *MysqlThirdSdkSlotLoader) GetBySlotId(slotId utils.ID) []*sdk_entity.ThirdSdkSlot {
	return loader.slotListMap[slotId]
}

func (loader *MysqlThirdSdkSlotLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlThirdSdkSlotDo, 0)
	if err := session.Table("third_sdk_slot").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	itemList := make(sdk_entity.ThirdSdkSlotList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		itemList = append(itemList, item)
	}
	zap.L().Info("load sdk_slot_index", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", len(itemList))))))

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()
	loader.slotListMap = loader.itemList.ToSlotIdMap()

	return nil
}
