package sdk_entity_loader

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/sdk_config_server/sdk_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"strconv"
	"time"
	"xorm.io/xorm"
)

type PlatformExtData struct {
	AppId  string `json:"appId"`
	AppKey string `json:"appKey"`
}

type MysqlPlatformDo struct {
	Id      int32  `xorm:"id"`
	Title   string `xorm:"title"`
	Value   string `xorm:"value"`
	Parent  int64  `xorm:"parent"`
	ExtData string `xorm:"ext_data"`
}

func (do *MysqlPlatformDo) ToEntity() (*sdk_entity.Platform, error) {
	ext := &PlatformExtData{}
	if len(do.ExtData) > 0 {
		if err := json.Unmarshal([]byte(do.ExtData), ext); err != nil {
			return nil, fmt.Errorf("unmarshal platform ext data err: %v", err)
		}
	}

	id, err := strconv.ParseInt(do.Value, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("parse platform value err: %v", err)
	}
	result := &sdk_entity.Platform{
		Id:     utils.ID(id),
		Name:   do.Title,
		AppId:  ext.AppId,
		AppKey: ext.AppKey,
	}

	return result, nil
}

type MysqlPlatformLoader struct {
	engine *xorm.Engine

	itemList sdk_entity.PlatformList
	itemMap  map[utils.ID]*sdk_entity.Platform

	intervalSec int
	term        chan struct{}
}

func NewMysqlPlatformLoader(engine *xorm.Engine, intervalSec int) *MysqlPlatformLoader {
	return &MysqlPlatformLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlPlatformLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlPlatformLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlPlatformLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlPlatformLoader) GetPlatformList() sdk_entity.PlatformList {
	return loader.itemList
}

func (loader *MysqlPlatformLoader) GetPlatformById(id utils.ID) *sdk_entity.Platform {
	return loader.itemMap[id]
}

func (loader *MysqlPlatformLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	parentList := make([]*MysqlPlatformDo, 0)
	if err := session.Table("ad_enums").
		Where("title =?", "platform").
		Where("parent = ?", 0).Find(&parentList); err != nil {
		return err
	}

	pIdList := make([]int32, 0)
	for _, item := range parentList {
		pIdList = append(pIdList, item.Id)
	}
	doList := make([]*MysqlPlatformDo, 0)
	if err := session.Table("ad_enums").
		In("parent", pIdList).Find(&doList); err != nil {
		return err
	}
	itemList := make(sdk_entity.PlatformList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			zap.L().Error("Platform ToEntity err", zap.String("do", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", do)))), zap.Error(err))
			return err
		}

		itemList = append(itemList, item)
	}

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}
