package sdk_entity_loader

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/sdk_config_server/sdk_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

type MysqlSdkSlotIndexDo struct {
	Id        int32  `xorm:"id"`
	MediaId   int32  `xorm:"media_id"`
	TargetStr string `xorm:"target_str"`
	TargetInt string `xorm:"target_int"`
}

func (do *MysqlSdkSlotIndexDo) ToEntity() (*sdk_entity.SdkSlotConfigIndex, error) {
	result := &sdk_entity.SdkSlotConfigIndex{IndexId: utils.ID(do.Id)}
	if len(do.TargetStr) != 0 {
		if err := json.Unmarshal([]byte(do.TargetStr), &result.TargetStr); err != nil {
			return nil, fmt.Errorf("[MysqlSdkSlotIndexDo] json.Unmarshal error: %v, id: %d", err, do.Id)
		}
	}

	if len(do.TargetInt) != 0 {
		if err := json.Unmarshal([]byte(do.TargetInt), &result.TargetInt); err != nil {
			return nil, fmt.Errorf("[MysqlSdkSlotIndexDo] json.Unmarshal error: %v, id: %d", err, do.Id)
		}
	}
	result.TargetInt["media_id"] = []int64{int64(do.MediaId)} // 添加媒体定向
	return result, nil
}

type MysqlSdkSlotIndexLoader struct {
	engine *xorm.Engine

	itemList sdk_entity.SdkSlotConfigIndexList
	itemMap  map[utils.ID]*sdk_entity.SdkSlotConfigIndex

	intervalSec int
	term        chan struct{}
}

func NewMysqlSdkSlotIndexLoader(engine *xorm.Engine, intervalSec int) *MysqlSdkSlotIndexLoader {
	return &MysqlSdkSlotIndexLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlSdkSlotIndexLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlSdkSlotIndexLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlSdkSlotIndexLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlSdkSlotIndexLoader) GetList() sdk_entity.SdkSlotConfigIndexList {
	return loader.itemList
}
func (loader *MysqlSdkSlotIndexLoader) GetById(id utils.ID) *sdk_entity.SdkSlotConfigIndex {
	return loader.itemMap[id]
}

func (loader *MysqlSdkSlotIndexLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlSdkSlotIndexDo, 0)
	if err := session.Table("sdk_slot_index").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	itemList := make(sdk_entity.SdkSlotConfigIndexList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		itemList = append(itemList, item)
	}
	zap.L().Info("load sdk_slot_index", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", len(itemList))))))

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()

	return nil
}
