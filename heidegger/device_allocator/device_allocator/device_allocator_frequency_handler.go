package device_allocator

import (
	"fmt"
	"go.uber.org/zap"
)

type FrequencyInterface interface {
	BatchQueryLegacy(queryMap map[string]uint32) (map[string]uint32, error)
}

type DeviceAllocatorFrequencyHandler struct {
	frequency FrequencyInterface
}

func NewDeviceAllocatorFrequencyHandler(frequency FrequencyInterface) *DeviceAllocatorFrequencyHandler {
	return &DeviceAllocatorFrequencyHandler{
		frequency: frequency,
	}
}

func (handler *DeviceAllocatorFrequencyHandler) Handle(config DevicePoolConfig, deviceItemList []DeviceItem) ([]DeviceItem, error) {
	if config.MaxFetchCount == 0 {
		return deviceItemList, nil
	}

	result := make([]DeviceItem, 0, len(deviceItemList))

	for len(deviceItemList) != 0 {
		batchSize := 500
		if len(deviceItemList) < 500 {
			batchSize = len(deviceItemList)
		}

		batch := deviceItemList[:batchSize]
		deviceItemList = deviceItemList[batchSize:]

		queryMap := make(map[string]uint32)
		for _, deviceItem := range batch {
			deviceId := deviceItem.GetDeviceId()
			freqKey := fmt.Sprintf(config.FrequencyFormat, deviceId)
			queryMap[freqKey] = uint32(config.FrequencyLength)
		}

		freqMap, err := handler.frequency.BatchQueryLegacy(queryMap)
		if err != nil {
			zap.L().Error("[DeviceAllocatorFrequencyHandler] BatchQuery failed", zap.Error(err))
			// put all batch back
			result = append(result, batch...)
			continue
		}

		for _, deviceItem := range batch {
			deviceId := deviceItem.GetDeviceId()
			freqKey := fmt.Sprintf(config.FrequencyFormat, deviceId)
			freq, ok := freqMap[freqKey]
			if !ok {
				zap.L().Error("[DeviceAllocatorFrequencyHandler] freq not found", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", freqKey)))))
				result = append(result, deviceItem)
				continue
			}

			if freq >= uint32(config.MaxFetchCount) {
				continue
			}
			deviceItem.FetchCount = uint16(freq)
			result = append(result, deviceItem)
		}
	}

	return result, nil
}
