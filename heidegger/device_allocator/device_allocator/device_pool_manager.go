package device_allocator

import (
	"go.uber.org/zap"
	"path/filepath"
	"sync"
	"time"
	"fmt"
)

type DevicePoolManagerItem struct {
	worker       *DevicePoolWorker
	preprocessor *DevicePoolPreprocessor
}

type DevicePoolManagerStatus struct {
	IsRunning bool                                   `json:"is_running"`
	Name      string                                 `json:"name"`
	Config    DevicePoolConfig                       `json:"config"`
	Status    map[string]DevicePoolManagerItemStatus `json:"status"`
}

type DevicePoolManagerItemStatus struct {
	WorkerStatus       DevicePoolWorkerStatus       `json:"worker_status"`
	PreprocessorStatus DevicePoolPreprocessorStatus `json:"preprocessor_status"`
}

type DevicePoolManager struct {
	rootDir string

	config *DevicePoolConfig

	keyMap     map[string]*DevicePoolManagerItem
	keyMapLock sync.Mutex

	workerMap map[string]*DevicePoolWorker

	deviceItemPreprocessHandler DeviceItemConfigPreprocessHandler

	isRunning bool
	term      chan struct{}
}

func NewDevicePoolManager(rootDir string) *DevicePoolManager {
	return &DevicePoolManager{
		rootDir: rootDir,

		keyMap: make(map[string]*DevicePoolManagerItem),
		deviceItemPreprocessHandler: func(config DevicePoolConfig, deviceItemList []DeviceItem) ([]DeviceItem, error) {
			return deviceItemList, nil
		},

		term: make(chan struct{}),
	}
}

func (manager *DevicePoolManager) GetName() string {
	return manager.config.Name
}

func (manager *DevicePoolManager) GetKeyType() KeyType {
	return manager.config.KeyType
}

func (manager *DevicePoolManager) SetDeviceItemPreprocessHandler(handler DeviceItemConfigPreprocessHandler) {
	manager.deviceItemPreprocessHandler = handler
}

func (manager *DevicePoolManager) Start() error {
	if err := manager.loadConfig(); err != nil {
		return err
	}

	if err := manager.startWorker(); err != nil {
		return err
	}

	manager.publishWorker()

	go manager.loop()

	return nil
}

func (manager *DevicePoolManager) Stop() error {
	close(manager.term)
	zap.L().Info("[DevicePoolManager] stop device pool manager, name", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", manager.GetName())))))

	manager.keyMapLock.Lock()
	defer manager.keyMapLock.Unlock()

	zap.L().Info("[DevicePoolManager] stop workers, name", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", manager.GetName())))))
	for _, item := range manager.keyMap {
		item.worker.Stop()
	}

	for _, item := range manager.keyMap {
		item.worker.Wait()
	}

	zap.L().Info("[DevicePoolManager] wait 5 sec")
	time.Sleep(time.Second * 5)

	zap.L().Info("[DevicePoolManager] stop preprocessor, name", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", manager.GetName())))))
	for _, item := range manager.keyMap {
		item.preprocessor.Stop()
	}

	for _, item := range manager.keyMap {
		item.preprocessor.Wait()
	}

	zap.L().Info("[DevicePoolManager] wait 5 sec")
	time.Sleep(time.Second * 5)

	zap.L().Info("[DevicePoolManager] stop done, name", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", manager.GetName())))))

	return nil
}

func (manager *DevicePoolManager) loadConfig() error {
	configPath := filepath.Join(manager.rootDir, "config.json")
	config := NewDevicePoolConfig()
	if err := config.ReadFile(configPath); err != nil {
		return err
	}

	manager.config = config
	return nil
}

func (manager *DevicePoolManager) GetPreprocessRoot() string {
	return filepath.Join(manager.rootDir, "preprocess")
}

func (manager *DevicePoolManager) GetDevicePoolRoot() string {
	return filepath.Join(manager.rootDir, "device_pool")
}

func (manager *DevicePoolManager) startWorker() error {
	// get all device pool dirs
	dirs, err := getDevicePoolDirs(manager.GetPreprocessRoot())
	if err != nil {
		return err
	}

	manager.keyMapLock.Lock()
	defer manager.keyMapLock.Unlock()

	// start worker for each dir
	for _, dir := range dirs {
		key := GetDevicePoolKeyFromDirName(dir)
		if key == "unknown" {
			return ErrDevicePoolBadKey
		}

		if _, ok := manager.keyMap[key]; ok {
			continue
		}

		preprocessor := NewDevicePoolPreprocess(
			key,
			manager.GetPreprocessRoot()+"/"+dir,
			manager.GetDevicePoolRoot()+"/"+dir,
			2,
			10000,
		)
		preprocessor.SetDeviceItemPreprocessHandler(func(deviceItemList []DeviceItem) ([]DeviceItem, error) {
			zap.L().Info("[DevicePoolManager] start device item preprocess, name:%s, key:%s, input", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(manager.GetName())))), key, len(deviceItemList))
			config := *manager.config
			result, err := manager.deviceItemPreprocessHandler(config, deviceItemList)
			if err != nil {
				return nil, err
			}

			zap.L().Info("[DevicePoolManager] device item preprocess done, name:%s, key:%s, input:, output:%d", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(manager.GetName())))), key, len(deviceItemList), len(result))
			return result, err
		})
		if err := preprocessor.Start(); err != nil {
			return err
		}

		worker := NewDevicePoolWorker(manager.GetDevicePoolRoot() + "/" + dir)
		worker.SetDepletedPoolCallback(preprocessor.SendDepletedDevicePool)
		if err := worker.Start(); err != nil {
			return err
		}

		manager.keyMap[key] = &DevicePoolManagerItem{
			worker:       worker,
			preprocessor: preprocessor,
		}
	}

	return nil
}

func (manager *DevicePoolManager) loop() {
	manager.isRunning = true
	defer func() {
		manager.isRunning = false
	}()

	keyScanTicker := time.NewTicker(time.Minute)
	defer keyScanTicker.Stop()

	for {
		select {
		case <-keyScanTicker.C:
			if err := manager.startWorker(); err != nil {
				zap.L().Error("[DevicePoolManager] start worker failed", zap.Error(err))
			}

			manager.publishWorker()
		case <-manager.term:
			return
		}
	}
}

func (manager *DevicePoolManager) publishWorker() {
	manager.keyMapLock.Lock()
	defer manager.keyMapLock.Unlock()

	workerMap := make(map[string]*DevicePoolWorker)
	for key, item := range manager.keyMap {
		workerMap[key] = item.worker
	}

	manager.workerMap = workerMap
}

func (manager *DevicePoolManager) GetPublishedWorker(key string) *DevicePoolWorker {
	if worker, ok := manager.workerMap[key]; ok {
		return worker
	}

	return nil
}

func (manager *DevicePoolManager) Status() DevicePoolManagerStatus {
	status := DevicePoolManagerStatus{
		IsRunning: manager.isRunning,
		Name:      manager.config.Name,
		Config:    *manager.config,
		Status:    map[string]DevicePoolManagerItemStatus{},
	}

	manager.keyMapLock.Lock()
	defer manager.keyMapLock.Unlock()

	for key, item := range manager.keyMap {
		itemStatus := DevicePoolManagerItemStatus{
			WorkerStatus:       item.worker.Status(),
			PreprocessorStatus: item.preprocessor.Status(),
		}

		status.Status[key] = itemStatus
	}

	return status
}
