package device_allocator

import (
	"go.uber.org/zap"
	"testing"
	"time"
	"fmt"
)

func TestNewDevicePoolManager(t *testing.T) {
	// logrus.SetLevel converted - configure zap logger instead

	manager := NewDevicePoolManager(
		"./../legacy_device_allocation_data/test_data/jojo/",
	)

	if err := manager.Start(); err != nil {
		t.Fatal(err)
	}

	for i := 0; i < 60; i++ {
		worker := manager.GetPublishedWorker("3")
		if worker == nil {
			zap.L().Info("worker is nil")
		} else {
			deviceItem, err := worker.GetDeviceItem()
			if err != nil {
				zap.L().Error("get device item failed", zap.Error(err))
			} else {
				zap.L().Info("device item", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", deviceItem)))))
			}
		}

		time.Sleep(time.Second)
	}

	if err := manager.Stop(); err != nil {
		t.Fatal(err)
	}
}
