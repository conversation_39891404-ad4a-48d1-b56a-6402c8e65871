package device_allocator

import (
	"go.uber.org/zap"
	"os"
	"sync"
	"time"
	"fmt"
)

type DeviceItemPreprocessHandler func(deviceItemList []DeviceItem) ([]DeviceItem, error)

type DevicePoolPreprocessorStatus struct {
	IsRunning            bool `json:"is_running"`
	ReadyFiles           int  `json:"ready_files"`
	ReadyDeviceCount     int  `json:"ready_device_count"`
	CandidateFiles       int  `json:"candidate_files"`
	CandidateDeviceCount int  `json:"candidate_device_count"`
	ProcessedDeviceCount int  `json:"processed_device_count"`
}

type DevicePoolPreprocessor struct {
	key           string
	preprocessDir string
	outputDir     string

	maxCandidate int
	splitSize    int

	deviceItemPreprocessHandler DeviceItemPreprocessHandler

	processingDevicePool  *DevicePool
	processedDeviceItem   []DeviceItem
	unprocessedDeviceItem []DeviceItem

	depletedPoolChan chan *DevicePool

	isRunning bool
	term      chan struct{}
	wg        sync.WaitGroup
}

func NewDevicePoolPreprocess(
	key string,
	preprocessDir string,
	outputDir string,
	maxCandidate int,
	splitSize int) *DevicePoolPreprocessor {
	return &DevicePoolPreprocessor{
		key:           key,
		preprocessDir: preprocessDir,
		outputDir:     outputDir,
		maxCandidate:  maxCandidate,
		splitSize:     splitSize,

		deviceItemPreprocessHandler: func(deviceItemList []DeviceItem) ([]DeviceItem, error) {
			return deviceItemList, nil
		},

		depletedPoolChan: make(chan *DevicePool, 4),
		term:             make(chan struct{}),
	}
}

func (p *DevicePoolPreprocessor) SetDeviceItemPreprocessHandler(handler DeviceItemPreprocessHandler) {
	p.deviceItemPreprocessHandler = handler
}

func (p *DevicePoolPreprocessor) Start() error {
	if err := os.MkdirAll(p.outputDir, 0755); err != nil {
		return err
	}

	p.wg.Add(1)
	go p.loop()
	return nil
}

func (p *DevicePoolPreprocessor) Stop() {
	close(p.term)
}

func (p *DevicePoolPreprocessor) Wait() {
	p.wg.Wait()
}

func (p *DevicePoolPreprocessor) loop() {
	p.isRunning = true
	defer func() {
		p.wg.Done()
		p.isRunning = false
	}()

	zap.L().Info("[DevicePoolPreprocessor] start to preprocess device pool data, preprocess dir: , output dir", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", p.preprocessDir)))), zap.String("param2", fmt.Sprintf("%v", p.outputDir)))
	processTimer := time.NewTimer(0)

	for {
		select {
		case <-p.term:
			if err := p.writeProcessedDeviceItem(p.processedDeviceItem); err != nil {
				zap.L().Error("[DevicePoolPreprocessor] write processed device item failed", zap.Error(err))
			}
			return
		case depletedPool := <-p.depletedPoolChan:
			if err := p.writeDepletedPool(depletedPool); err != nil {
				zap.L().Error("[DevicePoolPreprocessor] write processed device item failed", zap.Error(err))
			}
		case <-processTimer.C:
			if err := p.Process(); err != nil {
				zap.L().Error("[DevicePoolPreprocessor] process failed", zap.Error(err))
			}

			processTimer.Reset(time.Minute)
		}
	}
}

func (p *DevicePoolPreprocessor) IsDepleted() bool {
	candidate, err := getDevicePoolCandidatePath(p.preprocessDir)
	if err != nil {
		zap.L().Error("[DevicePoolPreprocessor][IsDepleted] failed, path:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", p.preprocessDir)))), zap.String("param2", fmt.Sprintf("%v", err)))
		return true
	}

	if len(candidate) != 0 {
		return false
	}

	return p.processingDevicePool == nil || p.processingDevicePool.IsDepleted()
}

func (p *DevicePoolPreprocessor) SendDepletedDevicePool(depletedPool *DevicePool) {
	select {
	case p.depletedPoolChan <- depletedPool:
	default:
		zap.L().Error("[DevicePoolPreprocessor] depleted pool chan is full, skip depleted pool", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", depletedPool.DiagnosticString())))))
	}
}

func (p *DevicePoolPreprocessor) loadNextDevicePool() error {
	candidate, err := getDevicePoolCandidatePath(p.preprocessDir)
	if err != nil {
		return err
	}

	if len(candidate) == 0 {
		return nil
	}

	path := candidate[0]
	p.processingDevicePool, err = LoadDevicePoolFromFile(path)
	if err != nil {
		return err
	}

	p.processingDevicePool.ResetForReading()

	if err := os.Remove(path); err != nil {
		return err
	}

	return nil
}

// Process
// 从preprocess文件夹读取数据，每一个DevicePool 都会在议论迭代内被完整读取
// 读取后的数据会存在与 processedDeviceItem 中
// 因此为了数据不丢失， 我们需要在进程停止时将整个processedDeviceItem数据完整的写入输出文件夹
func (p *DevicePoolPreprocessor) Process() error {
	for !p.IsDepleted() {
		if p.satisfyReadyCandidate() {
			zap.L().Debug("[DevicePoolPreprocessor] key:, enough candidates, skip preprocess", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", p.key)))))
			return nil
		}

		if p.processingDevicePool == nil {
			if err := p.loadNextDevicePool(); err != nil {
				zap.L().Error("[DevicePoolPreprocessor] load next device pool failed", zap.Error(err))
				return err
			}
		}

		// we are doing looping without changing the pool's index
		// so the pool status is not changed, pool.IsDepleted() is always false
		for i := p.processingDevicePool.CurrentIndex(); i != p.processingDevicePool.DeviceCount(); i++ {
			deviceItem, err := p.processingDevicePool.GetDeviceItemAtIndex(i)
			if err != nil {
				zap.L().Error("[DevicePoolPreprocessor] get device item failed, pool:, err:%v", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", p.processingDevicePool.DiagnosticString())))), err)
				return err
			}

			p.unprocessedDeviceItem = append(p.unprocessedDeviceItem, deviceItem)
		}
		p.processingDevicePool = nil

		if err := p.processDeviceItem(false); err != nil {
			zap.L().Error("[DevicePoolPreprocessor] processDeviceItem item failed", zap.Error(err))
			return err
		}
	}

	// 如果还未满足需求 且这是我们最后的数据了，把全部的数据都写完
	if !p.satisfyReadyCandidate() && p.IsDepleted() {
		if err := p.processDeviceItem(true); err != nil {
			zap.L().Error("[DevicePoolPreprocessor] processDeviceItem item failed", zap.Error(err))
			return err
		}
	}

	return nil
}

func (p *DevicePoolPreprocessor) satisfyReadyCandidate() bool {
	readyCandidates, err := getDevicePoolCandidatePath(p.outputDir)
	if err != nil {
		zap.L().Error("[DevicePoolPreprocessor] get ready candidate failed", zap.Error(err))
		return false
	}

	return len(readyCandidates) >= p.maxCandidate
}

func (p *DevicePoolPreprocessor) processDeviceItem(forceWriteAll bool) error {
	if len(p.unprocessedDeviceItem) != 0 {
		processed, err := p.deviceItemPreprocessHandler(p.unprocessedDeviceItem)
		if err != nil {
			return err
		}
		p.processedDeviceItem = append(p.processedDeviceItem, processed...)
	}

	p.unprocessedDeviceItem = nil

	var sliced bool

	for p.processedDeviceItem != nil && len(p.processedDeviceItem) >= p.splitSize {
		if err := p.writeProcessedDeviceItem(p.processedDeviceItem[:p.splitSize]); err != nil {
			return err
		}
		p.processedDeviceItem = p.processedDeviceItem[p.splitSize:]
		sliced = true
	}

	if forceWriteAll && p.processedDeviceItem != nil {
		if err := p.writeProcessedDeviceItem(p.processedDeviceItem); err != nil {
			return err
		}
		p.processedDeviceItem = nil
	}

	// 防止内存持续增长
	if p.processedDeviceItem != nil && sliced {
		copyBuffer := make([]DeviceItem, len(p.processedDeviceItem))
		copy(copyBuffer, p.processedDeviceItem)
		p.processedDeviceItem = copyBuffer
	}

	return nil
}

func (p *DevicePoolPreprocessor) writeProcessedDeviceItem(deviceItemList []DeviceItem) error {
	if deviceItemList == nil || len(deviceItemList) == 0 {
		return nil
	}

	header := NewDevicePoolHeader()
	header.SetKey(p.key)

	devicePool, err := CreateDevicePool(header)
	if err != nil {
		return err
	}

	devicePool.EnsureSizeForDeviceItems(uint32(len(deviceItemList)))
	for _, item := range deviceItemList {
		devicePool.AddDeviceItem(item)
	}

	path := GenerateDevicePoolPath(p.outputDir, devicePool)
	if err := EnsureDirForPath(path); err != nil {
		return err
	}

	zap.L().Debug("[DevicePoolPreprocessor][writeProcessedDeviceItem] write processed device item to path", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", path)))))
	if err := devicePool.SaveToFile(path); err != nil {
		return err
	}

	return nil
}

func (p *DevicePoolPreprocessor) writeDepletedPool(devicePool *DevicePool) error {
	path := GenerateDevicePoolPath(p.preprocessDir, devicePool)
	if err := EnsureDirForPath(path); err != nil {
		return err
	}

	zap.L().Debug("[DevicePoolPreprocessor][writeDepletedPool] write delpeted device item to path", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", path)))))
	if err := devicePool.SaveToFile(path); err != nil {
		return err
	}

	return nil
}

func (p *DevicePoolPreprocessor) Status() DevicePoolPreprocessorStatus {
	readyFiles, _ := getDevicePoolCandidatePath(p.outputDir)
	candidateFiles, _ := getDevicePoolCandidatePath(p.preprocessDir)

	return DevicePoolPreprocessorStatus{
		IsRunning:            p.isRunning,
		ReadyFiles:           len(readyFiles),
		ReadyDeviceCount:     int(getTotalDeviceCountFromPath(readyFiles)),
		CandidateFiles:       len(candidateFiles),
		CandidateDeviceCount: int(getTotalDeviceCountFromPath(candidateFiles)),
		ProcessedDeviceCount: len(p.processedDeviceItem),
	}
}
