package device_allocator

import (
	"go.uber.org/zap"
	"os"
	"sync"
	"time"
	"fmt"
)

type DepletedDevicePoolCallback func(devicePool *DevicePool)

type DevicePoolWorkerStatus struct {
	IsRunning            bool               `json:"is_running"`
	DevicePoolStatus     []DevicePoolStatus `json:"device_pool_status"`
	CandidateFiles       int                `json:"candidate_files"`
	CandidateDeviceCount int                `json:"candidate_device_count"`
}

// DevicePoolWorker
// 指定一个目标文件夹 loadingDir，DevicePoolWorker，会在从这个文件夹中加载文件
//
// 维护一个 devicePoolCandidate 的List
//
//	devicePoolCandidate 的逻辑为：
//		1. 从文件夹中加载最多maxCandidate个 dpool_*.bin 文件
//		2. 将这些文件中的 DevicePool 送入 devicePoolCandidate，将被加载的文件重命名
//	    3. 如果 devicePoolCandidate 中的 DevicePool 被Consume完, 则将其送入 depletedDevicePoolCallback
type DevicePoolWorker struct {
	loadingDir string

	devicePoolCandidate     []*DevicePool
	devicePoolCandidateLock sync.Mutex

	maxCandidate int

	depletedDevicePoolChan     chan *DevicePool
	depletedDevicePoolCallback func(devicePool *DevicePool)

	isRunning bool
	term      chan struct{}
	wg        sync.WaitGroup
}

func NewDevicePoolWorker(loadingDir string) *DevicePoolWorker {
	return &DevicePoolWorker{
		loadingDir:   loadingDir,
		maxCandidate: 2,

		depletedDevicePoolChan: make(chan *DevicePool, 4),
		term:                   make(chan struct{}),
	}
}

func (worker *DevicePoolWorker) SetDepletedPoolCallback(handler DepletedDevicePoolCallback) {
	worker.depletedDevicePoolCallback = handler
}

func (worker *DevicePoolWorker) Start() error {
	if err := worker.prepareDevicePool(); err != nil {
		zap.L().Error("[DevicePoolWorker] prepare device pool failed", zap.Error(err))
	}

	worker.wg.Add(1)
	go worker.loop()

	return nil
}

func (worker *DevicePoolWorker) Stop() {
	close(worker.term)
}

func (worker *DevicePoolWorker) Wait() {
	worker.wg.Wait()
}

func (worker *DevicePoolWorker) loop() {
	worker.isRunning = true
	defer func() {
		worker.wg.Done()
		worker.isRunning = false
	}()

	ticker := time.NewTicker(time.Second * 10)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := worker.prepareDevicePool(); err != nil {
				zap.L().Error("[DevicePoolWorker] prepare device pool failed", zap.Error(err))
			}

		case <-worker.depletedDevicePoolChan:
			// a hot device pool is depleted
			// try load a new device pool
			if err := worker.prepareDevicePool(); err != nil {
				zap.L().Error("[DevicePoolWorker] prepare device pool failed", zap.Error(err))
			}

		case <-worker.term:
			for _, devicePool := range worker.devicePoolCandidate {
				if worker.depletedDevicePoolCallback != nil {
					worker.depletedDevicePoolCallback(devicePool)
				}
			}
			return
		}
	}
}

func (worker *DevicePoolWorker) prepareDevicePool() error {
	candidate, err := getDevicePoolCandidatePath(worker.loadingDir)
	if err != nil {
		return err
	}

	for _, path := range candidate {
		if len(worker.devicePoolCandidate) >= worker.maxCandidate {
			break
		}

		devicePool, err := LoadDevicePoolFromFile(path)
		if err != nil {
			zap.L().Error("[DevicePoolWorker] load device pool failed", zap.Error(err))
			continue
		}
		devicePool.ResetForReading()
		zap.L().Info("[DevicePoolWorker] load device pool", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", devicePool.DiagnosticString())))))

		if err := os.Remove(path); err != nil {
			zap.L().Error("[DevicePoolWorker] remove device pool failed", zap.Error(err))
		}

		worker.devicePoolCandidate = append(worker.devicePoolCandidate, devicePool)
	}

	return nil
}

func (worker *DevicePoolWorker) GetDeviceItem() (DeviceItem, error) {
	candidateList := worker.devicePoolCandidate
	if len(candidateList) == 0 {
		return DeviceItem{}, ErrDevicePoolDepleted
	}

	for _, candidate := range worker.devicePoolCandidate {
		deviceItem, err := candidate.GetDeviceItem()
		if err != nil {
			if err == ErrDevicePoolDepleted {
				worker.onDevicePoolDepleted(candidate)
			} else {
				return DeviceItem{}, err
			}
		}

		return deviceItem, nil
	}

	return DeviceItem{}, ErrDevicePoolDepleted
}

func (worker *DevicePoolWorker) onDevicePoolDepleted(devicePool *DevicePool) {
	worker.devicePoolCandidateLock.Lock()
	defer worker.devicePoolCandidateLock.Unlock()

	if len(worker.devicePoolCandidate) == 0 {
		return
	}

	if worker.devicePoolCandidate[0] != devicePool {
		return
	}

	candidate := make([]*DevicePool, 0, len(worker.devicePoolCandidate))
	candidate = append(candidate, worker.devicePoolCandidate[1:]...)

	worker.devicePoolCandidate = candidate
	if worker.depletedDevicePoolCallback != nil {
		worker.depletedDevicePoolCallback(devicePool)
	}
	select {
	case worker.depletedDevicePoolChan <- devicePool:
	default:
		zap.L().Warn("[DevicePoolWorker] depleted device pool chan is full, skip depleted pool", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", devicePool.DiagnosticString())))))
	}
}

func (worker *DevicePoolWorker) Status() DevicePoolWorkerStatus {
	candidate, _ := getDevicePoolCandidatePath(worker.loadingDir)
	totalDeviceCount := getTotalDeviceCountFromPath(candidate)

	status := DevicePoolWorkerStatus{
		IsRunning:            worker.isRunning,
		CandidateFiles:       len(candidate),
		CandidateDeviceCount: int(totalDeviceCount),
	}

	for _, devicePool := range worker.devicePoolCandidate {
		status.DevicePoolStatus = append(status.DevicePoolStatus, devicePool.GetDevicePoolStatus())
	}

	return status
}
