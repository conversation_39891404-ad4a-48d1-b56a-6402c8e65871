package device_allocator

import (
	"fmt"
	"go.uber.org/zap"
	"path/filepath"
	"sync"
	"time"
)

type DeviceItemConfigPreprocessHandler func(config DevicePoolConfig, deviceItemList []DeviceItem) ([]DeviceItem, error)

type DeviceAllocatorManager struct {
	workingRootDir string

	publishedPoolManagerMap map[string]*DevicePoolManager

	poolManagerMap     map[string]*DevicePoolManager
	poolManagerMapLock sync.Mutex

	deviceInfoMapping           *DeviceInfoMapping
	deviceItemPreprocessHandler DeviceItemConfigPreprocessHandler

	autoDetect bool

	tern chan struct{}
}

func NewDeviceAllocatorManager() *DeviceAllocatorManager {
	return &DeviceAllocatorManager{
		publishedPoolManagerMap: make(map[string]*DevicePoolManager),
		poolManagerMap:          make(map[string]*DevicePoolManager),
		deviceItemPreprocessHandler: func(config DevicePoolConfig, deviceItemList []DeviceItem) ([]DeviceItem, error) {
			return deviceItemList, nil
		},
		tern: make(chan struct{}),
	}
}

func (manager *DeviceAllocatorManager) GetWorkingRootDir() string {
	return manager.workingRootDir
}

func (manager *DeviceAllocatorManager) SetAutoDetect(autoDetect bool) {
	manager.autoDetect = autoDetect
}

func (manager *DeviceAllocatorManager) SetDeviceItemPreprocessHandler(handler DeviceItemConfigPreprocessHandler) {
	manager.deviceItemPreprocessHandler = handler
}

func (manager *DeviceAllocatorManager) GetDeviceItemPreprocessHandler() DeviceItemConfigPreprocessHandler {
	return manager.deviceItemPreprocessHandler
}

func (manager *DeviceAllocatorManager) GetDeviceInfoMapping() *DeviceInfoMapping {
	return manager.deviceInfoMapping
}

func (manager *DeviceAllocatorManager) Start() error {
	if err := manager.loadDeviceInfoMapping(); err != nil {
		return err
	}

	go manager.loop()
	return nil
}

func (manager *DeviceAllocatorManager) Stop() error {
	close(manager.tern)
	manager.shutdown()
	return nil
}

func (manager *DeviceAllocatorManager) loop() {
	detectTimer := time.NewTimer(0)
	deviceInfoLoaderTimer := time.NewTimer(time.Minute * 5)

	for {
		select {
		case <-detectTimer.C:
			if manager.autoDetect {
				if err := manager.doAutoDetect(); err != nil {
					zap.L().Warn("[DeviceAllocatorManager] auto detect failed", zap.Error(err))
				}
			}

			manager.publishPoolManagerMap()
			detectTimer.Reset(time.Minute)
		case <-deviceInfoLoaderTimer.C:
			if err := manager.loadDeviceInfoMapping(); err != nil {
				zap.L().Warn("[DeviceAllocatorManager] load device info mapping failed", zap.Error(err))
			}

			deviceInfoLoaderTimer.Reset(time.Minute * 5)
		case <-manager.tern:
			return
		}
	}
}

func (manager *DeviceAllocatorManager) shutdown() {
	manager.poolManagerMapLock.Lock()
	defer manager.poolManagerMapLock.Unlock()

	for name, poolManager := range manager.poolManagerMap {
		if err := poolManager.Stop(); err != nil {
			zap.L().Warn("[DeviceAllocatorManager] stop device pool failed, name:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", name)))), zap.Error(err))
		}
	}
}

func (manager *DeviceAllocatorManager) SetWorkingRootDir(dir string) {
	manager.workingRootDir = dir
}

func (manager *DeviceAllocatorManager) StartDevicePool(name string) (*DevicePoolManager, error) {
	zap.L().Info("[DeviceAllocatorManager] start device pool, name", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", name)))))

	manager.poolManagerMapLock.Lock()
	defer manager.poolManagerMapLock.Unlock()

	if _, ok := manager.poolManagerMap[name]; ok {
		return nil, fmt.Errorf("device pool already started, name:%s", name)
	}

	dpRoot := filepath.Join(manager.workingRootDir, name)
	dpManager := NewDevicePoolManager(dpRoot)
	dpManager.SetDeviceItemPreprocessHandler(manager.GetDeviceItemPreprocessHandler())

	if err := dpManager.Start(); err != nil {
		return nil, err
	}

	manager.poolManagerMap[name] = dpManager

	return dpManager, nil
}

func (manager *DeviceAllocatorManager) StopDevicePool(name string) (*DevicePoolManager, error) {
	manager.poolManagerMapLock.Lock()
	defer manager.poolManagerMapLock.Unlock()

	if _, ok := manager.poolManagerMap[name]; !ok {
		return nil, fmt.Errorf("device pool not started, name:%s", name)
	}

	poolManager := manager.poolManagerMap[name]
	delete(manager.poolManagerMap, name)

	if err := poolManager.Stop(); err != nil {
		return nil, err
	}

	return poolManager, nil
}

func (manager *DeviceAllocatorManager) publishPoolManagerMap() {
	publishedMap := make(map[string]*DevicePoolManager)

	manager.poolManagerMapLock.Lock()
	defer manager.poolManagerMapLock.Unlock()

	for name, poolManager := range manager.poolManagerMap {
		publishedMap[name] = poolManager
	}

	manager.publishedPoolManagerMap = publishedMap
}

func (manager *DeviceAllocatorManager) GetDevicePoolManager(name string) *DevicePoolManager {
	if _, ok := manager.publishedPoolManagerMap[name]; !ok {
		return nil
	}

	return manager.publishedPoolManagerMap[name]
}

func (manager *DeviceAllocatorManager) GetDevicePoolManagerWithLock(name string) *DevicePoolManager {
	manager.poolManagerMapLock.Lock()
	defer manager.poolManagerMapLock.Unlock()

	if _, ok := manager.poolManagerMap[name]; !ok {
		return nil
	}

	return manager.poolManagerMap[name]
}

func (manager *DeviceAllocatorManager) GetDevicePoolManagerMap() map[string]*DevicePoolManager {
	return manager.publishedPoolManagerMap
}

func (manager *DeviceAllocatorManager) GetDeviceItem(poolName string, keyName string, keyType KeyType) (DeviceItem, *DeviceInfo, error) {
	poolManager := manager.GetDevicePoolManager(poolName)
	if poolManager == nil {
		return DeviceItem{}, nil, fmt.Errorf("device pool not found, name:%s", poolName)
	}

	if poolManager.GetKeyType() != keyType {
		return DeviceItem{}, nil, fmt.Errorf("key type not match, pool:%s, keyType:%s", poolName, keyType)
	}

	worker := poolManager.GetPublishedWorker(keyName)
	if worker == nil {
		return DeviceItem{}, nil, fmt.Errorf("device worker not found, name:%s", keyName)
	}

	deviceItem, err := worker.GetDeviceItem()
	if err != nil {
		return DeviceItem{}, nil, err
	}

	if deviceItem.DeviceInfoId != 0 {
		deviceInfo := manager.deviceInfoMapping.GetDeviceInfo(deviceItem.DeviceInfoId)
		if deviceInfo == nil {
			return DeviceItem{}, nil, fmt.Errorf("device info not found, id:%d", deviceItem.DeviceInfoId)
		}

		return deviceItem, deviceInfo, nil
	}

	return deviceItem, nil, nil
}

func (manager *DeviceAllocatorManager) doAutoDetect() error {
	subDirs, err := getSubDirs(manager.workingRootDir)
	if err != nil {
		return err
	}

	configList := make([]*DevicePoolConfig, 0, len(subDirs))

	for _, dir := range subDirs {
		configPath := filepath.Join(manager.workingRootDir, dir, "config.json")
		config := NewDevicePoolConfig()
		if err := config.ReadFile(configPath); err != nil {
			zap.L().Warn("[DeviceAllocatorManager] read device pool config failed, path:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", configPath)))), zap.Error(err))
			continue
		}

		configList = append(configList, config)
	}

	for _, config := range configList {
		dpManager := manager.GetDevicePoolManagerWithLock(config.Name)
		if config.Running && dpManager == nil {
			if _, err := manager.StartDevicePool(config.Name); err != nil {
				zap.L().Warn("[DeviceAllocatorManager] start device pool failed, name:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", config.Name)))), zap.Error(err))
			}

			continue
		}

		if !config.Running && dpManager != nil {
			if _, err := manager.StopDevicePool(config.Name); err != nil {
				zap.L().Warn("[DeviceAllocatorManager] stop device pool failed, name:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", config.Name)))), zap.Error(err))
			}

			continue
		}
	}

	return nil
}

func (manager *DeviceAllocatorManager) loadDeviceInfoMapping() error {
	csvLoader := NewDeviceInfoMappingCsvLoader(manager.workingRootDir + "/device_info.csv")
	err := csvLoader.Load()
	if err != nil {
		return err
	}
	manager.deviceInfoMapping = csvLoader.GetDeviceInfoMapping()
	return nil
}
