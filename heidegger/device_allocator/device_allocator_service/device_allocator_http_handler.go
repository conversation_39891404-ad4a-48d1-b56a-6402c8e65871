package device_allocator_service

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/device_allocator/device_allocator"
	"gitlab.com/dev/heidegger/device_allocator/legacy_device_allocation_data"
	"gitlab.com/dev/heidegger/library/echo_helper"
	"os"
	"path/filepath"
	"strconv"
	"strings"
)

type DeviceAllocatorHttpHandler struct {
	deviceAllocatorManager *device_allocator.DeviceAllocatorManager
}

func NewDeviceAllocatorHttpHandler(deviceAllocatorManager *device_allocator.DeviceAllocatorManager) *DeviceAllocatorHttpHandler {
	return &DeviceAllocatorHttpHandler{
		deviceAllocatorManager: deviceAllocatorManager,
	}
}

func (handler *DeviceAllocatorHttpHandler) RegisterEcho(e *echo.Echo) {
	e.Any("/device_allocator/device_pool/start", handler.OnStartDevicePool)
	e.Any("/device_allocator/device_pool/stop", handler.OnStopDevicePool)
	e.Any("/device_allocator/device_pool/status", handler.OnGetStatus)
	e.Any("/device_allocator/device_pool/get_device_item", handler.OnGetDeviceItem)
	e.Any("/device_allocator/device_pool/process_legacy_data", handler.OnProcessLegacyData)
	e.Any("/device_allocator/device_pool/get_device_info", handler.OnGetDeviceInfo)
}

func (handler *DeviceAllocatorHttpHandler) OnStartDevicePool(ctx echo.Context) error {
	name := ctx.QueryParam("name")
	dpManager, err := handler.deviceAllocatorManager.StartDevicePool(name)
	if err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	devicePoolManagerStatus := dpManager.Status()
	return echo_helper.Response(ctx, devicePoolManagerStatus)
}

func (handler *DeviceAllocatorHttpHandler) OnStopDevicePool(ctx echo.Context) error {
	name := ctx.QueryParam("name")
	dpManager, err := handler.deviceAllocatorManager.StopDevicePool(name)
	if err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	devicePoolManagerStatus := dpManager.Status()
	return echo_helper.Response(ctx, devicePoolManagerStatus)
}

func (handler *DeviceAllocatorHttpHandler) OnGetStatus(ctx echo.Context) error {
	name := ctx.QueryParam("name")
	dpManager := handler.deviceAllocatorManager.GetDevicePoolManager(name)
	if dpManager == nil {
		return echo_helper.ErrorResponse(ctx, fmt.Errorf("device pool not found, name:%s", name))
	}

	status := dpManager.Status()
	return echo_helper.Response(ctx, status)
}

func (handler *DeviceAllocatorHttpHandler) OnGetDeviceItem(ctx echo.Context) error {
	name := ctx.QueryParam("name")
	key := ctx.QueryParam("key")
	keyTypeStr := ctx.QueryParam("key_type")
	keyType := device_allocator.KeyTypeFromString(keyTypeStr)
	if keyType == device_allocator.KeyTypeUnknown {
		return echo_helper.ErrorResponse(ctx, fmt.Errorf("key type error"))
	}

	deviceItem, deviceInfo, err := handler.deviceAllocatorManager.GetDeviceItem(name, key, keyType)
	if err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	type ResponseItem struct {
		DeviceId   string                       `json:"device_id"`
		DeviceItem device_allocator.DeviceItem  `json:"device_item"`
		DeviceInfo *device_allocator.DeviceInfo `json:"device_info"`
	}

	return echo_helper.Response(ctx, ResponseItem{
		DeviceId:   deviceItem.GetDeviceId(),
		DeviceItem: deviceItem,
		DeviceInfo: deviceInfo,
	})
}

func (handler *DeviceAllocatorHttpHandler) OnProcessLegacyData(ctx echo.Context) error {
	type Request struct {
		Name            string `json:"name"`
		Path            string `json:"path"`
		Overwrite       bool   `json:"overwrite"`
		FrequencyId     int    `json:"frequency_id"`
		FrequencyLength int    `json:"frequency_length"`
		MaxFetchCount   int    `json:"max_fetch_count"`
		KeyType         string `json:"key_type"`
	}

	req := &Request{}
	if err := ctx.Bind(req); err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	keyType := device_allocator.KeyTypeFromString(req.KeyType)
	if keyType == device_allocator.KeyTypeUnknown {
		return echo_helper.ErrorResponse(ctx, fmt.Errorf("process fail, key type err"))
	}

	req.Name = strings.TrimSpace(req.Name)
	if strings.Contains(req.Name, ".") ||
		strings.Contains(req.Name, "~") ||
		strings.Contains(req.Name, "/") {
		return echo_helper.ErrorResponse(ctx, fmt.Errorf("process fail, name err"))
	}

	stat, err := os.Stat(req.Path)
	if err != nil {
		return echo_helper.ErrorResponse(ctx, fmt.Errorf("process fail"))
	}

	if stat.IsDir() {
		return echo_helper.ErrorResponse(ctx, fmt.Errorf("process fail"))
	}

	if handler.deviceAllocatorManager.GetDevicePoolManagerWithLock(req.Name) != nil {
		return echo_helper.ErrorResponse(ctx, fmt.Errorf("device pool already exists, name:%s, stop it first", req.Name))
	}

	outputTmp := filepath.Join(
		handler.deviceAllocatorManager.GetWorkingRootDir(),
		req.Name,
		"preprocess_tmp")
	if stat, _ := os.Stat(outputTmp); stat != nil {
		zap.L().Info("[DeviceAllocatorHttpHandler] remove tmp data, name:, path", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", req.Name)))), zap.String("param2", fmt.Sprintf("%v", outputTmp)))
		if err := os.RemoveAll(outputTmp); err != nil {
			return echo_helper.ErrorResponse(ctx, fmt.Errorf("process fail"))
		}
	}

	output := filepath.Join(
		handler.deviceAllocatorManager.GetWorkingRootDir(),
		req.Name,
		"preprocess")

	if stat, _ := os.Stat(output); stat != nil {
		if !req.Overwrite {
			return echo_helper.ErrorResponse(ctx, fmt.Errorf("process fail, data exists"))
		} else {
			zap.L().Info("[DeviceAllocatorHttpHandler] remove old data, name:, path", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", req.Name)))), zap.String("param2", fmt.Sprintf("%v", output)))
			if err := os.RemoveAll(output); err != nil {
				return echo_helper.ErrorResponse(ctx, fmt.Errorf("process fail"))
			}
		}
	}

	reader := legacy_device_allocation_data.NewLegacyDataReader(
		req.Path,
		outputTmp)
	reader.SetSplitSize(10000)
	reader.SetKeyType(keyType)

	if err := reader.Process(); err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	config := device_allocator.NewDevicePoolConfig()
	config.Name = req.Name
	config.Desc = req.Name
	config.KeyType = reader.GetKeyType()

	if req.FrequencyId != 0 {
		config.FrequencyFormat = "freq_" + strconv.Itoa(req.FrequencyId) + "_%s"
		config.FrequencyLength = int32(req.FrequencyLength)
		config.MaxFetchCount = int32(req.MaxFetchCount)
	}
	config.Running = true

	if err := os.Rename(outputTmp, output); err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	configPath := filepath.Join(
		handler.deviceAllocatorManager.GetWorkingRootDir(),
		req.Name,
		"config.json")
	if err := config.WriteFile(configPath); err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	return echo_helper.Response(ctx, output)
}

func (handler *DeviceAllocatorHttpHandler) OnGetDeviceInfo(ctx echo.Context) error {
	deviceInfoId, err := strconv.ParseInt(ctx.QueryParam("device_info_id"), 10, 64)
	if err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	deviceInfo := handler.deviceAllocatorManager.GetDeviceInfoMapping().GetDeviceInfo(uint64(deviceInfoId))
	if deviceInfo == nil {
		return echo_helper.ErrorResponse(ctx, fmt.Errorf("device info not found, id:%d", deviceInfoId))
	}

	return echo_helper.Response(ctx, deviceInfo)
}
