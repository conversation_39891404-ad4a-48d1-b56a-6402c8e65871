package main

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/device_allocator/device_allocator"
	"gitlab.com/dev/heidegger/device_allocator/device_allocator_service"
	"gitlab.com/dev/heidegger/frequency/frequency_client"
	"gitlab.com/dev/heidegger/master_server/master_server"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	_ "runtime/pprof"
	"sync"
	"syscall"
	"time"
)

func main() {
	var env Env
	debug := false

	if !debug {
		client := master_server.NewKeyValueHttpClient("http://local.master_server.com:28000")
		if err := client.GetInto("config", &env); err != nil {
			panic(err)
		}
	} else {
		env.FrequencyClient = RapidOption{
			Address: []string{"*********:12345"},
			Timeout: 100,
		}
	}

	cwd, _ := os.Getwd()
	//binDir := filepath.Dir(os.Args[0])
	//dataDir := filepath.Join(binDir, "data")
	dataDir := "./data"

	zap.L().Info("data dir: , cwd", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", dataDir)))), zap.String("param2", fmt.Sprintf("%v", cwd)))

	frequencyClient := frequency_client.NewFrequencyClient(
		env.FrequencyClient.Address,
		10000)
	if err := frequencyClient.Start(); err != nil {
		panic(err)
	}
	zap.L().Info("waiting for frequency client")
	time.Sleep(time.Second * 10)

	frequencyHandler := device_allocator.NewDeviceAllocatorFrequencyHandler(frequencyClient)

	deviceAllocatorManager := device_allocator.NewDeviceAllocatorManager()
	deviceAllocatorManager.SetWorkingRootDir(dataDir)
	deviceAllocatorManager.SetAutoDetect(true)
	deviceAllocatorManager.SetDeviceItemPreprocessHandler(frequencyHandler.Handle)
	if err := deviceAllocatorManager.Start(); err != nil {
		panic(err)
	}

	echoServer := echo.New()

	httpHandler := device_allocator_service.NewDeviceAllocatorHttpHandler(deviceAllocatorManager)
	httpHandler.RegisterEcho(echoServer)

	rapidServer := device_allocator_service.NewDeviceAllocatorRapidServer(deviceAllocatorManager)

	var gracefulStop = make(chan os.Signal)
	signal.Notify(gracefulStop, syscall.SIGTERM)
	signal.Notify(gracefulStop, syscall.SIGINT)
	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		sig := <-gracefulStop
		fmt.Printf("caught sig: %+v", sig)

		if err := deviceAllocatorManager.Stop(); err != nil {
			zap.L().Warn("[DeviceAllocatorManager] stop failed", zap.Error(err))
		}

		if err := echoServer.Shutdown(context.TODO()); err != nil {
			zap.L().Warn("[EchoServer] shutdown failed", zap.Error(err))
		}

		rapidServer.Stop()
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := echoServer.Start(fmt.Sprintf("%s:%d", "0.0.0.0", 58102)); err != nil {
			zap.L().Error("[EchoServer] start failed", zap.Error(err))
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := rapidServer.Start("0.0.0.0", 58101, 100); err != nil {
			zap.L().Error("[RapidServer] start failed", zap.Error(err))
		}
	}()

	go func() {
		defer wg.Done()
		if err := http.ListenAndServe("0.0.0.0:6060", nil); err != nil {
			zap.L().Error("[PprofServer] start failed", zap.Error(err))
		}
	}()

	wg.Wait()
}

type RapidOption struct {
	Address []string
	Timeout int
}

type Env struct {
	FrequencyClient RapidOption `json:"engine.frequency_client"`
}

func (env *Env) DumpJson() {
	result, _ := json.MarshalIndent(env, "", "  ")
	fmt.Println(string(result))
}
