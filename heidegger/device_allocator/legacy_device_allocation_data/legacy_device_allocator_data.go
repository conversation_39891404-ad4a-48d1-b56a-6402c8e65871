package legacy_device_allocation_data

import (
	"bufio"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/device_allocator/device_allocator"
	"math/rand"
	"os"
	"path/filepath"
	"strconv"
	"time"
)

type LegacyDataReader struct {
	loadPath     string
	writePath    string
	keyType      device_allocator.KeyType
	keyFetcher   func(data *LegacyData) string
	userAgentMap map[string]uint64

	splitSize int
}

func NewLegacyDataReader(loadPath string, writePath string) *LegacyDataReader {
	return &LegacyDataReader{
		loadPath:     loadPath,
		writePath:    writePath,
		userAgentMap: make(map[string]uint64),
	}
}

func (reader *LegacyDataReader) SetKeyType(keyType device_allocator.KeyType) {
	reader.keyType = keyType

	switch reader.keyType {
	case device_allocator.KeyTypeDeviceType:
		reader.SetDeviceTypeKeyFetcher()
	case device_allocator.KeyTypeDeviceTypeGeoCode:
		reader.SetDeviceTypeGeoCodeKeyFetcher()
	case device_allocator.KeyTypeDeviceIdType:
		reader.SetDeviceIdTypeKeyFetcher()
	case device_allocator.KeyTypeDeviceIdTypeGeoCode:
		reader.SetDeviceIdTypeGeoCodeKeyFetcher()
	case device_allocator.KeyTypeGeoCode:
		reader.SetGeoCodeKeyFetcher()
	default:
		reader.SetEmptyKeyFetcher()
	}
}

func (reader *LegacyDataReader) GetKeyType() device_allocator.KeyType {
	return reader.keyType
}

func (reader *LegacyDataReader) SetDeviceTypeKeyFetcher() {
	reader.keyFetcher = func(data *LegacyData) string {
		return strconv.Itoa(int(data.DeviceType))
	}
}

func (reader *LegacyDataReader) SetDeviceTypeGeoCodeKeyFetcher() {
	reader.keyFetcher = func(data *LegacyData) string {
		return fmt.Sprintf("%d_%d", data.DeviceType, data.GeoCode)
	}
}

func (reader *LegacyDataReader) SetDeviceIdTypeGeoCodeKeyFetcher() {
	reader.keyFetcher = func(data *LegacyData) string {
		return fmt.Sprintf("%d_%d", data.DeviceIdType, data.GeoCode)
	}
}

func (reader *LegacyDataReader) SetDeviceIdTypeKeyFetcher() {
	reader.keyFetcher = func(data *LegacyData) string {
		return strconv.Itoa(int(data.DeviceIdType))
	}
}

func (reader *LegacyDataReader) SetGeoCodeKeyFetcher() {
	reader.keyFetcher = func(data *LegacyData) string {
		return strconv.FormatInt(data.GeoCode, 10)
	}
}

func (reader *LegacyDataReader) SetEmptyKeyFetcher() {
	reader.keyFetcher = func(data *LegacyData) string {
		return ""
	}
}

func (reader *LegacyDataReader) SetKeyFetcher(fetcher func(data *LegacyData) string) {
	reader.keyFetcher = fetcher
}

func (reader *LegacyDataReader) SetSplitSize(splitSize int) {
	reader.splitSize = splitSize
}

func (reader *LegacyDataReader) Process() error {
	file, err := os.Open(reader.loadPath)
	if err != nil {
		return err
	}

	dataKeyMap := make(map[string][]*LegacyData)

	deviceInfoMapping := device_allocator.NewDeviceInfoMapping()

	// read each line
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()

		data := &LegacyData{}
		if err := data.ParseLine(line); err != nil {
			return err
		}

		deviceInfo := data.GetDeviceInfo()
		deviceInfoId := deviceInfoMapping.Add(deviceInfo)

		data.UserAgentId = int64(deviceInfoId)

		key := reader.keyFetcher(data)
		dataKeyMap[key] = append(dataKeyMap[key], data)

		if len(dataKeyMap[key]) >= reader.splitSize {
			if err := reader.writeFile(key, dataKeyMap[key]); err != nil {
				return err
			}
			dataKeyMap[key] = nil
		}
	}

	for key, list := range dataKeyMap {
		rand.Shuffle(len(list), func(i, j int) {
			list[i], list[j] = list[j], list[i]
		})

		if err := reader.writeFile(key, list); err != nil {
			return err
		}
		dataKeyMap[key] = nil
	}

	deviceInfoMapping.SetVersion(time.Now().Format("20060102150405"))
	if err := deviceInfoMapping.Save(filepath.Join(reader.writePath, "device_info_mapping.txt")); err != nil {
		return err
	}

	if err := deviceInfoMapping.SaveVersionFile(filepath.Join(reader.writePath, "device_info_mapping_version.txt")); err != nil {
		return err
	}

	zap.L().Info("LegacyDataReader process done, total pool", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(len(dataKeyMap))))))

	return nil
}

func (reader *LegacyDataReader) processData(data *LegacyData) {
	if len(data.UserAgent) == 0 {
		return
	}

	if _, ok := reader.userAgentMap[data.UserAgent]; !ok {
		reader.userAgentMap[data.UserAgent] = uint64(len(reader.userAgentMap))
	}

	data.UserAgentId = int64(reader.userAgentMap[data.UserAgent])
}

func (reader *LegacyDataReader) writeFile(key string, list []*LegacyData) error {
	if len(list) == 0 {
		return nil
	}

	header := device_allocator.NewDevicePoolHeader()
	header.SetKey(key)
	devicePool, err := device_allocator.CreateDevicePool(header)
	if err != nil {
		return err
	}

	devicePool.EnsureSizeForDeviceItems(uint32(len(list)))

	for _, data := range list {
		devicePool.AddDeviceItem(device_allocator.DeviceItem{
			DeviceId:     device_allocator.DeviceIdBytesFromString(data.UserId),
			DeviceIdType: uint16(data.DeviceIdType),
			DeviceInfoId: uint64(data.UserAgentId),
			FetchCount:   0,
		})
	}

	path := reader.getWriteFilePath(key, devicePool)
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	return devicePool.SaveToFile(path)
}

func (reader *LegacyDataReader) getWriteFilePath(key string, devicePool *device_allocator.DevicePool) string {
	dir := filepath.Join(
		reader.writePath,
		fmt.Sprintf("dpool_%s", key))

	path := device_allocator.GenerateDevicePoolPath(dir, devicePool)

	return path
}

func (reader *LegacyDataReader) GetOsTypeByUserIdType(userIdType device_allocator.DeviceIdType) device_allocator.DeviceType {
	return userIdType.ToDeviceType()
}
