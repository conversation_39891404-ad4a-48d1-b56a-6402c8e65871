package price_coder

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
	"go.uber.org/zap"
)

type YLHRtbCoder struct {
	id   string //可用于标识coder
	iKey string
	eKey string
}

func CreateYLHRtbCoder(id, iKey, eKey string) BaseCoder {
	return &YLHRtbCoder{
		id:   id,
		iKey: iKey,
		eKey: eKey,
	}
}

func (c *YLHRtbCoder) Decode(rawPrice string) (uint64, error) {
	return c.DecodeWithKey(rawPrice, c.iKey, c.eKey)
}

func (c *YLHRtbCoder) DecodeWithKey(rawPrice string, iKey, eKey string) (uint64, error) {
	return 0, fmt.Errorf("not support")
}

func (c *YLHRtbCoder) Encode(rawPrice uint64) (string, error) {
	return c.EncodeWithKey(rawPrice, c.i<PERSON>ey, c.e<PERSON>ey)
}

func (c *YLHRtbCoder) EncodeWithKey(rawPrice uint64, iKey, eKey string) (string, error) {
	//这个是对方给的公钥信息，使用这个加密
	const publicKeyPrefix = `
-----BEGIN PUBLIC KEY-----
%s
%s
-----END PUBLIC KEY-----`

	publicKey := fmt.Sprintf(publicKeyPrefix, iKey, eKey)

	block, _ := pem.Decode([]byte(publicKey))
	if block == nil {
		zap.L().Error("[YLHRtbCoder]failed to parse PEM block containing the public key")
		return "", fmt.Errorf("[YLHRtbCoder]failed to parse PEM block containing the public key")
	}

	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		zap.L().Error("[YLHRtbCoder]failed to parse DER encoded public key: ", zap.Error(err))

		return "", err
	}

	pubInfoss, _ := pub.(*rsa.PublicKey)

	priceInfos := fmt.Sprint(rawPrice) //需要加密的价格

	encryptedBytes, err := rsa.EncryptPKCS1v15(
		rand.Reader,
		pubInfoss,
		[]byte(priceInfos),
	)
	if err != nil {
		zap.L().Error("[YLHRtbCoder]failed to EncryptPKCS1v15: ", zap.Error(err))

		return "", err
	}

	//对方要求使用base64处理信息发送给对方
	encryptPrice := base64.StdEncoding.EncodeToString(encryptedBytes)

	//宏替换的时候encode
	//queryNncryptPrice := url.QueryEscape(encryptPrice)
	return encryptPrice, nil
}
