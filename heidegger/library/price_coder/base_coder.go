package price_coder

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/binary"
	"encoding/hex"
	"errors"
	"fmt"
	"hash"
	"os"
)

type BaseCoder interface {
	Decode(rawPrice string) (uint64, error)
	DecodeWithKey(rawPrice string, iKey, eKey string) (uint64, error)

	Encode(rawPrice uint64) (string, error)
	EncodeWithKey(rawPrice uint64, iKey, eKey string) (string, error)
}

func googleRtbPriceEncode(origData uint64, iKey, eKey string) ([]byte, error) {
	if len(iKey) != 32 || len(eKey) != 32 {
		return nil, errors.New("key invalid")
	}

	PayloadSize := 8
	InitVectorSize := 16
	SignatureSize := 4

	var (
		iv         = make([]byte, InitVectorSize)
		encoded    = make([]byte, PayloadSize)
		signature  = make([]byte, SignatureSize)
		priceBytes = make([]byte, PayloadSize)
	)

	sum := md5.Sum([]byte(""))
	copy(iv[:], sum[:])

	pad, err := hmac2(eKey, iv[:])
	if err != nil {
		return nil, err
	}
	pad = pad[:PayloadSize]

	binary.BigEndian.PutUint64(priceBytes, origData)

	// enc_price = pad <xor> price
	for i := range priceBytes {
		encoded[i] = pad[i] ^ priceBytes[i]
	}

	// signature = hmac(i_key, data || iv), first 4 bytes
	sig, err := hmac2(iKey, append(priceBytes[:], iv[:]...))
	if err != nil {
		return nil, err
	}
	signature = sig[:SignatureSize]

	result := append(append(iv[:], encoded[:]...), signature[:]...)

	// final_message = WebSafeBase64Encode( iv || enc_price || signature )
	//base64.RawURLEncoding.EncodeToString(result)

	return result, nil

}

func googleRtbPriceDecode(encodedData []byte, iKey, eKey string) (uint64, error) {
	if len(iKey) != 32 || len(eKey) != 32 {
		return 0, errors.New("key invalid")
	}

	PayloadSize := 8
	InitVectorSize := 16
	SignatureSize := 4

	var (
		iv         = make([]byte, InitVectorSize)
		p          = make([]byte, PayloadSize)
		signature  = make([]byte, SignatureSize)
		priceBytes = make([]byte, PayloadSize)
	)

	var err error

	//zap.L().Info("[googleRtbPriceDecode] %+v")
	if len(encodedData) < 28 {
		return 0, errors.New("googleRtbPriceDecode: encode data too short")
	}

	copy(iv[:], encodedData[0:16])
	copy(p[:], encodedData[16:24])
	copy(signature[:], encodedData[24:28])

	pad, err := hmac2(eKey, iv[:])
	if err != nil {
		return 0, err
	}

	pad = pad[:PayloadSize]
	for i := range p {
		priceBytes[i] = pad[i] ^ p[i]
	}

	sig, err := hmac2(iKey, append(priceBytes[:], iv[:]...))
	if err != nil {
		return 0, err
	}

	sig = sig[:SignatureSize]
	for i := range sig {
		if sig[i] != signature[i] {
			return 0, fmt.Errorf("googleRtbPriceDecode: failed to decrypt, got:%s, expect:%s",
				string(sig), string(signature))

		}
	}

	price := binary.BigEndian.Uint64(priceBytes)
	return price, nil
}

func aes128EcbPkcs5Encode(origData, key []byte) ([]byte, error) {
	if len(key) != 16 {
		return nil, errors.New("key length invalid, must be 16")
	}

	return aesEcbPkcs5Encode(origData, key)
}

func aes128EcbPkcs5Decode(encodedData, key []byte) ([]byte, error) {
	if len(key) != 16 {
		return nil, errors.New("key length invalid, must be 16")
	}

	return aesEcbPkcs5Decode(encodedData, key)
}

func aesEcbPkcs5Encode(origData, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	//AES分组长度为128位，所以blockSize=16，单位字节
	blockSize := block.BlockSize()
	origData = pKCS5Padding(origData, blockSize)

	encodedData := make([]byte, len(origData))

	for index := 0; index < len(origData); index += blockSize {
		block.Encrypt(encodedData[index:index+blockSize], origData[index:index+blockSize])
	}

	return encodedData, nil
}

func aesEcbPkcs5Decode(encodedData, key []byte) ([]byte, error) {
	if len(encodedData) < 1 {
		return nil, errors.New("input nil")

	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	//AES分组长度为128位，所以blockSize=16，单位字节
	blockSize := block.BlockSize()
	origData := make([]byte, len(encodedData))

	for index := 0; index < len(encodedData); index += blockSize {
		block.Decrypt(origData[index:index+blockSize], encodedData[index:index+blockSize])
	}

	origData, err = pKCS5UnPadding(origData)
	if err != nil {
		return nil, err
	}

	return origData, nil
}

func aesEcbPkcs5DecodeNoPadding(encodedData, key []byte) ([]byte, error) {
	if len(encodedData) < 1 {
		return nil, errors.New("input nil")

	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	//AES分组长度为128位，所以blockSize=16，单位字节
	blockSize := block.BlockSize()
	origData := make([]byte, len(encodedData))

	for index := 0; index < len(encodedData); index += blockSize {
		block.Decrypt(origData[index:index+blockSize], encodedData[index:index+blockSize])
	}

	return origData, nil
}

func aesCBCPkcs5Encrypte(s []byte, key string) string {
	keybytes := []byte(key)
	plaintext := pKCS5Padding([]byte(s), aes.BlockSize)
	block, _ := aes.NewCipher(keybytes[:aes.BlockSize])

	mode := cipher.NewCBCEncrypter(block, keybytes[:aes.BlockSize])
	crypted := make([]byte, len(plaintext))
	mode.CryptBlocks(crypted, plaintext)
	return string(hex.EncodeToString(crypted))
}

func aesCBCPkcs5Decrypte(decrypte string, key string) (string, error) {
	defer func() {
		if err := recover(); err != nil {
			fmt.Fprintf(os.Stderr, "error string:%s key:%s err:%v\n", decrypte, key, err)
		}
	}()

	if (len(decrypte) % (aes.BlockSize * 2)) != 0 {
		return "", errors.New("decrypte data too short")
	}
	keybytes := []byte(key)
	decrypteData, err := hex.DecodeString(decrypte)
	if err != nil {
		return "", errors.New("decrypte data format error")
	}

	block, _ := aes.NewCipher(keybytes[:aes.BlockSize])
	mode := cipher.NewCBCDecrypter(block, keybytes[:aes.BlockSize])

	decryptedData := make([]byte, len(decrypteData))
	mode.CryptBlocks(decryptedData, decrypteData)
	decryptedData, err = pKCS5UnPadding(decryptedData)
	if err != nil {
		return "", err
	}
	return string(decryptedData), nil
}

func aesStdCBCPkcs5Decrypte(decrypte []byte, eKey string, iKey string) (string, error) {
	defer func() {
		if err := recover(); err != nil {
			fmt.Fprintf(os.Stderr, "error string:%s eKey:%s iKey:%s err:%v\n", string(decrypte), eKey, iKey, err)
		}
	}()

	block, _ := aes.NewCipher([]byte(eKey))
	mode := cipher.NewCBCDecrypter(block, []byte(iKey))

	decryptedData := make([]byte, len(decrypte))
	mode.CryptBlocks(decryptedData, decrypte)
	decryptedData, err := pKCS5UnPadding(decryptedData)
	if err != nil {
		return "", err
	}
	return string(decryptedData), nil
}

func aesStdCBCPkcs5Encrypt(origData []byte, eKey string, iKey string) ([]byte, error) {
	block, err := aes.NewCipher([]byte(eKey))
	if err != nil {
		return nil, err
	}

	//AES分组长度为128位，所以blockSize=16，单位字节
	blockSize := block.BlockSize()

	mode := cipher.NewCBCEncrypter(block, []byte(iKey))
	plaintext := pKCS5Padding(origData, blockSize)

	crypted := make([]byte, len(plaintext))
	mode.CryptBlocks(crypted, plaintext)
	return crypted, nil
}

func AesCBCPkcs5PaddingEncrypt(origData []byte, eKey []byte, iKey []byte) ([]byte, error) {
	block, err := aes.NewCipher(eKey)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()

	if len(iKey) != blockSize {
		return nil, fmt.Errorf("IV length must equal block size")
	}

	mode := cipher.NewCBCEncrypter(block, iKey)
	plaintext := pKCS5Padding(origData, blockSize)

	crypted := make([]byte, len(plaintext))
	mode.CryptBlocks(crypted, plaintext)
	return crypted, nil
}

func pKCS5Padding(plaintext []byte, blockSize int) []byte {
	padding := blockSize - len(plaintext)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(plaintext, padText...)
}

func pKCS5UnPadding(origData []byte) ([]byte, error) {
	length := len(origData)
	if length == 0 {
		return nil, errors.New("pKCS5UnPadding origin data length 0")
	}

	unPadding := int(origData[length-1])
	if unPadding > length {
		return nil, errors.New("pKCS5UnPadding unPadding length > origin data length")
	}

	return origData[:(length - unPadding)], nil
}

func createHmac(key, mode string) (hash.Hash, error) {
	var k []byte
	var err error

	if mode == "utf-8" {
		k = []byte(key)
	} else {
		k, err = hex.DecodeString(key)
	}
	if err != nil {
		return nil, err
	}
	return hmac.New(sha1.New, k), nil
}

func hmacSum(_hmac hash.Hash, buf []byte) []byte {
	_hmac.Reset()
	_hmac.Write(buf)
	return _hmac.Sum(nil)
}

func hmac2(key string, buf []byte) ([]byte, error) {
	_hmac, err := createHmac(key, "utf-8")
	if err != nil {
		err = fmt.Errorf("jzt/encrypt: create hmac error, %s", err.Error())
		return nil, err
	}
	return hmacSum(_hmac, buf), nil
}
