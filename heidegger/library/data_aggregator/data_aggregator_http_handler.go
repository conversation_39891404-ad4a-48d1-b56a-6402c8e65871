package data_aggregator

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/echo_helper"
	"strconv"
	"time"
)

type DataAggregatorHttpHandler struct {
	manager *DataAggregatorManager
}

func NewDataAggregatorHttpHandler(manager *DataAggregatorManager) *DataAggregatorHttpHandler {
	return &DataAggregatorHttpHandler{
		manager: manager,
	}
}

func (handler *DataAggregatorHttpHandler) RegisterEcho(e *echo.Echo) {
	e.GET("/data_aggregator/info", handler.GetInfo)
	e.GET("/data_aggregator/result", handler.GetResult)
}

func (handler *DataAggregatorHttpHandler) GetInfo(c echo.Context) error {
	type DataAggregatorInfo struct {
		Name                    string `json:"name,omitempty"`
		RotationIntervalSeconds int    `json:"rotation_interval_seconds,omitempty"`
		LastRotationTime        int64  `json:"last_rotation_time,omitempty"`
		WindowSize              uint32 `json:"window_size,omitempty"`
		CurrentIndex            uint32 `json:"current_index,omitempty"`
	}

	result := make(map[string]DataAggregatorInfo)
	for name, dataAggregator := range handler.manager.GetAllDataAggregator() {
		result[name] = DataAggregatorInfo{
			Name:                    dataAggregator.Name,
			RotationIntervalSeconds: dataAggregator.RotationIntervalSeconds,
			LastRotationTime:        dataAggregator.LastRotationTime,
			WindowSize:              dataAggregator.WindowSize,
			CurrentIndex:            dataAggregator.CurrentIndex,
		}
	}

	return echo_helper.Response(c, result)
}

func (handler *DataAggregatorHttpHandler) GetResult(c echo.Context) error {
	timeStart := time.Now()
	defer func() {
		zap.L().Info("[DataAggregatorHttpHandler] GetResult from:, time:%v", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", c.Request())))).RemoteAddr, time.Since(timeStart))
	}()

	name := c.QueryParam("name")
	startTimeStr := c.QueryParam("start_time")
	startTime := int64(0)
	if len(startTimeStr) != 0 {
		paramStartTime, err := strconv.ParseInt(startTimeStr, 10, 64)
		if err != nil {
			return echo_helper.ErrorResponse(c, fmt.Errorf("invalid start_time:%s", startTimeStr))
		}
		startTime = paramStartTime
	}
	showCurrent := c.QueryParam("show_current") == "true"

	dataAggregator := handler.manager.GetDataAggregator(name)
	if dataAggregator == nil {
		return echo_helper.ErrorResponse(c, fmt.Errorf("data aggregator not found:%s", name))
	}

	result := dataAggregator.GetData(showCurrent)

	if startTime != 0 {
		result = result.After(startTime)
	}

	return echo_helper.Response(c, result)
}
