package data_aggregator

import (
	"encoding/csv"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/echo_helper"
	"gitlab.com/dev/heidegger/library/utils/net_utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"os"
	"sort"
	"time"
)

type DataAggregatorLoader interface {
	GetStatus() string
	LoadData() (DataAggregatorResultItemList, error)
	LoadDataAfter(time.Time) (DataAggregatorResultItemList, error)
}

type DataAggregatorSource interface {
	GetStatus() string
	LoadData() (DataAggregatorResultItemList, error)
	SaveData(data DataAggregatorResultItemList) error
}

type DataAggregatorCsvSource struct {
	FilePath string
}

func NewDataAggregatorCsvSource(filePath string) *DataAggregatorCsvSource {
	return &DataAggregatorCsvSource{
		FilePath: filePath,
	}
}

func (c *DataAggregatorCsvSource) GetStatus() string {
	return c.FilePath
}

func (c *DataAggregatorCsvSource) LoadData() (DataAggregatorResultItemList, error) {
	if _, err := os.Stat(c.FilePath); err != nil {
		if os.IsNotExist(err) {
			return nil, nil
		}
		return nil, err
	}

	file, err := os.Open(c.FilePath)
	if err != nil {
		return nil, err
	}

	defer file.Close()

	csvReader := csv.NewReader(file)
	records, err := csvReader.ReadAll()
	if err != nil {
		return nil, err
	}

	mapping := make(map[string]*DataAggregatorResultItem)
	if len(records) != 0 {
		records = records[1:]
	}

	for _, record := range records {
		startTime, err := time.Parse("2006-01-02 15:04:05", record[0])
		if err != nil {
			return nil, err
		}

		freezeTime, err := time.Parse("2006-01-02 15:04:05", record[1])
		if err != nil {
			return nil, err
		}

		resultItem, ok := mapping[record[0]]
		if !ok {
			resultItem = &DataAggregatorResultItem{
				StartTime:       startTime.Unix(),
				FreezeTime:      freezeTime.Unix(),
				StartTimeHuman:  record[0],
				FreezeTimeHuman: record[1],
				Data:            make(map[string]*DataAggregatorItem),
			}
			mapping[record[0]] = resultItem
		}

		key := record[2]
		item := &DataAggregatorItem{
			RequestCount:    type_convert.GetAssertInt32(record[3]),
			ScheduleCount:   type_convert.GetAssertInt32(record[4]),
			BroadcastCount:  type_convert.GetAssertInt32(record[5]),
			ResponseCount:   type_convert.GetAssertInt32(record[6]),
			ResponsePrice:   type_convert.GetAssertInt32(record[7]),
			BidCount:        type_convert.GetAssertInt32(record[8]),
			BidPrice:        type_convert.GetAssertInt32(record[9]),
			ImpressionCount: type_convert.GetAssertInt32(record[10]),
			ClickCount:      type_convert.GetAssertInt32(record[11]),
			ActionCount:     type_convert.GetAssertInt32(record[12]),
			Cost:            type_convert.GetAssertInt32(record[13]),
			Charge:          type_convert.GetAssertInt32(record[14]),
		}

		if _, ok := resultItem.Data[key]; !ok {
			resultItem.Data[key] = item
		} else {
			resultItem.Data[key].Merge(item)
		}
	}

	result := make(DataAggregatorResultItemList, 0, len(mapping))
	for _, item := range mapping {
		result = append(result, item)
	}

	result.Sort()

	return result, nil
}

func (c *DataAggregatorCsvSource) LoadDataAfter(t time.Time) (DataAggregatorResultItemList, error) {
	data, err := c.LoadData()
	if err != nil {
		return nil, err
	}

	var result DataAggregatorResultItemList
	for _, item := range data {
		if item.StartTime > t.Unix() {
			result = append(result, item)
		}
	}

	result.Sort()

	return result, nil
}

func (c *DataAggregatorCsvSource) SaveData(data DataAggregatorResultItemList) error {
	file, err := os.OpenFile(c.FilePath, os.O_CREATE|os.O_TRUNC|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer file.Close()

	sort.Slice(data, func(i, j int) bool {
		return data[i].StartTime < data[j].StartTime
	})

	csvWriter := csv.NewWriter(file)
	defer csvWriter.Flush()

	// write header
	if err := csvWriter.Write([]string{
		"start_time",
		"freeze_time",
		"key",
		"request_count",
		"schedule_count",
		"broadcast_count",
		"response_count",
		"response_price",
		"bid_count",
		"bid_price",
		"impression_count",
		"click_count",
		"action_count",
		"cost",
		"charge",
	}); err != nil {
		return err
	}

	for _, item := range data {
		for key, value := range item.Data {
			record := []string{
				item.StartTimeHuman,
				item.FreezeTimeHuman,
				key,
				type_convert.GetAssertString(value.RequestCount),
				type_convert.GetAssertString(value.ScheduleCount),
				type_convert.GetAssertString(value.BroadcastCount),
				type_convert.GetAssertString(value.ResponseCount),
				type_convert.GetAssertString(value.ResponsePrice),
				type_convert.GetAssertString(value.BidCount),
				type_convert.GetAssertString(value.BidPrice),
				type_convert.GetAssertString(value.ImpressionCount),
				type_convert.GetAssertString(value.ClickCount),
				type_convert.GetAssertString(value.ActionCount),
				type_convert.GetAssertString(value.Cost),
				type_convert.GetAssertString(value.Charge),
			}
			if err := csvWriter.Write(record); err != nil {
				return err
			}
		}
	}

	return nil
}

type DataAggregatorHttpClient struct {
	Address string
}

func NewDataAggregatorHttpClient(address string) *DataAggregatorHttpClient {
	return &DataAggregatorHttpClient{
		Address: address,
	}
}

func (c *DataAggregatorHttpClient) GetStatus() string {
	return c.Address
}

func (c *DataAggregatorHttpClient) LoadData() (DataAggregatorResultItemList, error) {
	var result DataAggregatorResultItemList
	if err := echo_helper.GetJson(c.Address, &result); err != nil {
		return nil, err
	}

	result.Sort()

	return result, nil
}

func (c *DataAggregatorHttpClient) LoadDataAfter(t time.Time) (DataAggregatorResultItemList, error) {
	address := net_utils.AppendParam(c.Address, "start_time", fmt.Sprintf("%d", t.Unix()))
	var result DataAggregatorResultItemList

	zap.L().Info("[DataAggregatorHttpClient] LoadDataAfter address", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", address)))))

	if err := echo_helper.GetJson(address, &result); err != nil {
		return nil, err
	}

	result.Sort()

	return result, nil
}

type dataAggregatorCollectorLoaderStatus struct {
	Loader            DataAggregatorLoader
	LastFetchDataTime time.Time
}

type DataAggregatorCollector struct {
	Source         DataAggregatorSource
	Loaders        []dataAggregatorCollectorLoaderStatus
	DataAggregator *DataAggregator

	term chan struct{}
}

func NewDataAggregatorCollector(aggr *DataAggregator, source DataAggregatorSource, loaders ...DataAggregatorLoader) *DataAggregatorCollector {
	result := &DataAggregatorCollector{
		Source:         source,
		DataAggregator: aggr,
		term:           make(chan struct{}),
	}
	for _, loader := range loaders {
		result.Loaders = append(result.Loaders, dataAggregatorCollectorLoaderStatus{
			Loader:            loader,
			LastFetchDataTime: time.Now(),
		})
	}
	return result
}

func (c *DataAggregatorCollector) Start() error {
	sourceData, err := c.Source.LoadData()
	if err != nil {
		return err
	}

	zap.L().Info("[DataAggregatorCollector] load data count:, from loader:%s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(len(sourceData))))), c.Source.GetStatus())

	dataTime := time.Now().Add(time.Hour * -1)
	if len(sourceData) != 0 {
		dataTime = time.Unix(sourceData[len(sourceData)-1].StartTime, 0)
	}

	for i, loader := range c.Loaders {
		zap.L().Info("[DataAggregatorCollector] load data from loader", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", loader.Loader.GetStatus())))))

		loaderData, err := loader.Loader.LoadDataAfter(dataTime)
		if err != nil {
			zap.L().Error("[DataAggregatorCollector] load data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
			continue
		}

		zap.L().Info("[DataAggregatorCollector] load data count:, from loader:%s, time:%s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(len(loaderData))))), loader.Loader.GetStatus(), dataTime)

		sourceData = sourceData.Merge(loaderData)
		c.Loaders[i].LastFetchDataTime = loaderData.DataTime()
	}

	c.DataAggregator.Merge(sourceData)

	go c.loop()

	return nil
}

func (c *DataAggregatorCollector) Stop() {
	close(c.term)
}

func (c *DataAggregatorCollector) loop() {
	loaderTicker := time.NewTicker(time.Second * 30)
	defer loaderTicker.Stop()

	saveTicker := time.NewTicker(time.Minute * 5)
	defer saveTicker.Stop()

	for {
		select {
		case <-loaderTicker.C:
			for i, loader := range c.Loaders {
				loaderData, err := loader.Loader.LoadDataAfter(loader.LastFetchDataTime)
				if err != nil {
					zap.L().Error("[DataAggregatorCollector] load data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
					continue
				}

				zap.L().Info("[DataAggregatorCollector] load data count:, from loader:%s, time:%s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(len(loaderData))))), loader.Loader.GetStatus(), loader.LastFetchDataTime)

				if len(loaderData) == 0 {
					continue
				}

				loaderData.Sort()
				c.DataAggregator.Merge(loaderData)
				c.Loaders[i].LastFetchDataTime = loaderData.DataTime()
			}
		case <-saveTicker.C:
			if err := c.Source.SaveData(c.DataAggregator.GetData(false)); err != nil {
				zap.L().Error("[DataAggregatorCollector] save data error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
			}
		case <-c.term:
			return
		}
	}
}
