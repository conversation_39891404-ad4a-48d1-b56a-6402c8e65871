package prometheus_helper

import (
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"net/http"
	"fmt"
)

type PrometheusManager struct {
	collector *MetricsCollector
}

func NewPrometheusManager() *PrometheusManager {
	return &PrometheusManager{
		collector: NewMetricsCollector(),
	}
}

func (manager *PrometheusManager) MustRegister(name string, provider MetricsProvider) MetricsProvider {
	var err error
	if provider, err = manager.Register(name, provider); err != nil {
		panic(err)
	}
	return provider
}

func (manager *PrometheusManager) Register(name string, provider MetricsProvider) (MetricsProvider, error) {
	var err error
	if provider, err = manager.collector.Register(name, provider); err != nil {
		return provider, err
	}

	return provider, nil
}

func (manager *PrometheusManager) Unregister(name string) {
	manager.collector.Unregister(name)
}

func (manager *PrometheusManager) onMetricsRequest(writer http.ResponseWriter, request *http.Request) {
	writer.Header()["Content-Type"] = []string{"text/plain; version=0.0.4; charset=utf-8"}
	writer.Header()["Date"] = []string{http.TimeFormat}
	writer.WriteHeader(http.StatusOK)

	promheaderMap := make(map[string]bool)

	items := manager.collector.GetItems()
	for _, item := range items {
		if _, ok := promheaderMap[item.rawName]; !ok {
			item.WriteHelp(writer)
			item.WriteType(writer)
			promheaderMap[item.rawName] = true
		}

		item.Write(writer)
	}
}

func (manager *PrometheusManager) onMetricsEcho(ctx echo.Context) error {
	zap.L().Info("Prometheus metrics requested by , remote:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", ctx.Request())))).RemoteAddr, ctx.RealIP())

	manager.onMetricsRequest(ctx.Response().Writer, ctx.Request())
	return nil
}

func (manager *PrometheusManager) RegisterEcho(server *echo.Echo) {
	server.Any("/metrics", manager.onMetricsEcho)
}
