package env_helper

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gopkg.in/yaml.v2"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
)

var (
	envHelper EnvHelper
)

type HostInfo struct {
	Hostname   string            `yaml:"hostname"`
	Cluster    string            `yaml:"cluster"`
	Region     string            `yaml:"region"`
	Id         string            `yaml:"id"`
	IP         string            `yaml:"ip"`
	ExternalIP map[string]string `yaml:"external_ip"`
}

type EnvHelper struct {
	HostInfo HostInfo `yaml:"host_info"`
}

func (c *EnvHelper) String() string {
	data, _ := json.Marshal(c)
	return string(data)
}

func init() {
	dirname, err := os.UserHomeDir()
	envConfigPath := filepath.Join(dirname, ".b9_env.yaml")

	gzConfigFile, err := os.OpenFile(envConfigPath, os.O_RDONLY, 0400)
	if err != nil {
		zap.L().Error("[EnvHelper] OpenFile ", zap.Error(err))
		return
	}
	defer gzConfigFile.Close()

	content, err := ioutil.ReadAll(gzConfigFile)
	if err != nil {
		zap.L().Error("[EnvHelper] ReadAll ", zap.Error(err))
		return
	}

	if err := yaml.Unmarshal(content, &envHelper); err != nil {
		zap.L().Fatal("[EnvHelper] ", zap.Error(err))
	}

	hostname, _ := os.Hostname()
	hostInfo, err := ParseHostname(hostname)
	if err != nil {
		zap.L().Fatal("[EnvHelper] ", zap.Error(err))
	}

	envHelper.HostInfo.Hostname = hostname
	if len(envHelper.HostInfo.Cluster) == 0 {
		envHelper.HostInfo.Cluster = hostInfo.Cluster
	}
	if len(envHelper.HostInfo.Region) == 0 {
		envHelper.HostInfo.Region = hostInfo.Region
	}
	if len(envHelper.HostInfo.Id) == 0 {
		envHelper.HostInfo.Id = hostInfo.Id
	}
	if envHelper.HostInfo.ExternalIP == nil {
		envHelper.HostInfo.ExternalIP = map[string]string{}
	}
	if len(envHelper.HostInfo.ExternalIP) == 0 && len(envHelper.HostInfo.IP) != 0 {
		envHelper.HostInfo.ExternalIP["0.0.0.0"] = envHelper.HostInfo.IP
	}

	zap.L().Info("[EnvHelper][ConfigLoaded] ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", envHelper.String())))))
}

func GetHostName() string {
	if len(envHelper.HostInfo.Hostname) == 0 {
		envHelper.HostInfo.Hostname, _ = os.Hostname()
	}
	return envHelper.HostInfo.Hostname
}

func GetHostRegion() string {
	return envHelper.HostInfo.Region
}

func GetHostCluster() string {
	return envHelper.HostInfo.Cluster
}

func GetHostId() string {
	return envHelper.HostInfo.Id
}

func GetHostIP() string {
	return envHelper.HostInfo.IP
}

func GetHostExternalIP() map[string]string {
	return envHelper.HostInfo.ExternalIP
}

func GetInternalIP() string {
	for ip := range GetHostExternalIP() {
		return ip
	}
	return ""
}

func ParseHostname(name string) (*HostInfo, error) {
	parts := strings.Split(name, "-")
	if len(parts) < 3 {
		return nil, fmt.Errorf("name format err:%s", name)
	}

	if parts[0] != "block9" && parts[0] != "lingju" {
		return nil, fmt.Errorf("name format err:%s", name)
	}

	result := &HostInfo{
		Hostname: name,
		Cluster:  parts[1],
		Region:   "",
		Id:       strings.Join(parts[2:], "-"),
	}
	return result, nil
}
