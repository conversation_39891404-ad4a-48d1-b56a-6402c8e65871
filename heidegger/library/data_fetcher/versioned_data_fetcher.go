package data_fetcher

import (
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"
)

type DataProviderObserver func(version string, reader io.ReadCloser) error

type LocalVersionProvider interface {
	GetVersion() string
}

type NullLocalVersionProvider struct {
}

func (provider *NullLocalVersionProvider) GetVersion() string {
	return ""
}

type VersionedDataFetcher interface {
	Start() error
	Stop()

	RegisterLocalVersionProvider(versionProvider LocalVersionProvider)

	GetOriginVersion() (string, error)
	GetOriginDataHead() (string, io.ReadCloser, error)
	GetOriginDataWithVersion(version string) (string, io.ReadCloser, error)

	RegisterObserver(intervalSeconds int, observer DataProviderObserver)
}

type FileVersionedDataFetcher struct {
	localVersionProvider  LocalVersionProvider
	originVersionFilePath string
	dataFilePathPattern   string
	observer              DataProviderObserver

	intervalSeconds int

	term chan struct{}
}

func NewFileVersionedDataFetcher(originVersionFilePath string) *FileVersionedDataFetcher {
	return &FileVersionedDataFetcher{
		originVersionFilePath: originVersionFilePath,
		dataFilePathPattern:   filepath.Join(filepath.Dir(originVersionFilePath), "%s"),
		localVersionProvider:  &NullLocalVersionProvider{},
		intervalSeconds:       300,
		term:                  make(chan struct{}),
	}
}

func (fetcher *FileVersionedDataFetcher) Start() error {
	go fetcher.loop()
	return nil
}

func (fetcher *FileVersionedDataFetcher) Stop() {
	close(fetcher.term)
}

func (fetcher *FileVersionedDataFetcher) RegisterLocalVersionProvider(versionProvider LocalVersionProvider) {
	fetcher.localVersionProvider = versionProvider
}

func (fetcher *FileVersionedDataFetcher) SetDataFilePathPattern(pattern string) {
	fetcher.dataFilePathPattern = pattern
}

func (fetcher *FileVersionedDataFetcher) GetOriginVersion() (string, error) {
	versionFile, err := os.OpenFile(fetcher.originVersionFilePath, os.O_RDONLY, 0644)
	if err != nil {
		return "", err
	}

	defer versionFile.Close()

	data, err := io.ReadAll(versionFile)
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(string(data)), nil
}

func (fetcher *FileVersionedDataFetcher) GetOriginDataHead() (string, io.ReadCloser, error) {
	version, err := fetcher.GetOriginVersion()
	if err != nil {
		return "", nil, err
	}

	dataFilePath := fmt.Sprintf(fetcher.dataFilePathPattern, version)
	file, err := os.OpenFile(dataFilePath, os.O_RDONLY, 0644)
	if err != nil {
		return "", nil, err
	}

	return version, file, nil
}

func (fetcher *FileVersionedDataFetcher) GetOriginDataWithVersion(version string) (string, io.ReadCloser, error) {
	dataFilePath := fmt.Sprintf(fetcher.dataFilePathPattern, version)
	file, err := os.OpenFile(dataFilePath, os.O_RDONLY, 0644)
	if err != nil {
		return "", nil, err
	}
	return version, file, nil
}

func (fetcher *FileVersionedDataFetcher) RegisterObserver(intervalSeconds int, observer DataProviderObserver) {
	fetcher.observer = observer
	fetcher.intervalSeconds = intervalSeconds
}

func (fetcher *FileVersionedDataFetcher) loop() {
	reloadTicker := time.NewTicker(time.Second * time.Duration(fetcher.intervalSeconds))

	for {
		select {
		case <-fetcher.term:
			return
		case <-reloadTicker.C:
			if err := fetcher.reload(); err != nil {
				zap.L().Error("[FileVersionedDataFetcher] reload failed, data:, err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", fetcher.originVersionFilePath)))), zap.Error(err))
			}
		}
	}
}

func (fetcher *FileVersionedDataFetcher) reload() error {
	version, err := fetcher.GetOriginVersion()
	if err != nil {
		return err
	}

	if version == fetcher.localVersionProvider.GetVersion() {
		return nil
	}

	dataFilePath := fmt.Sprintf(fetcher.dataFilePathPattern, version)
	dataFile, err := os.OpenFile(dataFilePath, os.O_RDONLY, 0644)
	if err != nil {
		return err
	}

	if fetcher.observer != nil {
		return fetcher.observer(version, dataFile)
	} else {
		dataFile.Close()
		return nil
	}
}

type HttpVersionedDataFetcher struct {
	versionURL           string
	dataURLPattern       string
	localVersionProvider LocalVersionProvider
	observer             DataProviderObserver
	intervalSeconds      int
	client               *http.Client
	term                 chan struct{}
}

func NewHttpVersionedDataFetcher(versionURL string) *HttpVersionedDataFetcher {
	// remove the filename part in the url
	parsedUrl, err := url.Parse(versionURL)
	if err != nil {
		panic(err)
	}

	parsedUrl.Path = path.Dir(parsedUrl.Path)
	pattern := parsedUrl.String()
	if strings.HasSuffix(pattern, "/") {
		pattern += "%s"
	} else {
		pattern += "/%s"
	}

	return &HttpVersionedDataFetcher{
		versionURL:           versionURL,
		dataURLPattern:       pattern,
		intervalSeconds:      300,
		localVersionProvider: &NullLocalVersionProvider{},
		client:               &http.Client{},
		term:                 make(chan struct{}),
	}
}

func (fetcher *HttpVersionedDataFetcher) Start() error {
	go fetcher.loop()
	return nil
}

func (fetcher *HttpVersionedDataFetcher) Stop() {
	close(fetcher.term)
}

func (fetcher *HttpVersionedDataFetcher) RegisterLocalVersionProvider(provider LocalVersionProvider) {
	fetcher.localVersionProvider = provider
}

func (fetcher *HttpVersionedDataFetcher) SetDataURLPattern(pattern string) {
	fetcher.dataURLPattern = pattern
}

func (fetcher *HttpVersionedDataFetcher) GetOriginVersion() (string, error) {
	zap.L().Info("[HttpVersionedDataFetcher] versionURL", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", fetcher.versionURL)))))

	resp, err := fetcher.client.Get(fetcher.versionURL)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	version, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(string(version)), nil
}

func (fetcher *HttpVersionedDataFetcher) GetOriginDataHead() (string, io.ReadCloser, error) {
	version, err := fetcher.GetOriginVersion()
	if err != nil {
		return "", nil, err
	}

	dataURL := fmt.Sprintf(fetcher.dataURLPattern, version)

	zap.L().Info("[HttpVersionedDataFetcher] GetOriginDataHead dataURL", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", dataURL)))))
	resp, err := fetcher.client.Get(dataURL)
	if err != nil {
		return "", nil, err
	}

	return version, resp.Body, nil
}

func (fetcher *HttpVersionedDataFetcher) GetOriginDataWithVersion(version string) (string, io.ReadCloser, error) {
	dataURL := fmt.Sprintf(fetcher.dataURLPattern, version)

	zap.L().Info("[HttpVersionedDataFetcher] GetOriginDataWithVersion dataURL", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", dataURL)))))
	resp, err := fetcher.client.Get(dataURL)
	if err != nil {
		return "", nil, err
	}

	return version, resp.Body, nil
}

func (fetcher *HttpVersionedDataFetcher) RegisterObserver(intervalSeconds int, observer DataProviderObserver) {
	fetcher.observer = observer
	fetcher.intervalSeconds = intervalSeconds
}

func (fetcher *HttpVersionedDataFetcher) loop() {
	reloadTicker := time.NewTicker(time.Second * time.Duration(fetcher.intervalSeconds))

	for {
		select {
		case <-fetcher.term:
			return
		case <-reloadTicker.C:
			if err := fetcher.reload(); err != nil {
				zap.L().Error("[HttpVersionedDataFetcher] reload failed, version URL: , err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", fetcher.versionURL)))), zap.Error(err))
			}
		}
	}
}

func (fetcher *HttpVersionedDataFetcher) reload() error {
	version, err := fetcher.GetOriginVersion()
	if err != nil {
		return err
	}

	if version == fetcher.localVersionProvider.GetVersion() {
		//zap.L().Info("[HttpVersionedDataFetcher] version is the same, no need to reload, url:, version", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", fetcher.versionURL)))), zap.String("param2", fmt.Sprintf("%v", version)))
		return nil
	}

	_, dataReader, err := fetcher.GetOriginDataWithVersion(version)
	if err != nil {
		return err
	}

	if fetcher.observer != nil {
		return fetcher.observer(version, dataReader)
	} else {
		dataReader.Close()
		return nil
	}
}

func CreateVersionedDataFetcher(path string) (VersionedDataFetcher, error) {
	if strings.HasPrefix(path, "http://") || strings.HasPrefix(path, "https://") {
		// Create HTTP fetcher
		return NewHttpVersionedDataFetcher(path), nil
	} else {
		// Create File fetcher
		return NewFileVersionedDataFetcher(path), nil
	}
}
