package redis_client

import (
	"context"
	"fmt"
	redisclient "github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"strings"
	"time"
)

type RedisClusterClient struct {
	address []string
	client  *redisclient.ClusterClient
}

func NewRedisClusterClient(address []string) *RedisClusterClient {
	return &RedisClusterClient{address: address}
}

func (r *RedisClusterClient) Start() error {
	r.client = redisclient.NewClusterClient(&redisclient.ClusterOptions{
		Addrs:          r.address,
		MaxRedirects:   -1,
		MaxRetries:     -1,
		MinIdleConns:   0,
		MaxIdleConns:   128,
		MaxActiveConns: 4096,
		DialTimeout:    time.Millisecond * 10,
		ReadTimeout:    time.Millisecond * 5,
		WriteTimeout:   time.Millisecond * 5,
		PoolTimeout:    time.Millisecond * 5,
	})

	if err := r.client.Ping(context.Background()).Err(); err != nil {
		return fmt.Errorf("redis cluster ping error: %v", err)
	}

	nodesInfo, err := r.client.ClusterNodes(context.Background()).Result()
	if err != nil {
		return fmt.Errorf("redis cluster nodes error: %v", err)
	}

	// split by line and logging
	nodesInfoList := strings.Split(nodesInfo, "\n")
	for _, nodeInfo := range nodesInfoList {
		if len(nodeInfo) == 0 {
			continue
		}
		zap.L().Info("[RedisClusterUserSegmentClient] nodeInfo", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", nodeInfo)))))
	}

	return nil
}

func (r *RedisClusterClient) Stop() error {
	return r.client.Close()
}

func (r *RedisClusterClient) Do(ctx context.Context, args ...interface{}) (interface{}, error) {
	return r.client.Do(ctx, args).Result()
}

func (r *RedisClusterClient) EvalSha(ctx context.Context, sha1 string, keys []string, args ...interface{}) (interface{}, error) {
	return r.client.EvalSha(ctx, sha1, keys, args...).Result()
}

func (r *RedisClusterClient) ForEachShard(ctx context.Context,
	fn func(ctx context.Context, client *redisclient.Client) error) error {
	return r.client.ForEachShard(ctx, fn)
}

func (r *RedisClusterClient) Set(key string, value interface{}, expiration time.Duration) error {
	return r.client.Set(context.Background(), key, value, expiration).Err()
}

func (r *RedisClusterClient) Get(ctx context.Context, key string) *redisclient.StringCmd {
	return r.client.Get(context.Background(), key)
}

func (r *RedisClusterClient) GetString(key string) (string, error) {
	return r.client.Get(context.Background(), key).Result()
}

func (r *RedisClusterClient) GetBytes(key string) ([]byte, error) {
	return r.client.Get(context.Background(), key).Bytes()
}

func (r *RedisClusterClient) Del(key string) error {
	return r.client.Del(context.Background(), key).Err()
}

func (r *RedisClusterClient) HGetAll(key string) (map[string]string, error) {
	return r.client.HGetAll(context.Background(), key).Result()
}

func (r *RedisClusterClient) Pipeline() redisclient.Pipeliner {
	return r.client.Pipeline()
}
