package star_tree

import (
	"archive/tar"
	"bufio"
	"compress/gzip"
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"unsafe"

	"go.uber.org/zap"
)

var (
	StarKey            = "*"
	StarKeyAlternative = "-1"
)

type StBuilderNode struct {
	Parent  *StBuilderNode
	NodeKey uint32

	Children []*StBuilderNode
	Values   []uint64

	Depth            uint32
	TotalLeaf        uint32
	TotalChild       uint32
	NodeBytes        uint64
	NodeOffsetBytes  uint64
	ValueBytes       uint64
	ValueOffsetBytes uint64
}

type StBuilder struct {
	FieldDesc []*StFieldDesc
	ValueDesc []*StValueDesc
	Root      *StBuilderNode

	totalNodeBytes  uint64
	totalValueBytes uint64
}

func NewStBuilder() *StBuilder {
	return &StBuilder{
		Root: &StBuilderNode{
			Depth:   0,
			NodeKey: 0,
		},
	}
}

func (builder *StBuilder) AddField(fieldName string, fieldType FieldType, hasStarNode bool) {
	field := &StFieldDesc{
		FieldId:            uint32(len(builder.FieldDesc)),
		FieldName:          fieldName,
		FieldType:          fieldType,
		HasStarNode:        hasStarNode,
		StringValueMapping: make(map[string]uint32),
	}
	if hasStarNode {
		if _, err := field.GetKeyIdForBuilder(StarKey); err != nil {
			panic(err)
		}
	}

	builder.FieldDesc = append(builder.FieldDesc, field)
}

func (builder *StBuilder) AddValue(valueName string) {
	builder.ValueDesc = append(builder.ValueDesc, &StValueDesc{
		ValueId:   uint32(len(builder.ValueDesc)),
		ValueName: valueName,
	})
}

func (builder *StBuilder) MustAddLeaf(keys []string, values []uint64) {
	err := builder.AddLeaf(keys, values)
	if err != nil {
		panic(err)
	}
}

func (builder *StBuilder) AddLeaf(keys []string, values []uint64) error {
	if len(keys) != len(builder.FieldDesc) {
		return ErrFieldKeysNotMatch
	}

	if len(values) != len(builder.ValueDesc) {
		return ErrValueKeysNotMatch
	}

	node := builder.Root
	for i, fieldDesc := range builder.FieldDesc {
		key := keys[i]
		fieldKeyId, err := fieldDesc.GetKeyIdForBuilder(key)
		if err != nil {
			return err
		}

		child, err := fieldDesc.GetChildForBuilder(node, fieldKeyId)
		if err != nil {
			return err
		}

		if i == len(builder.FieldDesc)-1 {
			child.Values = values
		}

		node = child
	}

	return nil
}

func (builder *StBuilder) Prepare() error {
	// calculate total leaf and node
	zap.L().Info("[StBuilder] Start to prepare star tree")
	err := builder.DeepFirstVisitLeafToRoot(builder.Root, func(node *StBuilderNode) error {
		if builder.IsLeaf(node) {
			node.TotalLeaf = 1
		}

		if builder.IsLeaf(node) && len(node.Values) != len(builder.ValueDesc) {
			return fmt.Errorf("values not match, %s", builder.DumpPath(node))
		}

		if !builder.IsLeaf(node) && len(node.Children) == 0 {
			return fmt.Errorf("path broken, %s", builder.DumpPath(node))
		}

		sort.Slice(node.Children, func(i, j int) bool {
			return node.Children[i].NodeKey < node.Children[j].NodeKey
		})

		node.TotalChild = uint32(len(node.Children))
		for _, child := range node.Children {
			node.TotalLeaf += child.TotalLeaf
			node.TotalChild += child.TotalChild
		}
		return nil
	})

	if err != nil {
		return err
	}

	zap.L().Info("[StBuilder] TotalLeaf: , TotalChild", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(builder.Root.TotalLeaf)))), zap.Int64("param2", int64(builder.Root.TotalChild)))

	// mark slot
	builder.totalNodeBytes, builder.totalValueBytes = builder.markByteOffset(builder.Root, 0, 0)
	zap.L().Info("[StBuilder] TotalNodeBytes: , TotalValueBytes", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(builder.totalNodeBytes)))), zap.Int64("param2", int64(builder.totalValueBytes)))

	return nil
}

func (builder *StBuilder) BuildNodes() []byte {
	nodes := make([]byte, builder.totalNodeBytes)
	builder.DeepFirstVisitRootToLeaf(builder.Root, func(node *StBuilderNode) {
		if len(node.Children) == 0 {
			return
		}

		// write node
		nodePtr := uintptr(unsafe.Pointer(&nodes[node.NodeOffsetBytes]))
		*(*uint32)(unsafe.Pointer(nodePtr)) = uint32(len(node.Children))
		nodePtr += unsafe.Sizeof(uint32(0))

		for _, child := range node.Children {
			if builder.IsLeaf(child) {
				*(*StNodeItem)(unsafe.Pointer(nodePtr)) = StNodeItem{
					NodeKey:         child.NodeKey,
					NodeOffsetBytes: child.ValueOffsetBytes,
				}
			} else {
				*(*StNodeItem)(unsafe.Pointer(nodePtr)) = StNodeItem{
					NodeKey:         child.NodeKey,
					NodeOffsetBytes: child.NodeOffsetBytes,
				}
			}
			nodePtr += unsafe.Sizeof(StNodeItem{})
		}
	})

	return nodes
}

func (builder *StBuilder) BuildValues() []byte {
	values := make([]byte, builder.totalValueBytes)
	builder.DeepFirstVisitRootToLeaf(builder.Root, func(node *StBuilderNode) {
		if len(node.Values) == 0 {
			return
		}

		valuePtr := uintptr(unsafe.Pointer(&values[node.ValueOffsetBytes]))
		for _, value := range node.Values {
			*(*uint64)(unsafe.Pointer(valuePtr)) = value
			valuePtr += unsafe.Sizeof(uint64(0))
		}
	})

	return values
}

func (builder *StBuilder) SaveSt(dir string) error {
	if err := builder.Prepare(); err != nil {
		return err
	}

	nodes := builder.BuildNodes()
	values := builder.BuildValues()

	st := NewStarTree(builder.FieldDesc, builder.ValueDesc, nodes, values)
	return st.Save(dir)
}

func (builder *StBuilder) CreateSt() (*StarTree, error) {
	if err := builder.Prepare(); err != nil {
		return nil, err
	}

	nodes := builder.BuildNodes()
	values := builder.BuildValues()

	st := NewStarTree(builder.FieldDesc, builder.ValueDesc, nodes, values)
	return st, nil
}

func (builder *StBuilder) LoadCsv(csvPath string) error {
	if strings.HasPrefix(csvPath, "http") {
		return builder.LoadHttpCsv(csvPath)
	} else {
		return builder.LoadFileCsv(csvPath)
	}
}

func (builder *StBuilder) LoadFileCsv(csvPath string) error {
	zap.L().Info("[StBuilder][LoadFileCsv] start loading csv file", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", csvPath)))))

	file, err := os.Open(csvPath)
	if err != nil {
		return err
	}
	defer file.Close()

	if err := builder.LoadCsvFromReader(file); err != nil {
		return err
	}

	zap.L().Info("[StBuilder] start loading finished, file", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", csvPath)))))
	return nil
}

func (builder *StBuilder) LoadHttpCsv(url string) error {
	zap.L().Info("[StBuilder][LoadHttpCsv] start loading csv url", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", url)))))

	response, err := http.Get(url)
	if err != nil {
		return err
	}

	defer response.Body.Close()

	if response.StatusCode != 200 {
		return fmt.Errorf("http status code:%d", response.StatusCode)
	}

	if err := builder.LoadCsvFromReader(response.Body); err != nil {
		return err
	}

	zap.L().Info("[StBuilder] start loading finished, url", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", url)))))
	return nil
}

func (builder *StBuilder) LoadTarFromReader(r io.Reader, targetCsvFile string) error {
	// 创建 gzip 解压缩器
	gzr, err := gzip.NewReader(r)
	if err != nil {
		zap.L().Error("[LoadTarFromReader] Failed to create gzip reader", zap.Error(err))
		return err
	}
	defer gzr.Close()

	// 创建 tar 读取器
	tr := tar.NewReader(gzr)

	// 遍历 tar 中的所有文件
	for {
		header, err := tr.Next()
		if err == io.EOF {
			return fmt.Errorf("file not found:%s", targetCsvFile)
		}

		if err != nil {
			return fmt.Errorf("failed to read tar file: %v", err)
		}

		// 只处理特定的文件名
		if header.Name == targetCsvFile {
			zap.L().Info("[LoadTarFromReader] Found target file", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", header.Name)))))

			if err := builder.LoadCsvFromReader(tr); err != nil {
				return err
			} else {
				return nil
			}
		}
	}
}

func (builder *StBuilder) LoadCsvFromReader(r io.Reader) error {
	reader := csv.NewReader(r)
	reader.Comma = '\t'

	header, err := reader.Read()
	if err != nil {
		return err
	}

	if err := builder.processCsvHeader(header); err != nil {
		return err
	}

	for _, field := range builder.FieldDesc {
		zap.L().Info("[StBuilder][loadCsv] field:%s, type", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(field.FieldName)))), zap.Int64("param2", int64(field.FieldType)))
	}
	for _, value := range builder.ValueDesc {
		zap.L().Info("[StBuilder][loadCsv] value", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", value.ValueName)))))
	}

	rowCount := 0
	for {
		data, err := reader.Read()
		if err != nil {
			if errors.Is(err, io.EOF) {
				break
			}
			return err
		}

		keys := data[:len(builder.FieldDesc)]
		values := make([]uint64, len(builder.ValueDesc))
		for i, valueStr := range data[len(builder.FieldDesc):] {
			value, err := strconv.ParseInt(valueStr, 10, 64)
			if err != nil {
				return err
			}

			values[i] = uint64(value)
		}

		if err := builder.AddLeaf(keys, values); err != nil {
			return err
		}

		rowCount++
		if rowCount%10000 == 0 {
			zap.L().Info("[StBuilder] loaded row", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(rowCount)))))
		}
	}

	return nil
}

func (builder *StBuilder) LoadFeaturedDataFile(dir string) error {
	featureFile, err := os.Open(filepath.Join(dir, "feature.txt"))
	if err != nil {
		return err
	}
	defer featureFile.Close()

	header := make([]string, 0)
	scanner := bufio.NewScanner(featureFile)

	for scanner.Scan() {
		header = append(header, scanner.Text())
	}

	if err := builder.processCsvHeader(header); err != nil {
		return err
	}

	dataFile, err := os.Open(filepath.Join(dir, "st_model.txt"))
	if err != nil {
		return err
	}
	defer dataFile.Close()

	reader := csv.NewReader(dataFile)
	reader.Comma = '\t'

	for {
		data, err := reader.Read()
		if err != nil {
			if errors.Is(err, io.EOF) {
				break
			}
			return err
		}

		keys := data[:len(builder.FieldDesc)]
		values := make([]uint64, len(builder.ValueDesc))
		for i, valueStr := range data[len(builder.FieldDesc):] {
			value, err := strconv.ParseInt(valueStr, 10, 64)
			if err != nil {
				return err
			}

			values[i] = uint64(value)
		}

		if err := builder.AddLeaf(keys, values); err != nil {
			return err
		}
	}

	return nil
}

func (builder *StBuilder) processCsvHeader(header []string) error {
	for _, field := range header {
		if strings.HasPrefix(field, "f_") {
			parts := strings.SplitN(strings.TrimPrefix(field, "f_"), "_", 2)
			fieldType := parts[0]
			switch fieldType {
			case "id":
				if len(parts) != 2 {
					return fmt.Errorf("field id format error")
				}

				fieldName := parts[1]
				builder.AddField(fieldName, FieldTypeStringMapping, true)
			case "range":
				if len(parts) != 2 {
					return fmt.Errorf("field id format error")
				}

				fieldName := parts[1]
				builder.AddField(fieldName, FieldTypeRange, false)
			case "weight":
				if len(parts) == 2 {
					fieldName := parts[1]
					builder.AddField(fieldName, FieldTypeWeight, false)
				} else {
					builder.AddField("weight", FieldTypeWeight, false)
				}
			default:
				return fmt.Errorf("unknown field type:%s", fieldType)
			}
		} else if strings.HasPrefix(field, "v_") {
			valueName := strings.TrimPrefix(field, "v_")
			builder.AddValue(valueName)
		}
	}

	return nil
}

func (builder *StBuilder) DeepFirstVisitLeafToRoot(node *StBuilderNode, visit func(*StBuilderNode) error) error {
	for _, child := range node.Children {
		if err := builder.DeepFirstVisitLeafToRoot(child, visit); err != nil {
			return err
		}
	}
	return visit(node)
}

func (builder *StBuilder) markByteOffset(node *StBuilderNode, currentNodeOffset uint64, currentValueOffset uint64) (uint64, uint64) {
	node.NodeOffsetBytes = currentNodeOffset
	node.ValueOffsetBytes = currentValueOffset

	if len(node.Children) > 0 {
		node.NodeBytes = GetBytesNeededForStNode(uint32(len(node.Children)))
		currentNodeOffset += node.NodeBytes
	}

	if len(node.Values) > 0 {
		node.ValueBytes = GetBytesNeededForStValue(uint32(len(node.Values)))
		currentValueOffset += node.ValueBytes
	}

	for _, child := range node.Children {
		currentNodeOffset, currentValueOffset = builder.markByteOffset(child, currentNodeOffset, currentValueOffset)
	}

	return currentNodeOffset, currentValueOffset
}

func (builder *StBuilder) DeepFirstVisitRootToLeaf(node *StBuilderNode, visit func(*StBuilderNode)) {
	visit(node)
	for _, child := range node.Children {
		builder.DeepFirstVisitRootToLeaf(child, visit)
	}
}

func (builder *StBuilder) DumpTree() {
	builder.dumpNode(builder.Root, 0)
}

func (builder *StBuilder) dumpNode(node *StBuilderNode, depth int) {
	for i := 0; i < depth; i++ {
		fmt.Print("  ")
	}
	fmt.Printf("Depth:%d, NodeKey: %d, NodeValues: %v, TotalChild: %d, TotalLeaf: %d, NodeOffset:%d, ValueOffset:%d\n",
		node.Depth,
		node.NodeKey,
		node.Values,
		node.TotalChild,
		node.TotalLeaf,
		node.NodeOffsetBytes,
		node.ValueOffsetBytes)

	for _, child := range node.Children {
		builder.dumpNode(child, depth+1)
	}
}

func (builder *StBuilder) IsLeaf(node *StBuilderNode) bool {
	return node.Depth == uint32(len(builder.FieldDesc))
}

func (builder *StBuilder) DumpPath(node *StBuilderNode) string {
	path := ""
	for node != nil {
		current := ""
		if node.Depth >= uint32(len(builder.FieldDesc)) {
			current = "leaf"
		} else {
			fieldDesc := builder.FieldDesc[node.Depth]
			current = fmt.Sprintf("%s(%d)", fieldDesc.FieldName, node.NodeKey)
		}

		if path == "" {
			path = current
		} else {
			path = fmt.Sprintf("%s/%s", current, path)
		}
	}
	return path
}
