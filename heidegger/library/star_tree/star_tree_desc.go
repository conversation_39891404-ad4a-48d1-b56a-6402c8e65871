package star_tree

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"unsafe"
)

type FieldType uint32

const (
	FieldTypeStringMapping FieldType = iota
	FieldTypeRange
	FieldTypeWeight
)

// StNode as cpp
//
//	struct StNode {
//		uint32 childrenCount;
//	    StNodeItem items[0];
//	}
type StNode struct {
	ptr unsafe.Pointer
}

func (stn StNode) ChildrenCount() uint32 {
	return *(*uint32)(stn.ptr)
}

func (stn StNode) GetItem(index uint32) StNodeItem {
	if index >= stn.ChildrenCount() {
		panic("index out of range")
	}
	// Calculate the position of the item
	itemPtr := uintptr(stn.ptr) + uintptr(unsafe.Sizeof(uint32(0))) + uintptr(index)*uintptr(unsafe.Sizeof(StNodeItem{}))
	return *(*StNodeItem)(unsafe.Pointer(itemPtr))
}

func (stn StNode) Diagnostic() string {
	result := fmt.Sprintf("ChildrenCount: %d", stn.ChildrenCount())
	for i := uint32(0); i < stn.ChildrenCount(); i++ {
		item := stn.GetItem(i)
		result += fmt.Sprintf(", [NodeKey: %d, NodeOffsetBytes: %d]", item.NodeKey, item.NodeOffsetBytes)
	}
	return result
}

func GetBytesNeededForStNode(itemCount uint32) uint64 {
	return uint64(unsafe.Sizeof(uint32(0))) + uint64(unsafe.Sizeof(StNodeItem{}))*uint64(itemCount)
}

func GetBytesNeededForStValue(valueCount uint32) uint64 {
	return uint64(unsafe.Sizeof(uint64(0))) * uint64(valueCount)
}

type StNodeItem struct {
	NodeKey         uint32
	NodeOffsetBytes uint64
}

type StHeader struct {
	FieldsDesc []*StFieldDesc `json:"fields_desc"`
	ValuesDesc []*StValueDesc `json:"values_desc"`

	NodeBytes  uint64 `json:"node_bytes"`
	ValueBytes uint64 `json:"value_bytes"`
}

type StarTree struct {
	StHeader

	nodes  []byte
	values []byte
}

func NewStarTree(FieldsDesc []*StFieldDesc, ValuesDesc []*StValueDesc, nodes []byte, values []byte) *StarTree {
	return &StarTree{
		StHeader: StHeader{
			FieldsDesc: FieldsDesc,
			ValuesDesc: ValuesDesc,
			NodeBytes:  uint64(len(nodes)),
			ValueBytes: uint64(len(values)),
		},
		nodes:  nodes,
		values: values,
	}
}

func (st *StarTree) Init() error {
	for _, desc := range st.FieldsDesc {
		if err := desc.Init(); err != nil {
			return err
		}
	}
	return nil
}

func (st *StarTree) GetFieldNameList() []string {
	result := make([]string, len(st.FieldsDesc))
	for i, fieldDesc := range st.FieldsDesc {
		result[i] = fieldDesc.FieldName
	}
	return result
}

func LoadStarTree(dir string) (*StarTree, error) {
	st := &StarTree{}
	if err := st.Load(dir); err != nil {
		return nil, err
	}
	return st, nil
}

func (st *StarTree) Load(dir string) error {
	if err := st.LoadDescFile(filepath.Join(dir, "st_desc.json")); err != nil {
		return err
	}

	if err := st.LoadIndexFile(filepath.Join(dir, "st_index.bin")); err != nil {
		return err
	}

	return nil
}

func (st *StarTree) LoadDescFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}

	defer file.Close()

	return json.NewDecoder(file).Decode(&st.StHeader)
}

func (st *StarTree) LoadIndexFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}

	defer file.Close()

	st.nodes = make([]byte, st.NodeBytes)
	if _, err := file.Read(st.nodes); err != nil {
		return err
	}

	st.values = make([]byte, st.ValueBytes)
	if _, err := file.Read(st.values); err != nil {
		return err
	}

	return nil
}

func (st *StarTree) Save(dir string) error {
	if err := st.SaveDescFile(filepath.Join(dir, "st_desc.json")); err != nil {
		return err
	}

	if err := st.SaveIndexFile(filepath.Join(dir, "st_index.bin")); err != nil {
		return err
	}

	return nil
}

func (st *StarTree) SaveDescFile(filename string) error {
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}

	defer file.Close()

	return json.NewEncoder(file).Encode(st.StHeader)
}

func (st *StarTree) SaveIndexFile(filename string) error {
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}

	defer file.Close()

	if _, err := file.Write(st.nodes); err != nil {
		return err
	}

	if _, err := file.Write(st.values); err != nil {
		return err
	}

	return nil
}

func (st *StarTree) Root() StNode {
	return st.getNode(0)
}

func (st *StarTree) Node(offset uint64) StNode {
	return st.getNode(offset)
}

func (st *StarTree) getNode(offset uint64) StNode {
	return StNode{unsafe.Pointer(&st.nodes[offset])}
}

func (st *StarTree) Value(offset uint64) []uint64 {
	count := len(st.ValuesDesc)
	sliceHeader := &reflect.SliceHeader{
		Data: uintptr(unsafe.Pointer(&st.values[offset])),
		Len:  int(count),
		Cap:  int(count),
	}
	return *(*[]uint64)(unsafe.Pointer(sliceHeader))
}

type StarTreeQuery struct {
	starTree      *StarTree
	currentNode   StNode
	currentField  uint32
	values        []uint64
	err           error
	starNodeCount int
}

func NewStarTreeQuery(starTree *StarTree) *StarTreeQuery {
	return &StarTreeQuery{
		starTree:    starTree,
		currentNode: starTree.Root(),
	}
}

func (q *StarTreeQuery) QueryString(value string) *StarTreeQuery {
	if q.IsError() {
		return q
	}

	if q.currentField >= uint32(len(q.starTree.FieldsDesc)) {
		q.SetError(ErrTooManyFields)
		return q
	}

	fieldDesc := q.starTree.FieldsDesc[q.currentField]
	if fieldDesc.FieldType != FieldTypeStringMapping {
		q.SetError(ErrFieldTypeNotMatch)
		return q
	}

	fieldKeyId, err := fieldDesc.GetKeyIdForQuery(value)
	if err != nil {
		q.SetError(err)
		return q
	}

	if fieldDesc.HasStarNode && fieldKeyId == 0 {
		q.starNodeCount++
	}

	return q.doQuery(fieldKeyId, fieldDesc)
}

func (q *StarTreeQuery) QueryUint32(value uint32) *StarTreeQuery {
	if q.IsError() {
		return q
	}

	if q.currentField >= uint32(len(q.starTree.FieldsDesc)) {
		q.SetError(ErrTooManyFields)
		return q
	}

	fieldKeyId := value

	fieldDesc := q.starTree.FieldsDesc[q.currentField]
	if fieldDesc.FieldType != FieldTypeRange && fieldDesc.FieldType != FieldTypeWeight {
		q.SetError(ErrFieldTypeNotMatch)
		return q
	}

	if fieldDesc.HasStarNode && fieldKeyId == 0 {
		q.starNodeCount++
	}

	return q.doQuery(fieldKeyId, fieldDesc)
}

func (q *StarTreeQuery) doQuery(fieldKeyId uint32, fieldDesc *StFieldDesc) *StarTreeQuery {
	nodeItem, err := fieldDesc.SearchNodeForQuery(&q.currentNode, fieldKeyId)
	if err != nil {
		q.SetError(err)
		return q
	}

	//zap.L().Info("currentField: %s(), fieldKeyId: , nodeItem.NodeOffsetBytes", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(//	fieldDesc.FieldName)))), zap.Int64("param2", int64(//	q.currentField)), zap.Int64("id", int64(//	fieldKeyId)), zap.Int64("param4", int64(//	nodeItem.NodeOffsetBytes)))

	if q.currentField != uint32(len(q.starTree.FieldsDesc))-1 {
		q.currentNode = q.starTree.Node(nodeItem.NodeOffsetBytes)
	} else {
		q.values = q.starTree.Value(nodeItem.NodeOffsetBytes)
	}

	q.currentField++

	return q
}

func (q *StarTreeQuery) VisitNodes(visitor func(intKey uint32, stringKey string, values []uint64) bool) {
	isChildLeaf := q.IsChildLeaf()

	nodeCount := q.currentNode.ChildrenCount()
	for i := uint32(0); i < nodeCount; i++ {
		item := q.currentNode.GetItem(i)
		var values []uint64
		if isChildLeaf {
			values = q.starTree.Value(item.NodeOffsetBytes)
		}

		if !visitor(
			item.NodeKey,
			q.starTree.FieldsDesc[q.currentField].mappingBack[item.NodeKey],
			values) {
			break
		}
	}
}

func (q *StarTreeQuery) IsLeaf() bool {
	return q.currentField == uint32(len(q.starTree.FieldsDesc))
}

func (q *StarTreeQuery) IsChildLeaf() bool {
	return q.currentField == uint32(len(q.starTree.FieldsDesc))-1
}

func (q *StarTreeQuery) Values() []uint64 {
	return q.values
}

func (q *StarTreeQuery) IsError() bool {
	return q.err != nil
}

func (q *StarTreeQuery) SetError(err error) {
	q.err = err
}

func (q *StarTreeQuery) Error() error {
	return q.err
}

func (q *StarTreeQuery) GetFieldName() string {
	if q.currentField >= uint32(len(q.starTree.FieldsDesc)) {
		return ""
	}
	return q.starTree.FieldsDesc[q.currentField].FieldName
}

func (q *StarTreeQuery) GetStarNodeCount() int {
	return q.starNodeCount
}

func (q *StarTreeQuery) Clone() *StarTreeQuery {
	return &StarTreeQuery{
		starTree:      q.starTree,
		currentNode:   q.currentNode,
		currentField:  q.currentField,
		values:        q.values,
		err:           q.err,
		starNodeCount: q.starNodeCount,
	}
}
