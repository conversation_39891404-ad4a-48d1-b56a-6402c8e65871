package main

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/mclock"
	"gitlab.com/dev/heidegger/library/rapid_rpc"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"sync/atomic"
	"time"
)

var beginTime uint64
var totalLat uint64
var totalCount uint64

func main() {
	go func() {
		for {
			time.Sleep(time.Second)
			lat := atomic.LoadUint64(&totalLat)
			count := atomic.LoadUint64(&totalCount)
			if count == 0 {
				continue
			}
			fmt.Printf("avgLat:%d\n", lat/count)
			fmt.Println("qps:%d", uint64(count)/uint64((uint64(mclock.Now())-beginTime)/1e9))
		}
	}()

	// logrus.SetLevel converted - configure zap logger instead

	client := rapid_rpc.NewClient(rapid_rpc.ClientOption{ClientCount: 2})
	if err := client.Connect(0, "127.0.0.1:8787"); err != nil {
		panic(err)
	}
	if err := client.Connect(0, "127.0.0.1:8788"); err != nil {
		panic(err)
	}

	beginTime = uint64(mclock.Now())
	for i := 0; i != 10000000; i++ {
		data := time.Now().Format("2006-01-02 15:04:05")
		ctx, _ := context.WithTimeout(context.Background(), time.Second)

		timeStart := mclock.Now()
		response, err := client.Send(ctx, 0, 1, type_convert.UnsafeStringToByte(data))
		if err != nil {
			zap.L().Error("send err ", zap.Error(err))
		} else {
			zap.L().Info("send success, response", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", type_convert.UnsafeByteToString(response.Get()))))))
		}
		timeSend := mclock.Now()
		atomic.AddUint64(&totalLat, uint64(timeSend-timeStart))
		atomic.AddUint64(&totalCount, 1)

		if i%1000 == 0 {
			zap.L().Info("", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(i)))))
		}
	}
}
