package rapid_rpc

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"runtime"
)

type ClientOption struct {
	ClientCount        int
	TimeoutMillisecond int
}

func (opt *ClientOption) Fix() {
	if opt.ClientCount == 0 {
		opt.ClientCount = runtime.NumCPU()
	}

	if opt.TimeoutMillisecond == 0 {
		opt.TimeoutMillisecond = 5
	}
}

type Client struct {
	clients []*RoundRobinClient
	option  ClientOption
}

func NewClient(opt ClientOption) *Client {
	opt.Fix()

	client := &Client{
		clients: make([]*RoundRobinClient, 0),
		option:  opt,
	}

	return client
}

func (client *Client) createRoundRobinClient() *RoundRobinClient {
	return newRoundRobinClient(client.option)
}

func (client *Client) createAddrClient(address string) *AddrClient {
	return newAddrClient(address, client.option)
}

func (client *Client) deepcopyClients() []*RoundRobinClient {
	// deep copy clients
	copyClients := make([]*RoundRobinClient, len(client.clients))
	for idx, hashClient := range client.clients {
		copyClients[idx] = client.createRoundRobinClient()
		copyClients[idx].copyFrom(hashClient)
	}
	return copyClients
}

func (client *Client) Connect(hashIndex int, address ...string) error {
	for _, addr := range address {
		if err := client.addClient(hashIndex, addr); err != nil {
			return fmt.Errorf("[Client][Connect] fail, %s", err)
		}
	}

	return nil
}

func (client *Client) addClient(hashIndex int, instance string) error {
	if hashIndex < 0 {
		return fmt.Errorf("invalid hash index")
	}

	if len(client.clients) < hashIndex && client.clients[hashIndex] != nil {
		return fmt.Errorf("duplicate hash index, %d", hashIndex)
	}

	zap.L().Debug("client addClient instance %s, hash", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(instance)))), zap.Int64("param2", int64(hashIndex)))

	for hashIndex >= len(client.clients) {
		client.clients = append(client.clients, client.createRoundRobinClient())
	}

	if err := client.clients[hashIndex].addClient(client.createAddrClient(instance)); err != nil {
		return fmt.Errorf("[Client][addClient] fail, %s", err)
	}

	return nil
}

func (client *Client) Send(ctx context.Context, hashIdx int, command uint16, data []byte) (buffer_pool.Buffer, error) {
	hashClients := client.clients
	if hashIdx < 0 || hashIdx >= len(hashClients) {
		return buffer_pool.Buffer{}, fmt.Errorf("RadpiClient Send with invalid hashIdx:%d", hashIdx)
	}

	resultFrame, err := client.clients[hashIdx].Send(ctx, command, data)
	if err != nil {
		return buffer_pool.Buffer{}, err
	}

	return resultFrame, nil
}

func (client *Client) GetClientSize() int {
	return len(client.clients)
}

func (client *Client) Close() {
	for _, hashClient := range client.clients {
		hashClient.Close()
	}
}
