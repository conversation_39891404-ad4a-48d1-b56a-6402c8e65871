package rapid_rpc

import (
	"github.com/panjf2000/ants/v2"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"net"
	"fmt"
)

type TransportHandler func(t *Transport, header FrameHeader, data buffer_pool.Buffer)

type Transport struct {
	framer     Framer
	conn       net.Conn
	handler    TransportHandler
	worker     *ants.Pool
	workerSize int
}

func NewTransport(workerSize int, conn net.Conn, handler TransportHandler) *Transport {
	return &Transport{
		conn:       conn,
		framer:     Framer{r: conn, w: conn},
		handler:    handler,
		workerSize: workerSize,
	}
}

func (t *Transport) Start() {
	t.worker, _ = ants.NewPool(t.workerSize, ants.WithNonblocking(false))
}

func (t *Transport) Stop() {
	if t.worker != nil {
		t.worker.Release()
	}
}

func (t *Transport) serve() {
	for {
		header, body, err := t.framer.ReadFrame()
		if err != nil {
			t.close()
			return
		}

		go func() {
			t.handler(t, header, body)
		}()

		//if err := t.worker.Submit(func() {
		//	t.handler(t, header, body)
		//}); err != nil {
		//	if rand.Uint32()%1000 == 0 {
		//		zap.L().Info("worker pool full")
		//	}
		//	header.frameType = FrameTypeOverload
		//	t.Response(header, nil)
		//}
	}
}

func (t *Transport) Response(header FrameHeader, data []byte) {
	if header.frameType == FrameTypeIgnore {
		return
	}

	if err := t.framer.WriteFrame(header, data); err != nil {
		zap.L().Debug("write frame error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
	}
}

func (t *Transport) close() {
	t.conn.Close()
}
