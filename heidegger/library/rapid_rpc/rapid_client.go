package rapid_rpc

import (
	"context"
	"fmt"
	"net"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/buffer_pool"
)

const (
	rapidClientShutDown = 1
)

type rapidClient struct {
	address string

	conn     net.Conn
	framer   *Framer
	keeper   *sync.Map
	keeperV2 [128 * 1024]keeperItem
	sequence uint32

	cancel chan int
	close  chan int

	shutMark int32

	index int
}

func newRapidClient(index int, address string) *rapidClient {
	client := &rapidClient{
		address:  address,
		close:    make(chan int, 1),
		index:    index,
		sequence: 0,
	}

	client.broken()
	client.startReconnect()
	return client
}

func (client *rapidClient) send(ctx context.Context, frameType uint8, commandId uint16, data []byte) (buffer_pool.Buffer, error) {
	if client.conn == nil {
		return buffer_pool.Buffer{}, fmt.Errorf("rapid client send transport broken")
	}

	seq := atomic.AddUint32(&client.sequence, 1)
	header := FrameHeader{
		size:       frameHeader<PERSON>en + uint32(len(data)),
		sequenceId: seq,
		frameType:  frameType,
		commandId:  commandId,
		extra:      0,
	}

	item := newKeeperItem()
	// if err := client.keep(seq, item); err != nil {
	// 	return buffer_pool.Buffer{}, fmt.Errorf("rapid client send keep fail")
	// }

	// defer client.keeper.Delete(seq) //发送失败也需要删掉item

	if err := client.keeoV2(seq, item); err != nil {
		return buffer_pool.Buffer{}, fmt.Errorf("rapid client send keep fail")
	}

	defer client.deleteV2(seq)

	err := client.framer.WriteFrame(header, data)
	if err != nil {
		return buffer_pool.Buffer{}, fmt.Errorf("rapid client send fail")
	}

	select {
	case <-client.cancel:
		return buffer_pool.Buffer{}, fmt.Errorf("rapid client send transport close")
	case <-ctx.Done():
		return buffer_pool.Buffer{}, fmt.Errorf("rapid client send context cancel")
	case responseData := <-item:
		return responseData, nil
	}
}

func (client *rapidClient) keep(sequenceId uint32, item keeperItem) error {
	_, load := client.keeper.LoadOrStore(sequenceId, item)
	if load {
		return fmt.Errorf("sequence id duplicate: %d", sequenceId)
	}

	return nil
}

func (client *rapidClient) keeoV2(sequenceId uint32, item keeperItem) error {
	sequenceId = sequenceId % uint32(len(client.keeperV2))
	if client.keeperV2[sequenceId] != nil {
		return fmt.Errorf("sequence id duplicate: %d", sequenceId)
	}

	client.keeperV2[sequenceId] = item
	return nil
}

func (client *rapidClient) deleteV2(sequenceId uint32) {
	sequenceId = sequenceId % uint32(len(client.keeperV2))
	client.keeperV2[sequenceId] = nil
}

func (client *rapidClient) getKeep(sequenceId uint32) keeperItem {
	val, ok := client.keeper.Load(sequenceId)
	if !ok {
		return nil
	}

	return val.(keeperItem)
}

func (client *rapidClient) getKeepV2(sequenceId uint32) keeperItem {
	sequenceId = sequenceId % uint32(len(client.keeperV2))
	return client.keeperV2[sequenceId]
}

func (client *rapidClient) read() (error, bool) {
	header, data, err := client.framer.ReadFrame()
	if err != nil {
		client.broken()
		return fmt.Errorf("rapid client read failed"), true
	}

	item := client.getKeepV2(header.sequenceId)
	if item == nil {
		return fmt.Errorf("rapid client sequence missmatch, sequenceId %d", header.sequenceId), false
	}

	//val, ok := client.keeper.Load(header.sequenceId)
	//if !ok {
	//	return fmt.Errorf("rapid client sequence missmatch, sequenceId %d", header.sequenceId), false
	//}

	item <- data
	return nil, false
}

func (client *rapidClient) broken() {
	if len(client.close) == 0 {
		client.close <- rapidClientShutDown
	}
}

func (client *rapidClient) ok() bool {
	return client.conn != nil
}

func (client *rapidClient) Close(shut bool) error {
	if shut {
		atomic.StoreInt32(&client.shutMark, rapidClientShutDown)
	}

	client.broken()

	return nil
}

func (client *rapidClient) startReconnect() {
	go func() {
		defer func() {
			if client.cancel != nil {
				close(client.cancel)
				client.cancel = nil
			}

			ClearChannel(client.close)
		}()

		for range client.close {
			//logfus.Debugf("rapid client start reconnect on address: %s", client.address)
			//logfus.Debugf("startReconnect gid %d", Goid())

			if client.conn != nil {
				client.conn.Close()
				client.conn = nil
			}

			if client.cancel != nil {
				close(client.cancel)
				client.cancel = nil
			}

			if atomic.LoadInt32(&client.shutMark) == rapidClientShutDown {
				zap.L().Info("[rapidClient] rapid client close, index: , addr: %s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(client.index)))), zap.Int64("param2", int64(client.address)))
				return
			}

			// 重试链接直到成功
			var newConn net.Conn = nil
			for {
				conn, err := net.DialTimeout("tcp", client.address, time.Second*3)

				if atomic.LoadInt32(&client.shutMark) == rapidClientShutDown {
					zap.L().Info("[rapidClient] rapid client close, index: , addr: %s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(client.index)))), zap.Int64("param2", int64(client.address)))
					return
				}

				if err != nil {
					zap.L().Error("[rapidClient] rapid client connect failed, index: , addr: %s, err: %s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(client.index)))), zap.Int64("param2", int64(client.address)), zap.Int64("param3", int64(err)))
					time.Sleep(time.Second * 5)
					continue
				}

				newConn = conn
				break
			}

			client.keeper = &sync.Map{}
			client.framer = &Framer{
				r: newConn,
				w: newConn,
			}
			client.cancel = make(chan int)
			client.conn = newConn

			zap.L().Info("[rapidClient] rapid client reconnect success on index: , address: %s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(client.index)))), zap.Int64("param2", int64(client.address)))

			ClearChannel(client.close)

			client.startReader(client.cancel)
			client.startHeartbeat(client.cancel)
			time.Sleep(5 * time.Second)
		}

		zap.L().Debug("[rapidClient] startReconnect exit loop")
	}()
}

func ClearChannel(c chan int) {
	length := len(c)
	for i := 0; i < length; i++ {
		<-c
	}
}

func Goid() int {
	defer func() {
		if err := recover(); err != nil {
			fmt.Println("panic recover:panic info:%v", err)
		}
	}()

	var buf [64]byte
	n := runtime.Stack(buf[:], false)
	idField := strings.Fields(strings.TrimPrefix(string(buf[:n]), "goroutine "))[0]
	id, err := strconv.Atoi(idField)
	if err != nil {
		panic(fmt.Sprintf("cannot get goroutine id: %v", err))
	}
	return id
}

func (client *rapidClient) startReader(cancel chan int) {
	go func() {
		for {
			select {
			case <-cancel:
				zap.L().Info("[rapidClient] startReader received cancel signal, gid , addr:%s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(Goid())))), client.address)
				return
			default:
				if err, needTerminate := client.read(); err != nil {
					if needTerminate {
						zap.L().Info("[rapidClient] client.read error", zap.Error(err))
						return
					}
				}
			}
		}
	}()
}

func (client *rapidClient) startHeartbeat(cancel chan int) {
	go func() {
		//zap.L().Info("client.startHeartbeat Enter, address", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", client.address)))))
		for {
			select {
			case <-cancel:
				zap.L().Info("[rapidClient] startHeartbeat received cancel signal, gid , addr:%s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(Goid())))), client.address)
				return
			case <-time.After(10 * time.Second):
				//logfus.Debugf("rapid client heart beat on address:%s", client.address)

				ctx, _ := context.WithTimeout(context.TODO(), time.Second*1)
				_, err := client.send(ctx, FrameTypeHeartbeat, 0, nil)
				if err != nil {
					zap.L().Error("[rapidClient] broken on heartbeat gid , err:%s", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(Goid())))), err)
					client.broken()
					return
				}

				//zap.L().Info("client.startHeartbeat heart beat success, stats", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", client.GetStats())))))
			}
		}
	}()
}

func (client *rapidClient) GetStats() string {
	return fmt.Sprintf("index: %d, address: %s, bookKeeper:%d, sequence:%d", client.index, client.address, client.GetBookKeeperCount(), client.sequence)
}

func (client *rapidClient) GetBookKeeperCount() int {
	count := 0
	client.keeper.Range(func(key, value interface{}) bool {
		count++
		return true
	})

	return count
}

type keeperItem chan buffer_pool.Buffer

func newKeeperItem() keeperItem {
	return make(chan buffer_pool.Buffer, 1)
}
