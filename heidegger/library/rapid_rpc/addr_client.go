package rapid_rpc

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"github.com/valyala/fastrand"
	"gitlab.com/dev/heidegger/library/buffer_pool"
)

type AddrClient struct {
	address   string
	conn      []*rapidClient
	connector chan int
	option    ClientOption
}

func newAddrClient(address string, option ClientOption) *AddrClient {
	client := &AddrClient{
		address:   address,
		option:    option,
		conn:      make([]*rapidClient, option.ClientCount),
		connector: make(chan int, option.ClientCount),
	}

	for i := 0; i != option.ClientCount; i++ {
		client.conn[i] = newRapidClient(i, address)
	}

	return client
}

func (client *AddrClient) send(ctx context.Context, commandId uint16, data []byte) (buffer_pool.Buffer, error) {
	rapidConns := client.conn

	needle := fastrand.Uint32()

	conn := rapidConns[needle%uint32(len(rapidConns))]
	if !conn.ok() {
		return buffer_pool.Buffer{}, fmt.Errorf("addr client send transport broken")
	}
	return conn.send(ctx, FrameTypeData, commandId, data)
}

func (client *AddrClient) ShutOff() error {
	for index, conn := range client.conn {
		zap.L().Info("addr client shutoff, index: , addr: %s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(index)))), zap.Int64("param2", int64(client.address)))
		conn.Close(true)
	}
	return nil
}

func (client *AddrClient) Close() error {
	for index, conn := range client.conn {
		zap.L().Info("addr client close, index: , addr: %s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(index)))), zap.Int64("param2", int64(client.address)))
		conn.Close(true)
	}
	return nil
}
