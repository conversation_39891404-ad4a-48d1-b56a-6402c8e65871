package rapid_rpc

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"net"
	"fmt"
)

type CommandFunc func(t *Transport, header FrameHeader, data buffer_pool.Buffer)

type Server struct {
	workerPerConn int
	commands      map[int]CommandFunc

	listener net.Listener
}

func NewServer(workerPerConn int) *Server {
	return &Server{
		workerPerConn: workerPerConn,
		commands:      make(map[int]CommandFunc),
	}
}

func (s *Server) RegisterService(commandId int, handler CommandFunc) {
	s.commands[commandId] = handler
}

func (s *Server) Serve(listener net.Listener) error {
	s.listener = listener

	for {
		rawConn, err := listener.Accept()
		if err != nil {
			return err
		}
		zap.L().Info("client conn, remote", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", rawConn.RemoteAddr())))).String())

		go func() {
			s.handleRawConn(rawConn)
		}()
	}
}

func (s *Server) Stop() {
	if s.listener != nil {
		s.listener.Close()
	}
}

func (s *Server) handleRawConn(rawConn net.Conn) {
	t := NewTransport(s.workerPerConn, rawConn, s.handleFrame)
	t.Start()
	t.serve()
	t.Stop()
}

func (s *Server) handleFrame(t *Transport, frameHeader FrameHeader, data buffer_pool.Buffer) {
	if frameHeader.frameType == FrameTypeOneway {
		frameHeader.frameType = FrameTypeIgnore
		data.Reset()
		t.Response(frameHeader, nil)
		return
	}

	if frameHeader.frameType == FrameTypeHeartbeat {
		data.Reset()
		t.Response(frameHeader, nil)
		return
	}

	if command, ok := s.commands[int(frameHeader.commandId)]; ok {
		command(t, frameHeader, data)
	} else {
		data.Reset()
		t.Response(frameHeader, nil)
		return
	}
}
