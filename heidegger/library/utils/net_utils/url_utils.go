package net_utils

import (
	"net/http"
	"net/url"
	"strings"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/utils/encryption_utils"
	"fmt"
)

func ExtractQueryParamString(req *http.Request, key string) string {
	if values, ok := req.URL.Query()[key]; ok && len(values) > 0 {
		return values[0]
	}
	return ""
}

func AppendParam(originalUrl string, paramName string, value string) string {
	if strings.Contains(originalUrl, "?") {
		if strings.HasSuffix(originalUrl, "&") {
			return originalUrl + paramName + "=" + url.QueryEscape(value)
		} else {
			return originalUrl + "&" + paramName + "=" + url.QueryEscape(value)
		}
	} else {
		return originalUrl + "?" + paramName + "=" + url.QueryEscape(value)
	}
}

func ReplaceMacroSimple(originalUrl string, macro string, value string) string {
	return strings.ReplaceAll(originalUrl, macro, value)
}

func IsValidUrl(url string) bool {
	return strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://")
}

func TryFixEncodedUrl(urlValue string) (string, error) {
	if len(urlValue) == 0 {
		return urlValue, nil
	}
	//老代码兼容可以去掉了
	//if strings.HasPrefix(urlValue, "b_") {
	//	decoded, err := base64.URLEncoding.DecodeString(urlValue[2:])
	//	if err != nil {
	//		zap.L().Error("TryFixEncodedUrl: fail  -> ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", urlValue)))), zap.String("param2", fmt.Sprintf("%v", decoded)))
	//		return urlValue, fmt.Errorf("TryFixEncodedUrl: %s -> %s", urlValue, decoded)
	//	}
	//
	//	zap.L().Info("TryFixEncodedUrl: base64  -> ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", urlValue)))), zap.Error(err))
	//	return string(decoded), nil
	//}

	//老代码兼容可以去掉了
	//if strings.HasPrefix(urlValue, "h_") {
	//	decoded, err := hex.DecodeString(urlValue[2:])
	//	if err != nil {
	//		zap.L().Error("TryFixEncodedUrl: fail  -> ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", urlValue)))), zap.String("param2", fmt.Sprintf("%v", decoded)))
	//		return urlValue, fmt.Errorf("TryFixEncodedUrl: %s -> %s", urlValue, decoded)
	//	}
	//
	//	zap.L().Info("TryFixEncodedUrl: hex  -> ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", urlValue)))), zap.String("param2", fmt.Sprintf("%v", string(decoded))))
	//	return string(decoded), nil
	//}

	if strings.HasPrefix(urlValue, "c_") {
		decoded := encryption_utils.SimpleDecrypt(urlValue[2:])
		zap.L().Info("TryFixEncodedUrl: encrypt  -> ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", urlValue)))), zap.String("param2", fmt.Sprintf("%v", decoded)))
		return decoded, nil
	}

	//两次urlencode 老代码可以去掉
	//for i := 0; i != 2; i++ {
	//	if strings.HasPrefix(urlValue, "http") && !strings.HasPrefix(urlValue, "http://") {
	//		urlValue, _ = url.QueryUnescape(urlValue)
	//	} else if strings.HasPrefix(urlValue, "https") && !strings.HasPrefix(urlValue, "https://") {
	//		urlValue, _ = url.QueryUnescape(urlValue)
	//	} else {
	//		return urlValue, nil
	//	}
	//}

	return urlValue, nil
}

func GetURLHeadFromUrl(rawURL string) string {
	if !strings.Contains(rawURL, "://") {
		return ""
	}

	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return ""
	}

	if strings.HasPrefix(rawURL, "https://") || strings.HasPrefix(rawURL, "http://") {
		return parsedURL.Scheme + "://" + parsedURL.Host
	} else {
		return parsedURL.Scheme + "://"
	}

}

func GetDomainFromUrl(urlValue string) string {
	if !strings.Contains(urlValue, "://") {
		urlValue = "http://" + urlValue
	}

	parsedURL, err := url.Parse(urlValue)
	if err != nil {
		return urlValue
	}

	hostname := parsedURL.Hostname()
	if hostname == "" {
		return urlValue
	}

	return hostname
}

func CutUrl(url, split string) string {
	if len(split) == 0 {
		return url
	}
	if ind := strings.Index(url, split); ind > 0 {
		return url[:ind]
	}
	return url
}

// check QuickApp deeplink url
func IsQuickApp(url string) bool {
	if len(url) == 0 {
		return false
	}
	return strings.HasPrefix(url, "hap://app/") ||
		strings.HasPrefix(url, "https://hapjs.org/app/") ||
		strings.HasPrefix(url, "http://hapjs.org/app/") ||
		strings.HasPrefix(url, "hwfastapp://")
}

func UpdateUrlQuery(link string, params map[string]string) string {
	if len(link) == 0 || len(params) == 0 {
		return link
	}

	parsedURL, err := url.Parse(link)
	if err != nil {
		return link
	}

	queries := parsedURL.Query()
	for k, v := range params {
		queries.Set(k, v)
	}

	parsedURL.RawQuery = queries.Encode()
	return parsedURL.String()
}
