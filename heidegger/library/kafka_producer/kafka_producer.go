package kafka_producer

import (
	"github.com/IBM/sarama"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/utils"
	"math/rand"
	"time"
	"fmt"
)

type KafkaProducer struct {
	id         string
	producer   sarama.AsyncProducer
	brokerList []string
	Version    string
	term       chan struct{}

	topicQpsCounter *prometheus_helper.LabelCounter
}

func NewKafkaProducer(brokerList []string, kafkaVer string) *KafkaProducer {
	return &KafkaProducer{
		id:              utils.NewUUID(),
		brokerList:      brokerList,
		Version:         kafkaVer,
		topicQpsCounter: prometheus_helper.RegisterLabelCounter("KafkaProducer", []string{"topic"}),
		term:            make(chan struct{}),
	}
}

func (p *KafkaProducer) Start() error {
	kafkaConfig := sarama.NewConfig()
	kafkaConfig.ChannelBufferSize = 1024 * 4
	kafkaConfig.Producer.RequiredAcks = sarama.NoResponse       // Only wait for the leader to ack
	kafkaConfig.Producer.Compression = sarama.CompressionSnappy // Compress messages
	kafkaConfig.Producer.Flush.MaxMessages = 1024 * 1024
	kafkaConfig.Producer.Flush.Messages = 1024
	kafkaConfig.Producer.Flush.Frequency = time.Millisecond * 1000
	kafkaConfig.Producer.Retry.Max = 2
	kafkaConfig.Producer.Retry.Backoff = time.Millisecond * 500
	kafkaConfig.Producer.MaxMessageBytes = 4 * 1024 * 1024

	// 网络设置
	kafkaConfig.Net.MaxOpenRequests = 10
	kafkaConfig.Net.DialTimeout = 10 * time.Second  // 拨号超时
	kafkaConfig.Net.ReadTimeout = 10 * time.Second  // 读取超时
	kafkaConfig.Net.WriteTimeout = 10 * time.Second // 写入超时

	//if config.AppVersion == "01100" {
	kafkaConfig.Version = sarama.V3_1_0_0
	//}

	if p.Version == "1.1.1.0" {
		kafkaConfig.Version = sarama.V1_1_1_0
	} else if p.Version == "3.1.0.0" {
		kafkaConfig.Version = sarama.V3_1_0_0
	} else {
		kafkaConfig.Version = sarama.V1_1_1_0
	}

	zap.L().Debug("[KafkaDataCollector] kafka config:, broker", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", kafkaConfig)))), zap.String("param2", fmt.Sprintf("%v", p.brokerList)))
	producer, err := sarama.NewAsyncProducer(p.brokerList, kafkaConfig)
	if err != nil {
		return err
	}

	p.producer = producer

	go p.loop()

	return nil
}

func (p *KafkaProducer) Stop() {
	close(p.term)
	if p.producer != nil {
		p.producer.Close()
	}
}

func (p *KafkaProducer) loop() {
	zap.L().Info("[KafkaProducer] start loop, addr", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", p.brokerList)))))

	for {
		select {
		case <-p.term:
			zap.L().Info("[KafkaProducer] stop loop, addr", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", p.brokerList)))))
			return
		case err := <-p.producer.Errors():
			if rand.Uint32()%1000 == 0 {
				zap.L().Error("[KafkaProducer] Failed to write access log entry:", err)
			}
		}
	}
}

func (p *KafkaProducer) Input(topic string, data []byte) {
	if p.producer == nil {
		zap.L().Error("[KafkaProducer] producer is nil")
		return
	}

	p.topicQpsCounter.Inc([]string{topic})

	p.producer.Input() <- &sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.ByteEncoder(data),
	}
}

func (p *KafkaProducer) InputWithKey(topic string, key string, data []byte) {
	if p.producer == nil {
		zap.L().Error("[KafkaProducer] producer is nil")
		return
	}

	p.topicQpsCounter.Inc([]string{topic})

	p.producer.Input() <- &sarama.ProducerMessage{
		Topic: topic,
		Key:   sarama.ByteEncoder(key),
		Value: sarama.ByteEncoder(data),
	}
}
