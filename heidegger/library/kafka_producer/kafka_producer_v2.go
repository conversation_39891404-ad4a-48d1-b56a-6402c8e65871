package kafka_producer

//
//import (
//	"fmt"
//	"github.com/confluentinc/confluent-kafka-go/kafka"
//	"go.uber.org/zap"
//	"math/rand"
//	"time"
//)
//
//type KafkaProducerV2 struct {
//	id         string
//	producer   *kafka.Producer
//	brokerList []string
//	Version    string
//	term       chan struct{}
//}
//
//func NewKafkaProducerV2(brokerList []string, kafkaVer string) *KafkaProducerV2 {
//	return &KafkaProducerV2{
//		id:         newUUID(),
//		brokerList: brokerList,
//		Version:    kafkaVer,
//		term:       make(chan struct{}),
//	}
//}
//
//func (p *KafkaProducerV2) Start() error {
//	config := &kafka.ConfigMap{
//		"bootstrap.servers":            p.brokerList,
//		"acks":                         "1",      // 等待所有副本确认
//		"compression.type":             "snappy", // 使用 Snappy 压缩
//		"queue.buffering.max.messages": 100000,
//		"queue.buffering.max.kbytes":   1024 * 16, // 缓冲区大小 16 MB
//		"queue.buffering.max.ms":       1000,      // 1秒钟flush一次
//		"message.max.bytes":            1000000,   // 单条消息的最大大小 1MB
//		"retries":                      1,
//		"retry.backoff.ms":             500,  // 重试间隔
//		"request.timeout.ms":           5000, // 请求超时
//	}
//
//	producer, err := kafka.NewProducer(config)
//	if err != nil {
//		return err
//	}
//
//	p.producer = producer
//
//	go p.loop()
//
//	return nil
//}
//
//func (p *KafkaProducerV2) Stop() {
//	close(p.term)
//	if p.producer != nil {
//		p.producer.Close()
//	}
//}
//
//func (p *KafkaProducerV2) loop() {
//	zap.L().Info("[KafkaProducerV2] start loop, addr", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", p.brokerList)))))
//
//	for {
//		select {
//		case <-p.term:
//			zap.L().Info("[KafkaProducerV2] stop loop, addr", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", p.brokerList)))))
//			return
//		case e := <-p.producer.Events():
//			switch ev := e.(type) {
//			case *kafka.Message:
//				if ev.TopicPartition.Error != nil {
//					if rand.Uint32()%1000 == 0 {
//						zap.L().Error("[KafkaProducerV2] Failed to deliver message", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", ev.TopicPartition.Error)))))
//					}
//				} else {
//					zap.L().Debug("Message delivered to ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", ev.TopicPartition)))))
//				}
//			}
//		}
//	}
//}
//
//func (p *KafkaProducerV2) Input(topic string, data []byte) {
//	if p.producer == nil {
//		zap.L().Error("[KafkaProducerV2] producer is nil")
//		return
//	}
//	err := p.producer.Produce(&kafka.Message{
//		TopicPartition: kafka.TopicPartition{Topic: &topic, Partition: kafka.PartitionAny},
//		Value:          data,
//	}, nil)
//
//	if err != nil {
//		zap.L().Error("[KafkaProducerV2] Failed to produce message", zap.Error(err))
//	}
//}
//
//func (p *KafkaProducerV2) InputWithKey(topic string, key string, data []byte) {
//	if p.producer == nil {
//		zap.L().Error("[KafkaProducerV2] producer is nil")
//		return
//	}
//	err := p.producer.Produce(&kafka.Message{
//		TopicPartition: kafka.TopicPartition{Topic: &topic, Partition: kafka.PartitionAny},
//		Key:            []byte(key),
//		Value:          data,
//	}, nil)
//
//	if err != nil {
//		zap.L().Error("[KafkaProducerV2] Failed to produce message with key", zap.Error(err))
//	}
//}
//
//// Helper function to generate a UUID (you can replace it with your own UUID generator)
//func newUUID() string {
//	return fmt.Sprintf("%d", time.Now().UnixNano())
//}
