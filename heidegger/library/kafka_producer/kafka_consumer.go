package kafka_producer

import (
	"context"
	"errors"
	"fmt"
	"github.com/IBM/sarama"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/utils"
	"log"
	"time"
)

type KafkaConsumer struct {
	brokerList      []string
	brokerVersion   string
	consumerGroupId string
	topics          []string

	client   sarama.ConsumerGroup
	consumer *Consumer
	cancel   context.CancelFunc
}

func NewKafkaConsumer(brokerList []string, brokerVersion string, consumerGroupId string, topics []string) *KafkaConsumer {
	return &KafkaConsumer{
		brokerList:      brokerList,
		brokerVersion:   brokerVersion,
		consumerGroupId: consumerGroupId,
		topics:          topics,
	}
}

func (p *KafkaConsumer) Start() error {
	kafkaConfig := sarama.NewConfig()
	kafkaConfig.Consumer.Offsets.Initial = sarama.OffsetOldest // 从最早的offset开始消费
	// 网络设置
	kafkaConfig.Net.DialTimeout = 5 * time.Second  // 拨号超时
	kafkaConfig.Net.ReadTimeout = 5 * time.Second  // 读取超时
	kafkaConfig.Net.WriteTimeout = 5 * time.Second // 写入超时

	kafkaConfig.Version = sarama.V3_1_0_0

	if p.brokerVersion == "1.1.1.0" {
		kafkaConfig.Version = sarama.V1_1_1_0
	} else if p.brokerVersion == "3.1.0.0" {
		kafkaConfig.Version = sarama.V3_1_0_0
	} else {
		kafkaConfig.Version = sarama.V1_1_1_0
	}

	ctx, cancel := context.WithCancel(context.Background())
	client, err := sarama.NewConsumerGroup(p.brokerList, p.consumerGroupId, kafkaConfig)
	if err != nil {
		cancel()
		return fmt.Errorf("Error creating consumer group client: %v", err)
	}

	/**
	 * Setup a new Sarama consumer group
	 */
	consumer := Consumer{
		ready:   make(chan bool),
		message: make(chan *sarama.ConsumerMessage, 512),
	}

	go func() {
		defer zap.L().Warn("Sarama consumer stop!...")
		for {
			// `Consume` should be called inside an infinite loop, when a
			// server-side rebalance happens, the consumer session will need to be
			// recreated to get the new claims
			if err := client.Consume(ctx, p.topics, &consumer); err != nil {
				if errors.Is(err, sarama.ErrClosedConsumerGroup) {
					return
				}

				zap.L().Error("Error from consumer", zap.Error(err))
			}
			// check if context was cancelled, signaling that the consumer should stop
			if ctx.Err() != nil {
				return
			}
			consumer.ready = make(chan bool)
		}
	}()

	<-consumer.ready // Await till the consumer has been set up
	zap.L().Info("Sarama consumer up and running!...")
	p.cancel = cancel
	p.client = client
	p.consumer = &consumer

	return nil
}

func (p *KafkaConsumer) Stop() {
	if p.consumer != nil {
		close(p.consumer.message)
	}
	if p.cancel != nil {
		p.cancel()
	}
	if p.client != nil {
		if err := p.client.Close(); err != nil {
			log.Panicf("Error closing client: %v", err)
		}
	}
}

func (p *KafkaConsumer) Message() <-chan *sarama.ConsumerMessage {
	return p.consumer.message
}

// Consumer represents a Sarama consumer group consumer
type Consumer struct {
	ready   chan bool
	message chan *sarama.ConsumerMessage
}

// Setup is run at the beginning of a new session, before ConsumeClaim
func (consumer *Consumer) Setup(sarama.ConsumerGroupSession) error {
	// Mark the consumer as ready
	close(consumer.ready)
	return nil
}

// Cleanup is run at the end of a session, once all ConsumeClaim goroutines have exited
func (consumer *Consumer) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim must start a consumer loop of ConsumerGroupClaim's Messages().
// Once the Messages() channel is closed, the Handler must finish its processing
// loop and exit.
func (consumer *Consumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	// NOTE:
	// Do not move the code below to a goroutine.
	// The `ConsumeClaim` itself is called within a goroutine, see:
	// https://github.com/IBM/sarama/blob/main/consumer_group.go#L27-L29
	for {
		select {
		case message, ok := <-claim.Messages():
			if !ok {
				zap.L().Error("claim.Messages() closed")
				return nil
			}

			select {
			case consumer.message <- message:
			default:
				zap.L().Error("consumer.message closed")
			}

			session.MarkMessage(message, utils.EmptyString)
		case <-session.Context().Done():
			return nil
		}
	}
}
