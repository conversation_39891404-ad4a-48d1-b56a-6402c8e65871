package traffic_strategy_entity

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/ad_service/err_code"
	"gitlab.com/dev/heidegger/library/bitmap_index_v2"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/traffic_strategy_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
)

const (
	FieldIdMediaSlotId bitmap_index_v2.FieldId = 1
	FieldIdAdId        bitmap_index_v2.FieldId = 2
	FieldIdAdGroupId   bitmap_index_v2.FieldId = 3
)

type TrafficStrategyRequest interface {
	GetMediaSlotId() utils.ID
	GetAdIdList() []int64
	GetAdGroupIdList() []int64
	GetGeoCode() int64
}

type DummyTrafficStrategyRequest struct {
	MediaSlotId   utils.ID `json:"media_slot_id"`
	AdIdList      []int64  `json:"ad_id_list"`
	AdGroupIdList []int64  `json:"ad_group_id_list"`
	GeoCode       int64    `json:"geo_code"`
}

func (d DummyTrafficStrategyRequest) GetMediaSlotId() utils.ID {
	return d.MediaSlotId
}

func (d DummyTrafficStrategyRequest) GetAdIdList() []int64 {
	return d.AdIdList
}

func (d DummyTrafficStrategyRequest) GetAdGroupIdList() []int64 {
	return d.AdGroupIdList
}

func (d DummyTrafficStrategyRequest) GetGeoCode() int64 {
	return d.GeoCode
}

type TrafficStrategyCallback func(strategy *entity.TrafficStrategy)

type TrafficStrategyIndex struct {
	indexManager *bitmap_index_v2.IndexManager

	loader traffic_strategy_loader.TrafficStrategyLoader

	term chan struct{}
}

func NewTrafficStrategyIndex(loader traffic_strategy_loader.TrafficStrategyLoader) *TrafficStrategyIndex {
	return &TrafficStrategyIndex{
		loader: loader,
		term:   make(chan struct{}),
	}
}

func (t *TrafficStrategyIndex) Start() error {
	if err := t.Load(); err != nil {
		return err
	}

	go t.loop()
	return nil
}

func (t *TrafficStrategyIndex) Stop() {
	close(t.term)
}

func (t *TrafficStrategyIndex) loop() {
	ticker := time.NewTicker(time.Minute)

	for {
		select {
		case <-ticker.C:
			if err := t.Load(); err != nil {
				zap.L().Error("[TrafficStrategyIndex] load index failed, err", zap.Error(err))
			}
		case <-t.term:
			ticker.Stop()
			return
		}
	}
}

func (t *TrafficStrategyIndex) Load() error {
	trafficStrategyList := t.loader.GetTrafficStrategyList()
	if err := t.buildIndex(trafficStrategyList); err != nil {
		return err
	}
	return nil
}

func (t *TrafficStrategyIndex) buildIndex(trafficStrategyList entity.TrafficStrategyList) error {
	indexManager := bitmap_index_v2.NewIndexManager()

	indexManager.MustAddField(FieldIdMediaSlotId, bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(FieldIdAdId, bitmap_index_v2.FieldTypeInt)
	indexManager.MustAddField(FieldIdAdGroupId, bitmap_index_v2.FieldTypeInt)

	for _, strategy := range trafficStrategyList {
		doc := bitmap_index_v2.Doc{
			DocId: bitmap_index_v2.Id(strategy.Id),
		}

		if len(strategy.Target.AdId) != 0 {
			doc.AddIntTarget(FieldIdAdId, strategy.Target.AdId, true)
		}

		if len(strategy.Target.MediaSlotId) != 0 {
			doc.AddIntTarget(FieldIdMediaSlotId, strategy.Target.MediaSlotId, true)
		}

		if len(strategy.Target.AdGroupId) != 0 {
			doc.AddIntTarget(FieldIdAdGroupId, strategy.Target.AdGroupId, true)
		}

		if err := indexManager.AddDoc(&doc); err != nil {
			return err
		}
	}

	if err := indexManager.BuildIndex(); err != nil {
		return err
	}

	t.indexManager = indexManager

	return nil
}

func (t *TrafficStrategyIndex) Do(trafficStrategyRequest TrafficStrategyRequest, callback TrafficStrategyCallback) error {
	if t.indexManager == nil {
		return err_code.ErrIndexNotReady
	}

	indexCtx := t.indexManager.CreateContext()
	defer indexCtx.Release()

	for {
		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64(indexCtx, FieldIdMediaSlotId, int64(trafficStrategyRequest.GetMediaSlotId())); err != nil {
			return err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64List(indexCtx, FieldIdAdId, trafficStrategyRequest.GetAdIdList()); err != nil {
			return err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		if hasCandidate, err := indexCtx.GetIndexManager().DoIndexInt64List(indexCtx, FieldIdAdGroupId, trafficStrategyRequest.GetAdGroupIdList()); err != nil {
			return err_code.ErrIndexFailed.Wrap(err)
		} else if !hasCandidate {
			break
		}

		break
	}

	strategyIdList := indexCtx.GetDocs()
	for _, id := range strategyIdList {
		strategy := t.loader.GetTrafficStrategyById(utils.ID(id))
		if strategy == nil {
			zap.L().Debug("[TrafficStrategyIndex] strategy not found, id", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(id)))))
			continue
		}

		callback(strategy)
	}

	return nil
}
