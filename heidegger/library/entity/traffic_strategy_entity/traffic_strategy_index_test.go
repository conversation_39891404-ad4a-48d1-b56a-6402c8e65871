package traffic_strategy_entity

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/traffic_strategy_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"testing"
	"fmt"
)

type testInput struct {
	mediaSlotId int64
	adIdList    []int64
	geoCode     int64
}

func (i *testInput) GetMediaSlotId() utils.ID {
	return utils.ID(i.mediaSlotId)
}
func (i *testInput) GetAdIdList() []int64 {
	return i.adIdList
}
func (i *testInput) GetGeoCode() int64 {
	return i.geoCode
}

func getSlotAdTestData() traffic_strategy_loader.TrafficStrategyLoader {
	itemList := entity.TrafficStrategyList{
		&entity.TrafficStrategy{
			Id:   1,
			Name: "traffic strategy for slot",
			Target: entity.TrafficStrategyTarget{
				MediaSlotId: []int64{1},
			},
			BidPrice: 1e9,
			BidType:  entity.BidTypeCpm,
			TrafficRequestModifierList: []*entity.TrafficRequestModifier{
				{
					Weight:    100,
					DspSlotId: 100,
				},
			},
			TrafficResponseModifierList: make([]*entity.TrafficResponseModifier, 0),
		},
		&entity.TrafficStrategy{
			Id:   2,
			Name: "traffic strategy for slot in ad",
			Target: entity.TrafficStrategyTarget{
				AdId:        []int64{1},
				MediaSlotId: []int64{1},
			},
			BidPrice: 1e9,
			BidType:  entity.BidTypeCpm,
			TrafficRequestModifierList: []*entity.TrafficRequestModifier{
				{
					Weight:    100,
					DspSlotId: 200,
				},
			},
			TrafficResponseModifierList: make([]*entity.TrafficResponseModifier, 0),
		},
	}

	return traffic_strategy_loader.NewDummyTrafficStrategyLoader(itemList)
}

func TestTrafficStrategyIndex_Do_getSlotAdTestData(t *testing.T) {
	index := NewTrafficStrategyIndex(getSlotAdTestData())
	if err := index.Load(); err != nil {
		t.Fatal(err)
	}

	input := &testInput{
		mediaSlotId: 1,
		adIdList:    []int64{1},
	}

	err := index.Do(input, func(strategy *entity.TrafficStrategy) {
		zap.L().Info("strategy", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", strategy.Name)))))
	})

	if err != nil {
		t.Fatal(err)
	}
}
