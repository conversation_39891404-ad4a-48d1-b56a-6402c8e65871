package ua_parser_manager

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/ua_parser_manager/ua_parser"
	"path/filepath"
	"strings"
	"time"
)

type TrafficUaInfo struct {
	DeviceType entity.DeviceType
	Os         entity.OsType
	Brand      string
	Model      string
	OsVersion  string
}

type UaParserResult struct {
	DeviceType     entity.DeviceType `json:"device_type"`
	OsType         entity.OsType     `json:"os_type"`
	OsVersionRaw   string            `json:"os_version_raw"`
	OsVersionMajor uint32            `json:"os_version_major"`
	OsVersionMinor uint32            `json:"os_version_minor"`
	OsVersionPatch uint32            `json:"os_version_patch"`
	BrandModelRaw  []string          `json:"brand_model_raw"`

	OsVersion string `json:"os_version"`
	Brand     string `json:"brand"`
	Model     string `json:"model"`

	OsVersionId  uint32 `json:"os_version_id"`
	BrandModelId uint32 `json:"brand_model_id"`
	BrandId      uint32 `json:"brand_id"`
	UaId         uint64 `json:"ua_id"`
}

func (r UaParserResult) DumpJson() string {
	data, _ := json.Marshal(r)
	return string(data)
}

type UaParserManager struct {
	idMapperDirectory string
	idMapper          *UserAgentIdMapper

	term chan struct{}
}

func NewUaParserManager(idMapperDirectory string) *UaParserManager {
	return &UaParserManager{
		idMapperDirectory: idMapperDirectory,
		idMapper:          NewUserAgentIdMapper(),

		term: make(chan struct{}),
	}
}

func (m *UaParserManager) Start() error {
	if err := m.loadIdMapper(); err != nil {
		return err
	}

	go m.loop()

	return nil
}

func (m *UaParserManager) Stop() {
	close(m.term)
}

func (m *UaParserManager) ParseTrafficUaInfo(traffic TrafficUaInfo) (UaParserResult, error) {
	result := UaParserResult{
		DeviceType:   traffic.DeviceType,
		OsType:       traffic.Os,
		OsVersionRaw: traffic.OsVersion,
	}

	result.OsVersionRaw = traffic.OsVersion
	result.OsVersion = m.fixOsVersionFromTraffic(traffic.OsVersion)
	result.OsVersionMajor, result.OsVersionMinor, result.OsVersionPatch = m.parseOsVersion(traffic.OsVersion)
	result.OsVersionId = m.idMapper.GetOsVersionIdWithFixing(result.OsType.String(), result.OsVersion)

	result.Brand = m.fixBrandFromTraffic(&result, traffic.Brand)
	result.Model = m.fixModelFromTraffic(&result, traffic.Model)
	result.BrandModelRaw = []string{
		result.Brand,
		result.Model,
		fmt.Sprintf("%s%s", result.Brand, result.Model),
		fmt.Sprintf("%s-%s", result.Brand, result.Model),
	}
	result.BrandModelId = m.idMapper.GetBrandModelIdWithFixing(result.BrandModelRaw)
	result.BrandId = result.BrandModelId / 1e4
	result.UaId = uint64(result.BrandModelId)*1e8 + uint64(result.OsVersionId)*1e4 + 1

	return result, nil
}

func (m *UaParserManager) Parse(ua string) (UaParserResult, error) {
	uaParser := ua_parser.CreateUserAgentParser(ua)
	if err := uaParser.Parse(); err != nil {
		return UaParserResult{}, err
	}

	result := UaParserResult{
		DeviceType: entity.DeviceTypeUnknown,
		OsType:     entity.OsTypeUnknown,
	}

	result.DeviceType = m.idMapper.GetDeviceType(uaParser.BaseName, uaParser.PlatformId, uaParser.OsId)
	result.OsType = m.idMapper.GetOsType(uaParser.OsId)
	result.OsVersionId = m.idMapper.GetOsVersionIdWithFixing(uaParser.Os, uaParser.OsVersion)
	result.OsVersionRaw = uaParser.OsVersion
	result.OsVersionMajor = uaParser.OsVersionMajor
	result.OsVersionMinor = uaParser.OsVersionMinor
	result.OsVersionPatch = uaParser.OsVersionPatch

	m.fixBrandModel(uaParser.BaseName, &uaParser)

	result.BrandModelRaw = uaParser.BrandModel
	result.BrandModelId = m.idMapper.GetBrandModelIdWithFixing(uaParser.BrandModel)
	result.BrandId = result.BrandModelId / 1e4
	result.UaId = uint64(result.BrandModelId)*1e8 + uint64(result.OsVersionId)*1e4 + 1

	result.OsVersion = m.idMapper.GetOsVersionRaw(result.OsVersionId)
	result.Brand, result.Model = m.idMapper.GetBrandModelRaw(result.BrandModelId)

	return result, nil
}

func (m *UaParserManager) loadIdMapper() error {
	if err := m.idMapper.LoadOsVersionToIdFromFile(m.getFilePath("id_os.csv")); err != nil {
		return fmt.Errorf("[UaParserManager] LoadOsVersionToIdFromFile failed, err:%s", err)
	}

	if err := m.idMapper.LoadIdToOsVersionFromFile(m.getFilePath("id_os.csv")); err != nil {
		return fmt.Errorf("[UaParserManager] LoadOsVersionToIdFromFile failed, err:%s", err)
	}

	if err := m.idMapper.LoadBrandModelToIdFromFile(m.getFilePath("brand_model_id.csv")); err != nil {
		return fmt.Errorf("[UaParserManager] loadBrandModelMapper failed, err:%s", err)
	}

	if err := m.idMapper.LoadIdToBrandModelFromFile(m.getFilePath("id_brand_model.csv")); err != nil {
		return fmt.Errorf("[UaParserManager] loadBrandModelMapper failed, err:%s", err)
	}

	return nil
}

func (m *UaParserManager) getFilePath(filename string) string {
	return filepath.Join(m.idMapperDirectory, filename)
}

func (m *UaParserManager) loop() {
	ticker := time.NewTicker(time.Second * 300)
	for {
		select {
		case <-m.term:
			return
		case <-ticker.C:
			if err := m.loadIdMapper(); err != nil {
				zap.L().Error("[UaParserManager] loadIdMapper failed, err", zap.Error(err))
			}
		}
	}
}

func (m *UaParserManager) fixBrandModel(brandModel string, uaParser *ua_parser.UaProductParser) string {
	if len(uaParser.BrandModel) == 0 && uaParser.PlatformId == ua_parser.PlatformTypeIPad {
		if uaParser.OsVersionMinor != 0 {
			uaParser.BrandModel = append(uaParser.BrandModel, fmt.Sprintf("ipad%d,%d", uaParser.OsVersionMajor, uaParser.OsVersionMinor))
		}

		if uaParser.OsVersionMajor != 0 {
			uaParser.BrandModel = append(uaParser.BrandModel, fmt.Sprintf("ipad%d", uaParser.OsVersionMajor))
		}

		uaParser.BrandModel = append(uaParser.BrandModel, "ipad")
	}

	if len(uaParser.BrandModel) == 0 && uaParser.PlatformId == ua_parser.PlatformTypeIPhone {
		if uaParser.OsVersionMinor != 0 {
			uaParser.BrandModel = append(uaParser.BrandModel, fmt.Sprintf("iphone%d,%d", uaParser.OsVersionMajor, uaParser.OsVersionMinor))
		}

		if uaParser.OsVersionMajor != 0 {
			uaParser.BrandModel = append(uaParser.BrandModel, fmt.Sprintf("iphone%d", uaParser.OsVersionMajor))
		}

		uaParser.BrandModel = append(uaParser.BrandModel, "iphone")
	}

	return brandModel
}

func (m *UaParserManager) GetUaId(uaId uint64) (UaParserResult, error) {
	osVersionId := uint32(uaId / 1e8)
	brandModelId := uint32(uaId % 1e8)

	brand, model := m.idMapper.GetBrandModelRaw(brandModelId)
	osVersion := m.idMapper.GetOsVersionRaw(osVersionId)

	return UaParserResult{
		OsVersionId:  osVersionId,
		BrandModelId: brandModelId,
		UaId:         uaId,
		Brand:        brand,
		Model:        model,
		OsVersion:    osVersion,
	}, nil
}

func (m *UaParserManager) fixOsVersionFromTraffic(osVersion string) string {
	// find the first and last digit
	firstDigit := -1
	lastDigit := -1

	hasComma := false
	hasUnderline := false
	hasHyphen := false
	hasSpace := false

	for idx, c := range osVersion {
		if c >= '0' && c <= '9' {
			if firstDigit == -1 {
				firstDigit = idx
			}
			lastDigit = idx
		}

		if firstDigit != -1 {
			if c == ',' {
				hasComma = true
			}

			if c == '_' {
				hasUnderline = true
			}

			if c == '-' {
				hasHyphen = true
			}

			if c == ' ' {
				hasSpace = true
			}
		}
	}

	if firstDigit == -1 {
		return osVersion
	}

	osVersion = osVersion[firstDigit : lastDigit+1]

	if hasComma {
		osVersion = strings.ReplaceAll(osVersion, ",", ".")
	}

	if hasUnderline {
		osVersion = strings.ReplaceAll(osVersion, "_", ".")
	}

	if hasHyphen {
		osVersion = strings.ReplaceAll(osVersion, "-", ".")
	}

	if hasSpace {
		osVersion = strings.ReplaceAll(osVersion, " ", ".")
	}

	return osVersion
}

func (m *UaParserManager) parseOsVersion(osVersion string) (uint32, uint32, uint32) {
	scanner := ua_parser.CreateUaStringScanner([]byte(osVersion))

	versionResult := scanner.ReadVersion()
	return versionResult[0], versionResult[1], versionResult[2]
}

func (m *UaParserManager) fixBrandFromTraffic(result *UaParserResult, brand string) string {
	if len(brand) == 0 {
		if result.OsType == entity.OsTypeIOS && result.DeviceType == entity.DeviceTypePad {
			brand = "apple"
		}
	}

	return strings.ToLower(brand)
}

func (m *UaParserManager) fixModelFromTraffic(result *UaParserResult, model string) string {
	if len(model) == 0 {
		if result.OsType == entity.OsTypeIOS && result.DeviceType == entity.DeviceTypePad {
			model = fmt.Sprintf("ipad%d,%d", result.OsVersionMajor, result.OsVersionMinor)
		}

		if result.OsType == entity.OsTypeIOS && result.DeviceType == entity.DeviceTypeMobile {
			model = fmt.Sprintf("iphone%d,%d", result.OsVersionMajor, result.OsVersionMinor)
		}
	}

	return strings.ToLower(model)
}
