package ua_parser

import (
	"go.uber.org/zap"
	"testing"
	"fmt"
)

func TestUaParser(t *testing.T) {
	uaString := "Mozilla/5.0 (Linux; Android 4.4.2; SM-G355H Build/KOT49H) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/44.0.2403.133 Mobile Safari/537.36"
	parser := CreateUserAgentParser(uaString)
	if err := parser.Parse(uaString); err != nil {
		t.Error(err)
	}

	zap.L().Info("PlatformId", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(parser.PlatformId)))))
	zap.L().Info("Platform", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", parser.Platform)))))
	zap.L().Info("OsId", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(parser.OsId)))))
	zap.L().Info("Os", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", parser.Os)))))
	zap.L().Info("OsVersion", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", parser.OsVersion)))))
	zap.L().Info("BrandModel", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", parser.BrandModel)))))
}
