package test_json

import (
	"encoding/json"
	"github.com/bytedance/sonic"
	"github.com/mailru/easyjson"
	"go.uber.org/zap"
	"github.com/valyala/fastjson"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"reflect"
	"testing"
	"fmt"
)

func Benchmark_StdJson_Marshal(b *testing.B) {
	testData := TestStruct{}
	if err := json.Unmarshal(mediumData, &testData); err != nil {
		b.<PERSON>al(err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		if _, err := json.<PERSON>(testData); err != nil {
			b.<PERSON>al(err)
		}
	}
}

func Benchmark_StdJson_Unmarshal(b *testing.B) {
	for i := 0; i < b.N; i++ {
		testData := TestStruct{}
		if err := json.Unmarshal(mediumData, &testData); err != nil {
			b.<PERSON>al(err)
		}
	}
}

func Benchmark_Fastjson(b *testing.B) {
	for i := 0; i < b.N; i++ {
		var p fastjson.Parser
		v, err := p.Parse<PERSON>ytes(mediumData)
		if err != nil {
			b.Fatal(err)
		}

		v.Bool()
	}
}

func Benchmark_Easyjson(b *testing.B) {
	for i := 0; i < b.N; i++ {
		testData := TestStruct{}
		if err := easyjson.Unmarshal(mediumData, &testData); err != nil {
			b.Fatal(err)
		}
	}
}

func Benchmark_EasyJson_Marshal(b *testing.B) {
	testData := TestStruct{}
	if err := easyjson.Unmarshal(mediumData, &testData); err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		if _, err := easyjson.Marshal(testData); err != nil {
			b.Fatal(err)
		}
	}
}

func Benchmark_EasyJson_Unmarshal(b *testing.B) {
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		testData := TestStruct{}
		if err := easyjson.Unmarshal(mediumData, &testData); err != nil {
			b.Fatal(err)
		}
	}
}

func TestEasyJsonWriter(t *testing.T) {
	testData := TestStruct{}
	if err := easyjson.Unmarshal(mediumData, &testData); err != nil {
		t.Fatal(err)
	}

	bufWriter := buffer_pool.NewBufferWriter()
	defer bufWriter.Release()

	written, err := easyjson.MarshalToWriter(&testData, bufWriter)
	if err != nil {
		t.Fatal(err)
	}

	zap.L().Info("written", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(written)))))

	if err := easyjson.Unmarshal(bufWriter.Get(), &testData); err != nil {
		t.Fatal(err)
	}
}

func TestSonicJson(t *testing.T) {
	testData := TestStruct{}
	if err := sonic.Unmarshal(mediumData, &testData); err != nil {
		t.Fatal(err)
	}

	result, err := sonic.MarshalString(testData)
	if err != nil {
		t.Fatal(err)
	}

	zap.L().Info("result", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", result)))))
}

func Benchmark_SonicJson_ConfigDefault_Marshal(b *testing.B) {
	testData := &TestSonicStruct{}

	err := sonic.Pretouch(reflect.TypeOf(testData))
	if err != nil {
		b.Fatal(err)
	}

	if err := sonic.ConfigDefault.Unmarshal(mediumData, testData); err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		if _, err := sonic.ConfigDefault.Marshal(testData); err != nil {
			b.Fatal(err)
		}
	}
}

func Benchmark_SonicJson_ConfigDefault_Unmarshal(b *testing.B) {
	dataType := &TestSonicStruct{}
	err := sonic.Pretouch(reflect.TypeOf(dataType))
	if err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		testData := TestSonicStruct{}
		if err := sonic.ConfigDefault.Unmarshal(mediumData, &testData); err != nil {
			b.Fatal(err)
		}
	}
}

func Benchmark_SonicJson_ConfigStd_Marshal(b *testing.B) {
	testData := &TestSonicStruct{}

	err := sonic.Pretouch(reflect.TypeOf(testData))
	if err != nil {
		b.Fatal(err)
	}

	if err := sonic.ConfigStd.Unmarshal(mediumData, testData); err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		if _, err := sonic.ConfigStd.Marshal(testData); err != nil {
			b.Fatal(err)
		}
	}
}

func Benchmark_SonicJson_ConfigStd_Unmarshal(b *testing.B) {
	dataType := &TestSonicStruct{}
	err := sonic.Pretouch(reflect.TypeOf(dataType))
	if err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		testData := TestSonicStruct{}
		if err := sonic.ConfigStd.Unmarshal(mediumData, &testData); err != nil {
			b.Fatal(err)
		}
	}
}

func Benchmark_SonicJson_ConfigFastest_Marshal(b *testing.B) {
	testData := &TestSonicStruct{}

	err := sonic.Pretouch(reflect.TypeOf(testData))
	if err != nil {
		b.Fatal(err)
	}

	if err := sonic.ConfigFastest.Unmarshal(mediumData, testData); err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		if _, err := sonic.ConfigFastest.Marshal(testData); err != nil {
			b.Fatal(err)
		}
	}
}

func Benchmark_SonicJson_ConfigFastest_Unmarshal(b *testing.B) {
	dataType := &TestSonicStruct{}
	err := sonic.Pretouch(reflect.TypeOf(dataType))
	if err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		testData := TestSonicStruct{}
		if err := sonic.ConfigFastest.Unmarshal(mediumData, &testData); err != nil {
			b.Fatal(err)
		}
	}
}
