package app_info_loader

import (
	"encoding/json"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
	"fmt"
)

type AppInfoDo struct {
	Id                 int64     `xorm:"id"`
	MediaId            int64     `xorm:"media_id"`
	Name               string    `xorm:"name"`
	AccessType         int       `xorm:"access_type"`
	TrafficType        int       `xorm:"traffic_type"`
	OsType             int       `xorm:"os_type"`
	AppStore           int       `xorm:"app_store"`
	DownloadUrl        string    `xorm:"download_url"`
	AndroidPackageName string    `xorm:"android_package_name"`
	IosPackageName     string    `xorm:"ios_package_name"`
	LogConfig          string    `xorm:"log_config"` // 日志配置
	AppList            string    `xorm:"app_list"`   // 应用（包名）列表
	Status             int       `xorm:"status"`
	Creator            int64     `xorm:"creator"`
	LastUpdater        int64     `xorm:"last_updater"`
	PermGroup          int64     `xorm:"perm_group"`
	UpdatedAt          time.Time `xorm:"update_time"`
	CreatedAt          time.Time `xorm:"create_time"`
}

type MysqlAppInfoLoader struct {
	engine   *xorm.Engine
	itemList AppInfoList
	itemMap  map[utils.ID]*AppInfo

	intervalSec int
	term        chan struct{}
}

func NewMysqlAppInfoLoader(engine *xorm.Engine, intervalSec int) *MysqlAppInfoLoader {
	return &MysqlAppInfoLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlAppInfoLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlAppInfoLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlAppInfoLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlAppInfoLoader) GetById(id utils.ID) *AppInfo {
	return loader.itemMap[id]
}

func (loader *MysqlAppInfoLoader) GetList() AppInfoList {
	return loader.itemList
}

type MysqlAppInfoDo struct {
}

func (loader *MysqlAppInfoLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*AppInfoDo, 0)
	err := session.Table("app_info").Where("status = 0").Find(&doList)
	if err != nil {
		return err
	}

	itemList := make(AppInfoList, 0, len(doList))
	for _, info := range doList {
		appInfo := &AppInfo{
			Id:          info.Id,
			Name:        info.Name,
			MediaId:     info.MediaId,
			AccessType:  info.AccessType,
			TrafficType: info.TrafficType,
			OsType:      info.OsType,
			AppStore:    info.AppStore,
			DownloadUrl: info.DownloadUrl,
			AndroidPkg:  info.AndroidPackageName,
			IosPkg:      info.IosPackageName,
			Status:      info.Status,
			CreateTime:  info.CreatedAt,
			UpdateTime:  info.UpdatedAt,
		}

		var logConfig *LogConfig
		if len(info.LogConfig) < 3 {
			logConfig = NewLogConfig()
		} else {
			logConfig = &LogConfig{}
			err := json.Unmarshal([]byte(info.LogConfig), logConfig)
			if err != nil {
				zap.L().Error("unmarshal logConfig err", zap.Error(err), zap.String("logConfig", fmt.Sprintf("%v", info.LogConfig)))
			}
		}
		appInfo.LogConfig = logConfig

		var appList []string
		if len(info.AppList) < 3 {
			appList = []string{}
		} else {
			err := json.Unmarshal([]byte(info.AppList), &appList)
			if err != nil {
				zap.L().Error("unmarshal appList err", zap.Error(err), zap.String("appList", fmt.Sprintf("%v", info.AppList)))
			}
		}
		appInfo.AppList = appList

		itemList = append(itemList, appInfo)
	}

	zap.L().Info("load app_info", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", len(itemList))))))

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()

	return err
}
