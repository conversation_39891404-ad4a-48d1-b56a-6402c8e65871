package ad_monitor_info_loader

import (
	"encoding/json"
	"time"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"xorm.io/xorm"
)

type MysqlAdMonitorInfoDo struct {
	Id                int32  `xorm:"id"`
	Name              string `xorm:"name"`
	LandingUrl        string `xorm:"landing_url"`
	DeepLinkUrl       string `xorm:"deep_link_url"`
	ClickMonitor      string `xorm:"click_monitor"`
	ImpressionMonitor string `xorm:"impression_monitor"`
	DeepLinkMonitor   string `xorm:"deep_link_monitor"`
	LandingStayTime   int    `xorm:"landing_stay_time"`
	ClickDelay        int    `xorm:"click_delay"`
	DelayMonitorUrl   string `xorm:"delay_monitor_url"`
	CpaEventType      string `xorm:"cpa_event_type"`
	CpaCallbackType   string `xorm:"cpa_callback_type"`
	C2sClickUrl       string `xorm:"c2s_click_url"`
	LandingType       int    `xorm:"landing_type"`
	AppName           string `xorm:"app_name"`
	AppPackageName    string `xorm:"app_package_name"`
	AppPackageSize    int    `xorm:"app_package_size"`
	AppVersion        string `xorm:"app_version"`
	AppDeveloper      string `xorm:"app_developer"`
	AppDownloadUrl    string `xorm:"app_download_url"`
	AppPrivacyUrl     string `xorm:"app_privacy_url"`
	AppPermissionUrl  string `xorm:"app_permission_url"`
	AppDesc           string `xorm:"app_desc"`
	AppDescUrl        string `xorm:"app_desc_url"`
	AppIconUrl        string `xorm:"app_icon_url"`
	WechatAppId       string `xorm:"wechat_app_id"`
	WechatAppPath     string `xorm:"wechat_app_path"`
	PddPid            string `xorm:"pdd_pid"`
	PddGoodsId        string `xorm:"pdd_goods_id"`
	AdvertiserId      int32  `xorm:"advertiser_id"`
	ProductId         int32  `xorm:"product_id"`
}

func (do *MysqlAdMonitorInfoDo) ToEntity() (*entity.AdMonitorInfo, error) {
	result := &entity.AdMonitorInfo{
		Id:              utils.ID(do.Id),
		Name:            do.Name,
		LandingUrl:      do.LandingUrl,
		DownloadUrl:     do.AppDownloadUrl,
		LandingStayTime: do.LandingStayTime,
		DeepLinkUrl:     do.DeepLinkUrl,
		C2sClickUrl:     do.C2sClickUrl,
		ClickDelay:      do.ClickDelay,
		CpaEventType:    do.CpaEventType,
		LandingAction:   entity.LandingType(do.LandingType),
		AppInfo: entity.AppInfo{
			PackageName: do.AppPackageName,
			AppName:     do.AppName,
			Icon:        do.AppIconUrl,
			AppVersion:  do.AppVersion,
			PackageSize: do.AppPackageSize,
			Privacy:     do.AppPrivacyUrl,
			Permission:  do.AppPermissionUrl,
			AppDesc:     do.AppDesc,
			AppDescURL:  do.AppDescUrl,
			Develop:     do.AppDeveloper,
		},
		PddGoodsId:   do.PddGoodsId,
		PddPid:       do.PddPid,
		AdvertiserId: utils.ID(do.AdvertiserId),
		ProductId:    utils.ID(do.ProductId),
	}

	if result.LandingAction == entity.LandingTypeWeChatProgram {
		result.AppInfo.WechatExt = &entity.WechatExt{
			ProgramId:   do.WechatAppId,
			ProgramPath: do.WechatAppPath,
		}
	}

	if len(do.ClickMonitor) != 0 {
		if err := json.Unmarshal([]byte(do.ClickMonitor), &result.ClickMonitorList); err != nil {
			return nil, err
		}
	}

	if len(do.ImpressionMonitor) != 0 {
		if err := json.Unmarshal([]byte(do.ImpressionMonitor), &result.ImpressionMonitorList); err != nil {
			return nil, err
		}
	}

	if len(do.DeepLinkMonitor) != 0 {
		if err := json.Unmarshal([]byte(do.DeepLinkMonitor), &result.DeepLinkMonitorList); err != nil {
			return nil, err
		}
	}

	if len(do.DelayMonitorUrl) != 0 {
		if err := json.Unmarshal([]byte(do.DelayMonitorUrl), &result.DelayMonitorUrlList); err != nil {
			return nil, err
		}
	}

	if len(do.CpaCallbackType) != 0 {
		_ = json.Unmarshal([]byte(do.CpaCallbackType), &result.CpaCallbackType)
	}

	return result, nil
}

type MysqlAdMonitorInfoLoader struct {
	engine *xorm.Engine

	itemList entity.AdMonitorInfoList
	itemMap  map[utils.ID]*entity.AdMonitorInfo

	intervalSec int
	term        chan struct{}
}

func NewMysqlAdMonitorInfoLoader(engine *xorm.Engine, intervalSec int) *MysqlAdMonitorInfoLoader {
	return &MysqlAdMonitorInfoLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlAdMonitorInfoLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlAdMonitorInfoLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlAdMonitorInfoLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlAdMonitorInfoLoader) GetAdMonitorInfoList() entity.AdMonitorInfoList {
	return loader.itemList
}

func (loader *MysqlAdMonitorInfoLoader) GetAdMonitorInfoById(monitorId utils.ID) *entity.AdMonitorInfo {
	return loader.itemMap[monitorId]
}

func (loader *MysqlAdMonitorInfoLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlAdMonitorInfoDo, 0)
	if err := session.Table("ad_monitor_info").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	itemList := make(entity.AdMonitorInfoList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		itemList = append(itemList, item)
	}

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}
