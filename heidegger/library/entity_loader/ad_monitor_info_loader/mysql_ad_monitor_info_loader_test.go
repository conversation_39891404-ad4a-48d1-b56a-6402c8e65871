package ad_monitor_info_loader

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/test_helper"
	"testing"
	"fmt"
)

func TestMysqlAdMonitorInfoLoader_Load(t *testing.T) {
	engine := test_helper.GetTestXormEngine()
	loader := NewMysqlAdMonitorInfoLoader(engine, 60)

	if err := loader.Load(); err != nil {
		panic(err)
	}

	info := loader.GetAdMonitorInfoById(90000)
	zap.L().Info("monitor", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", info)))))
}
