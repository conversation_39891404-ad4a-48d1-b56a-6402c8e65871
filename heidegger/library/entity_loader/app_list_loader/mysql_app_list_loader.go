package app_list_loader

import (
	"encoding/json"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

/*
todo
暂时不加载,没有用到映射表
*/

type MysqlAppListDo struct {
	Id                    int32  `xorm:"id"`
	Name                  string `xorm:"name"`
	AndroidPackageName    string `xorm:"android_package_name"`
	IosPackageName        string `xorm:"ios_package_name"`
	Category              string `xorm:"category"`
	SchemeDeepLink        string `xorm:"scheme_deep_link"`
	UniversalDeepLink     string `xorm:"universal_deep_link"`
	AdvertiserDisplayName string `xorm:"advertiser_display_name"`
	AdvertiserDisplayLogo string `xorm:"advertiser_display_logo"`
	Status                int    `xorm:"status"`
}

func (do *MysqlAppListDo) ToEntity() (*entity.AppList, error) {
	result := &entity.AppList{
		Id:                    utils.ID(do.Id),
		Name:                  do.Name,
		AndroidPackageName:    do.AndroidPackageName,
		IosPackageName:        do.IosPackageName,
		Category:              do.Category,
		AdvertiserDisplayName: do.AdvertiserDisplayName,
		AdvertiserDisplayLogo: do.AdvertiserDisplayLogo,
		Status:                do.Status,
	}

	if len(do.SchemeDeepLink) > 0 {
		if err := json.Unmarshal([]byte(do.SchemeDeepLink), &result.SchemeDeepLink); err != nil {
			zap.L().Error("[MysqlAppListDo] json.Unmarshal SchemeDeepLink, id: error", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(do.Id)))), zap.Error(err))
		}
	}

	if len(do.UniversalDeepLink) > 0 {
		if err := json.Unmarshal([]byte(do.UniversalDeepLink), &result.UniversalDeepLink); err != nil {
			zap.L().Error("[MysqlAppListDo] json.Unmarshal UniversalDeepLink, id: error", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(do.Id)))), zap.Error(err))
		}
	}

	return result, nil
}

type MysqlAppListLoader struct {
	engine *xorm.Engine

	appList             entity.AppListList
	appMap              map[utils.ID]*entity.AppList
	androidAppBundleMap map[string]*entity.AppList
	iosAppBundleMap     map[string]*entity.AppList
	appSchemeBundleMap  map[string]*entity.AppList

	intervalSec int
	term        chan struct{}
}

func NewMysqlAppListLoader(engine *xorm.Engine, intervalSec int) *MysqlAppListLoader {
	return &MysqlAppListLoader{
		engine:      engine,
		intervalSec: intervalSec,

		term: make(chan struct{}),
	}
}

func (loader *MysqlAppListLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlAppListDo, 0)
	if err := session.Table("app_list").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	appList := make(entity.AppListList, 0, len(doList))
	androidAppBundleMap := make(map[string]*entity.AppList, 0)
	iosAppBundleMap := make(map[string]*entity.AppList, 0)
	appSchemeBundleMap := make(map[string]*entity.AppList, 0)
	for _, do := range doList {
		app, err := do.ToEntity()
		if err != nil {
			continue
		}
		appList = append(appList, app)
		if len(app.IosPackageName) > 0 {
			iosAppBundleMap[app.IosPackageName] = app
		}

		if len(app.AndroidPackageName) > 0 {
			androidAppBundleMap[app.AndroidPackageName] = app
		}

		for _, dp := range app.UniversalDeepLink {
			if len(dp) > 0 {
				appSchemeBundleMap[dp] = app
			}
		}

		for _, dp := range app.SchemeDeepLink {
			if len(dp) > 0 {
				appSchemeBundleMap[dp] = app
			}
		}
	}

	loader.appList = appList
	loader.appMap = loader.appList.ToIdMap()
	loader.androidAppBundleMap = androidAppBundleMap
	loader.iosAppBundleMap = iosAppBundleMap
	loader.appSchemeBundleMap = appSchemeBundleMap

	return nil
}

func (loader *MysqlAppListLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlAppListLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlAppListLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlAppListLoader) GetAppList() entity.AppListList {
	return loader.appList
}

func (loader *MysqlAppListLoader) GetAppById(appId utils.ID) *entity.AppList {
	return loader.appMap[appId]
}

func (loader *MysqlAppListLoader) GetAppByAndroidBundle(bundle string) *entity.AppList {
	return loader.androidAppBundleMap[bundle]
}

func (loader *MysqlAppListLoader) GetAppByIosBundle(bundle string) *entity.AppList {
	return loader.iosAppBundleMap[bundle]
}

func (loader *MysqlAppListLoader) GetAppByBundle(bundle string) *entity.AppList {
	if _, ok := loader.androidAppBundleMap[bundle]; ok {
		return loader.androidAppBundleMap[bundle]
	}

	return loader.iosAppBundleMap[bundle]
}

func (loader *MysqlAppListLoader) GetAppByScheme(scheme string) *entity.AppList {
	return loader.appSchemeBundleMap[scheme]
}
