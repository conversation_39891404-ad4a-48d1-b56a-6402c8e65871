package app_list_loader

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

/*
todo
暂时不加载,没有用到映射表
*/

type MysqlExternalMappingDo struct {
	Id           int32  `json:"id"`
	ExternalType string `json:"external_type"` // 外部上下游类型，dsp/media
	SourceName   string `json:"source_name"`   // 原始应用名，App Name或其他Name
	SourceValue  string `json:"source_value"`  // 原始包名，可能是包名和id
	SourceType   string `json:"source_type"`   // 原始协议，媒体ID/DSP ID
	LocalId      int32  `json:"local_id"`      // 映射为本地表的id
	Status       int32  `json:"status"`
}

func (do *MysqlExternalMappingDo) ToEntity() (*entity.ExternalMapping, error) {
	result := &entity.ExternalMapping{
		Id:           utils.ID(do.Id),
		ExternalType: do.ExternalType,
		SourceName:   do.SourceName,
		SourceValue:  do.SourceValue,
		SourceType:   do.SourceType,
		LocalId:      utils.ID(do.LocalId),
	}

	return result, nil
}

type MysqlExternalMappingLoader struct {
	engine *xorm.Engine

	externalMappingList entity.ExternalMappingList
	externalMappingMap  map[utils.ID]*entity.ExternalMapping
	mediaAppMappingMap  map[string]*entity.ExternalMapping
	dspAppMappingMap    map[string]*entity.ExternalMapping

	intervalSec int
	term        chan struct{}
}

func NewMysqlExternalMappingLoader(engine *xorm.Engine, intervalSec int) *MysqlExternalMappingLoader {
	return &MysqlExternalMappingLoader{
		engine:      engine,
		intervalSec: intervalSec,

		term: make(chan struct{}),
	}
}

func (loader *MysqlExternalMappingLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlExternalMappingDo, 0)
	if err := session.Table("external_mapping").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	externalMappingList := make(entity.ExternalMappingList, 0, len(doList))
	mediaAppMappingMap := make(map[string]*entity.ExternalMapping)
	dspAppMappingMap := make(map[string]*entity.ExternalMapping)

	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			continue
		}
		externalMappingList = append(externalMappingList, item)

		if item.ExternalType == "media" {
			mediaAppMappingMap[item.SourceType+"_"+item.SourceValue] = item
		} else if item.ExternalType == "dsp" {
			dspAppMappingMap[item.SourceType+"_"+item.LocalId.String()] = item
		}
	}

	loader.externalMappingList = externalMappingList
	loader.externalMappingMap = loader.externalMappingList.ToIdMap()
	loader.mediaAppMappingMap = mediaAppMappingMap
	loader.dspAppMappingMap = dspAppMappingMap

	return nil
}

func (loader *MysqlExternalMappingLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlExternalMappingLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlExternalMappingLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlExternalMappingLoader) GetExternalMappingList() entity.ExternalMappingList {
	return loader.externalMappingList
}

func (loader *MysqlExternalMappingLoader) GetExternalMappingById(id utils.ID) *entity.ExternalMapping {
	return loader.externalMappingMap[id]
}

func (loader *MysqlExternalMappingLoader) GetMediaAppMappingMapByKey(key string) *entity.ExternalMapping {
	return loader.mediaAppMappingMap[key]
}

func (loader *MysqlExternalMappingLoader) GetDspAppMappingMapByKey(key string) *entity.ExternalMapping {
	return loader.dspAppMappingMap[key]
}
