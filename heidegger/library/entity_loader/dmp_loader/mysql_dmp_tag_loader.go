package dmp_loader

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

type MysqlDmpTagDo struct {
	DmpTagId        int32  `xorm:"dmp_tag_id"`
	DmpId           int32  `xorm:"dmp_id"`
	Name            string `xorm:"name"`
	Desc            string `xorm:"desc"`
	DmpQueryTagId   string `xorm:"dmp_query_tag_id"`
	CacheTimeSecond int32  `xorm:"cache_time_second"`
}

func (do *MysqlDmpTagDo) ToEntity() (*entity.DmpTag, error) {
	result := &entity.DmpTag{
		DmpTagId:        utils.ID(do.DmpTagId),
		DmpId:           utils.ID(do.DmpId),
		Name:            do.Name,
		Desc:            do.Desc,
		DmpQueryTagId:   do.DmpQueryTagId,
		CacheTimeSecond: do.CacheTimeSecond,
	}

	return result, nil
}

type MysqlDmpTagLoader struct {
	engine *xorm.Engine

	itemList entity.DmpTagList
	itemMap  map[utils.ID]*entity.DmpTag

	intervalSec int
	term        chan struct{}
}

func NewMysqlDmpTagLoader(engine *xorm.Engine, intervalSec int) *MysqlDmpTagLoader {
	return &MysqlDmpTagLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlDmpTagLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlDmpTagLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlDmpTagLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlDmpTagLoader) GetDmpTagList() entity.DmpTagList {
	return loader.itemList
}

func (loader *MysqlDmpTagLoader) GetDmpTagById(dspId utils.ID) *entity.DmpTag {
	return loader.itemMap[dspId]
}

func (loader *MysqlDmpTagLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlDmpTagDo, 0)
	if err := session.Table("dmp_tag").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	itemList := make(entity.DmpTagList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		itemList = append(itemList, item)
	}

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}
