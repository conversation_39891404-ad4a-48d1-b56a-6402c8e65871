package dmp_loader

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

type MysqlDmpProviderDo struct {
	DmpId          utils.ID `xorm:"dmp_id"`
	Name           string   `xorm:"name"`
	Protocol       string   `xorm:"protocol"`
	DmpConfig      string   `xorm:"dmp_config"`
	QpsLimit       int      `xorm:"qps_limit"`
	QueryPrice     int      `xorm:"query_price"`
	QueryPriceType int      `xorm:"query_price_type"`
}

func (do *MysqlDmpProviderDo) ToEntity() (*entity.DmpProvider, error) {
	result := &entity.DmpProvider{
		DmpId:          do.DmpId,
		Name:           do.Name,
		Protocol:       do.Protocol,
		DmpConfig:      do.DmpConfig,
		QpsLimit:       do.QpsLimit,
		QueryPrice:     do.QueryPrice,
		QueryPriceType: do.QueryPriceType,
	}

	return result, nil
}

type MysqlDmpProviderLoader struct {
	engine *xorm.Engine

	itemList entity.DmpProviderList
	itemMap  map[utils.ID]*entity.DmpProvider

	intervalSec int
	term        chan struct{}
}

func NewMysqlDmpProviderLoader(engine *xorm.Engine, intervalSec int) *MysqlDmpProviderLoader {
	return &MysqlDmpProviderLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlDmpProviderLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlDmpProviderLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlDmpProviderLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlDmpProviderLoader) GetDmpProviderList() entity.DmpProviderList {
	return loader.itemList
}

func (loader *MysqlDmpProviderLoader) GetDmpProviderById(dspId utils.ID) *entity.DmpProvider {
	return loader.itemMap[dspId]
}

func (loader *MysqlDmpProviderLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlDmpProviderDo, 0)
	if err := session.Table("dmp_provider").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	itemList := make(entity.DmpProviderList, 0, len(doList))
	for _, do := range doList {
		dsp, err := do.ToEntity()
		if err != nil {
			return err
		}

		itemList = append(itemList, dsp)
	}

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}
