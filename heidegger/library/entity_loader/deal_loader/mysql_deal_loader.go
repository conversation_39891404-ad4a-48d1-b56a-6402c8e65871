package deal_loader

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

type MysqlDealDo struct {
	Id         int32     `xorm:"id"`
	Name       string    `xorm:"name"`
	MediaId    int32     `xorm:"media_id"`
	DealId     string    `xorm:"deal_id"`
	DealPrice  uint32    `xorm:"deal_price"` // 单位: 分/CPM
	ExpiryTime time.Time `xorm:"expiry_time"`
	Status     int       `xorm:"status"`
}

func (do *MysqlDealDo) ToEntity() (*entity.DealInfo, error) {
	result := &entity.DealInfo{
		Id:         utils.ID(do.Id),
		Name:       do.Name,
		MediaId:    utils.ID(do.MediaId),
		DealId:     do.DealId,
		DealPrice:  do.DealPrice,
		ExpiryTime: do.ExpiryTime,
		Status:     do.Status,
	}
	result.DealKey = result.MediaId.String() + "_" + result.DealId

	return result, nil
}

type MysqlDealLoader struct {
	engine *xorm.Engine

	dealList entity.DealInfoList
	dealMap  map[utils.ID]*entity.DealInfo

	intervalSec int
	term        chan struct{}
}

func NewMysqlDealLoader(engine *xorm.Engine, intervalSec int) *MysqlDealLoader {
	return &MysqlDealLoader{
		engine:      engine,
		intervalSec: intervalSec,

		term: make(chan struct{}),
	}
}

func (loader *MysqlDealLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlDealDo, 0)
	if err := session.Table("media_deal").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	dealList := make(entity.DealInfoList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			continue
		}
		dealList = append(dealList, item)
	}

	loader.dealList = dealList
	loader.dealMap = loader.dealList.ToIdMap()

	return nil
}

func (loader *MysqlDealLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlDealLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlDealLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlDealLoader) GetDealList() entity.DealInfoList {
	return loader.dealList
}

func (loader *MysqlDealLoader) GetDealInfoById(id utils.ID) *entity.DealInfo {
	return loader.dealMap[id]
}
