package ad_loader

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/ad_monitor_info_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/budget_platform_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/creative_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/deal_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/device_allocation_setting_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/dmp_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/dsp_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/dsp_slot_info_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/frequency_control_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/sdk_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/traffic_strategy_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"xorm.io/xorm"
)

type MysqlAdDmpTagDo struct {
	TagId       int32  `json:"tagId"`
	AudienceTag string `json:"audienceTag"`
	Source      int32  `json:"source"`
	UseCache    bool   `json:"useCache"`
}

type MysqlAdDo struct {
	AdId                      int32  `xorm:"ad_id"`
	AdGroupId                 int32  `xorm:"ad_group_id"`
	AdIndexId                 int32  `xorm:"ad_index_id"`
	AdType                    int32  `xorm:"ad_type"`
	CreativeId                int32  `xorm:"creative_id"`
	DspId                     int32  `xorm:"dsp_id"`
	DspSlotId                 int32  `xorm:"dsp_slot_id"`
	FrequencyControlId        int32  `xorm:"frequency_control_id"`
	DeviceAllocationSettingId int32  `xorm:"device_allocation_setting_id"`
	AdMonitorInfoId           int32  `xorm:"ad_monitor_info_id"`
	BudgetPlatformId          int32  `xorm:"budget_platform_id"`
	Name                      string `xorm:"name"`
	AttributionRate           int    `xorm:"attribution_rate"`
	AttributionCallbackRate   int    `xorm:"attribution_callback_rate"`
	PacingType                int    `xorm:"pacing_type"`
	ImpressionLimit           int64  `xorm:"impression_limit"`
	ImpressionLimitByHour     string `xorm:"imp_dist_by_hour"`
	DmpTag                    string `xorm:"dmp_tag"`
	TrafficStrategyId         int    `xorm:"traffic_strategy_id"`
	SdkInteractionId          int32  `xorm:"sdk_interaction_id"`
	KpiTargetCtr              int    `xorm:"kpi_target_ctr"`
	QpsLimit                  int    `xorm:"qps_limit"`
	MediaDealIds              string `xorm:"media_deal_ids"`
	RankingModel              string `xorm:"ranking_model"`
	RankingModelControl       string `xorm:"ranking_model_control"`
	EventMapping              string `xorm:"event_mapping"`
	BlockLand                 string `xorm:"block_land"`
	WhiteLand                 string `xorm:"white_land"`
}

type EventMappingStruct struct {
	Before string `json:"before"`
	After  string `json:"after"`
}

func (do *MysqlAdDo) ToEntity() (*entity.Ad, error) {
	result := &entity.Ad{
		AdId:                      utils.ID(do.AdId),
		AdGroupId:                 utils.ID(do.AdGroupId),
		AdIndexId:                 utils.ID(do.AdIndexId),
		AdType:                    entity.AdType(do.AdType),
		CreativeId:                utils.ID(do.CreativeId),
		DspId:                     utils.ID(do.DspId),
		DspSlotId:                 utils.ID(do.DspSlotId),
		FrequencyControlId:        utils.ID(do.FrequencyControlId),
		DeviceAllocationSettingId: utils.ID(do.DeviceAllocationSettingId),
		AdMonitorInfoId:           utils.ID(do.AdMonitorInfoId),
		BudgetPlatformId:          utils.ID(do.BudgetPlatformId),
		TrafficStrategyId:         utils.ID(do.TrafficStrategyId),
		SdkInteractionId:          utils.ID(do.SdkInteractionId),
		Name:                      do.Name,
		AttributionRate:           do.AttributionRate,
		AttributionCallbackRate:   do.AttributionCallbackRate,
		PacingType:                entity.PacingType(do.PacingType),
		ImpressionLimit:           do.ImpressionLimit,
		Kpi: entity.Kpi{
			TargetCtr: float64(do.KpiTargetCtr) / 10000,
		},
		QpsLimit:     int64(do.QpsLimit),
		RankingModel: do.RankingModel,
		EventMapping: make(map[string]string),
	}

	if len(do.ImpressionLimitByHour) != 0 {
		if err := json.Unmarshal([]byte(do.ImpressionLimitByHour), &result.ImpressionLimitByHour); err != nil {
			return nil, fmt.Errorf("[MysqlAdDo] json.Unmarshal, id:%d error: %v", do.AdGroupId, err)
		}

		if len(result.ImpressionLimitByHour) == 0 {
			result.ImpressionLimitByHour = nil
		} else if len(result.ImpressionLimitByHour) != 24 {
			return nil, fmt.Errorf("[MysqlAdDo] ImpressionLimitByHour length error: %v", result.ImpressionLimitByHour)
		}
	}

	if len(do.RankingModelControl) != 0 {
		if err := json.Unmarshal([]byte(do.RankingModelControl), &result.RankingModelControl); err != nil {
			return nil, fmt.Errorf("[MysqlAdDo] json.Unmarshal, id:%d error: %v", do.AdGroupId, err)
		}
	}

	if len(do.EventMapping) > 0 {
		emList := make([]EventMappingStruct, 0)
		err := json.Unmarshal([]byte(do.EventMapping), &emList)
		if err == nil {
			for _, item := range emList {
				result.EventMapping[item.Before] = item.After
			}
		}
	}

	if len(do.BlockLand) > 0 {
		if err := json.Unmarshal([]byte(do.BlockLand), &result.BlockLand); err != nil {
			zap.L().Warn("[MysqlAdDo]adid: unmarshal BlockLand:%s err ", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(do.AdId)))), zap.Int64("param2", int64(do.BlockLand)))
		}
	}

	if len(do.WhiteLand) > 0 {
		if err := json.Unmarshal([]byte(do.WhiteLand), &result.WhiteLand); err != nil {
			zap.L().Warn("[MysqlAdDo]adid: unmarshal WhiteLand:%s err ", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(do.AdId)))), zap.Int64("param2", int64(do.WhiteLand)))
		}
	}

	return result, nil
}

func (do *MysqlAdDo) GetMysqlAdDmpTagDoList() []MysqlAdDmpTagDo {
	result := make([]MysqlAdDmpTagDo, 0)
	if len(do.DmpTag) != 0 {
		if err := json.Unmarshal([]byte(do.DmpTag), &result); err != nil {
			zap.L().Error("[MysqlAdDo] json.Unmarshal error", zap.Error(err))
		}
	}
	return result
}

func (do *MysqlAdDo) GetDealIdList() []int32 {
	result := make([]int32, 0)
	if len(do.MediaDealIds) != 0 {
		if err := json.Unmarshal([]byte(do.MediaDealIds), &result); err != nil {
			zap.L().Error("[MysqlAdDo] GetDealIdList json.Unmarshal error", zap.Error(err))
		}
	}
	return result
}

type MysqlAdLoader struct {
	engine                        *xorm.Engine
	adGroupLoader                 AdGroupLoader
	adIndexLoader                 AdIndexLoader
	adMonitorInfoLoader           ad_monitor_info_loader.AdMonitorInfoLoader
	frequencyControlLoader        frequency_control_loader.FrequencyControlLoader
	deviceAllocationSettingLoader device_allocation_setting_loader.DeviceAllocationSettingLoader
	budgetPlatformLoader          budget_platform_loader.BudgetPlatformLoader
	creativeLoader                creative_loader.CreativeLoader
	dmpTagLoader                  dmp_loader.DmpTagLoader
	dmpProviderLoader             dmp_loader.DmpProviderLoader
	trafficStrategyLoader         traffic_strategy_loader.TrafficStrategyLoader
	dealInfoLoader                deal_loader.DealInfoLoader
	dspLoader                     dsp_loader.DspLoader
	dspSlotInfoLoader             dsp_slot_info_loader.DspSlotInfoLoader
	sdkInteractionLoader          sdk_loader.SdkInteractionLoader

	adList entity.AdList
	adMap  map[utils.ID]*entity.Ad

	impressionMonitor    []string
	clickMonitor         []string
	actionCallbackUrl    string
	actionCallbackUrlMap map[string]string

	intervalSec int
	term        chan struct{}
}

func NewMysqlAdLoader(engine *xorm.Engine, intervalSec int) *MysqlAdLoader {
	return &MysqlAdLoader{
		engine:            engine,
		intervalSec:       intervalSec,
		impressionMonitor: []string{},
		clickMonitor:      []string{},

		term: make(chan struct{}),
	}
}

func (loader *MysqlAdLoader) WithAdGroupLoader(adGroupLoader AdGroupLoader) *MysqlAdLoader {
	loader.adGroupLoader = adGroupLoader
	return loader
}

func (loader *MysqlAdLoader) WithAdIndexLoader(adIndexLoader AdIndexLoader) *MysqlAdLoader {
	loader.adIndexLoader = adIndexLoader
	return loader
}

func (loader *MysqlAdLoader) WithFrequencyControlLoader(frequencyControlLoader frequency_control_loader.FrequencyControlLoader) *MysqlAdLoader {
	loader.frequencyControlLoader = frequencyControlLoader
	return loader
}

func (loader *MysqlAdLoader) WithDeviceAllocationSettingLoader(deviceAllocationSettingLoader device_allocation_setting_loader.DeviceAllocationSettingLoader) *MysqlAdLoader {
	loader.deviceAllocationSettingLoader = deviceAllocationSettingLoader
	return loader
}

func (loader *MysqlAdLoader) WithAdMonitorInfoLoader(adMonitorInfoLoader ad_monitor_info_loader.AdMonitorInfoLoader) *MysqlAdLoader {
	loader.adMonitorInfoLoader = adMonitorInfoLoader
	return loader
}

func (loader *MysqlAdLoader) WithDealInfoLoader(dealInfoLoader deal_loader.DealInfoLoader) *MysqlAdLoader {
	loader.dealInfoLoader = dealInfoLoader
	return loader
}

func (loader *MysqlAdLoader) WithSdkInteractionLoader(sdkInteractionLoader sdk_loader.SdkInteractionLoader) *MysqlAdLoader {
	loader.sdkInteractionLoader = sdkInteractionLoader
	return loader
}

func (loader *MysqlAdLoader) WithBudgetPlatformLoader(budgetPlatformLoader budget_platform_loader.BudgetPlatformLoader) *MysqlAdLoader {
	loader.budgetPlatformLoader = budgetPlatformLoader
	return loader
}

func (loader *MysqlAdLoader) WithCreativeLoader(creativeLoader creative_loader.CreativeLoader) *MysqlAdLoader {
	loader.creativeLoader = creativeLoader
	return loader
}

func (loader *MysqlAdLoader) WithDmpProviderLoader(dmpProviderLoader dmp_loader.DmpProviderLoader) *MysqlAdLoader {
	loader.dmpProviderLoader = dmpProviderLoader
	return loader
}

func (loader *MysqlAdLoader) WithDmpTagLoader(dmpTagLoader dmp_loader.DmpTagLoader) *MysqlAdLoader {
	loader.dmpTagLoader = dmpTagLoader
	return loader
}

func (loader *MysqlAdLoader) WithTrafficStrategyLoader(trafficStrategyLoader traffic_strategy_loader.TrafficStrategyLoader) *MysqlAdLoader {
	loader.trafficStrategyLoader = trafficStrategyLoader
	return loader
}

func (loader *MysqlAdLoader) WithDspLoader(dspLoader dsp_loader.DspLoader) *MysqlAdLoader {
	loader.dspLoader = dspLoader
	return loader
}

func (loader *MysqlAdLoader) WithDspSlotInfoLoader(dspSlotInfoLoader dsp_slot_info_loader.DspSlotInfoLoader) *MysqlAdLoader {
	loader.dspSlotInfoLoader = dspSlotInfoLoader
	return loader
}

func (loader *MysqlAdLoader) WithImpressionMonitor(url []string) *MysqlAdLoader {
	loader.impressionMonitor = url
	return loader
}

func (loader *MysqlAdLoader) WithClickMonitor(url []string) *MysqlAdLoader {
	loader.clickMonitor = url
	return loader
}

func (loader *MysqlAdLoader) WithActionCallbackUrl(url string) *MysqlAdLoader {
	loader.actionCallbackUrl = url
	return loader
}

func (loader *MysqlAdLoader) WithActionCallbackUrlMap(urlMap map[string]string) *MysqlAdLoader {
	loader.actionCallbackUrlMap = urlMap
	return loader
}

func (loader *MysqlAdLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlAdLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlAdLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlAdLoader) GetAdList() entity.AdList {
	return loader.adList
}

func (loader *MysqlAdLoader) GetAdById(adId utils.ID) *entity.Ad {
	return loader.adMap[adId]
}

func (loader *MysqlAdLoader) GetAdListByAdGroupId(adGroupId utils.ID) entity.AdList {
	adList := make(entity.AdList, 0)
	for _, ad := range loader.adList {
		if ad.AdGroupId == adGroupId {
			adList = append(adList, ad)
		}
	}
	return adList
}

func (loader *MysqlAdLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlAdDo, 0)
	if err := session.Table("ad_info").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	adList := make(entity.AdList, 0, len(doList))
	for _, do := range doList {
		ad, err := do.ToEntity()
		if err != nil {
			return err
		}

		if loader.adGroupLoader != nil {
			adGroup := loader.adGroupLoader.GetAdGroupById(ad.AdGroupId)
			if adGroup == nil {
				zap.L().Debug("[MysqlAdLoader] ad 's ad_group  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(ad.AdGroupId)))
				continue
			}
			ad.SetAdGroup(adGroup)
		}

		if loader.adIndexLoader != nil {
			adIndex := loader.adIndexLoader.GetAdIndexById(ad.AdIndexId)
			if adIndex == nil {
				zap.L().Warn("[MysqlAdLoader] ad 's ad_index  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(ad.AdIndexId)))
				continue
			}
			ad.SetAdIndex(adIndex)

			qpsLimitWeekHour := make(map[int]uint32)
			weekHours := adIndex.TargetInt["week_hour"]
			weekHourQpsLimit := adIndex.TargetInt["week_hour_qps_limit"]
			if len(weekHours) == len(weekHourQpsLimit) {
				for i, hour := range weekHours {
					limit := weekHourQpsLimit[i]
					if limit > ad.QpsLimit || limit <= 0 {
						continue
					}
					qpsLimitWeekHour[int(hour)] = uint32(limit)
				}
			} else {
				zap.L().Warn("[MysqlAdLoader] ad 's ad_index  week_hour_qps_limit's length not equal to week_hour's length", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(ad.AdIndexId)))
			}
			ad.QpsLimitWeekHour = qpsLimitWeekHour
		}

		if loader.frequencyControlLoader != nil {
			if ad.FrequencyControlId != 0 {
				frequencyControl := loader.frequencyControlLoader.GetFrequencyControlById(ad.FrequencyControlId)
				if frequencyControl == nil {
					zap.L().Warn("[MysqlAdLoader] ad 's frequency_control  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(ad.FrequencyControlId)))
					continue
				}
				ad.SetFrequencyControl(frequencyControl)
			}
		}

		if loader.deviceAllocationSettingLoader != nil {
			if ad.DeviceAllocationSettingId != 0 {
				deviceAllocationSetting := loader.deviceAllocationSettingLoader.GetDeviceAllocationSettingById(ad.DeviceAllocationSettingId)
				if deviceAllocationSetting == nil {
					zap.L().Warn("[MysqlAdLoader] ad 's device_allocation_setting  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(ad.DeviceAllocationSettingId)))
					continue
				}
				ad.SetDeviceAllocationSetting(deviceAllocationSetting)
			}
		}

		if loader.budgetPlatformLoader != nil {
			if ad.BudgetPlatformId != 0 {
				budgetPlatform := loader.budgetPlatformLoader.GetBudgetPlatformById(ad.BudgetPlatformId)
				if budgetPlatform == nil {
					zap.L().Warn("[MysqlAdLoader] ad 's budget_platform  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(ad.BudgetPlatformId)))
					continue
				}
				ad.SetBudgetPlatform(budgetPlatform)
			}
		}

		if loader.adMonitorInfoLoader != nil {
			if ad.AdMonitorInfoId != 0 {
				adMonitorInfo := loader.adMonitorInfoLoader.GetAdMonitorInfoById(ad.AdMonitorInfoId)
				if adMonitorInfo == nil {
					zap.L().Warn("[MysqlAdLoader] ad 's ad_monitor_info  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(ad.AdMonitorInfoId)))
					continue
				}
				ad.SetAdMonitorInfo(adMonitorInfo.Clone())
				ad.AppInfo = &entity.AppInfo{
					PackageName:    adMonitorInfo.AppInfo.PackageName,
					AppName:        adMonitorInfo.AppInfo.AppName,
					Icon:           adMonitorInfo.AppInfo.Icon,
					WechatExt:      adMonitorInfo.AppInfo.WechatExt,
					AppID:          adMonitorInfo.AppInfo.AppID,
					AppVersion:     adMonitorInfo.AppInfo.AppVersion,
					PackageSize:    adMonitorInfo.AppInfo.PackageSize,
					Privacy:        adMonitorInfo.AppInfo.Privacy,
					Permission:     adMonitorInfo.AppInfo.Permission,
					PermissionDesc: adMonitorInfo.AppInfo.PermissionDesc,
					AppDesc:        adMonitorInfo.AppInfo.AppDesc,
					AppDescURL:     adMonitorInfo.AppInfo.AppDescURL,
					Develop:        adMonitorInfo.AppInfo.Develop,
					AppBeian:       adMonitorInfo.AppInfo.AppBeian,
					AppAge:         adMonitorInfo.AppInfo.AppAge,
				}
				if adMonitorInfo.AppInfo.WechatExt != nil {
					ad.AppInfo.WechatExt = &entity.WechatExt{
						ProgramId:   adMonitorInfo.AppInfo.WechatExt.ProgramId,
						ProgramPath: adMonitorInfo.AppInfo.WechatExt.ProgramPath,
					}
				}
			}
		}

		if loader.creativeLoader != nil {
			if ad.CreativeId != 0 {
				creative := loader.creativeLoader.GetCreativeById(ad.CreativeId)
				if creative == nil {
					zap.L().Warn("[MysqlAdLoader] ad 's creative  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(ad.CreativeId)))
					continue
				}
				ad.SetCreative(creative)
			}
		}

		if loader.dmpTagLoader != nil {
			dmpTagDoList := do.GetMysqlAdDmpTagDoList()
			dmpTagItemList := make(entity.DmpTagRequirementItemList, 0, len(dmpTagDoList))
			for _, dmpTagDo := range dmpTagDoList {
				dmpTag := loader.dmpTagLoader.GetDmpTagById(utils.ID(dmpTagDo.TagId))
				if dmpTag == nil {
					zap.L().Warn("[MysqlAdLoader] ad 's dmp_tag  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(dmpTagDo.TagId)))
					continue
				}

				dmpTagItem := &entity.DmpTagRequirementItem{
					DmpTagId:    dmpTag.DmpTagId,
					DmpTagKey:   dmpTag.DmpQueryTagId,
					AudienceTag: dmpTag.DmpQueryTagId,
					Source:      utils.ID(dmpTag.DmpId),
					ExpireTime:  dmpTag.CacheTimeSecond,
					UseCache:    dmpTag.CacheTimeSecond > 0,
				}

				dmp := loader.dmpProviderLoader.GetDmpProviderById(dmpTag.DmpId)
				if dmp == nil {
					zap.L().Warn("[MysqlAdLoader] ad 's dmp  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(dmpTag.DmpId)))
					continue
				} else {
					dmpTagItem.DmpProtocol = dmp.Protocol
				}

				dmpTagItemList = append(dmpTagItemList, dmpTagItem)
			}
			ad.SetDmpTagRequirementItemList(dmpTagItemList)
		}

		if loader.trafficStrategyLoader != nil && ad.TrafficStrategyId != 0 {
			trafficStrategy := loader.trafficStrategyLoader.GetTrafficStrategyById(ad.TrafficStrategyId)
			if trafficStrategy == nil {
				zap.L().Warn("[MysqlAdLoader] ad 's traffic_strategy  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(ad.TrafficStrategyId)))
				continue
			}
			ad.SetTrafficStrategy(trafficStrategy)
		}

		if loader.dealInfoLoader != nil {
			dealIdList := do.GetDealIdList()
			for _, dealId := range dealIdList {
				dealInfo := loader.dealInfoLoader.GetDealInfoById(utils.ID(dealId))
				if dealInfo == nil {
					zap.L().Warn("[MysqlAdLoader] ad 's deal info  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(dealId)))
					continue
				}
				ad.AppendIndexDealInfo(dealInfo)
			}
		}

		if loader.dspLoader != nil && ad.DspId != 0 {
			dsp := loader.dspLoader.GetDspById(ad.DspId)
			if dsp == nil {
				zap.L().Warn("[MysqlAdLoader] ad 's dsp  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(ad.DspId)))
				continue
			}
			ad.SetDspInfo(dsp)
		}

		if loader.dspSlotInfoLoader != nil && ad.DspSlotId != 0 {
			dspSlotInfo := loader.dspSlotInfoLoader.GetDspSlotInfoById(ad.DspSlotId)
			if dspSlotInfo == nil {
				zap.L().Warn("[MysqlAdLoader] ad 's dsp_slot_info  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(ad.DspSlotId)))
				continue
			}
			ad.SetDspSlotInfo(dspSlotInfo)
		}

		if loader.sdkInteractionLoader != nil && ad.SdkInteractionId > 0 {
			sdkInteraction := loader.sdkInteractionLoader.GetSdkInteractionById(ad.SdkInteractionId)
			if sdkInteraction == nil {
				zap.L().Warn("[MysqlAdLoader] ad 's sdk_interaction  not found", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Int64("id", int64(ad.SdkInteractionId)))
			}
			ad.SetSdkInteraction(sdkInteraction)
		}

		if ad.GetAdMonitorInfo() == nil {
			ad.SetAdMonitorInfo(&entity.AdMonitorInfo{})
		}

		ad.GetAdMonitorInfo().PushFrontImpressionMonitorList(loader.impressionMonitor)
		ad.GetAdMonitorInfo().PushFrontClickMonitorList(loader.clickMonitor)
		if len(loader.actionCallbackUrl) != 0 {
			actionCallbackUrl := loader.actionCallbackUrl
			if ad.GetBudgetPlatform() != nil {
				switch ad.GetBudgetPlatform().Key {
				case entity.BudgetPlatformOneWay:
					actionCallbackUrl = strings.ReplaceAll(actionCallbackUrl, "__EVENT__", "__EVENT_TYPE__")
				case entity.BudgetPlatformSina:
					actionCallbackUrl = strings.ReplaceAll(actionCallbackUrl, "__EVENT__", "{ACTION_TYPE}")
				}
			}
			ad.GetAdMonitorInfo().SetActionCallbackUrl(actionCallbackUrl)
		}

		if len(loader.actionCallbackUrlMap) != 0 {
			if ad.GetBudgetPlatform() != nil {
				actionCallbackUrlMap := make(map[string]string, len(loader.actionCallbackUrlMap))
				switch ad.GetBudgetPlatform().Key {
				case entity.BudgetPlatformOneWay:
					for key, callbakUrl := range loader.actionCallbackUrlMap {
						actionCallbackUrlMap[key] = strings.ReplaceAll(callbakUrl, "__EVENT__", "__EVENT_TYPE__")
					}
				case entity.BudgetPlatformSina:
					for key, callbakUrl := range loader.actionCallbackUrlMap {
						actionCallbackUrlMap[key] = strings.ReplaceAll(callbakUrl, "__EVENT__", "{ACTION_TYPE}")
					}
				}
				ad.GetAdMonitorInfo().SetActionCallbackUrlMap(actionCallbackUrlMap)
			} else {
				ad.GetAdMonitorInfo().SetActionCallbackUrlMap(loader.actionCallbackUrlMap)
			}
		}

		if err := ad.Init(); err != nil {
			zap.L().Warn("[MysqlAdLoader] ad  init failed. err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(ad.AdId)))), zap.Error(err))
			continue
		}

		adList = append(adList, ad)
	}

	loader.adList = adList
	loader.adMap = loader.adList.ToIdMap()
	return nil
}
