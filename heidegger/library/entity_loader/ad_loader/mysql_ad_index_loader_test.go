package ad_loader

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/test_helper"
	"testing"
	"fmt"
)

func TestMysqlAdIndexLoader_Load(t *testing.T) {
	engine := test_helper.GetTestXormEngine()
	loader := NewMysqlAdIndexLoader(engine, 60)

	if err := loader.Load(); err != nil {
		panic(err)
	}

	if len(loader.adIndexList) == 0 {
		t.<PERSON><PERSON><PERSON>("adIndexList is empty")
	}

	zap.L().Info("adIndexList", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", loader.adIndexList)))))
}
