package sdk_loader

import (
	"time"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"xorm.io/xorm"
)

type MysqlSdkInteractionDo struct {
	Id                      int32  `xorm:"id"`
	Name                    string `xorm:"name"`
	Countdown               int32  `xorm:"countdown"`
	VideoSound              bool   `xorm:"video_sound"`
	CloseCountdown          int32  `xorm:"close_countdown"`
	Shake                   int32  `xorm:"shake"`
	Slide                   int32  `xorm:"slide"`
	Rotate                  int32  `xorm:"rotate"`
	FullScreenClick         bool   `xorm:"full_screen_click"`
	RedPacketRain           bool   `xorm:"red_packet_rain"`
	PopupCountdown          int32  `xorm:"popup_countdown"`
	PopupCloseCountdown     int32  `xorm:"popup_close_countdown"`
	FeedsGlide              bool   `xorm:"feed_glide"`                 //信息流是否开启滑动交互
	FeedsGlideDelay         int32  `xorm:"feed_glide_delay"`           //信息流滑动交互，多少秒之后展示，单位为s。默认3s
	FeedsGlideDisplay       int32  `xorm:"feeds_glide_display"`        //信息流滑动交互，展示时长，单位为s。默认5s
	AdCache                 bool   `xorm:"ad_cache"`                   //是否缓存广告，默认为false
	SkipSystemDeeplinkCheck bool   `xorm:"skip_system_deeplink_check"` //是否跳过系统deeplink检查，默认为false（不跳过）
}

func (do *MysqlSdkInteractionDo) ToEntity() (*entity.SdkInteraction, error) {
	result := &entity.SdkInteraction{
		Id:                      utils.ID(do.Id),
		Name:                    do.Name,
		Countdown:               do.Countdown,
		VideoSound:              do.VideoSound,
		CloseCountdown:          do.CloseCountdown,
		Shake:                   do.Shake,
		Slide:                   do.Slide,
		Rotate:                  do.Rotate,
		FullScreenClick:         do.FullScreenClick,
		RedPacketRain:           do.RedPacketRain,
		PopupCountdown:          do.PopupCountdown,
		PopupCloseCountdown:     do.PopupCloseCountdown,
		FeedsGlide:              do.FeedsGlide,
		FeedsGlideDelay:         do.FeedsGlideDelay,
		FeedsGlideDisplay:       do.FeedsGlideDisplay,
		AdCache:                 do.AdCache,
		SkipSystemDeeplinkCheck: do.SkipSystemDeeplinkCheck,
	}
	return result, nil
}

type MysqlSdkInteractionLoader struct {
	engine             *xorm.Engine
	sdkInteractionList entity.SdkInteractionList
	sdkInteractionMap  map[utils.ID]*entity.SdkInteraction
	intervalSec        int
	term               chan struct{}
}

func NewMysqlSdkInteractionLoader(engine *xorm.Engine, intervalSec int) *MysqlSdkInteractionLoader {
	return &MysqlSdkInteractionLoader{
		engine:             engine,
		sdkInteractionList: make(entity.SdkInteractionList, 0),
		sdkInteractionMap:  make(map[utils.ID]*entity.SdkInteraction),

		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlSdkInteractionLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlSdkInteractionLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlSdkInteractionLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlSdkInteractionLoader) GetSdkInteraction() entity.SdkInteractionList {
	return loader.sdkInteractionList
}

func (loader *MysqlSdkInteractionLoader) GetSdkInteractionById(id utils.ID) *entity.SdkInteraction {
	sdkInteraction, ok := loader.sdkInteractionMap[id]
	if !ok {
		return nil
	}
	return sdkInteraction
}

func (loader *MysqlSdkInteractionLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlSdkInteractionDo, 0)
	if err := session.Table("sdk_interaction").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	sdkInteractionList := make(entity.SdkInteractionList, 0, len(doList))
	for _, do := range doList {
		sdkInteraction, err := do.ToEntity()
		if err != nil {
			return err
		}

		sdkInteractionList = append(sdkInteractionList, sdkInteraction)
	}

	loader.sdkInteractionList = sdkInteractionList
	loader.sdkInteractionMap = sdkInteractionList.ToIdMap()
	return nil
}
