package blacklist_device_loader

import (
	"go.uber.org/zap"
	"time"

	"github.com/pkg/errors"
	"xorm.io/xorm"
)

type MysqlBlacklistDeviceDo struct {
	Id       int64  `json:"id" xorm:"id"`               // 主键
	DeviceId string `json:"device_id" xorm:"device_id"` // 设备ID
}

type MysqlBlacklistDeviceLoader struct {
	engine *xorm.Engine

	deviceMap map[string]struct{}

	intervalSec int
	term        chan struct{}
}

func NewMysqlBlacklistDeviceLoader(engine *xorm.Engine, intervalSec int) *MysqlBlacklistDeviceLoader {
	return &MysqlBlacklistDeviceLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlBlacklistDeviceLoader) Load() error {
	newItemMap := make(map[string]struct{})

	const batchSize = 10000
	var lastId int64 = 0

	session := loader.engine.NewSession()
	defer session.Close()
	for {
		zap.L().Debug("load blacklist device do batch, lastId", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(lastId)))))
		doList := make([]*MysqlBlacklistDeviceDo, 0, batchSize)
		err := session.Table("blacklist_device").Where("id > ?", lastId).
			OrderBy("id ASC").Limit(batchSize).Find(&doList)
		if err != nil {
			return errors.Wrapf(err, "load blacklist device do batch failed, lastId: %d", lastId)
		}
		zap.L().Debug("load blacklist device do batch success, lastId: , len", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(lastId)))), zap.Int64("param2", int64(len(doList))))
		if len(doList) < 0 {
			break
		}

		for _, do := range doList {
			// 前提：库中设备id是全小写格式
			newItemMap[do.DeviceId] = struct{}{}
		}
		if len(doList) < batchSize {
			break
		}
		lastId = doList[len(doList)-1].Id
	}

	loader.deviceMap = newItemMap
	zap.L().Debug("load blacklist device success, len", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(len(loader.deviceMap))))))
	return nil
}

func (loader *MysqlBlacklistDeviceLoader) Start() error {
	if err := loader.Load(); err != nil {
		return errors.Wrapf(err, "initial load blacklist device failed")
	}

	go func() {
		ticker := time.NewTicker(time.Duration(loader.intervalSec) * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlBlacklistDeviceLoader] loader.Load error", zap.Error(err))
				}
			case <-loader.term:
				return
			}
		}
	}()
	return nil
}

func (loader *MysqlBlacklistDeviceLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlBlacklistDeviceLoader) Contains(deviceId string) bool {
	_, ok := loader.deviceMap[deviceId]
	return ok
}
