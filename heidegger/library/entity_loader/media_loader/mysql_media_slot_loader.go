package media_loader

import (
	"encoding/json"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/ad_server/sdk_config_server/sdk_entity"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"xorm.io/xorm"
	"fmt"
)

type MysqlMediaSlotInfoDo struct {
	Id                     int32  `xorm:"id"`
	MediaId                int32  `xorm:"media_id"`
	Name                   string `xorm:"name"`
	SlotId                 string `xorm:"slot_id"`
	SlotType               int32  `xorm:"slot_type"`
	Timeout                int32  `xorm:"timeout"`
	CostType               int32  `xorm:"cost_type"`
	CostPrice              int32  `xorm:"cost_price"`
	CreativeTemplateIdList string `xorm:"creative_template_id_list"`
	BlockLand              string `xorm:"block_land"`
	WhiteLand              string `xorm:"white_land"`
	BlockKeyword           string `xorm:"block_keyword"`
	WhiteKeyword           string `xorm:"white_keyword"`
	BlockPkg               string `xorm:"block_pkg"`
	WhitePkg               string `xorm:"white_pkg"`
	BlockMurl              string `xorm:"block_murl"`
	WhiteMurl              string `xorm:"white_murl"`
	ExtData                string `xorm:"ext_data"`
}

type MysqlSdkSlotConfigDo struct {
	Id              int32  `xorm:"id"`
	Secure          int    `xorm:"secure"`
	BidFloor        string `xorm:"bid_floor"`
	Countdown       string `xorm:"countdown"`
	VideoSound      string `xorm:"video_sound"`
	CloseCountdown  string `xorm:"close_countdown"`
	ReqFreq         string `xorm:"req_freq"`
	ImpFreq         string `xorm:"imp_freq"`
	MinReqInterval  string `xorm:"min_req_interval"`
	Shake           string `xorm:"shake"`
	Slide           string `xorm:"slide"`
	FullScreenClick string `xorm:"full_screen_click"`
	DebugLog        bool   `xorm:"debug_log"`   // 是否开启debug日志。false-不开启（默认）；true-开启
	ClickRatio      int    `xorm:"click_ratio"` // 点击率控制
}

func (do *MysqlMediaSlotInfoDo) ToEntity() (*entity.MediaSlotInfo, error) {
	pkgWhitelist := make([]string, 0)
	pkgBlacklist := make([]string, 0)
	if len(do.WhitePkg) > 4 {
		if err := sonic.Unmarshal([]byte(do.WhitePkg), &pkgWhitelist); err != nil {
			zap.L().Warn("[MysqlMediaSlotInfoLoader] unmarshal WhitePkg error", zap.Error(err), zap.String("slotId", fmt.Sprintf("%v", do.Id)))
		}
	}
	if len(do.BlockPkg) > 4 {
		if err := sonic.Unmarshal([]byte(do.BlockPkg), &pkgBlacklist); err != nil {
			zap.L().Warn("[MysqlMediaSlotInfoLoader] unmarshal BlockPkg error", zap.Error(err), zap.String("slotId", fmt.Sprintf("%v", do.Id)))
		}
	}
	result := &entity.MediaSlotInfo{
		Id:           utils.ID(do.Id),
		MediaId:      utils.ID(do.MediaId),
		Name:         do.Name,
		MediaSlotKey: do.SlotId,
		SlotType:     entity.SlotType(do.SlotType),
		Timeout:      do.Timeout,
		CostType:     entity.CostType(do.CostType),
		CostPrice:    do.CostPrice,
		WhitePkg:     make(map[string]struct{}, len(pkgWhitelist)),
		BlockPkg:     make(map[string]struct{}, len(pkgBlacklist)),
	}

	if result.SlotType != entity.SlotTypeAttribution {
		result.SlotTagList = append(result.SlotTagList, entity.SlotTagBackgroundS2S.String())
	}

	if len(do.CreativeTemplateIdList) > 0 {
		if err := json.Unmarshal([]byte(do.CreativeTemplateIdList), &result.CreativeTemplateIdList); err != nil {
			return nil, err
		}
	}

	for _, pkg := range pkgWhitelist {
		result.WhitePkg[pkg] = struct{}{}
	}
	for _, pkg := range pkgBlacklist {
		result.BlockPkg[pkg] = struct{}{}
	}

	if len(do.BlockLand) > 4 {
		if err := json.Unmarshal([]byte(do.BlockLand), &result.BlockLand); err != nil {
			zap.L().Warn("[MysqlMediaSlotInfoDo] unmarshal BlockLand error", zap.Error(err), zap.String("slotId", fmt.Sprintf("%v", do.Id)))
		}
	}

	if len(do.WhiteLand) > 4 {
		if err := json.Unmarshal([]byte(do.WhiteLand), &result.WhiteLand); err != nil {
			zap.L().Warn("[MysqlMediaSlotInfoDo] unmarshal WhiteLand error", zap.Error(err), zap.String("slotId", fmt.Sprintf("%v", do.Id)))
		}
	}

	if len(do.BlockKeyword) > 4 {
		if err := json.Unmarshal([]byte(do.BlockKeyword), &result.BlockKeyword); err != nil {
			zap.L().Warn("[MysqlMediaSlotInfoDo] unmarshal BlockKeyword error", zap.Error(err), zap.String("slotId", fmt.Sprintf("%v", do.Id)))
		}
	}

	if len(do.WhiteKeyword) > 4 {
		if err := json.Unmarshal([]byte(do.WhiteKeyword), &result.WhiteKeyword); err != nil {
			zap.L().Warn("[MysqlMediaSlotInfoDo] unmarshal WhiteKeyword error", zap.Error(err), zap.String("slotId", fmt.Sprintf("%v", do.Id)))
		}
	}

	if len(do.BlockMurl) > 4 {
		if err := json.Unmarshal([]byte(do.BlockMurl), &result.BlockMurl); err != nil {
			zap.L().Warn("[MysqlMediaSlotInfoDo] unmarshal BlockMurl error", zap.Error(err), zap.String("slotId", fmt.Sprintf("%v", do.Id)))
		}
	}

	if len(do.WhiteMurl) > 4 {
		if err := json.Unmarshal([]byte(do.WhiteMurl), &result.WhiteMurl); err != nil {
			zap.L().Warn("[MysqlMediaSlotInfoDo] unmarshal WhiteMurl error", zap.Error(err), zap.String("slotId", fmt.Sprintf("%v", do.Id)))
		}
	}

	if len(do.ExtData) > 0 {
		if err := json.Unmarshal([]byte(do.ExtData), &result.ExtData); err != nil {
			zap.L().Warn("[MysqlMediaSlotInfoDo] unmarshal ExtData: %s error", zap.Error(err), zap.String("slotId", fmt.Sprintf("%v", do.Id)), zap.String("param1", fmt.Sprintf("%v", do.ExtData)))
		}
	}

	return result, nil
}

type MysqlMediaSlotLoader struct {
	engine *xorm.Engine

	itemList     entity.MediaSlotInfoList
	itemMap      map[utils.ID]*entity.MediaSlotInfo
	mediaItemMap map[utils.ID]entity.MediaSlotInfoList

	intervalSec int
	term        chan struct{}
}

func NewMysqlMediaSlotLoader(engine *xorm.Engine, intervalSec int) *MysqlMediaSlotLoader {
	return &MysqlMediaSlotLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlMediaSlotLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlMediaSlotLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlMediaSlotLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlMediaSlotLoader) GetMediaSlotList() entity.MediaSlotInfoList {
	return loader.itemList
}

func (loader *MysqlMediaSlotLoader) GetMediaSlotById(id utils.ID) *entity.MediaSlotInfo {
	return loader.itemMap[id]
}

func (loader *MysqlMediaSlotLoader) GetMediaSlotListByMediaId(mediaId utils.ID) entity.MediaSlotInfoList {
	return loader.mediaItemMap[mediaId]
}

func (loader *MysqlMediaSlotLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlMediaSlotInfoDo, 0)
	if err := session.Table("media_slot_info").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	sdkSlotConfigList := make([]*MysqlSdkSlotConfigDo, 0)
	if err := session.Table("sdk_slot_config").Where("status = 0").Find(&sdkSlotConfigList); err != nil {
		return err
	}
	sdkSlotConfigMap := make(map[int32]*MysqlSdkSlotConfigDo)
	for _, sdkSlotConfig := range sdkSlotConfigList {
		sdkSlotConfigMap[sdkSlotConfig.Id] = sdkSlotConfig
	}

	itemList := make(entity.MediaSlotInfoList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		if v, ok := sdkSlotConfigMap[do.Id]; ok {
			item.SdkSlotConfig = &sdk_entity.SdkSlotConfig{
				SlotId:     utils.ID(v.Id),
				Secure:     v.Secure,
				ClickRatio: v.ClickRatio,
			}
		}

		if err := item.Init(); err != nil {
			zap.L().Warn("[MysqlMediaSlotLoader] item.Init error", zap.Error(err))
			continue
		}

		itemList = append(itemList, item)
	}

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()
	loader.mediaItemMap = loader.itemList.ToMediaIdMap()

	return nil
}
