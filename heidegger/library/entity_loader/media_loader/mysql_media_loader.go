package media_loader

import (
	"encoding/json"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"xorm.io/xorm"
	"fmt"
)

type MysqlMediaInfoDo struct {
	Id              int32  `xorm:"id"`
	Name            string `xorm:"name"`
	ProtocolType    string `xorm:"protocol_type"`
	ClientSecret    string `xorm:"client_secret"`
	Ikey            string `xorm:"ikey"`
	Ekey            string `xorm:"ekey"`
	Timeout         int32  `xorm:"timeout"`
	AttributionType string `xorm:"attribution_type"`
	BlockLand       string `xorm:"block_land"`
	WhiteLand       string `xorm:"white_land"`
	BlockKeyword    string `xorm:"block_keyword"`
	WhiteKeyword    string `xorm:"white_keyword"`
	BlockPkg        string `xorm:"block_pkg"`
	WhitePkg        string `xorm:"white_pkg"`
	BlockMurl       string `xorm:"block_murl"`
	WhiteMurl       string `xorm:"white_murl"`
}

func (do *MysqlMediaInfoDo) ToEntity() (*entity.Media, error) {
	pkgWhitelist := make([]string, 0)
	pkgBlacklist := make([]string, 0)
	if len(do.WhitePkg) > 4 {
		if err := sonic.Unmarshal([]byte(do.WhitePkg), &pkgWhitelist); err != nil {
			zap.L().Warn("[MysqlMediaInfoLoader] unmarshal WhitePkg error", zap.Error(err), zap.String("mediaId", fmt.Sprintf("%v", do.Id)))
		}
	}
	if len(do.BlockPkg) > 4 {
		if err := sonic.Unmarshal([]byte(do.BlockPkg), &pkgBlacklist); err != nil {
			zap.L().Warn("[MysqlMediaInfoLoader] unmarshal BlockPkg error", zap.Error(err), zap.String("mediaId", fmt.Sprintf("%v", do.Id)))
		}
	}
	result := &entity.Media{
		Id:              utils.ID(do.Id),
		Name:            do.Name,
		ProtocolType:    do.ProtocolType,
		ClientSecret:    do.ClientSecret,
		Ikey:            do.Ikey,
		Ekey:            do.Ekey,
		Timeout:         do.Timeout,
		AttributionType: do.AttributionType,
		WhitePkg:        make(map[string]struct{}, len(pkgWhitelist)),
		BlockPkg:        make(map[string]struct{}, len(pkgBlacklist)),
	}

	for _, pkg := range pkgWhitelist {
		result.WhitePkg[pkg] = struct{}{}
	}
	for _, pkg := range pkgBlacklist {
		result.BlockPkg[pkg] = struct{}{}
	}

	if len(do.BlockLand) > 4 {
		if err := json.Unmarshal([]byte(do.BlockLand), &result.BlockLand); err != nil {
			zap.L().Warn("[MysqlMediaInfoDo] unmarshal BlockLand error", zap.Error(err), zap.String("mediaId", fmt.Sprintf("%v", do.Id)))
		}
	}

	if len(do.WhiteLand) > 4 {
		if err := json.Unmarshal([]byte(do.WhiteLand), &result.WhiteLand); err != nil {
			zap.L().Warn("[MysqlMediaInfoDo] unmarshal WhiteLand error", zap.Error(err), zap.String("mediaId", fmt.Sprintf("%v", do.Id)))
		}
	}

	if len(do.BlockKeyword) > 4 {
		if err := json.Unmarshal([]byte(do.BlockKeyword), &result.BlockKeyword); err != nil {
			zap.L().Warn("[MysqlMediaInfoDo] unmarshal BlockKeyword error", zap.Error(err), zap.String("mediaId", fmt.Sprintf("%v", do.Id)))
		}
	}

	if len(do.WhiteKeyword) > 4 {
		if err := json.Unmarshal([]byte(do.WhiteKeyword), &result.WhiteKeyword); err != nil {
			zap.L().Warn("[MysqlMediaInfoDo] unmarshal WhiteKeyword error", zap.Error(err), zap.String("mediaId", fmt.Sprintf("%v", do.Id)))
		}
	}

	if len(do.BlockMurl) > 4 {
		if err := json.Unmarshal([]byte(do.BlockMurl), &result.BlockMurl); err != nil {
			zap.L().Warn("[MysqlMediaInfoDo] unmarshal BlockMurl error", zap.Error(err), zap.String("mediaId", fmt.Sprintf("%v", do.Id)))
		}
	}

	if len(do.WhiteMurl) > 4 {
		if err := json.Unmarshal([]byte(do.WhiteMurl), &result.WhiteMurl); err != nil {
			zap.L().Warn("[MysqlMediaInfoDo] unmarshal WhiteMurl error", zap.Error(err), zap.String("mediaId", fmt.Sprintf("%v", do.Id)))
		}
	}

	return result, nil
}

type MysqlMediaLoader struct {
	engine *xorm.Engine

	itemList entity.MediaList
	itemMap  map[utils.ID]*entity.Media

	intervalSec int
	term        chan struct{}
}

func NewMysqlMediaLoader(engine *xorm.Engine, intervalSec int) *MysqlMediaLoader {
	return &MysqlMediaLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlMediaLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlMediaLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlMediaLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlMediaLoader) GetMediaList() entity.MediaList {
	return loader.itemList
}

func (loader *MysqlMediaLoader) GetMediaById(id utils.ID) *entity.Media {
	return loader.itemMap[id]
}

func (loader *MysqlMediaLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlMediaInfoDo, 0)
	if err := session.Table("media_info").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	itemList := make(entity.MediaList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		itemList = append(itemList, item)
	}

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}
