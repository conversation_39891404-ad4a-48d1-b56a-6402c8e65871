package traffic_strategy_loader

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/creative_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

type MysqlTrafficStrategyDo struct {
	Id                             utils.ID `xorm:"id"`
	Name                           string   `xorm:"name"`
	StrategyType                   int32    `xorm:"strategy_type"`
	AdId                           utils.ID `xorm:"ad_id"`
	AdGroupId                      utils.ID `xorm:"adgroup_id"`
	SlotId                         utils.ID `xorm:"slot_id"`
	BidPrice                       int32    `xorm:"bid_price"`
	BidType                        int32    `xorm:"bid_type"`
	ChargePrice                    int32    `xorm:"charge_price"`
	ChargePriceType                int32    `xorm:"charge_price_type"`
	ChargePriceUpperBound          int32    `xorm:"charge_price_upper_bound"`
	ChargePriceLowerBound          int32    `xorm:"charge_price_lower_bound"`
	ChargePriceUpperRatio          int32    `xorm:"charge_price_upper_ratio"`
	ChargePriceLowerRatio          int32    `xorm:"charge_price_lower_ratio"`
	BidFloor                       int32    `xorm:"bid_floor"`
	BidFloorType                   int32    `xorm:"bid_floor_type"`
	BiddingStrategyType            int32    `xorm:"bidding_strategy_type"`
	ChargeStrategyType             int32    `xorm:"charge_strategy_type"`
	BidFloorStrategyType           int32    `xorm:"bid_floor_strategy_type"`
	ProfitRatio                    int32    `xorm:"profit_ratio"`
	BiddingUpperProfitRatio        int32    `xorm:"bidding_upper_profit_ratio"`
	BiddingLowerProfitRatio        int32    `xorm:"bidding_lower_profit_ratio"`
	BiddingFloatRatio              int32    `xorm:"bidding_float_ratio"`
	UnderBidFloorProfitRate        int32    `xorm:"under_bid_floor_price_ratio"`
	UserScoreProfitRatio           string   `xorm:"user_score_profit_ratio"`
	DspPriceProfitRatio            string   `xorm:"dsp_price_profit_ratio "`
	TaskProfitRatio                string   `xorm:"task_profit_ratio"`
	BundleProfitRatio              string   `xorm:"bundle_profit_ratio"`
	UserScoreBroadcastRangeRatio   string   `xorm:"user_score_broadcast_range_ratio"`
	BroadcastControlStrategy       int32    `xorm:"broadcast_control_strategy"`
	BroadcastControlStrategyOption string   `xorm:"broadcast_control_strategy_option"`
	RequestModifier                string   `xorm:"request_modifier"`
	ResponseModifier               string   `xorm:"response_modifier"`
	Status                         int32    `xorm:"status"`
}

func (do *MysqlTrafficStrategyDo) ToEntity() (*entity.TrafficStrategy, error) {
	result := &entity.TrafficStrategy{
		Id:          do.Id,
		Name:        do.Name,
		Type:        entity.TrafficStrategyType(do.StrategyType),
		AdId:        utils.ID(do.AdId),
		AdGroupId:   utils.ID(do.AdGroupId),
		MediaSlotId: utils.ID(do.SlotId),
	}

	if do.BidPrice != -1 && do.BidType != -1 {
		result.SetBidPrice(entity.CreatePriceItem(uint32(do.BidPrice), entity.BidType(do.BidType)))
	}

	if do.ChargePrice != -1 && do.ChargePriceType != -1 {
		result.SetChargePrice(entity.CreatePriceItem(uint32(do.ChargePrice), entity.BidType(do.ChargePriceType)))
	}

	if do.ChargePriceUpperBound != -1 && do.ChargePriceLowerBound != -1 {
		result.SetChargePriceRatio(entity.CreatePriceRatioWithStaticBounds(uint32(do.ChargePriceLowerBound), uint32(do.ChargePriceUpperBound)))
	} else if do.ChargePriceUpperRatio != -1 && do.ChargePriceLowerRatio != -1 {
		result.SetChargePriceRatio(entity.CreatePriceRatioWithRateBounds(float64(do.ChargePriceLowerRatio)/10000.0, float64(do.ChargePriceUpperRatio)/10000.0))
	}

	if do.BidFloor != -1 && do.BidFloorType != -1 {
		result.SetBidFloor(entity.CreatePriceItem(uint32(do.BidFloor), entity.BidType(do.BidFloorType)))
	}

	if do.BiddingFloatRatio != -1 {
		result.SetBidFloorFloatRatio(entity.CreatePriceRatio(float64(do.BiddingFloatRatio) / 10000.0))
	}

	if do.BiddingStrategyType != -1 {
		result.SetBiddingStrategyType(entity.BiddingStrategyType(do.BiddingStrategyType))
	}

	if do.ChargeStrategyType != -1 {
		result.SetChargeStrategyType(entity.ChargeStrategyType(do.ChargeStrategyType))
	}

	if do.BidFloorStrategyType != -1 {
		result.SetBidFloorStrategyType(entity.BidFloorStrategyType(do.BidFloorStrategyType))
	}

	if do.UnderBidFloorProfitRate != -1 {
		result.SetUnderBidFloorProfitRatio(entity.CreatePriceRatio(float64(do.UnderBidFloorProfitRate) / 10000.0))
	}

	if do.ProfitRatio != -1 {
		result.SetProfitRatio(entity.CreatePriceRatio(float64(do.ProfitRatio) / 10000.0))
	} else if do.BiddingUpperProfitRatio != -1 && do.BiddingLowerProfitRatio != -1 {
		result.SetProfitRatio(entity.CreatePriceRatioWithRateBounds(float64(do.BiddingLowerProfitRatio)/10000.0, float64(do.BiddingUpperProfitRatio)/10000.0))
	}

	if len(do.TaskProfitRatio) != 0 {
		indexProfitRate, err := do.CovertTaskProfitRatio(do.TaskProfitRatio)
		if err != nil {
			return nil, err
		}

		if indexProfitRate != nil {
			result.SetIndexProfitRate(indexProfitRate)
		}
	}

	if len(do.BundleProfitRatio) != 0 {
		indexProfitRate, err := do.CovertBundleProfitRatio(do.BundleProfitRatio)
		if err != nil {
			return nil, err
		}

		if indexProfitRate != nil {
			result.SetIndexProfitRate(indexProfitRate)
		}
	}

	if len(do.UserScoreProfitRatio) != 0 {
		indexProfitRate, err := do.CovertUserScoreProfitRatio(do.UserScoreProfitRatio)
		if err != nil {
			return nil, err
		}

		if indexProfitRate != nil {
			result.SetIndexProfitRate(indexProfitRate)
		}
	}

	if len(do.DspPriceProfitRatio) != 0 {
		indexProfitRate, err := do.CovertDspPriceProfitRatio(do.DspPriceProfitRatio)
		if err != nil {
			return nil, err
		}

		if indexProfitRate != nil {
			result.SetIndexProfitRate(indexProfitRate)
		}
	}

	if len(do.UserScoreBroadcastRangeRatio) != 0 {
		indexBroadcastControl, err := do.CovertUserScoreBroadcastRangeRatio(do.UserScoreBroadcastRangeRatio)
		if err != nil {
			return nil, err
		}

		if indexBroadcastControl != nil {
			result.SetIndexBroadcastControl(*indexBroadcastControl)
		}
	}

	if do.BroadcastControlStrategy != -1 {
		result.SetBroadcastControlStrategy(entity.BroadcastControlStrategy(do.BroadcastControlStrategy))
	}

	if len(do.BroadcastControlStrategyOption) != 0 && do.BroadcastControlStrategyOption != "{}" {
		if err := json.Unmarshal([]byte(do.BroadcastControlStrategyOption), &result.BroadcastControlStrategyOption); err != nil {
			return nil, fmt.Errorf("[MysqlTrafficStrategyDo] json.Unmarshal broadcast_control_strategy_option error: %v", err)
		}
	}

	if do.AdId != 0 {
		result.Target.AdId = append(result.Target.AdId, int64(do.AdId))
	}

	if do.SlotId != 0 {
		result.Target.MediaSlotId = append(result.Target.MediaSlotId, int64(do.SlotId))
	}

	if do.AdGroupId != 0 {
		result.Target.AdGroupId = append(result.Target.AdGroupId, int64(do.AdGroupId))
	}

	//if len(do.GeoCode) != 0 {
	//	if err := json.Unmarshal([]byte(do.GeoCode), &result.Target.GeoCode); err != nil {
	//		return nil, fmt.Errorf("[MysqlTrafficStrategyDo] json.Unmarshal geo_code error: %v, data:'%s'", err, do.GeoCode)
	//	}
	//}
	//
	//if do.OsType > 0 {
	//	result.Target.OsType = append(result.Target.OsType, do.OsType)
	//}

	if len(do.RequestModifier) != 0 {
		if err := json.Unmarshal([]byte(do.RequestModifier), &result.TrafficRequestModifierList); err != nil {
			return nil, fmt.Errorf("[MysqlTrafficStrategyDo] json.Unmarshal request_modifier error: %v", err)
		}
	}

	if len(do.ResponseModifier) != 0 {
		if err := json.Unmarshal([]byte(do.ResponseModifier), &result.TrafficResponseModifierList); err != nil {
			return nil, fmt.Errorf("[MysqlTrafficStrategyDo] json.Unmarshal response_modifier error: %v", err)
		}
	}

	return result, nil
}

func (do *MysqlTrafficStrategyDo) CovertTaskProfitRatio(data string) (*entity.IndexProfitRatio, error) {
	type TaskProfitRatio struct {
		TaskId           string `json:"task_id"`
		LowerProfitRatio int    `json:"low_profit_ratio"`
		UpperProfitRatio int    `json:"high_profit_ratio"`
	}

	items := make([]*TaskProfitRatio, 0)
	if err := json.Unmarshal([]byte(data), &items); err != nil {
		return nil, fmt.Errorf("[MysqlTrafficStrategyDo] json.Unmarshal task_profit_ratio error: %v", err)
	}

	if len(items) == 0 {
		return nil, nil
	}

	result := entity.CreateKeyIndexProfitRatio()
	for _, item := range items {
		result.AddKeyIndex(item.TaskId, entity.CreatePriceRatioWithRateBounds(float64(item.LowerProfitRatio)/10000.0, float64(item.UpperProfitRatio)/10000.0))
	}

	result.SetName("task_id")

	return result, nil
}

func (do *MysqlTrafficStrategyDo) CovertBundleProfitRatio(data string) (*entity.IndexProfitRatio, error) {
	type TaskProfitRatio struct {
		Bundle           string `json:"bundle"`
		LowerProfitRatio int    `json:"low_profit_ratio"`
		UpperProfitRatio int    `json:"high_profit_ratio"`
	}

	items := make([]*TaskProfitRatio, 0)
	if err := json.Unmarshal([]byte(data), &items); err != nil {
		return nil, fmt.Errorf("[MysqlTrafficStrategyDo] json.Unmarshal task_profit_ratio error: %v", err)
	}

	if len(items) == 0 {
		return nil, nil
	}

	result := entity.CreateKeyIndexProfitRatio()
	for _, item := range items {
		result.AddKeyIndex(item.Bundle, entity.CreatePriceRatioWithRateBounds(float64(item.LowerProfitRatio)/10000.0, float64(item.UpperProfitRatio)/10000.0))
	}

	result.SetName("app_bundle_id")

	return result, nil
}

func (do *MysqlTrafficStrategyDo) CovertUserScoreProfitRatio(data string) (*entity.IndexProfitRatio, error) {
	type UserScoreProfitRatio struct {
		Score       int `json:"score"`
		ProfitRatio int `json:"profit_ratio"`
	}

	items := UserScoreProfitRatio{}
	if err := json.Unmarshal([]byte(data), &items); err != nil {
		return nil, fmt.Errorf("[MysqlTrafficStrategyDo] json.Unmarshal user_score_profit_ratio error: %v", err)
	}

	if items.Score == 0 && items.ProfitRatio == 0 {
		return nil, nil
	}

	result := entity.CreateLowerBoundRangeIndexProfitRatio()

	result.AddRangeIndex(
		float64(items.Score),
		float64(items.Score),
		entity.CreatePriceRatio(float64(items.ProfitRatio)/10000.0))

	result.SetName("user_score")

	return result, nil
}

func (do *MysqlTrafficStrategyDo) CovertDspPriceProfitRatio(data string) (*entity.IndexProfitRatio, error) {
	type DspPriceProfitRatio struct {
		DspBidPriceLow   int `json:"dsp_bid_price_low"`
		DspBidPriceHigh  int `json:"dsp_bid_price_high"`
		LowerProfitRatio int `json:"low_profit_ratio"`
		UpperProfitRatio int `json:"high_profit_ratio"`
	}

	items := []DspPriceProfitRatio{}
	if err := json.Unmarshal([]byte(data), &items); err != nil {
		return nil, fmt.Errorf("[MysqlTrafficStrategyDo] json.Unmarshal dsp_price_profit_ratio error: %v", err)
	}

	if len(items) == 0 {
		return nil, nil
	}

	result := entity.CreateRangeIndexProfitRatio()
	for _, item := range items {
		result.AddRangeIndex(
			float64(item.DspBidPriceLow),
			float64(item.DspBidPriceHigh),
			entity.CreatePriceRatioWithRateBounds(float64(item.LowerProfitRatio)/10000.0, float64(item.UpperProfitRatio)/10000.0))
	}

	result.SetName("dsp_price")

	return result, nil
}

func (do *MysqlTrafficStrategyDo) CovertUserScoreBroadcastRangeRatio(data string) (*entity.IndexBroadcastControl, error) {
	type Item struct {
		LowScore  int `json:"low_score"`
		HighScore int `json:"high_score"`
		Ratio     int `json:"ratio"`
	}

	items := []Item{}
	if err := json.Unmarshal([]byte(data), &items); err != nil {
		return nil, fmt.Errorf("[MysqlTrafficStrategyDo] json.Unmarshal user_score_broadcast_range_ratio error: %v", err)
	}

	if len(items) == 0 {
		return nil, nil
	}

	result := entity.CreateRangeIndexBroadcastControl()
	for _, item := range items {
		result.AddRangeIndex(
			float64(item.LowScore)/100,
			float64(item.HighScore)/100,
			entity.BroadcastControl{BroadcastRatio: float64(item.Ratio) / 10000})
	}

	result.SetName("user_score")
	return result, nil
}

type MysqlTrafficStrategyLoader struct {
	engine *xorm.Engine

	creativeLoader creative_loader.CreativeLoader

	itemList entity.TrafficStrategyList
	itemMap  map[utils.ID]*entity.TrafficStrategy

	intervalSec int
	term        chan struct{}
}

func NewMysqlTrafficStrategyLoader(engine *xorm.Engine, intervalSec int) *MysqlTrafficStrategyLoader {
	return &MysqlTrafficStrategyLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlTrafficStrategyLoader) WithCreativeLoader(creativeLoader creative_loader.CreativeLoader) *MysqlTrafficStrategyLoader {
	loader.creativeLoader = creativeLoader
	return loader
}

func (loader *MysqlTrafficStrategyLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlTrafficStrategyLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlTrafficStrategyLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlTrafficStrategyLoader) GetTrafficStrategyList() entity.TrafficStrategyList {
	return loader.itemList
}

func (loader *MysqlTrafficStrategyLoader) GetTrafficStrategyById(id utils.ID) *entity.TrafficStrategy {
	return loader.itemMap[id]
}

func (loader *MysqlTrafficStrategyLoader) GetAdGroupSpecificTrafficStrategy() *entity.TrafficStrategyList {
	result := make(entity.TrafficStrategyList, 0)
	for _, item := range loader.itemList {
		if item.IsAdGroupSpecified() {
			result = append(result, item)
		}
	}

	return &result
}

func (loader *MysqlTrafficStrategyLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlTrafficStrategyDo, 0)
	if err := session.Table("traffic_strategy").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	result := make(entity.TrafficStrategyList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			zap.L().Error("[MysqlTrafficStrategyLoader] item ToEntity error: , id", zap.Error(err), zap.Int64("id", int64(do.Id)))
			continue
		}

		if loader.creativeLoader != nil {
			for _, modifier := range item.TrafficResponseModifierList {
				if modifier.CreativeId != 0 {
					creative := loader.creativeLoader.GetCreativeById(utils.ID(modifier.CreativeId))
					if creative == nil {
						zap.L().Error("[MysqlTrafficStrategyLoader] creative not found, id", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(modifier.CreativeId)))))
						modifier.SetCreative(entity.BrokenCreative)
					} else {
						modifier.SetCreative(creative)
					}
				}
			}
		}

		if err := item.Init(); err != nil {
			zap.L().Error("[MysqlTrafficStrategyLoader] item.Init error", zap.Error(err))
			continue
		}

		result = append(result, item)
	}

	loader.itemList = result
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}
