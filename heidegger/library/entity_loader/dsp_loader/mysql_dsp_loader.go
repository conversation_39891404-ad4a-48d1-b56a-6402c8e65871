package dsp_loader

import (
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"xorm.io/xorm"
	"fmt"
)

type MysqlDspDo struct {
	DspId               int32  `xorm:"dsp_id"`
	Name                string `xorm:"name"`
	BidUrl              string `xorm:"bid_url"`
	QpsLimit            int64  `xorm:"qps_limit"`
	Timeout             int    `xorm:"timeout"`
	MaxRequestCandidate int    `xorm:"max_request_candidate"`
	Protocol            string `xorm:"protocol"`
	IKey                string `xorm:"ikey"`
	EKey                string `xorm:"ekey"`
	UseGzip             int    `xorm:"use_gzip"`
	UserScoreType       int    `xorm:"user_score_type"`
	PkgWhitelist        string `xorm:"pkg_whitelist"`
	PkgBlacklist        string `xorm:"pkg_blacklist"`
}

func (do *MysqlDspDo) ToEntity() (*entity.Dsp, error) {
	pkgWhitelist := make([]string, 0)
	pkgBlacklist := make([]string, 0)
	if len(do.PkgWhitelist) > 4 {
		if err := sonic.Unmarshal([]byte(do.PkgWhitelist), &pkgWhitelist); err != nil {
			zap.L().Error("[MysqlDspLoader] unmarshal pkgWhitelist error", zap.Error(err), zap.String("dspId", fmt.Sprintf("%v", do.DspId)))
			return nil, err
		}
	}
	if len(do.PkgBlacklist) > 4 {
		if err := sonic.Unmarshal([]byte(do.PkgBlacklist), &pkgBlacklist); err != nil {
			zap.L().Error("[MysqlDspLoader] unmarshal pkgBlacklist error", zap.Error(err), zap.String("dspId", fmt.Sprintf("%v", do.DspId)))
			return nil, err
		}
	}
	result := &entity.Dsp{
		DspId:               utils.ID(do.DspId),
		Name:                do.Name,
		BidUrl:              do.BidUrl,
		QpsLimit:            do.QpsLimit,
		Timeout:             do.Timeout,
		MaxRequestCandidate: do.MaxRequestCandidate,
		Protocol:            do.Protocol,
		Ikey:                do.IKey,
		Ekey:                do.EKey,
		UseGzip:             do.UseGzip == 1,
		UserScoreType:       entity.UserScoreType(do.UserScoreType),
		PkgWhitelist:        make(map[string]struct{}, len(pkgWhitelist)),
		PkgBlacklist:        make(map[string]struct{}, len(pkgBlacklist)),
	}

	if result.MaxRequestCandidate == 0 {
		result.MaxRequestCandidate = 1
	}

	for _, pkg := range pkgWhitelist {
		result.PkgWhitelist[pkg] = struct{}{}
	}
	for _, pkg := range pkgBlacklist {
		result.PkgBlacklist[pkg] = struct{}{}
	}

	return result, nil
}

type MysqlDspLoader struct {
	engine *xorm.Engine

	dspList entity.DspList
	dspMap  map[utils.ID]*entity.Dsp

	intervalSec int
	term        chan struct{}
}

func NewMysqlDspLoader(engine *xorm.Engine, intervalSec int) *MysqlDspLoader {
	return &MysqlDspLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlDspLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlDspLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlDspLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlDspLoader) GetDspList() entity.DspList {
	return loader.dspList
}

func (loader *MysqlDspLoader) GetDspById(dspId utils.ID) *entity.Dsp {
	return loader.dspMap[dspId]
}

func (loader *MysqlDspLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlDspDo, 0)
	if err := session.Table("dsp_info").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	dspList := make(entity.DspList, 0, len(doList))
	for _, do := range doList {
		dsp, err := do.ToEntity()
		if err != nil {
			return err
		}

		dspList = append(dspList, dsp)
	}

	loader.dspList = dspList
	loader.dspMap = loader.dspList.ToIdMap()
	return nil
}
