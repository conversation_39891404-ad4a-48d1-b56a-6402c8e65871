package frequency_control_loader

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

type MysqlFrequencyControlDo struct {
	Id           utils.ID `xorm:"id"`
	Name         string   `xorm:"name"`
	PeriodType   int      `xorm:"period_type"`
	PeriodLength int      `xorm:"period_length"`
	Count        int      `xorm:"count"`

	AdvanceFrequencyControl string `xorm:"advance_frequency_control"`
}

func (do *MysqlFrequencyControlDo) ToEntity() (*entity.FrequencyControl, error) {
	result := &entity.FrequencyControl{
		Id:   do.Id,
		Name: do.Name,
	}

	if len(do.AdvanceFrequencyControl) != 0 {
		if err := json.Unmarshal([]byte(do.AdvanceFrequencyControl), &result.FrequencyControlItem); err != nil {
			return nil, fmt.Errorf("json.Unmarshal MysqlFrequencyControlDo error: %v", err)
		}
	}

	if entity.FrequencyPeriodType(do.PeriodType) != entity.FrequencyPeriodTypeUnknown {
		result.FrequencyControlItem = append(result.FrequencyControlItem, &entity.FrequencyControlItem{
			ControlType:  entity.FrequencyControlTypeImpression,
			KeyType:      entity.FrequencyKeyTypeDeviceId,
			PeriodType:   entity.FrequencyPeriodType(do.PeriodType),
			PeriodLength: uint32(do.PeriodLength),
			LimitCount:   uint32(do.Count),
		})
	}

	return result, nil
}

type MysqlFrequencyControlLoader struct {
	engine *xorm.Engine

	frequencyControlList entity.FrequencyControlList
	frequencyControlMap  map[utils.ID]*entity.FrequencyControl

	intervalSec int
	term        chan struct{}
}

func NewMysqlFrequencyControlLoader(engine *xorm.Engine, intervalSec int) *MysqlFrequencyControlLoader {
	return &MysqlFrequencyControlLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlFrequencyControlLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlFrequencyControlLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlFrequencyControlLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlFrequencyControlLoader) GetFrequencyControlList() entity.FrequencyControlList {
	return loader.frequencyControlList
}

func (loader *MysqlFrequencyControlLoader) GetFrequencyControlById(id utils.ID) *entity.FrequencyControl {
	return loader.frequencyControlMap[id]
}

func (loader *MysqlFrequencyControlLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlFrequencyControlDo, 0)
	if err := session.Table("frequency_info").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	result := make(entity.FrequencyControlList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		if err := item.Init(); err != nil {
			zap.L().Error("[MysqlFrequencyControlLoader] item.Init error", zap.Error(err))
			continue
		}

		result = append(result, item)
	}

	loader.frequencyControlList = result
	loader.frequencyControlMap = loader.frequencyControlList.ToIdMap()
	return nil
}
