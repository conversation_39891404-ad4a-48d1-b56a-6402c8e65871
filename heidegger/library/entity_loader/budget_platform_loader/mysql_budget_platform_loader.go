package budget_platform_loader

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

type MysqlBudgetPlatformDo struct {
	Id   int32  `xorm:"id"`
	Name string `xorm:"name"`
	Key  string `xorm:"key"`
}

func (do *MysqlBudgetPlatformDo) ToEntity() (*entity.BudgetPlatform, error) {
	result := &entity.BudgetPlatform{
		Id:   utils.ID(do.Id),
		Name: do.Name,
		Key:  do.Key,
	}

	return result, nil
}

type MysqlBudgetPlatformLoader struct {
	engine *xorm.Engine

	itemList entity.BudgetPlatformList
	itemMap  map[utils.ID]*entity.BudgetPlatform

	intervalSec int
	term        chan struct{}
}

func NewMysqlBudgetPlatformLoader(engine *xorm.Engine, intervalSec int) *MysqlBudgetPlatformLoader {
	return &MysqlBudgetPlatformLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlBudgetPlatformLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlBudgetPlatformLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlBudgetPlatformLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlBudgetPlatformLoader) GetBudgetPlatformList() entity.BudgetPlatformList {
	return loader.itemList
}

func (loader *MysqlBudgetPlatformLoader) GetBudgetPlatformById(dspId utils.ID) *entity.BudgetPlatform {
	return loader.itemMap[dspId]
}

func (loader *MysqlBudgetPlatformLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlBudgetPlatformDo, 0)
	if err := session.Table("budget_platform").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	itemList := make(entity.BudgetPlatformList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		itemList = append(itemList, item)
	}

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}
