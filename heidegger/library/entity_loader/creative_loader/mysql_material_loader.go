package creative_loader

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

type MysqlMaterialDo struct {
	Id           int32   `xorm:"id"`
	Name         string  `xorm:"name"`
	MaterialType int32   `xorm:"material_type"`
	MimeType     int32   `xorm:"mime_type"`
	Duration     float64 `xorm:"duration"`
	Size         int32   `xorm:"size"`
	Width        int32   `xorm:"width"`
	Height       int32   `xorm:"height"`
	Tags         string  `xorm:"tags"`
	Url          string  `xorm:"url"`
	Status       int32   `xorm:"status"`
}

func (do *MysqlMaterialDo) ToEntity() (*entity.Material, error) {
	result := &entity.Material{
		Id:           utils.ID(do.Id),
		Name:         do.Name,
		MaterialType: entity.MaterialType(do.MaterialType),
		MimeType:     entity.MimeType(do.MimeType),
		Url:          do.Url,
		Height:       do.Height,
		Width:        do.Width,
		Duration:     do.Duration,
		FileSize:     do.Size,
	}

	if len(do.Tags) != 0 {
		if err := json.Unmarshal([]byte(do.Tags), &result.MaterialTags); err != nil {
			return nil, fmt.Errorf("[MysqlMaterialDo] json.Unmarshal fail, err:%s", err)
		}
	}

	if do.MaterialType == int32(entity.MaterialTypeTitle) ||
		do.MaterialType == int32(entity.MaterialTypeDesc) {
		result.Data = do.Name
	}

	return result, nil
}

type MysqlMaterialLoader struct {
	engine *xorm.Engine

	itemList entity.MaterialList
	itemMap  map[utils.ID]*entity.Material

	intervalSec int
	term        chan struct{}
}

func NewMysqlMaterialLoader(engine *xorm.Engine, intervalSec int) *MysqlMaterialLoader {
	return &MysqlMaterialLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlMaterialLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlMaterialLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlMaterialLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlMaterialLoader) GetMaterialList() entity.MaterialList {
	return loader.itemList
}

func (loader *MysqlMaterialLoader) GetMaterialById(id utils.ID) *entity.Material {
	return loader.itemMap[id]
}

func (loader *MysqlMaterialLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlMaterialDo, 0)
	if err := session.Table("material").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	itemList := make(entity.MaterialList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		if err := item.Init(); err != nil {
			zap.L().Warn("[MysqlMaterialLoader] item.Init error", zap.Error(err))
			continue
		}

		itemList = append(itemList, item)
	}

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}
