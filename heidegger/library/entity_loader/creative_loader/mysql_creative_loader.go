package creative_loader

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

type MysqlCreativeDo struct {
	Id           int32  `xorm:"id"`
	Name         string `xorm:"name"`
	CreativeKey  string `xorm:"creative_key"`
	MaterialList string `xorm:"material_list"`
}

func (do *MysqlCreativeDo) ToEntity() (*entity.Creative, error) {
	result := &entity.Creative{
		Id:          utils.ID(do.Id),
		Name:        do.Name,
		CreativeKey: do.CreativeKey,
	}

	if len(do.MaterialList) != 0 {
		namedMap := make(map[string][]utils.ID)
		if err := json.Unmarshal([]byte(do.MaterialList), &namedMap); err != nil {
			if err := json.Unmarshal([]byte(do.MaterialList), &result.MaterialIdList); err != nil {
				return nil, fmt.Errorf("[MysqlCreativeDo] json.Unmarshal fail, err:%s", err)
			}
		} else {
			for _, idList := range namedMap {
				result.MaterialIdList = append(result.MaterialIdList, idList...)
			}
		}

	}

	return result, nil
}

type MysqlCreativeLoader struct {
	engine *xorm.Engine

	itemList entity.CreativeList
	itemMap  map[utils.ID]*entity.Creative

	materialLoader MaterialLoader

	intervalSec int
	term        chan struct{}
}

func NewMysqlCreativeLoader(engine *xorm.Engine, intervalSec int) *MysqlCreativeLoader {
	return &MysqlCreativeLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlCreativeLoader) WithMaterialLoader(materialLoader MaterialLoader) *MysqlCreativeLoader {
	loader.materialLoader = materialLoader
	return loader
}

func (loader *MysqlCreativeLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlCreativeLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlCreativeLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlCreativeLoader) GetCreativeList() entity.CreativeList {
	return loader.itemList
}

func (loader *MysqlCreativeLoader) GetCreativeById(id utils.ID) *entity.Creative {
	return loader.itemMap[id]
}

func (loader *MysqlCreativeLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlCreativeDo, 0)
	if err := session.Table("creative").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	itemList := make(entity.CreativeList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		if loader.materialLoader != nil {
			if err := loader.loadMaterial(item, item.MaterialIdList); err != nil {
				zap.L().Warn("[MysqlCreativeLoader] loadMaterial error", zap.Error(err))
				continue
			}
		}

		itemList = append(itemList, item)
	}

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}

func (loader *MysqlCreativeLoader) loadMaterial(creative *entity.Creative, materialIdList []utils.ID) error {
	for _, materialId := range materialIdList {
		material := loader.materialLoader.GetMaterialById(materialId)
		if material == nil {
			return fmt.Errorf("[MysqlCreativeLoader] material not found, materialId:%d", materialId)
		}

		creative.AddMaterial(material)
	}

	return nil
}
