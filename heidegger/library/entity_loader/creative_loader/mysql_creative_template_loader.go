package creative_loader

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity/creative_entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

type MysqlCreativeTemplateDo struct {
	Id           int32  `xorm:"id"`
	Name         string `xorm:"name"`
	IsGeneric    int    `xorm:"is_generic"`
	TemplateType int    `xorm:"template_type"`
	Assets       string `xorm:"assets"`
	Index        string `xorm:"index"`
	Status       int32  `xorm:"status"`
}

func (do *MysqlCreativeTemplateDo) ToEntity() (*creative_entity.CreativeTemplate, error) {
	result := &creative_entity.CreativeTemplate{
		Id:           utils.ID(do.Id),
		Name:         do.Name,
		IsGeneric:    do.IsGeneric == 1,
		TemplateType: do.TemplateType,
	}

	if len(do.Assets) != 0 {
		if err := json.Unmarshal([]byte(do.Assets), &result.Assets); err != nil {
			return nil, fmt.Errorf("[MysqlCreativeTemplateDo] json.Unmarshal fail, err:%s", err)
		}
	}

	if len(do.Index) != 0 {
		if err := json.Unmarshal([]byte(do.Index), &result.CreativeTemplateIndex); err != nil {
			return nil, fmt.Errorf("[MysqlCreativeTemplateDo] json.Unmarshal fail, err:%s", err)
		}
	} else {
		result.CreativeTemplateIndex = &creative_entity.CreativeTemplateIndex{}
	}

	return result, nil
}

type MysqlCreativeTemplateLoader struct {
	engine *xorm.Engine

	itemList creative_entity.CreativeTemplateList
	itemMap  map[utils.ID]*creative_entity.CreativeTemplate

	intervalSec int
	term        chan struct{}
}

func NewMysqlCreativeTemplateLoader(engine *xorm.Engine, intervalSec int) *MysqlCreativeTemplateLoader {
	return &MysqlCreativeTemplateLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlCreativeTemplateLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlCreativeTemplateLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlCreativeTemplateLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlCreativeTemplateLoader) GetCreativeTemplateList() creative_entity.CreativeTemplateList {
	return loader.itemList
}

func (loader *MysqlCreativeTemplateLoader) GetCreativeTemplateById(id utils.ID) *creative_entity.CreativeTemplate {
	return loader.itemMap[id]
}

func (loader *MysqlCreativeTemplateLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlCreativeTemplateDo, 0)
	if err := session.Table("creative_template").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	itemList := make(creative_entity.CreativeTemplateList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		item.GetCreativeTemplateKeyWithCache()

		itemList = append(itemList, item)
	}

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}
