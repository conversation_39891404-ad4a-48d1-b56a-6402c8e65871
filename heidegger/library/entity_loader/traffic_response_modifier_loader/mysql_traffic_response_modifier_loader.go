package traffic_response_modifier_loader

import (
	"encoding/json"
	"errors"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/creative_loader"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

var (
	ErrorCreativeIdEmpty = errors.New("creative_id is empty")
)

type MysqlTrafficResponseModifierDo struct {
	Id                             utils.ID `xorm:"id"`
	TargetOsType                   string   `xorm:"target_os_type"`
	TargetSourceSlotId             string   `xorm:"target_source_slot_id"`
	TargetDspSlotId                string   `xorm:"target_dsp_slot_id"`
	TargetTrafficRequestModifierId string   `xorm:"target_traffic_request_modifier_id"`
	TargetCreativeKey              string   `xorm:"target_creative_key"`
	Weight                         uint32   `xorm:"weight"`
	Name                           string   `xorm:"name"`

	CreativeId int32 `xorm:"creative_id"`
}

func (do *MysqlTrafficResponseModifierDo) ToEntity() (*entity.TrafficResponseModifier, error) {
	if do.CreativeId == 0 {
		return nil, ErrorCreativeIdEmpty
	}

	result := &entity.TrafficResponseModifier{
		Id:         do.Id,
		Name:       do.Name,
		Weight:     do.Weight,
		CreativeId: utils.ID(do.CreativeId),
	}

	if len(do.TargetOsType) != 0 {
		if err := json.Unmarshal([]byte(do.TargetOsType), &result.Target.OsType); err != nil {
			return nil, err
		}
	}

	if len(do.TargetSourceSlotId) != 0 {
		if err := json.Unmarshal([]byte(do.TargetSourceSlotId), &result.Target.SourceSlotId); err != nil {
			return nil, err
		}
	}

	if len(do.TargetDspSlotId) != 0 {
		if err := json.Unmarshal([]byte(do.TargetDspSlotId), &result.Target.DspSlotId); err != nil {
			return nil, err
		}
	}

	if len(do.TargetTrafficRequestModifierId) != 0 {
		if err := json.Unmarshal([]byte(do.TargetTrafficRequestModifierId), &result.Target.TrafficRequestModifierId); err != nil {
			return nil, err
		}
	}

	return result, nil
}

type MysqlTrafficResponseModifierLoader struct {
	engine         *xorm.Engine
	creativeLoader creative_loader.CreativeLoader

	itemList entity.TrafficResponseModifierList
	itemMap  map[utils.ID]*entity.TrafficResponseModifier

	intervalSec int
	term        chan struct{}
}

func NewMysqlTrafficResponseModifierLoader(engine *xorm.Engine, intervalSec int) *MysqlTrafficResponseModifierLoader {
	return &MysqlTrafficResponseModifierLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlTrafficResponseModifierLoader) WithCreativeLoader(creativeLoader creative_loader.CreativeLoader) *MysqlTrafficResponseModifierLoader {
	loader.creativeLoader = creativeLoader
	return loader
}

func (loader *MysqlTrafficResponseModifierLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlTrafficResponseModifierLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlTrafficResponseModifierLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlTrafficResponseModifierLoader) GetTrafficResponseModifierList() entity.TrafficResponseModifierList {
	return loader.itemList
}

func (loader *MysqlTrafficResponseModifierLoader) GetTrafficResponseModifierById(id utils.ID) *entity.TrafficResponseModifier {
	return loader.itemMap[id]
}

func (loader *MysqlTrafficResponseModifierLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlTrafficResponseModifierDo, 0)
	if err := session.Table("traffic_response_modifier").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	result := make(entity.TrafficResponseModifierList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		if loader.creativeLoader != nil {
			creative := loader.creativeLoader.GetCreativeById(item.CreativeId)
			if creative == nil {
				zap.L().Error("[MysqlTrafficResponseModifierLoader] creative not found, id", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(item.CreativeId)))))
				continue
			}

			item.SetCreative(creative)
		}

		result = append(result, item)
	}

	loader.itemList = result
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}
