package device_allocation_setting_loader

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/device_allocator/device_allocator"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

type MysqlDeviceAllocationSettingDo struct {
	Id                  int32  `xorm:"id"`
	Name                string `xorm:"name"`
	PackageName         string `xorm:"package_name"`
	FetchCount          int32  `xorm:"fetch_count"`
	ReplaceRatio        int32  `xorm:"replace_ratio"`
	ReplaceReachType    int32  `xorm:"replace_reach_type"`
	MatchType           int32  `xorm:"match_type"`
	ReplaceTimeInterval int32  `xorm:"replace_time_interval"`
	MandatoryReplace    bool   `xorm:"mandatory_replace"`
	UseOsReplace        bool   `xorm:"use_os_replace"`
	IgnoreGeo           bool   `xorm:"ignore_geo"`
	KeyType             int32  `xorm:"key_type"`
}

func (do *MysqlDeviceAllocationSettingDo) ToEntity() (*entity.DeviceAllocationSetting, error) {
	result := &entity.DeviceAllocationSetting{
		Id:                  utils.ID(do.Id),
		Name:                do.Name,
		PackageName:         do.PackageName,
		FetchCount:          int(do.FetchCount),
		ReplaceRatio:        int(do.ReplaceRatio),
		ReplaceReachType:    int(do.ReplaceReachType),
		MatchType:           do.MatchType,
		ReplaceTimeInterval: do.ReplaceTimeInterval,
		MandatoryReplace:    do.MandatoryReplace,
		UseOsReplace:        do.UseOsReplace,
		IgnoreGeo:           do.IgnoreGeo,
		KeyType:             device_allocator.KeyType(do.KeyType),
	}

	return result, nil
}

type MysqlDeviceAllocationSettingLoader struct {
	engine *xorm.Engine

	itemList entity.DeviceAllocationSettingList
	itemMap  map[utils.ID]*entity.DeviceAllocationSetting

	intervalSec int
	term        chan struct{}
}

func NewMysqlDeviceAllocationSettingLoader(engine *xorm.Engine, intervalSec int) *MysqlDeviceAllocationSettingLoader {
	return &MysqlDeviceAllocationSettingLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlDeviceAllocationSettingLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlDeviceAllocationSettingLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlDeviceAllocationSettingLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlDeviceAllocationSettingLoader) GetDeviceAllocationSettingList() entity.DeviceAllocationSettingList {
	return loader.itemList
}

func (loader *MysqlDeviceAllocationSettingLoader) GetDeviceAllocationSettingById(dspId utils.ID) *entity.DeviceAllocationSetting {
	return loader.itemMap[dspId]
}

func (loader *MysqlDeviceAllocationSettingLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlDeviceAllocationSettingDo, 0)
	if err := session.Table("device_allocation_setting").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	itemList := make(entity.DeviceAllocationSettingList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		itemList = append(itemList, item)
	}

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}
