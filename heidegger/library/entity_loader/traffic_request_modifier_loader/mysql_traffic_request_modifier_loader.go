package traffic_request_modifier_loader

import (
	"encoding/json"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"time"
	"xorm.io/xorm"
)

type MysqlTrafficRequestModifierDo struct {
	Id                 utils.ID `xorm:"id"`
	TargetOsType       string   `xorm:"target_os_type"`
	TargetSourceSlotId string   `xorm:"target_source_slot_id"`
	Weight             uint32   `xorm:"weight"`
	Name               string   `xorm:"name"`
	DspSlotId          int32    `xorm:"dsp_slot_id"`
	AppPackageName     string   `xorm:"app_package_name"`
	AppBundle          string   `xorm:"app_bundle"`
	AppVersion         string   `xorm:"app_version"`
}

func (do *MysqlTrafficRequestModifierDo) ToEntity() (*entity.TrafficRequestModifier, error) {
	result := &entity.TrafficRequestModifier{
		Id:        do.Id,
		Name:      do.Name,
		Weight:    do.Weight,
		DspSlotId: do.DspSlotId,
	}

	if len(do.TargetOsType) != 0 {
		if err := json.Unmarshal([]byte(do.TargetOsType), &result.Target.OsType); err != nil {
			return nil, err
		}
	}

	//if len(do.TargetSourceSlotId) != 0 {
	//	if err := json.Unmarshal([]byte(do.TargetSourceSlotId), &result.Target.SourceSlotId); err != nil {
	//		return nil, err
	//	}
	//}

	if do.AppPackageName != "preserve" {
		result.AppPackageName = &do.AppPackageName
	}

	if do.AppBundle != "preserve" {
		result.AppBundle = &do.AppBundle
	}

	if do.AppVersion != "preserve" {
		result.AppVersion = &do.AppVersion
	}

	return result, nil
}

type MysqlTrafficRequestModifierLoader struct {
	engine *xorm.Engine

	itemList entity.TrafficRequestModifierList
	itemMap  map[utils.ID]*entity.TrafficRequestModifier

	intervalSec int
	term        chan struct{}
}

func NewMysqlTrafficRequestModifierLoader(engine *xorm.Engine, intervalSec int) *MysqlTrafficRequestModifierLoader {
	return &MysqlTrafficRequestModifierLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlTrafficRequestModifierLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlTrafficRequestModifierLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlTrafficRequestModifierLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlTrafficRequestModifierLoader) GetTrafficRequestModifierList() entity.TrafficRequestModifierList {
	return loader.itemList
}

func (loader *MysqlTrafficRequestModifierLoader) GetTrafficRequestModifierById(id utils.ID) *entity.TrafficRequestModifier {
	return loader.itemMap[id]
}

func (loader *MysqlTrafficRequestModifierLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlTrafficRequestModifierDo, 0)
	if err := session.Table("traffic_request_modifier").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	result := make(entity.TrafficRequestModifierList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			return err
		}

		result = append(result, item)
	}

	loader.itemList = result
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}
