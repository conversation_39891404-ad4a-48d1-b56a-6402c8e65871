package dsp_slot_info_loader

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/bytedance/sonic"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/utils"
	"xorm.io/xorm"
)

type MysqlDspSlotInfoDo struct {
	Id             int32  `xorm:"id"`
	Name           string `xorm:"name"`
	DspIds         string `xorm:"dsp_ids"`
	AndroidSlotId  string `xorm:"android_slot_id"`
	IosSlotId      string `xorm:"ios_slot_id"`
	TargetOsType   string `xorm:"target_os_type"`
	ExtraData      string `xorm:"extra_data"`
	Qps            int32  `xorm:"qps_limit"`
	HourlyQpsLimit string `xorm:"hourly_qps_limit"`
	PkgWhitelist   string `xorm:"pkg_whitelist"`
	PkgBlacklist   string `xorm:"pkg_blacklist"`
}

func (do *MysqlDspSlotInfoDo) ToEntity() (*entity.DspSlotInfo, error) {
	pkgWhitelist := make([]string, 0)
	pkgBlacklist := make([]string, 0)
	if len(do.PkgWhitelist) > 4 {
		if err := sonic.Unmarshal([]byte(do.PkgWhitelist), &pkgWhitelist); err != nil {
			zap.L().Error("[MysqlDspSlotInfoLoader] unmarshal pkgWhitelist error", zap.Error(err), zap.String("slotId", fmt.Sprintf("%v", do.Id)))
			return nil, err
		}
	}
	if len(do.PkgBlacklist) > 4 {
		if err := sonic.Unmarshal([]byte(do.PkgBlacklist), &pkgBlacklist); err != nil {
			zap.L().Error("[MysqlDspSlotInfoLoader] unmarshal pkgBlacklist error", zap.Error(err), zap.String("slotId", fmt.Sprintf("%v", do.Id)))
			return nil, err
		}
	}

	result := &entity.DspSlotInfo{
		Id:            utils.ID(do.Id),
		Name:          do.Name,
		AndroidSlotId: do.AndroidSlotId,
		IosSlotId:     do.IosSlotId,
		Qps:           do.Qps,
		PkgWhitelist:  make(map[string]struct{}, len(pkgWhitelist)),
		PkgBlacklist:  make(map[string]struct{}, len(pkgBlacklist)),
	}

	if len(do.DspIds) != 0 {
		if err := json.Unmarshal([]byte(do.DspIds), &result.DspIds); err != nil {
			return nil, fmt.Errorf("[MysqlDspSlotInfoDo] id:%d, field:DspIds, err:%s", do.Id, err)
		}
	}

	if len(do.TargetOsType) != 0 {
		if err := json.Unmarshal([]byte(do.TargetOsType), &result.Target.OsType); err != nil {
			return nil, fmt.Errorf("[MysqlDspSlotInfoDo] id:%d, field:OsType, err:%s", do.Id, err)
		}
	}

	if len(do.ExtraData) != 0 {
		if err := json.Unmarshal([]byte(do.ExtraData), &result.ExtraData); err != nil {
			return nil, fmt.Errorf("[MysqlDspSlotInfoDo] id:%d, field:ExtraData, err:%s", do.Id, err)
		}
	}

	if len(do.HourlyQpsLimit) != 0 {
		if err := json.Unmarshal([]byte(do.HourlyQpsLimit), &result.HourlyQps); err != nil {
			zap.L().Warn("[MysqlDspSlotInfoDo] id:, field:HourlyQpsLimit, err", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(do.Id)))), zap.Error(err))
		}
	}

	for _, pkg := range pkgWhitelist {
		result.PkgWhitelist[pkg] = struct{}{}
	}
	for _, pkg := range pkgBlacklist {
		result.PkgBlacklist[pkg] = struct{}{}
	}

	return result, nil
}

type MysqlDspSlotInfoLoader struct {
	engine *xorm.Engine

	itemList entity.DspSlotInfoList
	itemMap  map[utils.ID]*entity.DspSlotInfo

	intervalSec int
	term        chan struct{}
}

func NewMysqlDspSlotInfoLoader(engine *xorm.Engine, intervalSec int) *MysqlDspSlotInfoLoader {
	return &MysqlDspSlotInfoLoader{
		engine:      engine,
		intervalSec: intervalSec,
		term:        make(chan struct{}),
	}
}

func (loader *MysqlDspSlotInfoLoader) Start() error {
	if err := loader.Load(); err != nil {
		return err
	}

	go func() {
		for {
			select {
			case <-loader.term:
				return
			case <-time.After(time.Second * time.Duration(loader.intervalSec)):
				if err := loader.Load(); err != nil {
					zap.L().Error("[MysqlDspSlotInfoLoader] loader.Load error", zap.Error(err))
				}
			}
		}
	}()

	return nil
}

func (loader *MysqlDspSlotInfoLoader) Stop() {
	close(loader.term)
}

func (loader *MysqlDspSlotInfoLoader) GetDspSlotInfoList() entity.DspSlotInfoList {
	return loader.itemList
}

func (loader *MysqlDspSlotInfoLoader) GetDspSlotInfoById(dspId utils.ID) *entity.DspSlotInfo {
	return loader.itemMap[dspId]
}

func (loader *MysqlDspSlotInfoLoader) Load() error {
	session := loader.engine.NewSession()
	defer session.Close()

	doList := make([]*MysqlDspSlotInfoDo, 0)
	if err := session.Table("dsp_slot_info").Where("status = 0").Find(&doList); err != nil {
		return err
	}

	itemList := make(entity.DspSlotInfoList, 0, len(doList))
	for _, do := range doList {
		item, err := do.ToEntity()
		if err != nil {
			zap.L().Error("[MysqlDspSlotInfoLoader] do.ToEntity error", zap.Error(err))
			continue
		}

		itemList = append(itemList, item)
	}

	loader.itemList = itemList
	loader.itemMap = loader.itemList.ToIdMap()
	return nil
}
