package geo_parser

import (
	"bufio"
	"fmt"
	"github.com/labstack/gommon/log"
	"go.uber.org/zap"
	"os"
	"sort"
	"strconv"
	"strings"
	"sync/atomic"
)

type GeoParser struct {
	GeoRegions atomic.Value //*Regions
}

func NewGeoParser() *GeoParser {
	return &GeoParser{}
}

func (g *GeoParser) StartLoadGeoData(fileName string) error {
	geoLines, err := g.LoadGeoDataFromFile(fileName)
	if err != nil {
		return err
	}

	g.LoadGeoData(geoLines)

	return nil
}

func (g *GeoParser) LoadGeoDataFromFile(fileName string) ([]*GeoInfoLine, error) {
	lines := make([]*GeoInfoLine, 0)
	file, err := os.Open(fileName)
	if err != nil {
		zap.L().Error("无法打开文件", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.<PERSON>r())))))
		return lines, err
	}
	defer file.Close()

	reader := bufio.NewReader(file)
	for {
		line, err := reader.ReadString('\n')
		// 如果遇到文件末尾，退出循环
		if err != nil {
			if err.Error() == "EOF" {
				break
			}
			zap.L().Error("读取文件错误", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
			continue
		}

		// 去除行末的换行符
		line = strings.TrimSuffix(line, "\n")

		columns := strings.Split(line, ",")
		if len(columns) < 4 {
			fmt.Println("错误：行列数不符合预期")
			continue
		}

		data := &GeoInfoLine{
			Start:        columns[0],
			End:          columns[1],
			Gid:          columns[2],
			SubCarrierId: columns[3],
		}

		if len(columns) > 4 {
			data.CarrierId = columns[4]
		}

		lines = append(lines, data)
	}

	return lines, nil
}

func (g *GeoParser) LoadGeoData(lines []*GeoInfoLine) {
	var err error
	Regs := Regions{}

	for _, v := range lines {
		var r = Region{}

		if r.Start, err = InetAtoN(v.Start); err != nil {
			continue
		}
		if r.End, err = InetAtoN(v.End); err != nil {
			continue
		}
		if r.Geo, err = strconv.ParseUint(v.Gid, 10, 64); err != nil {
			continue
		}

		log.Debugf("LoadData, start: %s, end: %s, gid: %s", v.Start, v.End, v.Gid)
		log.Debugf("LoadData, region: ", r)

		Regs = append(Regs, r)
	}

	sort.Sort(Regs)

	log.Debugf("LoadData, regions len: %d", len(Regs))
	g.GeoRegions.Store(Regs)

	return
}

func (g *GeoParser) QueryGeoInfo(ipStr string) (GeoInfo, error) {
	info := GeoInfo{GeoId: 0, CountryId: 0, ProvinceId: 0, CityId: 0, SubCarrierId: 0, CarrierId: 0}
	ipInt, err := InetAtoN(ipStr)
	if err != nil {
		return info, err
	}

	var regs Regions
	regs = g.GeoRegions.Load().(Regions)

	log.Debugf("ipInt: ", ipInt)
	log.Debugf("regions len: %d", len(regs))

	r := Region{ipInt, ipInt, 0, 0, 0}
	rr, err := Search(regs, r)
	if err != nil {
		return info, err
	}
	info.GeoId = rr.Geo
	info.CountryId = rr.Geo / (1000000000)
	info.ProvinceId = rr.Geo % (1000000000) / (1000000)
	info.CityId = rr.Geo
	info.SubCarrierId = rr.SubCarrierId
	info.CarrierId = rr.CarrierId
	info.Ip = ipStr
	return info, nil
}
