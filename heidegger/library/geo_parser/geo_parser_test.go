package geo_parser

import (
	"go.uber.org/zap"
	"testing"
	"fmt"
)

func TestGeoParser(t *testing.T) {

	geoParser := &GeoParser{}

	err := geoParser.StartLoadGeoData("../../ad_server/app/data/geo_info.txt")

	if err != nil {
		return
	}

	ipStr := "**************"

	geoInfo, err := geoParser.QueryGeoInfo(ipStr)
	if err != nil {
		return
	}

	t.Logf("geoInfo:%v", geoInfo)

}

func TestGeoParserV6(t *testing.T) {

	geoParser := &GeoParserV6{}

	err := geoParser.StartLoadGeoData("../../ad_server/app/ad_server/data/geo_info_v6.txt")

	if err != nil {
		zap.L().Error("load error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return
	}

	ipStr := "2001:218:0:2000::95"

	geoInfo, err := geoParser.QueryGeoInfo(ipStr)
	if err != nil {
		zap.L().Error("query error", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return
	}

	zap.L().Info("geoInfo", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", geoInfo)))))

}
