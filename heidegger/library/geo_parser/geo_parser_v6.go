package geo_parser

import (
	"bufio"
	"fmt"
	"go.uber.org/zap"
	"os"
	"sort"
	"strconv"
	"strings"
	"sync/atomic"
)

type GeoParserV6 struct {
	GeoRegions atomic.Value //*Regions
}

func NewGeoParserV6() *GeoParserV6 {
	return &GeoParserV6{}
}

func (g *GeoParserV6) StartLoadGeoData(fileName string) error {
	geoLines, err := g.LoadGeoDataFromFile(fileName)
	if err != nil {
		return err
	}

	g.LoadGeoData(geoLines)

	return nil
}

func (g *GeoParserV6) LoadGeoDataFromFile(fileName string) ([]*GeoInfoLine, error) {
	lines := make([]*GeoInfoLine, 0)
	file, err := os.Open(fileName)
	if err != nil {
		zap.L().Error("无法打开文件", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
		return lines, err
	}
	defer file.Close()

	reader := bufio.NewReader(file)
	for {
		line, err := reader.ReadString('\n')
		// 如果遇到文件末尾，退出循环
		if err != nil {
			if err.Error() == "EOF" {
				break
			}
			zap.L().Error("读取文件错误", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))))
			continue
		}

		// 去除行末的换行符
		line = strings.TrimSuffix(line, "\n")

		columns := strings.Split(line, ",")
		if len(columns) < 4 {
			fmt.Println("错误：行列数不符合预期")
			continue
		}

		data := &GeoInfoLine{
			Start:        columns[0],
			End:          columns[1],
			Gid:          columns[2],
			SubCarrierId: columns[3],
		}

		if len(columns) > 4 {
			data.CarrierId = columns[4]
		}

		lines = append(lines, data)
	}

	return lines, nil
}

func (g *GeoParserV6) LoadGeoData(lines []*GeoInfoLine) {
	var err error
	Regs := RegionV6s{}

	for _, v := range lines {
		var r = RegionV6{}

		if len(v.Start) == 0 || len(v.End) == 0 {
			continue
		}

		if r.Start.High, r.Start.Low, err = StrIPv6ToUInt64s(v.Start); err != nil {
			zap.L().Error("load v6 data error:,data:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))), v.Start)
			continue
		}

		if r.End.High, r.End.Low, err = StrIPv6ToUInt64s(v.End); err != nil {
			zap.L().Error("load v6 data error:,data:%s", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", err.Error())))), v.End)
			continue
		}
		if r.Geo, err = strconv.ParseUint(v.Gid, 10, 64); err != nil {
			continue
		}

		zap.L().Debug("LoadData, start: , end: , gid", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", v.Start)))), zap.String("param2", fmt.Sprintf("%v", v.End)), zap.String("param3", fmt.Sprintf("%v", v.Gid)))
		zap.L().Debug("LoadData, region", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", r)))))

		Regs = append(Regs, r)
	}

	sort.Sort(Regs)

	zap.L().Debug("LoadData, regions len", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(len(Regs))))))
	g.GeoRegions.Store(Regs)

	return
}

func (g *GeoParserV6) QueryGeoInfo(ipStr string) (GeoInfo, error) {
	info := GeoInfo{GeoId: 0, CountryId: 0, ProvinceId: 0, CityId: 0, SubCarrierId: 0, CarrierId: 0}
	high, low, err := StrIPv6ToUInt64s(ipStr)
	if err != nil {
		return info, err
	}

	var regs RegionV6s
	regs = g.GeoRegions.Load().(RegionV6s)

	zap.L().Debug("high: low", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(high)))), zap.Int64("param2", int64(low)))
	zap.L().Debug("regions len", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(len(regs))))))

	r := RegionV6{
		Start:        IpV6Uint64{High: high, Low: low},
		End:          IpV6Uint64{High: high, Low: low},
		Geo:          0,
		SubCarrierId: 0,
		CarrierId:    0,
	}

	rr, err := SearchV6(regs, r)
	if err != nil {
		return info, err
	}
	info.GeoId = rr.Geo
	info.CountryId = rr.Geo / (1000000000)
	info.ProvinceId = rr.Geo % (1000000000) / (1000000)
	info.CityId = rr.Geo
	info.SubCarrierId = rr.SubCarrierId
	info.CarrierId = rr.CarrierId
	info.Ip = ipStr
	return info, nil
}
