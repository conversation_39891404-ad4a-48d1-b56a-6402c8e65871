package frequency_client

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/frequency/frequency_service"
	"gitlab.com/dev/heidegger/frequency/frequency_service/proto/frequency_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/rapid_rpc"
	"gitlab.com/dev/heidegger/library/utils"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"hash/crc32"
	"time"
)

type FrequencyClient struct {
	address []string
	client  []*rapid_rpc.Client
	timeOut int
	metrics *prometheus_helper.LabelHistogram
}

func NewFrequencyClient(address []string, timeoutMillisecond int) *FrequencyClient {
	return &FrequencyClient{
		address: address,
		timeOut: timeoutMillisecond,
		metrics: prometheus_helper.RegisterLabelHistogram("frequency_rapid_client", []string{"method", "result"}),
	}
}

func (client *FrequencyClient) Start() error {
	for _, addr := range client.address {
		zap.L().Info("[FrequencyClient] connect to ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", addr)))))
		c := rapid_rpc.NewClient(rapid_rpc.ClientOption{ClientCount: 64, TimeoutMillisecond: client.timeOut})
		if err := c.Connect(0, addr); err != nil {
			return err
		}

		client.client = append(client.client, c)
	}

	return nil
}

func (client *FrequencyClient) Stop() {
	if client.client != nil {
		for _, client := range client.client {
			client.Close()
		}
	}
}

func (client *FrequencyClient) DoSave() error {
	panic("implement me")
}

func (client *FrequencyClient) Ping(ctx context.Context) error {

	req := frequency_proto.PingRequest{}
	req.Message = "ping"

	buffer := buffer_pool.NewBuffer()
	defer buffer.Release()

	size := req.Size()
	buffer.EnsureSize(size)

	if _, err := req.MarshalToSizedBuffer(buffer.Get()); err != nil {
		return err
	}

	startTime := time.Now()
	label := []string{"Ping", "fail"}
	defer client.metrics.FinishTime(label, startTime)

	respBuffer, err := client.client[0].Send(ctx, 0, frequency_service.FreqMethodIdPing, buffer.Get())
	if err != nil {
		return err
	}
	defer respBuffer.Release()

	resp := frequency_proto.PingResponse{}
	if err := resp.Unmarshal(respBuffer.Get()); err != nil {
		return err
	}

	label[1] = "success"
	return nil
}

func (client *FrequencyClient) BatchQueryLegacy(queryMap map[string]uint32) (map[string]uint32, error) {
	defer client.metrics.FinishTime([]string{"BatchQueryLegacy"}, time.Now())

	req := frequency_proto.GetRequest{}

	for key, periodLength := range queryMap {
		req.GetItem = append(req.GetItem, frequency_proto.GetRequest_GetItem{
			Index:     0,
			Key:       []byte(key),
			Timestamp: 0,                 // 不知道干什么用的
			Type:      utils.EmptyString, // 不知道干什么用的
			Offset:    int32(periodLength),
			Hash:      utils.EmptyString,
		})
	}

	buffer := buffer_pool.NewBuffer()
	defer buffer.Release()

	size := req.Size()
	buffer.EnsureSize(size)

	if _, err := req.MarshalToSizedBuffer(buffer.Get()); err != nil {
		return nil, err
	}

	respBuffer, err := client.client[0].Send(context.TODO(), 0, frequency_service.FreqMethodIdGet, buffer.Get())
	if err != nil {
		return nil, err
	}
	defer respBuffer.Release()

	resp := frequency_proto.GetResponse{}
	if err := resp.Unmarshal(respBuffer.Get()); err != nil {
		return nil, err
	}

	if len(resp.Result) == 0 {
		return nil, nil
	}

	result := make(map[string]uint32, len(resp.Result))
	for _, item := range resp.Result {
		result[string(item.Key)] = uint32(item.Total)
	}

	return result, nil
}

func (client *FrequencyClient) Query(ctx context.Context, queryMap frequency_service.FrequencyBatchQuery) (frequency_service.FrequencyBatchResult, error) {
	startTime := time.Now()
	label := []string{"Query", "fail"}
	defer client.metrics.FinishTime(label, startTime)

	hashQueryMap := client.hashQueryMap(queryMap)

	if len(hashQueryMap) == 1 {
		for hash, hashQuery := range hashQueryMap {
			response, err := client.doQuery(ctx, hash, hashQuery)
			if err != nil {
				return frequency_service.FrequencyBatchResult{}, err
			}

			label[1] = "success"
			return response, nil
		}
		return frequency_service.FrequencyBatchResult{}, fmt.Errorf("should not be here")
	} else {
		type Result struct {
			Result frequency_service.FrequencyBatchResult
			Err    error
		}

		responseChan := make(chan Result, len(hashQueryMap))
		for hash, query := range hashQueryMap {
			localHash := hash
			localQuery := query

			go func() {
				r, err := client.doQuery(ctx, localHash, localQuery)
				responseChan <- Result{Result: r, Err: err}
			}()
		}

		response := frequency_service.CreateFrequencyBatchResult()

		for i := 0; i < len(hashQueryMap); i++ {
			select {
			case <-ctx.Done():
				return frequency_service.FrequencyBatchResult{}, ctx.Err()
			case result := <-responseChan:
				if result.Err != nil {
					return frequency_service.FrequencyBatchResult{}, result.Err
				}
				response.Merge(result.Result)
			}
		}

		label[1] = "success"
		return response, nil
	}
}

func (client *FrequencyClient) hashQueryMap(queryMap frequency_service.FrequencyBatchQuery) map[uint32][]frequency_service.FrequencyQueryItem {
	result := make(map[uint32][]frequency_service.FrequencyQueryItem)
	for _, item := range queryMap.Items {
		hash := client.hash(item.Key)
		result[hash] = append(result[hash], item)
	}

	return result
}

func (client *FrequencyClient) frequencyBatchQueryToPb(queryMap frequency_service.FrequencyBatchQuery) *frequency_proto.GetRequestB9 {
	req := &frequency_proto.GetRequestB9{}

	for _, item := range queryMap.Items {
		req.GetItem = append(req.GetItem, frequency_proto.GetRequestB9_GetItem{
			Key:         []byte(item.Key),
			KeyType:     int32(item.KeyType),
			FrequencyId: item.FrequencyControlIdList,
		})
	}

	return req
}

func (client *FrequencyClient) doQuery(ctx context.Context, hash uint32, queryMap []frequency_service.FrequencyQueryItem) (frequency_service.FrequencyBatchResult, error) {
	req := client.frequencyBatchQueryToPb(frequency_service.FrequencyBatchQuery{Items: queryMap})

	buffer := buffer_pool.NewBuffer()
	defer buffer.Release()

	buffer.EnsureSize(req.Size())

	if _, err := req.MarshalToSizedBuffer(buffer.Get()); err != nil {
		return frequency_service.FrequencyBatchResult{}, err
	}

	respBuffer, err := client.client[hash].Send(ctx, 0, frequency_service.FreqMethodIdGetV2, buffer.Get())
	if err != nil {
		return frequency_service.FrequencyBatchResult{}, err
	}
	defer respBuffer.Release()

	resp := frequency_proto.GetResponseB9{}
	if err := resp.Unmarshal(respBuffer.Get()); err != nil {
		return frequency_service.FrequencyBatchResult{}, err
	}

	if resp.Code != 0 {
		return frequency_service.FrequencyBatchResult{}, fmt.Errorf("[FrequencyClient] Query error, code: %d", resp.Code)
	}

	result := frequency_service.FrequencyBatchResult{}
	for _, item := range resp.Result {
		key := string(item.Key)
		for _, freq := range item.Item {
			result.MarkResult(key, frequency_service.FrequencyResult{
				FreqControlId:   uint32(freq.FrequencyId),
				ImpressionCount: uint32(freq.ImpressionCount),
				ClickCount:      uint32(freq.ClickCount),
				BroadcastCount:  uint32(freq.BroadcastCount),
				BidCount:        uint32(freq.BidCount),
				BlockingCode:    uint32(freq.BlockCode),
			})
		}
	}

	return result, nil
}

func (client *FrequencyClient) Incr(ctx context.Context, query frequency_service.FrequencyBatchIncr) error {
	startTime := time.Now()
	label := []string{"Incr", "fail"}
	defer client.metrics.FinishTime(label, startTime)

	hashIncrMap := client.hashIncrMap(query)
	if len(hashIncrMap) == 1 {
		for hash, hashQuery := range hashIncrMap {
			if err := client.doIncr(ctx, hash, hashQuery); err != nil {
				return err
			}

			label[1] = "success"
			return nil
		}
		return fmt.Errorf("should not be here")
	} else {
		type Result struct {
			Err error
		}

		responseChan := make(chan Result, len(hashIncrMap))
		for hash, query := range hashIncrMap {
			localHash := hash
			localQuery := query
			go func() {
				responseChan <- Result{Err: client.doIncr(ctx, localHash, localQuery)}
			}()
		}

		for i := 0; i < len(hashIncrMap); i++ {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case result := <-responseChan:
				if result.Err != nil {
					return result.Err
				}
			}
		}

		label[1] = "success"
		return nil
	}
}

func (client *FrequencyClient) doIncr(ctx context.Context, hash uint32, queryMap []frequency_service.FrequencyIncrItem) error {
	req := client.frequencyBatchIncrToPb(frequency_service.FrequencyBatchIncr{
		Items: queryMap,
	})

	buffer := buffer_pool.NewBuffer()
	defer buffer.Release()

	size := req.Size()
	buffer.EnsureSize(size)

	if _, err := req.MarshalToSizedBuffer(buffer.Get()); err != nil {
		return err
	}

	respBuffer, err := client.client[hash].Send(ctx, 0, frequency_service.FreqMethodIdAddV2, buffer.Get())
	if err != nil {
		return err
	}
	defer respBuffer.Release()

	resp := frequency_proto.AddResponseB9{}
	if err := resp.Unmarshal(respBuffer.Get()); err != nil {
		return err
	}

	for _, item := range resp.Result {
		if item.Code != 0 {
			return fmt.Errorf("[FrequencyClient] Incr error, code: %d", item.Code)
		}
	}

	return nil
}

func (client *FrequencyClient) hashIncrMap(queryMap frequency_service.FrequencyBatchIncr) map[uint32][]frequency_service.FrequencyIncrItem {
	result := make(map[uint32][]frequency_service.FrequencyIncrItem)
	for _, item := range queryMap.Items {
		hash := client.hash(item.FreqKey)
		result[hash] = append(result[hash], item)
	}

	return result
}

func (client *FrequencyClient) frequencyBatchIncrToPb(query frequency_service.FrequencyBatchIncr) *frequency_proto.AddRequestB9 {
	req := &frequency_proto.AddRequestB9{}

	for _, item := range query.Items {
		req.AddItem = append(req.AddItem, frequency_proto.AddRequestB9_AddItem{
			Key:           []byte(item.FreqKey),
			KeyType:       int32(item.FreqKeyType),
			FrequencyId:   int32(item.FreqControlId),
			FrequencyType: int32(item.FreqControlType),
		})
	}

	return req
}

func (client *FrequencyClient) hash(key string) uint32 {
	return crc32.ChecksumIEEE(type_convert.UnsafeStringToByte(key)) % uint32(len(client.client))
}
