package count_min_sketch

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/utils/rand_utils"
	"os"
	"sync"
	"time"
)

type CountMinSketchRotateConfig struct {
	RootDir          string    `json:"root_dir"`
	RotateType       string    `json:"rotate_type"`
	RotateLength     uint32    `json:"rotate_length"`
	RingSize         uint32    `json:"ring_size"`
	ItemCount        uint32    `json:"item_count"`
	AvgItemFrequency uint32    `json:"avg_item_frequency"`
	ErrorRate        float64   `json:"error_rate"`
	Width            uint64    `json:"width"`
	Height           uint64    `json:"height"`
	FileSize         uint64    `json:"file_size"`
	InitTime         time.Time `json:"init_time"`
	CurrentIndex     uint32    `json:"current_index"`
	CurrentTime      time.Time `json:"current_time"`
}

func (c *CountMinSketchRotateConfig) String() string {
	data, _ := json.Marshal(c)
	return string(data)
}

type CountMinSketchRotate struct {
	config     CountMinSketchRotateConfig
	sketchRing []*Sketch

	saveLock sync.Mutex
	term     chan struct{}
}

func NewCountMinSketchRotate(rootDir string, rotateType string, rotatelength uint32, itemCount uint32, avgItemFrequency uint32, errorRate float64) *CountMinSketchRotate {
	if avgItemFrequency > 255 {
		panic("avgItemFrequency should be less than 255")
	}

	return &CountMinSketchRotate{
		config: CountMinSketchRotateConfig{
			RootDir:          rootDir,
			RotateType:       rotateType,
			RotateLength:     rotatelength,
			ItemCount:        itemCount,
			AvgItemFrequency: avgItemFrequency,
			ErrorRate:        errorRate,
			RingSize:         rotatelength + 1,
		},
	}
}

func (r *CountMinSketchRotate) Start() error {
	go r.resetIndexLoop()
	go r.saveLoop()

	return nil
}

func (r *CountMinSketchRotate) GetConfig() CountMinSketchRotateConfig {
	return r.config
}

func (r *CountMinSketchRotate) LoadOrInit() error {
	if r.hasInit() {
		return r.Load()
	} else {
		return r.Init()
	}
}

func (r *CountMinSketchRotate) Load() error {
	zap.L().Info("[CountMinSketchRotate] load count min sketch rotate from ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", r.config.RootDir)))))
	config, err := r.loadConfigFile()
	if err != nil {
		return err
	}
	if config.RotateType != r.config.RotateType ||
		config.RotateLength != r.config.RotateLength ||
		config.ItemCount != r.config.ItemCount ||
		config.AvgItemFrequency != r.config.AvgItemFrequency ||
		config.ErrorRate != r.config.ErrorRate {
		return fmt.Errorf("config not match")
	}

	r.config = config

	zap.L().Info("[CountMinSketchRotate] load count min sketch config", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", r.config)))))

	r.sketchRing = make([]*Sketch, r.config.RingSize)
	for i := 0; i != len(r.sketchRing); i++ {
		sketch, err := NewSketchFromFile(r.config.RootDir + "/cm_" + fmt.Sprintf("%03d.data", i))
		if err != nil {
			return err
		}
		r.sketchRing[i] = sketch
	}

	r.resetIndex()
	if err := r.writeConfigFile(); err != nil {
		return err
	}

	return nil
}

func (r *CountMinSketchRotate) Init() error {
	zap.L().Info("[CountMinSketchRotate] init count min sketch rotate to ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", r.config.RootDir)))))

	if err := os.MkdirAll(r.config.RootDir, 0755); err != nil {
		return err
	}

	r.config.InitTime = r.getInitTime()
	r.getParams()

	r.sketchRing = make([]*Sketch, r.config.RingSize)
	for i := 0; i != len(r.sketchRing); i++ {
		sketch := NewSketchWithParameter(r.config.Width, r.config.Height)
		r.sketchRing[i] = sketch
	}

	r.resetIndex()
	if err := r.writeConfigFile(); err != nil {
		return err
	}

	return r.writeSketchRing()
}

func (r *CountMinSketchRotate) getParams() {
	w, h := getFrequencyParameters(int32(r.config.ItemCount), int32(r.config.AvgItemFrequency), r.config.ErrorRate)
	r.config.Width = w
	r.config.Height = h
	r.config.FileSize = r.config.Width * r.config.Height
}

func (r *CountMinSketchRotate) getInitTime() time.Time {
	if r.config.RotateType == "day" {
		return time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local)
	} else if r.config.RotateType == "hour" {
		return time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), 0, 0, 0, time.Local)
	} else if r.config.RotateType == "10minute" {
		return time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute()/10*10, 0, 0, time.Local)
	} else if r.config.RotateType == "minute" {
		return time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute(), 0, 0, time.Local)
	} else if r.config.RotateType == "10sec" {
		return time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), time.Now().Hour(), time.Now().Minute(), time.Now().Second()/10*10, 0, time.Local)
	} else {
		panic(fmt.Sprintf("not support rotate type, %s", r.config.RotateType))
	}
}

func (r *CountMinSketchRotate) resetIndex() {
	now := time.Now()

	lastIndex := r.config.CurrentIndex
	currentIndex := lastIndex
	if r.config.RotateType == "day" {
		currentIndex = uint32(now.Sub(r.config.InitTime) / time.Hour / 24)
	} else if r.config.RotateType == "hour" {
		currentIndex = uint32(now.Sub(r.config.InitTime) / time.Hour)
	} else if r.config.RotateType == "10minute" {
		currentIndex = uint32(now.Sub(r.config.InitTime) / time.Minute / 10)
	} else if r.config.RotateType == "minute" {
		currentIndex = uint32(now.Sub(r.config.InitTime) / time.Minute)
	} else if r.config.RotateType == "10sec" {
		currentIndex = uint32(now.Sub(r.config.InitTime) / time.Second / 10)
	} else {
		panic(fmt.Sprintf("not support rotate type: %s", r.config.RotateType))
	}

	if currentIndex == lastIndex {
		return
	}

	if lastIndex != currentIndex {
		gap := currentIndex - lastIndex
		if gap > r.config.RingSize {
			gap = r.config.RingSize
		}

		for i := uint32(0); i != gap; i++ {
			zap.L().Info("[CountMinSketchRotate] cm reset, index , type:%s", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64((lastIndex+i+1)))))%r.config.RingSize, r.config.RotateType)
			r.sketchRing[(lastIndex+i+1)%r.config.RingSize].Reset()
		}
	}

	r.config.CurrentIndex = currentIndex
	r.config.CurrentTime = now

	current := r.config.CurrentIndex
	size := r.config.RingSize
	needle := current + size
	idx := (needle) % size
	r.sketchRing[idx].Prefetch()

	zap.L().Info("[CountMinSketchRotate] set index ", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(r.config.CurrentIndex)))))
}

func (r *CountMinSketchRotate) writeConfigFile() error {
	file, err := os.OpenFile(r.config.RootDir+"/config.json", os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer file.Close()

	return json.NewEncoder(file).Encode(r.config)
}

func (r *CountMinSketchRotate) loadConfigFile() (CountMinSketchRotateConfig, error) {
	file, err := os.OpenFile(r.config.RootDir+"/config.json", os.O_RDONLY, 0644)
	if err != nil {
		return CountMinSketchRotateConfig{}, err
	}
	defer file.Close()

	var config CountMinSketchRotateConfig
	if err := json.NewDecoder(file).Decode(&config); err != nil {
		return CountMinSketchRotateConfig{}, err
	}

	return config, nil
}

func (r *CountMinSketchRotate) hasInit() bool {
	_, err := r.loadConfigFile()
	return err == nil
}

func (r *CountMinSketchRotate) writeSketchRing() error {
	for i := 0; i != len(r.sketchRing); i++ {
		if err := r.sketchRing[i].SaveToFile(r.config.RootDir + "/cm_" + fmt.Sprintf("%03d.data", i)); err != nil {
			return err
		}
	}
	return nil
}

func (r *CountMinSketchRotate) resetIndexLoop() {
	ticker := time.NewTicker(time.Second * 3)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			r.resetIndex()
		case <-r.term:
			return
		}
	}
}

func (r *CountMinSketchRotate) saveLoop() {
	configWriteDuration := time.Second * 3600
	saveDuration := time.Second * 3600

	if r.config.RotateType == "day" {
		configWriteDuration = time.Second * 3600
		saveDuration = time.Second * 3600
	} else if r.config.RotateType == "hour" {
		configWriteDuration = time.Second * 3600
		saveDuration = time.Second * 3600
	} else { // do not need to save
		configWriteDuration = time.Second * 3600
		saveDuration = time.Second * 3600
		return
	}

	configWriteTicker := time.NewTicker(configWriteDuration)
	defer configWriteTicker.Stop()

	saveTicker := time.NewTicker(saveDuration)
	defer saveTicker.Stop()

	for {
		select {
		case <-configWriteTicker.C:
			r.saveLock.Lock()

			if err := r.writeConfigFile(); err != nil {
				zap.L().Error("[CountMinSketchRotate] writeConfigFile error", zap.Error(err))
			}

			r.saveLock.Unlock()

		case <-saveTicker.C:
			r.saveLock.Lock()

			if err := r.writeSketchRing(); err != nil {
				zap.L().Error("[CountMinSketchRotate] writeSketchRing error", zap.Error(err))
			}

			r.saveLock.Unlock()

		case <-r.term:
			return
		}
	}
}

func (r *CountMinSketchRotate) DoSave() error {
	r.saveLock.Lock()
	defer r.saveLock.Unlock()

	if err := r.writeConfigFile(); err != nil {
		return err
	}

	if err := r.writeSketchRing(); err != nil {
		return err
	}

	return nil
}

func (r *CountMinSketchRotate) SelfCheck() float64 {
	totalCount := 0
	failCount := 0
	for i := 0; i != 10000; i++ {
		key := rand_utils.RandString("ABCDEF1234567890", 32)

		totalCount++
		count := r.AdaptiveQuery([]byte(key), 0, r.config.RotateLength)
		if count != 0 {
			zap.L().Info("[CountMinSketchRotate] self check fail, key:%s count", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(key)))), zap.Int64("param2", int64(count)))
			failCount++
		}
	}

	failRate := float64(failCount) / float64(totalCount)
	if failRate > 0.01 {
		zap.L().Error("[CountMinSketchRotate] self check fail rate %f")
	}

	return failRate
}

func (r *CountMinSketchRotate) Incr(key []byte, keyDim uint64) uint8 {
	//current := r.config.CurrentIndex
	//size := r.config.RingSize
	//
	//idx := (current) % size

	return r.sketchRing[r.config.CurrentIndex%r.config.RingSize].Incr(key, keyDim)
}

func (r *CountMinSketchRotate) Add(key []byte, keyDim uint64, count uint8) uint8 {
	current := r.config.CurrentIndex
	size := r.config.RingSize

	idx := (current) % size

	return r.sketchRing[idx].Add(key, keyDim, count)
}

func (r *CountMinSketchRotate) AdaptiveQuery(key []byte, keyDim uint64, length uint32) uint32 {
	if length > 1 {
		return r.QueryWithHashCache(key, keyDim, length)
	}

	return r.Query(key, keyDim, length)
}

func (r *CountMinSketchRotate) Query(key []byte, keyDim uint64, length uint32) uint32 {
	if length > r.config.RotateLength {
		length = r.config.RotateLength
	}

	current := r.config.CurrentIndex
	size := r.config.RingSize

	total := uint32(0)
	for i := 0; i != int(length); i++ {
		needle := current - uint32(i) + size
		idx := (needle) % size
		sketch := r.sketchRing[idx]
		count := sketch.Query(key, keyDim)
		total += uint32(count)
	}

	return total
}

func (r *CountMinSketchRotate) QueryWithHashCache(key []byte, keyDim uint64, length uint32) uint32 {
	if length > r.config.RotateLength {
		length = r.config.RotateLength
	}

	current := r.config.CurrentIndex
	size := r.config.RingSize

	var hashCache []uint64

	total := uint32(0)
	for i := 0; i != int(length); i++ {
		needle := current - uint32(i) + size
		idx := (needle) % size
		sketch := r.sketchRing[idx]

		var count uint8
		count, hashCache = sketch.QueryWithHash(key, keyDim, hashCache)
		total += uint32(count)
	}

	return total
}
