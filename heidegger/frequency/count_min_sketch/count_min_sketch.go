package count_min_sketch

import (
	"encoding/binary"
	"math"
	"os"
	"reflect"
	"sync/atomic"
	"unsafe"

	"github.com/dgryski/go-metro"

	"go.uber.org/zap"
)

// getParameters
// @errorRange
//
//	误差率（通常表示为ε）是指CMS估算频率值时的最大相对误差。
//	它是数据结构的一个重要参数，直接影响着CMS的宽度（列数）。
//	具体来说，如果一个元素的真实频率是f，那么CMS估算的频率会在f和f + εN之间，
//	其中N是数据流中所有元素的总频率。
//		例如，如果你设置ε为0.01（1%），并且某个元素的真实频率是100次，
//		那么CMS估算的频率可能在100到101之间（如果N是10000）。
//
// @errorRate
//
//		误差发生的概率（通常表示为δ）是指CMS估算的频率超出上述范围（由误差率确定）
//		的概率。它决定了CMS的深度（行数）。较低的δ值意味着更高的准确性，但需要更多的内存空间。
//	 例如，如果δ设置为0.01（1%），这意味着只有1%的概率估算的频率会超出由ε确定的误差范围。
func getParameters(errorRange float64, errorRate float64) (uint64, uint64) {
	width := int(math.Ceil(1 / errorRange))
	depth := int(math.Ceil(math.Log(1 / errorRate)))

	width = (width/4 + 1) * 4

	return uint64(width), uint64(depth)
}

type Sketch struct {
	name string

	width uint64 // bytes
	depth uint64 // layers

	data []uint8
}

func (s *Sketch) SetName(name string) {
	s.name = name
}

func (s *Sketch) Reset() {
	s.unload()

	s.data = make([]byte, int64(s.depth*s.width))
	// get the address of the first byte and assert it is aligned by 4 byte

	addr := uintptr(unsafe.Pointer(&s.data[0]))
	if addr%4 != 0 {
		panic("data is not aligned")
	}
}

func (s *Sketch) Incr(key []byte, keyDim uint64) uint8 {
	return s.Add(key, keyDim, 1)
}

func (s *Sketch) Add(key []byte, keyDim uint64, count uint8) uint8 {
	minValue := uint8(255)

	baseHash := s.doHash(key, keyDim)

	for i := 0; i != int(s.depth); i++ {
		//hash := s.doHash(key, i)
		hash := s.doMergeHash(baseHash, i)
		index := hash % s.width
		index = index + uint64(i)*s.width

		uint32Index, byteIndex := index/4*4, index%4

		var currentByte uint8
		for {
			currentUint32 := atomic.LoadUint32((*uint32)(unsafe.Pointer(&s.data[uint32Index])))
			currentByte = uint8(currentUint32 >> (byteIndex * 8))

			newByte := currentByte + count
			newUint32 := currentUint32&^(uint32(0xff)<<(byteIndex*8)) | (uint32(newByte) << (byteIndex * 8))
			if atomic.CompareAndSwapUint32((*uint32)(unsafe.Pointer(&s.data[uint32Index])), currentUint32, newUint32) {
				break
			}
		}

		if currentByte < minValue {
			minValue = currentByte
		}
	}

	return minValue
}

func (s *Sketch) Prefetch() {
	if s.data != nil && len(s.data) != 0 {
		s.prefetch(unsafe.Pointer(&s.data[0]), len(s.data))
	}
}

func (s *Sketch) UnsafeAccess(data []byte, index int) byte {
	return *(*byte)(unsafe.Pointer(uintptr(unsafe.Pointer(&data[0])) + uintptr(index)))
}

func (s *Sketch) Query(key []byte, keyDim uint64) uint8 {
	min := uint8(255)

	//zap.L().Info("width:, depth", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(s.width)))), zap.Int64("param2", int64(s.depth)))

	baseHash := s.doHash(key, keyDim)

	for i := 0; i != int(s.depth); i++ {
		//hash := uint64(murmur3.Sum32WithSeed(key, uint32(i)))
		//hash := s.doHash(key, i)
		hash := s.doMergeHash(baseHash, i)
		index := hash % s.width
		index = index + uint64(i)*s.width

		//uint32Index, byteOffset := index/4*4, index%4
		//
		//currentUint32 := atomic.LoadUint32((*uint32)(unsafe.Pointer(&s.data[uint32Index])))
		//currentByte := uint8(currentUint32 >> (byteOffset * 8))

		currentByte := s.UnsafeAccess(s.data, int(index))

		//zap.L().Info("currentUint32:, currentByte", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(currentUint32)))), zap.Int64("param2", int64(currentByte)))

		if currentByte < min {
			min = currentByte
		}
	}

	return min
}

func (s *Sketch) QueryWithHash(key []byte, keyDim uint64, hashCache []uint64) (uint8, []uint64) {
	min := uint8(255)

	//zap.L().Info("width:, depth", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(s.width)))), zap.Int64("param2", int64(s.depth)))

	baseHash := s.doHash(key, keyDim)

	for i := 0; i != int(s.depth); i++ {
		hash := uint64(0)
		if len(hashCache) > i {
			hash = hashCache[i]
		} else {
			//hash = uint64(murmur3.Sum32WithSeed(key, uint32(i)))
			//hash := s.doHash(key, i)
			hash := s.doMergeHash(baseHash, i)
			hashCache = append(hashCache, hash)
		}

		index := hash % s.width
		index = index + uint64(i)*s.width

		//uint32Index, byteOffset := index/4*4, index%4
		//
		//currentUint32 := atomic.LoadUint32((*uint32)(unsafe.Pointer(&s.data[uint32Index])))
		//currentByte := uint8(currentUint32 >> (byteOffset * 8))

		currentByte := s.UnsafeAccess(s.data, int(index))

		//zap.L().Info("currentUint32:, currentByte", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(currentUint32)))), zap.Int64("param2", int64(currentByte)))

		if currentByte < min {
			min = currentByte
		}
	}

	return min, hashCache
}

func (s *Sketch) doHash(key []byte, i uint64) uint64 {
	return metro.Hash64(key, i)
}

func (s *Sketch) doMergeHash(baseHash uint64, i int) uint64 {
	return (baseHash >> i) | (baseHash << (64 - i))
}

func (s *Sketch) SaveToFile(path string) error {
	file, err := os.OpenFile(path, os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}

	defer file.Close()

	if err := binary.Write(file, binary.LittleEndian, s.width); err != nil {
		return err
	}

	if err := binary.Write(file, binary.LittleEndian, s.depth); err != nil {
		return err
	}

	zap.L().Info("SaveToFile width:, depth", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(s.width)))), zap.Int64("param2", int64(s.depth)))

	if _, err := file.Write(s.data); err != nil {
		return err
	}

	return nil
}

func (s *Sketch) LoadFromFile(path string) error {
	file, err := os.OpenFile(path, os.O_RDONLY, 0644)
	if err != nil {
		return err
	}

	defer file.Close()

	if err := binary.Read(file, binary.LittleEndian, &s.width); err != nil {
		return err
	}

	if err := binary.Read(file, binary.LittleEndian, &s.depth); err != nil {
		return err
	}

	zap.L().Info("LoadFromFile width:, depth", zap.Int64("id", zap.String("value2", fmt.Sprintf("%v", int64(s.width)))), zap.Int64("param2", int64(s.depth)))

	s.data = make([]byte, int64(s.depth*s.width))
	if _, err := file.Read(s.data); err != nil {
		return err
	}

	s.SetName(path)
	s.Prefetch()

	return nil
}

func NewSketchForFrequency(totalItem int32, avgItemFreq int32, errorRate float64) *Sketch {
	// 我们将 errorRange 先估算到 1， 这样无法范围在 1附近
	// 然后再将其缩小到 1/2
	errorRange := 1 / float64(avgItemFreq) / float64(totalItem)
	errorRange = errorRange / 2

	w, d := getParameters(errorRange, errorRate)
	return NewSketchWithParameter(w, d)
}

func getFrequencyParameters(totalItem int32, avgItemFreq int32, errorRate float64) (uint64, uint64) {
	errorRange := 1 / float64(avgItemFreq) / float64(totalItem)
	errorRange = errorRange / 2

	return getParameters(errorRange, errorRate)
}

func NewSketchFromFile(path string) (*Sketch, error) {
	sketch := &Sketch{}
	if err := sketch.LoadFromFile(path); err != nil {
		return nil, err
	}

	return sketch, nil
}

func NewSketchWithParameter(width uint64, depth uint64) *Sketch {
	sketch := &Sketch{
		width: width,
		depth: depth,
	}

	sketch.Reset()
	return sketch
}

func StringBytes(str string) []byte {
	hdr := *(*reflect.StringHeader)(unsafe.Pointer(&str))
	return *(*[]byte)(unsafe.Pointer(&reflect.SliceHeader{
		Data: hdr.Data,
		Len:  hdr.Len,
		Cap:  hdr.Len,
	}))
}
