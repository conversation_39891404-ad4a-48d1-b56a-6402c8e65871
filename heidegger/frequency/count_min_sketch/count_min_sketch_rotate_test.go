package count_min_sketch

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"testing"
	"time"
)

func TestCountMinSketchRotate_Start(t *testing.T) {
	cmr := NewCountMinSketchRotate("./", "min", 5, 100, 2, 0.00001)
	err := cmr.LoadOrInit()
	if err != nil {
		t.Error(err)
		return
	}

	if err := cmr.Start(); err != nil {
		t.Error(err)
		return
	}

	ticker := time.NewTicker(time.Second * 10)
	defer ticker.Stop()

	testKey := "test"
	for range ticker.C {
		result := cmr.Query(type_convert.UnsafeStringToByte(testKey), 3)
		cmr.Incr(type_convert.UnsafeStringToByte(testKey))
		zap.L().Info("result", zap.Int64("param1", zap.String("value2", fmt.Sprintf("%v", int64(result)))))
	}
}
