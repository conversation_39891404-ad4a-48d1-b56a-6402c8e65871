package frequency_service

import (
	"context"
	"github.com/panjf2000/ants/v2"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/entity_loader/frequency_control_loader"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/utils"
	"sync"
	"fmt"
)

type LocalFrequencyManagerGroup struct {
	group                  map[string]*LocalFrequencyManager
	frequencyControlLoader frequency_control_loader.FrequencyControlLoader

	requestItemCounter *prometheus_helper.SimpleCounter
	maxBatch           int

	batchWorker *ants.Pool
}

func NewLocalFrequencyManagerGroup() *LocalFrequencyManagerGroup {
	worker, err := ants.NewPool(1024, ants.WithNonblocking(false))
	if err != nil {
		panic(err)
	}

	return &LocalFrequencyManagerGroup{
		group:              make(map[string]*LocalFrequencyManager),
		maxBatch:           4,
		requestItemCounter: prometheus_helper.RegisterSimpleCounter("frequency_request_item"),

		batchWorker: worker,
	}
}

func (m *LocalFrequencyManagerGroup) SetFrequencyControlLoader(loader frequency_control_loader.FrequencyControlLoader) {
	m.frequencyControlLoader = loader
}

func (m *LocalFrequencyManagerGroup) Register(rotationType string, manager *LocalFrequencyManager) error {
	if _, ok := m.group[rotationType]; ok {
		return ErrManagerAlreadyExist
	}
	m.group[rotationType] = manager
	zap.L().Info("LocalFrequencyManagerGroup Register", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", rotationType)))))
	return nil
}

func (m *LocalFrequencyManagerGroup) DoSave() error {
	for _, manager := range m.group {
		if err := manager.DoSave(); err != nil {
			return err
		}
	}
	return nil
}

func (m *LocalFrequencyManagerGroup) doSingleQuery(key string, keyType entity.FrequencyKeyType, freqControl *entity.FrequencyControl) (FrequencyResult, error) {
	result := FrequencyResult{
		FreqControlId: uint32(freqControl.Id),
	}

	for _, item := range freqControl.FrequencyControlItem {
		if item.KeyType != keyType {
			continue
		}

		blockingCode := BlockingCodeImpressionLimit
		value := &result.ImpressionCount
		freqKey := key

		switch item.ControlType {
		case entity.FrequencyControlTypeImpression:
			blockingCode = BlockingCodeImpressionLimit
			value = &result.ImpressionCount
		case entity.FrequencyControlTypeClick:
			blockingCode = BlockingCodeClickLimit
			value = &result.ClickCount
		case entity.FrequencyControlTypeBroadcast:
			blockingCode = BlockingCodeBroadcastLimit
			value = &result.BroadcastCount
		case entity.FrequencyControlTypeBid:
			blockingCode = BlockingCodeBidLimit
			value = &result.BidCount
		default:
		}

		count, err := m.doLocalQuery(freqKey, freqControl.Id, item.ControlType, item.PeriodType, item.PeriodLength)
		if err != nil {
			return FrequencyResult{}, err
		}

		if *value < count {
			*value = count
		}

		if count >= item.LimitCount {
			result.BlockingCode = blockingCode
			return result, nil
		}
	}

	return result, nil
}

func (m *LocalFrequencyManagerGroup) doLocalQuery(key string, controlId utils.ID, controlType entity.FrequencyControlType, periodType entity.FrequencyPeriodType, periodLength uint32) (uint32, error) {
	localManager := m.group[periodType.String()]
	if localManager == nil {
		zap.L().Error("LocalFrequencyManagerGroup doLocalQuery failed, periodType", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", periodType.String())))))
		return 0, ErrManagerNotFound
	}

	return localManager.SingleQuery(key, localManager.BuildKeyDim(uint32(controlId), uint32(controlType)), periodLength)
}

func (m *LocalFrequencyManagerGroup) Query(ctx context.Context, query FrequencyBatchQuery) (FrequencyBatchResult, error) {
	result := FrequencyBatchResult{}
	itemCount := 0

	for _, queryItem := range query.Items {
		for range queryItem.FrequencyControlIdList {
			itemCount++
		}
	}

	m.requestItemCounter.Add(float64(itemCount))

	batchGroup := (itemCount + (m.maxBatch - 1)) / m.maxBatch

	if batchGroup <= 1 {
		for _, queryItem := range query.Items {
			for _, freqControlId := range queryItem.FrequencyControlIdList {
				freqControl := m.frequencyControlLoader.GetFrequencyControlById(utils.ID(freqControlId))
				if freqControl == nil {
					continue
				}

				freqResult, err := m.doSingleQuery(queryItem.Key, entity.FrequencyKeyType(queryItem.KeyType), freqControl)
				if err != nil {
					return FrequencyBatchResult{}, err
				}

				result.MarkResult(queryItem.Key, freqResult)
			}
		}
	} else {
		ch := make(chan uint32, batchGroup)
		resultLock := sync.Mutex{}

		wg := sync.WaitGroup{}
		for i := 0; i < batchGroup; i++ {
			wg.Add(1)
			m.batchWorker.Submit(func() {
				defer wg.Done()
				for freqControlId := range ch {
					freqControl := m.frequencyControlLoader.GetFrequencyControlById(utils.ID(freqControlId))
					if freqControl == nil {
						continue
					}

					for _, queryItem := range query.Items {
						freqResult, err := m.doSingleQuery(queryItem.Key, entity.FrequencyKeyType(queryItem.KeyType), freqControl)
						if err != nil {
							continue
						}

						resultLock.Lock()
						result.MarkResult(queryItem.Key, freqResult)
						resultLock.Unlock()
					}
				}
			})
		}

		for _, queryItem := range query.Items {
			for _, freqControlId := range queryItem.FrequencyControlIdList {
				ch <- freqControlId
			}
		}

		close(ch)
		wg.Wait()
	}

	return result, nil
}

func (m *LocalFrequencyManagerGroup) Incr(ctx context.Context, query FrequencyBatchIncr) error {
	for _, queryItem := range query.Items {
		freqControl := m.frequencyControlLoader.GetFrequencyControlById(utils.ID(queryItem.FreqControlId))
		if freqControl == nil {
			return ErrFrequencyControlNotFound
		}

		for _, item := range freqControl.FrequencyControlItem {
			if item.ControlType != entity.FrequencyControlType(queryItem.FreqControlType) {
				continue
			}

			if item.KeyType != entity.FrequencyKeyType(queryItem.FreqKeyType) {
				continue
			}

			if err := m.doIncr(queryItem.FreqKey, entity.FrequencyKeyType(queryItem.FreqKeyType), freqControl, item); err != nil {
				return err
			}
		}
	}

	return nil
}

func (m *LocalFrequencyManagerGroup) Ping(ctx context.Context) error {
	return nil
}

func (m *LocalFrequencyManagerGroup) doIncr(key string, keyType entity.FrequencyKeyType, freqControl *entity.FrequencyControl, freqControlItem *entity.FrequencyControlItem) error {
	return m.doLocalIncr(key, freqControl.Id, freqControlItem.ControlType, freqControlItem.PeriodType, freqControlItem.PeriodLength)
}

func (m *LocalFrequencyManagerGroup) doLocalIncr(key string, controlId utils.ID, controlType entity.FrequencyControlType, periodType entity.FrequencyPeriodType, periodLength uint32) error {
	localManager := m.group[periodType.String()]
	if localManager == nil {
		return ErrManagerNotFound
	}

	return localManager.Incr(key, localManager.BuildKeyDim(uint32(controlId), uint32(controlType)))
}
