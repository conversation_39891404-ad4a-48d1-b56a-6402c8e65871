package frequency_service

import (
	"context"
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/echo_helper"
	"gitlab.com/dev/heidegger/library/mclock"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"fmt"
)

type FrequencyHttpHandler struct {
	frequencyManager FrequencyManagerInterface

	addHistogram *prometheus_helper.SimpleHistogram
	getHistogram *prometheus_helper.SimpleHistogram
}

func NewFrequencyHttpHandler(manager FrequencyManagerInterface) *FrequencyHttpHandler {
	return &FrequencyHttpHandler{
		frequencyManager: manager,
		addHistogram:     prometheus_helper.RegisterSimpleHistogram("frequency_http_handler_add"),
		getHistogram:     prometheus_helper.RegisterSimpleHistogram("frequency_http_handler_get"),
	}
}

func (handler *FrequencyHttpHandler) RegisterEcho(e *echo.Echo) {
	e.Any("/frequency/get", handler.onFrequencyGet)
	e.Any("/frequency/add", handler.onFrequencyAdd)
	e.Any("/frequency/save", handler.onFrequencySave)
}

func (handler *FrequencyHttpHandler) onFrequencyGet(ctx echo.Context) error {
	defer handler.getHistogram.ObserveMClockStart(mclock.Now())

	request := FrequencyBatchQuery{}
	if err := ctx.Bind(&request); err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	zap.L().Info("onFrequencyGet", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.DumpJson())))))

	count, err := handler.frequencyManager.Query(context.TODO(), request)
	if err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	return echo_helper.Response(ctx, count)
}

func (handler *FrequencyHttpHandler) onFrequencyAdd(ctx echo.Context) error {
	defer handler.getHistogram.ObserveMClockStart(mclock.Now())

	request := FrequencyBatchIncr{}
	if err := ctx.Bind(&request); err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	zap.L().Info("onFrequencyAdd", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", request.DumpJson())))))

	if err := handler.frequencyManager.Incr(context.TODO(), request); err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	return echo_helper.Response(ctx, nil)
}

func (handler *FrequencyHttpHandler) onFrequencySave(ctx echo.Context) error {
	defer handler.getHistogram.ObserveMClockStart(mclock.Now())

	if err := handler.frequencyManager.DoSave(); err != nil {
		return echo_helper.ErrorResponse(ctx, err)
	}

	return echo_helper.Response(ctx, nil)
}
