package frequency_service

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/frequency/frequency_service/proto/frequency_proto"
	"gitlab.com/dev/heidegger/library/buffer_pool"
	"gitlab.com/dev/heidegger/library/entity"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/rapid_rpc"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"net"
	"strconv"
	"strings"
	"time"
)

const (
	FreqMethodIdError = 0
	FreqMethodIdAdd   = 1
	FreqMethodIdGet   = 2
	FreqMethodIdAddV2 = 3
	FreqMethodIdGetV2 = 4
	FreqMethodIdPing  = 5

	FreqResponseCodeSuccess = 0
	FreqResponseCodeFail    = 1
)

type FrequencyRapidServer struct {
	frequencyServer     FrequencyManagerInterface
	rapidServer         *rapid_rpc.Server
	requestCountMetrics *prometheus_helper.LabelHistogram
}

func NewFrequencyRapidServer(manager FrequencyManagerInterface) *FrequencyRapidServer {
	return &FrequencyRapidServer{
		frequencyServer:     manager,
		requestCountMetrics: prometheus_helper.RegisterLabelHistogram("frequency_rapid_server", []string{"method"}),
	}
}

func (server *FrequencyRapidServer) Start(host string, port int, workers int) error {
	server.rapidServer = rapid_rpc.NewServer(workers)

	server.rapidServer.RegisterService(FreqMethodIdAdd, server.freqAddLegacy)
	server.rapidServer.RegisterService(FreqMethodIdGet, server.freqGetLegacy)
	server.rapidServer.RegisterService(FreqMethodIdAddV2, server.freqAdd)
	server.rapidServer.RegisterService(FreqMethodIdGetV2, server.freqGet)
	server.rapidServer.RegisterService(FreqMethodIdPing, server.Ping)

	listener, err := net.Listen("tcp", fmt.Sprintf("%s:%d", host, port))
	if err != nil {
		return err
	}

	if err := server.rapidServer.Serve(listener); err != nil {
		return err
	}

	return nil
}

func (server *FrequencyRapidServer) Stop() {

}

func (server *FrequencyRapidServer) freqAddLegacy(t *rapid_rpc.Transport, header rapid_rpc.FrameHeader, data buffer_pool.Buffer) {
	defer server.requestCountMetrics.FinishTime([]string{"AddLegacy"}, time.Now())

	defer data.Release()

	req := frequency_proto.AddRequest{}
	err := req.Unmarshal(data.Get())
	if err != nil {
		zap.L().Error("unmarshal add request failed, err", zap.Error(err))
		server.ResponseError(t, header, data.Get())
		return
	}

	query := FrequencyBatchIncr{
		Items: make([]FrequencyIncrItem, 0, len(req.AddItem)),
	}

	for idx := range req.AddItem {
		key := string(req.AddItem[idx].Key)
		if !strings.HasPrefix(key, "freq_") {
			zap.L().Error("unmarshal add request failed, key err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", key)))))
			server.ResponseError(t, header, data.Get())
			return
		}

		parts := strings.SplitN(key, "_", 3)
		if len(parts) != 3 {
			zap.L().Error("unmarshal add request failed, key err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", key)))))
			server.ResponseError(t, header, data.Get())
			return
		}

		freqControlId, err := strconv.ParseInt(parts[1], 10, 64)
		if err != nil {
			zap.L().Error("unmarshal add request failed, key err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", key)))))
			server.ResponseError(t, header, data.Get())
			return
		}

		freqKey := parts[2]

		query.Items = append(query.Items, FrequencyIncrItem{
			FreqKey:         freqKey,
			FreqKeyType:     uint32(entity.FrequencyKeyTypeDeviceId),
			FreqControlId:   uint32(freqControlId),
			FreqControlType: uint32(entity.FrequencyControlTypeImpression),
		})
	}

	err = server.frequencyServer.Incr(context.TODO(), query)
	if err != nil {
		zap.L().Error("incr failed, err", zap.Error(err))
	}

	resp := frequency_proto.AddResponse{}
	for idx := range req.AddItem {
		resp.Result = append(resp.Result, frequency_proto.AddResponse_AddResult{
			Key:  type_convert.CopyByteSlice(req.AddItem[idx].Key),
			Code: FreqResponseCodeSuccess,
		})
	}

	pbSize := resp.Size()
	data.EnsureSize(pbSize)

	if _, err := resp.MarshalToSizedBuffer(data.Get()); err != nil {
		zap.L().Error("marshal add response failed, err", zap.Error(err))
		server.ResponseError(t, header, data.Get())
		return
	}

	t.Response(header, data.Get())
}

func (server *FrequencyRapidServer) freqGetLegacy(t *rapid_rpc.Transport, header rapid_rpc.FrameHeader, data buffer_pool.Buffer) {
	defer server.requestCountMetrics.FinishTime([]string{"GetLegacy"}, time.Now())

	defer data.Release()

	req := frequency_proto.GetRequest{}
	err := req.Unmarshal(data.Get())
	if err != nil {
		zap.L().Error("unmarshal get request failed, err", zap.Error(err))
		server.ResponseError(t, header, data.Get())
		return
	}

	query := FrequencyBatchQuery{
		Items: make([]FrequencyQueryItem, 0, len(req.GetItem)),
	}

	for idx := range req.GetItem {
		key := string(req.GetItem[idx].Key)
		if !strings.HasPrefix(key, "freq_") {
			zap.L().Error("unmarshal get request failed, key err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", key)))))
			server.ResponseError(t, header, data.Get())
			return
		}

		parts := strings.SplitN(key, "_", 3)
		if len(parts) != 3 {
			zap.L().Error("unmarshal get request failed, key err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", key)))))
			server.ResponseError(t, header, data.Get())
			return
		}

		freqControlId, err := strconv.ParseInt(parts[1], 10, 64)
		if err != nil {
			zap.L().Error("unmarshal get request failed, key err", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", key)))))
			server.ResponseError(t, header, data.Get())
			return
		}

		freqKey := parts[2]

		query.Items = append(query.Items, FrequencyQueryItem{
			Key:                    freqKey,
			KeyType:                uint32(entity.FrequencyKeyTypeDeviceId),
			FrequencyControlIdList: []uint32{uint32(freqControlId)},
		})
	}

	result, err := server.frequencyServer.Query(context.TODO(), query)
	response := &frequency_proto.GetResponse{}

	if err != nil {
		zap.L().Error("freqGetLegacy err", zap.Error(err))
		server.ResponseError(t, header, data.Get())
	} else {
		response.Result = make([]frequency_proto.GetResponse_GetResult, 0, len(result.Result))

		for key, idResult := range result.Result {
			for _, freqResult := range idResult {
				freqKey := fmt.Sprintf("freq_%d_%s", freqResult.FreqControlId, key)
				response.Result = append(response.Result, frequency_proto.GetResponse_GetResult{
					Key:   []byte(freqKey),
					Total: int32(freqResult.ImpressionCount),
				})
			}
		}

		pbSize := response.Size()
		data.EnsureSize(pbSize)

		if _, err := response.MarshalToSizedBuffer(data.Get()); err != nil {
			zap.L().Error("marshal add response failed, err", zap.Error(err))
			server.ResponseError(t, header, data.Get())
			return
		}

		t.Response(header, data.Get())
	}
}

func (server *FrequencyRapidServer) freqAdd(t *rapid_rpc.Transport, header rapid_rpc.FrameHeader, data buffer_pool.Buffer) {
	defer server.requestCountMetrics.FinishTime([]string{"Add"}, time.Now())

	defer data.Release()

	req := frequency_proto.AddRequestB9{}
	err := req.Unmarshal(data.Get())
	if err != nil {
		zap.L().Error("unmarshal add request failed, err", zap.Error(err))
		server.ResponseError(t, header, data.Get())
		return
	}

	query := server.pbAddRequestToFrequencyBatchIncr(&req)

	err = server.frequencyServer.Incr(context.TODO(), query)
	if err != nil {
		zap.L().Error("incr failed, err", zap.Error(err))
	}

	resp := frequency_proto.AddResponseB9{}
	for idx := range req.AddItem {
		resp.Result = append(resp.Result, frequency_proto.AddResponseB9_AddResult{
			Key:     type_convert.CopyByteSlice(req.AddItem[idx].Key),
			KeyType: req.AddItem[idx].KeyType,
			Code:    FreqResponseCodeSuccess,
		})
	}

	pbSize := resp.Size()
	data.EnsureSize(pbSize)

	if _, err := resp.MarshalToSizedBuffer(data.Get()); err != nil {
		zap.L().Error("marshal add response failed, err", zap.Error(err))
		server.ResponseError(t, header, data.Get())
		return
	}

	t.Response(header, data.Get())
}

func (server *FrequencyRapidServer) freqGet(t *rapid_rpc.Transport, header rapid_rpc.FrameHeader, data buffer_pool.Buffer) {
	defer server.requestCountMetrics.FinishTime([]string{"Get"}, time.Now())

	defer data.Release()

	req := frequency_proto.GetRequestB9{}
	err := req.Unmarshal(data.Get())
	if err != nil {
		zap.L().Error("unmarshal get request failed, err", zap.Error(err))
		server.ResponseError(t, header, data.Get())
		return
	}

	query := server.pbAddRequestToFrequencyBatchQuery(&req)
	result, err := server.frequencyServer.Query(context.TODO(), query)
	response := server.frequencyBatchResultToPbGetResponse(result, err)

	pbSize := response.Size()
	data.EnsureSize(pbSize)

	if _, err := response.MarshalToSizedBuffer(data.Get()); err != nil {
		zap.L().Error("marshal get response failed, err", zap.Error(err))
		server.ResponseError(t, header, data.Get())
		return
	}

	t.Response(header, data.Get())
}

func (server *FrequencyRapidServer) Ping(t *rapid_rpc.Transport, header rapid_rpc.FrameHeader, data buffer_pool.Buffer) {
	defer server.requestCountMetrics.FinishTime([]string{"Ping"}, time.Now())

	defer data.Release()

	req := frequency_proto.PingRequest{}
	err := req.Unmarshal(data.Get())
	if err != nil {
		zap.L().Error("unmarshal ping request failed, err", zap.Error(err))
		server.ResponseError(t, header, data.Get())
		return
	}

	resp := frequency_proto.PingResponse{}
	resp.Message = req.Message

	pbSize := resp.Size()
	data.EnsureSize(pbSize)

	if _, err := resp.MarshalToSizedBuffer(data.Get()); err != nil {
		zap.L().Error("marshal ping response failed, err", zap.Error(err))
		server.ResponseError(t, header, data.Get())
		return
	}

	t.Response(header, data.Get())
}

func (server *FrequencyRapidServer) ResponseError(t *rapid_rpc.Transport, header rapid_rpc.FrameHeader, buffer []byte) {
	header.SetCommandId(FreqMethodIdError)
	t.Response(header, buffer)
}

func (server *FrequencyRapidServer) pbAddRequestToFrequencyBatchIncr(request *frequency_proto.AddRequestB9) FrequencyBatchIncr {
	query := FrequencyBatchIncr{
		Items: make([]FrequencyIncrItem, 0, len(request.AddItem)),
	}

	for idx := range request.AddItem {
		query.Items = append(query.Items, FrequencyIncrItem{
			FreqKey:         string(request.AddItem[idx].Key),
			FreqKeyType:     uint32(request.AddItem[idx].KeyType),
			FreqControlId:   uint32(request.AddItem[idx].FrequencyId),
			FreqControlType: uint32(request.AddItem[idx].FrequencyType),
		})
	}

	return query
}

func (server *FrequencyRapidServer) pbAddRequestToFrequencyBatchQuery(request *frequency_proto.GetRequestB9) FrequencyBatchQuery {
	query := FrequencyBatchQuery{
		Items: make([]FrequencyQueryItem, 0, len(request.GetItem)),
	}

	for idx := range request.GetItem {
		query.Items = append(query.Items, FrequencyQueryItem{
			Key:                    string(request.GetItem[idx].Key),
			KeyType:                uint32(request.GetItem[idx].KeyType),
			FrequencyControlIdList: request.GetItem[idx].FrequencyId,
		})
	}

	return query
}

func (server *FrequencyRapidServer) frequencyBatchResultToPbGetResponse(result FrequencyBatchResult, err error) *frequency_proto.GetResponseB9 {
	resp := &frequency_proto.GetResponseB9{}
	if err != nil {
		resp.Code = FreqResponseCodeFail
		return resp
	}

	resp.Code = FreqResponseCodeSuccess
	resp.Result = make([]frequency_proto.GetResponseB9_GetResult, 0, len(result.Result))

	for key, idResult := range result.Result {
		getResult := frequency_proto.GetResponseB9_GetResult{
			Key:  type_convert.CopyByteSlice([]byte(key)),
			Item: make([]frequency_proto.GetResponseB9_GetResult_GetResultItem, 0, len(idResult)),
		}

		for _, freqResult := range idResult {
			getResult.Item = append(getResult.Item, frequency_proto.GetResponseB9_GetResult_GetResultItem{
				FrequencyId:     int32(freqResult.FreqControlId),
				ImpressionCount: int32(freqResult.ImpressionCount),
				ClickCount:      int32(freqResult.ClickCount),
				BroadcastCount:  int32(freqResult.BroadcastCount),
				BidCount:        int32(freqResult.BidCount),
				BlockCode:       int32(freqResult.BlockingCode),
			})
		}

		resp.Result = append(resp.Result, getResult)
	}

	return resp
}
