package frequency_service

import (
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/frequency/count_min_sketch"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/utils/type_convert"
	"time"
	"fmt"
)

type LocalFrequencyManager struct {
	countMinSketchRotate *count_min_sketch.CountMinSketchRotate

	countMinSelfCheckGauge *prometheus_helper.SimpleGauge
	term                   chan struct{}
}

func NewLocalFrequencyManager(
	rootDir string,
	rotateType string,
	rotateLength uint32,
	itemCount uint32,
	avgItemFrequency uint32,
	errorRate float64) *LocalFrequencyManager {
	return &LocalFrequencyManager{
		countMinSketchRotate: count_min_sketch.NewCountMinSketchRotate(
			rootDir,
			rotateType,
			rotateLength,
			itemCount,
			avgItemFrequency,
			errorRate),

		countMinSelfCheckGauge: prometheus_helper.RegisterSimpleGauge("frequency_count_min_self_check"),
		term:                   make(chan struct{}),
	}
}

func (m *LocalFrequencyManager) Start() error {
	if err := m.countMinSketchRotate.LoadOrInit(); err != nil {
		return err
	}

	if err := m.countMinSketchRotate.Start(); err != nil {
		return err
	}

	go m.loop()

	return nil
}

func (m *LocalFrequencyManager) Stop() {
	close(m.term)
}

func (m *LocalFrequencyManager) DoSave() error {
	return m.countMinSketchRotate.DoSave()
}

func (m *LocalFrequencyManager) BuildKeyDim(controlId uint32, controlType uint32) uint64 {
	return uint64(controlType)<<32 | uint64(controlId)
}

func (m *LocalFrequencyManager) SingleQuery(key string, keyDim uint64, periodLength uint32) (uint32, error) {
	return m.countMinSketchRotate.AdaptiveQuery(type_convert.UnsafeStringToByte(key), keyDim, periodLength), nil
}

func (m *LocalFrequencyManager) Incr(freqKey string, keyDim uint64) error {
	m.countMinSketchRotate.Incr(type_convert.UnsafeStringToByte(freqKey), keyDim)
	return nil
}

func (m *LocalFrequencyManager) loop() {
	zap.L().Info("[LocalFrequencyManager] loop start")

	ticker := time.NewTicker(time.Second * 300)
	defer ticker.Stop()

	m.doSelfCheck()

	for {
		select {
		case <-ticker.C:
			m.doSelfCheck()
		case <-m.term:
			return
		}
	}
}

func (m *LocalFrequencyManager) doSelfCheck() {
	failRate := m.countMinSketchRotate.SelfCheck()
	zap.L().Info("[LocalFrequencyManager] self check, result rate %f, type", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", failRate)))), zap.String("param2", fmt.Sprintf("%v", m.countMinSketchRotate.GetConfig())).RotateType)
	m.countMinSelfCheckGauge.Set(failRate)
}
