package main

import (
	"net/http"
	_ "net/http/pprof"
	"os"

	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/frequency/app/internal"
	"fmt"
)

func main() {
	wd, err := os.Getwd()
	if err != nil {
		panic(err)
	}
	zap.L().Info("frequency_service start at", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", wd)))))

	// Start pprof server on port 58000
	go func() {
		zap.L().Info("Starting pprof server on :58000")
		if err := http.ListenAndServe(":58000", nil); err != nil {
			zap.L().Error("pprof server failed", zap.Error(err))
		}
	}()

	frequencyServer := internal.NewFrequencyServer()

	if err := frequencyServer.Start(); err != nil {
		zap.L().Error("frequency_service start failed", zap.Error(err))
		return
	}
}
