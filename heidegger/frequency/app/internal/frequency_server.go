package internal

import (
	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/frequency/frequency_service"
	"gitlab.com/dev/heidegger/library/entity_loader"
	"gitlab.com/dev/heidegger/library/entity_loader/frequency_control_loader"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/library/test_helper"
	"gitlab.com/dev/heidegger/master_server/master_server"
	"xorm.io/xorm"
	"fmt"
)

type FrequencyServer struct {
	env Env

	dbEngine *xorm.Engine

	dailyFrequencyManager  *frequency_service.LocalFrequencyManager
	hourFrequencyManager   *frequency_service.LocalFrequencyManager
	minuteFrequencyManager *frequency_service.LocalFrequencyManager
	secFrequencyManager    *frequency_service.LocalFrequencyManager
	groupManager           *frequency_service.LocalFrequencyManagerGroup

	rapidServer *frequency_service.FrequencyRapidServer
	httpServer  *frequency_service.FrequencyHttpHandler
	echoServer  *echo.Echo

	frequencyControlLoader frequency_control_loader.FrequencyControlLoader

	frequencyHttpHandler *frequency_service.FrequencyHttpHandler
	frequencyRapidServer *frequency_service.FrequencyRapidServer
}

func NewFrequencyServer() *FrequencyServer {
	return &FrequencyServer{
		env: GetEnv(),
	}
}

func (s *FrequencyServer) Start() error {
	if err := s.loadEnv(); err != nil {
		return err
	}

	if err := s.startBasic(); err != nil {
		return err
	}

	if err := s.startDataLoader(); err != nil {
		return err
	}

	if err := s.startFrequencyManager(); err != nil {
		return err
	}

	if err := s.StartGroupManager(); err != nil {
		return err
	}

	if err := s.StartHttpServer(); err != nil {
		return err
	}

	if err := s.StartRapidServer(); err != nil {
		return err
	}

	if err := s.StartService(); err != nil {
		return err
	}

	return nil
}

func (s *FrequencyServer) loadEnv() error {
	if s.env.IsDebug {
		return nil
	}

	zap.L().Info("[FrequencyServer] load env")

	client := master_server.NewKeyValueHttpClient("http://local.master_server.com:28000")
	if err := client.GetInto("config", &s.env); err != nil {
		return err
	}

	s.env.DumpJson()
	return nil
}

func (s *FrequencyServer) startBasic() error {
	// logrus.SetLevel converted - configure zap logger instead

	s.echoServer = echo.New()

	zap.L().Info("[AdServer] database name", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", s.env.DatabaseName)))))
	var engine *xorm.Engine
	if len(s.env.DatabaseConn) != 0 {
		zap.L().Info("[AdServer] database conn", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", s.env.DatabaseConn)))))
		engine = test_helper.GetDatabaseWithEncryptedConn(s.env.DatabaseConn)
	} else if s.env.DatabaseName == "local" {
		engine = test_helper.GetTestXormEngine()
	} else {
		panic("invalid database name")
	}

	s.dbEngine = engine

	prometheus_helper.GetGlobalPrometheusManager().RegisterEcho(s.echoServer)

	return nil
}

func (s *FrequencyServer) startDataLoader() error {
	reloadInterval := 30

	s.frequencyControlLoader = frequency_control_loader.NewMysqlFrequencyControlLoader(s.dbEngine, reloadInterval)
	if err := s.frequencyControlLoader.Start(); err != nil {
		return errors.Wrapf(err, "start frequency control loader failed")
	}

	entityLoaderHttpHandler := entity_loader.NewEntityLoaderHttpHandler()
	entityLoaderHttpHandler.SetFrequencyControlLoader(s.frequencyControlLoader)

	entityLoaderHttpHandler.RegisterEcho(s.echoServer)

	return nil
}

func (s *FrequencyServer) startFrequencyManager() error {
	sizeShrink := uint32(1)
	if s.env.IsDebug {
		sizeShrink = 1000
	}

	dailyItem := uint32(s.env.DailyItemCount)
	if dailyItem == 0 {
		dailyItem = 10 * 1000 * 1000
	}

	dailyItem = uint32(dailyItem / sizeShrink)
	hourItem := uint32(dailyItem / 10)
	minuteItem := uint32(hourItem / 10)
	secondItem := uint32(minuteItem / 4)

	dailyFrequencyManager := frequency_service.NewLocalFrequencyManager(
		"./data/day/count_min",
		frequency_service.RotationTypeDaily,
		3,
		dailyItem,
		3,
		0.00001)
	if err := dailyFrequencyManager.Start(); err != nil {
		return err
	}

	hourFrequencyManager := frequency_service.NewLocalFrequencyManager(
		"./data/hour/count_min",
		frequency_service.RotationTypeHour,
		12,
		hourItem,
		3,
		0.0001)
	if err := hourFrequencyManager.Start(); err != nil {
		return err
	}

	minuteFrequencyManager := frequency_service.NewLocalFrequencyManager(
		"./data/minute/count_min",
		frequency_service.RotationTypeMinute,
		10,
		minuteItem,
		3,
		0.0001)
	if err := minuteFrequencyManager.Start(); err != nil {
		return err
	}

	sec10FrequencyManager := frequency_service.NewLocalFrequencyManager(
		"./data/sec10/count_min",
		frequency_service.RotationType10Sec,
		10,
		secondItem,
		3,
		0.0001)
	if err := sec10FrequencyManager.Start(); err != nil {
		return err
	}

	s.dailyFrequencyManager = dailyFrequencyManager
	s.hourFrequencyManager = hourFrequencyManager
	s.minuteFrequencyManager = minuteFrequencyManager
	s.secFrequencyManager = sec10FrequencyManager

	return nil
}

func (s *FrequencyServer) StartGroupManager() error {
	groupManager := frequency_service.NewLocalFrequencyManagerGroup()
	groupManager.SetFrequencyControlLoader(s.frequencyControlLoader)

	if err := groupManager.Register(frequency_service.RotationTypeDaily, s.dailyFrequencyManager); err != nil {
		return err
	}

	if err := groupManager.Register(frequency_service.RotationTypeHour, s.hourFrequencyManager); err != nil {
		return err
	}

	if err := groupManager.Register(frequency_service.RotationTypeMinute, s.minuteFrequencyManager); err != nil {
		return err
	}

	if err := groupManager.Register(frequency_service.RotationType10Sec, s.secFrequencyManager); err != nil {
		return err
	}

	s.groupManager = groupManager
	return nil
}

func (s *FrequencyServer) StartHttpServer() error {
	s.frequencyHttpHandler = frequency_service.NewFrequencyHttpHandler(s.groupManager)
	s.frequencyHttpHandler.RegisterEcho(s.echoServer)

	return nil
}

func (s *FrequencyServer) StartRapidServer() error {
	s.frequencyRapidServer = frequency_service.NewFrequencyRapidServer(s.groupManager)
	return nil
}

func (s *FrequencyServer) StartService() error {
	go func() {
		if err := s.echoServer.Start("0.0.0.0:58002"); err != nil {
			panic(err)
		}
	}()

	if err := s.frequencyRapidServer.Start("0.0.0.0", 58001, 4096); err != nil {
		panic(err)
	}

	return nil
}
