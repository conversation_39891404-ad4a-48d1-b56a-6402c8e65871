package internal

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"os"
)

type Env struct {
	IsDebug bool `json:"-"`

	DatabaseName string `json:"engine.database_name"`
	DatabaseConn string `json:"engine.database_conn"`

	LogLevel       zapcore.Level `json:"ad_server.log_level"`
	DailyItemCount int          `json:"frequency_server.daily_item_count"`
}

func (env *Env) DumpJson() {
	result, _ := json.MarshalIndent(env, "", "  ")
	fmt.Println(string(result))
}

func GetEnv() Env {
	host, _ := os.Hostname()

	zap.L().Info("[GetEnv] Hostname", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", host)))))

	return GetLocalEnv()
}

func GetLocalEnv() Env {
	return Env{
		IsDebug:      false,
		LogLevel:     zapcore.DebugLevel,
		DatabaseName: "local",
	}
}
