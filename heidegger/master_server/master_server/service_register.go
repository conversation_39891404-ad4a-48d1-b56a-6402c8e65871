package master_server

import (
	"fmt"
	"go.uber.org/zap"
	"sync"
	"time"
)

type ServiceRegister interface {
	Start() error
	Stop()

	RegisterService(serviceName string, instanceName string) error
	Watch(serviceName string) error
	GetServiceCount(serviceName string) int
}

type MasterServiceRegister struct {
	client *KeyValueHttpClient

	serviceWatchMap     map[string]int
	serviceWatchMapLock sync.RWMutex

	serviceRegisterMap     map[string]string
	serviceRegisterMapLock sync.RWMutex

	term chan struct{}
}

func NewMasterServiceRegister(client *KeyValueHttpClient) *MasterServiceRegister {
	return &MasterServiceRegister{
		client: client,

		serviceWatchMap:        make(map[string]int),
		serviceRegisterMap:     make(map[string]string),
		serviceWatchMapLock:    sync.RWMutex{},
		serviceRegisterMapLock: sync.RWMutex{},
		term:                   make(chan struct{}),
	}
}

func (r *MasterServiceRegister) Start() error {
	if err := r.doRegister(); err != nil {
		return err
	}

	if err := r.doWatch(); err != nil {
		return err
	}

	go r.loop()

	return nil
}

func (r *MasterServiceRegister) Stop() {
	close(r.term)
}

func (r *MasterServiceRegister) RegisterService(serviceName string, instanceName string) error {
	r.serviceRegisterMapLock.Lock()
	r.serviceRegisterMap[serviceName] = instanceName
	r.serviceRegisterMapLock.Unlock()

	return nil
}

func (r *MasterServiceRegister) Watch(serviceName string) error {
	r.serviceWatchMapLock.Lock()
	r.serviceWatchMap[serviceName] = 0
	r.serviceWatchMapLock.Unlock()

	return nil
}

func (r *MasterServiceRegister) GetServiceCount(serviceName string) int {
	r.serviceWatchMapLock.RLock()
	defer r.serviceWatchMapLock.RUnlock()

	return r.serviceWatchMap[serviceName]
}

func (r *MasterServiceRegister) loop() {
	ticker := time.NewTicker(time.Second * 30)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := r.doRegister(); err != nil {
				zap.L().Error("[MasterServiceRegister] doRegister error", zap.Error(err))
			}

			if err := r.doWatch(); err != nil {
				zap.L().Error("[MasterServiceRegister] doWatch error", zap.Error(err))
			}
		case <-r.term:
			return
		}
	}
}

func (r *MasterServiceRegister) getRegisterData() map[string]string {
	r.serviceRegisterMapLock.RLock()
	defer r.serviceRegisterMapLock.RUnlock()

	result := make(map[string]string)
	for k, v := range r.serviceRegisterMap {
		result[k] = v
	}

	return result
}

func (r *MasterServiceRegister) getWatchData() map[string]int {
	r.serviceWatchMapLock.RLock()
	defer r.serviceWatchMapLock.RUnlock()

	result := make(map[string]int)
	for k, v := range r.serviceWatchMap {
		result[k] = v
	}

	return result
}

func (r *MasterServiceRegister) getServiceRegisterKey(serviceName string) string {
	return fmt.Sprintf("service_register_%s", serviceName)
}

func (r *MasterServiceRegister) doRegister() error {
	data := r.getRegisterData()
	for serviceName, instanceName := range data {
		key := r.getServiceRegisterKey(serviceName)
		registerTime := time.Now().Format("2006-01-02 15:04:05")
		err := r.client.Set(key, instanceName, registerTime, 120)
		if err != nil {
			return fmt.Errorf("[MasterServiceRegister] set key %s error: %v", key, err)
		} else {
			zap.L().Info("[MasterServiceRegister] set key  success", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", key)))))
		}
	}

	return nil
}

func (r *MasterServiceRegister) doWatch() error {
	data := r.getWatchData()
	for serviceName := range data {
		key := r.getServiceRegisterKey(serviceName)
		value, err := r.client.CountKeys(key, "")
		if err != nil {
			return fmt.Errorf("[MasterServiceRegister] get key %s error: %v", key, err)
		}

		r.setServiceWatchCount(serviceName, value)
	}

	return nil
}

func (r *MasterServiceRegister) setServiceWatchCount(serviceName string, count int) {
	r.serviceWatchMapLock.Lock()
	defer r.serviceWatchMapLock.Unlock()

	r.serviceWatchMap[serviceName] = count
}

type ServiceWatcher interface {
	GetServiceCount() int
}

type MasterServiceWatcher struct {
	register    ServiceRegister
	serviceName string
}

func NewMasterServiceWatcher(register ServiceRegister, serviceName string) *MasterServiceWatcher {
	return &MasterServiceWatcher{
		register:    register,
		serviceName: serviceName,
	}
}

func (w *MasterServiceWatcher) GetServiceCount() int {
	return w.register.GetServiceCount(w.serviceName)
}
