package master_server

import (
	"encoding/json"
	"go.uber.org/zap"
	"os"
	"fmt"
)

type KeyValueLoader struct {
	storageManager *KeyValueStorageManager
}

func NewKeyValueLoader(storageManager *KeyValueStorageManager) *KeyValueLoader {
	return &KeyValueLoader{
		storageManager: storageManager,
	}
}

func (l *KeyValueLoader) LoadMap(namespace string, data map[string]interface{}) error {
	storage := l.storageManager.GetNamespace(namespace)
	if storage == nil {
		storage = l.storageManager.CreateNamespace(namespace)
	}

	for key, value := range data {
		storage.Set(key, value)
	}

	return nil
}

func (l *KeyValueLoader) LoadJsonFile(namespace string, path string) error {
	data, err := l.loadJsonFile(path)
	if err != nil {
		return err
	}

	if err := l.LoadMap(namespace, data); err != nil {
		return err
	}

	zap.L().Info("[KeyValueLoader] LoadJsonFile  from ", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", namespace)))), zap.String("param2", fmt.Sprintf("%v", path)))
	return nil
}

func (l *KeyValueLoader) LoadInterface(namespace string, value interface{}) error {
	data, err := l.loadInterface(value)
	if err != nil {
		return err
	}

	return l.LoadMap(namespace, data)
}

func (l *KeyValueLoader) loadJsonFile(path string) (map[string]interface{}, error) {
	data := make(map[string]interface{})

	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	err = decoder.Decode(&data)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (l *KeyValueLoader) loadInterface(data interface{}) (map[string]interface{}, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	result := make(map[string]interface{})
	err = json.Unmarshal(jsonData, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
