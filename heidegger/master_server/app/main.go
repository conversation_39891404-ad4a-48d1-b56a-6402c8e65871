package main

import (
	"github.com/labstack/echo/v4"
	"go.uber.org/zap"
	"gitlab.com/dev/heidegger/library/prometheus_helper"
	"gitlab.com/dev/heidegger/master_server/master_server"
	"os"
	"path/filepath"
	"fmt"
)

func main() {
	echoServer := echo.New()

	prometheus_helper.GetGlobalPrometheusManager().RegisterEcho(echoServer)

	kvManager := master_server.NewKeyValueStorageManager()
	if err := kvManager.Start(); err != nil {
		panic(err)
	}

	kvHandler := master_server.NewKeyValueHttpHandler(kvManager)
	kvHandler.RegisterEcho(echoServer)

	cmd := os.Args[0]
	baseDir := filepath.Dir(cmd)
	workingDir, err := os.Getwd()
	if err != nil {
		panic(err)
	}

	zap.L().Info("baseDir: , workingDir", zap.String("param1", zap.String("value", fmt.Sprintf("%v", fmt.Sprintf("%v")), zap.String("value", fmt.Sprintf("%v", baseDir)))), zap.String("param2", fmt.Sprintf("%v", workingDir)))
	loader := master_server.NewKeyValueLoader(kvManager)

	if _, err := os.Stat(filepath.Join(baseDir, "kv_storage.json")); err == nil {
		if err := loader.LoadJsonFile("config", filepath.Join(baseDir, "kv_storage.json")); err != nil {
			panic(err)
		}
	} else if _, err := os.Stat(filepath.Join(workingDir, "kv_storage.json")); err == nil {
		if err := loader.LoadJsonFile("config", filepath.Join(workingDir, "kv_storage.json")); err != nil {
			panic(err)
		}
	}

	if err := echoServer.Start("0.0.0.0:28000"); err != nil {
		panic(err)
	}
}
